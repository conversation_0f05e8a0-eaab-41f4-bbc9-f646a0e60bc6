{"version": 3, "sources": ["../../@arcgis/core/layers/OpenStreetMapLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import\"../geometry.js\";import{property as o}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as t}from\"../core/accessorSupport/decorators/subclass.js\";import r from\"../geometry/Point.js\";import l from\"./WebTileLayer.js\";import s from\"./support/LOD.js\";import n from\"./support/TileInfo.js\";import i from\"../portal/PortalItem.js\";import p from\"../geometry/SpatialReference.js\";import a from\"../geometry/Extent.js\";let c=class extends l{constructor(...e){super(...e),this.portalItem=null,this.isReference=null,this.tileInfo=new n({size:[256,256],dpi:96,format:\"png8\",compressionQuality:0,origin:new r({x:-20037508.342787,y:20037508.342787,spatialReference:p.WebMercator}),spatialReference:p.WebMercator,lods:[new s({level:0,scale:591657527.591555,resolution:156543.033928}),new s({level:1,scale:295828763.795777,resolution:78271.5169639999}),new s({level:2,scale:147914381.897889,resolution:39135.7584820001}),new s({level:3,scale:73957190.948944,resolution:19567.8792409999}),new s({level:4,scale:36978595.474472,resolution:9783.93962049996}),new s({level:5,scale:18489297.737236,resolution:4891.96981024998}),new s({level:6,scale:9244648.868618,resolution:2445.98490512499}),new s({level:7,scale:4622324.434309,resolution:1222.99245256249}),new s({level:8,scale:2311162.217155,resolution:611.49622628138}),new s({level:9,scale:1155581.108577,resolution:305.748113140558}),new s({level:10,scale:577790.554289,resolution:152.874056570411}),new s({level:11,scale:288895.277144,resolution:76.4370282850732}),new s({level:12,scale:144447.638572,resolution:38.2185141425366}),new s({level:13,scale:72223.819286,resolution:19.1092570712683}),new s({level:14,scale:36111.909643,resolution:9.55462853563415}),new s({level:15,scale:18055.954822,resolution:4.77731426794937}),new s({level:16,scale:9027.977411,resolution:2.38865713397468}),new s({level:17,scale:4513.988705,resolution:1.19432856685505}),new s({level:18,scale:2256.994353,resolution:.597164283559817}),new s({level:19,scale:1128.497176,resolution:.298582141647617})]}),this.subDomains=[\"a\",\"b\",\"c\"],this.fullExtent=new a(-20037508.342787,-20037508.34278,20037508.34278,20037508.342787,p.WebMercator),this.urlTemplate=\"https://{subDomain}.tile.openstreetmap.org/{level}/{col}/{row}.png\",this.operationalLayerType=\"OpenStreetMap\",this.type=\"open-street-map\",this.copyright=\"Map data &copy; OpenStreetMap contributors, CC-BY-SA\"}get refreshInterval(){return 0}};e([o({type:i,json:{read:!1,write:!1,origins:{\"web-document\":{read:!1,write:!1}}}})],c.prototype,\"portalItem\",void 0),e([o({type:Boolean,json:{read:!1,write:!1}})],c.prototype,\"isReference\",void 0),e([o({type:Number,readOnly:!0,json:{read:!1,write:!1,origins:{\"web-document\":{read:!1,write:!1}}}})],c.prototype,\"refreshInterval\",null),e([o({type:n,json:{write:!1}})],c.prototype,\"tileInfo\",void 0),e([o({type:[\"show\",\"hide\"]})],c.prototype,\"listMode\",void 0),e([o({readOnly:!0,json:{read:!1,write:!1}})],c.prototype,\"subDomains\",void 0),e([o({readOnly:!0,json:{read:!1,write:!1},nonNullable:!0})],c.prototype,\"fullExtent\",void 0),e([o({readOnly:!0,json:{read:!1,write:!1}})],c.prototype,\"urlTemplate\",void 0),e([o({type:[\"OpenStreetMap\"]})],c.prototype,\"operationalLayerType\",void 0),e([o({json:{read:!1}})],c.prototype,\"type\",void 0),e([o({json:{read:!1,write:!1}})],c.prototype,\"copyright\",void 0),e([o({json:{read:!1,write:!1}})],c.prototype,\"wmtsInfo\",void 0),c=e([t(\"esri.layers.OpenStreetMapLayer\")],c);const u=c;export{u as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIuiB,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAC,UAAM,GAAGA,EAAC,GAAE,KAAK,aAAW,MAAK,KAAK,cAAY,MAAK,KAAK,WAAS,IAAI,EAAE,EAAC,MAAK,CAAC,KAAI,GAAG,GAAE,KAAI,IAAG,QAAO,QAAO,oBAAmB,GAAE,QAAO,IAAI,EAAE,EAAC,GAAE,oBAAiB,GAAE,mBAAgB,kBAAiB,EAAE,YAAW,CAAC,GAAE,kBAAiB,EAAE,aAAY,MAAK,CAAC,IAAI,EAAE,EAAC,OAAM,GAAE,OAAM,oBAAiB,YAAW,cAAa,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,GAAE,OAAM,oBAAiB,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,GAAE,OAAM,oBAAiB,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,GAAE,OAAM,mBAAgB,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,GAAE,OAAM,mBAAgB,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,GAAE,OAAM,mBAAgB,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,GAAE,OAAM,kBAAe,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,GAAE,OAAM,kBAAe,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,GAAE,OAAM,kBAAe,YAAW,gBAAe,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,GAAE,OAAM,kBAAe,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,IAAG,OAAM,eAAc,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,IAAG,OAAM,eAAc,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,IAAG,OAAM,eAAc,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,IAAG,OAAM,cAAa,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,IAAG,OAAM,cAAa,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,IAAG,OAAM,cAAa,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,IAAG,OAAM,aAAY,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,IAAG,OAAM,aAAY,YAAW,iBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,IAAG,OAAM,aAAY,YAAW,kBAAgB,CAAC,GAAE,IAAI,EAAE,EAAC,OAAM,IAAG,OAAM,aAAY,YAAW,kBAAgB,CAAC,CAAC,EAAC,CAAC,GAAE,KAAK,aAAW,CAAC,KAAI,KAAI,GAAG,GAAE,KAAK,aAAW,IAAIC,GAAE,oBAAiB,mBAAgB,kBAAe,mBAAgB,EAAE,WAAW,GAAE,KAAK,cAAY,sEAAqE,KAAK,uBAAqB,iBAAgB,KAAK,OAAK,mBAAkB,KAAK,YAAU;AAAA,EAAsD;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAO;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,MAAK,OAAG,OAAM,OAAG,SAAQ,EAAC,gBAAe,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,UAAS,MAAG,MAAK,EAAC,MAAK,OAAG,OAAM,OAAG,SAAQ,EAAC,gBAAe,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,mBAAkB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,OAAG,OAAM,MAAE,GAAE,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,eAAe,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,gCAAgC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["e", "w"]}