import {
  _
} from "./chunk-TV6WKLTU.js";
import "./chunk-5VDORDNW.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-P37TUI4J.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-67MHB3E3.js";
import "./chunk-MKG56XBH.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-VTOVDEW2.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-22GGEXM2.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import {
  v
} from "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/UnsupportedLayer.js
var u = class extends _(O(b)) {
  constructor(r) {
    super(r), this.resourceInfo = null, this.type = "unsupported";
  }
  initialize() {
    this.addResolvingPromise(new Promise((r, o) => {
      v(() => {
        const r2 = this.resourceInfo && (this.resourceInfo.layerType || this.resourceInfo.type);
        let s2 = "Unsupported layer type";
        r2 && (s2 += " " + r2), o(new s("layer:unsupported-layer-type", s2, { layerType: r2 }));
      });
    }));
  }
  read(r, e2) {
    const o = { resourceInfo: r };
    null != r.id && (o.id = r.id), null != r.title && (o.title = r.title), super.read(o, e2);
  }
  write(r) {
    return Object.assign(r || {}, this.resourceInfo, { id: this.id });
  }
};
e([y({ readOnly: true })], u.prototype, "resourceInfo", void 0), e([y({ type: ["show", "hide"] })], u.prototype, "listMode", void 0), e([y({ json: { read: false }, readOnly: true, value: "unsupported" })], u.prototype, "type", void 0), u = e([a("esri.layers.UnsupportedLayer")], u);
var c = u;
export {
  c as default
};
//# sourceMappingURL=UnsupportedLayer-KA6NZML3.js.map
