{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/WMTSLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import t from\"../../../core/Logger.js\";import{isAbortError as i}from\"../../../core/promiseUtils.js\";import{watch as s}from\"../../../core/reactiveUtils.js\";import{property as r}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as a}from\"../../../core/accessorSupport/decorators/subclass.js\";import{equals as l}from\"../../../geometry/support/spatialReferenceUtils.js\";import{BitmapTileLayerView2D as o}from\"./BitmapTileLayerView2D.js\";import{LayerView2DMixin as h}from\"./LayerView2D.js\";import{resampleImage as c}from\"./support/imageUtils.js\";import n from\"../tiling/TileInfoView.js\";import u from\"../tiling/TileKey.js\";import p from\"../tiling/TileQueue.js\";import f from\"../tiling/TileStrategy.js\";import m from\"../../layers/LayerView.js\";import d from\"../../layers/RefreshableLayerView.js\";const y=[102113,102100,3857,3785,900913],_=[0,0];let w=class extends(d(o(h(m)))){constructor(){super(...arguments),this._tileStrategy=null,this._fetchQueue=null,this._tileRequests=new Map,this.layer=null}get tileMatrixSet(){const e=this._getTileMatrixSetBySpatialReference(this.layer.activeLayer);return e?(e.id!==this.layer.activeLayer.tileMatrixSetId&&(this.layer.activeLayer.tileMatrixSetId=e.id),e):null}update(e){this._fetchQueue.pause(),this._fetchQueue.state=e.state,this._tileStrategy.update(e),this._fetchQueue.resume()}attach(){const e=this.tileMatrixSet?.tileInfo;e&&(this._tileInfoView=new n(e),this._fetchQueue=new p({tileInfoView:this._tileInfoView,concurrency:16,process:(e,t)=>this.fetchTile(e,t)}),this._tileStrategy=new f({cachePolicy:\"keep\",resampling:!0,acquireTile:e=>this.acquireTile(e),releaseTile:e=>this.releaseTile(e),tileInfoView:this._tileInfoView}),this.addAttachHandles(s((()=>[this.layer?.activeLayer?.styleId,this.tileMatrixSet]),(()=>this._refresh()))),super.attach())}detach(){super.detach(),this._tileStrategy?.destroy(),this._fetchQueue?.destroy(),this._fetchQueue=this._tileStrategy=this._tileInfoView=null}moveStart(){this.requestUpdate()}viewChange(){this.requestUpdate()}moveEnd(){this.requestUpdate()}releaseTile(e){this._fetchQueue.abort(e.key.id),this._bitmapView.removeChild(e),e.once(\"detach\",(()=>e.destroy())),this.requestUpdate()}acquireTile(e){const t=this._bitmapView.createTile(e),i=t.bitmap;return[i.x,i.y]=this._tileInfoView.getTileCoords(_,t.key),i.resolution=this._tileInfoView.getTileResolution(t.key),[i.width,i.height]=this._tileInfoView.tileInfo.size,this._enqueueTileFetch(t),this._bitmapView.addChild(t),this.requestUpdate(),t}async doRefresh(){!this.attached||this.updateRequested||this.suspended||this._refresh()}isUpdating(){return this._fetchQueue?.updating??!1}async fetchTile(e,t={}){const s=\"tilemapCache\"in this.layer?this.layer.tilemapCache:null,{signal:r,resamplingLevel:a=0}=t;if(!s)return this._fetchImage(e,r);const l=new u(0,0,0,0);let o;try{await s.fetchAvailabilityUpsample(e.level,e.row,e.col,l,{signal:r}),o=await this._fetchImage(l,r)}catch(h){if(i(h))throw h;if(a<3){const i=this._tileInfoView.getTileParentId(e.id);if(i){const s=new u(i),r=await this.fetchTile(s,{...t,resamplingLevel:a+1});return c(this._tileInfoView,r,s,e)}}throw h}return c(this._tileInfoView,o,l,e)}canResume(){const e=super.canResume();return e?null!==this.tileMatrixSet:e}supportsSpatialReference(e){return this.layer.activeLayer.tileMatrixSets?.some((t=>l(t.tileInfo?.spatialReference,e)))??!1}async _enqueueTileFetch(e){if(!this._fetchQueue.has(e.key.id)){try{const t=await this._fetchQueue.push(e.key);e.bitmap.source=t,e.bitmap.width=this._tileInfoView.tileInfo.size[0],e.bitmap.height=this._tileInfoView.tileInfo.size[1],e.once(\"attach\",(()=>this.requestUpdate()))}catch(s){i(s)||t.getLogger(this.declaredClass).error(s)}this.requestUpdate()}}async _fetchImage(e,t){return this.layer.fetchImageBitmapTile(e.level,e.row,e.col,{signal:t})}_refresh(){this._fetchQueue.reset(),this._tileStrategy.tiles.forEach((e=>{if(!e.bitmap.source)return;const t={id:e.key.id,fulfilled:!1,promise:this._fetchQueue.push(e.key).then((t=>{e.bitmap.source=t})).catch((t=>{i(t)||(e.bitmap.source=null)})).finally((()=>{e.requestRender(),t.fulfilled=!0}))};this._tileRequests.set(e,t)}))}_getTileMatrixSetBySpatialReference(e){const t=this.view.spatialReference;if(!e.tileMatrixSets)return null;let i=e.tileMatrixSets.find((e=>l(e.tileInfo?.spatialReference,t)));return!i&&t.isWebMercator&&(i=e.tileMatrixSets.find((e=>y.includes(e.tileInfo?.spatialReference.wkid??-1)))),i}};e([r()],w.prototype,\"_fetchQueue\",void 0),e([r({readOnly:!0})],w.prototype,\"tileMatrixSet\",null),w=e([a(\"esri.views.2d.layers.WMTSLayerView2D\")],w);const g=w;export{g as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIw7B,IAAMA,KAAE,CAAC,QAAO,QAAO,MAAK,MAAK,MAAM;AAAvC,IAAyC,IAAE,CAAC,GAAE,CAAC;AAAE,IAAI,IAAE,cAAc,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,gBAAc,MAAK,KAAK,cAAY,MAAK,KAAK,gBAAc,oBAAI,OAAI,KAAK,QAAM;AAAA,EAAI;AAAA,EAAC,IAAI,gBAAe;AAAC,UAAMC,KAAE,KAAK,oCAAoC,KAAK,MAAM,WAAW;AAAE,WAAOA,MAAGA,GAAE,OAAK,KAAK,MAAM,YAAY,oBAAkB,KAAK,MAAM,YAAY,kBAAgBA,GAAE,KAAIA,MAAG;AAAA,EAAI;AAAA,EAAC,OAAOA,IAAE;AAAC,SAAK,YAAY,MAAM,GAAE,KAAK,YAAY,QAAMA,GAAE,OAAM,KAAK,cAAc,OAAOA,EAAC,GAAE,KAAK,YAAY,OAAO;AAAA,EAAC;AAAA,EAAC,SAAQ;AAJj9C;AAIk9C,UAAMA,MAAE,UAAK,kBAAL,mBAAoB;AAAS,IAAAA,OAAI,KAAK,gBAAc,IAAI,EAAEA,EAAC,GAAE,KAAK,cAAY,IAAID,GAAE,EAAC,cAAa,KAAK,eAAc,aAAY,IAAG,SAAQ,CAACC,IAAEC,OAAI,KAAK,UAAUD,IAAEC,EAAC,EAAC,CAAC,GAAE,KAAK,gBAAc,IAAI,EAAE,EAAC,aAAY,QAAO,YAAW,MAAG,aAAY,CAAAD,OAAG,KAAK,YAAYA,EAAC,GAAE,aAAY,CAAAA,OAAG,KAAK,YAAYA,EAAC,GAAE,cAAa,KAAK,cAAa,CAAC,GAAE,KAAK,iBAAiB,EAAG,MAAE;AAJj0D,UAAAE,KAAA;AAIm0D,eAAC,MAAAA,MAAA,KAAK,UAAL,gBAAAA,IAAY,gBAAZ,mBAAyB,SAAQ,KAAK,aAAa;AAAA,OAAI,MAAI,KAAK,SAAS,CAAE,CAAC,GAAE,MAAM,OAAO;AAAA,EAAE;AAAA,EAAC,SAAQ;AAJ16D;AAI26D,UAAM,OAAO,IAAE,UAAK,kBAAL,mBAAoB,YAAU,UAAK,gBAAL,mBAAkB,WAAU,KAAK,cAAY,KAAK,gBAAc,KAAK,gBAAc;AAAA,EAAI;AAAA,EAAC,YAAW;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,YAAYF,IAAE;AAAC,SAAK,YAAY,MAAMA,GAAE,IAAI,EAAE,GAAE,KAAK,YAAY,YAAYA,EAAC,GAAEA,GAAE,KAAK,UAAU,MAAIA,GAAE,QAAQ,CAAE,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMC,KAAE,KAAK,YAAY,WAAWD,EAAC,GAAEG,KAAEF,GAAE;AAAO,WAAM,CAACE,GAAE,GAAEA,GAAE,CAAC,IAAE,KAAK,cAAc,cAAc,GAAEF,GAAE,GAAG,GAAEE,GAAE,aAAW,KAAK,cAAc,kBAAkBF,GAAE,GAAG,GAAE,CAACE,GAAE,OAAMA,GAAE,MAAM,IAAE,KAAK,cAAc,SAAS,MAAK,KAAK,kBAAkBF,EAAC,GAAE,KAAK,YAAY,SAASA,EAAC,GAAE,KAAK,cAAc,GAAEA;AAAA,EAAC;AAAA,EAAC,MAAM,YAAW;AAAC,KAAC,KAAK,YAAU,KAAK,mBAAiB,KAAK,aAAW,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,aAAY;AAJprF;AAIqrF,aAAO,UAAK,gBAAL,mBAAkB,aAAU;AAAA,EAAE;AAAA,EAAC,MAAM,UAAUD,IAAEC,KAAE,CAAC,GAAE;AAAC,UAAMG,KAAE,kBAAiB,KAAK,QAAM,KAAK,MAAM,eAAa,MAAK,EAAC,QAAOC,IAAE,iBAAgBC,KAAE,EAAC,IAAEL;AAAE,QAAG,CAACG,GAAE,QAAO,KAAK,YAAYJ,IAAEK,EAAC;AAAE,UAAME,KAAE,IAAIP,GAAE,GAAE,GAAE,GAAE,CAAC;AAAE,QAAI;AAAE,QAAG;AAAC,YAAMI,GAAE,0BAA0BJ,GAAE,OAAMA,GAAE,KAAIA,GAAE,KAAIO,IAAE,EAAC,QAAOF,GAAC,CAAC,GAAE,IAAE,MAAM,KAAK,YAAYE,IAAEF,EAAC;AAAA,IAAC,SAAOG,IAAE;AAAC,UAAG,EAAEA,EAAC,EAAE,OAAMA;AAAE,UAAGF,KAAE,GAAE;AAAC,cAAMH,KAAE,KAAK,cAAc,gBAAgBH,GAAE,EAAE;AAAE,YAAGG,IAAE;AAAC,gBAAMC,KAAE,IAAIJ,GAAEG,EAAC,GAAEE,KAAE,MAAM,KAAK,UAAUD,IAAE,EAAC,GAAGH,IAAE,iBAAgBK,KAAE,EAAC,CAAC;AAAE,iBAAO,EAAE,KAAK,eAAcD,IAAED,IAAEJ,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,YAAMQ;AAAA,IAAC;AAAC,WAAO,EAAE,KAAK,eAAc,GAAED,IAAEP,EAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,UAAMA,KAAE,MAAM,UAAU;AAAE,WAAOA,KAAE,SAAO,KAAK,gBAAcA;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAE;AAJ90G;AAI+0G,aAAO,UAAK,MAAM,YAAY,mBAAvB,mBAAuC,KAAM,CAAAC,OAAC;AAJp4G,UAAAC;AAIs4G,gBAAEA,MAAAD,GAAE,aAAF,gBAAAC,IAAY,kBAAiBF,EAAC;AAAA,WAAK;AAAA,EAAE;AAAA,EAAC,MAAM,kBAAkBA,IAAE;AAAC,QAAG,CAAC,KAAK,YAAY,IAAIA,GAAE,IAAI,EAAE,GAAE;AAAC,UAAG;AAAC,cAAMC,KAAE,MAAM,KAAK,YAAY,KAAKD,GAAE,GAAG;AAAE,QAAAA,GAAE,OAAO,SAAOC,IAAED,GAAE,OAAO,QAAM,KAAK,cAAc,SAAS,KAAK,CAAC,GAAEA,GAAE,OAAO,SAAO,KAAK,cAAc,SAAS,KAAK,CAAC,GAAEA,GAAE,KAAK,UAAU,MAAI,KAAK,cAAc,CAAE;AAAA,MAAC,SAAOI,IAAE;AAAC,UAAEA,EAAC,KAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAMA,EAAC;AAAA,MAAC;AAAC,WAAK,cAAc;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,YAAYJ,IAAEC,IAAE;AAAC,WAAO,KAAK,MAAM,qBAAqBD,GAAE,OAAMA,GAAE,KAAIA,GAAE,KAAI,EAAC,QAAOC,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,SAAK,YAAY,MAAM,GAAE,KAAK,cAAc,MAAM,QAAS,CAAAD,OAAG;AAAC,UAAG,CAACA,GAAE,OAAO,OAAO;AAAO,YAAMC,KAAE,EAAC,IAAGD,GAAE,IAAI,IAAG,WAAU,OAAG,SAAQ,KAAK,YAAY,KAAKA,GAAE,GAAG,EAAE,KAAM,CAAAC,OAAG;AAAC,QAAAD,GAAE,OAAO,SAAOC;AAAA,MAAC,CAAE,EAAE,MAAO,CAAAA,OAAG;AAAC,UAAEA,EAAC,MAAID,GAAE,OAAO,SAAO;AAAA,MAAK,CAAE,EAAE,QAAS,MAAI;AAAC,QAAAA,GAAE,cAAc,GAAEC,GAAE,YAAU;AAAA,MAAE,CAAE,EAAC;AAAE,WAAK,cAAc,IAAID,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,oCAAoCD,IAAE;AAAC,UAAMC,KAAE,KAAK,KAAK;AAAiB,QAAG,CAACD,GAAE,eAAe,QAAO;AAAK,QAAIG,KAAEH,GAAE,eAAe,KAAM,CAAAA,OAAC;AAJ9zI;AAIg0I,gBAAE,KAAAA,GAAE,aAAF,mBAAY,kBAAiBC,EAAC;AAAA,KAAE;AAAE,WAAM,CAACE,MAAGF,GAAE,kBAAgBE,KAAEH,GAAE,eAAe,KAAM,CAAAA,OAAC;AAJ15I;AAI45I,aAAAD,GAAE,WAAS,KAAAC,GAAE,aAAF,mBAAY,iBAAiB,SAAM,EAAE;AAAA,KAAE,IAAGG;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,sCAAsC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["y", "e", "t", "_a", "i", "s", "r", "a", "l", "h"]}