package org.thingsboard.server.dao.model.sql.smartProduction.dispatch;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordCompleteStatus;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sp_order_record")
public class OrderRecord {
    // id
    @TableId
    private String id;

    // 指令类型id
    private String type;

    // 指令类型名称
    private String typeName;

    // 完成状态。未完成/已完成
    private OrderRecordCompleteStatus completeStatus;

    // 指令状态。待发送/待接收/待回复/已回复/已拒绝
    private OrderRecordStatus commandStatus;

    // 发送站点
    private String sendDeptId;

    // 发送站点
    private String sendDeptName;

    // 接收站点
    private String receiveDeptId;

    // 接收站点
    private String receiveDeptName;

    // 发送的指令内容
    private String sendContent;

    // 执行时间
    private Date executionTime;

    // 指令备注
    private String remark;

    // 启用泵组（泵站ID，多个用逗号分隔）
    private String enablePumps;

    // 关闭泵组（泵站ID，多个用逗号分隔）
    private String disablePumps;

    // 发送人
    @ParseUsername
    private String sendUserId;

    // 发送时间
    private Date sendTime;

    // 接收人
    @ParseUsername
    private String receiveUserId;

    // 接收时间
    private Date receiveTime;

    // 回复的指令内容
    private String replyContent;

    // 回复时间
    private Date replyTime;

    // 拒绝备注/原因
    private String rejectRemark;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    private String tenantId;

}
