{"version": 3, "sources": ["../../@stencil/core/internal/client/shadow-css.js"], "sourcesContent": ["/*\n Stencil Client Platform v2.20.0 | MIT Licensed | https://stenciljs.com\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n *\n * This file is a port of shadowCSS from webcomponents.js to TypeScript.\n * https://github.com/webcomponents/webcomponentsjs/blob/4efecd7e0e/src/ShadowCSS/ShadowCSS.js\n * https://github.com/angular/angular/blob/master/packages/compiler/src/shadow_css.ts\n */\nconst safeSelector = (selector) => {\n    const placeholders = [];\n    let index = 0;\n    // Replaces attribute selectors with placeholders.\n    // The WS in [attr=\"va lue\"] would otherwise be interpreted as a selector separator.\n    selector = selector.replace(/(\\[[^\\]]*\\])/g, (_, keep) => {\n        const replaceBy = `__ph-${index}__`;\n        placeholders.push(keep);\n        index++;\n        return replaceBy;\n    });\n    // Replaces the expression in `:nth-child(2n + 1)` with a placeholder.\n    // WS and \"+\" would otherwise be interpreted as selector separators.\n    const content = selector.replace(/(:nth-[-\\w]+)(\\([^)]+\\))/g, (_, pseudo, exp) => {\n        const replaceBy = `__ph-${index}__`;\n        placeholders.push(exp);\n        index++;\n        return pseudo + replaceBy;\n    });\n    const ss = {\n        content,\n        placeholders,\n    };\n    return ss;\n};\nconst restoreSafeSelector = (placeholders, content) => {\n    return content.replace(/__ph-(\\d+)__/g, (_, index) => placeholders[+index]);\n};\nconst _polyfillHost = '-shadowcsshost';\nconst _polyfillSlotted = '-shadowcssslotted';\n// note: :host-context pre-processed to -shadowcsshostcontext.\nconst _polyfillHostContext = '-shadowcsscontext';\nconst _parenSuffix = ')(?:\\\\((' + '(?:\\\\([^)(]*\\\\)|[^)(]*)+?' + ')\\\\))?([^,{]*)';\nconst _cssColonHostRe = new RegExp('(' + _polyfillHost + _parenSuffix, 'gim');\nconst _cssColonHostContextRe = new RegExp('(' + _polyfillHostContext + _parenSuffix, 'gim');\nconst _cssColonSlottedRe = new RegExp('(' + _polyfillSlotted + _parenSuffix, 'gim');\nconst _polyfillHostNoCombinator = _polyfillHost + '-no-combinator';\nconst _polyfillHostNoCombinatorRe = /-shadowcsshost-no-combinator([^\\s]*)/;\nconst _shadowDOMSelectorsRe = [/::shadow/g, /::content/g];\nconst _selectorReSuffix = '([>\\\\s~+[.,{:][\\\\s\\\\S]*)?$';\nconst _polyfillHostRe = /-shadowcsshost/gim;\nconst _colonHostRe = /:host/gim;\nconst _colonSlottedRe = /::slotted/gim;\nconst _colonHostContextRe = /:host-context/gim;\nconst _commentRe = /\\/\\*\\s*[\\s\\S]*?\\*\\//g;\nconst stripComments = (input) => {\n    return input.replace(_commentRe, '');\n};\nconst _commentWithHashRe = /\\/\\*\\s*#\\s*source(Mapping)?URL=[\\s\\S]+?\\*\\//g;\nconst extractCommentsWithHash = (input) => {\n    return input.match(_commentWithHashRe) || [];\n};\nconst _ruleRe = /(\\s*)([^;\\{\\}]+?)(\\s*)((?:{%BLOCK%}?\\s*;?)|(?:\\s*;))/g;\nconst _curlyRe = /([{}])/g;\nconst _selectorPartsRe = /(^.*?[^\\\\])??((:+)(.*)|$)/;\nconst OPEN_CURLY = '{';\nconst CLOSE_CURLY = '}';\nconst BLOCK_PLACEHOLDER = '%BLOCK%';\nconst processRules = (input, ruleCallback) => {\n    const inputWithEscapedBlocks = escapeBlocks(input);\n    let nextBlockIndex = 0;\n    return inputWithEscapedBlocks.escapedString.replace(_ruleRe, (...m) => {\n        const selector = m[2];\n        let content = '';\n        let suffix = m[4];\n        let contentPrefix = '';\n        if (suffix && suffix.startsWith('{' + BLOCK_PLACEHOLDER)) {\n            content = inputWithEscapedBlocks.blocks[nextBlockIndex++];\n            suffix = suffix.substring(BLOCK_PLACEHOLDER.length + 1);\n            contentPrefix = '{';\n        }\n        const cssRule = {\n            selector,\n            content,\n        };\n        const rule = ruleCallback(cssRule);\n        return `${m[1]}${rule.selector}${m[3]}${contentPrefix}${rule.content}${suffix}`;\n    });\n};\nconst escapeBlocks = (input) => {\n    const inputParts = input.split(_curlyRe);\n    const resultParts = [];\n    const escapedBlocks = [];\n    let bracketCount = 0;\n    let currentBlockParts = [];\n    for (let partIndex = 0; partIndex < inputParts.length; partIndex++) {\n        const part = inputParts[partIndex];\n        if (part === CLOSE_CURLY) {\n            bracketCount--;\n        }\n        if (bracketCount > 0) {\n            currentBlockParts.push(part);\n        }\n        else {\n            if (currentBlockParts.length > 0) {\n                escapedBlocks.push(currentBlockParts.join(''));\n                resultParts.push(BLOCK_PLACEHOLDER);\n                currentBlockParts = [];\n            }\n            resultParts.push(part);\n        }\n        if (part === OPEN_CURLY) {\n            bracketCount++;\n        }\n    }\n    if (currentBlockParts.length > 0) {\n        escapedBlocks.push(currentBlockParts.join(''));\n        resultParts.push(BLOCK_PLACEHOLDER);\n    }\n    const strEscapedBlocks = {\n        escapedString: resultParts.join(''),\n        blocks: escapedBlocks,\n    };\n    return strEscapedBlocks;\n};\nconst insertPolyfillHostInCssText = (selector) => {\n    selector = selector\n        .replace(_colonHostContextRe, _polyfillHostContext)\n        .replace(_colonHostRe, _polyfillHost)\n        .replace(_colonSlottedRe, _polyfillSlotted);\n    return selector;\n};\nconst convertColonRule = (cssText, regExp, partReplacer) => {\n    // m[1] = :host(-context), m[2] = contents of (), m[3] rest of rule\n    return cssText.replace(regExp, (...m) => {\n        if (m[2]) {\n            const parts = m[2].split(',');\n            const r = [];\n            for (let i = 0; i < parts.length; i++) {\n                const p = parts[i].trim();\n                if (!p)\n                    break;\n                r.push(partReplacer(_polyfillHostNoCombinator, p, m[3]));\n            }\n            return r.join(',');\n        }\n        else {\n            return _polyfillHostNoCombinator + m[3];\n        }\n    });\n};\nconst colonHostPartReplacer = (host, part, suffix) => {\n    return host + part.replace(_polyfillHost, '') + suffix;\n};\nconst convertColonHost = (cssText) => {\n    return convertColonRule(cssText, _cssColonHostRe, colonHostPartReplacer);\n};\nconst colonHostContextPartReplacer = (host, part, suffix) => {\n    if (part.indexOf(_polyfillHost) > -1) {\n        return colonHostPartReplacer(host, part, suffix);\n    }\n    else {\n        return host + part + suffix + ', ' + part + ' ' + host + suffix;\n    }\n};\nconst convertColonSlotted = (cssText, slotScopeId) => {\n    const slotClass = '.' + slotScopeId + ' > ';\n    const selectors = [];\n    cssText = cssText.replace(_cssColonSlottedRe, (...m) => {\n        if (m[2]) {\n            const compound = m[2].trim();\n            const suffix = m[3];\n            const slottedSelector = slotClass + compound + suffix;\n            let prefixSelector = '';\n            for (let i = m[4] - 1; i >= 0; i--) {\n                const char = m[5][i];\n                if (char === '}' || char === ',') {\n                    break;\n                }\n                prefixSelector = char + prefixSelector;\n            }\n            const orgSelector = prefixSelector + slottedSelector;\n            const addedSelector = `${prefixSelector.trimRight()}${slottedSelector.trim()}`;\n            if (orgSelector.trim() !== addedSelector.trim()) {\n                const updatedSelector = `${addedSelector}, ${orgSelector}`;\n                selectors.push({\n                    orgSelector,\n                    updatedSelector,\n                });\n            }\n            return slottedSelector;\n        }\n        else {\n            return _polyfillHostNoCombinator + m[3];\n        }\n    });\n    return {\n        selectors,\n        cssText,\n    };\n};\nconst convertColonHostContext = (cssText) => {\n    return convertColonRule(cssText, _cssColonHostContextRe, colonHostContextPartReplacer);\n};\nconst convertShadowDOMSelectors = (cssText) => {\n    return _shadowDOMSelectorsRe.reduce((result, pattern) => result.replace(pattern, ' '), cssText);\n};\nconst makeScopeMatcher = (scopeSelector) => {\n    const lre = /\\[/g;\n    const rre = /\\]/g;\n    scopeSelector = scopeSelector.replace(lre, '\\\\[').replace(rre, '\\\\]');\n    return new RegExp('^(' + scopeSelector + ')' + _selectorReSuffix, 'm');\n};\nconst selectorNeedsScoping = (selector, scopeSelector) => {\n    const re = makeScopeMatcher(scopeSelector);\n    return !re.test(selector);\n};\nconst injectScopingSelector = (selector, scopingSelector) => {\n    return selector.replace(_selectorPartsRe, (_, before = '', _colonGroup, colon = '', after = '') => {\n        return before + scopingSelector + colon + after;\n    });\n};\nconst applySimpleSelectorScope = (selector, scopeSelector, hostSelector) => {\n    // In Android browser, the lastIndex is not reset when the regex is used in String.replace()\n    _polyfillHostRe.lastIndex = 0;\n    if (_polyfillHostRe.test(selector)) {\n        const replaceBy = `.${hostSelector}`;\n        return selector\n            .replace(_polyfillHostNoCombinatorRe, (_, selector) => injectScopingSelector(selector, replaceBy))\n            .replace(_polyfillHostRe, replaceBy + ' ');\n    }\n    return scopeSelector + ' ' + selector;\n};\nconst applyStrictSelectorScope = (selector, scopeSelector, hostSelector) => {\n    const isRe = /\\[is=([^\\]]*)\\]/g;\n    scopeSelector = scopeSelector.replace(isRe, (_, ...parts) => parts[0]);\n    const className = '.' + scopeSelector;\n    const _scopeSelectorPart = (p) => {\n        let scopedP = p.trim();\n        if (!scopedP) {\n            return '';\n        }\n        if (p.indexOf(_polyfillHostNoCombinator) > -1) {\n            scopedP = applySimpleSelectorScope(p, scopeSelector, hostSelector);\n        }\n        else {\n            // remove :host since it should be unnecessary\n            const t = p.replace(_polyfillHostRe, '');\n            if (t.length > 0) {\n                scopedP = injectScopingSelector(t, className);\n            }\n        }\n        return scopedP;\n    };\n    const safeContent = safeSelector(selector);\n    selector = safeContent.content;\n    let scopedSelector = '';\n    let startIndex = 0;\n    let res;\n    const sep = /( |>|\\+|~(?!=))\\s*/g;\n    // If a selector appears before :host it should not be shimmed as it\n    // matches on ancestor elements and not on elements in the host's shadow\n    // `:host-context(div)` is transformed to\n    // `-shadowcsshost-no-combinatordiv, div -shadowcsshost-no-combinator`\n    // the `div` is not part of the component in the 2nd selectors and should not be scoped.\n    // Historically `component-tag:host` was matching the component so we also want to preserve\n    // this behavior to avoid breaking legacy apps (it should not match).\n    // The behavior should be:\n    // - `tag:host` -> `tag[h]` (this is to avoid breaking legacy apps, should not match anything)\n    // - `tag :host` -> `tag [h]` (`tag` is not scoped because it's considered part of a\n    //   `:host-context(tag)`)\n    const hasHost = selector.indexOf(_polyfillHostNoCombinator) > -1;\n    // Only scope parts after the first `-shadowcsshost-no-combinator` when it is present\n    let shouldScope = !hasHost;\n    while ((res = sep.exec(selector)) !== null) {\n        const separator = res[1];\n        const part = selector.slice(startIndex, res.index).trim();\n        shouldScope = shouldScope || part.indexOf(_polyfillHostNoCombinator) > -1;\n        const scopedPart = shouldScope ? _scopeSelectorPart(part) : part;\n        scopedSelector += `${scopedPart} ${separator} `;\n        startIndex = sep.lastIndex;\n    }\n    const part = selector.substring(startIndex);\n    shouldScope = shouldScope || part.indexOf(_polyfillHostNoCombinator) > -1;\n    scopedSelector += shouldScope ? _scopeSelectorPart(part) : part;\n    // replace the placeholders with their original values\n    return restoreSafeSelector(safeContent.placeholders, scopedSelector);\n};\nconst scopeSelector = (selector, scopeSelectorText, hostSelector, slotSelector) => {\n    return selector\n        .split(',')\n        .map((shallowPart) => {\n        if (slotSelector && shallowPart.indexOf('.' + slotSelector) > -1) {\n            return shallowPart.trim();\n        }\n        if (selectorNeedsScoping(shallowPart, scopeSelectorText)) {\n            return applyStrictSelectorScope(shallowPart, scopeSelectorText, hostSelector).trim();\n        }\n        else {\n            return shallowPart.trim();\n        }\n    })\n        .join(', ');\n};\nconst scopeSelectors = (cssText, scopeSelectorText, hostSelector, slotSelector, commentOriginalSelector) => {\n    return processRules(cssText, (rule) => {\n        let selector = rule.selector;\n        let content = rule.content;\n        if (rule.selector[0] !== '@') {\n            selector = scopeSelector(rule.selector, scopeSelectorText, hostSelector, slotSelector);\n        }\n        else if (rule.selector.startsWith('@media') ||\n            rule.selector.startsWith('@supports') ||\n            rule.selector.startsWith('@page') ||\n            rule.selector.startsWith('@document')) {\n            content = scopeSelectors(rule.content, scopeSelectorText, hostSelector, slotSelector);\n        }\n        const cssRule = {\n            selector: selector.replace(/\\s{2,}/g, ' ').trim(),\n            content,\n        };\n        return cssRule;\n    });\n};\nconst scopeCssText = (cssText, scopeId, hostScopeId, slotScopeId, commentOriginalSelector) => {\n    cssText = insertPolyfillHostInCssText(cssText);\n    cssText = convertColonHost(cssText);\n    cssText = convertColonHostContext(cssText);\n    const slotted = convertColonSlotted(cssText, slotScopeId);\n    cssText = slotted.cssText;\n    cssText = convertShadowDOMSelectors(cssText);\n    if (scopeId) {\n        cssText = scopeSelectors(cssText, scopeId, hostScopeId, slotScopeId);\n    }\n    cssText = cssText.replace(/-shadowcsshost-no-combinator/g, `.${hostScopeId}`);\n    cssText = cssText.replace(/>\\s*\\*\\s+([^{, ]+)/gm, ' $1 ');\n    return {\n        cssText: cssText.trim(),\n        slottedSelectors: slotted.selectors,\n    };\n};\nconst scopeCss = (cssText, scopeId, commentOriginalSelector) => {\n    const hostScopeId = scopeId + '-h';\n    const slotScopeId = scopeId + '-s';\n    const commentsWithHash = extractCommentsWithHash(cssText);\n    cssText = stripComments(cssText);\n    const orgSelectors = [];\n    if (commentOriginalSelector) {\n        const processCommentedSelector = (rule) => {\n            const placeholder = `/*!@___${orgSelectors.length}___*/`;\n            const comment = `/*!@${rule.selector}*/`;\n            orgSelectors.push({ placeholder, comment });\n            rule.selector = placeholder + rule.selector;\n            return rule;\n        };\n        cssText = processRules(cssText, (rule) => {\n            if (rule.selector[0] !== '@') {\n                return processCommentedSelector(rule);\n            }\n            else if (rule.selector.startsWith('@media') ||\n                rule.selector.startsWith('@supports') ||\n                rule.selector.startsWith('@page') ||\n                rule.selector.startsWith('@document')) {\n                rule.content = processRules(rule.content, processCommentedSelector);\n                return rule;\n            }\n            return rule;\n        });\n    }\n    const scoped = scopeCssText(cssText, scopeId, hostScopeId, slotScopeId);\n    cssText = [scoped.cssText, ...commentsWithHash].join('\\n');\n    if (commentOriginalSelector) {\n        orgSelectors.forEach(({ placeholder, comment }) => {\n            cssText = cssText.replace(placeholder, comment);\n        });\n    }\n    scoped.slottedSelectors.forEach((slottedSelector) => {\n        cssText = cssText.replace(slottedSelector.orgSelector, slottedSelector.updatedSelector);\n    });\n    return cssText;\n};\n\nexport { scopeCss };\n"], "mappings": ";;;AAcA,IAAM,eAAe,CAAC,aAAa;AAC/B,QAAM,eAAe,CAAC;AACtB,MAAI,QAAQ;AAGZ,aAAW,SAAS,QAAQ,iBAAiB,CAAC,GAAG,SAAS;AACtD,UAAM,YAAY,QAAQ,KAAK;AAC/B,iBAAa,KAAK,IAAI;AACtB;AACA,WAAO;AAAA,EACX,CAAC;AAGD,QAAM,UAAU,SAAS,QAAQ,6BAA6B,CAAC,GAAG,QAAQ,QAAQ;AAC9E,UAAM,YAAY,QAAQ,KAAK;AAC/B,iBAAa,KAAK,GAAG;AACrB;AACA,WAAO,SAAS;AAAA,EACpB,CAAC;AACD,QAAM,KAAK;AAAA,IACP;AAAA,IACA;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,sBAAsB,CAAC,cAAc,YAAY;AACnD,SAAO,QAAQ,QAAQ,iBAAiB,CAAC,GAAG,UAAU,aAAa,CAAC,KAAK,CAAC;AAC9E;AACA,IAAM,gBAAgB;AACtB,IAAM,mBAAmB;AAEzB,IAAM,uBAAuB;AAC7B,IAAM,eAAe;AACrB,IAAM,kBAAkB,IAAI,OAAO,MAAM,gBAAgB,cAAc,KAAK;AAC5E,IAAM,yBAAyB,IAAI,OAAO,MAAM,uBAAuB,cAAc,KAAK;AAC1F,IAAM,qBAAqB,IAAI,OAAO,MAAM,mBAAmB,cAAc,KAAK;AAClF,IAAM,4BAA4B,gBAAgB;AAClD,IAAM,8BAA8B;AACpC,IAAM,wBAAwB,CAAC,aAAa,YAAY;AACxD,IAAM,oBAAoB;AAC1B,IAAM,kBAAkB;AACxB,IAAM,eAAe;AACrB,IAAM,kBAAkB;AACxB,IAAM,sBAAsB;AAC5B,IAAM,aAAa;AACnB,IAAM,gBAAgB,CAAC,UAAU;AAC7B,SAAO,MAAM,QAAQ,YAAY,EAAE;AACvC;AACA,IAAM,qBAAqB;AAC3B,IAAM,0BAA0B,CAAC,UAAU;AACvC,SAAO,MAAM,MAAM,kBAAkB,KAAK,CAAC;AAC/C;AACA,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,IAAM,mBAAmB;AACzB,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,eAAe,CAAC,OAAO,iBAAiB;AAC1C,QAAM,yBAAyB,aAAa,KAAK;AACjD,MAAI,iBAAiB;AACrB,SAAO,uBAAuB,cAAc,QAAQ,SAAS,IAAI,MAAM;AACnE,UAAM,WAAW,EAAE,CAAC;AACpB,QAAI,UAAU;AACd,QAAI,SAAS,EAAE,CAAC;AAChB,QAAI,gBAAgB;AACpB,QAAI,UAAU,OAAO,WAAW,MAAM,iBAAiB,GAAG;AACtD,gBAAU,uBAAuB,OAAO,gBAAgB;AACxD,eAAS,OAAO,UAAU,kBAAkB,SAAS,CAAC;AACtD,sBAAgB;AAAA,IACpB;AACA,UAAM,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,IACJ;AACA,UAAM,OAAO,aAAa,OAAO;AACjC,WAAO,GAAG,EAAE,CAAC,CAAC,GAAG,KAAK,QAAQ,GAAG,EAAE,CAAC,CAAC,GAAG,aAAa,GAAG,KAAK,OAAO,GAAG,MAAM;AAAA,EACjF,CAAC;AACL;AACA,IAAM,eAAe,CAAC,UAAU;AAC5B,QAAM,aAAa,MAAM,MAAM,QAAQ;AACvC,QAAM,cAAc,CAAC;AACrB,QAAM,gBAAgB,CAAC;AACvB,MAAI,eAAe;AACnB,MAAI,oBAAoB,CAAC;AACzB,WAAS,YAAY,GAAG,YAAY,WAAW,QAAQ,aAAa;AAChE,UAAM,OAAO,WAAW,SAAS;AACjC,QAAI,SAAS,aAAa;AACtB;AAAA,IACJ;AACA,QAAI,eAAe,GAAG;AAClB,wBAAkB,KAAK,IAAI;AAAA,IAC/B,OACK;AACD,UAAI,kBAAkB,SAAS,GAAG;AAC9B,sBAAc,KAAK,kBAAkB,KAAK,EAAE,CAAC;AAC7C,oBAAY,KAAK,iBAAiB;AAClC,4BAAoB,CAAC;AAAA,MACzB;AACA,kBAAY,KAAK,IAAI;AAAA,IACzB;AACA,QAAI,SAAS,YAAY;AACrB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,kBAAkB,SAAS,GAAG;AAC9B,kBAAc,KAAK,kBAAkB,KAAK,EAAE,CAAC;AAC7C,gBAAY,KAAK,iBAAiB;AAAA,EACtC;AACA,QAAM,mBAAmB;AAAA,IACrB,eAAe,YAAY,KAAK,EAAE;AAAA,IAClC,QAAQ;AAAA,EACZ;AACA,SAAO;AACX;AACA,IAAM,8BAA8B,CAAC,aAAa;AAC9C,aAAW,SACN,QAAQ,qBAAqB,oBAAoB,EACjD,QAAQ,cAAc,aAAa,EACnC,QAAQ,iBAAiB,gBAAgB;AAC9C,SAAO;AACX;AACA,IAAM,mBAAmB,CAAC,SAAS,QAAQ,iBAAiB;AAExD,SAAO,QAAQ,QAAQ,QAAQ,IAAI,MAAM;AACrC,QAAI,EAAE,CAAC,GAAG;AACN,YAAM,QAAQ,EAAE,CAAC,EAAE,MAAM,GAAG;AAC5B,YAAM,IAAI,CAAC;AACX,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,cAAM,IAAI,MAAM,CAAC,EAAE,KAAK;AACxB,YAAI,CAAC;AACD;AACJ,UAAE,KAAK,aAAa,2BAA2B,GAAG,EAAE,CAAC,CAAC,CAAC;AAAA,MAC3D;AACA,aAAO,EAAE,KAAK,GAAG;AAAA,IACrB,OACK;AACD,aAAO,4BAA4B,EAAE,CAAC;AAAA,IAC1C;AAAA,EACJ,CAAC;AACL;AACA,IAAM,wBAAwB,CAAC,MAAM,MAAM,WAAW;AAClD,SAAO,OAAO,KAAK,QAAQ,eAAe,EAAE,IAAI;AACpD;AACA,IAAM,mBAAmB,CAAC,YAAY;AAClC,SAAO,iBAAiB,SAAS,iBAAiB,qBAAqB;AAC3E;AACA,IAAM,+BAA+B,CAAC,MAAM,MAAM,WAAW;AACzD,MAAI,KAAK,QAAQ,aAAa,IAAI,IAAI;AAClC,WAAO,sBAAsB,MAAM,MAAM,MAAM;AAAA,EACnD,OACK;AACD,WAAO,OAAO,OAAO,SAAS,OAAO,OAAO,MAAM,OAAO;AAAA,EAC7D;AACJ;AACA,IAAM,sBAAsB,CAAC,SAAS,gBAAgB;AAClD,QAAM,YAAY,MAAM,cAAc;AACtC,QAAM,YAAY,CAAC;AACnB,YAAU,QAAQ,QAAQ,oBAAoB,IAAI,MAAM;AACpD,QAAI,EAAE,CAAC,GAAG;AACN,YAAM,WAAW,EAAE,CAAC,EAAE,KAAK;AAC3B,YAAM,SAAS,EAAE,CAAC;AAClB,YAAM,kBAAkB,YAAY,WAAW;AAC/C,UAAI,iBAAiB;AACrB,eAAS,IAAI,EAAE,CAAC,IAAI,GAAG,KAAK,GAAG,KAAK;AAChC,cAAM,OAAO,EAAE,CAAC,EAAE,CAAC;AACnB,YAAI,SAAS,OAAO,SAAS,KAAK;AAC9B;AAAA,QACJ;AACA,yBAAiB,OAAO;AAAA,MAC5B;AACA,YAAM,cAAc,iBAAiB;AACrC,YAAM,gBAAgB,GAAG,eAAe,UAAU,CAAC,GAAG,gBAAgB,KAAK,CAAC;AAC5E,UAAI,YAAY,KAAK,MAAM,cAAc,KAAK,GAAG;AAC7C,cAAM,kBAAkB,GAAG,aAAa,KAAK,WAAW;AACxD,kBAAU,KAAK;AAAA,UACX;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL;AACA,aAAO;AAAA,IACX,OACK;AACD,aAAO,4BAA4B,EAAE,CAAC;AAAA,IAC1C;AAAA,EACJ,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAM,0BAA0B,CAAC,YAAY;AACzC,SAAO,iBAAiB,SAAS,wBAAwB,4BAA4B;AACzF;AACA,IAAM,4BAA4B,CAAC,YAAY;AAC3C,SAAO,sBAAsB,OAAO,CAAC,QAAQ,YAAY,OAAO,QAAQ,SAAS,GAAG,GAAG,OAAO;AAClG;AACA,IAAM,mBAAmB,CAACA,mBAAkB;AACxC,QAAM,MAAM;AACZ,QAAM,MAAM;AACZ,EAAAA,iBAAgBA,eAAc,QAAQ,KAAK,KAAK,EAAE,QAAQ,KAAK,KAAK;AACpE,SAAO,IAAI,OAAO,OAAOA,iBAAgB,MAAM,mBAAmB,GAAG;AACzE;AACA,IAAM,uBAAuB,CAAC,UAAUA,mBAAkB;AACtD,QAAM,KAAK,iBAAiBA,cAAa;AACzC,SAAO,CAAC,GAAG,KAAK,QAAQ;AAC5B;AACA,IAAM,wBAAwB,CAAC,UAAU,oBAAoB;AACzD,SAAO,SAAS,QAAQ,kBAAkB,CAAC,GAAG,SAAS,IAAI,aAAa,QAAQ,IAAI,QAAQ,OAAO;AAC/F,WAAO,SAAS,kBAAkB,QAAQ;AAAA,EAC9C,CAAC;AACL;AACA,IAAM,2BAA2B,CAAC,UAAUA,gBAAe,iBAAiB;AAExE,kBAAgB,YAAY;AAC5B,MAAI,gBAAgB,KAAK,QAAQ,GAAG;AAChC,UAAM,YAAY,IAAI,YAAY;AAClC,WAAO,SACF,QAAQ,6BAA6B,CAAC,GAAGC,cAAa,sBAAsBA,WAAU,SAAS,CAAC,EAChG,QAAQ,iBAAiB,YAAY,GAAG;AAAA,EACjD;AACA,SAAOD,iBAAgB,MAAM;AACjC;AACA,IAAM,2BAA2B,CAAC,UAAUA,gBAAe,iBAAiB;AACxE,QAAM,OAAO;AACb,EAAAA,iBAAgBA,eAAc,QAAQ,MAAM,CAAC,MAAM,UAAU,MAAM,CAAC,CAAC;AACrE,QAAM,YAAY,MAAMA;AACxB,QAAM,qBAAqB,CAAC,MAAM;AAC9B,QAAI,UAAU,EAAE,KAAK;AACrB,QAAI,CAAC,SAAS;AACV,aAAO;AAAA,IACX;AACA,QAAI,EAAE,QAAQ,yBAAyB,IAAI,IAAI;AAC3C,gBAAU,yBAAyB,GAAGA,gBAAe,YAAY;AAAA,IACrE,OACK;AAED,YAAM,IAAI,EAAE,QAAQ,iBAAiB,EAAE;AACvC,UAAI,EAAE,SAAS,GAAG;AACd,kBAAU,sBAAsB,GAAG,SAAS;AAAA,MAChD;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,QAAM,cAAc,aAAa,QAAQ;AACzC,aAAW,YAAY;AACvB,MAAI,iBAAiB;AACrB,MAAI,aAAa;AACjB,MAAI;AACJ,QAAM,MAAM;AAYZ,QAAM,UAAU,SAAS,QAAQ,yBAAyB,IAAI;AAE9D,MAAI,cAAc,CAAC;AACnB,UAAQ,MAAM,IAAI,KAAK,QAAQ,OAAO,MAAM;AACxC,UAAM,YAAY,IAAI,CAAC;AACvB,UAAME,QAAO,SAAS,MAAM,YAAY,IAAI,KAAK,EAAE,KAAK;AACxD,kBAAc,eAAeA,MAAK,QAAQ,yBAAyB,IAAI;AACvE,UAAM,aAAa,cAAc,mBAAmBA,KAAI,IAAIA;AAC5D,sBAAkB,GAAG,UAAU,IAAI,SAAS;AAC5C,iBAAa,IAAI;AAAA,EACrB;AACA,QAAM,OAAO,SAAS,UAAU,UAAU;AAC1C,gBAAc,eAAe,KAAK,QAAQ,yBAAyB,IAAI;AACvE,oBAAkB,cAAc,mBAAmB,IAAI,IAAI;AAE3D,SAAO,oBAAoB,YAAY,cAAc,cAAc;AACvE;AACA,IAAM,gBAAgB,CAAC,UAAU,mBAAmB,cAAc,iBAAiB;AAC/E,SAAO,SACF,MAAM,GAAG,EACT,IAAI,CAAC,gBAAgB;AACtB,QAAI,gBAAgB,YAAY,QAAQ,MAAM,YAAY,IAAI,IAAI;AAC9D,aAAO,YAAY,KAAK;AAAA,IAC5B;AACA,QAAI,qBAAqB,aAAa,iBAAiB,GAAG;AACtD,aAAO,yBAAyB,aAAa,mBAAmB,YAAY,EAAE,KAAK;AAAA,IACvF,OACK;AACD,aAAO,YAAY,KAAK;AAAA,IAC5B;AAAA,EACJ,CAAC,EACI,KAAK,IAAI;AAClB;AACA,IAAM,iBAAiB,CAAC,SAAS,mBAAmB,cAAc,cAAc,4BAA4B;AACxG,SAAO,aAAa,SAAS,CAAC,SAAS;AACnC,QAAI,WAAW,KAAK;AACpB,QAAI,UAAU,KAAK;AACnB,QAAI,KAAK,SAAS,CAAC,MAAM,KAAK;AAC1B,iBAAW,cAAc,KAAK,UAAU,mBAAmB,cAAc,YAAY;AAAA,IACzF,WACS,KAAK,SAAS,WAAW,QAAQ,KACtC,KAAK,SAAS,WAAW,WAAW,KACpC,KAAK,SAAS,WAAW,OAAO,KAChC,KAAK,SAAS,WAAW,WAAW,GAAG;AACvC,gBAAU,eAAe,KAAK,SAAS,mBAAmB,cAAc,YAAY;AAAA,IACxF;AACA,UAAM,UAAU;AAAA,MACZ,UAAU,SAAS,QAAQ,WAAW,GAAG,EAAE,KAAK;AAAA,MAChD;AAAA,IACJ;AACA,WAAO;AAAA,EACX,CAAC;AACL;AACA,IAAM,eAAe,CAAC,SAAS,SAAS,aAAa,aAAa,4BAA4B;AAC1F,YAAU,4BAA4B,OAAO;AAC7C,YAAU,iBAAiB,OAAO;AAClC,YAAU,wBAAwB,OAAO;AACzC,QAAM,UAAU,oBAAoB,SAAS,WAAW;AACxD,YAAU,QAAQ;AAClB,YAAU,0BAA0B,OAAO;AAC3C,MAAI,SAAS;AACT,cAAU,eAAe,SAAS,SAAS,aAAa,WAAW;AAAA,EACvE;AACA,YAAU,QAAQ,QAAQ,iCAAiC,IAAI,WAAW,EAAE;AAC5E,YAAU,QAAQ,QAAQ,wBAAwB,MAAM;AACxD,SAAO;AAAA,IACH,SAAS,QAAQ,KAAK;AAAA,IACtB,kBAAkB,QAAQ;AAAA,EAC9B;AACJ;AACA,IAAM,WAAW,CAAC,SAAS,SAAS,4BAA4B;AAC5D,QAAM,cAAc,UAAU;AAC9B,QAAM,cAAc,UAAU;AAC9B,QAAM,mBAAmB,wBAAwB,OAAO;AACxD,YAAU,cAAc,OAAO;AAC/B,QAAM,eAAe,CAAC;AACtB,MAAI,yBAAyB;AACzB,UAAM,2BAA2B,CAAC,SAAS;AACvC,YAAM,cAAc,UAAU,aAAa,MAAM;AACjD,YAAM,UAAU,OAAO,KAAK,QAAQ;AACpC,mBAAa,KAAK,EAAE,aAAa,QAAQ,CAAC;AAC1C,WAAK,WAAW,cAAc,KAAK;AACnC,aAAO;AAAA,IACX;AACA,cAAU,aAAa,SAAS,CAAC,SAAS;AACtC,UAAI,KAAK,SAAS,CAAC,MAAM,KAAK;AAC1B,eAAO,yBAAyB,IAAI;AAAA,MACxC,WACS,KAAK,SAAS,WAAW,QAAQ,KACtC,KAAK,SAAS,WAAW,WAAW,KACpC,KAAK,SAAS,WAAW,OAAO,KAChC,KAAK,SAAS,WAAW,WAAW,GAAG;AACvC,aAAK,UAAU,aAAa,KAAK,SAAS,wBAAwB;AAClE,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AACA,QAAM,SAAS,aAAa,SAAS,SAAS,aAAa,WAAW;AACtE,YAAU,CAAC,OAAO,SAAS,GAAG,gBAAgB,EAAE,KAAK,IAAI;AACzD,MAAI,yBAAyB;AACzB,iBAAa,QAAQ,CAAC,EAAE,aAAa,QAAQ,MAAM;AAC/C,gBAAU,QAAQ,QAAQ,aAAa,OAAO;AAAA,IAClD,CAAC;AAAA,EACL;AACA,SAAO,iBAAiB,QAAQ,CAAC,oBAAoB;AACjD,cAAU,QAAQ,QAAQ,gBAAgB,aAAa,gBAAgB,eAAe;AAAA,EAC1F,CAAC;AACD,SAAO;AACX;", "names": ["scopeSelector", "selector", "part"]}