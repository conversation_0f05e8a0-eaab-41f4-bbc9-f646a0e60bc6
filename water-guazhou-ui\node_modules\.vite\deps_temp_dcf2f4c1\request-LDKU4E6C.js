import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  e
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/core/workers/request.js
var s2;
function r(r2, a) {
  let n = a.responseType;
  n ? "array-buffer" !== n && "blob" !== n && "json" !== n && "native" !== n && "native-request-init" !== n && "text" !== n && (n = "text") : n = "json", a.responseType = n;
  const o = e(a.signal);
  return delete a.signal, globalThis.invokeStaticMessage("request", { url: r2, options: a }, { signal: o }).then(async (t) => {
    let i, l, u, c, p;
    if (t.data) if (t.data instanceof ArrayBuffer) {
      if (!("json" !== n && "text" !== n && "blob" !== n || (i = new Blob([t.data]), "json" !== n && "text" !== n || (s2 || (s2 = new FileReaderSync()), c = s2.readAsText(i), "json" !== n)))) {
        try {
          l = JSON.parse(c || null);
        } catch (b) {
          const t2 = { ...b, url: r2, requestOptions: a };
          throw new s("request:server", b.message, t2);
        }
        if (l.error) {
          const t2 = { ...l.error, url: r2, requestOptions: a };
          throw new s("request:server", l.error.message, t2);
        }
      }
    } else "native" === n && (t.data.signal = o, u = await fetch(t.data.url, t.data), t.httpStatus = u.status);
    switch (n) {
      case "blob":
        p = i;
        break;
      case "json":
        p = l;
        break;
      case "native":
        p = u;
        break;
      case "text":
        p = c;
        break;
      default:
        p = t.data;
    }
    return { data: p, httpStatus: t.httpStatus, requestOptions: a, ssl: t.ssl, url: r2 };
  });
}
export {
  r as execute
};
//# sourceMappingURL=request-LDKU4E6C.js.map
