import "./chunk-PLDDJCW6.js";

// node_modules/@arcgis/core/core/has.js
var e;
function has(a2) {
  return "function" == typeof e[a2] ? e[a2] = e[a2](globalThis) : e[a2];
}
var _a, _b, _c, _d;
e = ((_a = globalThis.dojoConfig) == null ? void 0 : _a.has) || ((_b = globalThis.esriConfig) == null ? void 0 : _b.has) ? { ...(_c = globalThis.dojoConfig) == null ? void 0 : _c.has, ...(_d = globalThis.esriConfig) == null ? void 0 : _d.has } : {}, has.add = (a2, o2, d2, r2) => ((r2 || void 0 === e[a2]) && (e[a2] = o2), d2 && has(a2)), has.cache = e, has.add("esri-deprecation-warnings", true), (() => {
  var _a3;
  has.add("host-webworker", void 0 !== globalThis.WorkerGlobalScope && self instanceof globalThis.WorkerGlobalScope);
  const e3 = "undefined" != typeof window && "undefined" != typeof location && "undefined" != typeof document && window.location === location && window.document === document;
  if (has.add("host-browser", e3), has.add("host-node", "object" == typeof globalThis.process && ((_a3 = globalThis.process.versions) == null ? void 0 : _a3.node) && globalThis.process.versions.v8), has.add("dom", e3), has("host-browser")) {
    const e4 = navigator, a2 = e4.userAgent, o2 = e4.appVersion, d2 = parseFloat(o2);
    if (has.add("wp", parseFloat(a2.split("Windows Phone")[1]) || void 0), has.add("msapp", parseFloat(a2.split("MSAppHost/")[1]) || void 0), has.add("khtml", o2.includes("Konqueror") ? d2 : void 0), has.add("edge", parseFloat(a2.split("Edge/")[1]) || void 0), has.add("opr", parseFloat(a2.split("OPR/")[1]) || void 0), has.add("webkit", !has("wp") && !has("edge") && parseFloat(a2.split("WebKit/")[1]) || void 0), has.add("chrome", !has("edge") && !has("opr") && parseFloat(a2.split("Chrome/")[1]) || void 0), has.add("android", !has("wp") && parseFloat(a2.split("Android ")[1]) || void 0), has.add("safari", !o2.includes("Safari") || has("wp") || has("chrome") || has("android") || has("edge") || has("opr") ? void 0 : parseFloat(o2.split("Version/")[1])), has.add("mac", o2.includes("Macintosh")), !has("wp") && a2.match(/(iPhone|iPod|iPad)/)) {
      const e5 = RegExp.$1.replace(/P/, "p"), o3 = a2.match(/OS ([\d_]+)/) ? RegExp.$1 : "1", d3 = parseFloat(o3.replace(/_/, ".").replace(/_/g, ""));
      has.add(e5, d3), has.add("ios", d3);
    }
    has("webkit") || (!a2.includes("Gecko") || has("wp") || has("khtml") || has("edge") || has.add("mozilla", d2), has("mozilla") && has.add("ff", parseFloat(a2.split("Firefox/")[1] || a2.split("Minefield/")[1]) || void 0));
  }
})(), (() => {
  if (globalThis.navigator) {
    const e3 = navigator.userAgent, a2 = /Android|webOS|iPhone|iPad|iPod|BlackBerry|Opera Mini|IEMobile/i.test(e3), o2 = /iPhone/i.test(e3);
    a2 && has.add("esri-mobile", a2), o2 && has.add("esri-iPhone", o2), has.add("esri-geolocation", !!navigator.geolocation);
  }
  has.add("esri-wasm", "WebAssembly" in globalThis), has.add("esri-shared-array-buffer", () => {
    const e3 = "SharedArrayBuffer" in globalThis, a2 = false === globalThis.crossOriginIsolated;
    return e3 && !a2;
  }), has.add("wasm-simd", () => {
    const e3 = [0, 97, 115, 109, 1, 0, 0, 0, 1, 5, 1, 96, 0, 1, 123, 3, 2, 1, 0, 10, 10, 1, 8, 0, 65, 0, 253, 15, 253, 98, 11];
    return WebAssembly.validate(new Uint8Array(e3));
  }), has.add("esri-atomics", "Atomics" in globalThis), has.add("esri-workers", "Worker" in globalThis), has.add("web-feat:cache", "caches" in globalThis), has.add("esri-workers-arraybuffer-transfer", !has("safari") || Number(has("safari")) >= 12), has.add("featurelayer-simplify-thresholds", [0.5, 0.5, 0.5, 0.5]), has.add("featurelayer-simplify-payload-size-factors", [1, 1, 4]), has.add("featurelayer-snapshot-enabled", true), has.add("featurelayer-snapshot-point-min-threshold", 8e4), has.add("featurelayer-snapshot-point-max-threshold", 4e5), has.add("featurelayer-snapshot-point-coverage", 0.1), has.add("featurelayer-advanced-symbols", false), has.add("featurelayer-pbf", true), has.add("featurelayer-pbf-statistics", false), has.add("feature-layers-workers", true), has.add("feature-polyline-generalization-factor", 1), has.add("mapview-transitions-duration", 200), has.add("mapview-srswitch-adjust-rotation-scale-threshold", 24e6), has.add("mapserver-pbf-version-support", 10.81), has.add("mapservice-popup-identify-max-tolerance", 20), has.add("heatmap-allow-raster-fallback", true), has.add("heatmap-force-raster", false), has("host-webworker") || has("host-browser") && (has.add("esri-csp-restrictions", () => {
    try {
      new Function();
    } catch {
      return true;
    }
    return false;
  }), has.add("esri-image-decode", () => {
    if ("decode" in new Image()) {
      const e3 = new Image();
      return e3.src = 'data:image/svg+xml;charset=UTF-8,<svg version="1.1" xmlns="http://www.w3.org/2000/svg"></svg>', void e3.decode().then(() => {
        has.add("esri-image-decode", true, true, true);
      }).catch(() => {
        has.add("esri-image-decode", false, true, true);
      });
    }
    return false;
  }), has.add("esri-url-encodes-apostrophe", () => {
    const e3 = window.document.createElement("a");
    return e3.href = "?'", e3.href.includes("?%27");
  }));
})();

// node_modules/@arcgis/core/core/maybe.js
function r(n3) {
  return null != n3;
}

// node_modules/@arcgis/core/core/RandomLCG.js
var t = class _t {
  constructor(t3 = 1) {
    this._seed = t3;
  }
  set seed(e3) {
    this._seed = e3 ?? Math.random() * _t._m;
  }
  getInt() {
    return this._seed = (_t._a * this._seed + _t._c) % _t._m, this._seed;
  }
  getFloat() {
    return this.getInt() / (_t._m - 1);
  }
  getIntRange(t3, e3) {
    return Math.round(this.getFloatRange(t3, e3));
  }
  getFloatRange(e3, s3) {
    const n3 = s3 - e3;
    return e3 + this.getInt() / _t._m * n3;
  }
};
t._m = 2147483647, t._a = 48271, t._c = 0;

// node_modules/@arcgis/core/core/arrayUtils.js
var d = !!Array.prototype.fill;
var x = class {
  constructor() {
    this.last = 0;
  }
};
var y = new x();
var C = new t();

// node_modules/@arcgis/core/core/typedArrayUtil.js
function n(r2) {
  return r2 && r2.constructor && "Int8Array" === r2.constructor.name;
}
function o(r2) {
  return r2 && r2.constructor && "Uint8Array" === r2.constructor.name;
}
function c(r2) {
  return r2 && r2.constructor && "Uint8ClampedArray" === r2.constructor.name;
}
function u(r2) {
  return r2 && r2.constructor && "Int16Array" === r2.constructor.name;
}
function e2(r2) {
  return r2 && r2.constructor && "Uint16Array" === r2.constructor.name;
}
function a(r2) {
  return r2 && r2.constructor && "Int32Array" === r2.constructor.name;
}
function s(r2) {
  return r2 && r2.constructor && "Uint32Array" === r2.constructor.name;
}
function f(r2) {
  return r2 && r2.constructor && "Float32Array" === r2.constructor.name;
}
function i(r2) {
  return r2 && r2.constructor && "Float64Array" === r2.constructor.name;
}

// node_modules/@arcgis/core/core/lang.js
function p(t3) {
  if (!t3 || "object" != typeof t3 || "function" == typeof t3) return t3;
  const e3 = j(t3);
  if (r(e3)) return e3;
  if (m(t3)) return t3.clone();
  if (b(t3)) return t3.map(p);
  if (g(t3)) return t3.clone();
  const r2 = {};
  for (const n3 of Object.getOwnPropertyNames(t3)) r2[n3] = p(t3[n3]);
  return r2;
}
function m(t3) {
  return "function" == typeof t3.clone;
}
function b(t3) {
  return "function" == typeof t3.map && "function" == typeof t3.forEach;
}
function g(t3) {
  return "function" == typeof t3.notifyChange && "function" == typeof t3.watch;
}
function j(t3) {
  if (n(t3) || o(t3) || c(t3) || u(t3) || e2(t3) || a(t3) || s(t3) || f(t3) || i(t3)) return t3.slice();
  if (t3 instanceof Date) return new Date(t3.getTime());
  if (t3 instanceof ArrayBuffer) {
    return t3.slice(0, t3.byteLength);
  }
  if (t3 instanceof Map) {
    const n3 = /* @__PURE__ */ new Map();
    for (const [e3, r2] of t3) n3.set(e3, p(r2));
    return n3;
  }
  if (t3 instanceof Set) {
    const n3 = /* @__PURE__ */ new Set();
    for (const e3 of t3) n3.add(p(e3));
    return n3;
  }
  return null;
}

// node_modules/@arcgis/core/core/object.js
function n2(r2, n3, t3 = false) {
  return i3(r2, n3, t3);
}
function i3(n3, t3, o2) {
  return t3 ? Object.keys(t3).reduce((n4, e3) => {
    let u2 = n4[e3], c2 = t3[e3];
    return u2 === c2 ? n4 : void 0 === u2 ? (n4[e3] = p(c2), n4) : (Array.isArray(c2) || Array.isArray(n4) ? (u2 = u2 ? Array.isArray(u2) ? n4[e3] = u2.concat() : n4[e3] = [u2] : n4[e3] = [], c2 && (Array.isArray(c2) || (c2 = [c2]), o2 ? c2.forEach((r2) => {
      u2.includes(r2) || u2.push(r2);
    }) : n4[e3] = c2.concat())) : c2 && "object" == typeof c2 ? n4[e3] = i3(u2, c2, o2) : n4.hasOwnProperty(e3) && !t3.hasOwnProperty(e3) || (n4[e3] = c2), n4);
  }, n3 || {}) : n3;
}

// node_modules/@arcgis/core/config.js
var _a2;
var s2 = { analysisTheme: { accentColor: [255, 128, 0], textColor: "white" }, apiKey: void 0, applicationUrl: (_a2 = globalThis.location) == null ? void 0 : _a2.href, assetsPath: "", fontsUrl: "https://static.arcgis.com/fonts", geometryServiceUrl: "https://utility.arcgisonline.com/arcgis/rest/services/Geometry/GeometryServer", geoRSSServiceUrl: "https://utility.arcgis.com/sharing/rss", kmlServiceUrl: "https://utility.arcgis.com/sharing/kml", userPrivilegesApplied: false, portalUrl: "https://www.arcgis.com", routeServiceUrl: "https://route-api.arcgis.com/arcgis/rest/services/World/Route/NAServer/Route_World", workers: { loaderConfig: { has: {}, paths: {}, map: {}, packages: [] } }, request: { crossOriginNoCorsDomains: null, httpsDomains: ["arcgis.com", "arcgisonline.com", "esrikr.com", "premiumservices.blackbridge.com", "esripremium.accuweather.com", "gbm.digitalglobe.com", "firstlook.digitalglobe.com", "msi.digitalglobe.com"], interceptors: [], maxUrlLength: 2e3, priority: "high", proxyRules: [], proxyUrl: null, timeout: 6e4, trustedServers: [], useIdentity: true }, log: { interceptors: [], level: null } };
if (globalThis.esriConfig && (n2(s2, globalThis.esriConfig, true), delete s2.has), !s2.assetsPath) {
  {
    const e3 = "4.26.5";
    s2.assetsPath = `https://js.arcgis.com/${e3.slice(0, -2)}/@arcgis/core/assets`;
  }
  s2.defaultAssetsPath = s2.assetsPath;
}
export {
  s2 as default
};
//# sourceMappingURL=@arcgis_core_config.js.map
