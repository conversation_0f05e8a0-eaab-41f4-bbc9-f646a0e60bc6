import {
  L as L2,
  p as p5
} from "./chunk-JFOWKXSG.js";
import {
  o as o3
} from "./chunk-GTPQ7RHL.js";
import {
  o as o2
} from "./chunk-MWEMWROT.js";
import {
  j as j2,
  p as p3
} from "./chunk-IEIKQ72S.js";
import "./chunk-3IDKVHSA.js";
import {
  e as e2
} from "./chunk-WJWRKQWS.js";
import {
  p as p4
} from "./chunk-JV6TBH5W.js";
import {
  _
} from "./chunk-TV6WKLTU.js";
import "./chunk-5VDORDNW.js";
import {
  c
} from "./chunk-FHKOFAQ2.js";
import {
  O as O2
} from "./chunk-XGD5S6QR.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-P37TUI4J.js";
import {
  n
} from "./chunk-LAEW33J6.js";
import {
  t as t2
} from "./chunk-NGPCXWDX.js";
import "./chunk-IXMTA2RC.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-67MHB3E3.js";
import "./chunk-MKG56XBH.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-YJWWP4AU.js";
import "./chunk-EQYLMJEJ.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-VTOVDEW2.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import {
  U as U2
} from "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w3
} from "./chunk-XTO3XXZ3.js";
import {
  U,
  a as a2,
  l as l2
} from "./chunk-QUHG7NMD.js";
import "./chunk-XVA5SA7P.js";
import {
  A,
  L
} from "./chunk-U4SVMKOQ.js";
import {
  w as w2
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  p2,
  r as r3
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-7SWS36OI.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  O,
  w
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  p,
  t
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/TileInfoTilemapCache.js
var l3 = class {
  constructor(i2, l5 = 0, a3 = i2.lods.length - 1) {
    this.tileInfo = i2, this.minLOD = l5, this.maxLOD = a3;
  }
  getAvailability(i2, l5, a3) {
    var _a;
    const t3 = (_a = this.tileInfo) == null ? void 0 : _a.lodAt(i2);
    return !t3 || i2 < this.minLOD || i2 > this.maxLOD ? "unavailable" : t3.cols && t3.rows ? a3 >= t3.cols[0] && a3 <= t3.cols[1] && l5 >= t3.rows[0] && l5 <= t3.rows[1] ? "available" : "unavailable" : "available";
  }
  async fetchAvailability(l5, a3, t3, e3) {
    return await O(e3), this.getAvailability(l5, a3, t3);
  }
  async fetchAvailabilityUpsample(l5, a3, t3, e3, s2) {
    await O(s2), e3.level = l5, e3.row = a3, e3.col = t3;
    const o5 = this.tileInfo;
    for (o5.updateTileInfo(e3); ; ) {
      const i2 = this.getAvailability(e3.level, e3.row, e3.col);
      if ("unavailable" !== i2) return i2;
      if (!o5.upsampleTile(e3)) return "unavailable";
    }
  }
};

// node_modules/@arcgis/core/layers/support/TileMatrixSet.js
var l4;
var n2 = l4 = class extends l {
  constructor(t3) {
    super(t3), this.fullExtent = null, this.id = null, this.tileInfo = null;
  }
  clone() {
    const t3 = new l4();
    return this.hasOwnProperty("fullExtent") && (t3.fullExtent = this.fullExtent && this.fullExtent.clone()), this.hasOwnProperty("id") && (t3.id = this.id), this.hasOwnProperty("tileInfo") && (t3.tileInfo = this.tileInfo && this.tileInfo.clone()), t3;
  }
};
e([y({ type: w3, json: { read: { source: "fullExtent" } } })], n2.prototype, "fullExtent", void 0), e([y({ type: String, json: { read: { source: "id" } } })], n2.prototype, "id", void 0), e([y({ type: j2, json: { read: { source: "tileInfo" } } })], n2.prototype, "tileInfo", void 0), n2 = l4 = e([a("esri.layer.support.TileMatrixSet")], n2);
var p6 = n2;

// node_modules/@arcgis/core/layers/support/WMTSStyle.js
var o4;
var i = o4 = class extends l {
  constructor(r4) {
    super(r4), this.id = null, this.title = null, this.description = null, this.legendUrl = null;
  }
  clone() {
    const r4 = new o4();
    return this.hasOwnProperty("description") && (r4.description = this.description), this.hasOwnProperty("id") && (r4.id = this.id), this.hasOwnProperty("isDefault") && (r4.isDefault = this.isDefault), this.hasOwnProperty("keywords") && (r4.keywords = this.keywords && this.keywords.slice()), this.hasOwnProperty("legendUrl") && (r4.legendUrl = this.legendUrl), this.hasOwnProperty("title") && (r4.title = this.title), r4;
  }
};
e([y({ json: { read: { source: "id" } } })], i.prototype, "id", void 0), e([y({ json: { read: { source: "title" } } })], i.prototype, "title", void 0), e([y({ json: { read: { source: "abstract" } } })], i.prototype, "description", void 0), e([y({ json: { read: { source: "legendUrl" } } })], i.prototype, "legendUrl", void 0), e([y({ json: { read: { source: "isDefault" } } })], i.prototype, "isDefault", void 0), e([y({ json: { read: { source: "keywords" } } })], i.prototype, "keywords", void 0), i = o4 = e([a("esri.layer.support.WMTSStyle")], i);
var p7 = i;

// node_modules/@arcgis/core/layers/support/WMTSSublayer.js
var p8;
var m = p8 = class extends l {
  constructor(t3) {
    super(t3), this.fullExtent = null, this.fullExtents = null, this.imageFormats = null, this.id = null, this.layer = null, this.styles = null, this.tileMatrixSetId = null, this.tileMatrixSets = null;
  }
  get description() {
    return this._get("description");
  }
  set description(t3) {
    this._set("description", t3);
  }
  readFullExtent(t3, e3) {
    return (t3 = e3.fullExtent) ? w3.fromJSON(t3) : null;
  }
  readFullExtents(t3, e3) {
    var _a, _b;
    return ((_a = e3.fullExtents) == null ? void 0 : _a.length) ? e3.fullExtents.map((t4) => w3.fromJSON(t4)) : ((_b = e3.tileMatrixSets) == null ? void 0 : _b.map((t4) => w3.fromJSON(t4.fullExtent)).filter((t4) => t4)) ?? [];
  }
  get imageFormat() {
    let t3 = this._get("imageFormat");
    return t3 || (t3 = this.imageFormats && this.imageFormats.length ? this.imageFormats[0] : ""), t3;
  }
  set imageFormat(t3) {
    const e3 = this.imageFormats;
    t3 && (t3.includes("image/") || e3 && !e3.includes(t3)) && (t3.includes("image/") || (t3 = "image/" + t3), e3 && !e3.includes(t3)) ? console.error("The layer doesn't support the format of " + t3) : this._set("imageFormat", t3);
  }
  get styleId() {
    var _a;
    let t3 = this._get("styleId");
    return t3 || (t3 = ((_a = this.styles) == null ? void 0 : _a.length) ? this.styles.getItemAt(0).id : ""), t3;
  }
  set styleId(t3) {
    this._set("styleId", t3);
  }
  get title() {
    return this._get("title");
  }
  set title(t3) {
    this._set("title", t3);
  }
  get tileMatrixSet() {
    return this.tileMatrixSets ? this.tileMatrixSets.find((t3) => t3.id === this.tileMatrixSetId) : null;
  }
  clone() {
    var _a;
    const t3 = new p8();
    return this.hasOwnProperty("description") && (t3.description = this.description), this.hasOwnProperty("imageFormats") && (t3.imageFormats = this.imageFormats && this.imageFormats.slice()), this.hasOwnProperty("imageFormat") && (t3.imageFormat = this.imageFormat), this.hasOwnProperty("fullExtent") && (t3.fullExtent = this.fullExtent && this.fullExtent.clone()), this.hasOwnProperty("id") && (t3.id = this.id), this.hasOwnProperty("layer") && (t3.layer = this.layer), this.hasOwnProperty("styleId") && (t3.styleId = this.styleId), this.hasOwnProperty("styles") && (t3.styles = this.styles && this.styles.clone()), this.hasOwnProperty("tileMatrixSetId") && (t3.tileMatrixSetId = this.tileMatrixSetId), this.hasOwnProperty("tileMatrixSets") && (t3.tileMatrixSets = (_a = this.tileMatrixSets) == null ? void 0 : _a.clone()), this.hasOwnProperty("title") && (t3.title = this.title), t3;
  }
};
e([y()], m.prototype, "description", null), e([y()], m.prototype, "fullExtent", void 0), e([o("fullExtent", ["fullExtent"])], m.prototype, "readFullExtent", null), e([y({ readOnly: true })], m.prototype, "fullExtents", void 0), e([o("fullExtents", ["fullExtents", "tileMatrixSets"])], m.prototype, "readFullExtents", null), e([y()], m.prototype, "imageFormat", null), e([y({ json: { read: { source: "formats" } } })], m.prototype, "imageFormats", void 0), e([y()], m.prototype, "id", void 0), e([y()], m.prototype, "layer", void 0), e([y()], m.prototype, "styleId", null), e([y({ type: j.ofType(p7), json: { read: { source: "styles" } } })], m.prototype, "styles", void 0), e([y({ value: null, json: { write: { ignoreOrigin: true } } })], m.prototype, "title", null), e([y()], m.prototype, "tileMatrixSetId", void 0), e([y({ readOnly: true })], m.prototype, "tileMatrixSet", null), e([y({ type: j.ofType(p6), json: { read: { source: "tileMatrixSets" } } })], m.prototype, "tileMatrixSets", void 0), m = p8 = e([a("esri.layers.support.WMTSSublayer")], m);
var u = m;

// node_modules/@arcgis/core/layers/support/wmtsUtils.js
var u2 = 90.71428571428571;
function p9(t3) {
  const n3 = t3.replace(/ows:/gi, "");
  if (!g("Contents", new DOMParser().parseFromString(n3, "text/xml").documentElement)) throw new s("wmtslayer:wmts-capabilities-xml-is-not-valid", "the wmts get capabilities response is not compliant", { text: t3 });
}
function f(t3, n3) {
  var _a, _b;
  t3 = t3.replace(/ows:/gi, "");
  const i2 = new DOMParser().parseFromString(t3, "text/xml").documentElement, r4 = /* @__PURE__ */ new Map(), o5 = /* @__PURE__ */ new Map(), l5 = g("Contents", i2);
  if (!l5) throw new s("wmtslayer:wmts-capabilities-xml-is-not-valid");
  const s2 = (_a = g("OperationsMetadata", i2)) == null ? void 0 : _a.querySelector("[name='GetTile']"), a3 = s2 == null ? void 0 : s2.getElementsByTagName("Get"), c2 = a3 && Array.prototype.slice.call(a3), u3 = (_b = n3.url) == null ? void 0 : _b.indexOf("https"), p10 = void 0 !== u3 && u3 > -1;
  let f2, d2, m3 = n3.serviceMode, h2 = n3 == null ? void 0 : n3.url;
  if (c2 && c2.length && c2.some((e3) => {
    const t4 = g("Constraint", e3);
    return !t4 || C("AllowedValues", "Value", m3, t4) ? (h2 = e3.attributes[0].nodeValue, true) : (!t4 || C("AllowedValues", "Value", "RESTful", t4) || C("AllowedValues", "Value", "REST", t4) ? d2 = e3.attributes[0].nodeValue : t4 && !C("AllowedValues", "Value", "KVP", t4) || (f2 = e3.attributes[0].nodeValue), false);
  }), !h2) if (d2) h2 = d2, m3 = "RESTful";
  else if (f2) h2 = f2, m3 = "KVP";
  else {
    const e3 = g("ServiceMetadataURL", i2);
    h2 = e3 == null ? void 0 : e3.getAttribute("xlink:href");
  }
  const T2 = h2.indexOf("1.0.0/");
  -1 === T2 && "RESTful" === m3 ? h2 += "/" : T2 > -1 && (h2 = h2.substring(0, T2)), "KVP" === m3 && (h2 += T2 > -1 ? "" : "?"), p10 && (h2 = h2.replace(/^http:/i, "https:"));
  const y3 = w4("ServiceIdentification>ServiceTypeVersion", i2), R2 = w4("ServiceIdentification>AccessConstraints", i2), S2 = R2 && /^none$/i.test(R2) ? null : R2, V3 = x("Layer", l5), b3 = x("TileMatrixSet", l5), E2 = V3.map((e3) => {
    const t4 = w4("Identifier", e3);
    return r4.set(t4, e3), M(t4, e3, b3, p10, y3);
  });
  return { copyright: S2, dimensionMap: o5, layerMap: r4, layers: E2, serviceMode: m3, tileUrl: h2 };
}
function d(e3) {
  return e3.layers.forEach((e4) => {
    var _a;
    (_a = e4.tileMatrixSets) == null ? void 0 : _a.forEach((e5) => {
      var _a2;
      const t3 = e5.tileInfo;
      t3 && 96 !== t3.dpi && ((_a2 = t3.lods) == null ? void 0 : _a2.forEach((n3) => {
        var _a3;
        n3.scale = 96 * n3.scale / t3.dpi, n3.resolution = j3((_a3 = t3.spatialReference) == null ? void 0 : _a3.wkid, n3.scale * u2 / 96, e5.id);
      }), t3.dpi = 96);
    });
  }), e3;
}
function m2(e3) {
  return e3.nodeType === Node.ELEMENT_NODE;
}
function g(e3, t3) {
  for (let n3 = 0; n3 < t3.childNodes.length; n3++) {
    const i2 = t3.childNodes[n3];
    if (m2(i2) && i2.nodeName === e3) return i2;
  }
  return null;
}
function x(e3, t3) {
  const n3 = [];
  for (let i2 = 0; i2 < t3.childNodes.length; i2++) {
    const r4 = t3.childNodes[i2];
    m2(r4) && r4.nodeName === e3 && n3.push(r4);
  }
  return n3;
}
function h(e3, n3) {
  const i2 = [];
  for (let t3 = 0; t3 < n3.childNodes.length; t3++) {
    const r4 = n3.childNodes[t3];
    m2(r4) && r4.nodeName === e3 && i2.push(r4);
  }
  return i2.map((e4) => e4.textContent).filter(r);
}
function w4(e3, t3) {
  return e3.split(">").forEach((e4) => {
    t3 && (t3 = g(e4, t3));
  }), t3 && t3.textContent;
}
function C(e3, t3, n3, i2) {
  let r4;
  return Array.prototype.slice.call(i2.childNodes).some((i3) => {
    if (i3.nodeName.includes(e3)) {
      const e4 = g(t3, i3), o5 = e4 && e4.textContent;
      if (o5 === n3 || n3.split(":") && n3.split(":")[1] === o5) return r4 = i3, true;
    }
    return false;
  }), r4;
}
function M(e3, t3, n3, i2, r4) {
  const o5 = w4("Abstract", t3), l5 = h("Format", t3);
  return { id: e3, fullExtent: V(t3), fullExtents: b2(t3), description: o5, formats: l5, styles: E(t3, i2), title: w4("Title", t3), tileMatrixSets: L3(r4, t3, n3) };
}
function T(e3, t3) {
  var _a;
  const n3 = [], i2 = (_a = e3.layerMap) == null ? void 0 : _a.get(t3);
  if (!i2) return null;
  const r4 = x("ResourceURL", i2), o5 = x("Dimension", i2);
  let l5, s2, a3, c2;
  return o5.length && (l5 = w4("Identifier", o5[0]), s2 = h("Default", o5[0]) || h("Value", o5[0])), o5.length > 1 && (a3 = w4("Identifier", o5[1]), c2 = h("Default", o5[1]) || h("Value", o5[1])), e3.dimensionMap.set(t3, { dimensions: s2, dimensions2: c2 }), r4.forEach((e4) => {
    let t4 = e4.getAttribute("template");
    if ("tile" === e4.getAttribute("resourceType")) {
      if (l5 && s2.length) if (t4.includes("{" + l5 + "}")) t4 = t4.replace("{" + l5 + "}", "{dimensionValue}");
      else {
        const e5 = t4.toLowerCase().indexOf("{" + l5.toLowerCase() + "}");
        e5 > -1 && (t4 = t4.substring(0, e5) + "{dimensionValue}" + t4.substring(e5 + l5.length + 2));
      }
      if (a3 && c2.length) if (t4.includes("{" + a3 + "}")) t4 = t4.replace("{" + a3 + "}", "{dimensionValue2}");
      else {
        const e5 = t4.toLowerCase().indexOf("{" + a3.toLowerCase() + "}");
        e5 > -1 && (t4 = t4.substring(0, e5) + "{dimensionValue2}" + t4.substring(e5 + a3.length + 2));
      }
      n3.push({ template: t4, format: e4.getAttribute("format"), resourceType: "tile" });
    }
  }), n3;
}
function y2(e3, t3, n3, i2, r4, o5, l5, s2) {
  var _a, _b;
  const a3 = R(e3, t3, i2);
  if (!((a3 == null ? void 0 : a3.length) > 0)) return "";
  const { dimensionMap: c2 } = e3, u3 = (_a = c2.get(t3).dimensions) == null ? void 0 : _a[0], p10 = (_b = c2.get(t3).dimensions2) == null ? void 0 : _b[0];
  return a3[l5 % a3.length].template.replace(/\{Style\}/gi, r4 ?? "").replace(/\{TileMatrixSet\}/gi, n3 ?? "").replace(/\{TileMatrix\}/gi, o5).replace(/\{TileRow\}/gi, "" + l5).replace(/\{TileCol\}/gi, "" + s2).replace(/\{dimensionValue\}/gi, u3).replace(/\{dimensionValue2\}/gi, p10);
}
function R(e3, t3, n3) {
  const i2 = T(e3, t3), r4 = i2 == null ? void 0 : i2.filter((e4) => e4.format === n3);
  return ((r4 == null ? void 0 : r4.length) ? r4 : i2) ?? [];
}
function S(e3, t3, n3, i2) {
  const { dimensionMap: r4 } = e3, o5 = T(e3, t3);
  let l5 = "";
  if (o5 && o5.length > 0) {
    const e4 = r4.get(t3).dimensions && r4.get(t3).dimensions[0], s2 = r4.get(t3).dimensions2 && r4.get(t3).dimensions2[0];
    l5 = o5[0].template, l5.indexOf(".xxx") === l5.length - 4 && (l5 = l5.slice(0, l5.length - 4)), l5 = l5.replace(/\{Style\}/gi, i2), l5 = l5.replace(/\{TileMatrixSet\}/gi, n3), l5 = l5.replace(/\{TileMatrix\}/gi, "{level}"), l5 = l5.replace(/\{TileRow\}/gi, "{row}"), l5 = l5.replace(/\{TileCol\}/gi, "{col}"), l5 = l5.replace(/\{dimensionValue\}/gi, e4), l5 = l5.replace(/\{dimensionValue2\}/gi, s2);
  }
  return l5;
}
function V(e3) {
  const t3 = g("WGS84BoundingBox", e3), n3 = t3 ? w4("LowerCorner", t3).split(" ") : ["-180", "-90"], i2 = t3 ? w4("UpperCorner", t3).split(" ") : ["180", "90"];
  return { xmin: parseFloat(n3[0]), ymin: parseFloat(n3[1]), xmax: parseFloat(i2[0]), ymax: parseFloat(i2[1]), spatialReference: { wkid: 4326 } };
}
function b2(e3) {
  const t3 = [];
  return o2(e3, { BoundingBox: (e4) => {
    if (!e4.getAttribute("crs")) return;
    const n3 = e4.getAttribute("crs").toLowerCase(), i2 = N(n3), r4 = n3.includes("epsg") && o3(i2.wkid);
    let o5, a3, c2, u3;
    o2(e4, { LowerCorner: (e5) => {
      [o5, a3] = e5.textContent.split(" ").map((e6) => Number.parseFloat(e6)), r4 && ([o5, a3] = [a3, o5]);
    }, UpperCorner: (e5) => {
      [c2, u3] = e5.textContent.split(" ").map((e6) => Number.parseFloat(e6)), r4 && ([c2, u3] = [u3, c2]);
    } }), t3.push({ xmin: o5, ymin: a3, xmax: c2, ymax: u3, spatialReference: i2 });
  } }), t3;
}
function E(e3, t3) {
  return x("Style", e3).map((e4) => {
    const n3 = g("LegendURL", e4), i2 = g("Keywords", e4), r4 = i2 ? h("Keyword", i2) : [];
    let o5 = n3 && n3.getAttribute("xlink:href");
    t3 && (o5 = o5 && o5.replace(/^http:/i, "https:"));
    return { abstract: w4("Abstract", e4), id: w4("Identifier", e4), isDefault: "true" === e4.getAttribute("isDefault"), keywords: r4, legendUrl: o5, title: w4("Title", e4) };
  });
}
function L3(e3, t3, n3) {
  return x("TileMatrixSetLink", t3).map((t4) => I(e3, t4, n3));
}
function I(e3, t3, n3) {
  const i2 = g("TileMatrixSet", t3).textContent, r4 = h("TileMatrix", t3), o5 = n3.find((e4) => {
    const t4 = g("Identifier", e4), n4 = t4 && t4.textContent;
    return !!(n4 === i2 || i2.split(":") && i2.split(":")[1] === n4);
  }), l5 = g("TileMatrixSetLimits", t3), s2 = l5 && x("TileMatrixLimits", l5), a3 = /* @__PURE__ */ new Map();
  if (s2 == null ? void 0 : s2.length) for (const c2 of s2) {
    const e4 = g("TileMatrix", c2).textContent, t4 = +g("MinTileRow", c2).textContent, n4 = +g("MaxTileRow", c2).textContent, i3 = +g("MinTileCol", c2).textContent, r5 = +g("MaxTileCol", c2).textContent;
    a3.set(e4, { minCol: i3, maxCol: r5, minRow: t4, maxRow: n4 });
  }
  const u3 = w4("SupportedCRS", o5).toLowerCase(), p10 = A2(o5, u3), f2 = p10.spatialReference, d2 = g("TileMatrix", o5), m3 = [parseInt(w4("TileWidth", d2), 10), parseInt(w4("TileHeight", d2), 10)], M2 = [];
  if (r4.length) r4.forEach((e4, t4) => {
    const n4 = C("TileMatrix", "Identifier", e4, o5);
    M2.push(k(n4, u3, t4, i2, a3));
  });
  else {
    x("TileMatrix", o5).forEach((e4, t4) => {
      M2.push(k(e4, u3, t4, i2, a3));
    });
  }
  const T2 = F(e3, o5, p10, m3, M2[0]).toJSON(), y3 = new j2({ dpi: 96, spatialReference: f2, size: m3, origin: p10, lods: M2 }).toJSON();
  return { id: i2, fullExtent: T2, tileInfo: y3 };
}
function N(e3) {
  e3 = e3.toLowerCase();
  let n3 = parseInt(e3.split(":").pop(), 10);
  900913 !== n3 && 3857 !== n3 || (n3 = 102100);
  const i2 = U3(e3);
  return r(i2) && (n3 = i2), { wkid: n3 };
}
function A2(e3, t3) {
  return v(g("TileMatrix", e3), t3);
}
function v(e3, t3) {
  const n3 = N(t3), [i2, o5] = w4("TopLeftCorner", e3).split(" ").map((e4) => parseFloat(e4)), s2 = t3.includes("epsg") && o3(n3.wkid);
  return new w2(s2 ? { x: o5, y: i2, spatialReference: n3 } : { x: i2, y: o5, spatialReference: n3 });
}
function F(e3, t3, n3, r4, o5) {
  const l5 = g("BoundingBox", t3);
  let s2, a3, c2, u3, p10, f2;
  if (l5 && (s2 = w4("LowerCorner", l5).split(" "), a3 = w4("UpperCorner", l5).split(" ")), s2 && s2.length > 1 && a3 && a3.length > 1) c2 = parseFloat(s2[0]), p10 = parseFloat(s2[1]), u3 = parseFloat(a3[0]), f2 = parseFloat(a3[1]);
  else {
    const e4 = g("TileMatrix", t3), i2 = parseInt(w4("MatrixWidth", e4), 10), l6 = parseInt(w4("MatrixHeight", e4), 10);
    c2 = n3.x, f2 = n3.y, u3 = c2 + i2 * r4[0] * o5.resolution, p10 = f2 - l6 * r4[1] * o5.resolution;
  }
  return O3(e3, n3.spatialReference, n3) ? new w3(p10, c2, f2, u3, n3.spatialReference) : new w3(c2, p10, u3, f2, n3.spatialReference);
}
function O3(e3, t3, n3) {
  return "1.0.0" === e3 && o3(t3.wkid) && !(n3.spatialReference.isGeographic && n3.x < -90 && n3.y >= -90);
}
var D;
function U3(e3) {
  return e3.includes("crs84") || e3.includes("crs:84") ? D.CRS84 : e3.includes("crs83") || e3.includes("crs:83") ? D.CRS83 : e3.includes("crs27") || e3.includes("crs:27") ? D.CRS27 : null;
}
function k(e3, t3, n3, i2, r4) {
  const o5 = N(t3), l5 = w4("Identifier", e3);
  let s2 = parseFloat(w4("ScaleDenominator", e3));
  const c2 = j3(o5.wkid, s2, i2);
  s2 *= 96 / u2;
  const p10 = +w4("MatrixWidth", e3), f2 = +w4("MatrixHeight", e3), { maxCol: d2 = p10 - 1, maxRow: m3 = f2 - 1, minCol: g2 = 0, minRow: x2 = 0 } = r4.get(l5) ?? {}, { x: h2, y: C2 } = v(e3, t3);
  return new p3({ cols: [g2, d2], level: n3, levelValue: l5, origin: [h2, C2], scale: s2, resolution: c2, rows: [x2, m3] });
}
function j3(e3, t3, i2) {
  let r4;
  return r4 = r3.hasOwnProperty("" + e3) ? r3.values[r3[e3]] : "default028mm" === i2 ? 6370997 * Math.PI / 180 : p2(e3).metersPerDegree, 7 * t3 / 25e3 / r4;
}
!function(e3) {
  e3[e3.CRS84 = 4326] = "CRS84", e3[e3.CRS83 = 4269] = "CRS83", e3[e3.CRS27 = 4267] = "CRS27";
}(D || (D = {}));

// node_modules/@arcgis/core/layers/WMTSLayer.js
var A3 = { "image/png": ".png", "image/png8": ".png", "image/png24": ".png", "image/png32": ".png", "image/jpg": ".jpg", "image/jpeg": ".jpeg", "image/gif": ".gif", "image/bmp": ".bmp", "image/tiff": ".tif", "image/jpgpng": "", "image/jpegpng": "", "image/unknown": "" };
var F2 = /* @__PURE__ */ new Set(["version", "service", "request", "layer", "style", "format", "tilematrixset", "tilematrix", "tilerow", "tilecol"]);
var V2 = class extends n(p4(t2(c(_(O2(b)))))) {
  constructor(...e3) {
    super(...e3), this.copyright = "", this.customParameters = null, this.customLayerParameters = null, this.fullExtent = null, this.operationalLayerType = "WebTiledLayer", this.resourceInfo = null, this.serviceMode = "RESTful", this.sublayers = null, this.type = "wmts", this.version = "1.0.0", this.addHandles([l2(() => this.activeLayer, (e4, t3) => {
      t3 && (t3.layer = null), e4 && (e4.layer = this);
    }, U), a2(() => this.sublayers, "after-add", ({ item: e4 }) => {
      e4.layer = this;
    }, U), a2(() => this.sublayers, "after-remove", ({ item: e4 }) => {
      e4.layer = null;
    }, U), l2(() => this.sublayers, (e4, t3) => {
      if (t3) for (const r4 of t3) r4.layer = null;
      if (e4) for (const r4 of e4) r4.layer = this;
    }, U)]);
  }
  normalizeCtorArgs(e3, t3) {
    return "string" == typeof e3 ? { url: e3, ...t3 } : e3;
  }
  load(e3) {
    if ("KVP" === this.serviceMode || "RESTful" === this.serviceMode) return this.addResolvingPromise(this.loadFromPortal({ supportedTypes: ["WMTS"] }, e3).catch(w).then(() => this._fetchService(e3)).catch((e4) => {
      throw w(e4), new s("wmtslayer:unsupported-service-data", "Invalid response from the WMTS service.", { error: e4 });
    })), Promise.resolve(this);
    console.error("WMTS mode could only be 'KVP' or 'RESTful'");
  }
  get activeLayer() {
    return this._get("activeLayer");
  }
  set activeLayer(e3) {
    this._set("activeLayer", e3);
  }
  readActiveLayerFromService(e3, t3, r4) {
    this.activeLayer || (this.activeLayer = new u());
    let i2 = t3.layers.find((e4) => e4.id === this.activeLayer.id);
    return i2 || (i2 = t3.layers[0]), this.activeLayer.read(i2, r4), this.activeLayer;
  }
  readActiveLayerFromItemOrWebDoc(e3, t3) {
    const { templateUrl: r4, wmtsInfo: i2 } = t3, s2 = r4 ? this._getLowerCasedUrlParams(r4) : null, a3 = i2 == null ? void 0 : i2.layerIdentifier;
    let o5 = null;
    const l5 = i2 == null ? void 0 : i2.tileMatrixSet;
    l5 && (Array.isArray(l5) ? l5.length && (o5 = l5[0]) : o5 = l5);
    const n3 = s2 == null ? void 0 : s2.format, m3 = s2 == null ? void 0 : s2.style;
    return new u({ id: a3, imageFormat: n3, styleId: m3, tileMatrixSetId: o5 });
  }
  writeActiveLayer(e3, t3, r4, i2) {
    const s2 = this.activeLayer;
    t3.templateUrl = this.getUrlTemplate(s2.id, s2.tileMatrixSetId, s2.imageFormat, s2.styleId);
    const a3 = t("tileMatrixSet.tileInfo", s2);
    t3.tileInfo = a3 ? a3.toJSON(i2) : null, t3.wmtsInfo = { ...t3.wmtsInfo, layerIdentifier: s2.id, tileMatrixSet: s2.tileMatrixSetId };
  }
  readCustomParameters(e3, t3) {
    const r4 = t3.wmtsInfo;
    return r4 ? this._mergeParams(r4.customParameters, r4.url) : null;
  }
  get fullExtents() {
    return this.activeLayer.fullExtents;
  }
  readServiceMode(e3, t3) {
    return t3.templateUrl.includes("?") ? "KVP" : "RESTful";
  }
  readSublayersFromService(e3, t3, r4) {
    return $(t3.layers, r4);
  }
  get supportedSpatialReferences() {
    var _a;
    return ((_a = this.activeLayer.tileMatrixSets) == null ? void 0 : _a.map((e3) => {
      var _a2;
      return (_a2 = e3.tileInfo) == null ? void 0 : _a2.spatialReference;
    }).toArray().filter(r)) ?? [];
  }
  get tilemapCache() {
    var _a, _b;
    const e3 = (_b = (_a = this.activeLayer) == null ? void 0 : _a.tileMatrixSet) == null ? void 0 : _b.tileInfo;
    return e3 ? new l3(e3) : void 0;
  }
  get title() {
    var _a;
    return ((_a = this.activeLayer) == null ? void 0 : _a.title) ?? "Layer";
  }
  set title(e3) {
    this._overrideIfSome("title", e3);
  }
  get url() {
    return this._get("url");
  }
  set url(e3) {
    e3 && "/" === e3.substr(-1) ? this._set("url", e3.slice(0, -1)) : this._set("url", e3);
  }
  createWebTileLayer(e3) {
    var _a;
    const t3 = this.getUrlTemplate(this.activeLayer.id, this.activeLayer.tileMatrixSetId, this.activeLayer.imageFormat, this.activeLayer.styleId), r4 = (_a = this._getTileMatrixSetById(e3.tileMatrixSetId)) == null ? void 0 : _a.tileInfo, i2 = e3.fullExtent, s2 = new p5({ layerIdentifier: e3.id, tileMatrixSet: e3.tileMatrixSetId, url: this.url });
    return this.customLayerParameters && (s2.customLayerParameters = this.customLayerParameters), this.customParameters && (s2.customParameters = this.customParameters), new L2({ fullExtent: i2, urlTemplate: t3, tileInfo: r4, wmtsInfo: s2 });
  }
  async fetchTile(e3, r4, i2) {
    const s2 = this.getTileUrl(e3, r4, i2), { data: a3 } = await U2(s2, { responseType: "image" });
    return a3;
  }
  async fetchImageBitmapTile(e3, r4, i2) {
    const s2 = this.getTileUrl(e3, r4, i2), { data: a3 } = await U2(s2, { responseType: "blob" });
    return e2(a3, s2);
  }
  findSublayerById(e3) {
    var _a;
    return (_a = this.sublayers) == null ? void 0 : _a.find((t3) => t3.id === e3);
  }
  getTileUrl(e3, t3, r4) {
    var _a, _b;
    const i2 = (_b = (_a = this._getTileMatrixSetById(this.activeLayer.tileMatrixSetId)) == null ? void 0 : _a.tileInfo) == null ? void 0 : _b.lods[e3], s2 = i2 ? i2.levelValue ? i2.levelValue : `${i2.level}` : `${e3}`;
    let a3 = this.resourceInfo ? "" : y2({ dimensionMap: this.dimensionMap, layerMap: this.layerMap }, this.activeLayer.id, this.activeLayer.tileMatrixSetId, this.activeLayer.imageFormat, this.activeLayer.styleId, s2, t3, r4);
    if (!a3) {
      a3 = this.getUrlTemplate(this.activeLayer.id, this.activeLayer.tileMatrixSetId, this.activeLayer.imageFormat, this.activeLayer.styleId).replace(/\{level\}/gi, s2).replace(/\{row\}/gi, `${t3}`).replace(/\{col\}/gi, `${r4}`);
    }
    return a3 = this._appendCustomLayerParameters(a3), a3;
  }
  getUrlTemplate(e3, t3, r4, i2) {
    if (!this.resourceInfo) {
      const r5 = S({ dimensionMap: this.dimensionMap, layerMap: this.layerMap }, e3, t3, i2);
      if (r5) return r5;
    }
    if ("KVP" === this.serviceMode) return this.url + "?SERVICE=WMTS&VERSION=" + this.version + "&REQUEST=GetTile&LAYER=" + e3 + "&STYLE=" + i2 + "&FORMAT=" + r4 + "&TILEMATRIXSET=" + t3 + "&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}";
    if ("RESTful" === this.serviceMode) {
      let s2 = "";
      return A3[r4.toLowerCase()] && (s2 = A3[r4.toLowerCase()]), this.url + e3 + "/" + i2 + "/" + t3 + "/{level}/{row}/{col}" + s2;
    }
    return "";
  }
  async _fetchService(e3) {
    let t3;
    if (this.resourceInfo) "KVP" === this.resourceInfo.serviceMode && (this.url += this.url.includes("?") ? "" : "?"), t3 = { ssl: false, data: this.resourceInfo };
    else try {
      t3 = await this._getCapabilities(this.serviceMode, e3), p9(t3.data);
    } catch {
      const s2 = "KVP" === this.serviceMode ? "RESTful" : "KVP";
      try {
        t3 = await this._getCapabilities(s2, e3), p9(t3.data), this.serviceMode = s2;
      } catch (r4) {
        throw new s("wmtslayer:unsupported-service-data", "Services does not support RESTful or KVP service modes.", { error: r4 });
      }
    }
    this.resourceInfo ? t3.data = d(t3.data) : t3.data = f(t3.data, { serviceMode: this.serviceMode, url: this.url }), t3.data && this.read(t3.data, { origin: "service" });
  }
  async _getCapabilities(e3, r4) {
    const i2 = this._getCapabilitiesUrl(e3);
    return await U2(i2, { ...r4, responseType: "text" });
  }
  _getTileMatrixSetById(e3) {
    var _a, _b;
    const t3 = (_b = (_a = this.findSublayerById(this.activeLayer.id)) == null ? void 0 : _a.tileMatrixSets) == null ? void 0 : _b.find((t4) => t4.id === e3);
    return t3;
  }
  _appendCustomParameters(e3) {
    return this._appendParameters(e3, this.customParameters);
  }
  _appendCustomLayerParameters(e3) {
    return this._appendParameters(e3, { ...p(this.customParameters), ...this.customLayerParameters });
  }
  _appendParameters(e3, t3) {
    const r4 = L(e3), i2 = { ...r4.query, ...t3 }, s2 = A(i2);
    return "" === s2 ? r4.path : `${r4.path}?${s2}`;
  }
  _getCapabilitiesUrl(e3) {
    this.url = this.url.split("?")[0];
    const t3 = "KVP" === e3 ? `${this.url}?request=GetCapabilities&service=WMTS&version=${this.version}` : `${this.url}/${this.version}/WMTSCapabilities.xml`;
    return this._appendCustomParameters(t3);
  }
  _getLowerCasedUrlParams(e3) {
    if (!e3) return null;
    const t3 = L(e3).query;
    if (!t3) return null;
    const r4 = {};
    return Object.keys(t3).forEach((e4) => {
      r4[e4.toLowerCase()] = t3[e4];
    }), r4;
  }
  _mergeParams(e3, t3) {
    const r4 = this._getLowerCasedUrlParams(t3);
    if (r4) {
      const t4 = Object.keys(r4);
      t4.length && (e3 = e3 ? p(e3) : {}, t4.forEach((t5) => {
        e3.hasOwnProperty(t5) || F2.has(t5) || (e3[t5] = r4[t5]);
      }));
    }
    return e3;
  }
};
function $(e3, t3) {
  return e3.map((e4) => {
    const r4 = new u();
    return r4.read(e4, t3), r4;
  });
}
e([y()], V2.prototype, "dimensionMap", void 0), e([y()], V2.prototype, "layerMap", void 0), e([y({ type: u, json: { origins: { "web-document": { write: { ignoreOrigin: true } } } } })], V2.prototype, "activeLayer", null), e([o("service", "activeLayer", ["layers"])], V2.prototype, "readActiveLayerFromService", null), e([o(["web-document", "portal-item"], "activeLayer", ["wmtsInfo"])], V2.prototype, "readActiveLayerFromItemOrWebDoc", null), e([r2(["web-document", "portal-item"], "activeLayer", { templateUrl: { type: String }, tileInfo: { type: j2 }, "wmtsInfo.layerIdentifier": { type: String }, "wmtsInfo.tileMatrixSet": { type: String } })], V2.prototype, "writeActiveLayer", null), e([y({ type: String, value: "", json: { write: true } })], V2.prototype, "copyright", void 0), e([y({ type: ["show", "hide"] })], V2.prototype, "listMode", void 0), e([y({ json: { read: true, write: true } })], V2.prototype, "blendMode", void 0), e([y({ json: { origins: { "web-document": { read: { source: ["wmtsInfo.customParameters", "wmtsInfo.url"] }, write: { target: "wmtsInfo.customParameters" } }, "portal-item": { read: { source: ["wmtsInfo.customParameters", "wmtsInfo.url"] }, write: { target: "wmtsInfo.customParameters" } } } } })], V2.prototype, "customParameters", void 0), e([o(["portal-item", "web-document"], "customParameters")], V2.prototype, "readCustomParameters", null), e([y({ json: { origins: { "web-document": { read: { source: "wmtsInfo.customLayerParameters" }, write: { target: "wmtsInfo.customLayerParameters" } }, "portal-item": { read: { source: "wmtsInfo.customLayerParameters" }, write: { target: "wmtsInfo.customLayerParameters" } } } } })], V2.prototype, "customLayerParameters", void 0), e([y({ type: w3, json: { write: { ignoreOrigin: true }, origins: { "web-document": { read: { source: "fullExtent" } }, "portal-item": { read: { source: "fullExtent" } } } } })], V2.prototype, "fullExtent", void 0), e([y({ readOnly: true })], V2.prototype, "fullExtents", null), e([y({ type: ["WebTiledLayer"] })], V2.prototype, "operationalLayerType", void 0), e([y()], V2.prototype, "resourceInfo", void 0), e([y()], V2.prototype, "serviceMode", void 0), e([o(["portal-item", "web-document"], "serviceMode", ["templateUrl"])], V2.prototype, "readServiceMode", null), e([y({ type: j.ofType(u) })], V2.prototype, "sublayers", void 0), e([o("service", "sublayers", ["layers"])], V2.prototype, "readSublayersFromService", null), e([y({ readOnly: true })], V2.prototype, "supportedSpatialReferences", null), e([y({ readOnly: true })], V2.prototype, "tilemapCache", null), e([y({ json: { read: { source: "title" } } })], V2.prototype, "title", null), e([y({ json: { read: false }, readOnly: true, value: "wmts" })], V2.prototype, "type", void 0), e([y({ json: { origins: { service: { read: { source: "tileUrl" } }, "web-document": { read: { source: "wmtsInfo.url" }, write: { target: "wmtsInfo.url" } }, "portal-item": { read: { source: "wmtsInfo.url" }, write: { target: "wmtsInfo.url" } } } } })], V2.prototype, "url", null), e([y()], V2.prototype, "version", void 0), V2 = e([a("esri.layers.WMTSLayer")], V2);
var K = V2;
export {
  K as default
};
//# sourceMappingURL=WMTSLayer-BIQGQPPO.js.map
