import {
  r as r3
} from "./chunk-3LXNA5FS.js";
import {
  h
} from "./chunk-3IJGF72Q.js";
import {
  D
} from "./chunk-JODDYOCL.js";
import {
  f as f2
} from "./chunk-UDV5MSCV.js";
import "./chunk-BWWOCIFU.js";
import "./chunk-QW426QEA.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-CPQSD22U.js";
import "./chunk-MNYWPBDW.js";
import "./chunk-ZVJXF3ML.js";
import "./chunk-SKIEIN3S.js";
import "./chunk-3KCCETWY.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-FAMLZKHJ.js";
import {
  E
} from "./chunk-YELYN22P.js";
import "./chunk-XSQFM27N.js";
import "./chunk-QYOAH6AO.js";
import "./chunk-A7PY25IH.js";
import {
  r as r2
} from "./chunk-INH5JU5P.js";
import {
  l
} from "./chunk-UQWZJZ2S.js";
import "./chunk-5S4W3ME5.js";
import "./chunk-CDZ24ELJ.js";
import "./chunk-VHLK35TF.js";
import "./chunk-ZQY4DQCR.js";
import "./chunk-HURTVQSL.js";
import "./chunk-TNGCGN7L.js";
import "./chunk-ONE6GLG5.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-P2G4OGHI.js";
import "./chunk-6JIS2R4B.js";
import "./chunk-7BSY2CUN.js";
import "./chunk-6RN6WNY4.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-DTQ34PEY.js";
import {
  d
} from "./chunk-Q4VCSCSY.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import {
  n
} from "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  t as t2
} from "./chunk-2CM7MIII.js";
import {
  f,
  p as p2
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import {
  s2 as s
} from "./chunk-GZGAQUSK.js";
import {
  o,
  p,
  r,
  t,
  w
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/edgeRendering/EdgeWorkerHandle.js
var n2 = class extends h {
  constructor(e2) {
    super("EdgeProcessingWorker", "extract", { extract: (e3) => [e3.dataBuffer], extractComponentsEdgeLocations: (e3) => [e3.dataBuffer], extractEdgeLocations: (e3) => [e3.dataBuffer] }, e2);
  }
  async process(e2, t3, r4) {
    if (r4) return f2(e2);
    const n3 = await this.invoke(new a2(e2), t3);
    return this._unpackOutput(n3);
  }
  async extractEdgeLocations(e2, t3) {
    const s2 = await this.invokeMethod("extractEdgeLocations", new a2(e2), t3);
    return D(s2);
  }
  async extractComponentsEdgeLocations(e2, t3) {
    const s2 = await this.invokeMethod("extractComponentsEdgeLocations", new a2(e2), t3);
    return D(s2);
  }
  _unpackOutput(e2) {
    return { regular: { instancesData: D(e2.regular.instancesData), lodInfo: { lengths: new Float32Array(e2.regular.lodInfo.lengths) } }, silhouette: { instancesData: D(e2.silhouette.instancesData), lodInfo: { lengths: new Float32Array(e2.silhouette.lodInfo.lengths) } }, averageEdgeLength: e2.averageEdgeLength };
  }
};
var a2 = class {
  constructor(t3) {
    this.dataBuffer = t3.data.buffer, this.writerSettings = t3.writerSettings, this.skipDeduplicate = t3.skipDeduplicate, this.indices = Array.isArray(t3.indices) ? t3.indices : t3.indices.buffer, this.indicesType = Array.isArray(t3.indices) ? "Array" : s(t3.indices) ? "Uint32Array" : "Uint16Array", this.indicesLength = t3.indicesLength;
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/sceneLayerSource/SceneLayerSnappingSourceWorkerHandle.js
var p3 = class extends d {
  constructor(e2) {
    super(e2), this.availability = 0, this._ids = /* @__PURE__ */ new Set();
  }
  destroy() {
    this._workerHandle.destroy(), this._workerHandle = null;
  }
  initialize() {
    this._workerHandle = new l2(this.schedule, { fetchAllEdgeLocations: (e2, t3) => this._fetchAllEdgeLocations(e2, t3) });
  }
  async fetchCandidates(e2, t3) {
    const r4 = e2.coordinateHelper, { point: o2 } = e2, s2 = h2;
    this.renderCoordsHelper.toRenderCoords(o2, r4.spatialReference, s2);
    const n3 = e2.distance, a3 = "number" == typeof n3 ? n3 : n3.distance, d2 = await this._workerHandle.invoke({ bounds: E(s2[0], s2[1], s2[2], a3), types: e2.types }, t3);
    return d2.candidates.sort((e3, t4) => e3.distance - t4.distance), d2.candidates.map((e3) => this._convertCandidate(r4, e3));
  }
  async add(e2, t3) {
    this._ids.add(e2.id), await this._workerHandle.invokeMethod("add", e2, t3);
  }
  async remove(e2, t3) {
    this._ids.delete(e2.id), await this._workerHandle.invokeMethod("remove", e2, t3);
  }
  _convertCandidate(e2, t3) {
    switch (t3.type) {
      case "edge":
        return new r2({ objectId: t3.objectId, targetPoint: this._convertRenderCoordinate(e2, t3.target), edgeStart: this._convertRenderCoordinate(e2, t3.start), edgeEnd: this._convertRenderCoordinate(e2, t3.end), isDraped: false });
      case "vertex":
        return new r3({ objectId: t3.objectId, targetPoint: this._convertRenderCoordinate(e2, t3.target), isDraped: false });
    }
  }
  _convertRenderCoordinate({ spatialReference: e2 }, t3) {
    const r4 = n();
    return this.renderCoordsHelper.fromRenderCoords(t3, r4, e2), l(r4);
  }
  async _fetchAllEdgeLocations(e2, t3) {
    const r4 = [], o2 = [];
    for (const { id: s2, uid: n3 } of e2.components) this._ids.has(s2) && r4.push((async () => {
      const e3 = await this.fetchEdgeLocations(s2, t3.signal), r5 = e3.locations.buffer;
      return o2.push(r5), { id: s2, uid: n3, objectIds: e3.objectIds, locations: r5, origin: e3.origin, type: e3.type };
    })());
    return { result: { components: (await Promise.all(r4)).filter(({ id: e3 }) => this._ids.has(e3)) }, transferList: o2 };
  }
};
e([y({ constructOnly: true })], p3.prototype, "renderCoordsHelper", void 0), e([y({ constructOnly: true })], p3.prototype, "fetchEdgeLocations", void 0), e([y({ constructOnly: true })], p3.prototype, "schedule", void 0), e([y({ readOnly: true })], p3.prototype, "availability", void 0), p3 = e([a("esri.views.interactive.snapping.featureSources.sceneLayerSource.SceneLayerSnappingSourceWorker")], p3);
var l2 = class extends h {
  constructor(e2, t3) {
    super("SceneLayerSnappingSourceWorker", "fetchCandidates", {}, e2, { strategy: "dedicated", client: t3 });
  }
};
var h2 = n();

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/I3SSnappingSource.js
var h3 = class extends d {
  get updating() {
    return this.updatingHandles.updating;
  }
  constructor(e2) {
    super(e2), this.availability = 1, this._abortController = new AbortController();
  }
  destroy() {
    this._tracker = p(this._tracker), this._abortController = w(this._abortController);
  }
  initialize() {
    const { view: e2 } = this, r4 = e2.resourceController;
    this._edgeWorker = new n2((e3) => r4.immediate.schedule(e3)), this._workerHandle = new p3({ renderCoordsHelper: this.view.renderCoordsHelper, schedule: (e3) => r4.immediate.schedule(e3), fetchEdgeLocations: async (e3, r5) => {
      if (t(this._tracker)) throw new Error("tracker-not-initialized");
      return this._tracker.fetchEdgeLocations(e3, this._edgeWorker, r5);
    } }), this.updatingHandles.addPromise(this._setupLayerView()), this.handles.add([t2(this._workerHandle), t2(this._edgeWorker)]);
  }
  async fetchCandidates(e2, r4) {
    return this._workerHandle.fetchCandidates(e2, r4);
  }
  refresh() {
  }
  async _setupLayerView() {
    if (this.destroyed) return;
    const e2 = o(this._abortController, (e3) => e3.signal), r4 = await this.getLayerView();
    t(r4) || p2(e2) || (this._tracker = r4.trackSnappingSources({ add: (r5, t3) => {
      this.updatingHandles.addPromise(this._workerHandle.add({ id: r5, bounds: t3 }, e2));
    }, remove: (r5) => {
      this.updatingHandles.addPromise(this._workerHandle.remove({ id: r5 }, e2));
    } }));
  }
};
e([y({ constructOnly: true })], h3.prototype, "getLayerView", void 0), e([y({ constructOnly: true })], h3.prototype, "view", void 0), e([y({ readOnly: true })], h3.prototype, "updating", null), e([y({ readOnly: true })], h3.prototype, "availability", void 0), h3 = e([a("esri.views.interactive.snapping.featureSources.I3SSnappingSource")], h3);

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/SceneLayerSnappingSource.js
var c = class extends v {
  get updating() {
    return this._i3sSources.some((e2) => e2.updating);
  }
  constructor(e2) {
    super(e2), this.availability = 1, this._i3sSources = [];
  }
  destroy() {
    this._i3sSources.forEach((e2) => e2.destroy()), this._i3sSources.length = 0;
  }
  initialize() {
    const { view: e2 } = this, r4 = this.layerSource.layer;
    this._i3sSources = "building-scene" === r4.type ? this._getBuildingSceneI3SSources(e2, r4) : [this._getSceneLayerI3SSource(e2, r4)];
  }
  async fetchCandidates(e2, r4) {
    const t3 = await Promise.all(this._i3sSources.map((t4) => t4.fetchCandidates(e2, r4)));
    return f(r4), t3.flat();
  }
  refresh() {
    this._i3sSources.forEach((e2) => e2.refresh());
  }
  _getBuildingSceneI3SSources(e2, r4) {
    return r4.allSublayers.toArray().map((t3) => "building-component" === t3.type ? new h3({ getLayerView: async () => (await e2.whenLayerView(r4)).whenSublayerView(t3), view: e2 }) : null).filter(r);
  }
  _getSceneLayerI3SSource(e2, r4) {
    return new h3({ getLayerView: async () => {
      const t3 = await e2.whenLayerView(r4);
      return "scene-layer-graphics-3d" === t3.type ? void 0 : t3;
    }, view: e2 });
  }
};
e([y({ constructOnly: true })], c.prototype, "layerSource", void 0), e([y({ constructOnly: true })], c.prototype, "view", void 0), e([y({ readOnly: true })], c.prototype, "updating", null), e([y({ readOnly: true })], c.prototype, "availability", void 0), c = e([a("esri.views.interactive.snapping.featureSources.SceneLayerSnappingSource")], c);
export {
  c as SceneLayerSnappingSource
};
//# sourceMappingURL=SceneLayerSnappingSource-F4ZRD4M2.js.map
