{"version": 3, "sources": ["../../@arcgis/core/chunks/lerc-wasm.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nfunction t(t,n){for(var e=0;e<n.length;e++){const r=n[e];if(\"string\"!=typeof r&&!Array.isArray(r))for(const n in r)if(\"default\"!==n&&!(n in t)){const e=Object.getOwnPropertyDescriptor(r,n);e&&Object.defineProperty(t,n,e.get?e:{enumerable:!0,get:()=>r[n]})}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:\"Module\"}))}var n,e,r,i={};n={get exports(){return i},set exports(t){i=t}},e=\"undefined\"!=typeof document&&document.currentScript?document.currentScript.src:void 0,\"undefined\"!=typeof __filename&&(e=e||__filename),r=function(t){var n,r;(t=void 0!==(t=t||{})?t:{}).ready=new Promise((function(t,e){n=t,r=e}));var i,o,u,s,a,c,f=Object.assign({},t),p=\"object\"==typeof window,l=\"function\"==typeof importScripts,h=\"object\"==typeof process&&\"object\"==typeof process.versions&&\"string\"==typeof process.versions.node,d=\"\";function m(n){return t.locateFile?t.locateFile(n,d):d+n}h?(d=l?require(\"path\").dirname(d)+\"/\":__dirname+\"/\",c=()=>{a||(s=require(\"fs\"),a=require(\"path\"))},i=function(t,n){return c(),t=a.normalize(t),s.readFileSync(t,n?void 0:\"utf8\")},u=t=>{var n=i(t,!0);return n.buffer||(n=new Uint8Array(n)),n},o=(t,n,e)=>{c(),t=a.normalize(t),s.readFile(t,(function(t,r){t?e(t):n(r.buffer)}))},process.argv.length>1&&process.argv[1].replace(/\\\\/g,\"/\"),process.argv.slice(2),process.on(\"uncaughtException\",(function(t){if(!(t instanceof ht))throw t})),process.on(\"unhandledRejection\",(function(t){throw t})),t.inspect=function(){return\"[Emscripten Module object]\"}):(p||l)&&(l?d=self.location.href:\"undefined\"!=typeof document&&document.currentScript&&(d=document.currentScript.src),e&&(d=e),d=0!==d.indexOf(\"blob:\")?d.substr(0,d.replace(/[?#].*/,\"\").lastIndexOf(\"/\")+1):\"\",i=t=>{var n=new XMLHttpRequest;return n.open(\"GET\",t,!1),n.send(null),n.responseText},l&&(u=t=>{var n=new XMLHttpRequest;return n.open(\"GET\",t,!1),n.responseType=\"arraybuffer\",n.send(null),new Uint8Array(n.response)}),o=(t,n,e)=>{var r=new XMLHttpRequest;r.open(\"GET\",t,!0),r.responseType=\"arraybuffer\",r.onload=()=>{200==r.status||0==r.status&&r.response?n(r.response):e()},r.onerror=e,r.send(null)}),t.print||console.log.bind(console);var _,y,g=t.printErr||console.warn.bind(console);Object.assign(t,f),f=null,t.arguments&&t.arguments,t.thisProgram&&t.thisProgram,t.quit&&t.quit,t.wasmBinary&&(_=t.wasmBinary),t.noExitRuntime,\"object\"!=typeof WebAssembly&&L(\"no native wasm support detected\");var v,w,b,A,R,x,S=!1,P=\"undefined\"!=typeof TextDecoder?new TextDecoder(\"utf8\"):void 0;function E(t,n,e){for(var r=n+e,i=n;t[i]&&!(i>=r);)++i;if(i-n>16&&t.buffer&&P)return P.decode(t.subarray(n,i));for(var o=\"\";n<i;){var u=t[n++];if(128&u){var s=63&t[n++];if(192!=(224&u)){var a=63&t[n++];if((u=224==(240&u)?(15&u)<<12|s<<6|a:(7&u)<<18|s<<12|a<<6|63&t[n++])<65536)o+=String.fromCharCode(u);else{var c=u-65536;o+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else o+=String.fromCharCode((31&u)<<6|s)}else o+=String.fromCharCode(u)}return o}function I(t,n){return t?E(b,t,n):\"\"}function j(n){v=n,t.HEAP8=w=new Int8Array(n),t.HEAP16=new Int16Array(n),t.HEAP32=A=new Int32Array(n),t.HEAPU8=b=new Uint8Array(n),t.HEAPU16=new Uint16Array(n),t.HEAPU32=R=new Uint32Array(n),t.HEAPF32=new Float32Array(n),t.HEAPF64=new Float64Array(n)}t.INITIAL_MEMORY;var T=[],D=[],H=[];function M(){if(t.preRun)for(\"function\"==typeof t.preRun&&(t.preRun=[t.preRun]);t.preRun.length;)U(t.preRun.shift());V(T)}function O(){V(D)}function W(){if(t.postRun)for(\"function\"==typeof t.postRun&&(t.postRun=[t.postRun]);t.postRun.length;)C(t.postRun.shift());V(H)}function U(t){T.unshift(t)}function q(t){D.unshift(t)}function C(t){H.unshift(t)}var F=0,B=null;function k(n){F++,t.monitorRunDependencies&&t.monitorRunDependencies(F)}function z(n){if(F--,t.monitorRunDependencies&&t.monitorRunDependencies(F),0==F&&B){var e=B;B=null,e()}}function L(n){t.onAbort&&t.onAbort(n),g(n=\"Aborted(\"+n+\")\"),S=!0,n+=\". Build with -sASSERTIONS for more info.\";var e=new WebAssembly.RuntimeError(n);throw r(e),e}var G,X=\"data:application/octet-stream;base64,\";function N(t){return t.startsWith(X)}function Y(t){return t.startsWith(\"file://\")}function J(t){try{if(t==G&&_)return new Uint8Array(_);if(u)return u(t);throw\"both async and sync fetching of the wasm failed\"}catch(g){L(g)}}function K(){if(!_&&(p||l)){if(\"function\"==typeof fetch&&!Y(G))return fetch(G,{credentials:\"same-origin\"}).then((function(t){if(!t.ok)throw\"failed to load wasm binary file at '\"+G+\"'\";return t.arrayBuffer()})).catch((function(){return J(G)}));if(o)return new Promise((function(t,n){o(G,(function(n){t(new Uint8Array(n))}),n)}))}return Promise.resolve().then((function(){return J(G)}))}function Q(){var n={a:ct};function e(n,e){var r=n.exports;t.asm=r,j((y=t.asm.g).buffer),x=t.asm.m,q(t.asm.h),z()}function i(t){e(t.instance)}function o(t){return K().then((function(t){return WebAssembly.instantiate(t,n)})).then((function(t){return t})).then(t,(function(t){g(\"failed to asynchronously prepare wasm: \"+t),L(t)}))}function u(){return _||\"function\"!=typeof WebAssembly.instantiateStreaming||N(G)||Y(G)||h||\"function\"!=typeof fetch?o(i):fetch(G,{credentials:\"same-origin\"}).then((function(t){return WebAssembly.instantiateStreaming(t,n).then(i,(function(t){return g(\"wasm streaming compile failed: \"+t),g(\"falling back to ArrayBuffer instantiation\"),o(i)}))}))}if(k(),t.instantiateWasm)try{return t.instantiateWasm(n,e)}catch(s){return g(\"Module.instantiateWasm callback failed with error: \"+s),!1}return u().catch(r),{}}function V(n){for(;n.length>0;){var e=n.shift();if(\"function\"!=typeof e){var r=e.func;\"number\"==typeof r?void 0===e.arg?$(r)():$(r)(e.arg):r(void 0===e.arg?null:e.arg)}else e(t)}}N(G=\"lerc-wasm.wasm\")||(G=m(G));var Z=[];function $(t){var n=Z[t];return n||(t>=Z.length&&(Z.length=t+1),Z[t]=n=x.get(t)),n}function tt(t,n,e,r){L(\"Assertion failed: \"+I(t)+\", at: \"+[n?I(n):\"unknown filename\",e,r?I(r):\"unknown function\"])}function nt(t){return ft(t+24)+24}function et(t){this.excPtr=t,this.ptr=t-24,this.set_type=function(t){R[this.ptr+4>>2]=t},this.get_type=function(){return R[this.ptr+4>>2]},this.set_destructor=function(t){R[this.ptr+8>>2]=t},this.get_destructor=function(){return R[this.ptr+8>>2]},this.set_refcount=function(t){A[this.ptr>>2]=t},this.set_caught=function(t){t=t?1:0,w[this.ptr+12>>0]=t},this.get_caught=function(){return 0!=w[this.ptr+12>>0]},this.set_rethrown=function(t){t=t?1:0,w[this.ptr+13>>0]=t},this.get_rethrown=function(){return 0!=w[this.ptr+13>>0]},this.init=function(t,n){this.set_adjusted_ptr(0),this.set_type(t),this.set_destructor(n),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var t=A[this.ptr>>2];A[this.ptr>>2]=t+1},this.release_ref=function(){var t=A[this.ptr>>2];return A[this.ptr>>2]=t-1,1===t},this.set_adjusted_ptr=function(t){R[this.ptr+16>>2]=t},this.get_adjusted_ptr=function(){return R[this.ptr+16>>2]},this.get_exception_ptr=function(){if(lt(this.get_type()))return R[this.excPtr>>2];var t=this.get_adjusted_ptr();return 0!==t?t:this.excPtr}}function rt(t,n,e){throw new et(t).init(n,e),t}function it(){L(\"\")}function ot(t,n,e){b.copyWithin(t,n,n+e)}function ut(){return 2147483648}function st(t){try{return y.grow(t-v.byteLength+65535>>>16),j(y.buffer),1}catch(n){}}function at(t){var n=b.length;t>>>=0;var e=ut();if(t>e)return!1;let r=(t,n)=>t+(n-t%n)%n;for(var i=1;i<=4;i*=2){var o=n*(1+.2/i);if(o=Math.min(o,t+100663296),st(Math.min(e,r(Math.max(t,o),65536))))return!0}return!1}var ct={a:tt,c:nt,b:rt,d:it,f:ot,e:at};Q(),t.___wasm_call_ctors=function(){return(t.___wasm_call_ctors=t.asm.h).apply(null,arguments)},t._lerc_getBlobInfo=function(){return(t._lerc_getBlobInfo=t.asm.i).apply(null,arguments)},t._lerc_getDataRanges=function(){return(t._lerc_getDataRanges=t.asm.j).apply(null,arguments)},t._lerc_decode=function(){return(t._lerc_decode=t.asm.k).apply(null,arguments)},t._lerc_decode_4D=function(){return(t._lerc_decode_4D=t.asm.l).apply(null,arguments)};var ft=t._malloc=function(){return(ft=t._malloc=t.asm.n).apply(null,arguments)};t._free=function(){return(t._free=t.asm.o).apply(null,arguments)};var pt,lt=t.___cxa_is_pointer_type=function(){return(lt=t.___cxa_is_pointer_type=t.asm.p).apply(null,arguments)};function ht(t){this.name=\"ExitStatus\",this.message=\"Program terminated with exit(\"+t+\")\",this.status=t}function dt(e){function r(){pt||(pt=!0,t.calledRun=!0,S||(O(),n(t),t.onRuntimeInitialized&&t.onRuntimeInitialized(),W()))}F>0||(M(),F>0||(t.setStatus?(t.setStatus(\"Running...\"),setTimeout((function(){setTimeout((function(){t.setStatus(\"\")}),1),r()}),1)):r()))}if(B=function t(){pt||dt(),pt||(B=t)},t.run=dt,t.preInit)for(\"function\"==typeof t.preInit&&(t.preInit=[t.preInit]);t.preInit.length>0;)t.preInit.pop()();return dt(),t.ready},n.exports=r;const o=t({__proto__:null,default:i},[i]);export{o as l};\n"], "mappings": ";;;;;;;;;AAIA,SAAS,EAAEA,IAAEC,IAAE;AAAC,WAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,UAAMC,KAAEF,GAAEC,EAAC;AAAE,QAAG,YAAU,OAAOC,MAAG,CAAC,MAAM,QAAQA,EAAC;AAAE,iBAAUF,MAAKE,GAAE,KAAG,cAAYF,MAAG,EAAEA,MAAKD,KAAG;AAAC,cAAME,KAAE,OAAO,yBAAyBC,IAAEF,EAAC;AAAE,QAAAC,MAAG,OAAO,eAAeF,IAAEC,IAAEC,GAAE,MAAIA,KAAE,EAAC,YAAW,MAAG,KAAI,MAAIC,GAAEF,EAAC,EAAC,CAAC;AAAA,MAAC;AAAA;AAAA,EAAC;AAAC,SAAO,OAAO,OAAO,OAAO,eAAeD,IAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;AAAC;AAAC,IAAI;AAAJ,IAAM;AAAN,IAAQ;AAAR,IAAU,IAAE,CAAC;AAAE,IAAE,EAAC,IAAI,UAAS;AAAC,SAAO;AAAC,GAAE,IAAI,QAAQA,IAAE;AAAC,MAAEA;AAAC,EAAC,GAAE,IAAE,eAAa,OAAO,YAAU,SAAS,gBAAc,SAAS,cAAc,MAAI,QAAO,eAAa,OAAO,eAAa,IAAE,KAAG,aAAY,IAAE,SAASA,IAAE;AAAC,MAAIC,IAAEE;AAAE,GAACH,KAAE,YAAUA,KAAEA,MAAG,CAAC,KAAGA,KAAE,CAAC,GAAG,QAAM,IAAI,QAAS,SAASA,IAAEE,IAAE;AAAC,IAAAD,KAAED,IAAEG,KAAED;AAAA,EAAC,CAAE;AAAE,MAAIE,IAAEC,IAAE,GAAE,GAAE,GAAE,GAAE,IAAE,OAAO,OAAO,CAAC,GAAEL,EAAC,GAAE,IAAE,YAAU,OAAO,QAAO,IAAE,cAAY,OAAO,eAAc,IAAE,YAAU,OAAO,WAAS,YAAU,OAAO,QAAQ,YAAU,YAAU,OAAO,QAAQ,SAAS,MAAK,IAAE;AAAG,WAAS,EAAEC,IAAE;AAAC,WAAOD,GAAE,aAAWA,GAAE,WAAWC,IAAE,CAAC,IAAE,IAAEA;AAAA,EAAC;AAAC,OAAG,IAAE,IAAE,eAAgB,QAAQ,CAAC,IAAE,MAAI,YAAU,KAAI,IAAE,MAAI;AAAC,UAAI,IAAE,cAAc,IAAE;AAAA,EAAgB,GAAEG,KAAE,SAASJ,IAAEC,IAAE;AAAC,WAAO,EAAE,GAAED,KAAE,EAAE,UAAUA,EAAC,GAAE,EAAE,aAAaA,IAAEC,KAAE,SAAO,MAAM;AAAA,EAAC,GAAE,IAAE,CAAAD,OAAG;AAAC,QAAIC,KAAEG,GAAEJ,IAAE,IAAE;AAAE,WAAOC,GAAE,WAASA,KAAE,IAAI,WAAWA,EAAC,IAAGA;AAAA,EAAC,GAAEI,KAAE,CAACL,IAAEC,IAAEC,OAAI;AAAC,MAAE,GAAEF,KAAE,EAAE,UAAUA,EAAC,GAAE,EAAE,SAASA,IAAG,SAASA,IAAEG,IAAE;AAAC,MAAAH,KAAEE,GAAEF,EAAC,IAAEC,GAAEE,GAAE,MAAM;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,QAAQ,KAAK,SAAO,KAAG,QAAQ,KAAK,CAAC,EAAE,QAAQ,OAAM,GAAG,GAAE,QAAQ,KAAK,MAAM,CAAC,GAAE,QAAQ,GAAG,qBAAqB,SAASH,IAAE;AAAC,QAAG,EAAEA,cAAa,IAAI,OAAMA;AAAA,EAAC,CAAE,GAAE,QAAQ,GAAG,sBAAsB,SAASA,IAAE;AAAC,UAAMA;AAAA,EAAC,CAAE,GAAEA,GAAE,UAAQ,WAAU;AAAC,WAAM;AAAA,EAA4B,MAAI,KAAG,OAAK,IAAE,IAAE,KAAK,SAAS,OAAK,eAAa,OAAO,YAAU,SAAS,kBAAgB,IAAE,SAAS,cAAc,MAAK,MAAI,IAAE,IAAG,IAAE,MAAI,EAAE,QAAQ,OAAO,IAAE,EAAE,OAAO,GAAE,EAAE,QAAQ,UAAS,EAAE,EAAE,YAAY,GAAG,IAAE,CAAC,IAAE,IAAGI,KAAE,CAAAJ,OAAG;AAAC,QAAIC,KAAE,IAAI;AAAe,WAAOA,GAAE,KAAK,OAAMD,IAAE,KAAE,GAAEC,GAAE,KAAK,IAAI,GAAEA,GAAE;AAAA,EAAY,GAAE,MAAI,IAAE,CAAAD,OAAG;AAAC,QAAIC,KAAE,IAAI;AAAe,WAAOA,GAAE,KAAK,OAAMD,IAAE,KAAE,GAAEC,GAAE,eAAa,eAAcA,GAAE,KAAK,IAAI,GAAE,IAAI,WAAWA,GAAE,QAAQ;AAAA,EAAC,IAAGI,KAAE,CAACL,IAAEC,IAAEC,OAAI;AAAC,QAAIC,KAAE,IAAI;AAAe,IAAAA,GAAE,KAAK,OAAMH,IAAE,IAAE,GAAEG,GAAE,eAAa,eAAcA,GAAE,SAAO,MAAI;AAAC,aAAKA,GAAE,UAAQ,KAAGA,GAAE,UAAQA,GAAE,WAASF,GAAEE,GAAE,QAAQ,IAAED,GAAE;AAAA,IAAC,GAAEC,GAAE,UAAQD,IAAEC,GAAE,KAAK,IAAI;AAAA,EAAC,IAAGH,GAAE,SAAO,QAAQ,IAAI,KAAK,OAAO;AAAE,MAAI,GAAE,GAAE,IAAEA,GAAE,YAAU,QAAQ,KAAK,KAAK,OAAO;AAAE,SAAO,OAAOA,IAAE,CAAC,GAAE,IAAE,MAAKA,GAAE,aAAWA,GAAE,WAAUA,GAAE,eAAaA,GAAE,aAAYA,GAAE,QAAMA,GAAE,MAAKA,GAAE,eAAa,IAAEA,GAAE,aAAYA,GAAE,eAAc,YAAU,OAAO,eAAa,EAAE,iCAAiC;AAAE,MAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,OAAG,IAAE,eAAa,OAAO,cAAY,IAAI,YAAY,MAAM,IAAE;AAAO,WAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,aAAQC,KAAEF,KAAEC,IAAEE,KAAEH,IAAED,GAAEI,EAAC,KAAG,EAAEA,MAAGD,MAAI,GAAEC;AAAE,QAAGA,KAAEH,KAAE,MAAID,GAAE,UAAQ,EAAE,QAAO,EAAE,OAAOA,GAAE,SAASC,IAAEG,EAAC,CAAC;AAAE,aAAQC,KAAE,IAAGJ,KAAEG,MAAG;AAAC,UAAIE,KAAEN,GAAEC,IAAG;AAAE,UAAG,MAAIK,IAAE;AAAC,YAAIC,KAAE,KAAGP,GAAEC,IAAG;AAAE,YAAG,QAAM,MAAIK,KAAG;AAAC,cAAIE,KAAE,KAAGR,GAAEC,IAAG;AAAE,eAAIK,KAAE,QAAM,MAAIA,OAAI,KAAGA,OAAI,KAAGC,MAAG,IAAEC,MAAG,IAAEF,OAAI,KAAGC,MAAG,KAAGC,MAAG,IAAE,KAAGR,GAAEC,IAAG,KAAG,MAAM,CAAAI,MAAG,OAAO,aAAaC,EAAC;AAAA,eAAM;AAAC,gBAAIG,KAAEH,KAAE;AAAM,YAAAD,MAAG,OAAO,aAAa,QAAMI,MAAG,IAAG,QAAM,OAAKA,EAAC;AAAA,UAAC;AAAA,QAAC,MAAM,CAAAJ,MAAG,OAAO,cAAc,KAAGC,OAAI,IAAEC,EAAC;AAAA,MAAC,MAAM,CAAAF,MAAG,OAAO,aAAaC,EAAC;AAAA,IAAC;AAAC,WAAOD;AAAA,EAAC;AAAC,WAAS,EAAEL,IAAEC,IAAE;AAAC,WAAOD,KAAE,EAAE,GAAEA,IAAEC,EAAC,IAAE;AAAA,EAAE;AAAC,WAAS,EAAEA,IAAE;AAAC,QAAEA,IAAED,GAAE,QAAM,IAAE,IAAI,UAAUC,EAAC,GAAED,GAAE,SAAO,IAAI,WAAWC,EAAC,GAAED,GAAE,SAAO,IAAE,IAAI,WAAWC,EAAC,GAAED,GAAE,SAAO,IAAE,IAAI,WAAWC,EAAC,GAAED,GAAE,UAAQ,IAAI,YAAYC,EAAC,GAAED,GAAE,UAAQ,IAAE,IAAI,YAAYC,EAAC,GAAED,GAAE,UAAQ,IAAI,aAAaC,EAAC,GAAED,GAAE,UAAQ,IAAI,aAAaC,EAAC;AAAA,EAAC;AAAC,EAAAD,GAAE;AAAe,MAAI,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,CAAC;AAAE,WAAS,IAAG;AAAC,QAAGA,GAAE,OAAO,MAAI,cAAY,OAAOA,GAAE,WAASA,GAAE,SAAO,CAACA,GAAE,MAAM,IAAGA,GAAE,OAAO,SAAQ,GAAEA,GAAE,OAAO,MAAM,CAAC;AAAE,MAAE,CAAC;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,MAAE,CAAC;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,QAAGA,GAAE,QAAQ,MAAI,cAAY,OAAOA,GAAE,YAAUA,GAAE,UAAQ,CAACA,GAAE,OAAO,IAAGA,GAAE,QAAQ,SAAQ,GAAEA,GAAE,QAAQ,MAAM,CAAC;AAAE,MAAE,CAAC;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAE;AAAC,MAAE,QAAQA,EAAC;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAE;AAAC,MAAE,QAAQA,EAAC;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAE;AAAC,MAAE,QAAQA,EAAC;AAAA,EAAC;AAAC,MAAI,IAAE,GAAE,IAAE;AAAK,WAAS,EAAEC,IAAE;AAAC,SAAID,GAAE,0BAAwBA,GAAE,uBAAuB,CAAC;AAAA,EAAC;AAAC,WAAS,EAAEC,IAAE;AAAC,QAAG,KAAID,GAAE,0BAAwBA,GAAE,uBAAuB,CAAC,GAAE,KAAG,KAAG,GAAE;AAAC,UAAIE,KAAE;AAAE,UAAE,MAAKA,GAAE;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,EAAED,IAAE;AAAC,IAAAD,GAAE,WAASA,GAAE,QAAQC,EAAC,GAAE,EAAEA,KAAE,aAAWA,KAAE,GAAG,GAAE,IAAE,MAAGA,MAAG;AAA2C,QAAIC,KAAE,IAAI,YAAY,aAAaD,EAAC;AAAE,UAAME,GAAED,EAAC,GAAEA;AAAA,EAAC;AAAC,MAAI,GAAE,IAAE;AAAwC,WAAS,EAAEF,IAAE;AAAC,WAAOA,GAAE,WAAW,CAAC;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAE;AAAC,WAAOA,GAAE,WAAW,SAAS;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAE;AAAC,QAAG;AAAC,UAAGA,MAAG,KAAG,EAAE,QAAO,IAAI,WAAW,CAAC;AAAE,UAAG,EAAE,QAAO,EAAEA,EAAC;AAAE,YAAK;AAAA,IAAiD,SAAOU,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,QAAG,CAAC,MAAI,KAAG,IAAG;AAAC,UAAG,cAAY,OAAO,SAAO,CAAC,EAAE,CAAC,EAAE,QAAO,MAAM,GAAE,EAAC,aAAY,cAAa,CAAC,EAAE,KAAM,SAASV,IAAE;AAAC,YAAG,CAACA,GAAE,GAAG,OAAK,yCAAuC,IAAE;AAAI,eAAOA,GAAE,YAAY;AAAA,MAAC,CAAE,EAAE,MAAO,WAAU;AAAC,eAAO,EAAE,CAAC;AAAA,MAAC,CAAE;AAAE,UAAGK,GAAE,QAAO,IAAI,QAAS,SAASL,IAAEC,IAAE;AAAC,QAAAI,GAAE,GAAG,SAASJ,IAAE;AAAC,UAAAD,GAAE,IAAI,WAAWC,EAAC,CAAC;AAAA,QAAC,GAAGA,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAC,WAAO,QAAQ,QAAQ,EAAE,KAAM,WAAU;AAAC,aAAO,EAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,QAAIA,KAAE,EAAC,GAAE,GAAE;AAAE,aAASC,GAAED,IAAEC,IAAE;AAAC,UAAIC,KAAEF,GAAE;AAAQ,MAAAD,GAAE,MAAIG,IAAE,GAAG,IAAEH,GAAE,IAAI,GAAG,MAAM,GAAE,IAAEA,GAAE,IAAI,GAAE,EAAEA,GAAE,IAAI,CAAC,GAAE,EAAE;AAAA,IAAC;AAAC,aAASI,GAAEJ,IAAE;AAAC,MAAAE,GAAEF,GAAE,QAAQ;AAAA,IAAC;AAAC,aAASK,GAAEL,IAAE;AAAC,aAAO,EAAE,EAAE,KAAM,SAASA,IAAE;AAAC,eAAO,YAAY,YAAYA,IAAEC,EAAC;AAAA,MAAC,CAAE,EAAE,KAAM,SAASD,IAAE;AAAC,eAAOA;AAAA,MAAC,CAAE,EAAE,KAAKA,IAAG,SAASA,IAAE;AAAC,UAAE,4CAA0CA,EAAC,GAAE,EAAEA,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAC,aAASM,KAAG;AAAC,aAAO,KAAG,cAAY,OAAO,YAAY,wBAAsB,EAAE,CAAC,KAAG,EAAE,CAAC,KAAG,KAAG,cAAY,OAAO,QAAMD,GAAED,EAAC,IAAE,MAAM,GAAE,EAAC,aAAY,cAAa,CAAC,EAAE,KAAM,SAASJ,IAAE;AAAC,eAAO,YAAY,qBAAqBA,IAAEC,EAAC,EAAE,KAAKG,IAAG,SAASJ,IAAE;AAAC,iBAAO,EAAE,oCAAkCA,EAAC,GAAE,EAAE,2CAA2C,GAAEK,GAAED,EAAC;AAAA,QAAC,CAAE;AAAA,MAAC,CAAE;AAAA,IAAC;AAAC,QAAG,EAAE,GAAEJ,GAAE,gBAAgB,KAAG;AAAC,aAAOA,GAAE,gBAAgBC,IAAEC,EAAC;AAAA,IAAC,SAAOK,IAAE;AAAC,aAAO,EAAE,wDAAsDA,EAAC,GAAE;AAAA,IAAE;AAAC,WAAOD,GAAE,EAAE,MAAMH,EAAC,GAAE,CAAC;AAAA,EAAC;AAAC,WAAS,EAAEF,IAAE;AAAC,WAAKA,GAAE,SAAO,KAAG;AAAC,UAAIC,KAAED,GAAE,MAAM;AAAE,UAAG,cAAY,OAAOC,IAAE;AAAC,YAAIC,KAAED,GAAE;AAAK,oBAAU,OAAOC,KAAE,WAASD,GAAE,MAAI,EAAEC,EAAC,EAAE,IAAE,EAAEA,EAAC,EAAED,GAAE,GAAG,IAAEC,GAAE,WAASD,GAAE,MAAI,OAAKA,GAAE,GAAG;AAAA,MAAC,MAAM,CAAAA,GAAEF,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,IAAE,IAAE,gBAAgB,MAAI,IAAE,EAAE,CAAC;AAAG,MAAI,IAAE,CAAC;AAAE,WAAS,EAAEA,IAAE;AAAC,QAAIC,KAAE,EAAED,EAAC;AAAE,WAAOC,OAAID,MAAG,EAAE,WAAS,EAAE,SAAOA,KAAE,IAAG,EAAEA,EAAC,IAAEC,KAAE,EAAE,IAAID,EAAC,IAAGC;AAAA,EAAC;AAAC,WAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAE,uBAAqB,EAAEH,EAAC,IAAE,WAAS,CAACC,KAAE,EAAEA,EAAC,IAAE,oBAAmBC,IAAEC,KAAE,EAAEA,EAAC,IAAE,kBAAkB,CAAC;AAAA,EAAC;AAAC,WAAS,GAAGH,IAAE;AAAC,WAAO,GAAGA,KAAE,EAAE,IAAE;AAAA,EAAE;AAAC,WAAS,GAAGA,IAAE;AAAC,SAAK,SAAOA,IAAE,KAAK,MAAIA,KAAE,IAAG,KAAK,WAAS,SAASA,IAAE;AAAC,QAAE,KAAK,MAAI,KAAG,CAAC,IAAEA;AAAA,IAAC,GAAE,KAAK,WAAS,WAAU;AAAC,aAAO,EAAE,KAAK,MAAI,KAAG,CAAC;AAAA,IAAC,GAAE,KAAK,iBAAe,SAASA,IAAE;AAAC,QAAE,KAAK,MAAI,KAAG,CAAC,IAAEA;AAAA,IAAC,GAAE,KAAK,iBAAe,WAAU;AAAC,aAAO,EAAE,KAAK,MAAI,KAAG,CAAC;AAAA,IAAC,GAAE,KAAK,eAAa,SAASA,IAAE;AAAC,QAAE,KAAK,OAAK,CAAC,IAAEA;AAAA,IAAC,GAAE,KAAK,aAAW,SAASA,IAAE;AAAC,MAAAA,KAAEA,KAAE,IAAE,GAAE,EAAE,KAAK,MAAI,MAAI,CAAC,IAAEA;AAAA,IAAC,GAAE,KAAK,aAAW,WAAU;AAAC,aAAO,KAAG,EAAE,KAAK,MAAI,MAAI,CAAC;AAAA,IAAC,GAAE,KAAK,eAAa,SAASA,IAAE;AAAC,MAAAA,KAAEA,KAAE,IAAE,GAAE,EAAE,KAAK,MAAI,MAAI,CAAC,IAAEA;AAAA,IAAC,GAAE,KAAK,eAAa,WAAU;AAAC,aAAO,KAAG,EAAE,KAAK,MAAI,MAAI,CAAC;AAAA,IAAC,GAAE,KAAK,OAAK,SAASA,IAAEC,IAAE;AAAC,WAAK,iBAAiB,CAAC,GAAE,KAAK,SAASD,EAAC,GAAE,KAAK,eAAeC,EAAC,GAAE,KAAK,aAAa,CAAC,GAAE,KAAK,WAAW,KAAE,GAAE,KAAK,aAAa,KAAE;AAAA,IAAC,GAAE,KAAK,UAAQ,WAAU;AAAC,UAAID,KAAE,EAAE,KAAK,OAAK,CAAC;AAAE,QAAE,KAAK,OAAK,CAAC,IAAEA,KAAE;AAAA,IAAC,GAAE,KAAK,cAAY,WAAU;AAAC,UAAIA,KAAE,EAAE,KAAK,OAAK,CAAC;AAAE,aAAO,EAAE,KAAK,OAAK,CAAC,IAAEA,KAAE,GAAE,MAAIA;AAAA,IAAC,GAAE,KAAK,mBAAiB,SAASA,IAAE;AAAC,QAAE,KAAK,MAAI,MAAI,CAAC,IAAEA;AAAA,IAAC,GAAE,KAAK,mBAAiB,WAAU;AAAC,aAAO,EAAE,KAAK,MAAI,MAAI,CAAC;AAAA,IAAC,GAAE,KAAK,oBAAkB,WAAU;AAAC,UAAG,GAAG,KAAK,SAAS,CAAC,EAAE,QAAO,EAAE,KAAK,UAAQ,CAAC;AAAE,UAAIA,KAAE,KAAK,iBAAiB;AAAE,aAAO,MAAIA,KAAEA,KAAE,KAAK;AAAA,IAAM;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGF,EAAC,EAAE,KAAKC,IAAEC,EAAC,GAAEF;AAAA,EAAC;AAAC,WAAS,KAAI;AAAC,MAAE,EAAE;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,MAAE,WAAWF,IAAEC,IAAEA,KAAEC,EAAC;AAAA,EAAC;AAAC,WAAS,KAAI;AAAC,WAAO;AAAA,EAAU;AAAC,WAAS,GAAGF,IAAE;AAAC,QAAG;AAAC,aAAO,EAAE,KAAKA,KAAE,EAAE,aAAW,UAAQ,EAAE,GAAE,EAAE,EAAE,MAAM,GAAE;AAAA,IAAC,SAAOC,IAAE;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,GAAGD,IAAE;AAAC,QAAIC,KAAE,EAAE;AAAO,IAAAD,QAAK;AAAE,QAAIE,KAAE,GAAG;AAAE,QAAGF,KAAEE,GAAE,QAAM;AAAG,QAAIC,KAAE,CAACH,IAAEC,OAAID,MAAGC,KAAED,KAAEC,MAAGA;AAAE,aAAQG,KAAE,GAAEA,MAAG,GAAEA,MAAG,GAAE;AAAC,UAAIC,KAAEJ,MAAG,IAAE,MAAGG;AAAG,UAAGC,KAAE,KAAK,IAAIA,IAAEL,KAAE,SAAS,GAAE,GAAG,KAAK,IAAIE,IAAEC,GAAE,KAAK,IAAIH,IAAEK,EAAC,GAAE,KAAK,CAAC,CAAC,EAAE,QAAM;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAC,MAAI,KAAG,EAAC,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,GAAE;AAAE,IAAE,GAAEL,GAAE,qBAAmB,WAAU;AAAC,YAAOA,GAAE,qBAAmBA,GAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,EAAC,GAAEA,GAAE,oBAAkB,WAAU;AAAC,YAAOA,GAAE,oBAAkBA,GAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,EAAC,GAAEA,GAAE,sBAAoB,WAAU;AAAC,YAAOA,GAAE,sBAAoBA,GAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,EAAC,GAAEA,GAAE,eAAa,WAAU;AAAC,YAAOA,GAAE,eAAaA,GAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,EAAC,GAAEA,GAAE,kBAAgB,WAAU;AAAC,YAAOA,GAAE,kBAAgBA,GAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,EAAC;AAAE,MAAI,KAAGA,GAAE,UAAQ,WAAU;AAAC,YAAO,KAAGA,GAAE,UAAQA,GAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,EAAC;AAAE,EAAAA,GAAE,QAAM,WAAU;AAAC,YAAOA,GAAE,QAAMA,GAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,EAAC;AAAE,MAAI,IAAG,KAAGA,GAAE,yBAAuB,WAAU;AAAC,YAAO,KAAGA,GAAE,yBAAuBA,GAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,EAAC;AAAE,WAAS,GAAGA,IAAE;AAAC,SAAK,OAAK,cAAa,KAAK,UAAQ,kCAAgCA,KAAE,KAAI,KAAK,SAAOA;AAAA,EAAC;AAAC,WAAS,GAAGE,IAAE;AAAC,aAASC,KAAG;AAAC,aAAK,KAAG,MAAGH,GAAE,YAAU,MAAG,MAAI,EAAE,GAAEC,GAAED,EAAC,GAAEA,GAAE,wBAAsBA,GAAE,qBAAqB,GAAE,EAAE;AAAA,IAAG;AAAC,QAAE,MAAI,EAAE,GAAE,IAAE,MAAIA,GAAE,aAAWA,GAAE,UAAU,YAAY,GAAE,WAAY,WAAU;AAAC,iBAAY,WAAU;AAAC,QAAAA,GAAE,UAAU,EAAE;AAAA,MAAC,GAAG,CAAC,GAAEG,GAAE;AAAA,IAAC,GAAG,CAAC,KAAGA,GAAE;AAAA,EAAG;AAAC,MAAG,IAAE,SAASH,KAAG;AAAC,UAAI,GAAG,GAAE,OAAK,IAAEA;AAAA,EAAE,GAAEA,GAAE,MAAI,IAAGA,GAAE,QAAQ,MAAI,cAAY,OAAOA,GAAE,YAAUA,GAAE,UAAQ,CAACA,GAAE,OAAO,IAAGA,GAAE,QAAQ,SAAO,IAAG,CAAAA,GAAE,QAAQ,IAAI,EAAE;AAAE,SAAO,GAAG,GAAEA,GAAE;AAAK,GAAE,EAAE,UAAQ;AAAE,IAAM,IAAE,EAAE,EAAC,WAAU,MAAK,SAAQ,EAAC,GAAE,CAAC,CAAC,CAAC;", "names": ["t", "n", "e", "r", "i", "o", "u", "s", "a", "c", "g"]}