{"version": 3, "sources": ["../../@codemirror/lang-sql/dist/index.js"], "sourcesContent": ["import { syntaxTree, indentNodeProp, continuedIndent, foldNodeProp, LRLanguage, LanguageSupport } from '@codemirror/language';\nimport { styleTags, tags } from '@lezer/highlight';\nimport { ExternalTokenizer, LRParser } from '@lezer/lr';\nimport { ifNotIn, completeFromList } from '@codemirror/autocomplete';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst whitespace = 36,\n  LineComment = 1,\n  BlockComment = 2,\n  String$1 = 3,\n  Number = 4,\n  Bool = 5,\n  Null = 6,\n  ParenL = 7,\n  ParenR = 8,\n  BraceL = 9,\n  BraceR = 10,\n  BracketL = 11,\n  BracketR = 12,\n  Semi = 13,\n  Dot = 14,\n  Operator = 15,\n  Punctuation = 16,\n  SpecialVar = 17,\n  Identifier = 18,\n  QuotedIdentifier = 19,\n  Keyword = 20,\n  Type = 21,\n  Bits = 22,\n  Bytes = 23,\n  Builtin = 24;\n\nfunction isAlpha(ch) {\n    return ch >= 65 /* Ch.A */ && ch <= 90 /* Ch.Z */ || ch >= 97 /* Ch.a */ && ch <= 122 /* Ch.z */ || ch >= 48 /* Ch._0 */ && ch <= 57 /* Ch._9 */;\n}\nfunction isHexDigit(ch) {\n    return ch >= 48 /* Ch._0 */ && ch <= 57 /* Ch._9 */ || ch >= 97 /* Ch.a */ && ch <= 102 /* Ch.f */ || ch >= 65 /* Ch.A */ && ch <= 70 /* Ch.F */;\n}\nfunction readLiteral(input, endQuote, backslashEscapes) {\n    for (let escaped = false;;) {\n        if (input.next < 0)\n            return;\n        if (input.next == endQuote && !escaped) {\n            input.advance();\n            return;\n        }\n        escaped = backslashEscapes && !escaped && input.next == 92 /* Ch.Backslash */;\n        input.advance();\n    }\n}\nfunction readDoubleDollarLiteral(input, tag) {\n    scan: for (;;) {\n        if (input.next < 0)\n            return;\n        if (input.next == 36 /* Ch.Dollar */) {\n            input.advance();\n            for (let i = 0; i < tag.length; i++) {\n                if (input.next != tag.charCodeAt(i))\n                    continue scan;\n                input.advance();\n            }\n            if (input.next == 36 /* Ch.Dollar */) {\n                input.advance();\n                return;\n            }\n        }\n        else {\n            input.advance();\n        }\n    }\n}\nfunction readPLSQLQuotedLiteral(input, openDelim) {\n    let matchingDelim = \"[{<(\".indexOf(String.fromCharCode(openDelim));\n    let closeDelim = matchingDelim < 0 ? openDelim : \"]}>)\".charCodeAt(matchingDelim);\n    for (;;) {\n        if (input.next < 0)\n            return;\n        if (input.next == closeDelim && input.peek(1) == 39 /* Ch.SingleQuote */) {\n            input.advance(2);\n            return;\n        }\n        input.advance();\n    }\n}\nfunction readWord(input, result) {\n    for (;;) {\n        if (input.next != 95 /* Ch.Underscore */ && !isAlpha(input.next))\n            break;\n        if (result != null)\n            result += String.fromCharCode(input.next);\n        input.advance();\n    }\n    return result;\n}\nfunction readWordOrQuoted(input) {\n    if (input.next == 39 /* Ch.SingleQuote */ || input.next == 34 /* Ch.DoubleQuote */ || input.next == 96 /* Ch.Backtick */) {\n        let quote = input.next;\n        input.advance();\n        readLiteral(input, quote, false);\n    }\n    else {\n        readWord(input);\n    }\n}\nfunction readBits(input, endQuote) {\n    while (input.next == 48 /* Ch._0 */ || input.next == 49 /* Ch._1 */)\n        input.advance();\n    if (endQuote && input.next == endQuote)\n        input.advance();\n}\nfunction readNumber(input, sawDot) {\n    for (;;) {\n        if (input.next == 46 /* Ch.Dot */) {\n            if (sawDot)\n                break;\n            sawDot = true;\n        }\n        else if (input.next < 48 /* Ch._0 */ || input.next > 57 /* Ch._9 */) {\n            break;\n        }\n        input.advance();\n    }\n    if (input.next == 69 /* Ch.E */ || input.next == 101 /* Ch.e */) {\n        input.advance();\n        if (input.next == 43 /* Ch.Plus */ || input.next == 45 /* Ch.Dash */)\n            input.advance();\n        while (input.next >= 48 /* Ch._0 */ && input.next <= 57 /* Ch._9 */)\n            input.advance();\n    }\n}\nfunction eol(input) {\n    while (!(input.next < 0 || input.next == 10 /* Ch.Newline */))\n        input.advance();\n}\nfunction inString(ch, str) {\n    for (let i = 0; i < str.length; i++)\n        if (str.charCodeAt(i) == ch)\n            return true;\n    return false;\n}\nconst Space = \" \\t\\r\\n\";\nfunction keywords(keywords, types, builtin) {\n    let result = Object.create(null);\n    result[\"true\"] = result[\"false\"] = Bool;\n    result[\"null\"] = result[\"unknown\"] = Null;\n    for (let kw of keywords.split(\" \"))\n        if (kw)\n            result[kw] = Keyword;\n    for (let tp of types.split(\" \"))\n        if (tp)\n            result[tp] = Type;\n    for (let kw of (builtin || \"\").split(\" \"))\n        if (kw)\n            result[kw] = Builtin;\n    return result;\n}\nconst SQLTypes = \"array binary bit boolean char character clob date decimal double float int integer interval large national nchar nclob numeric object precision real smallint time timestamp varchar varying \";\nconst SQLKeywords = \"absolute action add after all allocate alter and any are as asc assertion at authorization before begin between both breadth by call cascade cascaded case cast catalog check close collate collation column commit condition connect connection constraint constraints constructor continue corresponding count create cross cube current current_date current_default_transform_group current_transform_group_for_type current_path current_role current_time current_timestamp current_user cursor cycle data day deallocate declare default deferrable deferred delete depth deref desc describe descriptor deterministic diagnostics disconnect distinct do domain drop dynamic each else elseif end end-exec equals escape except exception exec execute exists exit external fetch first for foreign found from free full function general get global go goto grant group grouping handle having hold hour identity if immediate in indicator initially inner inout input insert intersect into is isolation join key language last lateral leading leave left level like limit local localtime localtimestamp locator loop map match method minute modifies module month names natural nesting new next no none not of old on only open option or order ordinality out outer output overlaps pad parameter partial path prepare preserve primary prior privileges procedure public read reads recursive redo ref references referencing relative release repeat resignal restrict result return returns revoke right role rollback rollup routine row rows savepoint schema scroll search second section select session session_user set sets signal similar size some space specific specifictype sql sqlexception sqlstate sqlwarning start state static system_user table temporary then timezone_hour timezone_minute to trailing transaction translation treat trigger under undo union unique unnest until update usage user using value values view when whenever where while with without work write year zone \";\nconst defaults = {\n    backslashEscapes: false,\n    hashComments: false,\n    spaceAfterDashes: false,\n    slashComments: false,\n    doubleQuotedStrings: false,\n    doubleDollarQuotedStrings: false,\n    unquotedBitLiterals: false,\n    treatBitsAsBytes: false,\n    charSetCasts: false,\n    plsqlQuotingMechanism: false,\n    operatorChars: \"*+\\-%<>!=&|~^/\",\n    specialVar: \"?\",\n    identifierQuotes: '\"',\n    caseInsensitiveIdentifiers: false,\n    words: /*@__PURE__*/keywords(SQLKeywords, SQLTypes)\n};\nfunction dialect(spec, kws, types, builtin) {\n    let dialect = {};\n    for (let prop in defaults)\n        dialect[prop] = (spec.hasOwnProperty(prop) ? spec : defaults)[prop];\n    if (kws)\n        dialect.words = keywords(kws, types || \"\", builtin);\n    return dialect;\n}\nfunction tokensFor(d) {\n    return new ExternalTokenizer(input => {\n        var _a;\n        let { next } = input;\n        input.advance();\n        if (inString(next, Space)) {\n            while (inString(input.next, Space))\n                input.advance();\n            input.acceptToken(whitespace);\n        }\n        else if (next == 36 /* Ch.Dollar */ && d.doubleDollarQuotedStrings) {\n            let tag = readWord(input, \"\");\n            if (input.next == 36 /* Ch.Dollar */) {\n                input.advance();\n                readDoubleDollarLiteral(input, tag);\n                input.acceptToken(String$1);\n            }\n        }\n        else if (next == 39 /* Ch.SingleQuote */ || next == 34 /* Ch.DoubleQuote */ && d.doubleQuotedStrings) {\n            readLiteral(input, next, d.backslashEscapes);\n            input.acceptToken(String$1);\n        }\n        else if (next == 35 /* Ch.Hash */ && d.hashComments ||\n            next == 47 /* Ch.Slash */ && input.next == 47 /* Ch.Slash */ && d.slashComments) {\n            eol(input);\n            input.acceptToken(LineComment);\n        }\n        else if (next == 45 /* Ch.Dash */ && input.next == 45 /* Ch.Dash */ &&\n            (!d.spaceAfterDashes || input.peek(1) == 32 /* Ch.Space */)) {\n            eol(input);\n            input.acceptToken(LineComment);\n        }\n        else if (next == 47 /* Ch.Slash */ && input.next == 42 /* Ch.Star */) {\n            input.advance();\n            for (let depth = 1;;) {\n                let cur = input.next;\n                if (input.next < 0)\n                    break;\n                input.advance();\n                if (cur == 42 /* Ch.Star */ && input.next == 47 /* Ch.Slash */) {\n                    depth--;\n                    input.advance();\n                    if (!depth)\n                        break;\n                }\n                else if (cur == 47 /* Ch.Slash */ && input.next == 42 /* Ch.Star */) {\n                    depth++;\n                    input.advance();\n                }\n            }\n            input.acceptToken(BlockComment);\n        }\n        else if ((next == 101 /* Ch.e */ || next == 69 /* Ch.E */) && input.next == 39 /* Ch.SingleQuote */) {\n            input.advance();\n            readLiteral(input, 39 /* Ch.SingleQuote */, true);\n            input.acceptToken(String$1);\n        }\n        else if ((next == 110 /* Ch.n */ || next == 78 /* Ch.N */) && input.next == 39 /* Ch.SingleQuote */ &&\n            d.charSetCasts) {\n            input.advance();\n            readLiteral(input, 39 /* Ch.SingleQuote */, d.backslashEscapes);\n            input.acceptToken(String$1);\n        }\n        else if (next == 95 /* Ch.Underscore */ && d.charSetCasts) {\n            for (let i = 0;; i++) {\n                if (input.next == 39 /* Ch.SingleQuote */ && i > 1) {\n                    input.advance();\n                    readLiteral(input, 39 /* Ch.SingleQuote */, d.backslashEscapes);\n                    input.acceptToken(String$1);\n                    break;\n                }\n                if (!isAlpha(input.next))\n                    break;\n                input.advance();\n            }\n        }\n        else if (d.plsqlQuotingMechanism &&\n            (next == 113 /* Ch.q */ || next == 81 /* Ch.Q */) && input.next == 39 /* Ch.SingleQuote */ &&\n            input.peek(1) > 0 && !inString(input.peek(1), Space)) {\n            let openDelim = input.peek(1);\n            input.advance(2);\n            readPLSQLQuotedLiteral(input, openDelim);\n            input.acceptToken(String$1);\n        }\n        else if (next == 40 /* Ch.ParenL */) {\n            input.acceptToken(ParenL);\n        }\n        else if (next == 41 /* Ch.ParenR */) {\n            input.acceptToken(ParenR);\n        }\n        else if (next == 123 /* Ch.BraceL */) {\n            input.acceptToken(BraceL);\n        }\n        else if (next == 125 /* Ch.BraceR */) {\n            input.acceptToken(BraceR);\n        }\n        else if (next == 91 /* Ch.BracketL */) {\n            input.acceptToken(BracketL);\n        }\n        else if (next == 93 /* Ch.BracketR */) {\n            input.acceptToken(BracketR);\n        }\n        else if (next == 59 /* Ch.Semi */) {\n            input.acceptToken(Semi);\n        }\n        else if (d.unquotedBitLiterals && next == 48 /* Ch._0 */ && input.next == 98 /* Ch.b */) {\n            input.advance();\n            readBits(input);\n            input.acceptToken(Bits);\n        }\n        else if ((next == 98 /* Ch.b */ || next == 66 /* Ch.B */) && (input.next == 39 /* Ch.SingleQuote */ || input.next == 34 /* Ch.DoubleQuote */)) {\n            const quoteStyle = input.next;\n            input.advance();\n            if (d.treatBitsAsBytes) {\n                readLiteral(input, quoteStyle, d.backslashEscapes);\n                input.acceptToken(Bytes);\n            }\n            else {\n                readBits(input, quoteStyle);\n                input.acceptToken(Bits);\n            }\n        }\n        else if (next == 48 /* Ch._0 */ && (input.next == 120 /* Ch.x */ || input.next == 88 /* Ch.X */) ||\n            (next == 120 /* Ch.x */ || next == 88 /* Ch.X */) && input.next == 39 /* Ch.SingleQuote */) {\n            let quoted = input.next == 39 /* Ch.SingleQuote */;\n            input.advance();\n            while (isHexDigit(input.next))\n                input.advance();\n            if (quoted && input.next == 39 /* Ch.SingleQuote */)\n                input.advance();\n            input.acceptToken(Number);\n        }\n        else if (next == 46 /* Ch.Dot */ && input.next >= 48 /* Ch._0 */ && input.next <= 57 /* Ch._9 */) {\n            readNumber(input, true);\n            input.acceptToken(Number);\n        }\n        else if (next == 46 /* Ch.Dot */) {\n            input.acceptToken(Dot);\n        }\n        else if (next >= 48 /* Ch._0 */ && next <= 57 /* Ch._9 */) {\n            readNumber(input, false);\n            input.acceptToken(Number);\n        }\n        else if (inString(next, d.operatorChars)) {\n            while (inString(input.next, d.operatorChars))\n                input.advance();\n            input.acceptToken(Operator);\n        }\n        else if (inString(next, d.specialVar)) {\n            if (input.next == next)\n                input.advance();\n            readWordOrQuoted(input);\n            input.acceptToken(SpecialVar);\n        }\n        else if (inString(next, d.identifierQuotes)) {\n            readLiteral(input, next, false);\n            input.acceptToken(QuotedIdentifier);\n        }\n        else if (next == 58 /* Ch.Colon */ || next == 44 /* Ch.Comma */) {\n            input.acceptToken(Punctuation);\n        }\n        else if (isAlpha(next)) {\n            let word = readWord(input, String.fromCharCode(next));\n            input.acceptToken(input.next == 46 /* Ch.Dot */ || input.peek(-word.length - 1) == 46 /* Ch.Dot */\n                ? Identifier : (_a = d.words[word.toLowerCase()]) !== null && _a !== void 0 ? _a : Identifier);\n        }\n    });\n}\nconst tokens = /*@__PURE__*/tokensFor(defaults);\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser$1 = /*@__PURE__*/LRParser.deserialize({\n  version: 14,\n  states: \"%vQ]QQOOO#wQRO'#DSO$OQQO'#CwO%eQQO'#CxO%lQQO'#CyO%sQQO'#CzOOQQ'#DS'#DSOOQQ'#C}'#C}O'UQRO'#C{OOQQ'#Cv'#CvOOQQ'#C|'#C|Q]QQOOQOQQOOO'`QQO'#DOO(xQRO,59cO)PQQO,59cO)UQQO'#DSOOQQ,59d,59dO)cQQO,59dOOQQ,59e,59eO)jQQO,59eOOQQ,59f,59fO)qQQO,59fOOQQ-E6{-E6{OOQQ,59b,59bOOQQ-E6z-E6zOOQQ,59j,59jOOQQ-E6|-E6|O+VQRO1G.}O+^QQO,59cOOQQ1G/O1G/OOOQQ1G/P1G/POOQQ1G/Q1G/QP+kQQO'#C}O+rQQO1G.}O)PQQO,59cO,PQQO'#Cw\",\n  stateData: \",[~OtOSPOSQOS~ORUOSUOTUOUUOVROXSOZTO]XO^QO_UO`UOaPObPOcPOdUOeUOfUOgUOhUO~O^]ORvXSvXTvXUvXVvXXvXZvX]vX_vX`vXavXbvXcvXdvXevXfvXgvXhvX~OsvX~P!jOa_Ob_Oc_O~ORUOSUOTUOUUOVROXSOZTO^tO_UO`UOa`Ob`Oc`OdUOeUOfUOgUOhUO~OWaO~P$ZOYcO~P$ZO[eO~P$ZORUOSUOTUOUUOVROXSOZTO^QO_UO`UOaPObPOcPOdUOeUOfUOgUOhUO~O]hOsoX~P%zOajObjOcjO~O^]ORkaSkaTkaUkaVkaXkaZka]ka_ka`kaakabkackadkaekafkagkahka~Oska~P'kO^]O~OWvXYvX[vX~P!jOWnO~P$ZOYoO~P$ZO[pO~P$ZO^]ORkiSkiTkiUkiVkiXkiZki]ki_ki`kiakibkickidkiekifkigkihki~Oski~P)xOWkaYka[ka~P'kO]hO~P$ZOWkiYki[ki~P)xOasObsOcsO~O\",\n  goto: \"#hwPPPPPPPPPPPPPPPPPPPPPPPPPPx||||!Y!^!d!xPPP#[TYOZeUORSTWZbdfqT[OZQZORiZSWOZQbRQdSQfTZgWbdfqQ^PWk^lmrQl_Qm`RrseVORSTWZbdfq\",\n  nodeNames: \"⚠ LineComment BlockComment String Number Bool Null ( ) { } [ ] ; . Operator Punctuation SpecialVar Identifier QuotedIdentifier Keyword Type Bits Bytes Builtin Script Statement CompositeIdentifier Parens Braces Brackets Statement\",\n  maxTerm: 38,\n  nodeProps: [\n    [\"isolate\", -4,1,2,3,19,\"\"]\n  ],\n  skippedNodes: [0,1,2],\n  repeatNodeCount: 3,\n  tokenData: \"RORO\",\n  tokenizers: [0, tokens],\n  topRules: {\"Script\":[0,25]},\n  tokenPrec: 0\n});\n\nfunction tokenBefore(tree) {\n    let cursor = tree.cursor().moveTo(tree.from, -1);\n    while (/Comment/.test(cursor.name))\n        cursor.moveTo(cursor.from, -1);\n    return cursor.node;\n}\nfunction idName(doc, node) {\n    let text = doc.sliceString(node.from, node.to);\n    let quoted = /^([`'\"])(.*)\\1$/.exec(text);\n    return quoted ? quoted[2] : text;\n}\nfunction plainID(node) {\n    return node && (node.name == \"Identifier\" || node.name == \"QuotedIdentifier\");\n}\nfunction pathFor(doc, id) {\n    if (id.name == \"CompositeIdentifier\") {\n        let path = [];\n        for (let ch = id.firstChild; ch; ch = ch.nextSibling)\n            if (plainID(ch))\n                path.push(idName(doc, ch));\n        return path;\n    }\n    return [idName(doc, id)];\n}\nfunction parentsFor(doc, node) {\n    for (let path = [];;) {\n        if (!node || node.name != \".\")\n            return path;\n        let name = tokenBefore(node);\n        if (!plainID(name))\n            return path;\n        path.unshift(idName(doc, name));\n        node = tokenBefore(name);\n    }\n}\nfunction sourceContext(state, startPos) {\n    let pos = syntaxTree(state).resolveInner(startPos, -1);\n    let aliases = getAliases(state.doc, pos);\n    if (pos.name == \"Identifier\" || pos.name == \"QuotedIdentifier\" || pos.name == \"Keyword\") {\n        return { from: pos.from,\n            quoted: pos.name == \"QuotedIdentifier\" ? state.doc.sliceString(pos.from, pos.from + 1) : null,\n            parents: parentsFor(state.doc, tokenBefore(pos)),\n            aliases };\n    }\n    if (pos.name == \".\") {\n        return { from: startPos, quoted: null, parents: parentsFor(state.doc, pos), aliases };\n    }\n    else {\n        return { from: startPos, quoted: null, parents: [], empty: true, aliases };\n    }\n}\nconst EndFrom = /*@__PURE__*/new Set(/*@__PURE__*/\"where group having order union intersect except all distinct limit offset fetch for\".split(\" \"));\nfunction getAliases(doc, at) {\n    let statement;\n    for (let parent = at; !statement; parent = parent.parent) {\n        if (!parent)\n            return null;\n        if (parent.name == \"Statement\")\n            statement = parent;\n    }\n    let aliases = null;\n    for (let scan = statement.firstChild, sawFrom = false, prevID = null; scan; scan = scan.nextSibling) {\n        let kw = scan.name == \"Keyword\" ? doc.sliceString(scan.from, scan.to).toLowerCase() : null;\n        let alias = null;\n        if (!sawFrom) {\n            sawFrom = kw == \"from\";\n        }\n        else if (kw == \"as\" && prevID && plainID(scan.nextSibling)) {\n            alias = idName(doc, scan.nextSibling);\n        }\n        else if (kw && EndFrom.has(kw)) {\n            break;\n        }\n        else if (prevID && plainID(scan)) {\n            alias = idName(doc, scan);\n        }\n        if (alias) {\n            if (!aliases)\n                aliases = Object.create(null);\n            aliases[alias] = pathFor(doc, prevID);\n        }\n        prevID = /Identifier$/.test(scan.name) ? scan : null;\n    }\n    return aliases;\n}\nfunction maybeQuoteCompletions(quote, completions) {\n    if (!quote)\n        return completions;\n    return completions.map(c => (Object.assign(Object.assign({}, c), { label: c.label[0] == quote ? c.label : quote + c.label + quote, apply: undefined })));\n}\nconst Span = /^\\w*$/, QuotedSpan = /^[`'\"]?\\w*[`'\"]?$/;\nfunction isSelfTag(namespace) {\n    return namespace.self && typeof namespace.self.label == \"string\";\n}\nclass CompletionLevel {\n    constructor(idQuote, idCaseInsensitive) {\n        this.idQuote = idQuote;\n        this.idCaseInsensitive = idCaseInsensitive;\n        this.list = [];\n        this.children = undefined;\n    }\n    child(name) {\n        let children = this.children || (this.children = Object.create(null));\n        let found = children[name];\n        if (found)\n            return found;\n        if (name && !this.list.some(c => c.label == name))\n            this.list.push(nameCompletion(name, \"type\", this.idQuote, this.idCaseInsensitive));\n        return (children[name] = new CompletionLevel(this.idQuote, this.idCaseInsensitive));\n    }\n    maybeChild(name) {\n        return this.children ? this.children[name] : null;\n    }\n    addCompletion(option) {\n        let found = this.list.findIndex(o => o.label == option.label);\n        if (found > -1)\n            this.list[found] = option;\n        else\n            this.list.push(option);\n    }\n    addCompletions(completions) {\n        for (let option of completions)\n            this.addCompletion(typeof option == \"string\" ? nameCompletion(option, \"property\", this.idQuote, this.idCaseInsensitive) : option);\n    }\n    addNamespace(namespace) {\n        if (Array.isArray(namespace)) {\n            this.addCompletions(namespace);\n        }\n        else if (isSelfTag(namespace)) {\n            this.addNamespace(namespace.children);\n        }\n        else {\n            this.addNamespaceObject(namespace);\n        }\n    }\n    addNamespaceObject(namespace) {\n        for (let name of Object.keys(namespace)) {\n            let children = namespace[name], self = null;\n            let parts = name.replace(/\\\\?\\./g, p => p == \".\" ? \"\\0\" : p).split(\"\\0\");\n            let scope = this;\n            if (isSelfTag(children)) {\n                self = children.self;\n                children = children.children;\n            }\n            for (let i = 0; i < parts.length; i++) {\n                if (self && i == parts.length - 1)\n                    scope.addCompletion(self);\n                scope = scope.child(parts[i].replace(/\\\\\\./g, \".\"));\n            }\n            scope.addNamespace(children);\n        }\n    }\n}\nfunction nameCompletion(label, type, idQuote, idCaseInsensitive) {\n    if ((new RegExp(\"^[a-z_][a-z_\\\\d]*$\", idCaseInsensitive ? \"i\" : \"\")).test(label))\n        return { label, type };\n    return { label, type, apply: idQuote + label + idQuote };\n}\n// Some of this is more gnarly than it has to be because we're also\n// supporting the deprecated, not-so-well-considered style of\n// supplying the schema (dotted property names for schemas, separate\n// `tables` and `schemas` completions).\nfunction completeFromSchema(schema, tables, schemas, defaultTableName, defaultSchemaName, dialect) {\n    var _a;\n    let idQuote = ((_a = dialect === null || dialect === void 0 ? void 0 : dialect.spec.identifierQuotes) === null || _a === void 0 ? void 0 : _a[0]) || '\"';\n    let top = new CompletionLevel(idQuote, !!(dialect === null || dialect === void 0 ? void 0 : dialect.spec.caseInsensitiveIdentifiers));\n    let defaultSchema = defaultSchemaName ? top.child(defaultSchemaName) : null;\n    top.addNamespace(schema);\n    if (tables)\n        (defaultSchema || top).addCompletions(tables);\n    if (schemas)\n        top.addCompletions(schemas);\n    if (defaultSchema)\n        top.addCompletions(defaultSchema.list);\n    if (defaultTableName)\n        top.addCompletions((defaultSchema || top).child(defaultTableName).list);\n    return (context) => {\n        let { parents, from, quoted, empty, aliases } = sourceContext(context.state, context.pos);\n        if (empty && !context.explicit)\n            return null;\n        if (aliases && parents.length == 1)\n            parents = aliases[parents[0]] || parents;\n        let level = top;\n        for (let name of parents) {\n            while (!level.children || !level.children[name]) {\n                if (level == top && defaultSchema)\n                    level = defaultSchema;\n                else if (level == defaultSchema && defaultTableName)\n                    level = level.child(defaultTableName);\n                else\n                    return null;\n            }\n            let next = level.maybeChild(name);\n            if (!next)\n                return null;\n            level = next;\n        }\n        let quoteAfter = quoted && context.state.sliceDoc(context.pos, context.pos + 1) == quoted;\n        let options = level.list;\n        if (level == top && aliases)\n            options = options.concat(Object.keys(aliases).map(name => ({ label: name, type: \"constant\" })));\n        return {\n            from,\n            to: quoteAfter ? context.pos + 1 : undefined,\n            options: maybeQuoteCompletions(quoted, options),\n            validFor: quoted ? QuotedSpan : Span\n        };\n    };\n}\nfunction completeKeywords(keywords, upperCase) {\n    let completions = Object.keys(keywords).map(keyword => ({\n        label: upperCase ? keyword.toUpperCase() : keyword,\n        type: keywords[keyword] == Type ? \"type\" : keywords[keyword] == Keyword ? \"keyword\" : \"variable\",\n        boost: -1\n    }));\n    return ifNotIn([\"QuotedIdentifier\", \"SpecialVar\", \"String\", \"LineComment\", \"BlockComment\", \".\"], completeFromList(completions));\n}\n\nlet parser = /*@__PURE__*/parser$1.configure({\n    props: [\n        /*@__PURE__*/indentNodeProp.add({\n            Statement: /*@__PURE__*/continuedIndent()\n        }),\n        /*@__PURE__*/foldNodeProp.add({\n            Statement(tree, state) { return { from: Math.min(tree.from + 100, state.doc.lineAt(tree.from).to), to: tree.to }; },\n            BlockComment(tree) { return { from: tree.from + 2, to: tree.to - 2 }; }\n        }),\n        /*@__PURE__*/styleTags({\n            Keyword: tags.keyword,\n            Type: tags.typeName,\n            Builtin: /*@__PURE__*/tags.standard(tags.name),\n            Bits: tags.number,\n            Bytes: tags.string,\n            Bool: tags.bool,\n            Null: tags.null,\n            Number: tags.number,\n            String: tags.string,\n            Identifier: tags.name,\n            QuotedIdentifier: /*@__PURE__*/tags.special(tags.string),\n            SpecialVar: /*@__PURE__*/tags.special(tags.name),\n            LineComment: tags.lineComment,\n            BlockComment: tags.blockComment,\n            Operator: tags.operator,\n            \"Semi Punctuation\": tags.punctuation,\n            \"( )\": tags.paren,\n            \"{ }\": tags.brace,\n            \"[ ]\": tags.squareBracket\n        })\n    ]\n});\n/**\nRepresents an SQL dialect.\n*/\nclass SQLDialect {\n    constructor(\n    /**\n    @internal\n    */\n    dialect, \n    /**\n    The language for this dialect.\n    */\n    language, \n    /**\n    The spec used to define this dialect.\n    */\n    spec) {\n        this.dialect = dialect;\n        this.language = language;\n        this.spec = spec;\n    }\n    /**\n    Returns the language for this dialect as an extension.\n    */\n    get extension() { return this.language.extension; }\n    /**\n    Define a new dialect.\n    */\n    static define(spec) {\n        let d = dialect(spec, spec.keywords, spec.types, spec.builtin);\n        let language = LRLanguage.define({\n            name: \"sql\",\n            parser: parser.configure({\n                tokenizers: [{ from: tokens, to: tokensFor(d) }]\n            }),\n            languageData: {\n                commentTokens: { line: \"--\", block: { open: \"/*\", close: \"*/\" } },\n                closeBrackets: { brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"] }\n            }\n        });\n        return new SQLDialect(d, language, spec);\n    }\n}\n/**\nReturns a completion source that provides keyword completion for\nthe given SQL dialect.\n*/\nfunction keywordCompletionSource(dialect, upperCase = false) {\n    return completeKeywords(dialect.dialect.words, upperCase);\n}\n/**\nFIXME remove on 1.0 @internal\n*/\nfunction keywordCompletion(dialect, upperCase = false) {\n    return dialect.language.data.of({\n        autocomplete: keywordCompletionSource(dialect, upperCase)\n    });\n}\n/**\nReturns a completion sources that provides schema-based completion\nfor the given configuration.\n*/\nfunction schemaCompletionSource(config) {\n    return config.schema ? completeFromSchema(config.schema, config.tables, config.schemas, config.defaultTable, config.defaultSchema, config.dialect || StandardSQL)\n        : () => null;\n}\n/**\nFIXME remove on 1.0 @internal\n*/\nfunction schemaCompletion(config) {\n    return config.schema ? (config.dialect || StandardSQL).language.data.of({\n        autocomplete: schemaCompletionSource(config)\n    }) : [];\n}\n/**\nSQL language support for the given SQL dialect, with keyword\ncompletion, and, if provided, schema-based completion as extra\nextensions.\n*/\nfunction sql(config = {}) {\n    let lang = config.dialect || StandardSQL;\n    return new LanguageSupport(lang.language, [schemaCompletion(config), keywordCompletion(lang, !!config.upperCaseKeywords)]);\n}\n/**\nThe standard SQL dialect.\n*/\nconst StandardSQL = /*@__PURE__*/SQLDialect.define({});\n/**\nDialect for [PostgreSQL](https://www.postgresql.org).\n*/\nconst PostgreSQL = /*@__PURE__*/SQLDialect.define({\n    charSetCasts: true,\n    doubleDollarQuotedStrings: true,\n    operatorChars: \"+-*/<>=~!@#%^&|`?\",\n    specialVar: \"\",\n    keywords: SQLKeywords + \"abort abs absent access according ada admin aggregate alias also always analyse analyze array_agg array_max_cardinality asensitive assert assignment asymmetric atomic attach attribute attributes avg backward base64 begin_frame begin_partition bernoulli bit_length blocked bom cache called cardinality catalog_name ceil ceiling chain char_length character_length character_set_catalog character_set_name character_set_schema characteristics characters checkpoint class class_origin cluster coalesce cobol collation_catalog collation_name collation_schema collect column_name columns command_function command_function_code comment comments committed concurrently condition_number configuration conflict connection_name constant constraint_catalog constraint_name constraint_schema contains content control conversion convert copy corr cost covar_pop covar_samp csv cume_dist current_catalog current_row current_schema cursor_name database datalink datatype datetime_interval_code datetime_interval_precision db debug defaults defined definer degree delimiter delimiters dense_rank depends derived detach detail dictionary disable discard dispatch dlnewcopy dlpreviouscopy dlurlcomplete dlurlcompleteonly dlurlcompletewrite dlurlpath dlurlpathonly dlurlpathwrite dlurlscheme dlurlserver dlvalue document dump dynamic_function dynamic_function_code element elsif empty enable encoding encrypted end_frame end_partition endexec enforced enum errcode error event every exclude excluding exclusive exp explain expression extension extract family file filter final first_value flag floor following force foreach fortran forward frame_row freeze fs functions fusion generated granted greatest groups handler header hex hierarchy hint id ignore ilike immediately immutable implementation implicit import include including increment indent index indexes info inherit inherits inline insensitive instance instantiable instead integrity intersection invoker isnull key_member key_type label lag last_value lead leakproof least length library like_regex link listen ln load location lock locked log logged lower mapping matched materialized max max_cardinality maxvalue member merge message message_length message_octet_length message_text min minvalue mod mode more move multiset mumps name namespace nfc nfd nfkc nfkd nil normalize normalized nothing notice notify notnull nowait nth_value ntile nullable nullif nulls number occurrences_regex octet_length octets off offset oids operator options ordering others over overlay overriding owned owner parallel parameter_mode parameter_name parameter_ordinal_position parameter_specific_catalog parameter_specific_name parameter_specific_schema parser partition pascal passing passthrough password percent percent_rank percentile_cont percentile_disc perform period permission pg_context pg_datatype_name pg_exception_context pg_exception_detail pg_exception_hint placing plans pli policy portion position position_regex power precedes preceding prepared print_strict_params procedural procedures program publication query quote raise range rank reassign recheck recovery refresh regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy regr_syy reindex rename repeatable replace replica requiring reset respect restart restore result_oid returned_cardinality returned_length returned_octet_length returned_sqlstate returning reverse routine_catalog routine_name routine_schema routines row_count row_number rowtype rule scale schema_name schemas scope scope_catalog scope_name scope_schema security selective self sensitive sequence sequences serializable server server_name setof share show simple skip slice snapshot source specific_name sqlcode sqlerror sqrt stable stacked standalone statement statistics stddev_pop stddev_samp stdin stdout storage strict strip structure style subclass_origin submultiset subscription substring substring_regex succeeds sum symmetric sysid system system_time table_name tables tablesample tablespace temp template ties token top_level_count transaction_active transactions_committed transactions_rolled_back transform transforms translate translate_regex trigger_catalog trigger_name trigger_schema trim trim_array truncate trusted type types uescape unbounded uncommitted unencrypted unlink unlisten unlogged unnamed untyped upper uri use_column use_variable user_defined_type_catalog user_defined_type_code user_defined_type_name user_defined_type_schema vacuum valid validate validator value_of var_pop var_samp varbinary variable_conflict variadic verbose version versioning views volatile warning whitespace width_bucket window within wrapper xmlagg xmlattributes xmlbinary xmlcast xmlcomment xmlconcat xmldeclaration xmldocument xmlelement xmlexists xmlforest xmliterate xmlnamespaces xmlparse xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltext xmlvalidate yes\",\n    types: SQLTypes + \"bigint int8 bigserial serial8 varbit bool box bytea cidr circle precision float8 inet int4 json jsonb line lseg macaddr macaddr8 money numeric pg_lsn point polygon float4 int2 smallserial serial2 serial serial4 text timetz timestamptz tsquery tsvector txid_snapshot uuid xml\"\n});\nconst MySQLKeywords = \"accessible algorithm analyze asensitive authors auto_increment autocommit avg avg_row_length binlog btree cache catalog_name chain change changed checkpoint checksum class_origin client_statistics coalesce code collations columns comment committed completion concurrent consistent contains contributors convert database databases day_hour day_microsecond day_minute day_second delay_key_write delayed delimiter des_key_file dev_pop dev_samp deviance directory disable discard distinctrow div dual dumpfile enable enclosed ends engine engines enum errors escaped even event events every explain extended fast field fields flush force found_rows fulltext grants handler hash high_priority hosts hour_microsecond hour_minute hour_second ignore ignore_server_ids import index index_statistics infile innodb insensitive insert_method install invoker iterate keys kill linear lines list load lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modify mutex mysql_errno no_write_to_binlog offline offset one online optimize optionally outfile pack_keys parser partition partitions password phase plugin plugins prev processlist profile profiles purge query quick range read_write rebuild recover regexp relaylog remove rename reorganize repair repeatable replace require resume rlike row_format rtree schedule schema_name schemas second_microsecond security sensitive separator serializable server share show slave slow snapshot soname spatial sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result ssl starting starts std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace terminated triggers truncate uncommitted uninstall unlock upgrade use use_frm user_resources user_statistics utc_date utc_time utc_timestamp variables views warnings xa xor year_month zerofill\";\nconst MySQLTypes = SQLTypes + \"bool blob long longblob longtext medium mediumblob mediumint mediumtext tinyblob tinyint tinytext text bigint int1 int2 int3 int4 int8 float4 float8 varbinary varcharacter precision datetime unsigned signed\";\nconst MySQLBuiltin = \"charset clear edit ego help nopager notee nowarning pager print prompt quit rehash source status system tee\";\n/**\n[MySQL](https://dev.mysql.com/) dialect.\n*/\nconst MySQL = /*@__PURE__*/SQLDialect.define({\n    operatorChars: \"*+-%<>!=&|^\",\n    charSetCasts: true,\n    doubleQuotedStrings: true,\n    unquotedBitLiterals: true,\n    hashComments: true,\n    spaceAfterDashes: true,\n    specialVar: \"@?\",\n    identifierQuotes: \"`\",\n    keywords: SQLKeywords + \"group_concat \" + MySQLKeywords,\n    types: MySQLTypes,\n    builtin: MySQLBuiltin\n});\n/**\nVariant of [`MySQL`](https://codemirror.net/6/docs/ref/#lang-sql.MySQL) for\n[MariaDB](https://mariadb.org/).\n*/\nconst MariaSQL = /*@__PURE__*/SQLDialect.define({\n    operatorChars: \"*+-%<>!=&|^\",\n    charSetCasts: true,\n    doubleQuotedStrings: true,\n    unquotedBitLiterals: true,\n    hashComments: true,\n    spaceAfterDashes: true,\n    specialVar: \"@?\",\n    identifierQuotes: \"`\",\n    keywords: SQLKeywords + \"always generated groupby_concat hard persistent shutdown soft virtual \" + MySQLKeywords,\n    types: MySQLTypes,\n    builtin: MySQLBuiltin\n});\n/**\nSQL dialect for Microsoft [SQL\nServer](https://www.microsoft.com/en-us/sql-server).\n*/\nconst MSSQL = /*@__PURE__*/SQLDialect.define({\n    keywords: SQLKeywords + \"trigger proc view index for add constraint key primary foreign collate clustered nonclustered declare exec go if use index holdlock nolock nowait paglock pivot readcommitted readcommittedlock readpast readuncommitted repeatableread rowlock serializable snapshot tablock tablockx unpivot updlock with\",\n    types: SQLTypes + \"bigint smallint smallmoney tinyint money real text nvarchar ntext varbinary image hierarchyid uniqueidentifier sql_variant xml\",\n    builtin: \"binary_checksum checksum connectionproperty context_info current_request_id error_line error_message error_number error_procedure error_severity error_state formatmessage get_filestream_transaction_context getansinull host_id host_name isnull isnumeric min_active_rowversion newid newsequentialid rowcount_big xact_state object_id\",\n    operatorChars: \"*+-%<>!=^&|/\",\n    specialVar: \"@\"\n});\n/**\n[SQLite](https://sqlite.org/) dialect.\n*/\nconst SQLite = /*@__PURE__*/SQLDialect.define({\n    keywords: SQLKeywords + \"abort analyze attach autoincrement conflict database detach exclusive fail glob ignore index indexed instead isnull notnull offset plan pragma query raise regexp reindex rename replace temp vacuum virtual\",\n    types: SQLTypes + \"bool blob long longblob longtext medium mediumblob mediumint mediumtext tinyblob tinyint tinytext text bigint int2 int8 unsigned signed real\",\n    builtin: \"auth backup bail changes clone databases dbinfo dump echo eqp explain fullschema headers help import imposter indexes iotrace lint load log mode nullvalue once print prompt quit restore save scanstats separator shell show stats system tables testcase timeout timer trace vfsinfo vfslist vfsname width\",\n    operatorChars: \"*+-%<>!=&|/~\",\n    identifierQuotes: \"`\\\"\",\n    specialVar: \"@:?$\"\n});\n/**\nDialect for [Cassandra](https://cassandra.apache.org/)'s SQL-ish query language.\n*/\nconst Cassandra = /*@__PURE__*/SQLDialect.define({\n    keywords: \"add all allow alter and any apply as asc authorize batch begin by clustering columnfamily compact consistency count create custom delete desc distinct drop each_quorum exists filtering from grant if in index insert into key keyspace keyspaces level limit local_one local_quorum modify nan norecursive nosuperuser not of on one order password permission permissions primary quorum rename revoke schema select set storage superuser table three to token truncate ttl two type unlogged update use user users using values where with writetime infinity NaN\",\n    types: SQLTypes + \"ascii bigint blob counter frozen inet list map static text timeuuid tuple uuid varint\",\n    slashComments: true\n});\n/**\n[PL/SQL](https://en.wikipedia.org/wiki/PL/SQL) dialect.\n*/\nconst PLSQL = /*@__PURE__*/SQLDialect.define({\n    keywords: SQLKeywords + \"abort accept access add all alter and any arraylen as asc assert assign at attributes audit authorization avg base_table begin between binary_integer body by case cast char_base check close cluster clusters colauth column comment commit compress connected constant constraint crash create current currval cursor data_base database dba deallocate debugoff debugon declare default definition delay delete desc digits dispose distinct do drop else elseif elsif enable end entry exception exception_init exchange exclusive exists external fast fetch file for force form from function generic goto grant group having identified if immediate in increment index indexes indicator initial initrans insert interface intersect into is key level library like limited local lock log logging loop master maxextents maxtrans member minextents minus mislabel mode modify multiset new next no noaudit nocompress nologging noparallel not nowait number_base of off offline on online only option or order out package parallel partition pctfree pctincrease pctused pls_integer positive positiven pragma primary prior private privileges procedure public raise range raw rebuild record ref references refresh rename replace resource restrict return returning returns reverse revoke rollback row rowid rowlabel rownum rows run savepoint schema segment select separate set share snapshot some space split sql start statement storage subtype successful synonym tabauth table tables tablespace task terminate then to trigger truncate type union unique unlimited unrecoverable unusable update use using validate value values variable view views when whenever where while with work\",\n    builtin: \"appinfo arraysize autocommit autoprint autorecovery autotrace blockterminator break btitle cmdsep colsep compatibility compute concat copycommit copytypecheck define echo editfile embedded feedback flagger flush heading headsep instance linesize lno loboffset logsource longchunksize markup native newpage numformat numwidth pagesize pause pno recsep recsepchar repfooter repheader serveroutput shiftinout show showmode spool sqlblanklines sqlcase sqlcode sqlcontinue sqlnumber sqlpluscompatibility sqlprefix sqlprompt sqlterminator suffix tab term termout timing trimout trimspool ttitle underline verify version wrap\",\n    types: SQLTypes + \"ascii bfile bfilename bigserial bit blob dec long number nvarchar nvarchar2 serial smallint string text uid varchar2 xml\",\n    operatorChars: \"*/+-%<>!=~\",\n    doubleQuotedStrings: true,\n    charSetCasts: true,\n    plsqlQuotingMechanism: true\n});\n\nexport { Cassandra, MSSQL, MariaSQL, MySQL, PLSQL, PostgreSQL, SQLDialect, SQLite, StandardSQL, keywordCompletion, keywordCompletionSource, schemaCompletion, schemaCompletionSource, sql };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,aAAa;AAAnB,IACE,cAAc;AADhB,IAEE,eAAe;AAFjB,IAGE,WAAW;AAHb,IAIE,SAAS;AAJX,IAKE,OAAO;AALT,IAME,OAAO;AANT,IAOE,SAAS;AAPX,IAQE,SAAS;AARX,IASE,SAAS;AATX,IAUE,SAAS;AAVX,IAWE,WAAW;AAXb,IAYE,WAAW;AAZb,IAaE,OAAO;AAbT,IAcE,MAAM;AAdR,IAeE,WAAW;AAfb,IAgBE,cAAc;AAhBhB,IAiBE,aAAa;AAjBf,IAkBE,aAAa;AAlBf,IAmBE,mBAAmB;AAnBrB,IAoBE,UAAU;AApBZ,IAqBE,OAAO;AArBT,IAsBE,OAAO;AAtBT,IAuBE,QAAQ;AAvBV,IAwBE,UAAU;AAEZ,SAAS,QAAQ,IAAI;AACjB,SAAO,MAAM,MAAiB,MAAM,MAAiB,MAAM,MAAiB,MAAM,OAAkB,MAAM,MAAkB,MAAM;AACtI;AACA,SAAS,WAAW,IAAI;AACpB,SAAO,MAAM,MAAkB,MAAM,MAAkB,MAAM,MAAiB,MAAM,OAAkB,MAAM,MAAiB,MAAM;AACvI;AACA,SAAS,YAAY,OAAO,UAAU,kBAAkB;AACpD,WAAS,UAAU,WAAS;AACxB,QAAI,MAAM,OAAO;AACb;AACJ,QAAI,MAAM,QAAQ,YAAY,CAAC,SAAS;AACpC,YAAM,QAAQ;AACd;AAAA,IACJ;AACA,cAAU,oBAAoB,CAAC,WAAW,MAAM,QAAQ;AACxD,UAAM,QAAQ;AAAA,EAClB;AACJ;AACA,SAAS,wBAAwB,OAAO,KAAK;AACzC,OAAM,YAAS;AACX,QAAI,MAAM,OAAO;AACb;AACJ,QAAI,MAAM,QAAQ,IAAoB;AAClC,YAAM,QAAQ;AACd,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,YAAI,MAAM,QAAQ,IAAI,WAAW,CAAC;AAC9B,mBAAS;AACb,cAAM,QAAQ;AAAA,MAClB;AACA,UAAI,MAAM,QAAQ,IAAoB;AAClC,cAAM,QAAQ;AACd;AAAA,MACJ;AAAA,IACJ,OACK;AACD,YAAM,QAAQ;AAAA,IAClB;AAAA,EACJ;AACJ;AACA,SAAS,uBAAuB,OAAO,WAAW;AAC9C,MAAI,gBAAgB,OAAO,QAAQ,OAAO,aAAa,SAAS,CAAC;AACjE,MAAI,aAAa,gBAAgB,IAAI,YAAY,OAAO,WAAW,aAAa;AAChF,aAAS;AACL,QAAI,MAAM,OAAO;AACb;AACJ,QAAI,MAAM,QAAQ,cAAc,MAAM,KAAK,CAAC,KAAK,IAAyB;AACtE,YAAM,QAAQ,CAAC;AACf;AAAA,IACJ;AACA,UAAM,QAAQ;AAAA,EAClB;AACJ;AACA,SAAS,SAAS,OAAO,QAAQ;AAC7B,aAAS;AACL,QAAI,MAAM,QAAQ,MAA0B,CAAC,QAAQ,MAAM,IAAI;AAC3D;AACJ,QAAI,UAAU;AACV,gBAAU,OAAO,aAAa,MAAM,IAAI;AAC5C,UAAM,QAAQ;AAAA,EAClB;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,OAAO;AAC7B,MAAI,MAAM,QAAQ,MAA2B,MAAM,QAAQ,MAA2B,MAAM,QAAQ,IAAsB;AACtH,QAAI,QAAQ,MAAM;AAClB,UAAM,QAAQ;AACd,gBAAY,OAAO,OAAO,KAAK;AAAA,EACnC,OACK;AACD,aAAS,KAAK;AAAA,EAClB;AACJ;AACA,SAAS,SAAS,OAAO,UAAU;AAC/B,SAAO,MAAM,QAAQ,MAAkB,MAAM,QAAQ;AACjD,UAAM,QAAQ;AAClB,MAAI,YAAY,MAAM,QAAQ;AAC1B,UAAM,QAAQ;AACtB;AACA,SAAS,WAAW,OAAO,QAAQ;AAC/B,aAAS;AACL,QAAI,MAAM,QAAQ,IAAiB;AAC/B,UAAI;AACA;AACJ,eAAS;AAAA,IACb,WACS,MAAM,OAAO,MAAkB,MAAM,OAAO,IAAgB;AACjE;AAAA,IACJ;AACA,UAAM,QAAQ;AAAA,EAClB;AACA,MAAI,MAAM,QAAQ,MAAiB,MAAM,QAAQ,KAAgB;AAC7D,UAAM,QAAQ;AACd,QAAI,MAAM,QAAQ,MAAoB,MAAM,QAAQ;AAChD,YAAM,QAAQ;AAClB,WAAO,MAAM,QAAQ,MAAkB,MAAM,QAAQ;AACjD,YAAM,QAAQ;AAAA,EACtB;AACJ;AACA,SAAS,IAAI,OAAO;AAChB,SAAO,EAAE,MAAM,OAAO,KAAK,MAAM,QAAQ;AACrC,UAAM,QAAQ;AACtB;AACA,SAAS,SAAS,IAAI,KAAK;AACvB,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC5B,QAAI,IAAI,WAAW,CAAC,KAAK;AACrB,aAAO;AACf,SAAO;AACX;AACA,IAAM,QAAQ;AACd,SAAS,SAASA,WAAU,OAAO,SAAS;AACxC,MAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,SAAO,MAAM,IAAI,OAAO,OAAO,IAAI;AACnC,SAAO,MAAM,IAAI,OAAO,SAAS,IAAI;AACrC,WAAS,MAAMA,UAAS,MAAM,GAAG;AAC7B,QAAI;AACA,aAAO,EAAE,IAAI;AACrB,WAAS,MAAM,MAAM,MAAM,GAAG;AAC1B,QAAI;AACA,aAAO,EAAE,IAAI;AACrB,WAAS,OAAO,WAAW,IAAI,MAAM,GAAG;AACpC,QAAI;AACA,aAAO,EAAE,IAAI;AACrB,SAAO;AACX;AACA,IAAM,WAAW;AACjB,IAAM,cAAc;AACpB,IAAM,WAAW;AAAA,EACb,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,uBAAuB;AAAA,EACvB,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,4BAA4B;AAAA,EAC5B,OAAoB,SAAS,aAAa,QAAQ;AACtD;AACA,SAAS,QAAQ,MAAM,KAAK,OAAO,SAAS;AACxC,MAAIC,WAAU,CAAC;AACf,WAAS,QAAQ;AACb,IAAAA,SAAQ,IAAI,KAAK,KAAK,eAAe,IAAI,IAAI,OAAO,UAAU,IAAI;AACtE,MAAI;AACA,IAAAA,SAAQ,QAAQ,SAAS,KAAK,SAAS,IAAI,OAAO;AACtD,SAAOA;AACX;AACA,SAAS,UAAU,GAAG;AAClB,SAAO,IAAI,kBAAkB,WAAS;AAClC,QAAI;AACJ,QAAI,EAAE,KAAK,IAAI;AACf,UAAM,QAAQ;AACd,QAAI,SAAS,MAAM,KAAK,GAAG;AACvB,aAAO,SAAS,MAAM,MAAM,KAAK;AAC7B,cAAM,QAAQ;AAClB,YAAM,YAAY,UAAU;AAAA,IAChC,WACS,QAAQ,MAAsB,EAAE,2BAA2B;AAChE,UAAI,MAAM,SAAS,OAAO,EAAE;AAC5B,UAAI,MAAM,QAAQ,IAAoB;AAClC,cAAM,QAAQ;AACd,gCAAwB,OAAO,GAAG;AAClC,cAAM,YAAY,QAAQ;AAAA,MAC9B;AAAA,IACJ,WACS,QAAQ,MAA2B,QAAQ,MAA2B,EAAE,qBAAqB;AAClG,kBAAY,OAAO,MAAM,EAAE,gBAAgB;AAC3C,YAAM,YAAY,QAAQ;AAAA,IAC9B,WACS,QAAQ,MAAoB,EAAE,gBACnC,QAAQ,MAAqB,MAAM,QAAQ,MAAqB,EAAE,eAAe;AACjF,UAAI,KAAK;AACT,YAAM,YAAY,WAAW;AAAA,IACjC,WACS,QAAQ,MAAoB,MAAM,QAAQ,OAC9C,CAAC,EAAE,oBAAoB,MAAM,KAAK,CAAC,KAAK,KAAoB;AAC7D,UAAI,KAAK;AACT,YAAM,YAAY,WAAW;AAAA,IACjC,WACS,QAAQ,MAAqB,MAAM,QAAQ,IAAkB;AAClE,YAAM,QAAQ;AACd,eAAS,QAAQ,OAAK;AAClB,YAAI,MAAM,MAAM;AAChB,YAAI,MAAM,OAAO;AACb;AACJ,cAAM,QAAQ;AACd,YAAI,OAAO,MAAoB,MAAM,QAAQ,IAAmB;AAC5D;AACA,gBAAM,QAAQ;AACd,cAAI,CAAC;AACD;AAAA,QACR,WACS,OAAO,MAAqB,MAAM,QAAQ,IAAkB;AACjE;AACA,gBAAM,QAAQ;AAAA,QAClB;AAAA,MACJ;AACA,YAAM,YAAY,YAAY;AAAA,IAClC,YACU,QAAQ,OAAkB,QAAQ,OAAkB,MAAM,QAAQ,IAAyB;AACjG,YAAM,QAAQ;AACd,kBAAY,OAAO,IAAyB,IAAI;AAChD,YAAM,YAAY,QAAQ;AAAA,IAC9B,YACU,QAAQ,OAAkB,QAAQ,OAAkB,MAAM,QAAQ,MACxE,EAAE,cAAc;AAChB,YAAM,QAAQ;AACd,kBAAY,OAAO,IAAyB,EAAE,gBAAgB;AAC9D,YAAM,YAAY,QAAQ;AAAA,IAC9B,WACS,QAAQ,MAA0B,EAAE,cAAc;AACvD,eAAS,IAAI,KAAI,KAAK;AAClB,YAAI,MAAM,QAAQ,MAA2B,IAAI,GAAG;AAChD,gBAAM,QAAQ;AACd,sBAAY,OAAO,IAAyB,EAAE,gBAAgB;AAC9D,gBAAM,YAAY,QAAQ;AAC1B;AAAA,QACJ;AACA,YAAI,CAAC,QAAQ,MAAM,IAAI;AACnB;AACJ,cAAM,QAAQ;AAAA,MAClB;AAAA,IACJ,WACS,EAAE,0BACN,QAAQ,OAAkB,QAAQ,OAAkB,MAAM,QAAQ,MACnE,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,SAAS,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG;AACtD,UAAI,YAAY,MAAM,KAAK,CAAC;AAC5B,YAAM,QAAQ,CAAC;AACf,6BAAuB,OAAO,SAAS;AACvC,YAAM,YAAY,QAAQ;AAAA,IAC9B,WACS,QAAQ,IAAoB;AACjC,YAAM,YAAY,MAAM;AAAA,IAC5B,WACS,QAAQ,IAAoB;AACjC,YAAM,YAAY,MAAM;AAAA,IAC5B,WACS,QAAQ,KAAqB;AAClC,YAAM,YAAY,MAAM;AAAA,IAC5B,WACS,QAAQ,KAAqB;AAClC,YAAM,YAAY,MAAM;AAAA,IAC5B,WACS,QAAQ,IAAsB;AACnC,YAAM,YAAY,QAAQ;AAAA,IAC9B,WACS,QAAQ,IAAsB;AACnC,YAAM,YAAY,QAAQ;AAAA,IAC9B,WACS,QAAQ,IAAkB;AAC/B,YAAM,YAAY,IAAI;AAAA,IAC1B,WACS,EAAE,uBAAuB,QAAQ,MAAkB,MAAM,QAAQ,IAAe;AACrF,YAAM,QAAQ;AACd,eAAS,KAAK;AACd,YAAM,YAAY,IAAI;AAAA,IAC1B,YACU,QAAQ,MAAiB,QAAQ,QAAmB,MAAM,QAAQ,MAA2B,MAAM,QAAQ,KAA0B;AAC3I,YAAM,aAAa,MAAM;AACzB,YAAM,QAAQ;AACd,UAAI,EAAE,kBAAkB;AACpB,oBAAY,OAAO,YAAY,EAAE,gBAAgB;AACjD,cAAM,YAAY,KAAK;AAAA,MAC3B,OACK;AACD,iBAAS,OAAO,UAAU;AAC1B,cAAM,YAAY,IAAI;AAAA,MAC1B;AAAA,IACJ,WACS,QAAQ,OAAmB,MAAM,QAAQ,OAAkB,MAAM,QAAQ,QAC7E,QAAQ,OAAkB,QAAQ,OAAkB,MAAM,QAAQ,IAAyB;AAC5F,UAAI,SAAS,MAAM,QAAQ;AAC3B,YAAM,QAAQ;AACd,aAAO,WAAW,MAAM,IAAI;AACxB,cAAM,QAAQ;AAClB,UAAI,UAAU,MAAM,QAAQ;AACxB,cAAM,QAAQ;AAClB,YAAM,YAAY,MAAM;AAAA,IAC5B,WACS,QAAQ,MAAmB,MAAM,QAAQ,MAAkB,MAAM,QAAQ,IAAgB;AAC9F,iBAAW,OAAO,IAAI;AACtB,YAAM,YAAY,MAAM;AAAA,IAC5B,WACS,QAAQ,IAAiB;AAC9B,YAAM,YAAY,GAAG;AAAA,IACzB,WACS,QAAQ,MAAkB,QAAQ,IAAgB;AACvD,iBAAW,OAAO,KAAK;AACvB,YAAM,YAAY,MAAM;AAAA,IAC5B,WACS,SAAS,MAAM,EAAE,aAAa,GAAG;AACtC,aAAO,SAAS,MAAM,MAAM,EAAE,aAAa;AACvC,cAAM,QAAQ;AAClB,YAAM,YAAY,QAAQ;AAAA,IAC9B,WACS,SAAS,MAAM,EAAE,UAAU,GAAG;AACnC,UAAI,MAAM,QAAQ;AACd,cAAM,QAAQ;AAClB,uBAAiB,KAAK;AACtB,YAAM,YAAY,UAAU;AAAA,IAChC,WACS,SAAS,MAAM,EAAE,gBAAgB,GAAG;AACzC,kBAAY,OAAO,MAAM,KAAK;AAC9B,YAAM,YAAY,gBAAgB;AAAA,IACtC,WACS,QAAQ,MAAqB,QAAQ,IAAmB;AAC7D,YAAM,YAAY,WAAW;AAAA,IACjC,WACS,QAAQ,IAAI,GAAG;AACpB,UAAI,OAAO,SAAS,OAAO,OAAO,aAAa,IAAI,CAAC;AACpD,YAAM,YAAY,MAAM,QAAQ,MAAmB,MAAM,KAAK,CAAC,KAAK,SAAS,CAAC,KAAK,KAC7E,cAAc,KAAK,EAAE,MAAM,KAAK,YAAY,CAAC,OAAO,QAAQ,OAAO,SAAS,KAAK,UAAU;AAAA,IACrG;AAAA,EACJ,CAAC;AACL;AACA,IAAM,SAAsB,UAAU,QAAQ;AAG9C,IAAM,WAAwB,SAAS,YAAY;AAAA,EACjD,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,IACT,CAAC,WAAW,IAAG,GAAE,GAAE,GAAE,IAAG,EAAE;AAAA,EAC5B;AAAA,EACA,cAAc,CAAC,GAAE,GAAE,CAAC;AAAA,EACpB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY,CAAC,GAAG,MAAM;AAAA,EACtB,UAAU,EAAC,UAAS,CAAC,GAAE,EAAE,EAAC;AAAA,EAC1B,WAAW;AACb,CAAC;AAED,SAAS,YAAY,MAAM;AACvB,MAAI,SAAS,KAAK,OAAO,EAAE,OAAO,KAAK,MAAM,EAAE;AAC/C,SAAO,UAAU,KAAK,OAAO,IAAI;AAC7B,WAAO,OAAO,OAAO,MAAM,EAAE;AACjC,SAAO,OAAO;AAClB;AACA,SAAS,OAAO,KAAK,MAAM;AACvB,MAAI,OAAO,IAAI,YAAY,KAAK,MAAM,KAAK,EAAE;AAC7C,MAAI,SAAS,kBAAkB,KAAK,IAAI;AACxC,SAAO,SAAS,OAAO,CAAC,IAAI;AAChC;AACA,SAAS,QAAQ,MAAM;AACnB,SAAO,SAAS,KAAK,QAAQ,gBAAgB,KAAK,QAAQ;AAC9D;AACA,SAAS,QAAQ,KAAK,IAAI;AACtB,MAAI,GAAG,QAAQ,uBAAuB;AAClC,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,YAAY,IAAI,KAAK,GAAG;AACrC,UAAI,QAAQ,EAAE;AACV,aAAK,KAAK,OAAO,KAAK,EAAE,CAAC;AACjC,WAAO;AAAA,EACX;AACA,SAAO,CAAC,OAAO,KAAK,EAAE,CAAC;AAC3B;AACA,SAAS,WAAW,KAAK,MAAM;AAC3B,WAAS,OAAO,CAAC,OAAK;AAClB,QAAI,CAAC,QAAQ,KAAK,QAAQ;AACtB,aAAO;AACX,QAAI,OAAO,YAAY,IAAI;AAC3B,QAAI,CAAC,QAAQ,IAAI;AACb,aAAO;AACX,SAAK,QAAQ,OAAO,KAAK,IAAI,CAAC;AAC9B,WAAO,YAAY,IAAI;AAAA,EAC3B;AACJ;AACA,SAAS,cAAc,OAAO,UAAU;AACpC,MAAI,MAAM,WAAW,KAAK,EAAE,aAAa,UAAU,EAAE;AACrD,MAAI,UAAU,WAAW,MAAM,KAAK,GAAG;AACvC,MAAI,IAAI,QAAQ,gBAAgB,IAAI,QAAQ,sBAAsB,IAAI,QAAQ,WAAW;AACrF,WAAO;AAAA,MAAE,MAAM,IAAI;AAAA,MACf,QAAQ,IAAI,QAAQ,qBAAqB,MAAM,IAAI,YAAY,IAAI,MAAM,IAAI,OAAO,CAAC,IAAI;AAAA,MACzF,SAAS,WAAW,MAAM,KAAK,YAAY,GAAG,CAAC;AAAA,MAC/C;AAAA,IAAQ;AAAA,EAChB;AACA,MAAI,IAAI,QAAQ,KAAK;AACjB,WAAO,EAAE,MAAM,UAAU,QAAQ,MAAM,SAAS,WAAW,MAAM,KAAK,GAAG,GAAG,QAAQ;AAAA,EACxF,OACK;AACD,WAAO,EAAE,MAAM,UAAU,QAAQ,MAAM,SAAS,CAAC,GAAG,OAAO,MAAM,QAAQ;AAAA,EAC7E;AACJ;AACA,IAAM,UAAuB,IAAI,IAAiB,sFAAsF,MAAM,GAAG,CAAC;AAClJ,SAAS,WAAW,KAAK,IAAI;AACzB,MAAI;AACJ,WAAS,SAAS,IAAI,CAAC,WAAW,SAAS,OAAO,QAAQ;AACtD,QAAI,CAAC;AACD,aAAO;AACX,QAAI,OAAO,QAAQ;AACf,kBAAY;AAAA,EACpB;AACA,MAAI,UAAU;AACd,WAAS,OAAO,UAAU,YAAY,UAAU,OAAO,SAAS,MAAM,MAAM,OAAO,KAAK,aAAa;AACjG,QAAI,KAAK,KAAK,QAAQ,YAAY,IAAI,YAAY,KAAK,MAAM,KAAK,EAAE,EAAE,YAAY,IAAI;AACtF,QAAI,QAAQ;AACZ,QAAI,CAAC,SAAS;AACV,gBAAU,MAAM;AAAA,IACpB,WACS,MAAM,QAAQ,UAAU,QAAQ,KAAK,WAAW,GAAG;AACxD,cAAQ,OAAO,KAAK,KAAK,WAAW;AAAA,IACxC,WACS,MAAM,QAAQ,IAAI,EAAE,GAAG;AAC5B;AAAA,IACJ,WACS,UAAU,QAAQ,IAAI,GAAG;AAC9B,cAAQ,OAAO,KAAK,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO;AACP,UAAI,CAAC;AACD,kBAAU,uBAAO,OAAO,IAAI;AAChC,cAAQ,KAAK,IAAI,QAAQ,KAAK,MAAM;AAAA,IACxC;AACA,aAAS,cAAc,KAAK,KAAK,IAAI,IAAI,OAAO;AAAA,EACpD;AACA,SAAO;AACX;AACA,SAAS,sBAAsB,OAAO,aAAa;AAC/C,MAAI,CAAC;AACD,WAAO;AACX,SAAO,YAAY,IAAI,OAAM,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,QAAQ,EAAE,QAAQ,QAAQ,EAAE,QAAQ,OAAO,OAAO,OAAU,CAAC,CAAE;AAC3J;AACA,IAAM,OAAO;AAAb,IAAsB,aAAa;AACnC,SAAS,UAAU,WAAW;AAC1B,SAAO,UAAU,QAAQ,OAAO,UAAU,KAAK,SAAS;AAC5D;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EAClB,YAAY,SAAS,mBAAmB;AACpC,SAAK,UAAU;AACf,SAAK,oBAAoB;AACzB,SAAK,OAAO,CAAC;AACb,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,MAAM,MAAM;AACR,QAAI,WAAW,KAAK,aAAa,KAAK,WAAW,uBAAO,OAAO,IAAI;AACnE,QAAI,QAAQ,SAAS,IAAI;AACzB,QAAI;AACA,aAAO;AACX,QAAI,QAAQ,CAAC,KAAK,KAAK,KAAK,OAAK,EAAE,SAAS,IAAI;AAC5C,WAAK,KAAK,KAAK,eAAe,MAAM,QAAQ,KAAK,SAAS,KAAK,iBAAiB,CAAC;AACrF,WAAQ,SAAS,IAAI,IAAI,IAAI,iBAAgB,KAAK,SAAS,KAAK,iBAAiB;AAAA,EACrF;AAAA,EACA,WAAW,MAAM;AACb,WAAO,KAAK,WAAW,KAAK,SAAS,IAAI,IAAI;AAAA,EACjD;AAAA,EACA,cAAc,QAAQ;AAClB,QAAI,QAAQ,KAAK,KAAK,UAAU,OAAK,EAAE,SAAS,OAAO,KAAK;AAC5D,QAAI,QAAQ;AACR,WAAK,KAAK,KAAK,IAAI;AAAA;AAEnB,WAAK,KAAK,KAAK,MAAM;AAAA,EAC7B;AAAA,EACA,eAAe,aAAa;AACxB,aAAS,UAAU;AACf,WAAK,cAAc,OAAO,UAAU,WAAW,eAAe,QAAQ,YAAY,KAAK,SAAS,KAAK,iBAAiB,IAAI,MAAM;AAAA,EACxI;AAAA,EACA,aAAa,WAAW;AACpB,QAAI,MAAM,QAAQ,SAAS,GAAG;AAC1B,WAAK,eAAe,SAAS;AAAA,IACjC,WACS,UAAU,SAAS,GAAG;AAC3B,WAAK,aAAa,UAAU,QAAQ;AAAA,IACxC,OACK;AACD,WAAK,mBAAmB,SAAS;AAAA,IACrC;AAAA,EACJ;AAAA,EACA,mBAAmB,WAAW;AAC1B,aAAS,QAAQ,OAAO,KAAK,SAAS,GAAG;AACrC,UAAI,WAAW,UAAU,IAAI,GAAG,OAAO;AACvC,UAAI,QAAQ,KAAK,QAAQ,UAAU,OAAK,KAAK,MAAM,OAAO,CAAC,EAAE,MAAM,IAAI;AACvE,UAAI,QAAQ;AACZ,UAAI,UAAU,QAAQ,GAAG;AACrB,eAAO,SAAS;AAChB,mBAAW,SAAS;AAAA,MACxB;AACA,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAI,QAAQ,KAAK,MAAM,SAAS;AAC5B,gBAAM,cAAc,IAAI;AAC5B,gBAAQ,MAAM,MAAM,MAAM,CAAC,EAAE,QAAQ,SAAS,GAAG,CAAC;AAAA,MACtD;AACA,YAAM,aAAa,QAAQ;AAAA,IAC/B;AAAA,EACJ;AACJ;AACA,SAAS,eAAe,OAAO,MAAM,SAAS,mBAAmB;AAC7D,MAAK,IAAI,OAAO,sBAAsB,oBAAoB,MAAM,EAAE,EAAG,KAAK,KAAK;AAC3E,WAAO,EAAE,OAAO,KAAK;AACzB,SAAO,EAAE,OAAO,MAAM,OAAO,UAAU,QAAQ,QAAQ;AAC3D;AAKA,SAAS,mBAAmB,QAAQ,QAAQ,SAAS,kBAAkB,mBAAmBA,UAAS;AAC/F,MAAI;AACJ,MAAI,YAAY,KAAKA,aAAY,QAAQA,aAAY,SAAS,SAASA,SAAQ,KAAK,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,MAAM;AACrJ,MAAI,MAAM,IAAI,gBAAgB,SAAS,CAAC,EAAEA,aAAY,QAAQA,aAAY,SAAS,SAASA,SAAQ,KAAK,2BAA2B;AACpI,MAAI,gBAAgB,oBAAoB,IAAI,MAAM,iBAAiB,IAAI;AACvE,MAAI,aAAa,MAAM;AACvB,MAAI;AACA,KAAC,iBAAiB,KAAK,eAAe,MAAM;AAChD,MAAI;AACA,QAAI,eAAe,OAAO;AAC9B,MAAI;AACA,QAAI,eAAe,cAAc,IAAI;AACzC,MAAI;AACA,QAAI,gBAAgB,iBAAiB,KAAK,MAAM,gBAAgB,EAAE,IAAI;AAC1E,SAAO,CAAC,YAAY;AAChB,QAAI,EAAE,SAAS,MAAM,QAAQ,OAAO,QAAQ,IAAI,cAAc,QAAQ,OAAO,QAAQ,GAAG;AACxF,QAAI,SAAS,CAAC,QAAQ;AAClB,aAAO;AACX,QAAI,WAAW,QAAQ,UAAU;AAC7B,gBAAU,QAAQ,QAAQ,CAAC,CAAC,KAAK;AACrC,QAAI,QAAQ;AACZ,aAAS,QAAQ,SAAS;AACtB,aAAO,CAAC,MAAM,YAAY,CAAC,MAAM,SAAS,IAAI,GAAG;AAC7C,YAAI,SAAS,OAAO;AAChB,kBAAQ;AAAA,iBACH,SAAS,iBAAiB;AAC/B,kBAAQ,MAAM,MAAM,gBAAgB;AAAA;AAEpC,iBAAO;AAAA,MACf;AACA,UAAI,OAAO,MAAM,WAAW,IAAI;AAChC,UAAI,CAAC;AACD,eAAO;AACX,cAAQ;AAAA,IACZ;AACA,QAAI,aAAa,UAAU,QAAQ,MAAM,SAAS,QAAQ,KAAK,QAAQ,MAAM,CAAC,KAAK;AACnF,QAAI,UAAU,MAAM;AACpB,QAAI,SAAS,OAAO;AAChB,gBAAU,QAAQ,OAAO,OAAO,KAAK,OAAO,EAAE,IAAI,WAAS,EAAE,OAAO,MAAM,MAAM,WAAW,EAAE,CAAC;AAClG,WAAO;AAAA,MACH;AAAA,MACA,IAAI,aAAa,QAAQ,MAAM,IAAI;AAAA,MACnC,SAAS,sBAAsB,QAAQ,OAAO;AAAA,MAC9C,UAAU,SAAS,aAAa;AAAA,IACpC;AAAA,EACJ;AACJ;AACA,SAAS,iBAAiBD,WAAU,WAAW;AAC3C,MAAI,cAAc,OAAO,KAAKA,SAAQ,EAAE,IAAI,cAAY;AAAA,IACpD,OAAO,YAAY,QAAQ,YAAY,IAAI;AAAA,IAC3C,MAAMA,UAAS,OAAO,KAAK,OAAO,SAASA,UAAS,OAAO,KAAK,UAAU,YAAY;AAAA,IACtF,OAAO;AAAA,EACX,EAAE;AACF,SAAO,QAAQ,CAAC,oBAAoB,cAAc,UAAU,eAAe,gBAAgB,GAAG,GAAG,iBAAiB,WAAW,CAAC;AAClI;AAEA,IAAI,SAAsB,SAAS,UAAU;AAAA,EACzC,OAAO;AAAA,IACU,eAAe,IAAI;AAAA,MAC5B,WAAwB,gBAAgB;AAAA,IAC5C,CAAC;AAAA,IACY,aAAa,IAAI;AAAA,MAC1B,UAAU,MAAM,OAAO;AAAE,eAAO,EAAE,MAAM,KAAK,IAAI,KAAK,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,IAAI,EAAE,EAAE,GAAG,IAAI,KAAK,GAAG;AAAA,MAAG;AAAA,MAClH,aAAa,MAAM;AAAE,eAAO,EAAE,MAAM,KAAK,OAAO,GAAG,IAAI,KAAK,KAAK,EAAE;AAAA,MAAG;AAAA,IAC1E,CAAC;AAAA,IACY,UAAU;AAAA,MACnB,SAAS,KAAK;AAAA,MACd,MAAM,KAAK;AAAA,MACX,SAAsB,KAAK,SAAS,KAAK,IAAI;AAAA,MAC7C,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,MACZ,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,MACb,YAAY,KAAK;AAAA,MACjB,kBAA+B,KAAK,QAAQ,KAAK,MAAM;AAAA,MACvD,YAAyB,KAAK,QAAQ,KAAK,IAAI;AAAA,MAC/C,aAAa,KAAK;AAAA,MAClB,cAAc,KAAK;AAAA,MACnB,UAAU,KAAK;AAAA,MACf,oBAAoB,KAAK;AAAA,MACzB,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AACJ,CAAC;AAID,IAAM,aAAN,MAAM,YAAW;AAAA,EACb,YAIAC,UAIA,UAIA,MAAM;AACF,SAAK,UAAUA;AACf,SAAK,WAAW;AAChB,SAAK,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,YAAY;AAAE,WAAO,KAAK,SAAS;AAAA,EAAW;AAAA;AAAA;AAAA;AAAA,EAIlD,OAAO,OAAO,MAAM;AAChB,QAAI,IAAI,QAAQ,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,OAAO;AAC7D,QAAI,WAAW,WAAW,OAAO;AAAA,MAC7B,MAAM;AAAA,MACN,QAAQ,OAAO,UAAU;AAAA,QACrB,YAAY,CAAC,EAAE,MAAM,QAAQ,IAAI,UAAU,CAAC,EAAE,CAAC;AAAA,MACnD,CAAC;AAAA,MACD,cAAc;AAAA,QACV,eAAe,EAAE,MAAM,MAAM,OAAO,EAAE,MAAM,MAAM,OAAO,KAAK,EAAE;AAAA,QAChE,eAAe,EAAE,UAAU,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,EAAE;AAAA,MAC9D;AAAA,IACJ,CAAC;AACD,WAAO,IAAI,YAAW,GAAG,UAAU,IAAI;AAAA,EAC3C;AACJ;AAKA,SAAS,wBAAwBA,UAAS,YAAY,OAAO;AACzD,SAAO,iBAAiBA,SAAQ,QAAQ,OAAO,SAAS;AAC5D;AAIA,SAAS,kBAAkBA,UAAS,YAAY,OAAO;AACnD,SAAOA,SAAQ,SAAS,KAAK,GAAG;AAAA,IAC5B,cAAc,wBAAwBA,UAAS,SAAS;AAAA,EAC5D,CAAC;AACL;AAKA,SAAS,uBAAuB,QAAQ;AACpC,SAAO,OAAO,SAAS,mBAAmB,OAAO,QAAQ,OAAO,QAAQ,OAAO,SAAS,OAAO,cAAc,OAAO,eAAe,OAAO,WAAW,WAAW,IAC1J,MAAM;AAChB;AAIA,SAAS,iBAAiB,QAAQ;AAC9B,SAAO,OAAO,UAAU,OAAO,WAAW,aAAa,SAAS,KAAK,GAAG;AAAA,IACpE,cAAc,uBAAuB,MAAM;AAAA,EAC/C,CAAC,IAAI,CAAC;AACV;AAMA,SAAS,IAAI,SAAS,CAAC,GAAG;AACtB,MAAI,OAAO,OAAO,WAAW;AAC7B,SAAO,IAAI,gBAAgB,KAAK,UAAU,CAAC,iBAAiB,MAAM,GAAG,kBAAkB,MAAM,CAAC,CAAC,OAAO,iBAAiB,CAAC,CAAC;AAC7H;AAIA,IAAM,cAA2B,WAAW,OAAO,CAAC,CAAC;AAIrD,IAAM,aAA0B,WAAW,OAAO;AAAA,EAC9C,cAAc;AAAA,EACd,2BAA2B;AAAA,EAC3B,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,UAAU,cAAc;AAAA,EACxB,OAAO,WAAW;AACtB,CAAC;AACD,IAAM,gBAAgB;AACtB,IAAM,aAAa,WAAW;AAC9B,IAAM,eAAe;AAIrB,IAAM,QAAqB,WAAW,OAAO;AAAA,EACzC,eAAe;AAAA,EACf,cAAc;AAAA,EACd,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,UAAU,cAAc,kBAAkB;AAAA,EAC1C,OAAO;AAAA,EACP,SAAS;AACb,CAAC;AAKD,IAAM,WAAwB,WAAW,OAAO;AAAA,EAC5C,eAAe;AAAA,EACf,cAAc;AAAA,EACd,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,UAAU,cAAc,2EAA2E;AAAA,EACnG,OAAO;AAAA,EACP,SAAS;AACb,CAAC;AAKD,IAAM,QAAqB,WAAW,OAAO;AAAA,EACzC,UAAU,cAAc;AAAA,EACxB,OAAO,WAAW;AAAA,EAClB,SAAS;AAAA,EACT,eAAe;AAAA,EACf,YAAY;AAChB,CAAC;AAID,IAAM,SAAsB,WAAW,OAAO;AAAA,EAC1C,UAAU,cAAc;AAAA,EACxB,OAAO,WAAW;AAAA,EAClB,SAAS;AAAA,EACT,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,YAAY;AAChB,CAAC;AAID,IAAM,YAAyB,WAAW,OAAO;AAAA,EAC7C,UAAU;AAAA,EACV,OAAO,WAAW;AAAA,EAClB,eAAe;AACnB,CAAC;AAID,IAAM,QAAqB,WAAW,OAAO;AAAA,EACzC,UAAU,cAAc;AAAA,EACxB,SAAS;AAAA,EACT,OAAO,WAAW;AAAA,EAClB,eAAe;AAAA,EACf,qBAAqB;AAAA,EACrB,cAAc;AAAA,EACd,uBAAuB;AAC3B,CAAC;", "names": ["keywords", "dialect"]}