import {
  r
} from "./chunk-7VG4CGLX.js";
import {
  o
} from "./chunk-EPJSBV4J.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/chunks/nl_NL.js
function r2(e, t) {
  for (var r3 = 0; r3 < t.length; r3++) {
    const o3 = t[r3];
    if ("string" != typeof o3 && !Array.isArray(o3)) {
      for (const t2 in o3) if ("default" !== t2 && !(t2 in e)) {
        const r4 = Object.getOwnPropertyDescriptor(o3, t2);
        r4 && Object.defineProperty(e, t2, r4.get ? r4 : { enumerable: true, get: () => o3[t2] });
      }
    }
  }
  return Object.freeze(Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }));
}
var o2;
var a;
var n = {};
var i = { get exports() {
  return n;
}, set exports(e) {
  n = e;
} };
o2 = i, void 0 !== (a = function(e, t) {
  Object.defineProperty(t, "__esModule", { value: true }), t.default = { _decimalSeparator: ",", _thousandSeparator: ".", _percentPrefix: null, _percentSuffix: "%", _date_millisecond: "mm:ss SSS", _date_second: "HH:mm:ss", _date_minute: "HH:mm", _date_hour: "HH:mm", _date_day: "d MMM", _date_week: "ww", _date_month: "MMM", _date_year: "yyyy", _duration_millisecond: "SSS", _duration_second: "ss", _duration_minute: "mm", _duration_hour: "hh", _duration_day: "dd", _duration_week: "ww", _duration_month: "MM", _duration_year: "yyyy", _era_ad: "AD", _era_bc: "v.C.", A: "A", P: "P", AM: "AM", PM: "PM", "A.M.": "a.m.", "P.M.": "p.m.", January: "januari", February: "februari", March: "maart", April: "april", May: "mei", June: "juni", July: "juli", August: "augustus", September: "september", October: "oktober", November: "november", December: "december", Jan: "jan", Feb: "feb", Mar: "mrt", Apr: "apr", "May(short)": "mei", Jun: "jun", Jul: "jul", Aug: "aug", Sep: "sep", Oct: "okt", Nov: "nov", Dec: "dec", Sunday: "zondag", Monday: "maandag", Tuesday: "dinsdag", Wednesday: "woensdag", Thursday: "donderdag", Friday: "vrijdag", Saturday: "zaterdag", Sun: "Zo", Mon: "Ma", Tue: "Di", Wed: "Wo", Thu: "Do", Fri: "Vr", Sat: "Za", _dateOrd: function(e2) {
    var t2 = "de";
    return (1 == e2 || 8 == e2 || e2 > 19) && (t2 = "ste"), t2;
  }, "Zoom Out": "Uitzoomen", Play: "Afspelen", Stop: "Stoppen", Legend: "Legenda", "Click, tap or press ENTER to toggle": "Klik, tik of druk op Enter om aan of uit te zetten", Loading: "Laden", Home: "Home", Chart: "Grafiek", "Serial chart": "Periodieke grafiek", "X/Y chart": "X-Y grafiek", "Pie chart": "Taartdiagram", "Gauge chart": "Meterdiagram", "Radar chart": "Radardiagram", "Sankey diagram": "Sankey-diagram", "Chord diagram": "Chord-diagram", "Flow diagram": "Flow-diagram", "TreeMap chart": "Treemap-grafiek", Series: "Reeks", "Candlestick Series": "Candlestick-reeks", "Column Series": "Kolomreeks", "Line Series": "Lijnreeks", "Pie Slice Series": "Taartpuntreeks", "X/Y Series": "XY reeks", Map: "Kaart", "Press ENTER to zoom in": "Druk op Enter om in te zoomen", "Press ENTER to zoom out": "Druk op Enter om uit te zoomen", "Use arrow keys to zoom in and out": "Zoom in of uit met de pijltjestoetsen", "Use plus and minus keys on your keyboard to zoom in and out": "Zoom in of uit met de plus- en minustoetsen", Export: "Exporteren", Image: "Afbeelding", Data: "Data", Print: "Printen", "Click, tap or press ENTER to open": "Klik, tik of druk op Enter om te openen", "Click, tap or press ENTER to print.": "Klik, tik of druk op Enter om te printen", "Click, tap or press ENTER to export as %1.": "Klik, tik of druk op Enter om te exporteren als %1", 'To save the image, right-click this link and choose "Save picture as..."': 'Klik met de rechtermuisknop op deze link en kies "Afbeelding opslaan als..." om de afbeelding op te slaan', 'To save the image, right-click thumbnail on the left and choose "Save picture as..."': 'Klik met de rechtermuisknop op de miniatuur links en kies "Afbeelding opslaan als..." om de afbeelding op te slaan', "(Press ESC to close this message)": "(Druk op ESC om dit bericht te sluiten)", "Image Export Complete": "Afbeelding exporteren gereed", "Export operation took longer than expected. Something might have gone wrong.": "Exportproces duurt langer dan verwacht. Er is misschien iets fout gegaan.", "Saved from": "Opgeslagen via:", PNG: "PNG", JPG: "JPG", GIF: "GIF", SVG: "SVG", PDF: "PDF", JSON: "JSON", CSV: "CSV", XLSX: "XLSX", "Use TAB to select grip buttons or left and right arrows to change selection": "Gebruik Tab om de hendels te selecteren of linker- en rechterpijltje om de selectie te veranderen", "Use left and right arrows to move selection": "Gebruik linker- en rechterpijltje om de selectie te verplaatsen", "Use left and right arrows to move left selection": "Gebruik linker- en rechterpijltje om de linkerselectie te verplaatsen", "Use left and right arrows to move right selection": "Gebruik linker- en rechterpijltje om de rechterselectie te verplaatsen", "Use TAB select grip buttons or up and down arrows to change selection": "Gebruik Tab om de hendels te selecteren of pijltje omhoog en omlaag om de selectie te veranderen", "Use up and down arrows to move selection": "Gebruik pijltje omhoog en omlaag om de selectie te verplaatsen", "Use up and down arrows to move lower selection": "Gebruik pijltje omhoog en omlaag om de onderste selectie te verplaatsen", "Use up and down arrows to move upper selection": "Gebruik pijltje omhoog en omlaag om de bovenste selectie te verplaatsen", "From %1 to %2": "Van %1 tot %2", "From %1": "Van %1", "To %1": "Tot %2", "No parser available for file: %1": "Geen data-parser beschikbaar voor dit bestand: %1", "Error parsing file: %1": "Fout tijdens parsen van bestand: %1", "Unable to load file: %1": "Kan bestand niet laden: %1", "Invalid date": "Ongeldige datum" };
}(r, n)) && (o2.exports = a);
var s = r2({ __proto__: null, default: o(n) }, [n]);
export {
  s as n
};
//# sourceMappingURL=nl_NL-2NGBMYV7.js.map
