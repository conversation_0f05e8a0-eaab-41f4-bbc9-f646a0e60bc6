{"version": 3, "sources": ["../../@arcgis/core/chunks/pl_PL.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{g as e}from\"./_commonjsHelpers.js\";import{c as r}from\"./_commonjs-dynamic-modules.js\";function a(e,r){for(var a=0;a<r.length;a++){const o=r[a];if(\"string\"!=typeof o&&!Array.isArray(o))for(const r in o)if(\"default\"!==r&&!(r in e)){const a=Object.getOwnPropertyDescriptor(o,r);a&&Object.defineProperty(e,r,a.get?a:{enumerable:!0,get:()=>o[r]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}))}var o,t,_={},i={get exports(){return _},set exports(e){_=e}};o=i,void 0!==(t=function(e,r){Object.defineProperty(r,\"__esModule\",{value:!0}),r.default={_decimalSeparator:\",\",_thousandSeparator:\" \",_percentPrefix:null,_percentSuffix:\"%\",_big_number_suffix_3:\"k\",_big_number_suffix_6:\"M\",_big_number_suffix_9:\"G\",_big_number_suffix_12:\"T\",_big_number_suffix_15:\"P\",_big_number_suffix_18:\"E\",_big_number_suffix_21:\"Z\",_big_number_suffix_24:\"Y\",_small_number_suffix_3:\"m\",_small_number_suffix_6:\"μ\",_small_number_suffix_9:\"n\",_small_number_suffix_12:\"p\",_small_number_suffix_15:\"f\",_small_number_suffix_18:\"a\",_small_number_suffix_21:\"z\",_small_number_suffix_24:\"y\",_byte_suffix_B:\"B\",_byte_suffix_KB:\"KB\",_byte_suffix_MB:\"MB\",_byte_suffix_GB:\"GB\",_byte_suffix_TB:\"TB\",_byte_suffix_PB:\"PB\",_date_millisecond:\"mm:ss SSS\",_date_second:\"HH:mm:ss\",_date_minute:\"HH:mm\",_date_hour:\"HH:mm\",_date_day:\"MMM dd\",_date_week:\"ww\",_date_month:\"MMM\",_date_year:\"yyyy\",_duration_millisecond:\"SSS\",_duration_millisecond_second:\"ss.SSS\",_duration_millisecond_minute:\"mm:ss SSS\",_duration_millisecond_hour:\"hh:mm:ss SSS\",_duration_millisecond_day:\"d'd' mm:ss SSS\",_duration_millisecond_week:\"d'd' mm:ss SSS\",_duration_millisecond_month:\"M'm' dd'd' mm:ss SSS\",_duration_millisecond_year:\"y'y' MM'm' dd'd' mm:ss SSS\",_duration_second:\"ss\",_duration_second_minute:\"mm:ss\",_duration_second_hour:\"hh:mm:ss\",_duration_second_day:\"d'd' hh:mm:ss\",_duration_second_week:\"d'd' hh:mm:ss\",_duration_second_month:\"M'm' dd'd' hh:mm:ss\",_duration_second_year:\"y'y' MM'm' dd'd' hh:mm:ss\",_duration_minute:\"mm\",_duration_minute_hour:\"hh:mm\",_duration_minute_day:\"d'd' hh:mm\",_duration_minute_week:\"d'd' hh:mm\",_duration_minute_month:\"M'm' dd'd' hh:mm\",_duration_minute_year:\"y'y' MM'm' dd'd' hh:mm\",_duration_hour:\"hh'h'\",_duration_hour_day:\"d'd' hh'h'\",_duration_hour_week:\"d'd' hh'h'\",_duration_hour_month:\"M'm' dd'd' hh'h'\",_duration_hour_year:\"y'y' MM'm' dd'd' hh'h'\",_duration_day:\"d'd'\",_duration_day_week:\"d'd'\",_duration_day_month:\"M'm' dd'd'\",_duration_day_year:\"y'y' MM'm' dd'd'\",_duration_week:\"w'w'\",_duration_week_month:\"w'w'\",_duration_week_year:\"w'w'\",_duration_month:\"M'm'\",_duration_month_year:\"y'y' MM'm'\",_duration_year:\"y'y'\",_era_ad:\"n.e.\",_era_bc:\"p.n.e.\",A:\"a\",P:\"p\",AM:\"AM\",PM:\"PM\",\"A.M.\":\"AM\",\"P.M.\":\"PM\",January:\"stycznia\",February:\"lutego\",March:\"marca\",April:\"kwietnia\",May:\"maja\",June:\"czerwca\",July:\"lipca\",August:\"sierpnia\",September:\"września\",October:\"października\",November:\"listopada\",December:\"grudnia\",Jan:\"sty\",Feb:\"lut\",Mar:\"mar\",Apr:\"kwi\",\"May(short)\":\"maj\",Jun:\"cze\",Jul:\"lip\",Aug:\"sie\",Sep:\"wrz\",Oct:\"paź\",Nov:\"lis\",Dec:\"gru\",Sunday:\"niedziela\",Monday:\"poniedziałek\",Tuesday:\"wtorek\",Wednesday:\"środa\",Thursday:\"czwartek\",Friday:\"piątek\",Saturday:\"sobota\",Sun:\"niedz.\",Mon:\"pon.\",Tue:\"wt.\",Wed:\"śr.\",Thu:\"czw.\",Fri:\"pt.\",Sat:\"sob.\",_dateOrd:function(e){var r=\"th\";if(e<11||e>13)switch(e%10){case 1:r=\"st\";break;case 2:r=\"nd\";break;case 3:r=\"rd\"}return r},\"Zoom Out\":\"Zmiana skali\",Play:\"Odtwarzanie\",Stop:\"Zatrzymaj\",Legend:\"Legenda\",\"Click, tap or press ENTER to toggle\":\"\",Loading:\"Wczytywanie\",Home:\"Strona główna\",Chart:\"\",\"Serial chart\":\"\",\"X/Y chart\":\"\",\"Pie chart\":\"\",\"Gauge chart\":\"\",\"Radar chart\":\"\",\"Sankey diagram\":\"\",\"Flow diagram\":\"\",\"Chord diagram\":\"\",\"TreeMap chart\":\"\",\"Sliced chart\":\"\",Series:\"\",\"Candlestick Series\":\"\",\"OHLC Series\":\"\",\"Column Series\":\"\",\"Line Series\":\"\",\"Pie Slice Series\":\"\",\"Funnel Series\":\"\",\"Pyramid Series\":\"\",\"X/Y Series\":\"\",Map:\"\",\"Press ENTER to zoom in\":\"\",\"Press ENTER to zoom out\":\"\",\"Use arrow keys to zoom in and out\":\"\",\"Use plus and minus keys on your keyboard to zoom in and out\":\"\",Export:\"Drukuj\",Image:\"Obraz\",Data:\"Dane\",Print:\"Drukuj\",\"Click, tap or press ENTER to open\":\"\",\"Click, tap or press ENTER to print.\":\"\",\"Click, tap or press ENTER to export as %1.\":\"\",'To save the image, right-click this link and choose \"Save picture as...\"':\"\",'To save the image, right-click thumbnail on the left and choose \"Save picture as...\"':\"\",\"(Press ESC to close this message)\":\"\",\"Image Export Complete\":\"\",\"Export operation took longer than expected. Something might have gone wrong.\":\"\",\"Saved from\":\"\",PNG:\"\",JPG:\"\",GIF:\"\",SVG:\"\",PDF:\"\",JSON:\"\",CSV:\"\",XLSX:\"\",\"Use TAB to select grip buttons or left and right arrows to change selection\":\"\",\"Use left and right arrows to move selection\":\"\",\"Use left and right arrows to move left selection\":\"\",\"Use left and right arrows to move right selection\":\"\",\"Use TAB select grip buttons or up and down arrows to change selection\":\"\",\"Use up and down arrows to move selection\":\"\",\"Use up and down arrows to move lower selection\":\"\",\"Use up and down arrows to move upper selection\":\"\",\"From %1 to %2\":\"Od %1 do %2\",\"From %1\":\"Od %1\",\"To %1\":\"Do %1\",\"No parser available for file: %1\":\"\",\"Error parsing file: %1\":\"\",\"Unable to load file: %1\":\"\",\"Invalid date\":\"\"}}(r,_))&&(o.exports=t);const n=a({__proto__:null,default:e(_)},[_]);export{n as p};\n"], "mappings": ";;;;;;;;;AAI6F,SAAS,EAAE,GAAEA,IAAE;AAAC,WAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,UAAMC,KAAEF,GAAEC,EAAC;AAAE,QAAG,YAAU,OAAOC,MAAG,CAAC,MAAM,QAAQA,EAAC;AAAE,iBAAUF,MAAKE,GAAE,KAAG,cAAYF,MAAG,EAAEA,MAAK,IAAG;AAAC,cAAMC,KAAE,OAAO,yBAAyBC,IAAEF,EAAC;AAAE,QAAAC,MAAG,OAAO,eAAe,GAAED,IAAEC,GAAE,MAAIA,KAAE,EAAC,YAAW,MAAG,KAAI,MAAIC,GAAEF,EAAC,EAAC,CAAC;AAAA,MAAC;AAAA;AAAA,EAAC;AAAC,SAAO,OAAO,OAAO,OAAO,eAAe,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;AAAC;AAAC,IAAIE;AAAJ,IAAM;AAAN,IAAQ,IAAE,CAAC;AAAX,IAAa,IAAE,EAAC,IAAI,UAAS;AAAC,SAAO;AAAC,GAAE,IAAI,QAAQ,GAAE;AAAC,MAAE;AAAC,EAAC;AAAEA,KAAE,GAAE,YAAU,IAAE,SAAS,GAAEF,IAAE;AAAC,SAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAEA,GAAE,UAAQ,EAAC,mBAAkB,KAAI,oBAAmB,KAAI,gBAAe,MAAK,gBAAe,KAAI,sBAAqB,KAAI,sBAAqB,KAAI,sBAAqB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,wBAAuB,KAAI,wBAAuB,KAAI,wBAAuB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,gBAAe,KAAI,iBAAgB,MAAK,iBAAgB,MAAK,iBAAgB,MAAK,iBAAgB,MAAK,iBAAgB,MAAK,mBAAkB,aAAY,cAAa,YAAW,cAAa,SAAQ,YAAW,SAAQ,WAAU,UAAS,YAAW,MAAK,aAAY,OAAM,YAAW,QAAO,uBAAsB,OAAM,8BAA6B,UAAS,8BAA6B,aAAY,4BAA2B,gBAAe,2BAA0B,kBAAiB,4BAA2B,kBAAiB,6BAA4B,wBAAuB,4BAA2B,8BAA6B,kBAAiB,MAAK,yBAAwB,SAAQ,uBAAsB,YAAW,sBAAqB,iBAAgB,uBAAsB,iBAAgB,wBAAuB,uBAAsB,uBAAsB,6BAA4B,kBAAiB,MAAK,uBAAsB,SAAQ,sBAAqB,cAAa,uBAAsB,cAAa,wBAAuB,oBAAmB,uBAAsB,0BAAyB,gBAAe,SAAQ,oBAAmB,cAAa,qBAAoB,cAAa,sBAAqB,oBAAmB,qBAAoB,0BAAyB,eAAc,QAAO,oBAAmB,QAAO,qBAAoB,cAAa,oBAAmB,oBAAmB,gBAAe,QAAO,sBAAqB,QAAO,qBAAoB,QAAO,iBAAgB,QAAO,sBAAqB,cAAa,gBAAe,QAAO,SAAQ,QAAO,SAAQ,UAAS,GAAE,KAAI,GAAE,KAAI,IAAG,MAAK,IAAG,MAAK,QAAO,MAAK,QAAO,MAAK,SAAQ,YAAW,UAAS,UAAS,OAAM,SAAQ,OAAM,YAAW,KAAI,QAAO,MAAK,WAAU,MAAK,SAAQ,QAAO,YAAW,WAAU,YAAW,SAAQ,gBAAe,UAAS,aAAY,UAAS,WAAU,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,cAAa,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,QAAO,aAAY,QAAO,gBAAe,SAAQ,UAAS,WAAU,SAAQ,UAAS,YAAW,QAAO,UAAS,UAAS,UAAS,KAAI,UAAS,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,OAAM,KAAI,QAAO,UAAS,SAASG,IAAE;AAAC,QAAIH,KAAE;AAAK,QAAGG,KAAE,MAAIA,KAAE,GAAG,SAAOA,KAAE,IAAG;AAAA,MAAC,KAAK;AAAE,QAAAH,KAAE;AAAK;AAAA,MAAM,KAAK;AAAE,QAAAA,KAAE;AAAK;AAAA,MAAM,KAAK;AAAE,QAAAA,KAAE;AAAA,IAAI;AAAC,WAAOA;AAAA,EAAC,GAAE,YAAW,gBAAe,MAAK,eAAc,MAAK,aAAY,QAAO,WAAU,uCAAsC,IAAG,SAAQ,eAAc,MAAK,iBAAgB,OAAM,IAAG,gBAAe,IAAG,aAAY,IAAG,aAAY,IAAG,eAAc,IAAG,eAAc,IAAG,kBAAiB,IAAG,gBAAe,IAAG,iBAAgB,IAAG,iBAAgB,IAAG,gBAAe,IAAG,QAAO,IAAG,sBAAqB,IAAG,eAAc,IAAG,iBAAgB,IAAG,eAAc,IAAG,oBAAmB,IAAG,iBAAgB,IAAG,kBAAiB,IAAG,cAAa,IAAG,KAAI,IAAG,0BAAyB,IAAG,2BAA0B,IAAG,qCAAoC,IAAG,+DAA8D,IAAG,QAAO,UAAS,OAAM,SAAQ,MAAK,QAAO,OAAM,UAAS,qCAAoC,IAAG,uCAAsC,IAAG,8CAA6C,IAAG,4EAA2E,IAAG,wFAAuF,IAAG,qCAAoC,IAAG,yBAAwB,IAAG,gFAA+E,IAAG,cAAa,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,MAAK,IAAG,KAAI,IAAG,MAAK,IAAG,+EAA8E,IAAG,+CAA8C,IAAG,oDAAmD,IAAG,qDAAoD,IAAG,yEAAwE,IAAG,4CAA2C,IAAG,kDAAiD,IAAG,kDAAiD,IAAG,iBAAgB,eAAc,WAAU,SAAQ,SAAQ,SAAQ,oCAAmC,IAAG,0BAAyB,IAAG,2BAA0B,IAAG,gBAAe,GAAE;AAAC,EAAE,GAAE,CAAC,OAAKE,GAAE,UAAQ;AAAG,IAAM,IAAE,EAAE,EAAC,WAAU,MAAK,SAAQ,EAAE,CAAC,EAAC,GAAE,CAAC,CAAC,CAAC;", "names": ["r", "a", "o", "e"]}