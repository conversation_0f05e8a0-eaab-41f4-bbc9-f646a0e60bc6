{"version": 3, "sources": ["../../@arcgis/core/layers/graphics/sources/WFSSourceWorker.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{createTask as e}from\"../../../core/asyncUtils.js\";import t from\"../../../core/Error.js\";import r from\"../../../core/Logger.js\";import{isSome as s,unwrap as a}from\"../../../core/maybe.js\";import{throwIfAborted as i,isAbortError as o}from\"../../../core/promiseUtils.js\";import{equals as n,WGS84 as u}from\"../../../geometry/support/spatialReferenceUtils.js\";import{convertFromGeometry as p,convertToGeometry as h}from\"../featureConversionUtils.js\";import y from\"../data/FeatureStore.js\";import{project as c,checkProjectionSupport as m}from\"../data/projectionSupport.js\";import{QueryEngine as l}from\"../data/QueryEngine.js\";import{validateGeoJSON as g,createOptimizedFeatures as f}from\"./geojson/geojson.js\";import{mixAttributes as _}from\"./support/sourceUtils.js\";import{getFeature as d}from\"../../ogc/wfsUtils.js\";import w from\"../../support/FieldsIndex.js\";class E{constructor(){this._queryEngine=null,this._customParameters=null,this._snapshotFeatures=async e=>{const{objectIdField:t}=this._queryEngine,r=await d(this._getFeatureUrl??\"\",this._featureType.typeName,this._getFeatureOutputFormat,{customParameters:this._customParameters,dateFields:this._queryEngine.fieldsIndex.dateFields.map((e=>e.name)),signal:e});await g(r),i(e);const a=f(r,{geometryType:this._queryEngine.geometryType,hasZ:!1,objectIdField:t});if(!n(this._queryEngine.spatialReference,u))for(const i of a)s(i.geometry)&&(i.geometry=p(c(h(i.geometry,this._queryEngine.geometryType,!1,!1),u,this._queryEngine.spatialReference)));let o=1;for(const s of a){const e={};_(this._fieldsIndex,e,s.attributes,!0),s.attributes=e,null==s.attributes[t]&&(s.objectId=s.attributes[t]=o++)}return a}}destroy(){this._queryEngine?.destroy(),this._queryEngine=null}async load(e,t){const{getFeatureUrl:r,getFeatureOutputFormat:s,spatialReference:o,fields:n,geometryType:u,featureType:p,objectIdField:h,customParameters:c}=e;this._featureType=p,this._customParameters=c,this._getFeatureUrl=r,this._getFeatureOutputFormat=s,this._fieldsIndex=new w(n),await this._checkProjection(o),i(t),this._queryEngine=new l({fields:n,geometryType:u,hasM:!1,hasZ:!1,objectIdField:h,spatialReference:o,timeInfo:null,featureStore:new y({geometryType:u,hasM:!1,hasZ:!1})});const m=await this._snapshotFeatures(a(t.signal));return this._queryEngine.featureStore.addMany(m),{extent:(await this._queryEngine.fetchRecomputedExtents()).fullExtent}}async applyEdits(){throw new t(\"wfs-source:editing-not-supported\",\"applyEdits() is not supported on WFSLayer\")}async queryFeatures(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQuery(e,t.signal)}async queryFeatureCount(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForCount(e,t.signal)}async queryObjectIds(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForIds(e,t.signal)}async queryExtent(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForExtent(e,t.signal)}async querySnapping(e,t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForSnapping(e,t.signal)}async refresh(s){return this._customParameters=s,this._snapshotTask?.abort(),this._snapshotTask=e(this._snapshotFeatures),this._snapshotTask.promise.then((e=>{this._queryEngine.featureStore.clear(),e&&this._queryEngine.featureStore.addMany(e)}),(e=>{this._queryEngine.featureStore.clear(),o(e)||r.getLogger(\"esri.layers.WFSLayer\").error(new t(\"wfs-layer:getfeature-error\",\"An error occurred during the GetFeature request\",{error:e}))})),await this._waitSnapshotComplete(),{extent:(await this._queryEngine.fetchRecomputedExtents()).fullExtent}}async _waitSnapshotComplete(){if(this._snapshotTask&&!this._snapshotTask.finished){try{await this._snapshotTask.promise}catch{}return this._waitSnapshotComplete()}}async _checkProjection(e){try{await m(u,e)}catch{throw new t(\"unsupported-projection\",\"Projection not supported\",{spatialReference:e})}}}export{E as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI+1B,IAAMA,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,eAAa,MAAK,KAAK,oBAAkB,MAAK,KAAK,oBAAkB,OAAMC,OAAG;AAAC,YAAK,EAAC,eAAc,EAAC,IAAE,KAAK,cAAaC,KAAE,MAAM,EAAE,KAAK,kBAAgB,IAAG,KAAK,aAAa,UAAS,KAAK,yBAAwB,EAAC,kBAAiB,KAAK,mBAAkB,YAAW,KAAK,aAAa,YAAY,WAAW,IAAK,CAAAD,OAAGA,GAAE,IAAK,GAAE,QAAOA,GAAC,CAAC;AAAE,YAAM,EAAEC,EAAC,GAAE,EAAED,EAAC;AAAE,YAAM,IAAE,EAAEC,IAAE,EAAC,cAAa,KAAK,aAAa,cAAa,MAAK,OAAG,eAAc,EAAC,CAAC;AAAE,UAAG,CAAC,EAAE,KAAK,aAAa,kBAAiB,CAAC,EAAE,YAAU,KAAK,EAAE,GAAE,EAAE,QAAQ,MAAI,EAAE,WAAS,GAAE,EAAE,GAAE,EAAE,UAAS,KAAK,aAAa,cAAa,OAAG,KAAE,GAAE,GAAE,KAAK,aAAa,gBAAgB,CAAC;AAAG,UAAI,IAAE;AAAE,iBAAUC,MAAK,GAAE;AAAC,cAAMF,KAAE,CAAC;AAAE,UAAE,KAAK,cAAaA,IAAEE,GAAE,YAAW,IAAE,GAAEA,GAAE,aAAWF,IAAE,QAAME,GAAE,WAAW,CAAC,MAAIA,GAAE,WAASA,GAAE,WAAW,CAAC,IAAE;AAAA,MAAI;AAAC,aAAO;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAJroD;AAIsoD,eAAK,iBAAL,mBAAmB,WAAU,KAAK,eAAa;AAAA,EAAI;AAAA,EAAC,MAAM,KAAKF,IAAE,GAAE;AAAC,UAAK,EAAC,eAAcC,IAAE,wBAAuBC,IAAE,kBAAiB,GAAE,QAAO,GAAE,cAAa,GAAE,aAAY,GAAE,eAAc,GAAE,kBAAiBC,GAAC,IAAEH;AAAE,SAAK,eAAa,GAAE,KAAK,oBAAkBG,IAAE,KAAK,iBAAeF,IAAE,KAAK,0BAAwBC,IAAE,KAAK,eAAa,IAAID,GAAE,CAAC,GAAE,MAAM,KAAK,iBAAiB,CAAC,GAAE,EAAE,CAAC,GAAE,KAAK,eAAa,IAAI,GAAE,EAAC,QAAO,GAAE,cAAa,GAAE,MAAK,OAAG,MAAK,OAAG,eAAc,GAAE,kBAAiB,GAAE,UAAS,MAAK,cAAa,IAAIG,GAAE,EAAC,cAAa,GAAE,MAAK,OAAG,MAAK,MAAE,CAAC,EAAC,CAAC;AAAE,UAAMC,KAAE,MAAM,KAAK,kBAAkB,EAAE,EAAE,MAAM,CAAC;AAAE,WAAO,KAAK,aAAa,aAAa,QAAQA,EAAC,GAAE,EAAC,SAAQ,MAAM,KAAK,aAAa,uBAAuB,GAAG,WAAU;AAAA,EAAC;AAAA,EAAC,MAAM,aAAY;AAAC,UAAM,IAAIH,GAAE,oCAAmC,2CAA2C;AAAA,EAAC;AAAA,EAAC,MAAM,cAAcF,KAAE,CAAC,GAAE,IAAE,CAAC,GAAE;AAAC,WAAO,MAAM,KAAK,sBAAsB,GAAE,KAAK,aAAa,aAAaA,IAAE,EAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,kBAAkBA,KAAE,CAAC,GAAE,IAAE,CAAC,GAAE;AAAC,WAAO,MAAM,KAAK,sBAAsB,GAAE,KAAK,aAAa,qBAAqBA,IAAE,EAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeA,KAAE,CAAC,GAAE,IAAE,CAAC,GAAE;AAAC,WAAO,MAAM,KAAK,sBAAsB,GAAE,KAAK,aAAa,mBAAmBA,IAAE,EAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,YAAYA,KAAE,CAAC,GAAE,IAAE,CAAC,GAAE;AAAC,WAAO,MAAM,KAAK,sBAAsB,GAAE,KAAK,aAAa,sBAAsBA,IAAE,EAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,cAAcA,IAAE,IAAE,CAAC,GAAE;AAAC,WAAO,MAAM,KAAK,sBAAsB,GAAE,KAAK,aAAa,wBAAwBA,IAAE,EAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQE,IAAE;AAJjjG;AAIkjG,WAAO,KAAK,oBAAkBA,KAAE,UAAK,kBAAL,mBAAoB,SAAQ,KAAK,gBAAcI,GAAE,KAAK,iBAAiB,GAAE,KAAK,cAAc,QAAQ,KAAM,CAAAN,OAAG;AAAC,WAAK,aAAa,aAAa,MAAM,GAAEA,MAAG,KAAK,aAAa,aAAa,QAAQA,EAAC;AAAA,IAAC,GAAI,CAAAA,OAAG;AAAC,WAAK,aAAa,aAAa,MAAM,GAAE,EAAEA,EAAC,KAAG,EAAE,UAAU,sBAAsB,EAAE,MAAM,IAAIE,GAAE,8BAA6B,mDAAkD,EAAC,OAAMF,GAAC,CAAC,CAAC;AAAA,IAAC,CAAE,GAAE,MAAM,KAAK,sBAAsB,GAAE,EAAC,SAAQ,MAAM,KAAK,aAAa,uBAAuB,GAAG,WAAU;AAAA,EAAC;AAAA,EAAC,MAAM,wBAAuB;AAAC,QAAG,KAAK,iBAAe,CAAC,KAAK,cAAc,UAAS;AAAC,UAAG;AAAC,cAAM,KAAK,cAAc;AAAA,MAAO,QAAM;AAAA,MAAC;AAAC,aAAO,KAAK,sBAAsB;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiBA,IAAE;AAAC,QAAG;AAAC,YAAMO,GAAE,GAAEP,EAAC;AAAA,IAAC,QAAM;AAAC,YAAM,IAAIE,GAAE,0BAAyB,4BAA2B,EAAC,kBAAiBF,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;", "names": ["E", "e", "r", "s", "c", "g", "m", "j", "f"]}