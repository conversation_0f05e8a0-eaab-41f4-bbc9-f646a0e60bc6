{"version": 3, "sources": ["../../@arcgis/core/layers/support/GeoreferenceBase.js", "../../@arcgis/core/layers/support/ControlPointsGeoreference.js", "../../@arcgis/core/layers/support/CornersGeoreference.js", "../../@arcgis/core/layers/support/ExtentAndRotationGeoreference.js", "../../@arcgis/core/layers/support/MediaElementBase.js", "../../@arcgis/core/layers/support/ImageElement.js", "../../@arcgis/core/layers/support/VideoElement.js", "../../@arcgis/core/layers/support/LocalMediaElementSource.js", "../../@arcgis/core/layers/MediaLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{Clonable as r}from\"../../core/Clonable.js\";import o from\"../../core/Logger.js\";import{isNone as t}from\"../../core/maybe.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import\"../../core/Error.js\";import\"../../core/has.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";import{projectOrLoad as c}from\"../../geometry/projection.js\";let a=class extends r{projectOrWarn(e,r){if(t(e))return e;const{geometry:s,pending:a}=c(e,r);return a?null:a||s?s:(o.getLogger(this.declaredClass).warn(\"geometry could not be projected to the spatial reference\",{georeference:this,geometry:e,sourceSpatialReference:e.spatialReference,targetSpatialReference:r}),null)}};a=e([s(\"esri.layers.support.GeoreferenceBase\")],a);const p=a;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../chunks/tslib.es6.js\";import t from\"../../core/Accessor.js\";import r from\"../../core/Error.js\";import{JSONSupportMixin as e}from\"../../core/JSONSupport.js\";import n from\"../../core/Logger.js\";import{isNone as i,isSome as s,unwrap as c}from\"../../core/maybe.js\";import{transformProjective as l,getProjectiveTransform as a}from\"../../core/perspectiveUtils.js\";import{createScreenPoint as p}from\"../../core/screenUtils.js\";import{property as u}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{reader as m}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as f}from\"../../core/accessorSupport/decorators/subclass.js\";import{writer as P}from\"../../core/accessorSupport/decorators/writer.js\";import{e as h}from\"../../chunks/mat3.js\";import{f as y,c as d}from\"../../chunks/mat3f64.js\";import{s as g,r as j,l as w}from\"../../chunks/vec2.js\";import{a as v,f as R}from\"../../chunks/vec2f64.js\";import x from\"../../geometry/Point.js\";import S from\"../../geometry/Polygon.js\";import{projectOrLoad as C}from\"../../geometry/projection.js\";import T from\"../../geometry/SpatialReference.js\";import _ from\"./GeoreferenceBase.js\";const O=d(),b=v();let k=class extends t{constructor(){super(...arguments),this.sourcePoint=null,this.mapPoint=null}};o([u()],k.prototype,\"sourcePoint\",void 0),o([u({type:x})],k.prototype,\"mapPoint\",void 0),k=o([f(\"esri.layers.support.ControlPoint\")],k);let I=class extends(e(_)){constructor(o){super(o),this.controlPoints=null,this.height=0,this.type=\"control-points\",this.width=0}readControlPoints(o,t){const r=T.fromJSON(t.spatialReference),e=y(...t.coefficients,1);return o.map((o=>(g(b,o.x,o.y),l(b,b,e),{sourcePoint:o,mapPoint:new x({x:b[0],y:b[1],spatialReference:r})})))}writeControlPoints(o,t,e,l){if(i(this.transform)){const o=new r(\"web-document-write:invalid-georeference\",\"Invalid 'controlPoints', 'width', 'height' configuration.\",{layer:l?.layer,georeference:this});l?.messages?l.messages.push(o):n.getLogger(this.declaredClass).error(o.name,o.message)}else s(o)&&M(o[0])&&(t.controlPoints=o.map((o=>{const t=c(o.sourcePoint);return{x:t.x,y:t.y}})),t.spatialReference=o[0].mapPoint.spatialReference.toJSON(),t.coefficients=this.transform.slice(0,8))}get coords(){if(i(this.controlPoints))return null;const o=this._updateTransform(O);if(i(o)||!M(this.controlPoints[0]))return null;const t=this.controlPoints[0].mapPoint.spatialReference;return X(o,this.width,this.height,t)}set coords(o){if(i(this.controlPoints)||!M(this.controlPoints[0]))return;const t=this.controlPoints[0].mapPoint.spatialReference;if(o=this.projectOrWarn(o,t),i(o))return;const{width:r,height:e}=this,{rings:[[n,s,a,u]]}=o,m={sourcePoint:p(0,e),mapPoint:new x({x:n[0],y:n[1],spatialReference:t})},f={sourcePoint:p(0,0),mapPoint:new x({x:s[0],y:s[1],spatialReference:t})},P={sourcePoint:p(r,0),mapPoint:new x({x:a[0],y:a[1],spatialReference:t})},h={sourcePoint:p(r,e),mapPoint:new x({x:u[0],y:u[1],spatialReference:t})};M(m)&&M(f)&&M(P)&&M(h)&&(F(O,m,f,P,h),this.controlPoints=c(this.controlPoints).map((({sourcePoint:o})=>(g(b,o.x,o.y),l(b,b,O),{sourcePoint:o,mapPoint:new x({x:b[0],y:b[1],spatialReference:t})}))))}get inverseTransform(){return i(this.transform)?null:h(d(),this.transform)}get transform(){return this._updateTransform()}toMap(o){if(i(o)||i(this.transform)||i(this.controlPoints)||!M(this.controlPoints[0]))return null;g(b,o.x,o.y);const t=this.controlPoints[0].mapPoint.spatialReference;return l(b,b,this.transform),new x({x:b[0],y:b[1],spatialReference:t})}toSource(o){if(i(o)||i(this.inverseTransform)||i(this.controlPoints)||!M(this.controlPoints[0]))return null;const t=this.controlPoints[0].mapPoint.spatialReference;return o=o.normalize(),o=C(o,t).geometry,i(o)?null:(g(b,o.x,o.y),l(b,b,this.inverseTransform),p(b[0],b[1]))}_updateTransform(o){const{controlPoints:t,width:r,height:e}=this;if(i(t)||!(r>0)||!(e>0))return null;const[n,s,c,l]=t;if(!M(n))return null;const a=n.mapPoint.spatialReference,p=this._projectControlPoint(s,a),u=this._projectControlPoint(c,a),m=this._projectControlPoint(l,a);if(!p.valid||!u.valid||!m.valid)return null;if(!M(p.controlPoint))return null;i(o)&&(o=d());let f=null;return f=M(u.controlPoint)&&M(m.controlPoint)?F(o,n,p.controlPoint,u.controlPoint,m.controlPoint):M(u.controlPoint)?D(o,n,p.controlPoint,u.controlPoint):W(o,n,p.controlPoint),f.every((o=>0===o))?null:f}_projectControlPoint(o,t){if(!M(o))return{valid:!0,controlPoint:o};const{sourcePoint:r,mapPoint:e}=o,{geometry:i,pending:s}=C(e,t);return s?{valid:!1,controlPoint:null}:s||i?{valid:!0,controlPoint:{sourcePoint:r,mapPoint:i}}:(n.getLogger(this.declaredClass).warn(\"map point could not be projected to the spatial reference\",{georeference:this,controlPoint:o,sourceSpatialReference:e.spatialReference,targetSpatialReference:t}),{valid:!1,controlPoint:null})}};function M(o){return s(o)&&s(o.sourcePoint)&&s(o.mapPoint)}o([u({type:[k],json:{write:{allowNull:!1,isRequired:!0}}})],I.prototype,\"controlPoints\",void 0),o([m(\"controlPoints\")],I.prototype,\"readControlPoints\",null),o([P(\"controlPoints\")],I.prototype,\"writeControlPoints\",null),o([u()],I.prototype,\"coords\",null),o([u({json:{write:!0}})],I.prototype,\"height\",void 0),o([u({readOnly:!0})],I.prototype,\"inverseTransform\",null),o([u({readOnly:!0})],I.prototype,\"transform\",null),o([u({json:{write:!0}})],I.prototype,\"width\",void 0),I=o([f(\"esri.layers.support.ControlPointsGeoreference\")],I);const N=v(),A=v(),J=v(),L=v(),U=v(),G=v(),q=v(),z=v(),B=Math.PI/2;function E(o,t,r){g(o,r.sourcePoint.x,r.sourcePoint.y),g(t,r.mapPoint.x,r.mapPoint.y)}function W(o,t,r){return E(N,U,t),E(A,G,r),j(J,A,N,B),j(L,N,A,B),j(q,G,U,-B),j(z,U,G,-B),V(o,N,A,J,L,U,G,q,z)}function D(o,t,r,e){return E(N,U,t),E(A,G,r),E(J,q,e),w(L,N,A,.5),j(L,J,L,Math.PI),w(z,U,G,.5),j(z,q,z,Math.PI),V(o,N,A,J,L,U,G,q,z)}function F(o,t,r,e,n){return E(N,U,t),E(A,G,r),E(J,q,e),E(L,z,n),V(o,N,A,J,L,U,G,q,z)}const H=new Array(8).fill(0),K=new Array(8).fill(0);function Q(o,t,r,e,n){return o[0]=t[0],o[1]=t[1],o[2]=r[0],o[3]=r[1],o[4]=e[0],o[5]=e[1],o[6]=n[0],o[7]=n[1],o}function V(o,t,r,e,n,i,s,c,l){return a(o,Q(H,t,r,e,n),Q(K,i,s,c,l))}function X(o,t,r,e){const n=R(0,r),i=R(0,0),s=R(t,0),c=R(t,r);return l(n,n,o),l(i,i,o),l(s,s,o),l(c,c,o),new S({rings:[[n,i,s,c,n]],spatialReference:e})}const Y=I;export{Y as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import{isNone as e}from\"../../core/maybe.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as r}from\"../../core/accessorSupport/decorators/subclass.js\";import s from\"../../geometry/Point.js\";import p from\"../../geometry/Polygon.js\";import i from\"./GeoreferenceBase.js\";let n=class extends i{constructor(t){super(t),this.bottomLeft=null,this.bottomRight=null,this.topLeft=null,this.topRight=null,this.type=\"corners\"}get coords(){let{topLeft:t,topRight:o,bottomLeft:r,bottomRight:s}=this;if(e(t)||e(o)||e(r)||e(s))return null;const i=t.spatialReference;return o=this.projectOrWarn(o,i),r=this.projectOrWarn(r,i),s=this.projectOrWarn(s,i),e(o)||e(r)||e(s)?null:new p({rings:[[[r.x,r.y],[t.x,t.y],[o.x,o.y],[s.x,s.y],[r.x,r.y]]],spatialReference:i})}set coords(t){const{topLeft:o}=this;if(e(o))return;const r=o.spatialReference;if(t=this.projectOrWarn(t,r),e(t))return;const{rings:[[p,i,n,c]]}=t;this.bottomLeft=new s({x:p[0],y:p[1],spatialReference:r}),this.topLeft=new s({x:i[0],y:i[1],spatialReference:r}),this.topRight=new s({x:n[0],y:n[1],spatialReference:r}),this.bottomRight=new s({x:c[0],y:c[1],spatialReference:r})}};t([o()],n.prototype,\"coords\",null),t([o({type:s})],n.prototype,\"bottomLeft\",void 0),t([o({type:s})],n.prototype,\"bottomRight\",void 0),t([o({type:s})],n.prototype,\"topLeft\",void 0),t([o({type:s})],n.prototype,\"topRight\",void 0),n=t([r(\"esri.layers.support.CornersGeoreference\")],n);const c=n;export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import{rad2deg as e}from\"../../core/mathUtils.js\";import{isNone as r}from\"../../core/maybe.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";import{t as n}from\"../../chunks/common.js\";import i from\"../../geometry/Extent.js\";import a from\"../../geometry/Polygon.js\";import c from\"./GeoreferenceBase.js\";let p=class extends c{constructor(t){super(t),this.extent=null,this.rotation=0,this.type=\"extent-and-rotation\"}get coords(){if(r(this.extent))return null;const{xmin:t,ymin:e,xmax:o,ymax:s,spatialReference:n}=this.extent;let i;if(this.rotation){const{x:r,y:n}=this.extent.center,a=m(r,n,this.rotation);i=[a(t,e),a(t,s),a(o,s),a(o,e)],i.push(i[0])}else i=[[t,e],[t,s],[o,s],[o,e],[t,e]];return new a({rings:[i],spatialReference:n})}set coords(t){if(r(t)||r(this.extent))return;const o=this.extent.spatialReference;if(t=this.projectOrWarn(t,o),r(t)||r(t.extent))return;const{rings:[[s,n,a]],extent:{center:{x:c,y:p}}}=t,x=e(Math.PI/2-Math.atan2(n[1]-s[1],n[0]-s[0])),u=m(c,p,-x),[f,h]=u(s[0],s[1]),[l,y]=u(a[0],a[1]);this.extent=new i({xmin:f,ymin:h,xmax:l,ymax:y,spatialReference:o}),this.rotation=x}};function m(t,e,r){const o=n(r),s=Math.cos(o),i=Math.sin(o);return(r,o)=>[s*(r-t)+i*(o-e)+t,s*(o-e)-i*(r-t)+e]}t([o()],p.prototype,\"coords\",null),t([o({type:i})],p.prototype,\"extent\",void 0),t([o({type:Number})],p.prototype,\"rotation\",void 0),p=t([s(\"esri.layers.support.ExtentAndRotationGeoreference\")],p);const x=p;export{x as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{NumericIdentifiableMixin as r}from\"../../core/Identifiable.js\";import{JSONSupportMixin as o}from\"../../core/JSONSupport.js\";import t from\"../../core/Loadable.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{reader as p}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as c}from\"../../core/accessorSupport/decorators/subclass.js\";import n from\"./ControlPointsGeoreference.js\";import a from\"./CornersGeoreference.js\";import i from\"./ExtentAndRotationGeoreference.js\";import m from\"./GeoreferenceBase.js\";const f={key:\"type\",base:m,typeMap:{\"control-points\":n,corners:a,\"extent-and-rotation\":i}};let l=class extends(r(o(t))){constructor(){super(...arguments),this.georeference=null,this.opacity=1}readGeoreference(e){return n.fromJSON(e)}};e([s({types:f,json:{write:!0}})],l.prototype,\"georeference\",void 0),e([p(\"georeference\")],l.prototype,\"readGeoreference\",null),e([s()],l.prototype,\"opacity\",void 0),l=e([c(\"esri.layers.support.MediaElementBase\")],l);const d=l;export{d as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import t from\"../../request.js\";import r from\"../../core/Error.js\";import{isNone as o}from\"../../core/maybe.js\";import{isDataProtocol as s,isBlobProtocol as i,isAbsolute as n,dataToBlob as a,join as m}from\"../../core/urlUtils.js\";import{generateUUID as p}from\"../../core/uuid.js\";import{property as c}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{reader as l}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as u}from\"../../core/accessorSupport/decorators/subclass.js\";import{writer as d}from\"../../core/accessorSupport/decorators/writer.js\";import f from\"./MediaElementBase.js\";import{getResourceContentExtension as g}from\"../../portal/support/resourceExtension.js\";import{f as h,t as y,i as v,M as j}from\"../../chunks/persistableUrlUtils.js\";let I=class extends f{constructor(e){super(e),this.content=null,this.image=null,this.type=\"image\",this.image=null}load(){const e=this.image;if(\"string\"==typeof e){const r=t(e,{responseType:\"image\"}).then((({data:e})=>{this._set(\"content\",e)}));this.addResolvingPromise(r)}else if(e instanceof HTMLImageElement){const t=e.decode().then((()=>{this._set(\"content\",e)}));this.addResolvingPromise(t)}else e?this._set(\"content\",e):this.addResolvingPromise(Promise.reject(new r(\"image-element:invalid-image-type\",\"Invalid image type\",{image:e})));return Promise.resolve(this)}readImage(e,t,r){return h(t.url,r)}writeImage(e,t,r,a){if(o(e))return;const m=a?.portalItem,p=a?.resources;if(!m||!p)return void(\"string\"==typeof e&&(t[r]=y(e,a)));const c=\"string\"!=typeof e||s(e)||i(e)?null:e;if(c){if(null==v(c))return void(t[r]=c);const e=y(c,{...a,verifyItemRelativeUrls:a&&a.verifyItemRelativeUrls?{writtenUrls:a.verifyItemRelativeUrls.writtenUrls,rootPath:void 0}:void 0},j.NO);if(m&&e&&!n(e))return p.toKeep.push({resource:m.resourceFromPath(e),compress:!1}),void(t[r]=e)}t[r]=\"<pending>\",p.pendingOperations.push(E(e).then((e=>{const o=U(e,m);t[r]=o.itemRelativeUrl,p.toAdd.push({resource:o,content:e,compress:!1,finish:e=>{this.image=e.url}})})))}};e([c({readOnly:!0})],I.prototype,\"content\",void 0),e([c({json:{name:\"url\",type:String}})],I.prototype,\"image\",void 0),e([l(\"image\",[\"url\"])],I.prototype,\"readImage\",null),e([d(\"image\")],I.prototype,\"writeImage\",null),e([c({readOnly:!0,json:{name:\"mediaType\"}})],I.prototype,\"type\",void 0),I=e([u(\"esri.layers.support.ImageElement\")],I);const w=I;async function E(e){if(\"string\"==typeof e){if(i(e)){const{data:r}=await t(e,{responseType:\"blob\"});return r}if(s(e))return a(e);return E((await t(e,{responseType:\"image\"})).data)}return new Promise((t=>T(e).toBlob(t)))}function T(e){if(e instanceof HTMLCanvasElement)return e;const t=e instanceof HTMLImageElement?e.naturalWidth:e.width,r=e instanceof HTMLImageElement?e.naturalHeight:e.height,o=document.createElement(\"canvas\"),s=o.getContext(\"2d\");return o.width=t,o.height=r,e instanceof HTMLImageElement?s.drawImage(e,0,0,e.width,e.height):e instanceof ImageData&&s.putImageData(e,0,0),o}function U(e,t){const r=p(),o=`${m(\"media\",r)}.${g(e)}`;return t.resourceFromPath(o)}export{w as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import o from\"../../core/Error.js\";import s from\"../../core/Logger.js\";import{property as t}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as r}from\"../../core/accessorSupport/decorators/subclass.js\";import i from\"./MediaElementBase.js\";let n=class extends i{constructor(e){super(e),this.content=null,this.type=\"video\"}load(){const e=this.video;if(\"string\"==typeof e){const o=document.createElement(\"video\");o.src=e,o.crossOrigin=\"anonymous\",o.autoplay=!0,o.muted=!0,o.loop=!0,this.addResolvingPromise(this._loadVideo(o).then((()=>{this._set(\"content\",o)})))}else e instanceof HTMLVideoElement?this.addResolvingPromise(this._loadVideo(e).then((()=>{this._set(\"content\",e)}))):this.addResolvingPromise(Promise.reject(new o(\"video-element:invalid-video-type\",\"Invalid video type\",{video:e})));return Promise.resolve(this)}set video(e){\"not-loaded\"===this.loadStatus?this._set(\"video\",e):s.getLogger(this.declaredClass).error(\"#video\",\"video cannot be changed after the element is loaded.\")}_loadVideo(e){return new Promise(((o,s)=>{e.oncanplay=()=>{e.oncanplay=null,e.play().then(o,s)},\"anonymous\"!==e.crossOrigin&&(e.crossOrigin=\"anonymous\",e.src=e.src)}))}};e([t({readOnly:!0})],n.prototype,\"content\",void 0),e([t()],n.prototype,\"video\",null),n=e([r(\"esri.layers.support.VideoElement\")],n);const a=n;export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import\"../../geometry.js\";import t from\"../../core/Collection.js\";import{referenceSetter as s}from\"../../core/collectionUtils.js\";import r from\"../../core/Evented.js\";import{HandleOwnerMixin as o}from\"../../core/HandleOwner.js\";import n from\"../../core/Loadable.js\";import i from\"../../core/Logger.js\";import{isSome as a,unwrap as l,isNone as m}from\"../../core/maybe.js\";import{EsriPromiseMixin as c}from\"../../core/Promise.js\";import{throwIfAborted as p}from\"../../core/promiseUtils.js\";import{watch as d}from\"../../core/reactiveUtils.js\";import{property as h}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as f}from\"../../core/accessorSupport/decorators/subclass.js\";import u from\"../../geometry/Extent.js\";import{initializeProjection as g,project as _}from\"../../geometry/projection.js\";import{fromExtent as y}from\"../../geometry/support/aaBoundingRect.js\";import{extentIntersectsPolygon as x}from\"../../geometry/support/intersectsBase.js\";import{equals as j}from\"../../geometry/support/spatialReferenceUtils.js\";import{BoundsStore as R}from\"../graphics/data/BoundsStore.js\";import E from\"./ImageElement.js\";import w from\"./MediaElementBase.js\";import{MediaElementView as M}from\"./MediaElementView.js\";import I from\"./VideoElement.js\";import S from\"../../geometry/SpatialReference.js\";const V={key:\"type\",defaultKeyValue:\"image\",base:w,typeMap:{image:E,video:I}},C=t.ofType(V);let b=class extends(n.LoadableMixin(c(o(r.EventedAccessor)))){constructor(e){super(e),this._index=new R,this._elementViewsMap=new Map,this._elementsIndexes=new Map,this._elementsChangedHandler=e=>{for(const s of e.removed){const e=this._elementViewsMap.get(s);this._elementViewsMap.delete(s),this._index.delete(e),this.handles.remove(e),e.destroy(),this.notifyChange(\"fullExtent\")}const{spatialReference:t}=this;for(const s of e.added){if(this._elementViewsMap.get(s))continue;const e=new M({spatialReference:t,element:s});this._elementViewsMap.set(s,e);const r=d((()=>e.coords),(()=>this._updateIndexForElement(e,!1)));this._updateIndexForElement(e,!0),this.handles.add(r,e)}this._elementsIndexes.clear(),this.elements.forEach(((e,t)=>this._elementsIndexes.set(e,t))),this.emit(\"refresh\")},this.elements=new C}async load(e){if(p(e),!this.spatialReference){const e=this.elements.find((e=>a(e.georeference)&&a(e.georeference.coords)));this._set(\"spatialReference\",e?l(l(e.georeference).coords).spatialReference:S.WGS84)}return this._elementsChangedHandler({added:this.elements.items,removed:[]}),this.handles.add(this.elements.on(\"change\",this._elementsChangedHandler)),this}destroy(){this._index.clear(),this._elementViewsMap.clear(),this._elementsIndexes.clear()}set elements(e){this._set(\"elements\",s(e,this._get(\"elements\"),C))}get fullExtent(){if(\"not-loaded\"===this.loadStatus)return null;const e=this._index.fullBounds;return m(e)?null:new u({xmin:e[0],ymin:e[1],xmax:e[2],ymax:e[3],spatialReference:this.spatialReference})}set spatialReference(e){\"not-loaded\"===this.loadStatus?this._set(\"spatialReference\",e):i.getLogger(this.declaredClass).error(\"#spatialReference\",\"spatialReference cannot be changed after the source is loaded.\")}async queryElements(e,t){await this.load(),await g(e.spatialReference,this.spatialReference,null,t);const s=j(e.spatialReference,this.spatialReference)?e:_(e,this.spatialReference);if(!s)return[];const r=s.normalize(),o=[];for(const n of r)this._index.forEachInBounds(y(n),(({normalizedCoords:e,element:t})=>{a(e)&&x(n,e)&&o.push(t)}));return o.sort(((e,t)=>this._elementsIndexes.get(e)-this._elementsIndexes.get(t))),o}_updateIndexForElement(e,t){const s=e.normalizedBounds,r=this._index.has(e),o=a(s);this._index.delete(e),o&&this._index.set(e,s),this.notifyChange(\"fullExtent\"),t||(r!==o?this.emit(\"refresh\"):this.emit(\"change\",{element:e.element}))}};e([h()],b.prototype,\"elements\",null),e([h({readOnly:!0})],b.prototype,\"fullExtent\",null),e([h()],b.prototype,\"spatialReference\",null),b=e([f(\"esri.layers.support.LocalMediaElementSource\")],b);const v=b;export{v as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import r from\"../core/Collection.js\";import o from\"../core/Error.js\";import t from\"../core/Logger.js\";import{unwrap as s}from\"../core/maybe.js\";import{MultiOriginJSONMixin as i}from\"../core/MultiOriginJSONSupport.js\";import{property as a}from\"../core/accessorSupport/decorators/property.js\";import{cast as c}from\"../core/accessorSupport/decorators/cast.js\";import\"../core/arrayUtils.js\";import{reader as p}from\"../core/accessorSupport/decorators/reader.js\";import{subclass as l}from\"../core/accessorSupport/decorators/subclass.js\";import{writer as n}from\"../core/accessorSupport/decorators/writer.js\";import u from\"./Layer.js\";import{BlendLayer as m}from\"./mixins/BlendLayer.js\";import{OperationalLayer as d}from\"./mixins/OperationalLayer.js\";import{ScaleRangeLayer as y}from\"./mixins/ScaleRangeLayer.js\";import f from\"./support/ImageElement.js\";import h from\"./support/LocalMediaElementSource.js\";import S from\"./support/VideoElement.js\";function g(e){return\"object\"==typeof e&&null!=e&&\"type\"in e}let j=class extends(m(y(d(i(u))))){constructor(e){super(e),this.effectiveSource=null,this.copyright=null,this.operationalLayerType=\"MediaLayer\",this.spatialReference=null,this.type=\"media\",this.source=new h}load(e){const t=this.source;if(!t)return this.addResolvingPromise(Promise.reject(new o(\"media-layer:source-missing\",\"Set 'MediaLayer.source' before loading the layer.\"))),Promise.resolve(this);const s=g(t)?new h({elements:new r([t])}):t;this._set(\"effectiveSource\",s),this.spatialReference&&(s.spatialReference=this.spatialReference);const i=s.load(e).then((()=>{this.spatialReference=s.spatialReference}));return this.addResolvingPromise(i),Promise.resolve(this)}destroy(){s(this.effectiveSource)?.destroy(),s(this.source)?.destroy()}get fullExtent(){return this.loaded?this.effectiveSource.fullExtent:null}set source(e){\"not-loaded\"===this.loadStatus?this._set(\"source\",e):t.getLogger(this.declaredClass).error(\"#source\",\"source cannot be changed after the layer is loaded.\")}castSource(e){return e?Array.isArray(e)||e instanceof r?new h({elements:e}):e:null}readSource(e,r,o){const t=\"image\"===r.mediaType?new f:\"video\"===r.mediaType?new S:null;return t?.read(r,o),t}writeSource(e,r,t,s){e&&g(e)&&\"image\"===e.type?e.write(r,s):s?.messages&&s?.messages?.push(new o(\"media-layer:unsupported-source\",\"source must be an 'ImageElement'\"))}};e([a({readOnly:!0})],j.prototype,\"effectiveSource\",void 0),e([a({type:String})],j.prototype,\"copyright\",void 0),e([a({readOnly:!0})],j.prototype,\"fullExtent\",null),e([a({type:[\"MediaLayer\"]})],j.prototype,\"operationalLayerType\",void 0),e([a({type:[\"show\",\"hide\"]})],j.prototype,\"listMode\",void 0),e([a({nonNullable:!0,json:{write:{enabled:!0,allowNull:!1}}})],j.prototype,\"source\",null),e([c(\"source\")],j.prototype,\"castSource\",null),e([p(\"source\",[\"url\"])],j.prototype,\"readSource\",null),e([n(\"source\")],j.prototype,\"writeSource\",null),e([a()],j.prototype,\"spatialReference\",void 0),e([a({readOnly:!0})],j.prototype,\"type\",void 0),j=e([l(\"esri.layers.MediaLayer\")],j);const v=j;export{v as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAImc,IAAIA,KAAE,cAAc,EAAC;AAAA,EAAC,cAAcC,IAAEC,IAAE;AAAC,QAAG,EAAED,EAAC,EAAE,QAAOA;AAAE,UAAK,EAAC,UAASE,IAAE,SAAQH,GAAC,IAAE,GAAEC,IAAEC,EAAC;AAAE,WAAOF,KAAE,OAAKA,MAAGG,KAAEA,MAAG,EAAE,UAAU,KAAK,aAAa,EAAE,KAAK,4DAA2D,EAAC,cAAa,MAAK,UAASF,IAAE,wBAAuBA,GAAE,kBAAiB,wBAAuBC,GAAC,CAAC,GAAE;AAAA,EAAK;AAAC;AAAEF,KAAEC,GAAE,CAAC,EAAE,sCAAsC,CAAC,GAAED,EAAC;AAAE,IAAM,IAAEA;;;ACAwa,IAAMI,KAAEC,GAAE;AAAV,IAAYC,KAAEC,GAAE;AAAE,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,cAAY,MAAK,KAAK,WAAS;AAAA,EAAI;AAAC;AAAEF,GAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAEA,GAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,IAAEA,GAAE,CAAC,EAAE,kCAAkC,CAAC,GAAE,CAAC;AAAE,IAAI,IAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,YAAYG,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,gBAAc,MAAK,KAAK,SAAO,GAAE,KAAK,OAAK,kBAAiB,KAAK,QAAM;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAEC,IAAE;AAAC,UAAMC,KAAEC,GAAE,SAASF,GAAE,gBAAgB,GAAEJ,KAAEI,GAAE,GAAGA,GAAE,cAAa,CAAC;AAAE,WAAOD,GAAE,IAAK,CAAAA,QAAIE,GAAEJ,IAAEE,GAAE,GAAEA,GAAE,CAAC,GAAE,EAAEF,IAAEA,IAAED,EAAC,GAAE,EAAC,aAAYG,IAAE,UAAS,IAAI,EAAE,EAAC,GAAEF,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBI,GAAC,CAAC,EAAC,EAAG;AAAA,EAAC;AAAA,EAAC,mBAAmBF,IAAEC,IAAEJ,IAAEO,IAAE;AAAC,QAAG,EAAE,KAAK,SAAS,GAAE;AAAC,YAAMJ,KAAE,IAAIK,GAAE,2CAA0C,6DAA4D,EAAC,OAAMD,MAAA,gBAAAA,GAAG,OAAM,cAAa,KAAI,CAAC;AAAE,OAAAA,MAAA,gBAAAA,GAAG,YAASA,GAAE,SAAS,KAAKJ,EAAC,IAAE,EAAE,UAAU,KAAK,aAAa,EAAE,MAAMA,GAAE,MAAKA,GAAE,OAAO;AAAA,IAAC,MAAM,GAAEA,EAAC,KAAG,EAAEA,GAAE,CAAC,CAAC,MAAIC,GAAE,gBAAcD,GAAE,IAAK,CAAAA,OAAG;AAAC,YAAMC,KAAE,EAAED,GAAE,WAAW;AAAE,aAAM,EAAC,GAAEC,GAAE,GAAE,GAAEA,GAAE,EAAC;AAAA,IAAC,CAAE,GAAEA,GAAE,mBAAiBD,GAAE,CAAC,EAAE,SAAS,iBAAiB,OAAO,GAAEC,GAAE,eAAa,KAAK,UAAU,MAAM,GAAE,CAAC;AAAA,EAAE;AAAA,EAAC,IAAI,SAAQ;AAAC,QAAG,EAAE,KAAK,aAAa,EAAE,QAAO;AAAK,UAAMD,KAAE,KAAK,iBAAiBJ,EAAC;AAAE,QAAG,EAAEI,EAAC,KAAG,CAAC,EAAE,KAAK,cAAc,CAAC,CAAC,EAAE,QAAO;AAAK,UAAMC,KAAE,KAAK,cAAc,CAAC,EAAE,SAAS;AAAiB,WAAO,EAAED,IAAE,KAAK,OAAM,KAAK,QAAOC,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAOD,IAAE;AAAC,QAAG,EAAE,KAAK,aAAa,KAAG,CAAC,EAAE,KAAK,cAAc,CAAC,CAAC,EAAE;AAAO,UAAMC,KAAE,KAAK,cAAc,CAAC,EAAE,SAAS;AAAiB,QAAGD,KAAE,KAAK,cAAcA,IAAEC,EAAC,GAAE,EAAED,EAAC,EAAE;AAAO,UAAK,EAAC,OAAME,IAAE,QAAOL,GAAC,IAAE,MAAK,EAAC,OAAM,CAAC,CAACE,IAAEM,IAAEC,IAAEC,EAAC,CAAC,EAAC,IAAEP,IAAEQ,KAAE,EAAC,aAAY,EAAE,GAAEX,EAAC,GAAE,UAAS,IAAI,EAAE,EAAC,GAAEE,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBE,GAAC,CAAC,EAAC,GAAEE,KAAE,EAAC,aAAY,EAAE,GAAE,CAAC,GAAE,UAAS,IAAI,EAAE,EAAC,GAAEE,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBJ,GAAC,CAAC,EAAC,GAAE,IAAE,EAAC,aAAY,EAAEC,IAAE,CAAC,GAAE,UAAS,IAAI,EAAE,EAAC,GAAEI,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBL,GAAC,CAAC,EAAC,GAAEQ,KAAE,EAAC,aAAY,EAAEP,IAAEL,EAAC,GAAE,UAAS,IAAI,EAAE,EAAC,GAAEU,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBN,GAAC,CAAC,EAAC;AAAE,MAAEO,EAAC,KAAG,EAAEL,EAAC,KAAG,EAAE,CAAC,KAAG,EAAEM,EAAC,MAAI,EAAEb,IAAEY,IAAEL,IAAE,GAAEM,EAAC,GAAE,KAAK,gBAAc,EAAE,KAAK,aAAa,EAAE,IAAK,CAAC,EAAC,aAAYT,GAAC,OAAKE,GAAEJ,IAAEE,GAAE,GAAEA,GAAE,CAAC,GAAE,EAAEF,IAAEA,IAAEF,EAAC,GAAE,EAAC,aAAYI,IAAE,UAAS,IAAI,EAAE,EAAC,GAAEF,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBG,GAAC,CAAC,EAAC,EAAG;AAAA,EAAE;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,EAAE,KAAK,SAAS,IAAE,OAAKM,GAAEV,GAAE,GAAE,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,MAAMG,IAAE;AAAC,QAAG,EAAEA,EAAC,KAAG,EAAE,KAAK,SAAS,KAAG,EAAE,KAAK,aAAa,KAAG,CAAC,EAAE,KAAK,cAAc,CAAC,CAAC,EAAE,QAAO;AAAK,IAAAE,GAAEJ,IAAEE,GAAE,GAAEA,GAAE,CAAC;AAAE,UAAMC,KAAE,KAAK,cAAc,CAAC,EAAE,SAAS;AAAiB,WAAO,EAAEH,IAAEA,IAAE,KAAK,SAAS,GAAE,IAAI,EAAE,EAAC,GAAEA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBG,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,SAASD,IAAE;AAAC,QAAG,EAAEA,EAAC,KAAG,EAAE,KAAK,gBAAgB,KAAG,EAAE,KAAK,aAAa,KAAG,CAAC,EAAE,KAAK,cAAc,CAAC,CAAC,EAAE,QAAO;AAAK,UAAMC,KAAE,KAAK,cAAc,CAAC,EAAE,SAAS;AAAiB,WAAOD,KAAEA,GAAE,UAAU,GAAEA,KAAE,GAAEA,IAAEC,EAAC,EAAE,UAAS,EAAED,EAAC,IAAE,QAAME,GAAEJ,IAAEE,GAAE,GAAEA,GAAE,CAAC,GAAE,EAAEF,IAAEA,IAAE,KAAK,gBAAgB,GAAE,EAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,EAAE;AAAA,EAAC,iBAAiBE,IAAE;AAAC,UAAK,EAAC,eAAcC,IAAE,OAAMC,IAAE,QAAOL,GAAC,IAAE;AAAK,QAAG,EAAEI,EAAC,KAAG,EAAEC,KAAE,MAAI,EAAEL,KAAE,GAAG,QAAO;AAAK,UAAK,CAACE,IAAEM,IAAEK,IAAEN,EAAC,IAAEH;AAAE,QAAG,CAAC,EAAEF,EAAC,EAAE,QAAO;AAAK,UAAMO,KAAEP,GAAE,SAAS,kBAAiBY,KAAE,KAAK,qBAAqBN,IAAEC,EAAC,GAAEC,KAAE,KAAK,qBAAqBG,IAAEJ,EAAC,GAAEE,KAAE,KAAK,qBAAqBJ,IAAEE,EAAC;AAAE,QAAG,CAACK,GAAE,SAAO,CAACJ,GAAE,SAAO,CAACC,GAAE,MAAM,QAAO;AAAK,QAAG,CAAC,EAAEG,GAAE,YAAY,EAAE,QAAO;AAAK,MAAEX,EAAC,MAAIA,KAAEH,GAAE;AAAG,QAAIM,KAAE;AAAK,WAAOA,KAAE,EAAEI,GAAE,YAAY,KAAG,EAAEC,GAAE,YAAY,IAAE,EAAER,IAAED,IAAEY,GAAE,cAAaJ,GAAE,cAAaC,GAAE,YAAY,IAAE,EAAED,GAAE,YAAY,IAAE,EAAEP,IAAED,IAAEY,GAAE,cAAaJ,GAAE,YAAY,IAAE,EAAEP,IAAED,IAAEY,GAAE,YAAY,GAAER,GAAE,MAAO,CAAAH,OAAG,MAAIA,EAAE,IAAE,OAAKG;AAAA,EAAC;AAAA,EAAC,qBAAqBH,IAAEC,IAAE;AAAC,QAAG,CAAC,EAAED,EAAC,EAAE,QAAM,EAAC,OAAM,MAAG,cAAaA,GAAC;AAAE,UAAK,EAAC,aAAYE,IAAE,UAASL,GAAC,IAAEG,IAAE,EAAC,UAASY,IAAE,SAAQP,GAAC,IAAE,GAAER,IAAEI,EAAC;AAAE,WAAOI,KAAE,EAAC,OAAM,OAAG,cAAa,KAAI,IAAEA,MAAGO,KAAE,EAAC,OAAM,MAAG,cAAa,EAAC,aAAYV,IAAE,UAASU,GAAC,EAAC,KAAG,EAAE,UAAU,KAAK,aAAa,EAAE,KAAK,6DAA4D,EAAC,cAAa,MAAK,cAAaZ,IAAE,wBAAuBH,GAAE,kBAAiB,wBAAuBI,GAAC,CAAC,GAAE,EAAC,OAAM,OAAG,cAAa,KAAI;AAAA,EAAE;AAAC;AAAE,SAAS,EAAED,IAAE;AAAC,SAAO,EAAEA,EAAC,KAAG,EAAEA,GAAE,WAAW,KAAG,EAAEA,GAAE,QAAQ;AAAC;AAACH,GAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,GAAE,MAAK,EAAC,OAAM,EAAC,WAAU,OAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAEA,GAAE,CAAC,EAAE,eAAe,CAAC,GAAE,EAAE,WAAU,qBAAoB,IAAI,GAAEA,GAAE,CAACK,GAAE,eAAe,CAAC,GAAE,EAAE,WAAU,sBAAqB,IAAI,GAAEL,GAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,UAAS,IAAI,GAAEA,GAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAEA,GAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAEA,GAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAEA,GAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,IAAEA,GAAE,CAAC,EAAE,+CAA+C,CAAC,GAAE,CAAC;AAAE,IAAM,IAAEE,GAAE;AAAV,IAAYc,KAAEd,GAAE;AAAhB,IAAkB,IAAEA,GAAE;AAAtB,IAAwBe,KAAEf,GAAE;AAA5B,IAA8BgB,KAAEhB,GAAE;AAAlC,IAAoC,IAAEA,GAAE;AAAxC,IAA0C,IAAEA,GAAE;AAA9C,IAAgD,IAAEA,GAAE;AAApD,IAAsD,IAAE,KAAK,KAAG;AAAE,SAASiB,GAAEhB,IAAEC,IAAEC,IAAE;AAAC,EAAAA,GAAEF,IAAEE,GAAE,YAAY,GAAEA,GAAE,YAAY,CAAC,GAAEA,GAAED,IAAEC,GAAE,SAAS,GAAEA,GAAE,SAAS,CAAC;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAEC,IAAE;AAAC,SAAOc,GAAE,GAAED,IAAEd,EAAC,GAAEe,GAAEH,IAAE,GAAEX,EAAC,GAAE,EAAE,GAAEW,IAAE,GAAE,CAAC,GAAE,EAAEC,IAAE,GAAED,IAAE,CAAC,GAAE,EAAE,GAAE,GAAEE,IAAE,CAAC,CAAC,GAAE,EAAE,GAAEA,IAAE,GAAE,CAAC,CAAC,GAAEE,GAAEjB,IAAE,GAAEa,IAAE,GAAEC,IAAEC,IAAE,GAAE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAEf,IAAEC,IAAEC,IAAEL,IAAE;AAAC,SAAOmB,GAAE,GAAED,IAAEd,EAAC,GAAEe,GAAEH,IAAE,GAAEX,EAAC,GAAEc,GAAE,GAAE,GAAEnB,EAAC,GAAE,EAAEiB,IAAE,GAAED,IAAE,GAAE,GAAE,EAAEC,IAAE,GAAEA,IAAE,KAAK,EAAE,GAAE,EAAE,GAAEC,IAAE,GAAE,GAAE,GAAE,EAAE,GAAE,GAAE,GAAE,KAAK,EAAE,GAAEE,GAAEjB,IAAE,GAAEa,IAAE,GAAEC,IAAEC,IAAE,GAAE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAEf,IAAEC,IAAEC,IAAEL,IAAEE,IAAE;AAAC,SAAOiB,GAAE,GAAED,IAAEd,EAAC,GAAEe,GAAEH,IAAE,GAAEX,EAAC,GAAEc,GAAE,GAAE,GAAEnB,EAAC,GAAEmB,GAAEF,IAAE,GAAEf,EAAC,GAAEkB,GAAEjB,IAAE,GAAEa,IAAE,GAAEC,IAAEC,IAAE,GAAE,GAAE,CAAC;AAAC;AAAC,IAAM,IAAE,IAAI,MAAM,CAAC,EAAE,KAAK,CAAC;AAA3B,IAA6B,IAAE,IAAI,MAAM,CAAC,EAAE,KAAK,CAAC;AAAE,SAAS,EAAEf,IAAEC,IAAEC,IAAEL,IAAEE,IAAE;AAAC,SAAOC,GAAE,CAAC,IAAEC,GAAE,CAAC,GAAED,GAAE,CAAC,IAAEC,GAAE,CAAC,GAAED,GAAE,CAAC,IAAEE,GAAE,CAAC,GAAEF,GAAE,CAAC,IAAEE,GAAE,CAAC,GAAEF,GAAE,CAAC,IAAEH,GAAE,CAAC,GAAEG,GAAE,CAAC,IAAEH,GAAE,CAAC,GAAEG,GAAE,CAAC,IAAED,GAAE,CAAC,GAAEC,GAAE,CAAC,IAAED,GAAE,CAAC,GAAEC;AAAC;AAAC,SAASiB,GAAEjB,IAAEC,IAAEC,IAAEL,IAAEE,IAAEa,IAAEP,IAAEK,IAAEN,IAAE;AAAC,SAAOc,GAAElB,IAAE,EAAE,GAAEC,IAAEC,IAAEL,IAAEE,EAAC,GAAE,EAAE,GAAEa,IAAEP,IAAEK,IAAEN,EAAC,CAAC;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAEC,IAAEL,IAAE;AAAC,QAAME,KAAEG,GAAE,GAAEA,EAAC,GAAEU,KAAEV,GAAE,GAAE,CAAC,GAAEG,KAAEH,GAAED,IAAE,CAAC,GAAES,KAAER,GAAED,IAAEC,EAAC;AAAE,SAAO,EAAEH,IAAEA,IAAEC,EAAC,GAAE,EAAEY,IAAEA,IAAEZ,EAAC,GAAE,EAAEK,IAAEA,IAAEL,EAAC,GAAE,EAAEU,IAAEA,IAAEV,EAAC,GAAE,IAAImB,GAAE,EAAC,OAAM,CAAC,CAACpB,IAAEa,IAAEP,IAAEK,IAAEX,EAAC,CAAC,GAAE,kBAAiBF,GAAC,CAAC;AAAC;AAAC,IAAMuB,KAAE;;;ACApzL,IAAIC,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,aAAW,MAAK,KAAK,cAAY,MAAK,KAAK,UAAQ,MAAK,KAAK,WAAS,MAAK,KAAK,OAAK;AAAA,EAAS;AAAA,EAAC,IAAI,SAAQ;AAAC,QAAG,EAAC,SAAQA,IAAE,UAASC,IAAE,YAAWC,IAAE,aAAYC,GAAC,IAAE;AAAK,QAAG,EAAEH,EAAC,KAAG,EAAEC,EAAC,KAAG,EAAEC,EAAC,KAAG,EAAEC,EAAC,EAAE,QAAO;AAAK,UAAMC,KAAEJ,GAAE;AAAiB,WAAOC,KAAE,KAAK,cAAcA,IAAEG,EAAC,GAAEF,KAAE,KAAK,cAAcA,IAAEE,EAAC,GAAED,KAAE,KAAK,cAAcA,IAAEC,EAAC,GAAE,EAAEH,EAAC,KAAG,EAAEC,EAAC,KAAG,EAAEC,EAAC,IAAE,OAAK,IAAIE,GAAE,EAAC,OAAM,CAAC,CAAC,CAACH,GAAE,GAAEA,GAAE,CAAC,GAAE,CAACF,GAAE,GAAEA,GAAE,CAAC,GAAE,CAACC,GAAE,GAAEA,GAAE,CAAC,GAAE,CAACE,GAAE,GAAEA,GAAE,CAAC,GAAE,CAACD,GAAE,GAAEA,GAAE,CAAC,CAAC,CAAC,GAAE,kBAAiBE,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAOJ,IAAE;AAAC,UAAK,EAAC,SAAQC,GAAC,IAAE;AAAK,QAAG,EAAEA,EAAC,EAAE;AAAO,UAAMC,KAAED,GAAE;AAAiB,QAAGD,KAAE,KAAK,cAAcA,IAAEE,EAAC,GAAE,EAAEF,EAAC,EAAE;AAAO,UAAK,EAAC,OAAM,CAAC,CAACM,IAAEF,IAAEL,IAAEQ,EAAC,CAAC,EAAC,IAAEP;AAAE,SAAK,aAAW,IAAI,EAAE,EAAC,GAAEM,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBJ,GAAC,CAAC,GAAE,KAAK,UAAQ,IAAI,EAAE,EAAC,GAAEE,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBF,GAAC,CAAC,GAAE,KAAK,WAAS,IAAI,EAAE,EAAC,GAAEH,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBG,GAAC,CAAC,GAAE,KAAK,cAAY,IAAI,EAAE,EAAC,GAAEK,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBL,GAAC,CAAC;AAAA,EAAC;AAAC;AAAEM,GAAE,CAAC,EAAE,CAAC,GAAET,GAAE,WAAU,UAAS,IAAI,GAAES,GAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAET,GAAE,WAAU,cAAa,MAAM,GAAES,GAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAET,GAAE,WAAU,eAAc,MAAM,GAAES,GAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAET,GAAE,WAAU,WAAU,MAAM,GAAES,GAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAET,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAES,GAAE,CAAC,EAAE,yCAAyC,CAAC,GAAET,EAAC;AAAE,IAAMQ,KAAER;;;ACAxhC,IAAIU,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,SAAO,MAAK,KAAK,WAAS,GAAE,KAAK,OAAK;AAAA,EAAqB;AAAA,EAAC,IAAI,SAAQ;AAAC,QAAG,EAAE,KAAK,MAAM,EAAE,QAAO;AAAK,UAAK,EAAC,MAAKA,IAAE,MAAKC,IAAE,MAAKC,IAAE,MAAKC,IAAE,kBAAiBC,GAAC,IAAE,KAAK;AAAO,QAAIC;AAAE,QAAG,KAAK,UAAS;AAAC,YAAK,EAAC,GAAEC,IAAE,GAAEF,GAAC,IAAE,KAAK,OAAO,QAAOG,KAAEC,GAAEF,IAAEF,IAAE,KAAK,QAAQ;AAAE,MAAAC,KAAE,CAACE,GAAEP,IAAEC,EAAC,GAAEM,GAAEP,IAAEG,EAAC,GAAEI,GAAEL,IAAEC,EAAC,GAAEI,GAAEL,IAAED,EAAC,CAAC,GAAEI,GAAE,KAAKA,GAAE,CAAC,CAAC;AAAA,IAAC,MAAM,CAAAA,KAAE,CAAC,CAACL,IAAEC,EAAC,GAAE,CAACD,IAAEG,EAAC,GAAE,CAACD,IAAEC,EAAC,GAAE,CAACD,IAAED,EAAC,GAAE,CAACD,IAAEC,EAAC,CAAC;AAAE,WAAO,IAAIQ,GAAE,EAAC,OAAM,CAACJ,EAAC,GAAE,kBAAiBD,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAOJ,IAAE;AAAC,QAAG,EAAEA,EAAC,KAAG,EAAE,KAAK,MAAM,EAAE;AAAO,UAAME,KAAE,KAAK,OAAO;AAAiB,QAAGF,KAAE,KAAK,cAAcA,IAAEE,EAAC,GAAE,EAAEF,EAAC,KAAG,EAAEA,GAAE,MAAM,EAAE;AAAO,UAAK,EAAC,OAAM,CAAC,CAACG,IAAEC,IAAEG,EAAC,CAAC,GAAE,QAAO,EAAC,QAAO,EAAC,GAAEG,IAAE,GAAEX,GAAC,EAAC,EAAC,IAAEC,IAAEW,KAAE,EAAE,KAAK,KAAG,IAAE,KAAK,MAAMP,GAAE,CAAC,IAAED,GAAE,CAAC,GAAEC,GAAE,CAAC,IAAED,GAAE,CAAC,CAAC,CAAC,GAAES,KAAEJ,GAAEE,IAAEX,IAAE,CAACY,EAAC,GAAE,CAACE,IAAEC,EAAC,IAAEF,GAAET,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAE,CAACY,IAAEC,EAAC,IAAEJ,GAAEL,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE,SAAK,SAAO,IAAIU,GAAE,EAAC,MAAKJ,IAAE,MAAKC,IAAE,MAAKC,IAAE,MAAKC,IAAE,kBAAiBd,GAAC,CAAC,GAAE,KAAK,WAASS;AAAA,EAAC;AAAC;AAAE,SAASH,GAAER,IAAEC,IAAEK,IAAE;AAAC,QAAMJ,KAAEI,GAAEA,EAAC,GAAEH,KAAE,KAAK,IAAID,EAAC,GAAEG,KAAE,KAAK,IAAIH,EAAC;AAAE,SAAM,CAACI,IAAEJ,OAAI,CAACC,MAAGG,KAAEN,MAAGK,MAAGH,KAAED,MAAGD,IAAEG,MAAGD,KAAED,MAAGI,MAAGC,KAAEN,MAAGC,EAAC;AAAC;AAACA,GAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,UAAS,IAAI,GAAEE,GAAE,CAAC,EAAE,EAAC,MAAKgB,GAAC,CAAC,CAAC,GAAElB,GAAE,WAAU,UAAS,MAAM,GAAEE,GAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAEF,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAEE,GAAE,CAAC,EAAE,mDAAmD,CAAC,GAAEF,EAAC;AAAE,IAAMY,KAAEZ;;;ACA37B,IAAMmB,KAAE,EAAC,KAAI,QAAO,MAAK,GAAE,SAAQ,EAAC,kBAAiBC,IAAE,SAAQC,IAAE,uBAAsBC,GAAC,EAAC;AAAE,IAAIC,KAAE,cAAc,EAAE,EAAEC,EAAC,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,MAAK,KAAK,UAAQ;AAAA,EAAC;AAAA,EAAC,iBAAiBC,IAAE;AAAC,WAAOL,GAAE,SAASK,EAAC;AAAA,EAAC;AAAC;AAAEA,GAAE,CAAC,EAAE,EAAC,OAAMN,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEI,GAAE,WAAU,gBAAe,MAAM,GAAEE,GAAE,CAAC,EAAE,cAAc,CAAC,GAAEF,GAAE,WAAU,oBAAmB,IAAI,GAAEE,GAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,WAAU,MAAM,GAAEA,KAAEE,GAAE,CAAC,EAAE,sCAAsC,CAAC,GAAEF,EAAC;AAAE,IAAM,IAAEA;;;ACAtP,IAAIG,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,UAAQ,MAAK,KAAK,QAAM,MAAK,KAAK,OAAK,SAAQ,KAAK,QAAM;AAAA,EAAI;AAAA,EAAC,OAAM;AAAC,UAAMA,KAAE,KAAK;AAAM,QAAG,YAAU,OAAOA,IAAE;AAAC,YAAMC,KAAE,EAAED,IAAE,EAAC,cAAa,QAAO,CAAC,EAAE,KAAM,CAAC,EAAC,MAAKA,GAAC,MAAI;AAAC,aAAK,KAAK,WAAUA,EAAC;AAAA,MAAC,CAAE;AAAE,WAAK,oBAAoBC,EAAC;AAAA,IAAC,WAASD,cAAa,kBAAiB;AAAC,YAAME,KAAEF,GAAE,OAAO,EAAE,KAAM,MAAI;AAAC,aAAK,KAAK,WAAUA,EAAC;AAAA,MAAC,CAAE;AAAE,WAAK,oBAAoBE,EAAC;AAAA,IAAC,MAAM,CAAAF,KAAE,KAAK,KAAK,WAAUA,EAAC,IAAE,KAAK,oBAAoB,QAAQ,OAAO,IAAIG,GAAE,oCAAmC,sBAAqB,EAAC,OAAMH,GAAC,CAAC,CAAC,CAAC;AAAE,WAAO,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAEE,IAAED,IAAE;AAAC,WAAOG,GAAEF,GAAE,KAAID,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWD,IAAEE,IAAED,IAAEI,IAAE;AAAC,QAAG,EAAEL,EAAC,EAAE;AAAO,UAAMM,KAAED,MAAA,gBAAAA,GAAG,YAAWE,KAAEF,MAAA,gBAAAA,GAAG;AAAU,QAAG,CAACC,MAAG,CAACC,GAAE,QAAO,MAAK,YAAU,OAAOP,OAAIE,GAAED,EAAC,IAAEK,GAAEN,IAAEK,EAAC;AAAI,UAAMD,KAAE,YAAU,OAAOJ,MAAG,GAAEA,EAAC,KAAG,EAAEA,EAAC,IAAE,OAAKA;AAAE,QAAGI,IAAE;AAAC,UAAG,QAAMI,GAAEJ,EAAC,EAAE,QAAO,MAAKF,GAAED,EAAC,IAAEG;AAAG,YAAMJ,KAAEM,GAAEF,IAAE,EAAC,GAAGC,IAAE,wBAAuBA,MAAGA,GAAE,yBAAuB,EAAC,aAAYA,GAAE,uBAAuB,aAAY,UAAS,OAAM,IAAE,OAAM,GAAE,EAAE,EAAE;AAAE,UAAGC,MAAGN,MAAG,CAAC,EAAEA,EAAC,EAAE,QAAOO,GAAE,OAAO,KAAK,EAAC,UAASD,GAAE,iBAAiBN,EAAC,GAAE,UAAS,MAAE,CAAC,GAAE,MAAKE,GAAED,EAAC,IAAED;AAAA,IAAE;AAAC,IAAAE,GAAED,EAAC,IAAE,aAAYM,GAAE,kBAAkB,KAAKE,GAAET,EAAC,EAAE,KAAM,CAAAA,OAAG;AAAC,YAAMU,KAAEF,GAAER,IAAEM,EAAC;AAAE,MAAAJ,GAAED,EAAC,IAAES,GAAE,iBAAgBH,GAAE,MAAM,KAAK,EAAC,UAASG,IAAE,SAAQV,IAAE,UAAS,OAAG,QAAO,CAAAA,OAAG;AAAC,aAAK,QAAMA,GAAE;AAAA,MAAG,EAAC,CAAC;AAAA,IAAC,CAAE,CAAC;AAAA,EAAC;AAAC;AAAEA,GAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAED,GAAE,WAAU,WAAU,MAAM,GAAEC,GAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,OAAM,MAAK,OAAM,EAAC,CAAC,CAAC,GAAED,GAAE,WAAU,SAAQ,MAAM,GAAEC,GAAE,CAAC,EAAE,SAAQ,CAAC,KAAK,CAAC,CAAC,GAAED,GAAE,WAAU,aAAY,IAAI,GAAEC,GAAE,CAACC,GAAE,OAAO,CAAC,GAAEF,GAAE,WAAU,cAAa,IAAI,GAAEC,GAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,YAAW,EAAC,CAAC,CAAC,GAAED,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAEC,GAAE,CAAC,EAAE,kCAAkC,CAAC,GAAED,EAAC;AAAE,IAAMY,KAAEZ;AAAE,eAAeU,GAAET,IAAE;AAAC,MAAG,YAAU,OAAOA,IAAE;AAAC,QAAG,EAAEA,EAAC,GAAE;AAAC,YAAK,EAAC,MAAKC,GAAC,IAAE,MAAM,EAAED,IAAE,EAAC,cAAa,OAAM,CAAC;AAAE,aAAOC;AAAA,IAAC;AAAC,QAAG,GAAED,EAAC,EAAE,QAAO,GAAEA,EAAC;AAAE,WAAOS,IAAG,MAAM,EAAET,IAAE,EAAC,cAAa,QAAO,CAAC,GAAG,IAAI;AAAA,EAAC;AAAC,SAAO,IAAI,QAAS,CAAAE,OAAG,EAAEF,EAAC,EAAE,OAAOE,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEF,IAAE;AAAC,MAAGA,cAAa,kBAAkB,QAAOA;AAAE,QAAME,KAAEF,cAAa,mBAAiBA,GAAE,eAAaA,GAAE,OAAMC,KAAED,cAAa,mBAAiBA,GAAE,gBAAcA,GAAE,QAAOU,KAAE,SAAS,cAAc,QAAQ,GAAEP,KAAEO,GAAE,WAAW,IAAI;AAAE,SAAOA,GAAE,QAAMR,IAAEQ,GAAE,SAAOT,IAAED,cAAa,mBAAiBG,GAAE,UAAUH,IAAE,GAAE,GAAEA,GAAE,OAAMA,GAAE,MAAM,IAAEA,cAAa,aAAWG,GAAE,aAAaH,IAAE,GAAE,CAAC,GAAEU;AAAC;AAAC,SAASF,GAAER,IAAEE,IAAE;AAAC,QAAMD,KAAEW,GAAE,GAAEF,KAAE,GAAG,EAAE,SAAQT,EAAC,CAAC,IAAIC,GAAEF,EAAC,CAAC;AAAG,SAAOE,GAAE,iBAAiBQ,EAAC;AAAC;;;ACAjuF,IAAIG,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,UAAQ,MAAK,KAAK,OAAK;AAAA,EAAO;AAAA,EAAC,OAAM;AAAC,UAAMA,KAAE,KAAK;AAAM,QAAG,YAAU,OAAOA,IAAE;AAAC,YAAMC,KAAE,SAAS,cAAc,OAAO;AAAE,MAAAA,GAAE,MAAID,IAAEC,GAAE,cAAY,aAAYA,GAAE,WAAS,MAAGA,GAAE,QAAM,MAAGA,GAAE,OAAK,MAAG,KAAK,oBAAoB,KAAK,WAAWA,EAAC,EAAE,KAAM,MAAI;AAAC,aAAK,KAAK,WAAUA,EAAC;AAAA,MAAC,CAAE,CAAC;AAAA,IAAC,MAAM,CAAAD,cAAa,mBAAiB,KAAK,oBAAoB,KAAK,WAAWA,EAAC,EAAE,KAAM,MAAI;AAAC,WAAK,KAAK,WAAUA,EAAC;AAAA,IAAC,CAAE,CAAC,IAAE,KAAK,oBAAoB,QAAQ,OAAO,IAAIE,GAAE,oCAAmC,sBAAqB,EAAC,OAAMF,GAAC,CAAC,CAAC,CAAC;AAAE,WAAO,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,MAAMA,IAAE;AAAC,qBAAe,KAAK,aAAW,KAAK,KAAK,SAAQA,EAAC,IAAE,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,UAAS,sDAAsD;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,WAAO,IAAI,QAAS,CAACC,IAAEC,OAAI;AAAC,MAAAF,GAAE,YAAU,MAAI;AAAC,QAAAA,GAAE,YAAU,MAAKA,GAAE,KAAK,EAAE,KAAKC,IAAEC,EAAC;AAAA,MAAC,GAAE,gBAAcF,GAAE,gBAAcA,GAAE,cAAY,aAAYA,GAAE,MAAIA,GAAE;AAAA,IAAI,CAAE;AAAA,EAAC;AAAC;AAAEA,GAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAED,GAAE,WAAU,WAAU,MAAM,GAAEC,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,SAAQ,IAAI,GAAEA,KAAEC,GAAE,CAAC,EAAE,kCAAkC,CAAC,GAAED,EAAC;AAAE,IAAMI,KAAEJ;;;ACAV,IAAMK,KAAE,EAAC,KAAI,QAAO,iBAAgB,SAAQ,MAAK,GAAE,SAAQ,EAAC,OAAMC,IAAE,OAAMC,GAAC,EAAC;AAA5E,IAA8E,IAAE,EAAE,OAAOF,EAAC;AAAE,IAAIG,KAAE,cAAcC,GAAE,cAAc,EAAEF,GAAE,EAAE,eAAe,CAAC,CAAC,EAAE;AAAA,EAAC,YAAYG,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,SAAO,IAAIC,MAAE,KAAK,mBAAiB,oBAAI,OAAI,KAAK,mBAAiB,oBAAI,OAAI,KAAK,0BAAwB,CAAAD,OAAG;AAAC,iBAAUE,MAAKF,GAAE,SAAQ;AAAC,cAAMA,KAAE,KAAK,iBAAiB,IAAIE,EAAC;AAAE,aAAK,iBAAiB,OAAOA,EAAC,GAAE,KAAK,OAAO,OAAOF,EAAC,GAAE,KAAK,QAAQ,OAAOA,EAAC,GAAEA,GAAE,QAAQ,GAAE,KAAK,aAAa,YAAY;AAAA,MAAC;AAAC,YAAK,EAAC,kBAAiBG,GAAC,IAAE;AAAK,iBAAUD,MAAKF,GAAE,OAAM;AAAC,YAAG,KAAK,iBAAiB,IAAIE,EAAC,EAAE;AAAS,cAAMF,KAAE,IAAII,GAAE,EAAC,kBAAiBD,IAAE,SAAQD,GAAC,CAAC;AAAE,aAAK,iBAAiB,IAAIA,IAAEF,EAAC;AAAE,cAAMK,KAAEC,GAAG,MAAIN,GAAE,QAAS,MAAI,KAAK,uBAAuBA,IAAE,KAAE,CAAE;AAAE,aAAK,uBAAuBA,IAAE,IAAE,GAAE,KAAK,QAAQ,IAAIK,IAAEL,EAAC;AAAA,MAAC;AAAC,WAAK,iBAAiB,MAAM,GAAE,KAAK,SAAS,QAAS,CAACA,IAAEG,OAAI,KAAK,iBAAiB,IAAIH,IAAEG,EAAC,CAAE,GAAE,KAAK,KAAK,SAAS;AAAA,IAAC,GAAE,KAAK,WAAS,IAAI;AAAA,EAAC;AAAA,EAAC,MAAM,KAAKH,IAAE;AAAC,QAAG,EAAEA,EAAC,GAAE,CAAC,KAAK,kBAAiB;AAAC,YAAMA,KAAE,KAAK,SAAS,KAAM,CAAAA,OAAG,EAAEA,GAAE,YAAY,KAAG,EAAEA,GAAE,aAAa,MAAM,CAAE;AAAE,WAAK,KAAK,oBAAmBA,KAAE,EAAE,EAAEA,GAAE,YAAY,EAAE,MAAM,EAAE,mBAAiBO,GAAE,KAAK;AAAA,IAAC;AAAC,WAAO,KAAK,wBAAwB,EAAC,OAAM,KAAK,SAAS,OAAM,SAAQ,CAAC,EAAC,CAAC,GAAE,KAAK,QAAQ,IAAI,KAAK,SAAS,GAAG,UAAS,KAAK,uBAAuB,CAAC,GAAE;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,SAAK,OAAO,MAAM,GAAE,KAAK,iBAAiB,MAAM,GAAE,KAAK,iBAAiB,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,SAASP,IAAE;AAAC,SAAK,KAAK,YAAWQ,GAAER,IAAE,KAAK,KAAK,UAAU,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,QAAG,iBAAe,KAAK,WAAW,QAAO;AAAK,UAAMA,KAAE,KAAK,OAAO;AAAW,WAAO,EAAEA,EAAC,IAAE,OAAK,IAAIJ,GAAE,EAAC,MAAKI,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,kBAAiB,KAAK,iBAAgB,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,iBAAiBA,IAAE;AAAC,qBAAe,KAAK,aAAW,KAAK,KAAK,oBAAmBA,EAAC,IAAE,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,qBAAoB,gEAAgE;AAAA,EAAC;AAAA,EAAC,MAAM,cAAcA,IAAEG,IAAE;AAAC,UAAM,KAAK,KAAK,GAAE,MAAM,GAAEH,GAAE,kBAAiB,KAAK,kBAAiB,MAAKG,EAAC;AAAE,UAAMD,KAAE,EAAEF,GAAE,kBAAiB,KAAK,gBAAgB,IAAEA,KAAE,GAAEA,IAAE,KAAK,gBAAgB;AAAE,QAAG,CAACE,GAAE,QAAM,CAAC;AAAE,UAAMG,KAAEH,GAAE,UAAU,GAAED,KAAE,CAAC;AAAE,eAAUO,MAAKH,GAAE,MAAK,OAAO,gBAAgBI,GAAED,EAAC,GAAG,CAAC,EAAC,kBAAiBR,IAAE,SAAQG,GAAC,MAAI;AAAC,QAAEH,EAAC,KAAG,EAAEQ,IAAER,EAAC,KAAGC,GAAE,KAAKE,EAAC;AAAA,IAAC,CAAE;AAAE,WAAOF,GAAE,KAAM,CAACD,IAAEG,OAAI,KAAK,iBAAiB,IAAIH,EAAC,IAAE,KAAK,iBAAiB,IAAIG,EAAC,CAAE,GAAEF;AAAA,EAAC;AAAA,EAAC,uBAAuBD,IAAEG,IAAE;AAAC,UAAMD,KAAEF,GAAE,kBAAiBK,KAAE,KAAK,OAAO,IAAIL,EAAC,GAAEC,KAAE,EAAEC,EAAC;AAAE,SAAK,OAAO,OAAOF,EAAC,GAAEC,MAAG,KAAK,OAAO,IAAID,IAAEE,EAAC,GAAE,KAAK,aAAa,YAAY,GAAEC,OAAIE,OAAIJ,KAAE,KAAK,KAAK,SAAS,IAAE,KAAK,KAAK,UAAS,EAAC,SAAQD,GAAE,QAAO,CAAC;AAAA,EAAE;AAAC;AAAEA,GAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,YAAW,IAAI,GAAEE,GAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEF,GAAE,WAAU,cAAa,IAAI,GAAEE,GAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,oBAAmB,IAAI,GAAEA,KAAEE,GAAE,CAAC,EAAE,6CAA6C,CAAC,GAAEF,EAAC;AAAE,IAAMY,KAAEZ;;;ACA3kG,SAAS,EAAEa,IAAE;AAAC,SAAM,YAAU,OAAOA,MAAG,QAAMA,MAAG,UAASA;AAAC;AAAC,IAAIC,KAAE,cAAcC,GAAEC,GAAEC,GAAE,EAAEC,EAAC,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,YAAYL,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,kBAAgB,MAAK,KAAK,YAAU,MAAK,KAAK,uBAAqB,cAAa,KAAK,mBAAiB,MAAK,KAAK,OAAK,SAAQ,KAAK,SAAO,IAAIM;AAAA,EAAC;AAAA,EAAC,KAAKN,IAAE;AAAC,UAAMG,KAAE,KAAK;AAAO,QAAG,CAACA,GAAE,QAAO,KAAK,oBAAoB,QAAQ,OAAO,IAAII,GAAE,8BAA6B,mDAAmD,CAAC,CAAC,GAAE,QAAQ,QAAQ,IAAI;AAAE,UAAMA,KAAE,EAAEJ,EAAC,IAAE,IAAIG,GAAE,EAAC,UAAS,IAAI,EAAE,CAACH,EAAC,CAAC,EAAC,CAAC,IAAEA;AAAE,SAAK,KAAK,mBAAkBI,EAAC,GAAE,KAAK,qBAAmBA,GAAE,mBAAiB,KAAK;AAAkB,UAAMC,KAAED,GAAE,KAAKP,EAAC,EAAE,KAAM,MAAI;AAAC,WAAK,mBAAiBO,GAAE;AAAA,IAAgB,CAAE;AAAE,WAAO,KAAK,oBAAoBC,EAAC,GAAE,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,UAAS;AAJ1rD;AAI2rD,YAAE,KAAK,eAAe,MAAtB,mBAAyB,YAAU,OAAE,KAAK,MAAM,MAAb,mBAAgB;AAAA,EAAS;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,SAAO,KAAK,gBAAgB,aAAW;AAAA,EAAI;AAAA,EAAC,IAAI,OAAOR,IAAE;AAAC,qBAAe,KAAK,aAAW,KAAK,KAAK,UAASA,EAAC,IAAE,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,WAAU,qDAAqD;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,WAAOA,KAAE,MAAM,QAAQA,EAAC,KAAGA,cAAa,IAAE,IAAIM,GAAE,EAAC,UAASN,GAAC,CAAC,IAAEA,KAAE;AAAA,EAAI;AAAA,EAAC,WAAWA,IAAES,IAAEC,IAAE;AAAC,UAAMP,KAAE,YAAUM,GAAE,YAAU,IAAIE,OAAE,YAAUF,GAAE,YAAU,IAAIG,OAAE;AAAK,WAAOT,MAAA,gBAAAA,GAAG,KAAKM,IAAEC,KAAGP;AAAA,EAAC;AAAA,EAAC,YAAYH,IAAES,IAAEN,IAAEI,IAAE;AAJ/rE;AAIgsE,IAAAP,MAAG,EAAEA,EAAC,KAAG,YAAUA,GAAE,OAAKA,GAAE,MAAMS,IAAEF,EAAC,KAAEA,MAAA,gBAAAA,GAAG,eAAU,KAAAA,MAAA,gBAAAA,GAAG,aAAH,mBAAa,KAAK,IAAIA,GAAE,kCAAiC,kCAAkC;AAAA,EAAE;AAAC;AAAEP,GAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEC,GAAE,WAAU,mBAAkB,MAAM,GAAED,GAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAEC,GAAE,WAAU,aAAY,MAAM,GAAED,GAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEC,GAAE,WAAU,cAAa,IAAI,GAAED,GAAE,CAAC,EAAE,EAAC,MAAK,CAAC,YAAY,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,wBAAuB,MAAM,GAAED,GAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,YAAW,MAAM,GAAED,GAAE,CAAC,EAAE,EAAC,aAAY,MAAG,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,WAAU,MAAE,EAAC,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,UAAS,IAAI,GAAED,GAAE,CAACO,GAAE,QAAQ,CAAC,GAAEN,GAAE,WAAU,cAAa,IAAI,GAAED,GAAE,CAAC,EAAE,UAAS,CAAC,KAAK,CAAC,CAAC,GAAEC,GAAE,WAAU,cAAa,IAAI,GAAED,GAAE,CAACS,GAAE,QAAQ,CAAC,GAAER,GAAE,WAAU,eAAc,IAAI,GAAED,GAAE,CAAC,EAAE,CAAC,GAAEC,GAAE,WAAU,oBAAmB,MAAM,GAAED,GAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEC,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAED,GAAE,CAAC,EAAE,wBAAwB,CAAC,GAAEC,EAAC;AAAE,IAAMK,KAAEL;", "names": ["a", "e", "r", "s", "O", "e", "b", "n", "o", "t", "r", "f", "l", "s", "a", "u", "m", "h", "c", "p", "i", "A", "L", "U", "E", "V", "j", "v", "Y", "n", "t", "o", "r", "s", "i", "v", "p", "c", "e", "p", "t", "e", "o", "s", "n", "i", "r", "a", "m", "v", "c", "x", "u", "f", "h", "l", "y", "w", "f", "Y", "c", "x", "l", "m", "e", "I", "e", "r", "t", "s", "c", "a", "m", "p", "U", "E", "o", "w", "n", "n", "e", "o", "s", "a", "V", "w", "a", "b", "m", "e", "o", "s", "t", "u", "r", "l", "f", "n", "c", "v", "e", "j", "n", "t", "c", "b", "v", "s", "i", "r", "o", "w", "a"]}