<!-- 指令监控 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable class="card-table" :config="TableConfig">
      <template #operation="{ row }">
        <el-button type="primary" size="small" @click="showDetail(row)">
          详情
        </el-button>
        <el-button type="success" size="small" @click="exportDetail(row)">
          导出
        </el-button>
        <el-button type="warning" size="small" @click="showScheduleImpact(row)">
          调度影响
        </el-button>
      </template>
    </CardTable>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      title="调度详情"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="detail-container">
        <div class="detail-header">
          <el-button type="primary" @click="exportCurrentDetail">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>

        <div class="detail-content">
          <div class="detail-section">
            <h3>基本信息</h3>
            <el-descriptions :column="3" border>
              <el-descriptions-item label="指令类型">{{ currentDetail.typeName }}</el-descriptions-item>
              <el-descriptions-item label="指令状态">
                <el-tag :color="commandStatus[currentDetail.commandStatus]?.color">
                  {{ commandStatus[currentDetail.commandStatus]?.value }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="完成状态">
                <el-tag :color="finishedCondition[currentDetail.completeStatus]?.color">
                  {{ finishedCondition[currentDetail.completeStatus]?.value }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="发送站点">{{ currentDetail.sendDeptName }}</el-descriptions-item>
              <el-descriptions-item label="接收站点">{{ currentDetail.receiveDeptName }}</el-descriptions-item>
              <el-descriptions-item label="执行时间">{{ currentDetail.executionTime }}</el-descriptions-item>
              <el-descriptions-item label="发送人">{{ currentDetail.sendUserName }}</el-descriptions-item>
              <el-descriptions-item label="发送时间">{{ currentDetail.sendTime }}</el-descriptions-item>
              <el-descriptions-item label="接收人">{{ currentDetail.receiveUserName }}</el-descriptions-item>
              <el-descriptions-item label="接收时间">{{ currentDetail.receiveTime }}</el-descriptions-item>
              <el-descriptions-item label="回复时间">{{ currentDetail.replyTime }}</el-descriptions-item>
              <el-descriptions-item label="已完成">{{ currentDetail.completeStatus === 'COMPLETED' ? '是' : '否' }}</el-descriptions-item>
            </el-descriptions>
          </div>

          <div class="detail-section">
            <h3>指令内容</h3>
            <el-input
              v-model="currentDetail.sendContent"
              type="textarea"
              :rows="3"
              readonly
            />
          </div>

          <div class="detail-section" v-if="currentDetail.enablePumps || currentDetail.disablePumps">
            <h3>启用泵组</h3>
            <el-input
              v-model="currentDetail.enablePumpNames"
              readonly
              placeholder="无"
            />
          </div>

          <div class="detail-section" v-if="currentDetail.disablePumps">
            <h3>关闭泵组</h3>
            <el-input
              v-model="currentDetail.disablePumpNames"
              readonly
              placeholder="无"
            />
          </div>

          <div class="detail-section" v-if="currentDetail.replyContent">
            <h3>回复内容</h3>
            <el-input
              v-model="currentDetail.replyContent"
              type="textarea"
              :rows="3"
              readonly
            />
          </div>

          <div class="detail-section" v-if="currentDetail.rejectRemark">
            <h3>拒绝原因</h3>
            <el-input
              v-model="currentDetail.rejectRemark"
              type="textarea"
              :rows="2"
              readonly
            />
          </div>

          <div class="detail-section">
            <h3>指令备注</h3>
            <el-input
              v-model="currentDetail.remark"
              type="textarea"
              :rows="2"
              readonly
              placeholder="无备注"
            />
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 调度影响弹窗 -->
    <el-dialog
      v-model="scheduleImpactVisible"
      title="调度影响"
      width="70%"
      :close-on-click-modal="false"
    >
      <div class="schedule-impact-container">
        <div class="impact-section">
          <h3>累计流量</h3>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="累计流量">文本内容</el-descriptions-item>
            <el-descriptions-item label="累计电量">文本内容</el-descriptions-item>
            <el-descriptions-item label="运行时长">文本内容</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="impact-section">
          <h3>供水总量</h3>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="耗电总量">文本内容</el-descriptions-item>
            <el-descriptions-item label="平均流量">文本内容</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="impact-section">
          <h3>吨水耗电</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="吨水耗电">文本内容</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { Refresh, Download } from '@element-plus/icons-vue';
import { commandStatus, finishedCondition } from '../data';
import { ICONS } from '@/common/constans/common';
import { getWaterSupplyTree } from '@/api/company_org';
import {
  getOrderRecordList,
  getorderRecordType
} from '@/api/productionScheduling/schedulingInstructions';
import { traverse } from '@/utils/GlobalHelper';
import { ElMessage } from 'element-plus';
import * as XLSX from 'xlsx';

const refSearch = ref<ICardSearchIns>();

// 弹窗控制
const detailVisible = ref(false);
const scheduleImpactVisible = ref(false);
const currentDetail = ref<any>({});

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '发送时间', field: 'time', type: 'daterange' },
    {
      xl: 8,
      label: '计划状态',
      field: 'commandStatus',
      type: 'radio-button',
      options: [
        { label: '全部', value: '' },
        { label: '待发送', value: 'PENDING' },
        { label: '待接收', value: 'WAITING_RECEIVE' },
        { label: '待回复', value: 'WAITING_REPLY' },
        { label: '已回复', value: 'REPLIED' },
        { label: '已拒绝', value: 'DECLINED' }
      ]
    },
    { label: '发送人', field: 'sendUserId', type: 'department-user' },
    {
      type: 'select-tree',
      label: '接收部门',
      checkStrictly: true,
      options: computed(() => data.WaterSupplyTree) as any,
      field: 'receiveDeptId'
    },
    {
      type: 'select',
      label: '指令类型',
      field: 'type',
      options: computed(() => data.types) as any
    },
    { label: '指令查找', field: 'sendContent', type: 'input' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  columns: [
    { label: '指令类型', prop: 'typeName' },
    {
      label: '指令状态',
      prop: 'commandStatus',
      tag: true,
      tagColor: (row): string => commandStatus[row.commandStatus]?.color || '',
      formatter: (val) => commandStatus[val.commandStatus]?.value || ''
    },
    {
      label: '完成状态',
      prop: 'completeStatus',
      tag: true,
      tagColor: (row): string =>
        finishedCondition[row.completeStatus]?.color || '',
      formatter: (val) => finishedCondition[val.completeStatus]?.value || ''
    },
    { label: '发送站点', prop: 'sendDeptName' },
    { label: '接收站点', prop: 'receiveDeptName' },
    { label: '指令内容', prop: 'sendContent' },
    { label: '执行时间', prop: 'executionTime' },
    { label: '指令备注', prop: 'remark' },
    { label: '发送人', prop: 'sendUserName' },
    { label: '发送时间', prop: 'sendTime' },
    { label: '接收人', prop: 'receiveUserName' },
    { label: '接收时间', prop: 'receiveTime' },
    { label: '回复内容', prop: 'replyContent' },
    { label: '回复时间', prop: 'replyTime' },
    { label: '拒绝原因', prop: 'rejectRemark' },
    {
      label: '操作',
      prop: 'operation',
      width: '200px',
      fixed: 'right'
    } as any
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const data = reactive({
  // 部门
  WaterSupplyTree: [],
  getWaterSupplyTreeValue: () => {
    const depth = 2;
    getWaterSupplyTree(depth).then((res) => {
      data.WaterSupplyTree = traverse(res.data.data || []);
    });
  },
  // 指令类型
  types: [] as any[],
  getTypes: () => {
    const params = {
      size: -1,
      page: 1
    };
    getorderRecordType(params).then((res) => {
      data.types = traverse(res.data.data.data || [], 'children', {
        label: 'name',
        value: 'id'
      });
    });
  }
});

const refreshData = async () => {
  const params: any = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  };
  if (params.time && params.time?.length > 1) {
    params.sendTimeFrom = (refSearch.value?.queryParams as any).time[0] || '';
    params.sendTimeTo = (refSearch.value?.queryParams as any).time[1] || '';
  }
  delete params.time;
  getOrderRecordList(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

// 显示详情
const showDetail = (row: any) => {
  currentDetail.value = { ...row };
  detailVisible.value = true;
};

// 导出单条记录
const exportDetail = (row: any) => {
  exportToExcel([row], `指令详情_${row.typeName}_${new Date().getTime()}`);
};

// 导出当前详情
const exportCurrentDetail = () => {
  exportToExcel([currentDetail.value], `指令详情_${currentDetail.value.typeName}_${new Date().getTime()}`);
};

// 显示调度影响
const showScheduleImpact = (row: any) => {
  currentDetail.value = { ...row };
  scheduleImpactVisible.value = true;
};

// 导出Excel功能
const exportToExcel = (data: any[], filename: string) => {
  try {
    const exportData = data.map(item => ({
      '指令类型': item.typeName || '',
      '指令状态': commandStatus[item.commandStatus]?.value || '',
      '完成状态': finishedCondition[item.completeStatus]?.value || '',
      '发送站点': item.sendDeptName || '',
      '接收站点': item.receiveDeptName || '',
      '指令内容': item.sendContent || '',
      '启用泵组': item.enablePumpNames || '',
      '关闭泵组': item.disablePumpNames || '',
      '执行时间': item.executionTime || '',
      '指令备注': item.remark || '',
      '发送人': item.sendUserName || '',
      '发送时间': item.sendTime || '',
      '接收人': item.receiveUserName || '',
      '接收时间': item.receiveTime || '',
      '回复内容': item.replyContent || '',
      '回复时间': item.replyTime || '',
      '拒绝原因': item.rejectRemark || ''
    }));

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '指令详情');
    XLSX.writeFile(wb, `${filename}.xlsx`);
    ElMessage.success('导出成功');
  } catch (error) {
    ElMessage.error('导出失败');
    console.error('Export error:', error);
  }
};

onMounted(async () => {
  refreshData();
  data.getWaterSupplyTreeValue();
  data.getTypes();
});
</script>
