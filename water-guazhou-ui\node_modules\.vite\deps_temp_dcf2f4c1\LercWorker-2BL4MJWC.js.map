{"version": 3, "sources": ["../../@arcgis/core/layers/support/LercWorker.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clampFloat32 as e}from\"../../core/mathUtils.js\";var t={};t.defaultNoDataValue=e(-1/0),t.decode=function(s,r){var o=(r=r||{}).encodedMaskData||null===r.encodedMaskData,f=l(s,r.inputOffset||0,o),u=null!=r.noDataValue?e(r.noDataValue):t.defaultNoDataValue,m=i(f,r.pixelType||Float32Array,r.encodedMaskData,u,r.returnMask),d={width:f.width,height:f.height,pixelData:m.resultPixels,minValue:f.pixels.minValue,maxValue:f.pixels.maxValue,noDataValue:u};return m.resultMask&&(d.maskData=m.resultMask),r.returnEncodedMask&&f.mask&&(d.encodedMaskData=f.mask.bitset?f.mask.bitset:null),r.returnFileInfo&&(d.fileInfo=n(f,u),r.computeUsedBitDepths&&(d.fileInfo.bitDepths=a(f))),d};var i=function(e,t,i,n,a){var l,r,o=0,f=e.pixels.numBlocksX,u=e.pixels.numBlocksY,m=Math.floor(e.width/f),d=Math.floor(e.height/u),c=2*e.maxZError;i=i||(e.mask?e.mask.bitset:null),l=new t(e.width*e.height),a&&i&&(r=new Uint8Array(e.width*e.height));for(var g,h,k=new Float32Array(m*d),x=0;x<=u;x++){var p=x!==u?d:e.height%u;if(0!==p)for(var w=0;w<=f;w++){var y=w!==f?m:e.width%f;if(0!==y){var V,B,v,U,D=x*e.width*d+w*m,M=e.width-y,b=e.pixels.blocks[o];if(b.encoding<2?(0===b.encoding?V=b.rawData:(s(b.stuffedData,b.bitsPerPixel,b.numValidPixels,b.offset,c,k,e.pixels.maxValue),V=k),B=0):v=2===b.encoding?0:b.offset,i)for(h=0;h<p;h++){for(7&D&&(U=i[D>>3],U<<=7&D),g=0;g<y;g++)7&D||(U=i[D>>3]),128&U?(r&&(r[D]=1),l[D++]=b.encoding<2?V[B++]:v):(r&&(r[D]=0),l[D++]=n),U<<=1;D+=M}else if(b.encoding<2)for(h=0;h<p;h++){for(g=0;g<y;g++)l[D++]=V[B++];D+=M}else for(h=0;h<p;h++)if(l.fill)l.fill(v,D,D+y),D+=y+M;else{for(g=0;g<y;g++)l[D++]=v;D+=M}if(1===b.encoding&&B!==b.numValidPixels)throw\"Block and Mask do not match\";o++}}}return{resultPixels:l,resultMask:r}},n=function(e,t){return{fileIdentifierString:e.fileIdentifierString,fileVersion:e.fileVersion,imageType:e.imageType,height:e.height,width:e.width,maxZError:e.maxZError,eofOffset:e.eofOffset,mask:e.mask?{numBlocksX:e.mask.numBlocksX,numBlocksY:e.mask.numBlocksY,numBytes:e.mask.numBytes,maxValue:e.mask.maxValue}:null,pixels:{numBlocksX:e.pixels.numBlocksX,numBlocksY:e.pixels.numBlocksY,numBytes:e.pixels.numBytes,maxValue:e.pixels.maxValue,minValue:e.pixels.minValue,noDataValue:t}}},a=function(e){for(var t=e.pixels.numBlocksX*e.pixels.numBlocksY,i={},n=0;n<t;n++){var a=e.pixels.blocks[n];0===a.encoding?i.float32=!0:1===a.encoding?i[a.bitsPerPixel]=!0:i[0]=!0}return Object.keys(i)},l=function(e,t,i){var n={},a=new Uint8Array(e,t,10);if(n.fileIdentifierString=String.fromCharCode.apply(null,a),\"CntZImage\"!=n.fileIdentifierString.trim())throw\"Unexpected file identifier string: \"+n.fileIdentifierString;t+=10;var l=new DataView(e,t,24);if(n.fileVersion=l.getInt32(0,!0),n.imageType=l.getInt32(4,!0),n.height=l.getUint32(8,!0),n.width=l.getUint32(12,!0),n.maxZError=l.getFloat64(16,!0),t+=24,!i)if(l=new DataView(e,t,16),n.mask={},n.mask.numBlocksY=l.getUint32(0,!0),n.mask.numBlocksX=l.getUint32(4,!0),n.mask.numBytes=l.getUint32(8,!0),n.mask.maxValue=l.getFloat32(12,!0),t+=16,n.mask.numBytes>0){var s=new Uint8Array(Math.ceil(n.width*n.height/8)),r=(l=new DataView(e,t,n.mask.numBytes)).getInt16(0,!0),o=2,f=0;do{if(r>0)for(;r--;)s[f++]=l.getUint8(o++);else{var u=l.getUint8(o++);for(r=-r;r--;)s[f++]=u}r=l.getInt16(o,!0),o+=2}while(o<n.mask.numBytes);if(-32768!==r||f<s.length)throw\"Unexpected end of mask RLE encoding\";n.mask.bitset=s,t+=n.mask.numBytes}else if(0==(n.mask.numBytes|n.mask.numBlocksY|n.mask.maxValue)){s=new Uint8Array(Math.ceil(n.width*n.height/8));n.mask.bitset=s}l=new DataView(e,t,16),n.pixels={},n.pixels.numBlocksY=l.getUint32(0,!0),n.pixels.numBlocksX=l.getUint32(4,!0),n.pixels.numBytes=l.getUint32(8,!0),n.pixels.maxValue=l.getFloat32(12,!0),t+=16;var m=n.pixels.numBlocksX,d=n.pixels.numBlocksY,c=m+(n.width%m>0?1:0),g=d+(n.height%d>0?1:0);n.pixels.blocks=new Array(c*g);for(var h=1e9,k=0,x=0;x<g;x++)for(var p=0;p<c;p++){var w=0,y=e.byteLength-t;l=new DataView(e,t,Math.min(10,y));var V={};n.pixels.blocks[k++]=V;var B=l.getUint8(0);if(w++,V.encoding=63&B,V.encoding>3)throw\"Invalid block encoding (\"+V.encoding+\")\";if(2!==V.encoding){if(0!==B&&2!==B){if(B>>=6,V.offsetType=B,2===B)V.offset=l.getInt8(1),w++;else if(1===B)V.offset=l.getInt16(1,!0),w+=2;else{if(0!==B)throw\"Invalid block offset type\";V.offset=l.getFloat32(1,!0),w+=4}if(h=Math.min(V.offset,h),1===V.encoding)if(B=l.getUint8(w),w++,V.bitsPerPixel=63&B,B>>=6,V.numValidPixelsType=B,2===B)V.numValidPixels=l.getUint8(w),w++;else if(1===B)V.numValidPixels=l.getUint16(w,!0),w+=2;else{if(0!==B)throw\"Invalid valid pixel count type\";V.numValidPixels=l.getUint32(w,!0),w+=4}}var v;if(t+=w,3!=V.encoding)if(0===V.encoding){var U=(n.pixels.numBytes-1)/4;if(U!==Math.floor(U))throw\"uncompressed block has invalid length\";v=new ArrayBuffer(4*U),new Uint8Array(v).set(new Uint8Array(e,t,4*U));for(var D=new Float32Array(v),M=0;M<D.length;M++)h=Math.min(h,D[M]);V.rawData=D,t+=4*U}else if(1===V.encoding){var b=Math.ceil(V.numValidPixels*V.bitsPerPixel/8),I=Math.ceil(b/4);v=new ArrayBuffer(4*I),new Uint8Array(v).set(new Uint8Array(e,t,b)),V.stuffedData=new Uint32Array(v),t+=b}}else t++,h=Math.min(h,0)}return n.pixels.minValue=h,n.eofOffset=t,n},s=function(e,t,i,n,a,l,s){var r,o,f,u=(1<<t)-1,m=0,d=0,c=Math.ceil((s-n)/a),g=4*e.length-Math.ceil(t*i/8);for(e[e.length-1]<<=8*g,r=0;r<i;r++){if(0===d&&(f=e[m++],d=32),d>=t)o=f>>>d-t&u,d-=t;else{var h=t-d;o=(f&u)<<h&u,o+=(f=e[m++])>>>(d=32-h)}l[r]=o<c?n+o*a:s}return l};const r=t.decode;class o{_decode(e){const t=r(e.buffer,e.options);return Promise.resolve({result:t,transferList:[t.pixelData.buffer]})}}function f(){return new o}export{f as default};\n"], "mappings": ";;;;;;;;;;;AAIuD,IAAI,IAAE,CAAC;AAAE,EAAE,qBAAmB,EAAE,KAAG,CAAC,GAAE,EAAE,SAAO,SAASA,IAAEC,IAAE;AAAC,MAAIC,MAAGD,KAAEA,MAAG,CAAC,GAAG,mBAAiB,SAAOA,GAAE,iBAAgBE,KAAE,EAAEH,IAAEC,GAAE,eAAa,GAAEC,EAAC,GAAE,IAAE,QAAMD,GAAE,cAAY,EAAEA,GAAE,WAAW,IAAE,EAAE,oBAAmB,IAAE,EAAEE,IAAEF,GAAE,aAAW,cAAaA,GAAE,iBAAgB,GAAEA,GAAE,UAAU,GAAE,IAAE,EAAC,OAAME,GAAE,OAAM,QAAOA,GAAE,QAAO,WAAU,EAAE,cAAa,UAASA,GAAE,OAAO,UAAS,UAASA,GAAE,OAAO,UAAS,aAAY,EAAC;AAAE,SAAO,EAAE,eAAa,EAAE,WAAS,EAAE,aAAYF,GAAE,qBAAmBE,GAAE,SAAO,EAAE,kBAAgBA,GAAE,KAAK,SAAOA,GAAE,KAAK,SAAO,OAAMF,GAAE,mBAAiB,EAAE,WAAS,EAAEE,IAAE,CAAC,GAAEF,GAAE,yBAAuB,EAAE,SAAS,YAAU,EAAEE,EAAC,KAAI;AAAC;AAAE,IAAI,IAAE,SAAS,GAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEP,IAAEC,KAAE,GAAEC,KAAE,EAAE,OAAO,YAAW,IAAE,EAAE,OAAO,YAAW,IAAE,KAAK,MAAM,EAAE,QAAMA,EAAC,GAAE,IAAE,KAAK,MAAM,EAAE,SAAO,CAAC,GAAE,IAAE,IAAE,EAAE;AAAU,EAAAE,KAAEA,OAAI,EAAE,OAAK,EAAE,KAAK,SAAO,OAAMG,KAAE,IAAIJ,GAAE,EAAE,QAAM,EAAE,MAAM,GAAEG,MAAGF,OAAIJ,KAAE,IAAI,WAAW,EAAE,QAAM,EAAE,MAAM;AAAG,WAAQ,GAAE,GAAE,IAAE,IAAI,aAAa,IAAE,CAAC,GAAE,IAAE,GAAE,KAAG,GAAE,KAAI;AAAC,QAAI,IAAE,MAAI,IAAE,IAAE,EAAE,SAAO;AAAE,QAAG,MAAI,EAAE,UAAQ,IAAE,GAAE,KAAGE,IAAE,KAAI;AAAC,UAAI,IAAE,MAAIA,KAAE,IAAE,EAAE,QAAMA;AAAE,UAAG,MAAI,GAAE;AAAC,YAAI,GAAE,GAAE,GAAE,GAAE,IAAE,IAAE,EAAE,QAAM,IAAE,IAAE,GAAE,IAAE,EAAE,QAAM,GAAE,IAAE,EAAE,OAAO,OAAOD,EAAC;AAAE,YAAG,EAAE,WAAS,KAAG,MAAI,EAAE,WAAS,IAAE,EAAE,WAAS,EAAE,EAAE,aAAY,EAAE,cAAa,EAAE,gBAAe,EAAE,QAAO,GAAE,GAAE,EAAE,OAAO,QAAQ,GAAE,IAAE,IAAG,IAAE,KAAG,IAAE,MAAI,EAAE,WAAS,IAAE,EAAE,QAAOG,GAAE,MAAI,IAAE,GAAE,IAAE,GAAE,KAAI;AAAC,eAAI,IAAE,MAAI,IAAEA,GAAE,KAAG,CAAC,GAAE,MAAI,IAAE,IAAG,IAAE,GAAE,IAAE,GAAE,IAAI,KAAE,MAAI,IAAEA,GAAE,KAAG,CAAC,IAAG,MAAI,KAAGJ,OAAIA,GAAE,CAAC,IAAE,IAAGO,GAAE,GAAG,IAAE,EAAE,WAAS,IAAE,EAAE,GAAG,IAAE,MAAIP,OAAIA,GAAE,CAAC,IAAE,IAAGO,GAAE,GAAG,IAAEF,KAAG,MAAI;AAAE,eAAG;AAAA,QAAC;AAAA,iBAAS,EAAE,WAAS,EAAE,MAAI,IAAE,GAAE,IAAE,GAAE,KAAI;AAAC,eAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAAE,GAAE,GAAG,IAAE,EAAE,GAAG;AAAE,eAAG;AAAA,QAAC;AAAA,YAAM,MAAI,IAAE,GAAE,IAAE,GAAE,IAAI,KAAGA,GAAE,KAAK,CAAAA,GAAE,KAAK,GAAE,GAAE,IAAE,CAAC,GAAE,KAAG,IAAE;AAAA,aAAM;AAAC,eAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAAA,GAAE,GAAG,IAAE;AAAE,eAAG;AAAA,QAAC;AAAC,YAAG,MAAI,EAAE,YAAU,MAAI,EAAE,eAAe,OAAK;AAA8B,QAAAN;AAAA,MAAG;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM,EAAC,cAAaM,IAAE,YAAWP,GAAC;AAAC;AAA7hC,IAA+hC,IAAE,SAAS,GAAEG,IAAE;AAAC,SAAM,EAAC,sBAAqB,EAAE,sBAAqB,aAAY,EAAE,aAAY,WAAU,EAAE,WAAU,QAAO,EAAE,QAAO,OAAM,EAAE,OAAM,WAAU,EAAE,WAAU,WAAU,EAAE,WAAU,MAAK,EAAE,OAAK,EAAC,YAAW,EAAE,KAAK,YAAW,YAAW,EAAE,KAAK,YAAW,UAAS,EAAE,KAAK,UAAS,UAAS,EAAE,KAAK,SAAQ,IAAE,MAAK,QAAO,EAAC,YAAW,EAAE,OAAO,YAAW,YAAW,EAAE,OAAO,YAAW,UAAS,EAAE,OAAO,UAAS,UAAS,EAAE,OAAO,UAAS,UAAS,EAAE,OAAO,UAAS,aAAYA,GAAC,EAAC;AAAC;AAAjgD,IAAmgD,IAAE,SAAS,GAAE;AAAC,WAAQA,KAAE,EAAE,OAAO,aAAW,EAAE,OAAO,YAAWC,KAAE,CAAC,GAAEC,KAAE,GAAEA,KAAEF,IAAEE,MAAI;AAAC,QAAIC,KAAE,EAAE,OAAO,OAAOD,EAAC;AAAE,UAAIC,GAAE,WAASF,GAAE,UAAQ,OAAG,MAAIE,GAAE,WAASF,GAAEE,GAAE,YAAY,IAAE,OAAGF,GAAE,CAAC,IAAE;AAAA,EAAE;AAAC,SAAO,OAAO,KAAKA,EAAC;AAAC;AAA3sD,IAA6sD,IAAE,SAAS,GAAED,IAAEC,IAAE;AAAC,MAAIC,KAAE,CAAC,GAAEC,KAAE,IAAI,WAAW,GAAEH,IAAE,EAAE;AAAE,MAAGE,GAAE,uBAAqB,OAAO,aAAa,MAAM,MAAKC,EAAC,GAAE,eAAaD,GAAE,qBAAqB,KAAK,EAAE,OAAK,wCAAsCA,GAAE;AAAqB,EAAAF,MAAG;AAAG,MAAII,KAAE,IAAI,SAAS,GAAEJ,IAAE,EAAE;AAAE,MAAGE,GAAE,cAAYE,GAAE,SAAS,GAAE,IAAE,GAAEF,GAAE,YAAUE,GAAE,SAAS,GAAE,IAAE,GAAEF,GAAE,SAAOE,GAAE,UAAU,GAAE,IAAE,GAAEF,GAAE,QAAME,GAAE,UAAU,IAAG,IAAE,GAAEF,GAAE,YAAUE,GAAE,WAAW,IAAG,IAAE,GAAEJ,MAAG,IAAG,CAACC;AAAE,QAAGG,KAAE,IAAI,SAAS,GAAEJ,IAAE,EAAE,GAAEE,GAAE,OAAK,CAAC,GAAEA,GAAE,KAAK,aAAWE,GAAE,UAAU,GAAE,IAAE,GAAEF,GAAE,KAAK,aAAWE,GAAE,UAAU,GAAE,IAAE,GAAEF,GAAE,KAAK,WAASE,GAAE,UAAU,GAAE,IAAE,GAAEF,GAAE,KAAK,WAASE,GAAE,WAAW,IAAG,IAAE,GAAEJ,MAAG,IAAGE,GAAE,KAAK,WAAS,GAAE;AAAC,UAAIN,KAAE,IAAI,WAAW,KAAK,KAAKM,GAAE,QAAMA,GAAE,SAAO,CAAC,CAAC,GAAEL,MAAGO,KAAE,IAAI,SAAS,GAAEJ,IAAEE,GAAE,KAAK,QAAQ,GAAG,SAAS,GAAE,IAAE,GAAEJ,KAAE,GAAEC,KAAE;AAAE,SAAE;AAAC,YAAGF,KAAE,EAAE,QAAKA,OAAK,CAAAD,GAAEG,IAAG,IAAEK,GAAE,SAASN,IAAG;AAAA,aAAM;AAAC,cAAI,IAAEM,GAAE,SAASN,IAAG;AAAE,eAAID,KAAE,CAACA,IAAEA,OAAK,CAAAD,GAAEG,IAAG,IAAE;AAAA,QAAC;AAAC,QAAAF,KAAEO,GAAE,SAASN,IAAE,IAAE,GAAEA,MAAG;AAAA,MAAC,SAAOA,KAAEI,GAAE,KAAK;AAAU,UAAG,WAASL,MAAGE,KAAEH,GAAE,OAAO,OAAK;AAAsC,MAAAM,GAAE,KAAK,SAAON,IAAEI,MAAGE,GAAE,KAAK;AAAA,IAAQ,WAAS,MAAIA,GAAE,KAAK,WAASA,GAAE,KAAK,aAAWA,GAAE,KAAK,WAAU;AAAC,MAAAN,KAAE,IAAI,WAAW,KAAK,KAAKM,GAAE,QAAMA,GAAE,SAAO,CAAC,CAAC;AAAE,MAAAA,GAAE,KAAK,SAAON;AAAA,IAAC;AAAA;AAAC,EAAAQ,KAAE,IAAI,SAAS,GAAEJ,IAAE,EAAE,GAAEE,GAAE,SAAO,CAAC,GAAEA,GAAE,OAAO,aAAWE,GAAE,UAAU,GAAE,IAAE,GAAEF,GAAE,OAAO,aAAWE,GAAE,UAAU,GAAE,IAAE,GAAEF,GAAE,OAAO,WAASE,GAAE,UAAU,GAAE,IAAE,GAAEF,GAAE,OAAO,WAASE,GAAE,WAAW,IAAG,IAAE,GAAEJ,MAAG;AAAG,MAAI,IAAEE,GAAE,OAAO,YAAW,IAAEA,GAAE,OAAO,YAAW,IAAE,KAAGA,GAAE,QAAM,IAAE,IAAE,IAAE,IAAG,IAAE,KAAGA,GAAE,SAAO,IAAE,IAAE,IAAE;AAAG,EAAAA,GAAE,OAAO,SAAO,IAAI,MAAM,IAAE,CAAC;AAAE,WAAQ,IAAE,KAAI,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAI,UAAQ,IAAE,GAAE,IAAE,GAAE,KAAI;AAAC,QAAI,IAAE,GAAE,IAAE,EAAE,aAAWF;AAAE,IAAAI,KAAE,IAAI,SAAS,GAAEJ,IAAE,KAAK,IAAI,IAAG,CAAC,CAAC;AAAE,QAAI,IAAE,CAAC;AAAE,IAAAE,GAAE,OAAO,OAAO,GAAG,IAAE;AAAE,QAAI,IAAEE,GAAE,SAAS,CAAC;AAAE,QAAG,KAAI,EAAE,WAAS,KAAG,GAAE,EAAE,WAAS,EAAE,OAAK,6BAA2B,EAAE,WAAS;AAAI,QAAG,MAAI,EAAE,UAAS;AAAC,UAAG,MAAI,KAAG,MAAI,GAAE;AAAC,YAAG,MAAI,GAAE,EAAE,aAAW,GAAE,MAAI,EAAE,GAAE,SAAOA,GAAE,QAAQ,CAAC,GAAE;AAAA,iBAAY,MAAI,EAAE,GAAE,SAAOA,GAAE,SAAS,GAAE,IAAE,GAAE,KAAG;AAAA,aAAM;AAAC,cAAG,MAAI,EAAE,OAAK;AAA4B,YAAE,SAAOA,GAAE,WAAW,GAAE,IAAE,GAAE,KAAG;AAAA,QAAC;AAAC,YAAG,IAAE,KAAK,IAAI,EAAE,QAAO,CAAC,GAAE,MAAI,EAAE,SAAS,KAAG,IAAEA,GAAE,SAAS,CAAC,GAAE,KAAI,EAAE,eAAa,KAAG,GAAE,MAAI,GAAE,EAAE,qBAAmB,GAAE,MAAI,EAAE,GAAE,iBAAeA,GAAE,SAAS,CAAC,GAAE;AAAA,iBAAY,MAAI,EAAE,GAAE,iBAAeA,GAAE,UAAU,GAAE,IAAE,GAAE,KAAG;AAAA,aAAM;AAAC,cAAG,MAAI,EAAE,OAAK;AAAiC,YAAE,iBAAeA,GAAE,UAAU,GAAE,IAAE,GAAE,KAAG;AAAA,QAAC;AAAA,MAAC;AAAC,UAAI;AAAE,UAAGJ,MAAG,GAAE,KAAG,EAAE;AAAS,YAAG,MAAI,EAAE,UAAS;AAAC,cAAI,KAAGE,GAAE,OAAO,WAAS,KAAG;AAAE,cAAG,MAAI,KAAK,MAAM,CAAC,EAAE,OAAK;AAAwC,cAAE,IAAI,YAAY,IAAE,CAAC,GAAE,IAAI,WAAW,CAAC,EAAE,IAAI,IAAI,WAAW,GAAEF,IAAE,IAAE,CAAC,CAAC;AAAE,mBAAQ,IAAE,IAAI,aAAa,CAAC,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,KAAE,KAAK,IAAI,GAAE,EAAE,CAAC,CAAC;AAAE,YAAE,UAAQ,GAAEA,MAAG,IAAE;AAAA,QAAC,WAAS,MAAI,EAAE,UAAS;AAAC,cAAI,IAAE,KAAK,KAAK,EAAE,iBAAe,EAAE,eAAa,CAAC,GAAE,IAAE,KAAK,KAAK,IAAE,CAAC;AAAE,cAAE,IAAI,YAAY,IAAE,CAAC,GAAE,IAAI,WAAW,CAAC,EAAE,IAAI,IAAI,WAAW,GAAEA,IAAE,CAAC,CAAC,GAAE,EAAE,cAAY,IAAI,YAAY,CAAC,GAAEA,MAAG;AAAA,QAAC;AAAA;AAAA,IAAC,MAAM,CAAAA,MAAI,IAAE,KAAK,IAAI,GAAE,CAAC;AAAA,EAAC;AAAC,SAAOE,GAAE,OAAO,WAAS,GAAEA,GAAE,YAAUF,IAAEE;AAAC;AAA14I,IAA44I,IAAE,SAAS,GAAEF,IAAEC,IAAEC,IAAEC,IAAEC,IAAER,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAE,KAAG,KAAGC,MAAG,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,KAAK,MAAMJ,KAAEM,MAAGC,EAAC,GAAE,IAAE,IAAE,EAAE,SAAO,KAAK,KAAKH,KAAEC,KAAE,CAAC;AAAE,OAAI,EAAE,EAAE,SAAO,CAAC,MAAI,IAAE,GAAEJ,KAAE,GAAEA,KAAEI,IAAEJ,MAAI;AAAC,QAAG,MAAI,MAAIE,KAAE,EAAE,GAAG,GAAE,IAAE,KAAI,KAAGC,GAAE,CAAAF,KAAEC,OAAI,IAAEC,KAAE,GAAE,KAAGA;AAAA,SAAM;AAAC,UAAI,IAAEA,KAAE;AAAE,MAAAF,MAAGC,KAAE,MAAI,IAAE,GAAED,OAAIC,KAAE,EAAE,GAAG,QAAM,IAAE,KAAG;AAAA,IAAE;AAAC,IAAAK,GAAEP,EAAC,IAAEC,KAAE,IAAEI,KAAEJ,KAAEK,KAAEP;AAAA,EAAC;AAAC,SAAOQ;AAAC;AAAE,IAAM,IAAE,EAAE;AAAO,IAAM,IAAN,MAAO;AAAA,EAAC,QAAQ,GAAE;AAAC,UAAMJ,KAAE,EAAE,EAAE,QAAO,EAAE,OAAO;AAAE,WAAO,QAAQ,QAAQ,EAAC,QAAOA,IAAE,cAAa,CAACA,GAAE,UAAU,MAAM,EAAC,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,IAAG;AAAC,SAAO,IAAI;AAAC;", "names": ["s", "r", "o", "f", "t", "i", "n", "a", "l"]}