package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecord;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

import static org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordStatus.PENDING;
import static org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordCompleteStatus.PROCESSING;

@Getter
@Setter
public class OrderRecordSaveRequest extends SaveRequest<OrderRecord> {

    // 指令类型
    private String type;

    // 发送站点
    @NotNullOrEmpty
    private String sendDeptId;

    // 接收站点
    private String receiveDeptId;

    // 发送的指令内容
    private String sendContent;

    // 执行时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date executionTime;

    // 指令备注
    private String remark;

    // 启用泵组（泵站ID，多个用逗号分隔）
    private String enablePumps;

    // 关闭泵组（泵站ID，多个用逗号分隔）
    private String disablePumps;

    // 发送人
    // private String sendUserId;

    // 发送时间
    // private Date sendTime;

    @Override
    protected OrderRecord build() {
        OrderRecord entity = new OrderRecord();
        entity.setCreator(currentUserUUID());
        entity.setSendUserId(currentUserUUID());
        entity.setCreateTime(createTime());
        // entity.setSendTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected OrderRecord update(String id) {
        disallowUpdate();
        OrderRecord entity = new OrderRecord();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(OrderRecord entity) {
        entity.setType(type);
        entity.setCompleteStatus(PROCESSING);
        entity.setCommandStatus(PENDING);
        entity.setSendDeptId(sendDeptId);
        entity.setReceiveDeptId(receiveDeptId);
        entity.setSendContent(sendContent);
        entity.setExecutionTime(executionTime);
        entity.setRemark(remark);
        entity.setEnablePumps(enablePumps);
        entity.setDisablePumps(disablePumps);
    }
}