import {
  h,
  j as j2,
  u as u3
} from "./chunk-WE5BDY6M.js";
import {
  t as t4
} from "./chunk-XX6IKIRW.js";
import {
  o as o2
} from "./chunk-ZK5O2DLX.js";
import "./chunk-CCFNWAA2.js";
import "./chunk-3CFQMNJK.js";
import {
  e as e3,
  t as t2
} from "./chunk-A7PY25IH.js";
import "./chunk-FRO3RSRO.js";
import {
  u as u2
} from "./chunk-ST2RRB55.js";
import {
  n as n2
} from "./chunk-PCLDCFRI.js";
import {
  A,
  L,
  r as r4
} from "./chunk-SROTSYJS.js";
import {
  n as n5,
  r as r5
} from "./chunk-FOE4ICAJ.js";
import "./chunk-WGU7CS6R.js";
import {
  c as c4
} from "./chunk-FHKOFAQ2.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import {
  _n,
  rn,
  un
} from "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import {
  a as a2
} from "./chunk-Q4VCSCSY.js";
import {
  n as n4
} from "./chunk-LAEW33J6.js";
import {
  t as t3
} from "./chunk-NGPCXWDX.js";
import "./chunk-IXMTA2RC.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  b as b2
} from "./chunk-67MHB3E3.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-YJWWP4AU.js";
import "./chunk-EQYLMJEJ.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import {
  c as c2
} from "./chunk-I7WHRVHF.js";
import {
  i
} from "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import {
  m as m3
} from "./chunk-37DYRJVQ.js";
import {
  n as n3
} from "./chunk-CTPXU2ZH.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import {
  m
} from "./chunk-EDS4WCRT.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  U
} from "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import {
  l
} from "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import {
  v as v2
} from "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w2,
  x
} from "./chunk-XTO3XXZ3.js";
import {
  l as l2
} from "./chunk-QUHG7NMD.js";
import {
  R,
  U as U2,
  c as c3,
  m as m2
} from "./chunk-XVA5SA7P.js";
import {
  V,
  Y,
  Z,
  st,
  tt
} from "./chunk-U4SVMKOQ.js";
import {
  w
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  E,
  f2
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import {
  c
} from "./chunk-G5KX4JSG.js";
import "./chunk-74XRRMG4.js";
import {
  b
} from "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import {
  r as r3
} from "./chunk-EGHLQERQ.js";
import {
  s as s3
} from "./chunk-7SWS36OI.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  u2 as u
} from "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e as e2,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  f
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  e,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/GeoreferenceBase.js
var a3 = class extends l {
  projectOrWarn(e4, r6) {
    if (t(e4)) return e4;
    const { geometry: s4, pending: a5 } = un(e4, r6);
    return a5 ? null : a5 || s4 ? s4 : (s.getLogger(this.declaredClass).warn("geometry could not be projected to the spatial reference", { georeference: this, geometry: e4, sourceSpatialReference: e4.spatialReference, targetSpatialReference: r6 }), null);
  }
};
a3 = e2([a("esri.layers.support.GeoreferenceBase")], a3);
var p = a3;

// node_modules/@arcgis/core/layers/support/ControlPointsGeoreference.js
var O2 = e3();
var b3 = n5();
var k = class extends v {
  constructor() {
    super(...arguments), this.sourcePoint = null, this.mapPoint = null;
  }
};
e2([y()], k.prototype, "sourcePoint", void 0), e2([y({ type: w })], k.prototype, "mapPoint", void 0), k = e2([a("esri.layers.support.ControlPoint")], k);
var I = class extends u(p) {
  constructor(o3) {
    super(o3), this.controlPoints = null, this.height = 0, this.type = "control-points", this.width = 0;
  }
  readControlPoints(o3, t5) {
    const r6 = f2.fromJSON(t5.spatialReference), e4 = t2(...t5.coefficients, 1);
    return o3.map((o4) => (r4(b3, o4.x, o4.y), h(b3, b3, e4), { sourcePoint: o4, mapPoint: new w({ x: b3[0], y: b3[1], spatialReference: r6 }) }));
  }
  writeControlPoints(o3, t5, e4, l4) {
    if (t(this.transform)) {
      const o4 = new s2("web-document-write:invalid-georeference", "Invalid 'controlPoints', 'width', 'height' configuration.", { layer: l4 == null ? void 0 : l4.layer, georeference: this });
      (l4 == null ? void 0 : l4.messages) ? l4.messages.push(o4) : s.getLogger(this.declaredClass).error(o4.name, o4.message);
    } else r(o3) && M(o3[0]) && (t5.controlPoints = o3.map((o4) => {
      const t6 = e(o4.sourcePoint);
      return { x: t6.x, y: t6.y };
    }), t5.spatialReference = o3[0].mapPoint.spatialReference.toJSON(), t5.coefficients = this.transform.slice(0, 8));
  }
  get coords() {
    if (t(this.controlPoints)) return null;
    const o3 = this._updateTransform(O2);
    if (t(o3) || !M(this.controlPoints[0])) return null;
    const t5 = this.controlPoints[0].mapPoint.spatialReference;
    return X(o3, this.width, this.height, t5);
  }
  set coords(o3) {
    if (t(this.controlPoints) || !M(this.controlPoints[0])) return;
    const t5 = this.controlPoints[0].mapPoint.spatialReference;
    if (o3 = this.projectOrWarn(o3, t5), t(o3)) return;
    const { width: r6, height: e4 } = this, { rings: [[n8, s4, a5, u4]] } = o3, m5 = { sourcePoint: c(0, e4), mapPoint: new w({ x: n8[0], y: n8[1], spatialReference: t5 }) }, f4 = { sourcePoint: c(0, 0), mapPoint: new w({ x: s4[0], y: s4[1], spatialReference: t5 }) }, P = { sourcePoint: c(r6, 0), mapPoint: new w({ x: a5[0], y: a5[1], spatialReference: t5 }) }, h2 = { sourcePoint: c(r6, e4), mapPoint: new w({ x: u4[0], y: u4[1], spatialReference: t5 }) };
    M(m5) && M(f4) && M(P) && M(h2) && (F(O2, m5, f4, P, h2), this.controlPoints = e(this.controlPoints).map(({ sourcePoint: o4 }) => (r4(b3, o4.x, o4.y), h(b3, b3, O2), { sourcePoint: o4, mapPoint: new w({ x: b3[0], y: b3[1], spatialReference: t5 }) })));
  }
  get inverseTransform() {
    return t(this.transform) ? null : u2(e3(), this.transform);
  }
  get transform() {
    return this._updateTransform();
  }
  toMap(o3) {
    if (t(o3) || t(this.transform) || t(this.controlPoints) || !M(this.controlPoints[0])) return null;
    r4(b3, o3.x, o3.y);
    const t5 = this.controlPoints[0].mapPoint.spatialReference;
    return h(b3, b3, this.transform), new w({ x: b3[0], y: b3[1], spatialReference: t5 });
  }
  toSource(o3) {
    if (t(o3) || t(this.inverseTransform) || t(this.controlPoints) || !M(this.controlPoints[0])) return null;
    const t5 = this.controlPoints[0].mapPoint.spatialReference;
    return o3 = o3.normalize(), o3 = un(o3, t5).geometry, t(o3) ? null : (r4(b3, o3.x, o3.y), h(b3, b3, this.inverseTransform), c(b3[0], b3[1]));
  }
  _updateTransform(o3) {
    const { controlPoints: t5, width: r6, height: e4 } = this;
    if (t(t5) || !(r6 > 0) || !(e4 > 0)) return null;
    const [n8, s4, c6, l4] = t5;
    if (!M(n8)) return null;
    const a5 = n8.mapPoint.spatialReference, p3 = this._projectControlPoint(s4, a5), u4 = this._projectControlPoint(c6, a5), m5 = this._projectControlPoint(l4, a5);
    if (!p3.valid || !u4.valid || !m5.valid) return null;
    if (!M(p3.controlPoint)) return null;
    t(o3) && (o3 = e3());
    let f4 = null;
    return f4 = M(u4.controlPoint) && M(m5.controlPoint) ? F(o3, n8, p3.controlPoint, u4.controlPoint, m5.controlPoint) : M(u4.controlPoint) ? D(o3, n8, p3.controlPoint, u4.controlPoint) : W(o3, n8, p3.controlPoint), f4.every((o4) => 0 === o4) ? null : f4;
  }
  _projectControlPoint(o3, t5) {
    if (!M(o3)) return { valid: true, controlPoint: o3 };
    const { sourcePoint: r6, mapPoint: e4 } = o3, { geometry: i2, pending: s4 } = un(e4, t5);
    return s4 ? { valid: false, controlPoint: null } : s4 || i2 ? { valid: true, controlPoint: { sourcePoint: r6, mapPoint: i2 } } : (s.getLogger(this.declaredClass).warn("map point could not be projected to the spatial reference", { georeference: this, controlPoint: o3, sourceSpatialReference: e4.spatialReference, targetSpatialReference: t5 }), { valid: false, controlPoint: null });
  }
};
function M(o3) {
  return r(o3) && r(o3.sourcePoint) && r(o3.mapPoint);
}
e2([y({ type: [k], json: { write: { allowNull: false, isRequired: true } } })], I.prototype, "controlPoints", void 0), e2([o("controlPoints")], I.prototype, "readControlPoints", null), e2([r2("controlPoints")], I.prototype, "writeControlPoints", null), e2([y()], I.prototype, "coords", null), e2([y({ json: { write: true } })], I.prototype, "height", void 0), e2([y({ readOnly: true })], I.prototype, "inverseTransform", null), e2([y({ readOnly: true })], I.prototype, "transform", null), e2([y({ json: { write: true } })], I.prototype, "width", void 0), I = e2([a("esri.layers.support.ControlPointsGeoreference")], I);
var N = n5();
var A2 = n5();
var J = n5();
var L2 = n5();
var U3 = n5();
var G = n5();
var q = n5();
var z = n5();
var B = Math.PI / 2;
function E2(o3, t5, r6) {
  r4(o3, r6.sourcePoint.x, r6.sourcePoint.y), r4(t5, r6.mapPoint.x, r6.mapPoint.y);
}
function W(o3, t5, r6) {
  return E2(N, U3, t5), E2(A2, G, r6), L(J, A2, N, B), L(L2, N, A2, B), L(q, G, U3, -B), L(z, U3, G, -B), V2(o3, N, A2, J, L2, U3, G, q, z);
}
function D(o3, t5, r6, e4) {
  return E2(N, U3, t5), E2(A2, G, r6), E2(J, q, e4), A(L2, N, A2, 0.5), L(L2, J, L2, Math.PI), A(z, U3, G, 0.5), L(z, q, z, Math.PI), V2(o3, N, A2, J, L2, U3, G, q, z);
}
function F(o3, t5, r6, e4, n8) {
  return E2(N, U3, t5), E2(A2, G, r6), E2(J, q, e4), E2(L2, z, n8), V2(o3, N, A2, J, L2, U3, G, q, z);
}
var H = new Array(8).fill(0);
var K = new Array(8).fill(0);
function Q(o3, t5, r6, e4, n8) {
  return o3[0] = t5[0], o3[1] = t5[1], o3[2] = r6[0], o3[3] = r6[1], o3[4] = e4[0], o3[5] = e4[1], o3[6] = n8[0], o3[7] = n8[1], o3;
}
function V2(o3, t5, r6, e4, n8, i2, s4, c6, l4) {
  return j2(o3, Q(H, t5, r6, e4, n8), Q(K, i2, s4, c6, l4));
}
function X(o3, t5, r6, e4) {
  const n8 = r5(0, r6), i2 = r5(0, 0), s4 = r5(t5, 0), c6 = r5(t5, r6);
  return h(n8, n8, o3), h(i2, i2, o3), h(s4, s4, o3), h(c6, c6, o3), new v2({ rings: [[n8, i2, s4, c6, n8]], spatialReference: e4 });
}
var Y2 = I;

// node_modules/@arcgis/core/layers/support/CornersGeoreference.js
var n6 = class extends p {
  constructor(t5) {
    super(t5), this.bottomLeft = null, this.bottomRight = null, this.topLeft = null, this.topRight = null, this.type = "corners";
  }
  get coords() {
    let { topLeft: t5, topRight: o3, bottomLeft: r6, bottomRight: s4 } = this;
    if (t(t5) || t(o3) || t(r6) || t(s4)) return null;
    const i2 = t5.spatialReference;
    return o3 = this.projectOrWarn(o3, i2), r6 = this.projectOrWarn(r6, i2), s4 = this.projectOrWarn(s4, i2), t(o3) || t(r6) || t(s4) ? null : new v2({ rings: [[[r6.x, r6.y], [t5.x, t5.y], [o3.x, o3.y], [s4.x, s4.y], [r6.x, r6.y]]], spatialReference: i2 });
  }
  set coords(t5) {
    const { topLeft: o3 } = this;
    if (t(o3)) return;
    const r6 = o3.spatialReference;
    if (t5 = this.projectOrWarn(t5, r6), t(t5)) return;
    const { rings: [[p3, i2, n8, c6]] } = t5;
    this.bottomLeft = new w({ x: p3[0], y: p3[1], spatialReference: r6 }), this.topLeft = new w({ x: i2[0], y: i2[1], spatialReference: r6 }), this.topRight = new w({ x: n8[0], y: n8[1], spatialReference: r6 }), this.bottomRight = new w({ x: c6[0], y: c6[1], spatialReference: r6 });
  }
};
e2([y()], n6.prototype, "coords", null), e2([y({ type: w })], n6.prototype, "bottomLeft", void 0), e2([y({ type: w })], n6.prototype, "bottomRight", void 0), e2([y({ type: w })], n6.prototype, "topLeft", void 0), e2([y({ type: w })], n6.prototype, "topRight", void 0), n6 = e2([a("esri.layers.support.CornersGeoreference")], n6);
var c5 = n6;

// node_modules/@arcgis/core/layers/support/ExtentAndRotationGeoreference.js
var p2 = class extends p {
  constructor(t5) {
    super(t5), this.extent = null, this.rotation = 0, this.type = "extent-and-rotation";
  }
  get coords() {
    if (t(this.extent)) return null;
    const { xmin: t5, ymin: e4, xmax: o3, ymax: s4, spatialReference: n8 } = this.extent;
    let i2;
    if (this.rotation) {
      const { x: r6, y: n9 } = this.extent.center, a5 = m4(r6, n9, this.rotation);
      i2 = [a5(t5, e4), a5(t5, s4), a5(o3, s4), a5(o3, e4)], i2.push(i2[0]);
    } else i2 = [[t5, e4], [t5, s4], [o3, s4], [o3, e4], [t5, e4]];
    return new v2({ rings: [i2], spatialReference: n8 });
  }
  set coords(t5) {
    if (t(t5) || t(this.extent)) return;
    const o3 = this.extent.spatialReference;
    if (t5 = this.projectOrWarn(t5, o3), t(t5) || t(t5.extent)) return;
    const { rings: [[s4, n8, a5]], extent: { center: { x: c6, y: p3 } } } = t5, x3 = b(Math.PI / 2 - Math.atan2(n8[1] - s4[1], n8[0] - s4[0])), u4 = m4(c6, p3, -x3), [f4, h2] = u4(s4[0], s4[1]), [l4, y2] = u4(a5[0], a5[1]);
    this.extent = new w2({ xmin: f4, ymin: h2, xmax: l4, ymax: y2, spatialReference: o3 }), this.rotation = x3;
  }
};
function m4(t5, e4, r6) {
  const o3 = r3(r6), s4 = Math.cos(o3), i2 = Math.sin(o3);
  return (r7, o4) => [s4 * (r7 - t5) + i2 * (o4 - e4) + t5, s4 * (o4 - e4) - i2 * (r7 - t5) + e4];
}
e2([y()], p2.prototype, "coords", null), e2([y({ type: w2 })], p2.prototype, "extent", void 0), e2([y({ type: Number })], p2.prototype, "rotation", void 0), p2 = e2([a("esri.layers.support.ExtentAndRotationGeoreference")], p2);
var x2 = p2;

// node_modules/@arcgis/core/layers/support/MediaElementBase.js
var f3 = { key: "type", base: p, typeMap: { "control-points": Y2, corners: c5, "extent-and-rotation": x2 } };
var l3 = class extends i(u(m3)) {
  constructor() {
    super(...arguments), this.georeference = null, this.opacity = 1;
  }
  readGeoreference(e4) {
    return Y2.fromJSON(e4);
  }
};
e2([y({ types: f3, json: { write: true } })], l3.prototype, "georeference", void 0), e2([o("georeference")], l3.prototype, "readGeoreference", null), e2([y()], l3.prototype, "opacity", void 0), l3 = e2([a("esri.layers.support.MediaElementBase")], l3);
var d = l3;

// node_modules/@arcgis/core/layers/support/ImageElement.js
var I2 = class extends d {
  constructor(e4) {
    super(e4), this.content = null, this.image = null, this.type = "image", this.image = null;
  }
  load() {
    const e4 = this.image;
    if ("string" == typeof e4) {
      const r6 = U(e4, { responseType: "image" }).then(({ data: e5 }) => {
        this._set("content", e5);
      });
      this.addResolvingPromise(r6);
    } else if (e4 instanceof HTMLImageElement) {
      const t5 = e4.decode().then(() => {
        this._set("content", e4);
      });
      this.addResolvingPromise(t5);
    } else e4 ? this._set("content", e4) : this.addResolvingPromise(Promise.reject(new s2("image-element:invalid-image-type", "Invalid image type", { image: e4 })));
    return Promise.resolve(this);
  }
  readImage(e4, t5, r6) {
    return c3(t5.url, r6);
  }
  writeImage(e4, t5, r6, a5) {
    if (t(e4)) return;
    const m5 = a5 == null ? void 0 : a5.portalItem, p3 = a5 == null ? void 0 : a5.resources;
    if (!m5 || !p3) return void ("string" == typeof e4 && (t5[r6] = m2(e4, a5)));
    const c6 = "string" != typeof e4 || tt(e4) || Z(e4) ? null : e4;
    if (c6) {
      if (null == U2(c6)) return void (t5[r6] = c6);
      const e5 = m2(c6, { ...a5, verifyItemRelativeUrls: a5 && a5.verifyItemRelativeUrls ? { writtenUrls: a5.verifyItemRelativeUrls.writtenUrls, rootPath: void 0 } : void 0 }, R.NO);
      if (m5 && e5 && !Y(e5)) return p3.toKeep.push({ resource: m5.resourceFromPath(e5), compress: false }), void (t5[r6] = e5);
    }
    t5[r6] = "<pending>", p3.pendingOperations.push(E3(e4).then((e5) => {
      const o3 = U4(e5, m5);
      t5[r6] = o3.itemRelativeUrl, p3.toAdd.push({ resource: o3, content: e5, compress: false, finish: (e6) => {
        this.image = e6.url;
      } });
    }));
  }
};
e2([y({ readOnly: true })], I2.prototype, "content", void 0), e2([y({ json: { name: "url", type: String } })], I2.prototype, "image", void 0), e2([o("image", ["url"])], I2.prototype, "readImage", null), e2([r2("image")], I2.prototype, "writeImage", null), e2([y({ readOnly: true, json: { name: "mediaType" } })], I2.prototype, "type", void 0), I2 = e2([a("esri.layers.support.ImageElement")], I2);
var w3 = I2;
async function E3(e4) {
  if ("string" == typeof e4) {
    if (Z(e4)) {
      const { data: r6 } = await U(e4, { responseType: "blob" });
      return r6;
    }
    if (tt(e4)) return st(e4);
    return E3((await U(e4, { responseType: "image" })).data);
  }
  return new Promise((t5) => T(e4).toBlob(t5));
}
function T(e4) {
  if (e4 instanceof HTMLCanvasElement) return e4;
  const t5 = e4 instanceof HTMLImageElement ? e4.naturalWidth : e4.width, r6 = e4 instanceof HTMLImageElement ? e4.naturalHeight : e4.height, o3 = document.createElement("canvas"), s4 = o3.getContext("2d");
  return o3.width = t5, o3.height = r6, e4 instanceof HTMLImageElement ? s4.drawImage(e4, 0, 0, e4.width, e4.height) : e4 instanceof ImageData && s4.putImageData(e4, 0, 0), o3;
}
function U4(e4, t5) {
  const r6 = n2(), o3 = `${V("media", r6)}.${t4(e4)}`;
  return t5.resourceFromPath(o3);
}

// node_modules/@arcgis/core/layers/support/VideoElement.js
var n7 = class extends d {
  constructor(e4) {
    super(e4), this.content = null, this.type = "video";
  }
  load() {
    const e4 = this.video;
    if ("string" == typeof e4) {
      const o3 = document.createElement("video");
      o3.src = e4, o3.crossOrigin = "anonymous", o3.autoplay = true, o3.muted = true, o3.loop = true, this.addResolvingPromise(this._loadVideo(o3).then(() => {
        this._set("content", o3);
      }));
    } else e4 instanceof HTMLVideoElement ? this.addResolvingPromise(this._loadVideo(e4).then(() => {
      this._set("content", e4);
    })) : this.addResolvingPromise(Promise.reject(new s2("video-element:invalid-video-type", "Invalid video type", { video: e4 })));
    return Promise.resolve(this);
  }
  set video(e4) {
    "not-loaded" === this.loadStatus ? this._set("video", e4) : s.getLogger(this.declaredClass).error("#video", "video cannot be changed after the element is loaded.");
  }
  _loadVideo(e4) {
    return new Promise((o3, s4) => {
      e4.oncanplay = () => {
        e4.oncanplay = null, e4.play().then(o3, s4);
      }, "anonymous" !== e4.crossOrigin && (e4.crossOrigin = "anonymous", e4.src = e4.src);
    });
  }
};
e2([y({ readOnly: true })], n7.prototype, "content", void 0), e2([y()], n7.prototype, "video", null), n7 = e2([a("esri.layers.support.VideoElement")], n7);
var a4 = n7;

// node_modules/@arcgis/core/layers/support/LocalMediaElementSource.js
var V3 = { key: "type", defaultKeyValue: "image", base: d, typeMap: { image: w3, video: a4 } };
var C = j.ofType(V3);
var b4 = class extends m3.LoadableMixin(m(a2(n.EventedAccessor))) {
  constructor(e4) {
    super(e4), this._index = new o2(), this._elementViewsMap = /* @__PURE__ */ new Map(), this._elementsIndexes = /* @__PURE__ */ new Map(), this._elementsChangedHandler = (e5) => {
      for (const s4 of e5.removed) {
        const e6 = this._elementViewsMap.get(s4);
        this._elementViewsMap.delete(s4), this._index.delete(e6), this.handles.remove(e6), e6.destroy(), this.notifyChange("fullExtent");
      }
      const { spatialReference: t5 } = this;
      for (const s4 of e5.added) {
        if (this._elementViewsMap.get(s4)) continue;
        const e6 = new u3({ spatialReference: t5, element: s4 });
        this._elementViewsMap.set(s4, e6);
        const r6 = l2(() => e6.coords, () => this._updateIndexForElement(e6, false));
        this._updateIndexForElement(e6, true), this.handles.add(r6, e6);
      }
      this._elementsIndexes.clear(), this.elements.forEach((e6, t6) => this._elementsIndexes.set(e6, t6)), this.emit("refresh");
    }, this.elements = new C();
  }
  async load(e4) {
    if (f(e4), !this.spatialReference) {
      const e5 = this.elements.find((e6) => r(e6.georeference) && r(e6.georeference.coords));
      this._set("spatialReference", e5 ? e(e(e5.georeference).coords).spatialReference : f2.WGS84);
    }
    return this._elementsChangedHandler({ added: this.elements.items, removed: [] }), this.handles.add(this.elements.on("change", this._elementsChangedHandler)), this;
  }
  destroy() {
    this._index.clear(), this._elementViewsMap.clear(), this._elementsIndexes.clear();
  }
  set elements(e4) {
    this._set("elements", n3(e4, this._get("elements"), C));
  }
  get fullExtent() {
    if ("not-loaded" === this.loadStatus) return null;
    const e4 = this._index.fullBounds;
    return t(e4) ? null : new w2({ xmin: e4[0], ymin: e4[1], xmax: e4[2], ymax: e4[3], spatialReference: this.spatialReference });
  }
  set spatialReference(e4) {
    "not-loaded" === this.loadStatus ? this._set("spatialReference", e4) : s.getLogger(this.declaredClass).error("#spatialReference", "spatialReference cannot be changed after the source is loaded.");
  }
  async queryElements(e4, t5) {
    await this.load(), await _n(e4.spatialReference, this.spatialReference, null, t5);
    const s4 = E(e4.spatialReference, this.spatialReference) ? e4 : rn(e4, this.spatialReference);
    if (!s4) return [];
    const r6 = s4.normalize(), o3 = [];
    for (const n8 of r6) this._index.forEachInBounds(c2(n8), ({ normalizedCoords: e5, element: t6 }) => {
      r(e5) && x(n8, e5) && o3.push(t6);
    });
    return o3.sort((e5, t6) => this._elementsIndexes.get(e5) - this._elementsIndexes.get(t6)), o3;
  }
  _updateIndexForElement(e4, t5) {
    const s4 = e4.normalizedBounds, r6 = this._index.has(e4), o3 = r(s4);
    this._index.delete(e4), o3 && this._index.set(e4, s4), this.notifyChange("fullExtent"), t5 || (r6 !== o3 ? this.emit("refresh") : this.emit("change", { element: e4.element }));
  }
};
e2([y()], b4.prototype, "elements", null), e2([y({ readOnly: true })], b4.prototype, "fullExtent", null), e2([y()], b4.prototype, "spatialReference", null), b4 = e2([a("esri.layers.support.LocalMediaElementSource")], b4);
var v3 = b4;

// node_modules/@arcgis/core/layers/MediaLayer.js
function g(e4) {
  return "object" == typeof e4 && null != e4 && "type" in e4;
}
var j3 = class extends n4(t3(c4(O(b2)))) {
  constructor(e4) {
    super(e4), this.effectiveSource = null, this.copyright = null, this.operationalLayerType = "MediaLayer", this.spatialReference = null, this.type = "media", this.source = new v3();
  }
  load(e4) {
    const t5 = this.source;
    if (!t5) return this.addResolvingPromise(Promise.reject(new s2("media-layer:source-missing", "Set 'MediaLayer.source' before loading the layer."))), Promise.resolve(this);
    const s4 = g(t5) ? new v3({ elements: new j([t5]) }) : t5;
    this._set("effectiveSource", s4), this.spatialReference && (s4.spatialReference = this.spatialReference);
    const i2 = s4.load(e4).then(() => {
      this.spatialReference = s4.spatialReference;
    });
    return this.addResolvingPromise(i2), Promise.resolve(this);
  }
  destroy() {
    var _a, _b;
    (_a = e(this.effectiveSource)) == null ? void 0 : _a.destroy(), (_b = e(this.source)) == null ? void 0 : _b.destroy();
  }
  get fullExtent() {
    return this.loaded ? this.effectiveSource.fullExtent : null;
  }
  set source(e4) {
    "not-loaded" === this.loadStatus ? this._set("source", e4) : s.getLogger(this.declaredClass).error("#source", "source cannot be changed after the layer is loaded.");
  }
  castSource(e4) {
    return e4 ? Array.isArray(e4) || e4 instanceof j ? new v3({ elements: e4 }) : e4 : null;
  }
  readSource(e4, r6, o3) {
    const t5 = "image" === r6.mediaType ? new w3() : "video" === r6.mediaType ? new a4() : null;
    return t5 == null ? void 0 : t5.read(r6, o3), t5;
  }
  writeSource(e4, r6, t5, s4) {
    var _a;
    e4 && g(e4) && "image" === e4.type ? e4.write(r6, s4) : (s4 == null ? void 0 : s4.messages) && ((_a = s4 == null ? void 0 : s4.messages) == null ? void 0 : _a.push(new s2("media-layer:unsupported-source", "source must be an 'ImageElement'")));
  }
};
e2([y({ readOnly: true })], j3.prototype, "effectiveSource", void 0), e2([y({ type: String })], j3.prototype, "copyright", void 0), e2([y({ readOnly: true })], j3.prototype, "fullExtent", null), e2([y({ type: ["MediaLayer"] })], j3.prototype, "operationalLayerType", void 0), e2([y({ type: ["show", "hide"] })], j3.prototype, "listMode", void 0), e2([y({ nonNullable: true, json: { write: { enabled: true, allowNull: false } } })], j3.prototype, "source", null), e2([s3("source")], j3.prototype, "castSource", null), e2([o("source", ["url"])], j3.prototype, "readSource", null), e2([r2("source")], j3.prototype, "writeSource", null), e2([y()], j3.prototype, "spatialReference", void 0), e2([y({ readOnly: true })], j3.prototype, "type", void 0), j3 = e2([a("esri.layers.MediaLayer")], j3);
var v4 = j3;
export {
  v4 as default
};
//# sourceMappingURL=MediaLayer-TUT74NLX.js.map
