/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AdditionalFilters: typeof import('./src/components/additionalFilters/index.vue')['default']
    AddOrUpdateDialog: typeof import('./src/components/addOrUpdateDialog/index.vue')['default']
    ArcAnimatedLines: typeof import('./src/components/arcMap/widgets/ArcAnimatedLines.vue')['default']
    ArcAreaMeasure: typeof import('./src/components/arcMap/widgets/ArcAreaMeasure.vue')['default']
    ArcBasemapGallary: typeof import('./src/components/arcMap/widgets/ArcBasemapGallary.vue')['default']
    ArcCoordinate: typeof import('./src/components/arcMap/widgets/ArcCoordinate.vue')['default']
    ArcDraw: typeof import('./src/components/arcMap/widgets/ArcDraw.vue')['default']
    ArcHome: typeof import('./src/components/arcMap/widgets/ArcHome.vue')['default']
    ArcLayerList: typeof import('./src/components/arcMap/widgets/ArcLayerList.vue')['default']
    ArcLayout: typeof import('./src/components/arcMap/widgets/ArcLayout.vue')['default']
    ArcLegend: typeof import('./src/components/arcMap/widgets/ArcLegend.vue')['default']
    ArcLengthMeasure: typeof import('./src/components/arcMap/widgets/ArcLengthMeasure.vue')['default']
    ArcLocatePicker: typeof import('./src/components/arcMap/widgets/ArcLocatePicker.vue')['default']
    ArcMap: typeof import('./src/components/arcMap/arcMap.vue')['default']
    ArcOverview: typeof import('./src/components/arcMap/widgets/ArcOverview.vue')['default']
    ArcPipe: typeof import('./src/components/arcMap/widgets/ArcPipe.vue')['default']
    ArcPipeBar: typeof import('./src/components/arcMap/widgets/ArcPipeBar.vue')['default']
    ArcPipePick: typeof import('./src/components/arcMap/widgets/ArcPipePick.vue')['default']
    ArcPoi: typeof import('./src/components/arcMap/widgets/ArcPoi.vue')['default']
    ArcPop: typeof import('./src/components/arcMap/widgets/ArcPop.vue')['default']
    ArcPops: typeof import('./src/components/arcMap/widgets/ArcPops.vue')['default']
    ArcPrint: typeof import('./src/components/arcMap/widgets/ArcPrint.vue')['default']
    ArcScale: typeof import('./src/components/arcMap/widgets/ArcScale.vue')['default']
    ArcSqlGenerator: typeof import('./src/components/arcMap/widgets/ArcSqlGenerator.vue')['default']
    ArcStationWarning: typeof import('./src/components/arcMap/widgets/ArcStationWarning.vue')['default']
    ArcView: typeof import('./src/components/arcMap/ArcView.vue')['default']
    ArcWidgetButton: typeof import('./src/components/arcMap/arcWidgetButton.vue')['default']
    ArcZoom: typeof import('./src/components/arcMap/widgets/ArcZoom.vue')['default']
    ArcZoomTo: typeof import('./src/components/arcMap/widgets/ArcZoomTo.vue')['default']
    AreaMeasure: typeof import('./src/components/arcMap/AreaMeasure.vue')['default']
    AttrTable: typeof import('./src/components/Form/AttrTable.vue')['default']
    AttrTableCellContent: typeof import('./src/components/Form/AttrTableCellContent.vue')['default']
    AvatarUploader: typeof import('./src/components/Form/AvatarUploader.vue')['default']
    Breadcrumb: typeof import('./src/components/Breadcrumb/index.vue')['default']
    Button: typeof import('./src/components/Form/Button.vue')['default']
    Card: typeof import('./src/components/SLCard/Card.vue')['default']
    CardFineReport: typeof import('./src/components/cardFineReport/index.vue')['default']
    CardSearch: typeof import('./src/components/Form/CardSearch.vue')['default']
    CardTable: typeof import('./src/components/Form/CardTable.vue')['default']
    ChooseUserByRole: typeof import('./src/components/chooseUserByRole/index.vue')['default']
    ColorPicker: typeof import('./src/components/Form/ColorPicker.vue')['default']
    copy: typeof import('./src/components/Form/Search copy.vue')['default']
    DataPointFilter: typeof import('./src/components/additionalFilters/components/dataPointFilter.vue')['default']
    DepartmentUser: typeof import('./src/components/Form/DepartmentUser.vue')['default']
    Descriptions: typeof import('./src/components/Descriptions/index.vue')['default']
    DialogForm: typeof import('./src/components/Form/DialogForm.vue')['default']
    DPlayer: typeof import('./src/components/videoPlayer/DPlayer.vue')['default']
    DPlayer_v2: typeof import('./src/components/videoPlayer/DPlayer_v2.vue')['default']
    DraggableFormItem: typeof import('./src/components/Form/DraggableFormItem.vue')['default']
    DrawerBox: typeof import('./src/components/DrawerBox/DrawerBox.vue')['default']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCalendar: typeof import('element-plus/es')['ElCalendar']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCarousel: typeof import('element-plus/es')['ElCarousel']
    ElCarouselItem: typeof import('element-plus/es')['ElCarouselItem']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElCollapseTransition: typeof import('element-plus/es')['ElCollapseTransition']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    FieldSet: typeof import('./src/components/Form/FieldSet.vue')['default']
    FieldSetV1: typeof import('./src/components/Form/FieldSetV1.vue')['default']
    FolderBtn: typeof import('./src/components/DrawerBox/components/FolderBtn.vue')['default']
    Form: typeof import('./src/components/Form/Form.vue')['default']
    FormItem: typeof import('./src/components/Form/FormItem.vue')['default']
    FormMap: typeof import('./src/components/arcMap/FormMap.vue')['default']
    FormTable: typeof import('./src/components/Form/FormTable.vue')['default']
    FormTableColumn: typeof import('./src/components/Form/FormTableColumn.vue')['default']
    FormTableColumnFilter: typeof import('./src/components/Form/FormTableColumnFilter.vue')['default']
    FormTree: typeof import('./src/components/Form/FormTree.vue')['default']
    FormWangeditor: typeof import('./src/components/Form/FormWangeditor.vue')['default']
    FullScreen: typeof import('./src/components/FullScreen/index.vue')['default']
    Hamburger: typeof import('./src/components/Hamburger/index.vue')['default']
    IconSelector: typeof import('./src/components/Form/IconSelector.vue')['default']
    ImgViewer: typeof import('./src/components/Form/ImgViewer.vue')['default']
    ImportBtn: typeof import('./src/components/importBtn/index.vue')['default']
    ImportButton: typeof import('./src/components/Form/ImportButton.vue')['default']
    ImportButtonSuccess: typeof import('./src/components/Form/importButtonSuccess.vue')['default']
    ImportJsonButton: typeof import('./src/components/Form/ImportJsonButton.vue')['default']
    IndependentVideo: typeof import('./src/components/IndependentVideo/IndependentVideo.vue')['default']
    InlineForm: typeof import('./src/components/Form/InlineForm.vue')['default']
    Input: typeof import('./src/components/Form/Input.vue')['default']
    IntervalFilter: typeof import('./src/components/additionalFilters/components/intervalFilter.vue')['default']
    LinearBorder: typeof import('./src/components/LinearBorder/index.vue')['default']
    LinearText: typeof import('./src/components/LinearText/index.vue')['default']
    List: typeof import('./src/components/Form/List.vue')['default']
    LivePlayer: typeof import('./src/components/videoPlayer/LivePlayer.vue')['default']
    Map: typeof import('./src/components/arcMap/Map.vue')['default']
    MenuItem: typeof import('./src/components/MenuItem/MenuItem.vue')['default']
    NestedDraggableFormGroup: typeof import('./src/components/Form/NestedDraggableFormGroup.vue')['default']
    Orillusion3D: typeof import('./src/components/Orillusion3D/index.vue')['default']
    Pagination: typeof import('./src/components/Form/Pagination.vue')['default']
    Panel: typeof import('./src/components/Panel/Panel.vue')['default']
    PipeLength: typeof import('./src/components/arcMap/PipeLength.vue')['default']
    PoiSearch: typeof import('./src/components/arcMap/PoiSearch.vue')['default']
    PoiSearchV2: typeof import('./src/components/arcMap/PoiSearchV2.vue')['default']
    PopLayout: typeof import('./src/components/arcMap/PopLayout.vue')['default']
    PopWindow: typeof import('./src/components/arcMap/popWindow.vue')['default']
    Progress: typeof import('./src/components/Progress/index.vue')['default']
    RangeSelecter: typeof import('./src/components/Form/RangeSelecter.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Search: typeof import('./src/components/Form/Search.vue')['default']
    'Search copy': typeof import('./src/components/Form/Search copy.vue')['default']
    SearchMore: typeof import('./src/components/Form/SearchMore.vue')['default']
    SideDrawer: typeof import('./src/components/DrawerBox/components/SideDrawer.vue')['default']
    SimpleLines: typeof import('./src/components/Echarts/SimpleLines.vue')['default']
    SimplePie: typeof import('./src/components/Echarts/SimplePie.vue')['default']
    SimpleTree: typeof import('./src/components/SimpleTree/SimpleTree.vue')['default']
    SLAmap: typeof import('./src/components/SLAmap/index.vue')['default']
    SLButton: typeof import('./src/components/SLButton/index.vue')['default']
    SLCard: typeof import('./src/components/SLCard/index.vue')['default']
    SLCardSearch: typeof import('./src/components/SLCardSearch/index.vue')['default']
    SLCardTable: typeof import('./src/components/SLCardTable/index.vue')['default']
    SLCodeMirror: typeof import('./src/components/SLCodemirror/SLCodeMirror.vue')['default']
    SLDescriptionHeader: typeof import('./src/components/SLDescriptionHeader/index.vue')['default']
    SLDialog: typeof import('./src/components/SLDialog/index.vue')['default']
    SLDialogForm: typeof import('./src/components/SLDialogForm/index.vue')['default']
    SLDrawer: typeof import('./src/components/SLDrawer/index.vue')['default']
    SLFileUploader: typeof import('./src/components/SLFileUploader/index.vue')['default']
    SLFlexGroup: typeof import('./src/components/SLFlexGroup/index.vue')['default']
    SLForm: typeof import('./src/components/SLForm/index.vue')['default']
    SLFormItem: typeof import('./src/components/SLFormItem/index.vue')['default']
    SLMoreFilter: typeof import('./src/components/SLMoreFilter/index.vue')['default']
    SLPagination: typeof import('./src/components/SLPagination/index.vue')['default']
    SLSteps: typeof import('./src/components/SLSteps/index.vue')['default']
    SLTable: typeof import('./src/components/SLTable/index.vue')['default']
    SLTextItem: typeof import('./src/components/SLTextItem/index.vue')['default']
    SLTree: typeof import('./src/components/SLTree/index.vue')['default']
    SLUploader: typeof import('./src/components/SLUploader/index.vue')['default']
    Split: typeof import('./src/components/Split/Split.vue')['default']
    SvgIcon: typeof import('./src/components/SvgIcon/SvgIcon.vue')['default']
    TableColumn: typeof import('./src/components/SLTable/TableColumn.vue')['default']
    Tabs: typeof import('./src/components/Form/Tabs.vue')['default']
    Tag: typeof import('./src/components/Form/Tag.vue')['default']
    TagGroup: typeof import('./src/components/Form/TagGroup.vue')['default']
    TimeFilter: typeof import('./src/components/additionalFilters/components/timeFilter.vue')['default']
    TiniImageUploader: typeof import('./src/components/Form/TiniImageUploader.vue')['default']
    TreeBox: typeof import('./src/components/TreeBox/TreeBox.vue')['default']
    TreeList: typeof import('./src/components/treeList/index.vue')['default']
    UserSelector: typeof import('./src/components/Form/UserSelector.vue')['default']
    VideojsPlayer: typeof import('./src/components/videoPlayer/VideojsPlayer.vue')['default']
    VideoPlayer: typeof import('./src/components/videoPlayer/index.vue')['default']
    Videor: typeof import('./src/components/Form/Videor.vue')['default']
    VideoSelector: typeof import('./src/components/VideoSelector/VideoSelector.vue')['default']
    Voicer: typeof import('./src/components/Form/Voicer.vue')['default']
    VueScroll: typeof import('./src/components/VueScroll/index.vue')['default']
    WSPlayer: typeof import('./src/components/videoPlayer/WSPlayer.vue')['default']
    XGPlayer: typeof import('./src/components/videoPlayer/XGPlayer.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
