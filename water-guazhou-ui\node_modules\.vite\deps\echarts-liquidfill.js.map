{"version": 3, "sources": ["../../echarts-liquidfill/src/liquidFillSeries.js", "../../echarts-liquidfill/src/liquidFillShape.js", "../../echarts-liquidfill/src/liquidFillView.js"], "sourcesContent": ["import * as echarts from 'echarts/lib/echarts';\n\necharts.extendSeriesModel({\n\n    type: 'series.liquidFill',\n\n    optionUpdated: function () {\n        var option = this.option;\n        option.gridSize = Math.max(Math.floor(option.gridSize), 4);\n    },\n\n    getInitialData: function (option, ecModel) {\n        var dimensions = echarts.helper.createDimensions(option.data, {\n            coordDimensions: ['value']\n        });\n        var list = new echarts.List(dimensions, this);\n        list.initData(option.data);\n        return list;\n    },\n\n    defaultOption: {\n        color: ['#294D99', '#156ACF', '#1598ED', '#45BDFF'],\n        center: ['50%', '50%'],\n        radius: '50%',\n        amplitude: '8%',\n        waveLength: '80%',\n        phase: 'auto',\n        period: 'auto',\n        direction: 'right',\n        shape: 'circle',\n\n        waveAnimation: true,\n        animationEasing: 'linear',\n        animationEasingUpdate: 'linear',\n        animationDuration: 2000,\n        animationDurationUpdate: 1000,\n\n        outline: {\n            show: true,\n            borderDistance: 8,\n            itemStyle: {\n                color: 'none',\n                borderColor: '#294D99',\n                borderWidth: 8,\n                shadowBlur: 20,\n                shadowColor: 'rgba(0, 0, 0, 0.25)'\n            }\n        },\n\n        backgroundStyle: {\n            color: '#E3F7FF'\n        },\n\n        itemStyle: {\n            opacity: 0.95,\n            shadowBlur: 50,\n            shadowColor: 'rgba(0, 0, 0, 0.4)'\n        },\n\n        label: {\n            show: true,\n            color: '#294D99',\n            insideColor: '#fff',\n            fontSize: 50,\n            fontWeight: 'bold',\n\n            align: 'center',\n            baseline: 'middle',\n            position: 'inside'\n        },\n\n        emphasis: {\n            itemStyle: {\n                opacity: 0.8\n            }\n        }\n    }\n});\n", "import * as echarts from 'echarts/lib/echarts';\n\nexport default echarts.graphic.extendShape({\n    type: 'ec-liquid-fill',\n\n    shape: {\n        waveLength: 0,\n        radius: 0,\n        radiusY: 0,\n        cx: 0,\n        cy: 0,\n        waterLevel: 0,\n        amplitude: 0,\n        phase: 0,\n        inverse: false\n    },\n\n    buildPath: function (ctx, shape) {\n        if (shape.radiusY == null) {\n            shape.radiusY = shape.radius;\n        }\n\n        /**\n         * We define a sine wave having 4 waves, and make sure at least 8 curves\n         * is drawn. Otherwise, it may cause blank area for some waves when\n         * wave length is large enough.\n         */\n        var curves = Math.max(\n            Math.ceil(2 * shape.radius / shape.waveLength * 4) * 2,\n            8\n        );\n\n        // map phase to [-Math.PI * 2, 0]\n        while (shape.phase < -Math.PI * 2) {\n            shape.phase += Math.PI * 2;\n        }\n        while (shape.phase > 0) {\n            shape.phase -= Math.PI * 2;\n        }\n        var phase = shape.phase / Math.PI / 2 * shape.waveLength;\n\n        var left = shape.cx - shape.radius + phase - shape.radius * 2;\n\n        /**\n         * top-left corner as start point\n         *\n         * draws this point\n         *  |\n         * \\|/\n         *  ~~~~~~~~\n         *  |      |\n         *  +------+\n         */\n        ctx.moveTo(left, shape.waterLevel);\n\n        /**\n         * top wave\n         *\n         * ~~~~~~~~ <- draws this sine wave\n         * |      |\n         * +------+\n         */\n        var waveRight = 0;\n        for (var c = 0; c < curves; ++c) {\n            var stage = c % 4;\n            var pos = getWaterPositions(c * shape.waveLength / 4, stage,\n                shape.waveLength, shape.amplitude);\n            ctx.bezierCurveTo(pos[0][0] + left, -pos[0][1] + shape.waterLevel,\n                pos[1][0] + left, -pos[1][1] + shape.waterLevel,\n                pos[2][0] + left, -pos[2][1] + shape.waterLevel);\n\n            if (c === curves - 1) {\n                waveRight = pos[2][0];\n            }\n        }\n\n        if (shape.inverse) {\n            /**\n             * top-right corner\n             *                  2. draws this line\n             *                          |\n             *                       +------+\n             * 3. draws this line -> |      | <- 1. draws this line\n             *                       ~~~~~~~~\n             */\n            ctx.lineTo(waveRight + left, shape.cy - shape.radiusY);\n            ctx.lineTo(left, shape.cy - shape.radiusY);\n            ctx.lineTo(left, shape.waterLevel);\n        }\n        else {\n            /**\n             * top-right corner\n             *\n             *                       ~~~~~~~~\n             * 3. draws this line -> |      | <- 1. draws this line\n             *                       +------+\n             *                          ^\n             *                          |\n             *                  2. draws this line\n             */\n            ctx.lineTo(waveRight + left, shape.cy + shape.radiusY);\n            ctx.lineTo(left, shape.cy + shape.radiusY);\n            ctx.lineTo(left, shape.waterLevel);\n        }\n\n        ctx.closePath();\n    }\n});\n\n\n\n/**\n * Using Bezier curves to fit sine wave.\n * There is 4 control points for each curve of wave,\n * which is at 1/4 wave length of the sine wave.\n *\n * The control points for a wave from (a) to (d) are a-b-c-d:\n *          c *----* d\n *     b *\n *       |\n * ... a * ..................\n *\n * whose positions are a: (0, 0), b: (0.5, 0.5), c: (1, 1), d: (PI / 2, 1)\n *\n * @param {number} x          x position of the left-most point (a)\n * @param {number} stage      0-3, stating which part of the wave it is\n * @param {number} waveLength wave length of the sine wave\n * @param {number} amplitude  wave amplitude\n */\nfunction getWaterPositions(x, stage, waveLength, amplitude) {\n    if (stage === 0) {\n        return [\n            [x + 1 / 2 * waveLength / Math.PI / 2, amplitude / 2],\n            [x + 1 / 2 * waveLength / Math.PI,     amplitude],\n            [x + waveLength / 4,                   amplitude]\n        ];\n    }\n    else if (stage === 1) {\n        return [\n            [x + 1 / 2 * waveLength / Math.PI / 2 * (Math.PI - 2),\n            amplitude],\n            [x + 1 / 2 * waveLength / Math.PI / 2 * (Math.PI - 1),\n            amplitude / 2],\n            [x + waveLength / 4,                   0]\n        ]\n    }\n    else if (stage === 2) {\n        return [\n            [x + 1 / 2 * waveLength / Math.PI / 2, -amplitude / 2],\n            [x + 1 / 2 * waveLength / Math.PI,     -amplitude],\n            [x + waveLength / 4,                   -amplitude]\n        ]\n    }\n    else {\n        return [\n            [x + 1 / 2 * waveLength / Math.PI / 2 * (Math.PI - 2),\n            -amplitude],\n            [x + 1 / 2 * waveLength / Math.PI / 2 * (Math.PI - 1),\n            -amplitude / 2],\n            [x + waveLength / 4,                   0]\n        ]\n    }\n}\n", "import * as echarts from 'echarts/lib/echarts';\nimport * as numberUtil from 'echarts/lib/util/number';\nimport LiquidShape from './liquidFillShape';\n\nvar parsePercent = numberUtil.parsePercent;\n\nfunction isPathSymbol(symbol) {\n    return symbol && symbol.indexOf('path://') === 0\n}\n\necharts.extendChartView({\n\n    type: 'liquidFill',\n\n    render: function (seriesModel, ecModel, api) {\n        var self = this;\n        var group = this.group;\n        group.removeAll();\n\n        var data = seriesModel.getData();\n\n        var itemModel = data.getItemModel(0);\n\n        var center = itemModel.get('center');\n        var radius = itemModel.get('radius');\n\n        var width = api.getWidth();\n        var height = api.getHeight();\n        var size = Math.min(width, height);\n        // itemStyle\n        var outlineDistance = 0;\n        var outlineBorderWidth = 0;\n        var showOutline = seriesModel.get('outline.show');\n\n        if (showOutline) {\n            outlineDistance = seriesModel.get('outline.borderDistance');\n            outlineBorderWidth = parsePercent(\n                seriesModel.get('outline.itemStyle.borderWidth'), size\n            );\n        }\n\n        var cx = parsePercent(center[0], width);\n        var cy = parsePercent(center[1], height);\n\n        var outterRadius;\n        var innerRadius;\n        var paddingRadius;\n\n        var isFillContainer = false;\n\n        var symbol = seriesModel.get('shape');\n        if (symbol === 'container') {\n            // a shape that fully fills the container\n            isFillContainer = true;\n\n            outterRadius = [\n                width / 2,\n                height / 2\n            ];\n            innerRadius = [\n                outterRadius[0] - outlineBorderWidth / 2,\n                outterRadius[1] - outlineBorderWidth / 2\n            ];\n            paddingRadius = [\n                parsePercent(outlineDistance, width),\n                parsePercent(outlineDistance, height)\n            ];\n\n            radius = [\n                Math.max(innerRadius[0] - paddingRadius[0], 0),\n                Math.max(innerRadius[1] - paddingRadius[1], 0)\n            ];\n        }\n        else {\n            outterRadius = parsePercent(radius, size) / 2;\n            innerRadius = outterRadius - outlineBorderWidth / 2;\n            paddingRadius = parsePercent(outlineDistance, size);\n\n            radius = Math.max(innerRadius - paddingRadius, 0);\n        }\n\n        if (showOutline) {\n            var outline = getOutline();\n            outline.style.lineWidth = outlineBorderWidth;\n            group.add(getOutline());\n        }\n\n        var left = isFillContainer ? 0 : cx - radius;\n        var top = isFillContainer ? 0 : cy - radius;\n\n        var wavePath = null;\n\n        group.add(getBackground());\n\n        // each data item for a wave\n        var oldData = this._data;\n        var waves = [];\n        data.diff(oldData)\n            .add(function (idx) {\n                var wave = getWave(idx, false);\n\n                var waterLevel = wave.shape.waterLevel;\n                wave.shape.waterLevel = isFillContainer ? height / 2 : radius;\n                echarts.graphic.initProps(wave, {\n                    shape: {\n                        waterLevel: waterLevel\n                    }\n                }, seriesModel);\n\n                wave.z2 = 2;\n                setWaveAnimation(idx, wave, null);\n\n                group.add(wave);\n                data.setItemGraphicEl(idx, wave);\n                waves.push(wave);\n            })\n            .update(function (newIdx, oldIdx) {\n                var waveElement = oldData.getItemGraphicEl(oldIdx);\n\n                // new wave is used to calculate position, but not added\n                var newWave = getWave(newIdx, false, waveElement);\n\n                // changes with animation\n                var shape = {};\n                var shapeAttrs = ['amplitude', 'cx', 'cy', 'phase', 'radius', 'radiusY', 'waterLevel', 'waveLength'];\n                for (var i = 0; i < shapeAttrs.length; ++i) {\n                    var attr = shapeAttrs[i];\n                    if (newWave.shape.hasOwnProperty(attr)) {\n                        shape[attr] = newWave.shape[attr];\n                    }\n                }\n\n                var style = {};\n                var styleAttrs = ['fill', 'opacity', 'shadowBlur', 'shadowColor'];\n                for (var i = 0; i < styleAttrs.length; ++i) {\n                    var attr = styleAttrs[i];\n                    if (newWave.style.hasOwnProperty(attr)) {\n                        style[attr] = newWave.style[attr];\n                    }\n                }\n\n                if (isFillContainer) {\n                    shape.radiusY = height / 2;\n                }\n\n                // changes with animation\n                echarts.graphic.updateProps(waveElement, {\n                    shape: shape,\n                    x: newWave.x,\n                    y: newWave.y\n                }, seriesModel);\n\n                if (seriesModel.isUniversalTransitionEnabled && seriesModel.isUniversalTransitionEnabled()) {\n                    echarts.graphic.updateProps(waveElement, {\n                        style: style\n                    }, seriesModel);\n                }\n                else {\n                    waveElement.useStyle(style);\n                }\n\n                // instant changes\n                var oldWaveClipPath = waveElement.getClipPath();\n                var newWaveClipPath = newWave.getClipPath();\n\n                waveElement.setClipPath(newWave.getClipPath());\n                waveElement.shape.inverse = newWave.inverse;\n\n                if (oldWaveClipPath && newWaveClipPath\n                    && self._shape === symbol\n                    // TODO use zrender morphing to apply complex symbol animation.\n                    && !isPathSymbol(symbol)\n                ) {\n                    // Can be animated.\n                    echarts.graphic.updateProps(newWaveClipPath, {\n                        shape: oldWaveClipPath.shape\n                    }, seriesModel, { isFrom: true });\n                }\n\n                setWaveAnimation(newIdx, waveElement, waveElement);\n                group.add(waveElement);\n                data.setItemGraphicEl(newIdx, waveElement);\n                waves.push(waveElement);\n            })\n            .remove(function (idx) {\n                var wave = oldData.getItemGraphicEl(idx);\n                group.remove(wave);\n            })\n            .execute();\n\n        if (itemModel.get('label.show')) {\n            group.add(getText(waves));\n        }\n\n        this._shape = symbol;\n        this._data = data;\n\n        /**\n         * Get path for outline, background and clipping\n         *\n         * @param {number} r outter radius of shape\n         * @param {boolean|undefined} isForClipping if the shape is used\n         *                                          for clipping\n         */\n        function getPath(r, isForClipping) {\n            if (symbol) {\n                // customed symbol path\n                if (isPathSymbol(symbol)) {\n                    var path = echarts.graphic.makePath(symbol.slice(7), {});\n                    var bouding = path.getBoundingRect();\n                    var w = bouding.width;\n                    var h = bouding.height;\n                    if (w > h) {\n                        h = r * 2 / w * h;\n                        w = r * 2;\n                    }\n                    else {\n                        w = r * 2 / h * w;\n                        h = r * 2;\n                    }\n\n                    var left = isForClipping ? 0 : cx - w / 2;\n                    var top = isForClipping ? 0 : cy - h / 2;\n                    path = echarts.graphic.makePath(\n                        symbol.slice(7),\n                        {},\n                        new echarts.graphic.BoundingRect(left, top, w, h)\n                    );\n                    if (isForClipping) {\n                        path.x = -w / 2;\n                        path.y = -h / 2;\n                    }\n                    return path;\n                }\n                else if (isFillContainer) {\n                    // fully fill the container\n                    var x = isForClipping ? -r[0] : cx - r[0];\n                    var y = isForClipping ? -r[1] : cy - r[1];\n                    return echarts.helper.createSymbol(\n                        'rect', x, y, r[0] * 2, r[1] * 2\n                    );\n                }\n                else {\n                    var x = isForClipping ? -r : cx - r;\n                    var y = isForClipping ? -r : cy - r;\n                    if (symbol === 'pin') {\n                        y += r;\n                    }\n                    else if (symbol === 'arrow') {\n                        y -= r;\n                    }\n                    return echarts.helper.createSymbol(symbol, x, y, r * 2, r * 2);\n                }\n            }\n\n            return new echarts.graphic.Circle({\n                shape: {\n                    cx: isForClipping ? 0 : cx,\n                    cy: isForClipping ? 0 : cy,\n                    r: r\n                }\n            });\n        }\n        /**\n         * Create outline\n         */\n        function getOutline() {\n            var outlinePath = getPath(outterRadius);\n            outlinePath.style.fill = null;\n\n            outlinePath.setStyle(seriesModel.getModel('outline.itemStyle')\n                .getItemStyle());\n\n            return outlinePath;\n        }\n\n        /**\n         * Create background\n         */\n        function getBackground() {\n            // Seperate stroke and fill, so we can use stroke to cover the alias of clipping.\n            var strokePath = getPath(radius);\n            strokePath.setStyle(seriesModel.getModel('backgroundStyle')\n                .getItemStyle());\n            strokePath.style.fill = null;\n\n            // Stroke is front of wave\n            strokePath.z2 = 5;\n\n            var fillPath = getPath(radius);\n            fillPath.setStyle(seriesModel.getModel('backgroundStyle')\n                .getItemStyle());\n            fillPath.style.stroke = null;\n\n            var group = new echarts.graphic.Group();\n            group.add(strokePath);\n            group.add(fillPath);\n\n            return group;\n        }\n\n        /**\n         * wave shape\n         */\n        function getWave(idx, isInverse, oldWave) {\n            var radiusX = isFillContainer ? radius[0] : radius;\n            var radiusY = isFillContainer ? height / 2 : radius;\n\n            var itemModel = data.getItemModel(idx);\n            var itemStyleModel = itemModel.getModel('itemStyle');\n            var phase = itemModel.get('phase');\n            var amplitude = parsePercent(itemModel.get('amplitude'),\n                radiusY * 2);\n            var waveLength = parsePercent(itemModel.get('waveLength'),\n                radiusX * 2);\n\n            var value = data.get('value', idx);\n            var waterLevel = radiusY - value * radiusY * 2;\n            phase = oldWave ? oldWave.shape.phase\n                : (phase === 'auto' ? idx * Math.PI / 4 : phase);\n            var normalStyle = itemStyleModel.getItemStyle();\n            if (!normalStyle.fill) {\n                var seriesColor = seriesModel.get('color');\n                var id = idx % seriesColor.length;\n                normalStyle.fill = seriesColor[id];\n            }\n\n            var x = radiusX * 2;\n            var wave = new LiquidShape({\n                shape: {\n                    waveLength: waveLength,\n                    radius: radiusX,\n                    radiusY: radiusY,\n                    cx: x,\n                    cy: 0,\n                    waterLevel: waterLevel,\n                    amplitude: amplitude,\n                    phase: phase,\n                    inverse: isInverse\n                },\n                style: normalStyle,\n                x: cx,\n                y: cy,\n            });\n            wave.shape._waterLevel = waterLevel;\n\n            var hoverStyle = itemModel.getModel('emphasis.itemStyle')\n                .getItemStyle();\n            hoverStyle.lineWidth = 0;\n\n            wave.ensureState('emphasis').style = hoverStyle;\n            echarts.helper.enableHoverEmphasis(wave);\n\n            // clip out the part outside the circle\n            var clip = getPath(radius, true);\n            // set fill for clipPath, otherwise it will not trigger hover event\n            clip.setStyle({\n                fill: 'white'\n            });\n            wave.setClipPath(clip);\n\n            return wave;\n        }\n\n        function setWaveAnimation(idx, wave, oldWave) {\n            var itemModel = data.getItemModel(idx);\n\n            var maxSpeed = itemModel.get('period');\n            var direction = itemModel.get('direction');\n\n            var value = data.get('value', idx);\n\n            var phase = itemModel.get('phase');\n            phase = oldWave ? oldWave.shape.phase\n                : (phase === 'auto' ? idx * Math.PI / 4 : phase);\n\n            var defaultSpeed = function (maxSpeed) {\n                var cnt = data.count();\n                return cnt === 0 ? maxSpeed : maxSpeed *\n                    (0.2 + (cnt - idx) / cnt * 0.8);\n            };\n            var speed = 0;\n            if (maxSpeed === 'auto') {\n                speed = defaultSpeed(5000);\n            }\n            else {\n                speed = typeof maxSpeed === 'function'\n                    ? maxSpeed(value, idx) : maxSpeed;\n            }\n\n            // phase for moving left/right\n            var phaseOffset = 0;\n            if (direction === 'right' || direction == null) {\n                phaseOffset = Math.PI;\n            }\n            else if (direction === 'left') {\n                phaseOffset = -Math.PI;\n            }\n            else if (direction === 'none') {\n                phaseOffset = 0;\n            }\n            else {\n                console.error('Illegal direction value for liquid fill.');\n            }\n\n            // wave animation of moving left/right\n            if (direction !== 'none' && itemModel.get('waveAnimation')) {\n                wave\n                    .animate('shape', true)\n                    .when(0, {\n                        phase: phase\n                    })\n                    .when(speed / 2, {\n                        phase: phaseOffset + phase\n                    })\n                    .when(speed, {\n                        phase: phaseOffset * 2 + phase\n                    })\n                    .during(function () {\n                        if (wavePath) {\n                            wavePath.dirty(true);\n                        }\n                    })\n                    .start();\n            }\n        }\n\n        /**\n         * text on wave\n         */\n        function getText(waves) {\n            var labelModel = itemModel.getModel('label');\n\n            function formatLabel() {\n                var formatted = seriesModel.getFormattedLabel(0, 'normal');\n                var defaultVal = (data.get('value', 0) * 100);\n                var defaultLabel = data.getName(0) || seriesModel.name;\n                if (!isNaN(defaultVal)) {\n                    defaultLabel = defaultVal.toFixed(0) + '%';\n                }\n                return formatted == null ? defaultLabel : formatted;\n            }\n\n            var textRectOption = {\n                z2: 10,\n                shape: {\n                    x: left,\n                    y: top,\n                    width: (isFillContainer ? radius[0] : radius) * 2,\n                    height: (isFillContainer ? radius[1] : radius) * 2\n                },\n                style: {\n                    fill: 'transparent'\n                },\n                textConfig: {\n                    position: labelModel.get('position') || 'inside'\n                },\n                silent: true\n            };\n            var textOption = {\n                style: {\n                    text: formatLabel(),\n                    textAlign: labelModel.get('align'),\n                    textVerticalAlign: labelModel.get('baseline')\n                }\n            };\n            Object.assign(textOption.style, echarts.helper.createTextStyle(labelModel));\n\n            var outsideTextRect = new echarts.graphic.Rect(textRectOption);\n            var insideTextRect = new echarts.graphic.Rect(textRectOption);\n            insideTextRect.disableLabelAnimation = true;\n            outsideTextRect.disableLabelAnimation = true;\n\n            var outsideText = new echarts.graphic.Text(textOption);\n            var insideText = new echarts.graphic.Text(textOption);\n            outsideTextRect.setTextContent(outsideText);\n\n            insideTextRect.setTextContent(insideText);\n            var insColor = labelModel.get('insideColor');\n            insideText.style.fill = insColor;\n\n            var group = new echarts.graphic.Group();\n            group.add(outsideTextRect);\n            group.add(insideTextRect);\n\n            // clip out waves for insideText\n            var boundingCircle = getPath(radius, true);\n\n            wavePath = new echarts.graphic.CompoundPath({\n                shape: {\n                    paths: waves\n                },\n                x: cx,\n                y: cy\n            });\n\n            wavePath.setClipPath(boundingCircle);\n            insideTextRect.setClipPath(wavePath);\n\n            return group;\n        }\n    },\n\n    dispose: function () {\n        // dispose nothing here\n    }\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEQ,kBAAkB;AAAA,EAEtB,MAAM;AAAA,EAEN,eAAe,WAAY;AACvB,QAAI,SAAS,KAAK;AAClB,WAAO,WAAW,KAAK,IAAI,KAAK,MAAM,OAAO,QAAQ,GAAG,CAAC;AAAA,EAC7D;AAAA,EAEA,gBAAgB,SAAU,QAAQ,SAAS;AACvC,QAAI,aAAqB,eAAO,iBAAiB,OAAO,MAAM;AAAA,MAC1D,iBAAiB,CAAC,OAAO;AAAA,IAC7B,CAAC;AACD,QAAI,OAAO,IAAY,mBAAK,YAAY,IAAI;AAC5C,SAAK,SAAS,OAAO,IAAI;AACzB,WAAO;AAAA,EACX;AAAA,EAEA,eAAe;AAAA,IACX,OAAO,CAAC,WAAW,WAAW,WAAW,SAAS;AAAA,IAClD,QAAQ,CAAC,OAAO,KAAK;AAAA,IACrB,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,OAAO;AAAA,IAEP,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IAEzB,SAAS;AAAA,MACL,MAAM;AAAA,MACN,gBAAgB;AAAA,MAChB,WAAW;AAAA,QACP,OAAO;AAAA,QACP,aAAa;AAAA,QACb,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IAEA,iBAAiB;AAAA,MACb,OAAO;AAAA,IACX;AAAA,IAEA,WAAW;AAAA,MACP,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,aAAa;AAAA,IACjB;AAAA,IAEA,OAAO;AAAA,MACH,MAAM;AAAA,MACN,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU;AAAA,MACV,YAAY;AAAA,MAEZ,OAAO;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,IACd;AAAA,IAEA,UAAU;AAAA,MACN,WAAW;AAAA,QACP,SAAS;AAAA,MACb;AAAA,IACJ;AAAA,EACJ;AACJ,CAAC;;;AC3ED,IAAO,0BAAgB,gBAAQ,YAAY;AAAA,EACvC,MAAM;AAAA,EAEN,OAAO;AAAA,IACH,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS;AAAA,EACb;AAAA,EAEA,WAAW,SAAU,KAAK,OAAO;AAC7B,QAAI,MAAM,WAAW,MAAM;AACvB,YAAM,UAAU,MAAM;AAAA,IAC1B;AAOA,QAAI,SAAS,KAAK;AAAA,MACd,KAAK,KAAK,IAAI,MAAM,SAAS,MAAM,aAAa,CAAC,IAAI;AAAA,MACrD;AAAA,IACJ;AAGA,WAAO,MAAM,QAAQ,CAAC,KAAK,KAAK,GAAG;AAC/B,YAAM,SAAS,KAAK,KAAK;AAAA,IAC7B;AACA,WAAO,MAAM,QAAQ,GAAG;AACpB,YAAM,SAAS,KAAK,KAAK;AAAA,IAC7B;AACA,QAAI,QAAQ,MAAM,QAAQ,KAAK,KAAK,IAAI,MAAM;AAE9C,QAAI,OAAO,MAAM,KAAK,MAAM,SAAS,QAAQ,MAAM,SAAS;AAY5D,QAAI,OAAO,MAAM,MAAM,UAAU;AASjC,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC7B,UAAI,QAAQ,IAAI;AAChB,UAAI,MAAM;AAAA,QAAkB,IAAI,MAAM,aAAa;AAAA,QAAG;AAAA,QAClD,MAAM;AAAA,QAAY,MAAM;AAAA,MAAS;AACrC,UAAI;AAAA,QAAc,IAAI,CAAC,EAAE,CAAC,IAAI;AAAA,QAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAM;AAAA,QACnD,IAAI,CAAC,EAAE,CAAC,IAAI;AAAA,QAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAM;AAAA,QACrC,IAAI,CAAC,EAAE,CAAC,IAAI;AAAA,QAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAM;AAAA,MAAU;AAEnD,UAAI,MAAM,SAAS,GAAG;AAClB,oBAAY,IAAI,CAAC,EAAE,CAAC;AAAA,MACxB;AAAA,IACJ;AAEA,QAAI,MAAM,SAAS;AASf,UAAI,OAAO,YAAY,MAAM,MAAM,KAAK,MAAM,OAAO;AACrD,UAAI,OAAO,MAAM,MAAM,KAAK,MAAM,OAAO;AACzC,UAAI,OAAO,MAAM,MAAM,UAAU;AAAA,IACrC,OACK;AAWD,UAAI,OAAO,YAAY,MAAM,MAAM,KAAK,MAAM,OAAO;AACrD,UAAI,OAAO,MAAM,MAAM,KAAK,MAAM,OAAO;AACzC,UAAI,OAAO,MAAM,MAAM,UAAU;AAAA,IACrC;AAEA,QAAI,UAAU;AAAA,EAClB;AACJ,CAAC;AAsBD,SAAS,kBAAkB,GAAG,OAAO,YAAY,WAAW;AACxD,MAAI,UAAU,GAAG;AACb,WAAO;AAAA,MACH,CAAC,IAAI,IAAI,IAAI,aAAa,KAAK,KAAK,GAAG,YAAY,CAAC;AAAA,MACpD,CAAC,IAAI,IAAI,IAAI,aAAa,KAAK,IAAQ,SAAS;AAAA,MAChD,CAAC,IAAI,aAAa,GAAqB,SAAS;AAAA,IACpD;AAAA,EACJ,WACS,UAAU,GAAG;AAClB,WAAO;AAAA,MACH;AAAA,QAAC,IAAI,IAAI,IAAI,aAAa,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,QACnD;AAAA,MAAS;AAAA,MACT;AAAA,QAAC,IAAI,IAAI,IAAI,aAAa,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,QACnD,YAAY;AAAA,MAAC;AAAA,MACb,CAAC,IAAI,aAAa,GAAqB,CAAC;AAAA,IAC5C;AAAA,EACJ,WACS,UAAU,GAAG;AAClB,WAAO;AAAA,MACH,CAAC,IAAI,IAAI,IAAI,aAAa,KAAK,KAAK,GAAG,CAAC,YAAY,CAAC;AAAA,MACrD,CAAC,IAAI,IAAI,IAAI,aAAa,KAAK,IAAQ,CAAC,SAAS;AAAA,MACjD,CAAC,IAAI,aAAa,GAAqB,CAAC,SAAS;AAAA,IACrD;AAAA,EACJ,OACK;AACD,WAAO;AAAA,MACH;AAAA,QAAC,IAAI,IAAI,IAAI,aAAa,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,QACnD,CAAC;AAAA,MAAS;AAAA,MACV;AAAA,QAAC,IAAI,IAAI,IAAI,aAAa,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,QACnD,CAAC,YAAY;AAAA,MAAC;AAAA,MACd,CAAC,IAAI,aAAa,GAAqB,CAAC;AAAA,IAC5C;AAAA,EACJ;AACJ;;;AC9JA,IAAIA,gBAA0B;AAE9B,SAAS,aAAa,QAAQ;AAC1B,SAAO,UAAU,OAAO,QAAQ,SAAS,MAAM;AACnD;AAEQ,gBAAgB;AAAA,EAEpB,MAAM;AAAA,EAEN,QAAQ,SAAU,aAAa,SAAS,KAAK;AACzC,QAAI,OAAO;AACX,QAAI,QAAQ,KAAK;AACjB,UAAM,UAAU;AAEhB,QAAI,OAAO,YAAY,QAAQ;AAE/B,QAAI,YAAY,KAAK,aAAa,CAAC;AAEnC,QAAI,SAAS,UAAU,IAAI,QAAQ;AACnC,QAAI,SAAS,UAAU,IAAI,QAAQ;AAEnC,QAAI,QAAQ,IAAI,SAAS;AACzB,QAAI,SAAS,IAAI,UAAU;AAC3B,QAAI,OAAO,KAAK,IAAI,OAAO,MAAM;AAEjC,QAAI,kBAAkB;AACtB,QAAI,qBAAqB;AACzB,QAAI,cAAc,YAAY,IAAI,cAAc;AAEhD,QAAI,aAAa;AACb,wBAAkB,YAAY,IAAI,wBAAwB;AAC1D,2BAAqBA;AAAA,QACjB,YAAY,IAAI,+BAA+B;AAAA,QAAG;AAAA,MACtD;AAAA,IACJ;AAEA,QAAI,KAAKA,cAAa,OAAO,CAAC,GAAG,KAAK;AACtC,QAAI,KAAKA,cAAa,OAAO,CAAC,GAAG,MAAM;AAEvC,QAAI;AACJ,QAAI;AACJ,QAAI;AAEJ,QAAI,kBAAkB;AAEtB,QAAI,SAAS,YAAY,IAAI,OAAO;AACpC,QAAI,WAAW,aAAa;AAExB,wBAAkB;AAElB,qBAAe;AAAA,QACX,QAAQ;AAAA,QACR,SAAS;AAAA,MACb;AACA,oBAAc;AAAA,QACV,aAAa,CAAC,IAAI,qBAAqB;AAAA,QACvC,aAAa,CAAC,IAAI,qBAAqB;AAAA,MAC3C;AACA,sBAAgB;AAAA,QACZA,cAAa,iBAAiB,KAAK;AAAA,QACnCA,cAAa,iBAAiB,MAAM;AAAA,MACxC;AAEA,eAAS;AAAA,QACL,KAAK,IAAI,YAAY,CAAC,IAAI,cAAc,CAAC,GAAG,CAAC;AAAA,QAC7C,KAAK,IAAI,YAAY,CAAC,IAAI,cAAc,CAAC,GAAG,CAAC;AAAA,MACjD;AAAA,IACJ,OACK;AACD,qBAAeA,cAAa,QAAQ,IAAI,IAAI;AAC5C,oBAAc,eAAe,qBAAqB;AAClD,sBAAgBA,cAAa,iBAAiB,IAAI;AAElD,eAAS,KAAK,IAAI,cAAc,eAAe,CAAC;AAAA,IACpD;AAEA,QAAI,aAAa;AACb,UAAI,UAAU,WAAW;AACzB,cAAQ,MAAM,YAAY;AAC1B,YAAM,IAAI,WAAW,CAAC;AAAA,IAC1B;AAEA,QAAI,OAAO,kBAAkB,IAAI,KAAK;AACtC,QAAI,MAAM,kBAAkB,IAAI,KAAK;AAErC,QAAI,WAAW;AAEf,UAAM,IAAI,cAAc,CAAC;AAGzB,QAAI,UAAU,KAAK;AACnB,QAAI,QAAQ,CAAC;AACb,SAAK,KAAK,OAAO,EACZ,IAAI,SAAU,KAAK;AAChB,UAAI,OAAO,QAAQ,KAAK,KAAK;AAE7B,UAAI,aAAa,KAAK,MAAM;AAC5B,WAAK,MAAM,aAAa,kBAAkB,SAAS,IAAI;AACvD,MAAQ,gBAAQ,UAAU,MAAM;AAAA,QAC5B,OAAO;AAAA,UACH;AAAA,QACJ;AAAA,MACJ,GAAG,WAAW;AAEd,WAAK,KAAK;AACV,uBAAiB,KAAK,MAAM,IAAI;AAEhC,YAAM,IAAI,IAAI;AACd,WAAK,iBAAiB,KAAK,IAAI;AAC/B,YAAM,KAAK,IAAI;AAAA,IACnB,CAAC,EACA,OAAO,SAAU,QAAQ,QAAQ;AAC9B,UAAI,cAAc,QAAQ,iBAAiB,MAAM;AAGjD,UAAI,UAAU,QAAQ,QAAQ,OAAO,WAAW;AAGhD,UAAI,QAAQ,CAAC;AACb,UAAI,aAAa,CAAC,aAAa,MAAM,MAAM,SAAS,UAAU,WAAW,cAAc,YAAY;AACnG,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AACxC,YAAI,OAAO,WAAW,CAAC;AACvB,YAAI,QAAQ,MAAM,eAAe,IAAI,GAAG;AACpC,gBAAM,IAAI,IAAI,QAAQ,MAAM,IAAI;AAAA,QACpC;AAAA,MACJ;AAEA,UAAI,QAAQ,CAAC;AACb,UAAI,aAAa,CAAC,QAAQ,WAAW,cAAc,aAAa;AAChE,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AACxC,YAAI,OAAO,WAAW,CAAC;AACvB,YAAI,QAAQ,MAAM,eAAe,IAAI,GAAG;AACpC,gBAAM,IAAI,IAAI,QAAQ,MAAM,IAAI;AAAA,QACpC;AAAA,MACJ;AAEA,UAAI,iBAAiB;AACjB,cAAM,UAAU,SAAS;AAAA,MAC7B;AAGA,MAAQ,gBAAQ,YAAY,aAAa;AAAA,QACrC;AAAA,QACA,GAAG,QAAQ;AAAA,QACX,GAAG,QAAQ;AAAA,MACf,GAAG,WAAW;AAEd,UAAI,YAAY,gCAAgC,YAAY,6BAA6B,GAAG;AACxF,QAAQ,gBAAQ,YAAY,aAAa;AAAA,UACrC;AAAA,QACJ,GAAG,WAAW;AAAA,MAClB,OACK;AACD,oBAAY,SAAS,KAAK;AAAA,MAC9B;AAGA,UAAI,kBAAkB,YAAY,YAAY;AAC9C,UAAI,kBAAkB,QAAQ,YAAY;AAE1C,kBAAY,YAAY,QAAQ,YAAY,CAAC;AAC7C,kBAAY,MAAM,UAAU,QAAQ;AAEpC,UAAI,mBAAmB,mBAChB,KAAK,WAAW,UAEhB,CAAC,aAAa,MAAM,GACzB;AAEE,QAAQ,gBAAQ,YAAY,iBAAiB;AAAA,UACzC,OAAO,gBAAgB;AAAA,QAC3B,GAAG,aAAa,EAAE,QAAQ,KAAK,CAAC;AAAA,MACpC;AAEA,uBAAiB,QAAQ,aAAa,WAAW;AACjD,YAAM,IAAI,WAAW;AACrB,WAAK,iBAAiB,QAAQ,WAAW;AACzC,YAAM,KAAK,WAAW;AAAA,IAC1B,CAAC,EACA,OAAO,SAAU,KAAK;AACnB,UAAI,OAAO,QAAQ,iBAAiB,GAAG;AACvC,YAAM,OAAO,IAAI;AAAA,IACrB,CAAC,EACA,QAAQ;AAEb,QAAI,UAAU,IAAI,YAAY,GAAG;AAC7B,YAAM,IAAI,QAAQ,KAAK,CAAC;AAAA,IAC5B;AAEA,SAAK,SAAS;AACd,SAAK,QAAQ;AASb,aAAS,QAAQ,GAAG,eAAe;AAC/B,UAAI,QAAQ;AAER,YAAI,aAAa,MAAM,GAAG;AACtB,cAAI,OAAe,gBAAQ,SAAS,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;AACvD,cAAI,UAAU,KAAK,gBAAgB;AACnC,cAAI,IAAI,QAAQ;AAChB,cAAI,IAAI,QAAQ;AAChB,cAAI,IAAI,GAAG;AACP,gBAAI,IAAI,IAAI,IAAI;AAChB,gBAAI,IAAI;AAAA,UACZ,OACK;AACD,gBAAI,IAAI,IAAI,IAAI;AAChB,gBAAI,IAAI;AAAA,UACZ;AAEA,cAAIC,QAAO,gBAAgB,IAAI,KAAK,IAAI;AACxC,cAAIC,OAAM,gBAAgB,IAAI,KAAK,IAAI;AACvC,iBAAe,gBAAQ;AAAA,YACnB,OAAO,MAAM,CAAC;AAAA,YACd,CAAC;AAAA,YACD,IAAY,gBAAQ,aAAaD,OAAMC,MAAK,GAAG,CAAC;AAAA,UACpD;AACA,cAAI,eAAe;AACf,iBAAK,IAAI,CAAC,IAAI;AACd,iBAAK,IAAI,CAAC,IAAI;AAAA,UAClB;AACA,iBAAO;AAAA,QACX,WACS,iBAAiB;AAEtB,cAAI,IAAI,gBAAgB,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC;AACxC,cAAI,IAAI,gBAAgB,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC;AACxC,iBAAe,eAAO;AAAA,YAClB;AAAA,YAAQ;AAAA,YAAG;AAAA,YAAG,EAAE,CAAC,IAAI;AAAA,YAAG,EAAE,CAAC,IAAI;AAAA,UACnC;AAAA,QACJ,OACK;AACD,cAAI,IAAI,gBAAgB,CAAC,IAAI,KAAK;AAClC,cAAI,IAAI,gBAAgB,CAAC,IAAI,KAAK;AAClC,cAAI,WAAW,OAAO;AAClB,iBAAK;AAAA,UACT,WACS,WAAW,SAAS;AACzB,iBAAK;AAAA,UACT;AACA,iBAAe,eAAO,aAAa,QAAQ,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC;AAAA,QACjE;AAAA,MACJ;AAEA,aAAO,IAAY,gBAAQ,OAAO;AAAA,QAC9B,OAAO;AAAA,UACH,IAAI,gBAAgB,IAAI;AAAA,UACxB,IAAI,gBAAgB,IAAI;AAAA,UACxB;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AAIA,aAAS,aAAa;AAClB,UAAI,cAAc,QAAQ,YAAY;AACtC,kBAAY,MAAM,OAAO;AAEzB,kBAAY,SAAS,YAAY,SAAS,mBAAmB,EACxD,aAAa,CAAC;AAEnB,aAAO;AAAA,IACX;AAKA,aAAS,gBAAgB;AAErB,UAAI,aAAa,QAAQ,MAAM;AAC/B,iBAAW,SAAS,YAAY,SAAS,iBAAiB,EACrD,aAAa,CAAC;AACnB,iBAAW,MAAM,OAAO;AAGxB,iBAAW,KAAK;AAEhB,UAAI,WAAW,QAAQ,MAAM;AAC7B,eAAS,SAAS,YAAY,SAAS,iBAAiB,EACnD,aAAa,CAAC;AACnB,eAAS,MAAM,SAAS;AAExB,UAAIC,SAAQ,IAAY,gBAAQ,MAAM;AACtC,MAAAA,OAAM,IAAI,UAAU;AACpB,MAAAA,OAAM,IAAI,QAAQ;AAElB,aAAOA;AAAA,IACX;AAKA,aAAS,QAAQ,KAAK,WAAW,SAAS;AACtC,UAAI,UAAU,kBAAkB,OAAO,CAAC,IAAI;AAC5C,UAAI,UAAU,kBAAkB,SAAS,IAAI;AAE7C,UAAIC,aAAY,KAAK,aAAa,GAAG;AACrC,UAAI,iBAAiBA,WAAU,SAAS,WAAW;AACnD,UAAI,QAAQA,WAAU,IAAI,OAAO;AACjC,UAAI,YAAYJ;AAAA,QAAaI,WAAU,IAAI,WAAW;AAAA,QAClD,UAAU;AAAA,MAAC;AACf,UAAI,aAAaJ;AAAA,QAAaI,WAAU,IAAI,YAAY;AAAA,QACpD,UAAU;AAAA,MAAC;AAEf,UAAI,QAAQ,KAAK,IAAI,SAAS,GAAG;AACjC,UAAI,aAAa,UAAU,QAAQ,UAAU;AAC7C,cAAQ,UAAU,QAAQ,MAAM,QACzB,UAAU,SAAS,MAAM,KAAK,KAAK,IAAI;AAC9C,UAAI,cAAc,eAAe,aAAa;AAC9C,UAAI,CAAC,YAAY,MAAM;AACnB,YAAI,cAAc,YAAY,IAAI,OAAO;AACzC,YAAI,KAAK,MAAM,YAAY;AAC3B,oBAAY,OAAO,YAAY,EAAE;AAAA,MACrC;AAEA,UAAI,IAAI,UAAU;AAClB,UAAI,OAAO,IAAI,wBAAY;AAAA,QACvB,OAAO;AAAA,UACH;AAAA,UACA,QAAQ;AAAA,UACR;AAAA,UACA,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS;AAAA,QACb;AAAA,QACA,OAAO;AAAA,QACP,GAAG;AAAA,QACH,GAAG;AAAA,MACP,CAAC;AACD,WAAK,MAAM,cAAc;AAEzB,UAAI,aAAaA,WAAU,SAAS,oBAAoB,EACnD,aAAa;AAClB,iBAAW,YAAY;AAEvB,WAAK,YAAY,UAAU,EAAE,QAAQ;AACrC,MAAQ,eAAO,oBAAoB,IAAI;AAGvC,UAAI,OAAO,QAAQ,QAAQ,IAAI;AAE/B,WAAK,SAAS;AAAA,QACV,MAAM;AAAA,MACV,CAAC;AACD,WAAK,YAAY,IAAI;AAErB,aAAO;AAAA,IACX;AAEA,aAAS,iBAAiB,KAAK,MAAM,SAAS;AAC1C,UAAIA,aAAY,KAAK,aAAa,GAAG;AAErC,UAAI,WAAWA,WAAU,IAAI,QAAQ;AACrC,UAAI,YAAYA,WAAU,IAAI,WAAW;AAEzC,UAAI,QAAQ,KAAK,IAAI,SAAS,GAAG;AAEjC,UAAI,QAAQA,WAAU,IAAI,OAAO;AACjC,cAAQ,UAAU,QAAQ,MAAM,QACzB,UAAU,SAAS,MAAM,KAAK,KAAK,IAAI;AAE9C,UAAI,eAAe,SAAUC,WAAU;AACnC,YAAI,MAAM,KAAK,MAAM;AACrB,eAAO,QAAQ,IAAIA,YAAWA,aACzB,OAAO,MAAM,OAAO,MAAM;AAAA,MACnC;AACA,UAAI,QAAQ;AACZ,UAAI,aAAa,QAAQ;AACrB,gBAAQ,aAAa,GAAI;AAAA,MAC7B,OACK;AACD,gBAAQ,OAAO,aAAa,aACtB,SAAS,OAAO,GAAG,IAAI;AAAA,MACjC;AAGA,UAAI,cAAc;AAClB,UAAI,cAAc,WAAW,aAAa,MAAM;AAC5C,sBAAc,KAAK;AAAA,MACvB,WACS,cAAc,QAAQ;AAC3B,sBAAc,CAAC,KAAK;AAAA,MACxB,WACS,cAAc,QAAQ;AAC3B,sBAAc;AAAA,MAClB,OACK;AACD,gBAAQ,MAAM,0CAA0C;AAAA,MAC5D;AAGA,UAAI,cAAc,UAAUD,WAAU,IAAI,eAAe,GAAG;AACxD,aACK,QAAQ,SAAS,IAAI,EACrB,KAAK,GAAG;AAAA,UACL;AAAA,QACJ,CAAC,EACA,KAAK,QAAQ,GAAG;AAAA,UACb,OAAO,cAAc;AAAA,QACzB,CAAC,EACA,KAAK,OAAO;AAAA,UACT,OAAO,cAAc,IAAI;AAAA,QAC7B,CAAC,EACA,OAAO,WAAY;AAChB,cAAI,UAAU;AACV,qBAAS,MAAM,IAAI;AAAA,UACvB;AAAA,QACJ,CAAC,EACA,MAAM;AAAA,MACf;AAAA,IACJ;AAKA,aAAS,QAAQE,QAAO;AACpB,UAAI,aAAa,UAAU,SAAS,OAAO;AAE3C,eAAS,cAAc;AACnB,YAAI,YAAY,YAAY,kBAAkB,GAAG,QAAQ;AACzD,YAAI,aAAc,KAAK,IAAI,SAAS,CAAC,IAAI;AACzC,YAAI,eAAe,KAAK,QAAQ,CAAC,KAAK,YAAY;AAClD,YAAI,CAAC,MAAM,UAAU,GAAG;AACpB,yBAAe,WAAW,QAAQ,CAAC,IAAI;AAAA,QAC3C;AACA,eAAO,aAAa,OAAO,eAAe;AAAA,MAC9C;AAEA,UAAI,iBAAiB;AAAA,QACjB,IAAI;AAAA,QACJ,OAAO;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,QAAQ,kBAAkB,OAAO,CAAC,IAAI,UAAU;AAAA,UAChD,SAAS,kBAAkB,OAAO,CAAC,IAAI,UAAU;AAAA,QACrD;AAAA,QACA,OAAO;AAAA,UACH,MAAM;AAAA,QACV;AAAA,QACA,YAAY;AAAA,UACR,UAAU,WAAW,IAAI,UAAU,KAAK;AAAA,QAC5C;AAAA,QACA,QAAQ;AAAA,MACZ;AACA,UAAI,aAAa;AAAA,QACb,OAAO;AAAA,UACH,MAAM,YAAY;AAAA,UAClB,WAAW,WAAW,IAAI,OAAO;AAAA,UACjC,mBAAmB,WAAW,IAAI,UAAU;AAAA,QAChD;AAAA,MACJ;AACA,aAAO,OAAO,WAAW,OAAe,eAAO,gBAAgB,UAAU,CAAC;AAE1E,UAAI,kBAAkB,IAAY,gBAAQ,KAAK,cAAc;AAC7D,UAAI,iBAAiB,IAAY,gBAAQ,KAAK,cAAc;AAC5D,qBAAe,wBAAwB;AACvC,sBAAgB,wBAAwB;AAExC,UAAI,cAAc,IAAY,gBAAQ,KAAK,UAAU;AACrD,UAAI,aAAa,IAAY,gBAAQ,KAAK,UAAU;AACpD,sBAAgB,eAAe,WAAW;AAE1C,qBAAe,eAAe,UAAU;AACxC,UAAI,WAAW,WAAW,IAAI,aAAa;AAC3C,iBAAW,MAAM,OAAO;AAExB,UAAIH,SAAQ,IAAY,gBAAQ,MAAM;AACtC,MAAAA,OAAM,IAAI,eAAe;AACzB,MAAAA,OAAM,IAAI,cAAc;AAGxB,UAAI,iBAAiB,QAAQ,QAAQ,IAAI;AAEzC,iBAAW,IAAY,gBAAQ,aAAa;AAAA,QACxC,OAAO;AAAA,UACH,OAAOG;AAAA,QACX;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,MACP,CAAC;AAED,eAAS,YAAY,cAAc;AACnC,qBAAe,YAAY,QAAQ;AAEnC,aAAOH;AAAA,IACX;AAAA,EACJ;AAAA,EAEA,SAAS,WAAY;AAAA,EAErB;AACJ,CAAC;", "names": ["parsePercent", "left", "top", "group", "itemModel", "maxSpeed", "waves"]}