import {
  M,
  c as c5,
  r as r4
} from "./chunk-U6GJBSCL.js";
import "./chunk-RY6ZYWKC.js";
import "./chunk-AEJDTXF3.js";
import {
  n as n3,
  s as s6
} from "./chunk-223SE4BY.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import {
  c as c4,
  g as g5,
  p,
  r as r3
} from "./chunk-B3E6KHYE.js";
import {
  s as s7
} from "./chunk-CV76WXPW.js";
import {
  b as b2
} from "./chunk-LGU2JTOA.js";
import "./chunk-MYYUEN6M.js";
import {
  o as o4
} from "./chunk-O4X2QQEQ.js";
import {
  q as q3
} from "./chunk-EVNRYDNL.js";
import {
  ee
} from "./chunk-KU2IHOQF.js";
import "./chunk-DJASJBTH.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-6OFWBRK2.js";
import "./chunk-WAPZ634R.js";
import {
  o as o3
} from "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-BM3BRFSV.js";
import "./chunk-D7BTYVTV.js";
import "./chunk-JLELSJK5.js";
import "./chunk-RURSJOSG.js";
import "./chunk-FDLE77SA.js";
import "./chunk-6DAQTVXB.js";
import "./chunk-NQB6UCZ5.js";
import "./chunk-2CLVPBYJ.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-M4ZUXRA3.js";
import "./chunk-WJKHSSMC.js";
import {
  f as f4,
  g as g3
} from "./chunk-J5A2YARY.js";
import "./chunk-OEIEPNC6.js";
import {
  s as s5
} from "./chunk-ZQY4DQCR.js";
import {
  d as d3
} from "./chunk-6U5WUWZN.js";
import {
  i as i2
} from "./chunk-3JR5KBYG.js";
import "./chunk-FRO3RSRO.js";
import {
  l as l3
} from "./chunk-DSTI5UIS.js";
import {
  h
} from "./chunk-MSIU52YL.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import {
  j as j3
} from "./chunk-IEIKQ72S.js";
import "./chunk-3IDKVHSA.js";
import "./chunk-4VO6N7OL.js";
import "./chunk-7VXHHPI3.js";
import "./chunk-2RO3UJ2R.js";
import "./chunk-ONE6GLG5.js";
import {
  c as c3,
  d as d2
} from "./chunk-S7FENR5U.js";
import "./chunk-NSJUSNRV.js";
import {
  c as c2,
  q as q2
} from "./chunk-YMY3DTA5.js";
import {
  n as n2
} from "./chunk-TNP2LXZZ.js";
import {
  G,
  It,
  T,
  U as U2,
  W,
  at,
  dt,
  mt,
  ot,
  ut,
  wt
} from "./chunk-DBDYNPQT.js";
import "./chunk-RE7K5Z3I.js";
import {
  o as o2,
  s as s4,
  t as t3
} from "./chunk-SEO6KEGF.js";
import "./chunk-4DICTWL2.js";
import "./chunk-WGU7CS6R.js";
import "./chunk-4JOAXMIS.js";
import "./chunk-UYHYCNVE.js";
import "./chunk-INCFUNSS.js";
import {
  h as h2
} from "./chunk-7BSY2CUN.js";
import "./chunk-SY6DBVDS.js";
import {
  g as g4
} from "./chunk-HTXGAKOK.js";
import {
  r as r2
} from "./chunk-D3MAF4VS.js";
import "./chunk-2WMCP27R.js";
import "./chunk-3HW44BD3.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-2AZSZWPE.js";
import "./chunk-PTIRBOGQ.js";
import {
  d
} from "./chunk-Q4VCSCSY.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  x
} from "./chunk-USWRDFDJ.js";
import "./chunk-XM4RHPJM.js";
import "./chunk-6T5FEO66.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import {
  a as a4,
  m,
  y as y2
} from "./chunk-GE5PSQPZ.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-MUYX6GXF.js";
import {
  T as T2
} from "./chunk-N7ADFPOO.js";
import {
  a as a3
} from "./chunk-AVKOL7OR.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  U as U3
} from "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import {
  v as v2
} from "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import {
  f as f3,
  j as j2,
  l as l2
} from "./chunk-QUHG7NMD.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  E as E2,
  R,
  f2
} from "./chunk-JXLVNWKF.js";
import {
  s as s3
} from "./chunk-LJHVXLBF.js";
import "./chunk-G5KX4JSG.js";
import {
  a as a2
} from "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e as e2,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  D,
  E,
  L,
  U,
  b,
  f,
  g as g2,
  j
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  t as t2
} from "./chunk-GZGAQUSK.js";
import {
  c,
  e,
  g,
  i,
  l,
  o,
  q,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/features/processors.js
function o5(o7) {
  return "heatmap" === o7 ? import("./HeatmapProcessor-XJ74TRD6.js") : import("./SymbolProcessor-BVVD4ZWL.js");
}

// node_modules/@arcgis/core/views/2d/layers/features/support/FeatureSetReaderPBFHeader.js
var r5 = 268435455;
var n4 = class {
  constructor() {
    this.fieldMap = /* @__PURE__ */ new Map(), this.fields = [], this.hasFeatures = false, this.exceededTransferLimit = false, this.fieldCount = 0, this.featureCount = 0, this.objectIdFieldIndex = 0, this.vertexCount = 0, this.offsets = { attributes: new Array(), geometry: new Array() }, this.centroid = new Array();
  }
  hasField(e3) {
    return this.fieldMap.has(e3);
  }
  isDateField(e3) {
    var _a;
    return (null != e3 && ((_a = this.fieldMap.get(e3)) == null ? void 0 : _a.isDate)) ?? false;
  }
  getFieldIndex(e3) {
    var _a;
    return null != e3 ? (_a = this.fieldMap.get(e3)) == null ? void 0 : _a.index : void 0;
  }
};
function a5(e3) {
  const t5 = 1, r6 = 2, n7 = e3.asUnsafe(), a10 = n7.getLength(), i5 = n7.pos() + a10, o7 = { name: "", isDate: false };
  for (; n7.pos() < i5 && n7.next(); ) switch (n7.tag()) {
    case t5:
      o7.name = n7.getString();
      break;
    case r6:
      "esriFieldTypeDate" === c2(n7.getEnum()) && (o7.isDate = true);
      break;
    default:
      n7.skip();
  }
  return o7;
}
function i3(e3) {
  return e3.toLowerCase().trim();
}
function o6(s9, o7, f10 = false) {
  const c9 = 1, d9 = 3, l7 = 9, u3 = 12, g8 = 13, p5 = 15, h6 = s9.asUnsafe(), m4 = h6.pos(), b5 = new n4();
  let w3 = 0, k2 = 0;
  const x4 = 1, y6 = 2, I3 = 4, F4 = 3;
  let L3 = null, A3 = null, C = null, S3 = false;
  for (; h6.next(); ) switch (h6.tag()) {
    case c9:
      L3 = h6.getString();
      break;
    case d9:
      A3 = h6.getString();
      break;
    case u3:
      C = h6.processMessage(q2);
      break;
    case l7:
      if (b5.exceededTransferLimit = h6.getBool(), b5.exceededTransferLimit) {
        b5.offsets.geometry = f10 ? new Float64Array(8e3) : new Int32Array(8e3), b5.centroid = f10 ? new Float64Array(16e3) : new Int32Array(16e3);
        for (let e3 = 0; e3 < b5.centroid.length; e3++) b5.centroid[e3] = r5;
      }
      break;
    case g8: {
      const e3 = a5(s9), t5 = e3.name, r6 = i3(e3.name), n7 = { fieldName: t5, index: w3++, isDate: e3.isDate };
      b5.fields.push(n7), b5.fieldMap.set(e3.name, n7), b5.fieldMap.set(r6, n7);
      break;
    }
    case p5: {
      const e3 = h6.getLength(), t5 = h6.pos() + e3;
      if (!b5.exceededTransferLimit) {
        const e4 = b5.offsets.geometry, t6 = b5.centroid;
        e4.push(0), t6.push(r5), t6.push(r5);
      }
      !S3 && b5.exceededTransferLimit && (S3 = true, b5.offsets.attributes = f10 ? new Float64Array(8e3 * w3) : new Uint32Array(8e3 * w3));
      let s10 = k2 * w3;
      for (; h6.pos() < t5 && h6.next(); ) switch (h6.tag()) {
        case x4: {
          if (S3) b5.offsets.attributes[s10++] = h6.pos();
          else {
            b5.offsets.attributes.push(h6.pos());
          }
          const e4 = h6.getLength();
          h6.skipLen(e4);
          break;
        }
        case y6:
          if (o7) {
            const e4 = h6.getLength(), t6 = h6.pos() + e4;
            for (; h6.pos() < t6 && h6.next(); ) switch (h6.tag()) {
              case F4: {
                h6.getUInt32();
                const e5 = h6.getSInt64(), t7 = h6.getSInt64();
                b5.centroid[2 * k2] = e5, b5.centroid[2 * k2 + 1] = t7;
                break;
              }
              default:
                h6.skip();
            }
          } else {
            b5.offsets.geometry[k2] = h6.pos();
            const e4 = h6.getLength();
            b5.vertexCount += e4, h6.skipLen(e4);
          }
          break;
        case I3: {
          const e4 = h6.getLength(), t6 = h6.pos() + e4;
          for (; h6.pos() < t6 && h6.next(); ) switch (h6.tag()) {
            case F4: {
              h6.getUInt32();
              const e5 = h6.getSInt64(), t7 = h6.getSInt64();
              b5.centroid[2 * k2] = e5, b5.centroid[2 * k2 + 1] = t7;
              break;
            }
            default:
              h6.skip();
          }
          break;
        }
        default:
          h6.skip();
      }
      k2++, b5.hasFeatures = true;
      break;
    }
    default:
      h6.skip();
  }
  const M2 = L3 || A3;
  if (!M2) throw new s2("FeatureSet has no objectId or globalId field name");
  return b5.featureCount = k2, b5.fieldCount = w3, b5.objectIdFieldIndex = b5.getFieldIndex(M2), b5.transform = C, b5.displayIds = new Uint32Array(b5.featureCount), b5.groupIds = new Uint16Array(b5.featureCount), h6.move(m4), b5;
}

// node_modules/@arcgis/core/views/2d/layers/features/support/FeatureSetReaderPBF.js
var g6 = true;
var l4 = 268435455;
var _ = 128;
var f5 = 128e3;
var y3 = { small: { delta: new Int32Array(_), decoded: new Int32Array(_) }, large: { delta: new Int32Array(f5), decoded: new Int32Array(f5) } };
function I(e3) {
  return e3 <= y3.small.delta.length ? y3.small : (e3 <= y3.large.delta.length || (y3.large.delta = new Int32Array(Math.round(1.25 * e3)), y3.large.decoded = new Int32Array(Math.round(1.25 * e3))), y3.large);
}
function p2(e3) {
  return e3.toLowerCase().trim();
}
function m2(r6) {
  try {
    const e3 = 2, t5 = new n2(new Uint8Array(r6), new DataView(r6));
    for (; t5.next(); ) {
      if (t5.tag() === e3) return x2(t5.getMessage());
      t5.skip();
    }
  } catch (s9) {
    const r7 = new s2("query:parsing-pbf", "Error while parsing FeatureSet PBF payload", { error: s9 });
    s.getLogger("esri.view.2d.layers.features.support.FeatureSetReaderPBF").error(r7);
  }
  return null;
}
function x2(e3) {
  const t5 = 1;
  for (; e3.next(); ) {
    if (e3.tag() === t5) return e3.getMessage();
    e3.skip();
  }
  return null;
}
function S(e3) {
  const t5 = 1, r6 = 2, s9 = 3, i5 = 4, n7 = 5, a10 = 6, h6 = 7, o7 = 8, d9 = 9, u3 = e3.getLength(), c9 = e3.pos() + u3;
  for (; e3.pos() < c9 && e3.next(); ) switch (e3.tag()) {
    case t5:
      return e3.getString();
    case r6:
      return e3.getFloat();
    case s9:
      return e3.getDouble();
    case i5:
      return e3.getSInt32();
    case n7:
      return e3.getUInt32();
    case a10:
      return e3.getInt64();
    case h6:
      return e3.getUInt64();
    case o7:
      return e3.getSInt64();
    case d9:
      return e3.getBool();
    default:
      return e3.skip(), null;
  }
  return null;
}
function F(e3, t5, r6, s9, i5, n7) {
  return 0.5 * Math.abs(e3 * s9 + r6 * n7 + i5 * t5 - e3 * n7 - r6 * t5 - i5 * s9);
}
function v3(e3, t5, r6, s9) {
  return 0 === e3 * s9 - r6 * t5 && e3 * r6 + t5 * s9 > 0;
}
var G2 = class _G extends b2 {
  static fromBuffer(e3, t5, r6 = false) {
    const s9 = t5.geometryType, i5 = m2(e3), n7 = o6(i5, "esriGeometryPoint" === s9, r6), a10 = b2.createInstance();
    return new _G(a10, i5, n7, t5);
  }
  constructor(e3, t5, r6, s9) {
    super(e3, s9), this._hasNext = false, this._isPoints = false, this._featureIndex = -1, this._featureOffset = 0, this._cache = { area: 0, unquantGeometry: void 0, geometry: void 0, centroid: void 0, legacyFeature: void 0, optFeature: void 0 }, this._geometryType = s9.geometryType, this._reader = t5, this._header = r6, this._hasNext = r6.hasFeatures, this._isPoints = "esriGeometryPoint" === s9.geometryType;
  }
  get geometryType() {
    return this._geometryType;
  }
  get _size() {
    return this._header.featureCount;
  }
  get hasZ() {
    return false;
  }
  get hasM() {
    return false;
  }
  get stride() {
    return 2 + (this.hasZ ? 1 : 0) + (this.hasM ? 1 : 0);
  }
  get hasFeatures() {
    return this._header.hasFeatures;
  }
  get hasNext() {
    return this._hasNext;
  }
  get exceededTransferLimit() {
    return this._header.exceededTransferLimit;
  }
  hasField(e3) {
    return this._header.hasField(e3) || this._header.hasField(p2(e3));
  }
  getFieldNames() {
    return this._header.fields.map((e3) => e3.fieldName);
  }
  getSize() {
    return this._size;
  }
  getQuantizationTransform() {
    return this._header.transform;
  }
  getCursor() {
    return this.copy();
  }
  getIndex() {
    return this._featureIndex;
  }
  setIndex(e3) {
    this._cache.area = 0, this._cache.unquantGeometry = void 0, this._cache.geometry = void 0, this._cache.centroid = void 0, this._cache.legacyFeature = void 0, this._cache.optFeature = void 0, this._featureIndex = e3;
  }
  getAttributeHash() {
    let e3 = "";
    return this._header.fields.forEach(({ index: t5 }) => {
      e3 += this._readAttributeAtIndex(t5) + ".";
    }), e3;
  }
  getObjectId() {
    return this._readAttributeAtIndex(this._header.objectIdFieldIndex);
  }
  getDisplayId() {
    return this._header.displayIds[this._featureIndex];
  }
  setDisplayId(e3) {
    this._header.displayIds[this._featureIndex] = e3;
  }
  getGroupId() {
    return this._header.groupIds[this._featureIndex];
  }
  setGroupId(e3) {
    this._header.groupIds[this._featureIndex] = e3;
  }
  readLegacyFeature() {
    if (void 0 === this._cache.legacyFeature) {
      const e3 = this.readCentroid(), t5 = { attributes: this.readAttributes(), geometry: this._isPoints ? this.readLegacyPointGeometry() : this.readLegacyGeometry(), centroid: (e3 && { x: e3.coords[0], y: e3.coords[1] }) ?? null };
      return this._cache.legacyFeature = t5, t5;
    }
    return this._cache.legacyFeature;
  }
  readOptimizedFeature() {
    if (void 0 === this._cache.optFeature) {
      const e3 = new s4(this.readGeometry(), this.readAttributes(), this.readCentroid());
      return e3.objectId = this.getObjectId(), e3.displayId = this.getDisplayId(), this._cache.optFeature = e3, e3;
    }
    return this._cache.optFeature;
  }
  getXHydrated() {
    const e3 = this._header.centroid[2 * this._featureIndex], t5 = this.getQuantizationTransform();
    return t(t5) ? e3 : e3 * t5.scale[0] + t5.translate[0];
  }
  getYHydrated() {
    const e3 = this._header.centroid[2 * this._featureIndex + 1], t5 = this.getQuantizationTransform();
    return t(t5) ? e3 : t5.translate[1] - e3 * t5.scale[1];
  }
  getX() {
    return this._header.centroid[2 * this._featureIndex] * this._sx + this._tx;
  }
  getY() {
    return this._header.centroid[2 * this._featureIndex + 1] * this._sy + this._ty;
  }
  readLegacyPointGeometry() {
    return { x: this.getX(), y: this.getY() };
  }
  readLegacyGeometry(e3) {
    const t5 = this.readGeometry(e3);
    return ut(t5, this.geometryType, false, false);
  }
  readLegacyCentroid() {
    const e3 = this.readCentroid();
    if (!e3) return null;
    const [t5, r6] = e3.coords;
    return { x: t5, y: r6 };
  }
  readGeometryArea() {
    return this._cache.area || this.readGeometry(true), this._cache.area;
  }
  readUnquantizedGeometry(e3 = false) {
    if (void 0 === this._cache.unquantGeometry) {
      const t5 = this.readGeometry(e3);
      if (!t5) return this._cache.unquantGeometry = void 0, null;
      const r6 = I(t5.coords.length).decoded, s9 = t5.clone(r6), i5 = s9.coords;
      let n7 = 0;
      for (const e4 of s9.lengths) {
        for (let t6 = 1; t6 < e4; t6++) {
          const e5 = 2 * (n7 + t6), r7 = 2 * (n7 + t6 - 1);
          i5[e5] += i5[r7], i5[e5 + 1] += i5[r7 + 1];
        }
        n7 += e4;
      }
      return this._cache.unquantGeometry = s9, s9;
    }
    return this._cache.unquantGeometry;
  }
  readHydratedGeometry() {
    if (this._isPoints) {
      if (this._header.centroid[2 * this._featureIndex] === l4) return null;
      const e4 = this.getXHydrated(), t6 = this.getYHydrated();
      return new t3([], [e4, t6]);
    }
    const e3 = this.readGeometry();
    if (!e3) return null;
    const t5 = e3.clone(), r6 = this.getQuantizationTransform();
    return r(r6) && wt(t5, t5, this.hasZ, this.hasM, r6), t5;
  }
  readGeometry(e3 = false) {
    if (void 0 === this._cache.geometry) {
      let r6 = null;
      if (this._isPoints) {
        if (this._header.centroid[2 * this._featureIndex] === l4) return null;
        const e4 = this.getX(), t5 = this.getY();
        r6 = new t3([], [e4, t5]);
      } else {
        const s9 = this._header.offsets.geometry[this._featureIndex], i5 = this._reader;
        if (0 === s9) {
          const e4 = this._readServerCentroid();
          if (!e4) return null;
          const [t5, r7] = e4.coords;
          return this.createQuantizedExtrudedQuad(t5, r7);
        }
        i5.move(s9);
        try {
          if (r6 = e3 ? this._parseGeometryForDisplay(i5) : this._parseGeometry(i5), null === r6) {
            const e4 = this._readServerCentroid();
            if (!e4) return null;
            const [t5, r7] = e4.coords;
            return this.createQuantizedExtrudedQuad(t5, r7);
          }
        } catch (t5) {
          return console.error("Failed to parse geometry!", t5), null;
        }
      }
      return this._cache.geometry = r6, r6;
    }
    return this._cache.geometry;
  }
  readCentroid() {
    if (void 0 === this._cache.centroid) {
      let e3;
      return e3 = this._computeCentroid(), e3 || (e3 = this._readServerCentroid()), this._cache.centroid = e3 ?? void 0, e3 ?? null;
    }
    return this._cache.centroid;
  }
  copy() {
    const e3 = this._reader.clone(), t5 = new _G(this.instance, e3, this._header, this.fullSchema());
    return this.copyInto(t5), t5;
  }
  next() {
    for (this._cache.area = 0, this._cache.unquantGeometry = void 0, this._cache.geometry = void 0, this._cache.centroid = void 0, this._cache.legacyFeature = void 0, this._cache.optFeature = void 0; ++this._featureIndex < this._size && !this._getExists(); ) ;
    return this._featureIndex < this._size;
  }
  _readAttribute(e3, t5) {
    const r6 = this._header.hasField(e3) ? e3 : p2(e3), s9 = this._header.getFieldIndex(r6);
    if (null == s9) return;
    const i5 = this._readAttributeAtIndex(s9);
    if (!t5) return i5;
    if (null == i5) return i5;
    return this._header.isDateField(r6) ? new Date(i5) : i5;
  }
  _readAttributes() {
    const e3 = {};
    return this._header.fields.forEach(({ fieldName: t5, index: r6 }) => {
      e3[t5] = this._readAttributeAtIndex(r6);
    }), e3;
  }
  copyInto(e3) {
    super.copyInto(e3), e3._featureIndex = this._featureIndex, e3._featureOffset = this._featureOffset, e3._hasNext = this._hasNext;
  }
  _readAttributeAtIndex(e3) {
    const t5 = this._header.offsets.attributes[this._featureIndex * this._header.fieldCount + e3], r6 = this._reader;
    return r6.move(t5), S(r6);
  }
  _readServerCentroid() {
    const e3 = this._header.centroid[2 * this._featureIndex] + this._tx, t5 = this._header.centroid[2 * this._featureIndex + 1] + this._ty;
    return e3 === l4 ? null : new t3([], [e3, t5]);
  }
  _parseGeometry(e3) {
    const t5 = 2, r6 = 3, s9 = e3.asUnsafe(), i5 = s9.getLength(), n7 = s9.pos() + i5, a10 = [], h6 = [];
    for (; s9.pos() < n7 && s9.next(); ) switch (s9.tag()) {
      case t5: {
        const e4 = s9.getUInt32(), t6 = s9.pos() + e4;
        for (; s9.pos() < t6; ) h6.push(s9.getUInt32());
        break;
      }
      case r6: {
        const e4 = s9.getUInt32(), t6 = s9.pos() + e4;
        for (a10.push(s9.getSInt32() + this._tx), a10.push(s9.getSInt32() + this._ty), this.hasZ && s9.getSInt32(), this.hasM && s9.getSInt32(); s9.pos() < t6; ) a10.push(s9.getSInt32()), a10.push(s9.getSInt32()), this.hasZ && s9.getSInt32(), this.hasM && s9.getSInt32();
        break;
      }
      default:
        s9.skip();
    }
    return new t3(h6, a10);
  }
  _parseGeometryForDisplay(e3) {
    const t5 = 2, r6 = 3, s9 = e3.asUnsafe(), n7 = s9.getLength(), a10 = s9.pos() + n7, h6 = [], o7 = [];
    let u3 = 0, c9 = 0, l7 = null, _3 = 0;
    const f10 = "esriGeometryPolygon" === this.geometryType;
    for (; s9.pos() < a10 && s9.next(); ) switch (s9.tag()) {
      case t5: {
        const e4 = s9.getUInt32(), t6 = s9.pos() + e4;
        for (; s9.pos() < t6; ) {
          const e5 = s9.getUInt32();
          h6.push(e5), u3 += e5;
        }
        l7 = I(2 * u3).delta;
        break;
      }
      case r6: {
        s9.getUInt32();
        const e4 = 2 + (this.hasZ ? 1 : 0) + (this.hasM ? 1 : 0);
        i(l7);
        for (const t6 of h6) if (c9 + e4 * t6 > l7.length) for (let e5 = 0; e5 < t6; e5++) s9.getSInt32(), s9.getSInt32(), this.hasZ && s9.getSInt32(), this.hasM && s9.getSInt32();
        else if (f10 && g6) {
          const e5 = this.getAreaSimplificationThreshold(t6, this._header.vertexCount);
          let r7 = 2, i5 = 1;
          const n8 = false;
          let a11 = s9.getSInt32(), h7 = s9.getSInt32();
          l7[c9++] = a11, l7[c9++] = h7, this.hasZ && s9.getSInt32(), this.hasM && s9.getSInt32();
          let d9 = s9.getSInt32(), u4 = s9.getSInt32();
          for (this.hasZ && s9.getSInt32(), this.hasM && s9.getSInt32(); r7 < t6; ) {
            let t7 = s9.getSInt32(), n9 = s9.getSInt32();
            this.hasZ && s9.getSInt32(), this.hasM && s9.getSInt32();
            const o8 = a11 + d9, g8 = h7 + u4;
            F(a11, h7, o8, g8, o8 + t7, g8 + n9) >= e5 ? (_3 += -0.5 * (o8 - a11) * (g8 + h7), i5 > 1 && v3(l7[c9 - 2], l7[c9 - 1], d9, u4) ? (l7[c9 - 2] += d9, l7[c9 - 1] += u4) : (l7[c9++] = d9, l7[c9++] = u4, i5++), a11 = o8, h7 = g8) : (t7 += d9, n9 += u4), d9 = t7, u4 = n9, r7++;
          }
          i5 < 3 || n8 ? c9 -= 2 * i5 : (_3 += -0.5 * (a11 + d9 - a11) * (h7 + u4 + h7), v3(l7[c9 - 2], l7[c9 - 1], d9, u4) ? (l7[c9 - 2] += d9, l7[c9 - 1] += u4, o7.push(i5)) : (l7[c9++] = d9, l7[c9++] = u4, o7.push(++i5)));
        } else {
          let e5 = 0, r7 = s9.getSInt32(), i5 = s9.getSInt32();
          this.hasZ && s9.getSInt32(), this.hasM && s9.getSInt32(), l7[c9++] = r7, l7[c9++] = i5, e5 += 1;
          for (let n8 = 1; n8 < t6; n8++) {
            const t7 = s9.getSInt32(), a11 = s9.getSInt32(), h7 = r7 + t7, o8 = i5 + a11;
            _3 += -0.5 * (h7 - r7) * (o8 + i5), this.hasZ && s9.getSInt32(), this.hasM && s9.getSInt32(), n8 > 2 && v3(l7[c9 - 2], l7[c9 - 1], t7, a11) ? (l7[c9 - 2] += t7, l7[c9 - 1] += a11) : (l7[c9++] = t7, l7[c9++] = a11, e5 += 1), r7 = h7, i5 = o8;
          }
          o7.push(e5);
        }
        break;
      }
      default:
        s9.skip();
    }
    if (this._cache.area = _3, !o7.length) return null;
    if (this._tx || this._ty) {
      let e4 = 0;
      i(l7);
      for (const t6 of o7) l7[2 * e4] += this._tx, l7[2 * e4 + 1] += this._ty, e4 += t6;
    }
    return new t3(o7, l7);
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/controllers/support/sourceAdapters.js
var p3 = class {
  constructor(e3) {
    this.service = e3;
  }
  destroy() {
  }
};
function f6(e3) {
  return Array.isArray(e3.source);
}
function l5(e3) {
  return "ogc-source" === (e3 == null ? void 0 : e3.type);
}
function y4(e3) {
  const { capabilities: t5 } = e3;
  return l5(e3.source) ? new q4(e3) : f6(e3) ? new d4(e3) : t5.query.supportsFormatPBF && has("featurelayer-pbf") ? new v4(e3) : new F2(e3);
}
async function h3(e3) {
  const t5 = new h2();
  return await t5.open(e3, {}), t5;
}
var d4 = class extends p3 {
  constructor(e3) {
    super(e3), this._portsOpen = h3(e3.source).then((e4) => this.client = e4);
  }
  destroy() {
    this.client.close(), this.client = null;
  }
  async executeQuery(e3, t5) {
    await this._portsOpen;
    const r6 = await this.client.invoke("queryFeatures", e3.toJSON(), t5);
    return c5.fromFeatureSet(r6, this.service);
  }
};
var v4 = class extends p3 {
  async executeQuery(e3, t5) {
    const { data: r6 } = await d2(this.service.source, e3, t5), s9 = !e3.quantizationParameters;
    return G2.fromBuffer(r6, this.service, s9);
  }
};
var F2 = class extends p3 {
  async executeQuery(r6, o7) {
    var _a;
    const { source: n7, capabilities: m4, spatialReference: p5, objectIdField: f10, geometryType: l7 } = this.service;
    if (r(r6.quantizationParameters) && !m4.query.supportsQuantization) {
      const e3 = r6.clone(), m5 = s5(e(e3.quantizationParameters));
      e3.quantizationParameters = null;
      const { data: l8 } = await c3(n7, e3, p5, o7), y7 = at(l8, f10);
      return dt(m5, y7), c5.fromOptimizedFeatureSet(y7, this.service);
    }
    const { data: y6 } = await c3(n7, r6, this.service.spatialReference, o7);
    return "esriGeometryPoint" === l7 && (y6.features = (_a = y6.features) == null ? void 0 : _a.filter((t5) => {
      if (r(t5.geometry)) {
        const e3 = t5.geometry;
        return Number.isFinite(e3.x) && Number.isFinite(e3.y);
      }
      return true;
    })), c5.fromFeatureSet(y6, this.service);
  }
};
var q4 = class extends p3 {
  async executeQuery(e3, r6) {
    const { capabilities: i5 } = this.service;
    if (e3.quantizationParameters && !i5.query.supportsQuantization) {
      const i6 = e3.clone(), n8 = s5(e(i6.quantizationParameters));
      i6.quantizationParameters = null;
      const c9 = await q3(this.service.source, e3, r6);
      return dt(n8, c9), c5.fromOptimizedFeatureSet(c9, this.service);
    }
    const n7 = await q3(this.service.source, e3, r6);
    return c5.fromOptimizedFeatureSet(n7, this.service);
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/support/UpdateToken.js
var t4 = class _t {
  constructor() {
    this.version = 0, this.source = false, this.targets = { feature: false, aggregate: false }, this.storage = { filters: false, data: false }, this.mesh = false, this.queryFilter = false, this.why = { mesh: [], source: [] };
  }
  static create(e3) {
    const s9 = new _t();
    for (const t5 in e3) {
      const r6 = e3[t5];
      if ("object" == typeof r6) for (const e4 in r6) {
        const a10 = r6[e4];
        s9[t5][e4] = a10;
      }
      s9[t5] = r6;
    }
    return s9;
  }
  static empty() {
    return _t.create({});
  }
  static all() {
    return _t.create({ source: true, targets: { feature: true, aggregate: true }, storage: { filters: true, data: true }, mesh: true });
  }
  unset(t5) {
    this.version = t5.version, t5.source && (this.source = false), t5.targets.feature && (this.targets.feature = false), t5.targets.aggregate && (this.targets.aggregate = false), t5.storage.filters && (this.storage.filters = false), t5.storage.data && (this.storage.data = false), t5.mesh && (this.mesh = false), t5.queryFilter && (this.queryFilter = false);
  }
  any() {
    return this.source || this.mesh || this.storage.filters || this.storage.data || this.targets.feature || this.targets.aggregate || this.queryFilter;
  }
  describe() {
    let t5 = 0, e3 = "";
    if (this.mesh) {
      t5 += 20, e3 += "-> (20) Mesh needs update\n";
      for (const t6 of this.why.mesh) e3 += `    + ${t6}
`;
    }
    if (this.source) {
      t5 += 10, e3 += "-> (10) The source needs update\n";
      for (const t6 of this.why.source) e3 += `    + ${t6}
`;
    }
    this.targets.feature && (t5 += 5, e3 += "-> (5) Feature target parameters changed\n"), this.storage.filters && (t5 += 5, e3 += "-> (5) Feature filter parameters changed\n"), this.targets.aggregate && (t5 += 4, e3 += "-> (4) Aggregate target parameters changed\n"), this.storage.data && (t5 += 1, e3 += "-> (1) Texture storage parameters changed");
    const s9 = t5 < 5 ? "Fastest" : t5 < 10 ? "Fast" : t5 < 15 ? "Moderate" : t5 < 20 ? "Slow" : "Very Slow";
    console.debug(`Applying ${s9} update of cost ${t5}/45 `), console.debug(e3);
  }
  toJSON() {
    return { queryFilter: this.queryFilter, source: this.source, targets: this.targets, storage: this.storage, mesh: this.mesh };
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/sources/DataTileSubscription.js
var a6 = class {
  constructor(t5, s9) {
    this.requests = { done: new Array(), stream: new s7(10) }, this._edits = null, this._abortController = new AbortController(), this._version = 0, this._done = false, this.didSend = false, this.tile = t5, this._version = s9;
  }
  get signal() {
    return this._abortController.signal;
  }
  get options() {
    return { signal: this._abortController.signal };
  }
  get empty() {
    return !this.requests.done.length && t(this.edits);
  }
  get edits() {
    return this._edits;
  }
  get done() {
    return this._done;
  }
  end() {
    this._done = true;
  }
  clear() {
    this.requests.done = [];
  }
  applyUpdate(e3) {
    this.requests.done.forEach((t5) => t5.message.status.unset(e3)), this._version = e3.version, r(this._edits) && this._edits.status.unset(e3);
  }
  add(e3) {
    e3.message.status = e3.message.status ?? t4.empty(), e3.message.status.version = this._version, has("esri-2d-update-debug") && console.debug(this.tile.id, "DataTileSubscription:add", this._version), e3.message.end && this.requests.done.forEach((e4) => {
      r(e4.message) && e4.message.end && (e4.message.end = false);
    }), this.requests.done.push(e3);
  }
  edit(e3, a10) {
    const o7 = e3.getQuantizationTransform(), n7 = e3.fullSchema(), h6 = Array.from(e3.features()).filter(r), u3 = [...a10, ...h6.map((e4) => e4.objectId)];
    if (this.removeIds(u3), this._invalidate(), t(this._edits)) return void (this._edits = { type: "append", addOrUpdate: c5.fromOptimizedFeatures(h6, n7, e(o7)), id: this.tile.id, status: t4.empty(), end: true });
    this.requests.done.forEach((e4) => e4.message.end = false);
    e(this._edits.addOrUpdate).append(e3.features());
  }
  *readers() {
    for (const { message: e3 } of this.requests.done) r(e3.addOrUpdate) && (yield e3.addOrUpdate);
    r(this._edits) && r(this._edits.addOrUpdate) && (yield this._edits.addOrUpdate);
  }
  _invalidate() {
    for (const e3 of this.requests.done) e3.message.status = t4.empty();
    r(this._edits) && (this._edits.status = t4.empty());
  }
  removeIds(e3) {
    this._invalidate();
    for (const { message: t5 } of this.requests.done) {
      const r6 = t5.addOrUpdate;
      r(r6) && (r6.removeIds(e3), r6.isEmpty && (has("esri-2d-update-debug") && console.debug("Removing FeatureSetReader"), t5.addOrUpdate = null));
    }
    r(this._edits) && r(this._edits.addOrUpdate) && this._edits.addOrUpdate.removeIds(e3), this.requests.done = this.requests.done.filter((e4) => e4.message.addOrUpdate || e4.message.end);
  }
  abort() {
    this._abortController.abort();
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/sources/DataTileSource.js
function m3(e3, s9) {
  const t5 = /* @__PURE__ */ new Set();
  return e3 && e3.forEach((e4) => t5.add(e4)), s9 && s9.forEach((e4) => t5.add(e4)), t5.has("*") ? ["*"] : Array.from(t5);
}
var f7 = class extends v {
  constructor(e3) {
    super(), this.events = new n(), this._resolver = D(), this._didEdit = false, this._subscriptions = /* @__PURE__ */ new Map(), this._outSR = e3.outSR, this._serviceInfo = e3.serviceInfo, this._onTileUpdateMessage = e3.onMessage;
  }
  async _onMessage(e3) {
    const s9 = this._subscriptions.get(e3.id);
    if (!s9) return;
    const t5 = { ...e3, remove: e3.remove ?? [], status: e3.status ?? t4.empty() };
    return g2(this._onTileUpdateMessage(t5, s9.options));
  }
  update(s9, t5) {
    var _a;
    const i5 = t5.fields.length;
    t5.outFields = m3((_a = this._schema) == null ? void 0 : _a.outFields, t5.outFields), t5.outFields = t5.outFields.length >= 0.75 * i5 ? ["*"] : t5.outFields, t5.outFields.sort();
    const r6 = m(this._schema, t5);
    if (!r6) return;
    has("esri-2d-update-debug") && console.debug("Applying Update - Source:", r6);
    const o7 = "orderByFields" in this._serviceInfo && this._serviceInfo.orderByFields ? this._serviceInfo.orderByFields : this._serviceInfo.objectIdField + " ASC", n7 = { returnCentroid: "esriGeometryPolygon" === this._serviceInfo.geometryType, returnGeometry: true, timeReferenceUnknownClient: "stream" !== this._serviceInfo.type && this._serviceInfo.timeReferenceUnknownClient, outFields: t5.outFields, outSpatialReference: this._outSR, orderByFields: [o7], where: t5.definitionExpression || "1=1", gdbVersion: t5.gdbVersion, historicMoment: t5.historicMoment, timeExtent: t5.timeExtent ? T2.fromJSON(t5.timeExtent) : null }, c9 = this._schema && a4(r6, "outFields");
    this._schema && y2(r6, ["timeExtent", "definitionExpression", "gdbVersion", "historicMoment", "customParameters"]) && (s9.why.mesh.push("Layer filter and/or custom parameters changed"), s9.why.source.push("Layer filter and/or custom parameters changed"), s9.mesh = true, s9.source = true, s9.queryFilter = true), c9 && (s9.why.source.push("Layer required fields changed"), s9.source = true), m(n7, this._queryInfo) && (this._queryInfo = n7), this._schema = t5, this._resolver.resolve();
  }
  whenInitialized() {
    return this._resolver.promise;
  }
  async applyUpdate(e3) {
    if (e3.queryFilter || e3.source && this._didEdit) return this.refresh(e3.version), void (this._didEdit = false);
    this._subscriptions.forEach((s9) => s9.applyUpdate(e3)), await this.resend();
  }
  refresh(e3, s9) {
    for (const t5 of this._tiles()) this.unsubscribe(t5), this.subscribe(t5, e3);
  }
  subscribe(e3, s9) {
    const t5 = new a6(e3, s9);
    this._subscriptions.set(e3.id, t5);
  }
  unsubscribe(e3) {
    const s9 = this.getSubscription(e3.id);
    r(s9) && s9.abort(), this._subscriptions.delete(e3.id);
  }
  createQuery(e3 = {}) {
    const s9 = this._queryInfo.historicMoment ? new Date(this._queryInfo.historicMoment) : null;
    return new x({ ...this._queryInfo, historicMoment: s9, ...e3 });
  }
  getSubscription(e3) {
    return this._subscriptions.has(e3) ? this._subscriptions.get(e3) : null;
  }
  async queryLastEditDate() {
    throw new Error("Service does not support query type");
  }
  async query(e3, s9) {
    throw new Error("Service does not support query");
  }
  *_tiles() {
    const e3 = Array.from(this._subscriptions.values());
    for (const s9 of e3) yield s9.tile;
  }
  async edit(e3, s9) {
    const t5 = Array.from(this._subscriptions.values()), i5 = t5.map(({ tile: e4 }) => e4);
    for (const r6 of t5) r6.removeIds(s9);
    if (e3.length) {
      const t6 = i5.map((s10) => {
        const t7 = this.createTileQuery(s10);
        return t7.objectIds = e3, { tile: s10, query: t7 };
      }).map(async ({ tile: e4, query: s10 }) => ({ tile: e4, result: await this.query(s10, { query: { tile: has("esri-tiles-debug") ? e4.id.replace(/\//g, ".") : void 0 } }), query: s10 })), r6 = (await L(t6)).map(async ({ tile: t7, result: i6 }) => {
        if (!i6.hasFeatures && !s9.length && !e3.length) return;
        const r7 = this._subscriptions.get(t7.key.id);
        r7 && r7.edit(i6, e3);
      });
      await E(r6);
    }
    this._didEdit = true;
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/sources/BaseFeatureSource.js
var p4 = 4;
var d5 = class extends f7 {
  constructor(e3) {
    super(e3), this.type = "feature", this.mode = "on-demand", this._adapter = y4(e3.serviceInfo), this._queue = new l3({ concurrency: 8, process: async (e4) => {
      var _a, _b;
      if (f(e4), r(e4.tile)) {
        const t5 = e4.tile.key.id, { signal: r6 } = e4, s9 = has("esri-tiles-debug") ? { tile: t5.replace(/\//g, "."), depth: e4.depth } : void 0, i5 = await this._adapter.executeQuery(e4.query, { signal: r6, query: { ...s9, ...(_a = this._schema) == null ? void 0 : _a.customParameters } });
        return i5.level = e4.tile.key.level, i5;
      }
      return this._adapter.executeQuery(e4.query, { ...e4, query: (_b = this._schema) == null ? void 0 : _b.customParameters });
    } }), this._patchQueue = new l3({ concurrency: 8, process: async (e4) => {
      var _a, _b;
      if (f(e4), r(e4.tile)) {
        const t5 = e4.tile.key.id, { signal: r6 } = e4, s9 = has("esri-tiles-debug") ? { tile: t5.replace(/\//g, "."), depth: e4.depth } : void 0, i5 = await this._adapter.executeQuery(e4.query, { signal: r6, query: { ...s9, ...(_a = this._schema) == null ? void 0 : _a.customParameters } });
        return i5.level = e4.tile.key.level, i5;
      }
      return this._adapter.executeQuery(e4.query, { ...e4, query: (_b = this._schema) == null ? void 0 : _b.customParameters });
    } });
  }
  destroy() {
    super.destroy(), this._adapter.destroy(), this._queue.destroy(), this._patchQueue.destroy();
  }
  get updating() {
    return !!this._queue.length || Array.from(this._subscriptions.values()).some((e3) => !e3.done);
  }
  get maxRecordCountFactor() {
    const { query: e3 } = this._serviceInfo.capabilities;
    return e3.supportsMaxRecordCountFactor ? p4 : null;
  }
  get maxPageSize() {
    const { query: e3 } = this._serviceInfo.capabilities;
    return (e3.maxRecordCount ?? 8e3) * l(this.maxRecordCountFactor, 1);
  }
  get pageSize() {
    return Math.min(8e3, this.maxPageSize);
  }
  enableEvent(e3, t5) {
  }
  subscribe(e3, s9) {
    super.subscribe(e3, s9);
    const i5 = this._subscriptions.get(e3.id);
    this._fetchDataTile(e3).catch((s10) => {
      j(s10) || s.getLogger("esri.views.2d.layers.features.sources.BaseFeatureSource").error(new s2("mapview-query-error", "Encountered error when fetching tile", { tile: e3, error: s10 }));
    }).then(() => i5.end());
  }
  unsubscribe(e3) {
    super.unsubscribe(e3);
  }
  readers(e3) {
    return this._subscriptions.get(e3).readers();
  }
  async query(e3, t5 = {}) {
    var _a;
    const r6 = t5.query ?? {};
    return this._adapter.executeQuery(e3, { ...t5, query: { ...r6, ...(_a = this._schema) == null ? void 0 : _a.customParameters } });
  }
  async queryLastEditDate() {
    const t5 = this._serviceInfo.source, r6 = { ...t5.query, f: "json" };
    return (await U3(t5.path, { query: r6, responseType: "json" })).data.editingInfo.lastEditDate;
  }
  createTileQuery(e3, t5 = {}) {
    const r6 = this._serviceInfo.geometryType, s9 = this.createQuery(t5);
    s9.quantizationParameters = t5.quantizationParameters ?? e3.getQuantizationParameters(), s9.resultType = "tile", s9.geometry = e3.extent, this._serviceInfo.capabilities.query.supportsQuantization ? "esriGeometryPolyline" === r6 && (s9.maxAllowableOffset = e3.resolution * has("feature-polyline-generalization-factor")) : "esriGeometryPolyline" !== r6 && "esriGeometryPolygon" !== r6 || (s9.maxAllowableOffset = e3.resolution, "esriGeometryPolyline" === r6 && (s9.maxAllowableOffset *= has("feature-polyline-generalization-factor")));
    const i5 = this._serviceInfo.capabilities.query;
    return s9.defaultSpatialReferenceEnabled = i5.supportsDefaultSpatialReference, s9.compactGeometryEnabled = i5.supportsCompactGeometry, s9;
  }
  async _executePatchQuery(e3, t5, r6, i5) {
    const a10 = t5.clone();
    a10.outFields = [this._serviceInfo.objectIdField, ...r6], a10.returnCentroid = false, a10.returnGeometry = false;
    const o7 = r(a10.start) ? a10.start / 8e3 : 0, n7 = i5.signal;
    return this._patchQueue.push({ tile: e3, query: a10, signal: n7, depth: o7 });
  }
  async _resend(e3, t5) {
    const { query: r6, message: i5 } = e3, n7 = r(r6.outFields) ? r6.outFields : [], u3 = this._queryInfo.outFields, c9 = u3.filter((e4) => !n7.includes(e4));
    if (t(i5.addOrUpdate)) this._onMessage({ ...i5, type: "append" });
    else if (c9.length) try {
      const e4 = this._subscriptions.get(i5.id).tile, s9 = await this._executePatchQuery(e4, r6, c9, t5);
      f(t5), r6.outFields = u3, i5.addOrUpdate.joinAttributes(s9), this._onMessage({ ...i5, end: i5.end, type: "append" });
    } catch (l7) {
    }
    else this._onMessage({ ...i5, type: "append" });
  }
  async _resendSubscription(e3) {
    if (has("esri-2d-update-debug") && console.debug(e3.tile.id, "Resend Subscription"), e3.empty) return this._onMessage({ id: e3.tile.id, addOrUpdate: null, end: false, type: "append" });
    const t5 = e3.signal;
    for (const r6 of e3.requests.done) await this._resend(r6, { signal: t5 });
    return r(e3.edits) ? this._onMessage(e3.edits) : void 0;
  }
  async resend() {
    const e3 = Array.from(this._subscriptions.values());
    await Promise.all(e3.map((e4) => this._resendSubscription(e4)));
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/sources/DrillDownFeatureSource.js
var s8 = has("esri-mobile");
var i4 = { maxDrillLevel: s8 ? 1 : 4, maxRecordCountFactor: s8 ? 1 : 3 };
var a7 = class extends d5 {
  constructor(e3) {
    super(e3);
  }
  async _fetchDataTile(r6) {
    const s9 = this._serviceInfo.capabilities.query.supportsMaxRecordCountFactor, a10 = this._subscriptions.get(r6.key.id), o7 = a10.signal, n7 = r6.getQuantizationParameters();
    let c9 = 0;
    const d9 = async (u3, l7) => {
      const p5 = this._queryInfo, m4 = this.createTileQuery(u3, { maxRecordCountFactor: s9 ? i4.maxRecordCountFactor : void 0, returnExceededLimitFeatures: false, quantizationParameters: n7 });
      c9++;
      try {
        const t5 = await this._queue.push({ tile: r6, query: m4, signal: o7, depth: l7 });
        if (c9--, f(o7), !t5) return;
        if (p5 !== this._queryInfo) return void d9(u3, l7);
        if (t5.exceededTransferLimit && l7 < i4.maxDrillLevel) {
          for (const e3 of u3.createChildTiles()) d9(e3, l7 + 1);
          return;
        }
        const s10 = { id: r6.id, addOrUpdate: t5, end: 0 === c9, type: "append" };
        a10.add({ query: m4, message: s10 }), this._onMessage(s10);
      } catch (h6) {
        j(h6) || this._onMessage({ id: r6.id, addOrUpdate: null, end: true, type: "append" });
      }
    };
    d9(r6, 0);
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/sources/PagedFeatureSource.js
var n5 = class extends d5 {
  constructor(e3) {
    super(e3);
  }
  async _fetchDataTile(r6) {
    const i5 = 6, o7 = 20, n7 = this._subscriptions.get(r6.key.id);
    let d9 = false, c9 = 0, u3 = 0;
    const p5 = (e3, t5) => {
      u3--, f(n7);
      const a10 = r6.id, i6 = e3.reader, o8 = e3.query;
      if (!i6.exceededTransferLimit) {
        if (d9 = true, 0 !== t5 && !i6.hasFeatures) {
          const e5 = { id: a10, addOrUpdate: i6, end: 0 === u3, type: "append" };
          return n7.add({ message: e5, query: o8 }), void this._onMessage(e5);
        }
        const e4 = { id: a10, addOrUpdate: i6, end: 0 === u3, type: "append" };
        return n7.add({ message: e4, query: o8 }), void this._onMessage(e4);
      }
      const c10 = { id: a10, addOrUpdate: i6, end: d9 && 0 === u3, type: "append" };
      n7.add({ message: c10, query: o8 }), this._onMessage(c10);
    };
    let h6 = 0, m4 = 0;
    for (; !d9 && m4++ < o7; ) {
      let o8;
      for (let s9 = 0; s9 < h6 + 1; s9++) {
        const s10 = c9++;
        u3++, o8 = this._fetchDataTilePage(r6, s10, n7).then((e3) => e3 && p5(e3, s10)).catch((s11) => {
          d9 = true, j(s11) || (s.getLogger("esri.views.2d.layers.features.sources.PagedFeatureSource").error(new s2("mapview-query-error", "Encountered error when fetching tile", { tile: r6, error: s11 })), this._onMessage({ id: r6.id, addOrUpdate: null, end: d9, type: "append" }));
        });
      }
      await o8, f(n7), h6 = Math.min(h6 + 2, i5);
    }
  }
  async _fetchDataTilePage(e3, t5, a10) {
    f(a10);
    const o7 = this._queryInfo, n7 = { start: this.pageSize * t5, num: this.pageSize, returnExceededLimitFeatures: true, quantizationParameters: e3.getQuantizationParameters() };
    r(this.maxRecordCountFactor) && (n7.maxRecordCountFactor = this.maxRecordCountFactor);
    const d9 = this.createTileQuery(e3, n7);
    try {
      const r6 = a10.signal, i5 = await this._queue.push({ tile: e3, query: d9, signal: r6, depth: t5 });
      return f(a10), i5 ? o7 !== this._queryInfo ? this._fetchDataTilePage(e3, t5, a10) : { reader: i5, query: d9 } : null;
    } catch (c9) {
      return b(c9), null;
    }
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/sources/SnapshotFeatureSource.js
function h4(t5, e3, s9) {
  const r6 = t5.getXHydrated(), o7 = t5.getYHydrated(), a10 = e3.getColumnForX(r6), n7 = Math.floor(e3.normalizeCol(a10));
  return `${s9}/${Math.floor(e3.getRowForY(o7))}/${n7}`;
}
function l6(t5, e3) {
  if (t(t5)) return null;
  const s9 = e3.transform, o7 = t5.getQuantizationTransform();
  if (t(o7)) {
    const [e4, r6] = s9.scale, [o8, a11] = s9.translate, n8 = -o8 / e4, i6 = 1 / e4, d10 = a11 / r6, u4 = 1 / -r6;
    return t5.transform(n8, d10, i6, u4);
  }
  const [a10, n7] = o7.scale, [i5, d9] = o7.translate, [u3, h6] = s9.scale, [l7, c9] = s9.translate, g8 = a10 / u3, _3 = (i5 - l7) / u3, p5 = n7 / h6, f10 = (-d9 + c9) / h6;
  return t5.transform(_3, f10, g8, p5);
}
var c6 = class extends d5 {
  constructor(t5) {
    super(t5), this.mode = "snapshot", this._loading = true, this._controller = new AbortController(), this._downloadPromise = null, this._didSendEnd = false, this._queries = new Array(), this._invalidated = false, this._hasAggregates = false, this._random = new t2(1e3), this._store = t5.store, this._markedIdsBufId = this._store.storage.createBitset();
  }
  destroy() {
    super.destroy(), this._controller.abort();
  }
  get loading() {
    return this._loading;
  }
  get _signal() {
    return this._controller.signal;
  }
  update(t5, s9) {
    var _a;
    super.update(t5, s9), null == this._featureCount && (this._featureCount = s9.initialFeatureCount), r(s9.changedFeatureCount) && (this._featureCount = s9.changedFeatureCount), this._hasAggregates = !!((_a = t5.targets) == null ? void 0 : _a.aggregate);
  }
  async resend(t5 = false) {
    if (await this._downloadPromise, this._invalidated || t5) {
      const t6 = c(this._featureCount, "Expected featureCount to be defined");
      return this._invalidated = false, this._subscriptions.forEach((t7) => t7.clear()), this._downloadPromise = this._download(t6), void await this._downloadPromise;
    }
    const e3 = this._queries.map(({ query: t6, reader: e4 }) => this._sendPatchQuery(t6, e4));
    await Promise.all(e3), this._subscriptions.forEach((t6) => {
      t6.requests.done.forEach((t7) => this._onMessage(t7.message));
    });
  }
  async refresh(t5, e3) {
    e3 && (this._featureCount = e3.featureCount), await this.resend(true);
  }
  async _sendPatchQuery(t5, s9) {
    const r6 = r(t5.outFields) ? t5.outFields : [], a10 = this._queryInfo.outFields, n7 = a10.filter((t6) => !r6.includes(t6));
    if (!n7.length) return;
    const i5 = t5.clone(), d9 = this._signal;
    i5.returnGeometry = false, i5.returnCentroid = false, i5.outFields = n7, t5.outFields = a10;
    const u3 = await this._queue.push({ query: i5, depth: 0, signal: d9 });
    f({ signal: d9 }), s9.joinAttributes(u3);
  }
  async _fetchDataTile(t5) {
    if (!this._downloadPromise) {
      const t6 = c(this._featureCount, "Expected featureCount to be defined");
      this._downloadPromise = this._download(t6);
    }
    const e3 = this._store.search(t5), r6 = this._subscriptions.get(t5.key.id), o7 = e3.length - 1;
    for (let s9 = 0; s9 < o7; s9++) {
      const o8 = l6(e3[s9], t5), n8 = { type: "append", id: t5.id, addOrUpdate: o8, end: false, status: t4.empty() };
      r6.add({ query: null, message: n8 }), this._hasAggregates || await U(1), this._onMessage(n8);
    }
    const n7 = l6(o7 >= 0 ? e3[o7] : null, t5), i5 = this._didSendEnd, d9 = { type: "append", id: t5.id, addOrUpdate: n7, end: i5, status: t4.empty() };
    r6.add({ query: null, message: d9 }), this._onMessage(d9);
  }
  async _download(e3) {
    try {
      await this.whenInitialized();
      const t5 = this._store.storage.getBitset(this._markedIdsBufId), s9 = /* @__PURE__ */ new Set();
      t5.clear();
      const r6 = Math.ceil(e3 / this.pageSize), o7 = Array.from({ length: r6 }, (t6, e4) => e4).sort((t6, e4) => this._random.getInt() - this._random.getInt()).map((e4) => this._downloadPage(e4, t5, s9));
      await Promise.all(o7), this._store.sweepFeatures(t5, this._store.storage), this._store.sweepFeatureSets(s9);
    } catch (s9) {
      s.getLogger("esri.views.2d.layers.features.sources.SnapshotFeatureSource").error("mapview-snapshot-source", "Encountered and error when downloading feature snapshot", s9);
    }
    this._sendEnd(), this._loading = false;
  }
  async _downloadPage(t5, s9, r6) {
    const a10 = this.pageSize, n7 = { start: t5 * a10, num: a10, cacheHint: true };
    r(this.maxRecordCountFactor) && (n7.maxRecordCountFactor = this.maxRecordCountFactor);
    const i5 = this.createQuery(n7), d9 = this._signal, u3 = await this._queue.push({ query: i5, depth: t5, signal: d9 });
    f({ signal: d9 }), this._queries.push({ query: i5, reader: u3 }), this._store.insert(u3), r6.add(u3.instance);
    const h6 = u3.getCursor();
    for (; h6.next(); ) s9.set(h6.getDisplayId());
    this._send(u3);
  }
  _send(t5) {
    if (!this._subscriptions.size) return;
    let s9 = null;
    const o7 = /* @__PURE__ */ new Map(), a10 = /* @__PURE__ */ new Set(), n7 = /* @__PURE__ */ new Map();
    this._subscriptions.forEach((t6) => {
      const e3 = t6.tile;
      o7.set(e3.key.id, null), s9 = e3.tileInfoView, a10.add(e3.level);
      const { row: r6, col: i5 } = e3.key, d9 = `${e3.level}/${r6}/${i5}`, u3 = n7.get(d9) ?? [];
      u3.push(t6), n7.set(d9, u3);
    });
    for (const e3 of a10) {
      const a11 = s9.getLODInfoAt(e3), i5 = t5.getCursor();
      for (; i5.next(); ) {
        const t6 = h4(i5, a11, e3), s10 = i5.getIndex();
        if (n7.has(t6)) for (const e4 of n7.get(t6)) {
          const t7 = e4.tile.id;
          let a12 = o7.get(t7);
          t(a12) && (a12 = [], o7.set(t7, a12)), a12.push(s10);
        }
      }
    }
    o7.forEach((s10, r6) => {
      if (r(s10)) {
        const e3 = this._subscriptions.get(r6), o8 = { type: "append", id: r6, addOrUpdate: l6(r3.from(t5, s10), e3.tile), end: false, status: t4.empty() };
        e3.add({ query: null, message: o8 }), this._onMessage(o8);
      }
    });
  }
  _sendEnd() {
    this._subscriptions.forEach((t5) => {
      const e3 = { type: "append", id: t5.tile.id, addOrUpdate: null, end: true, status: t4.empty() };
      t5.add({ query: null, message: e3 }), this._onMessage(e3);
    }), this._didSendEnd = true;
  }
};

// node_modules/@arcgis/core/layers/graphics/data/StreamFeatureManager.js
var d6 = "__esri_stream_id__";
var a8 = "__esri_timestamp__";
var h5 = 1e3;
var n6 = class {
  constructor(t5, e3, s9, i5, r6 = 128) {
    this._trackIdToObservations = /* @__PURE__ */ new Map(), this._idCounter = 0, this._lastPurge = performance.now(), this._addOrUpdated = /* @__PURE__ */ new Map(), this._removed = [], this._maxAge = 0, this._timeInfo = s9, this._purgeOptions = i5, this.store = t5, this.objectIdField = e3, this.purgeInterval = r6, this._useGeneratedIds = this.objectIdField === d6;
  }
  removeById(t5) {
    this._removed.push(t5);
  }
  removeByTrackId(t5) {
    const e3 = this._trackIdToObservations.get(t5);
    if (e3) for (const s9 of e3.entries) this._removed.push(s9);
  }
  add(r6) {
    var _a;
    if (this._useGeneratedIds) {
      const t5 = this._nextId();
      r6.attributes[this.objectIdField] = t5, r6.objectId = t5;
    } else r6.objectId = r6.attributes[this.objectIdField];
    const o7 = r6.objectId;
    if (this._addOrUpdated.set(o7, r6), this._maxAge = Math.max(this._maxAge, r6.attributes[this._timeInfo.startTimeField]), !this._timeInfo.trackIdField) return t(this._trackIdLessObservations) && (this._trackIdLessObservations = new s7(1e5)), void this._trackIdLessObservations.enqueue(o7);
    const d9 = r6.attributes[this._timeInfo.trackIdField];
    if (!this._trackIdToObservations.has(d9)) {
      const s9 = r(this._purgeOptions) && null != this._purgeOptions.maxObservations ? this._purgeOptions.maxObservations : h5, r7 = a2(s9, 0, h5);
      this._trackIdToObservations.set(d9, new s7(r7));
    }
    const a10 = (_a = this._trackIdToObservations.get(d9)) == null ? void 0 : _a.enqueue(o7);
    r(a10) && (this._addOrUpdated.has(a10) ? this._addOrUpdated.delete(a10) : this._removed.push(a10));
  }
  checkForUpdates() {
    const t5 = this._getToAdd(), e3 = this._getToRemove(), s9 = performance.now();
    s9 - this._lastPurge >= this.purgeInterval && (this._purge(s9), this._lastPurge = s9);
    const o7 = [];
    if (r(e3)) for (const r6 of e3) {
      const t6 = this.store.removeById(r6);
      r(t6) && o7.push(t6);
    }
    const d9 = [];
    if (r(t5)) {
      const i5 = new Set(l(e3, []));
      for (const e4 of t5) i5.has(e4.objectId) || (e4.attributes[a8] = s9, this.store.add(e4), d9.push(e4));
    }
    (d9.length || (o7 == null ? void 0 : o7.length)) && this.store.update(d9, o7);
  }
  _getToAdd() {
    if (!this._addOrUpdated.size) return null;
    const t5 = new Array(this._addOrUpdated.size);
    let e3 = 0;
    return this._addOrUpdated.forEach((s9) => t5[e3++] = s9), this._addOrUpdated.clear(), t5;
  }
  _getToRemove() {
    const t5 = this._removed;
    return this._removed.length ? (this._removed = [], t5) : null;
  }
  _nextId() {
    const t5 = this._idCounter;
    return this._idCounter = (this._idCounter + 1) % 4294967294 + 1, t5;
  }
  _purge(t5) {
    const e3 = this._purgeOptions;
    r(e3) && (this._purgeSomeByDisplayCount(e3), this._purgeByAge(e3), this._purgeByAgeReceived(t5, e3), this._purgeTracks());
  }
  _purgeSomeByDisplayCount(t5) {
    if (!t5.displayCount) return;
    let e3 = this.store.size;
    if (e3 > t5.displayCount) {
      if (this._timeInfo.trackIdField) {
        for (const s9 of this._trackIdToObservations.values()) if (e3 > t5.displayCount && s9.size) {
          const t6 = e(s9.dequeue());
          this._removed.push(t6), e3--;
        }
      }
      if (r(this._trackIdLessObservations)) {
        let s9 = e3 - t5.displayCount;
        for (; s9-- > 0; ) {
          const t6 = this._trackIdLessObservations.dequeue();
          r(t6) && this._removed.push(t6);
        }
      }
    }
  }
  _purgeByAge(t5) {
    var _a;
    const e3 = (_a = this._timeInfo) == null ? void 0 : _a.startTimeField;
    if (!t5.age || !e3) return;
    const s9 = 60 * t5.age * 1e3, i5 = this._maxAge - s9;
    this.store.forEach((t6) => {
      t6.attributes[e3] < i5 && this._removed.push(t6.objectId);
    });
  }
  _purgeByAgeReceived(t5, e3) {
    if (!e3.ageReceived) return;
    const s9 = t5 - 60 * e3.ageReceived * 1e3;
    this.store.forEach((t6) => {
      t6.attributes[a8] < s9 && this._removed.push(t6.objectId);
    });
  }
  _purgeTracks() {
    this._trackIdToObservations.forEach((t5, e3) => {
      0 === t5.size && this._trackIdToObservations.delete(e3);
    });
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/sources/StreamSource.js
var _2 = 2500;
function y5(e3, s9) {
  const r6 = e3.weakClone();
  if (r(e3.geometry)) {
    const t5 = G(s9, e3.geometry.coords[0]), i5 = T(s9, e3.geometry.coords[1]);
    r6.geometry = new t3([], [t5, i5]);
  }
  return r6;
}
function g7(e3) {
  return "esriGeometryPoint" === e3 ? y5 : (t5, s9) => {
    const r6 = t5.weakClone(), i5 = new t3(), o7 = false, a10 = false, c9 = mt(i5, t5.geometry, o7, a10, e3, s9, false, false);
    return r6.geometry = c9, r6;
  };
}
function f8(e3) {
  return "esriGeometryPoint" === e3 ? (e4) => r(e4.geometry) ? { minX: e4.geometry.coords[0], minY: e4.geometry.coords[1], maxX: e4.geometry.coords[0], maxY: e4.geometry.coords[1] } : { minX: 1 / 0, minY: 1 / 0, maxX: -1 / 0, maxY: -1 / 0 } : (e4) => {
    let s9 = 1 / 0, r6 = 1 / 0, i5 = -1 / 0, o7 = -1 / 0;
    return r(e4.geometry) && e4.geometry.forEachVertex((e5, t5) => {
      s9 = Math.min(s9, e5), r6 = Math.min(r6, t5), i5 = Math.max(i5, e5), o7 = Math.max(o7, t5);
    }), { minX: s9, minY: r6, maxX: i5, maxY: o7 };
  };
}
function b3(e3, t5) {
  const s9 = i2(9, f8(t5));
  return s9.load(e3), s9;
}
function v5(e3, t5) {
  return e3.search({ minX: t5.bounds[0], minY: t5.bounds[1], maxX: t5.bounds[2], maxY: t5.bounds[3] });
}
var I2 = class {
  constructor(e3, t5) {
    this.onUpdate = e3, this._geometryType = t5, this._objectIdToFeature = /* @__PURE__ */ new Map(), this._index = null;
  }
  get _features() {
    const e3 = [];
    return this._objectIdToFeature.forEach((t5) => e3.push(t5)), e3;
  }
  add(e3) {
    this._objectIdToFeature.set(e3.objectId, e3), this._index = null;
  }
  get(e3) {
    return this._objectIdToFeature.has(e3) ? this._objectIdToFeature.get(e3) : null;
  }
  forEach(e3) {
    this._objectIdToFeature.forEach(e3);
  }
  search(e3) {
    return this._index || (this._index = b3(this._features, this._geometryType)), v5(this._index, e3);
  }
  clear() {
    this._index = null, this._objectIdToFeature.clear();
  }
  removeById(e3) {
    const t5 = this._objectIdToFeature.get(e3);
    return t5 ? (this._objectIdToFeature.delete(e3), this._index = null, t5) : null;
  }
  update(e3, t5) {
    this.onUpdate(e3, t5);
  }
  get size() {
    return this._objectIdToFeature.size;
  }
};
var T3 = class extends f7 {
  constructor(e3) {
    super(e3), this.type = "stream", this._updateIntervalId = 0, this._level = 0, this._updateInfo = { websocket: 0, client: 0 }, this._isPaused = false, this._inUpdate = false;
    const { outSR: t5 } = e3, { geometryType: s9, objectIdField: r6, timeInfo: i5, purgeOptions: o7, source: n7, spatialReference: a10, serviceFilter: c9, maxReconnectionAttempts: d9, maxReconnectionInterval: p5, updateInterval: l7, customParameters: m4, enabledEventTypes: y6 } = e3.serviceInfo, f10 = new I2(this._onUpdate.bind(this), s9), b5 = new n6(f10, r6, i5, o7), v6 = o4(n7, a10, t5, s9, c9, d9, p5, m4 ?? {});
    this._store = f10, this._manager = b5, this._connection = v6, this._quantize = g7(s9), this._enabledEventTypes = new Set(y6), this._handles = [this._connection.on("data-received", (e4) => this._onFeature(e4)), this._connection.on("message-received", (e4) => this._onWebSocketMessage(e4))], this._initUpdateInterval = () => {
      let t6 = performance.now();
      this._updateIntervalId = setInterval(() => {
        const s10 = performance.now(), r7 = s10 - t6;
        if (r7 > _2) {
          t6 = s10;
          const e4 = Math.round(this._updateInfo.client / (r7 / 1e3)), i6 = Math.round(this._updateInfo.websocket / (r7 / 1e3));
          this._updateInfo.client = 0, this._updateInfo.websocket = 0, this.events.emit("updateRate", { client: e4, websocket: i6 });
        }
        e3.canAcceptRequest() && !this._inUpdate && this._manager.checkForUpdates();
      }, l7);
    }, this._initUpdateInterval();
  }
  destroy() {
    super.destroy(), this._clearUpdateInterval(), this._handles.forEach((e3) => e3.remove()), this._connection.destroy();
  }
  _fetchDataTile() {
  }
  get connectionStatus() {
    var _a;
    return this._isPaused ? "paused" : (_a = this._connection) == null ? void 0 : _a.connectionStatus;
  }
  get errorString() {
    var _a;
    return (_a = this._connection) == null ? void 0 : _a.errorString;
  }
  updateCustomParameters(e3) {
    this._connection.updateCustomParameters(e3);
  }
  pauseStream() {
    this._isPaused || (this._isPaused = true, this._clearUpdateInterval());
  }
  resumeStream() {
    this._isPaused && (this._isPaused = false, this._initUpdateInterval());
  }
  sendMessageToSocket(e3) {
    this._connection.sendMessageToSocket(e3);
  }
  sendMessageToClient(e3) {
    this._connection.sendMessageToClient(e3);
  }
  enableEvent(e3, t5) {
    t5 ? this._enabledEventTypes.add(e3) : this._enabledEventTypes.delete(e3);
  }
  get updating() {
    return false;
  }
  subscribe(e3, t5) {
    super.subscribe(e3, t5);
    const s9 = this._subscriptions.get(e3.id);
    this._level = e3.level;
    const r6 = this._getTileFeatures(e3);
    this._onMessage({ type: "append", id: e3.key.id, addOrUpdate: r6, end: true }), s9.didSend = true;
  }
  unsubscribe(e3) {
    super.unsubscribe(e3);
  }
  *readers(e3) {
    const t5 = this._subscriptions.get(e3), { tile: s9 } = t5;
    yield this._getTileFeatures(s9);
  }
  createTileQuery(e3) {
    throw new Error("Service does not support tile  queries");
  }
  async resend() {
    this._subscriptions.forEach((e3) => {
      const { tile: t5 } = e3, s9 = { type: "append", id: t5.id, addOrUpdate: this._getTileFeatures(t5), end: true };
      this._onMessage(s9);
    });
  }
  _getTileFeatures(e3) {
    const t5 = this._store.search(e3).map((t6) => this._quantize(t6, e3.transform));
    return c5.fromOptimizedFeatures(t5, this._serviceInfo, e3.transform);
  }
  _onWebSocketMessage(e3) {
    if (this._enabledEventTypes.has("message-received") && this.events.emit("message-received", e3), "type" in e3) switch (e3.type) {
      case "delete":
        if (e3.objectIds) for (const t5 of e3.objectIds) this._manager.removeById(t5);
        if (e3.trackIds) for (const t5 of e3.trackIds) this._manager.removeByTrackId(t5);
        break;
      case "clear":
        this._store.forEach((e4) => this._manager.removeById(e4.objectId));
    }
  }
  _onFeature(e3) {
    this._updateInfo.websocket++;
    try {
      this._enabledEventTypes.has("data-received") && this.events.emit("data-received", e3);
      const t5 = ot(e3, this._serviceInfo.geometryType, false, false, this._serviceInfo.objectIdField);
      this._manager.add(t5);
    } catch (t5) {
    }
  }
  _clearUpdateInterval() {
    clearInterval(this._updateIntervalId), this._updateIntervalId = 0;
  }
  async _onUpdate(e3, s9) {
    this._inUpdate = true;
    try {
      r(e3) && (this._updateInfo.client += e3.length), this._subscriptions.forEach((e4, t5) => {
        e4.didSend && e4.tile.level === this._level && this._onMessage({ type: "append", id: t5, addOrUpdate: null, clear: true, end: false });
      });
      const s10 = [];
      this._subscriptions.forEach((e4, t5) => {
        if (!e4.didSend || e4.tile.level !== this._level) return;
        const r6 = e4.tile, i5 = { type: "append", id: t5, addOrUpdate: this._getTileFeatures(r6), remove: [], end: false, status: t4.empty() };
        e4.requests.stream.enqueue(i5), s10.push(this._onMessage(i5));
      }), await Promise.all(s10), this._subscriptions.forEach((e4, t5) => {
        e4.didSend && e4.tile.level === this._level && this._onMessage({ type: "append", id: t5, addOrUpdate: null, end: true });
      });
    } catch {
    }
    this._inUpdate = false;
  }
};
e2([y()], T3.prototype, "_isPaused", void 0), e2([y()], T3.prototype, "connectionStatus", null), e2([y()], T3.prototype, "errorString", null), T3 = e2([a("esri.views.2d.layers.features.sources")], T3);

// node_modules/@arcgis/core/views/2d/layers/features/sources/createSource.js
function a9(e3, r6, a10, u3, i5, p5) {
  const f10 = c7(e3, r6, a10, u3, i5, p5);
  switch (f10.type) {
    case "feature":
      switch (f10.origin) {
        case "hosted":
        case "local":
          return new n5(f10);
        case "snapshot":
          return new c6(f10);
        default:
          return new a7(f10);
      }
    case "stream":
      return new T3(f10);
  }
}
function c7(t5, o7, s9, n7, a10, c9) {
  switch (t5.type) {
    case "snapshot":
      return { type: "feature", origin: "snapshot", featureCount: l(t5.featureCount, 0), serviceInfo: t5, onMessage: n7, outSR: o7, tileInfoView: s9, canAcceptRequest: a10, store: c9 };
    case "stream":
      return { type: "stream", serviceInfo: t5, onMessage: n7, outSR: o7, canAcceptRequest: a10 };
    case "memory":
    case "on-demand":
      return { type: "feature", serviceInfo: t5, onMessage: n7, outSR: o7, origin: u3(t5.source), tileInfoView: s9, canAcceptRequest: a10 };
  }
  function u3(e3) {
    return Array.isArray(e3) ? "local" : "path" in e3 && g4(e3.path) ? "hosted" : "unknown";
  }
}

// node_modules/@arcgis/core/geohash/geohashUtils.js
var c8 = new Float64Array(2);
var f9 = new Float64Array(2);
function X(t5, n7) {
  let o7 = -90, e3 = 90, r6 = -180, c9 = 180;
  for (let f10 = 0; f10 < n7; f10++) {
    const n8 = Math.ceil((f10 + 1) / 2), l7 = Math.floor((f10 + 1) / 2), u3 = 1 - f10 % 2, h6 = 30 - (3 * n8 + 2 * l7), s9 = 30 - (2 * n8 + 3 * l7), i5 = 3 * u3 + 2 * (1 - u3), a10 = 2 * u3 + 3 * (1 - u3), g8 = 3 * u3 + 7 * (1 - u3) << s9, A3 = (7 * u3 + 3 * (1 - u3) << h6 & t5.geohashX) >> h6, d9 = (g8 & t5.geohashY) >> s9;
    for (let t6 = i5 - 1; t6 >= 0; t6--) {
      const n9 = (r6 + c9) / 2, o8 = A3 & 1 << t6 ? 1 : 0;
      r6 = (1 - o8) * r6 + o8 * n9, c9 = (1 - o8) * n9 + o8 * c9;
    }
    for (let t6 = a10 - 1; t6 >= 0; t6--) {
      const n9 = (o7 + e3) / 2, r7 = d9 & 1 << t6 ? 1 : 0;
      o7 = (1 - r7) * o7 + r7 * n9, e3 = (1 - r7) * n9 + r7 * e3;
    }
  }
  return [r6, o7, c9, e3];
}
function Y(t5, n7, o7, e3) {
  e3 % 2 && (e3 += 1);
  let r6 = 0, c9 = 0, f10 = -90, l7 = 90, u3 = -180, h6 = 180;
  for (let s9 = 0; s9 < e3 / 2; s9++) {
    for (let t6 = 0; t6 < 5; t6++) {
      const n8 = (u3 + h6) / 2, e4 = o7 > n8 ? 1 : 0;
      r6 |= e4 << 29 - (t6 + 5 * s9), u3 = (1 - e4) * u3 + e4 * n8, h6 = (1 - e4) * n8 + e4 * h6;
    }
    for (let t6 = 0; t6 < 5; t6++) {
      const o8 = (f10 + l7) / 2, e4 = n7 > o8 ? 1 : 0;
      c9 |= e4 << 29 - (t6 + 5 * s9), f10 = (1 - e4) * f10 + e4 * o8, l7 = (1 - e4) * o8 + e4 * l7;
    }
  }
  t5.geohashX = r6, t5.geohashY = c9;
}
function b4(t5, n7, o7, e3, r6) {
  r6 % 2 && (r6 += 1);
  let c9 = 0, f10 = 0, l7 = -90, u3 = 90, h6 = -180, s9 = 180;
  for (let i5 = 0; i5 < r6 / 2; i5++) {
    for (let t6 = 0; t6 < 5; t6++) {
      const n8 = (h6 + s9) / 2, o8 = e3 > n8 ? 1 : 0;
      c9 |= o8 << 29 - (t6 + 5 * i5), h6 = (1 - o8) * h6 + o8 * n8, s9 = (1 - o8) * n8 + o8 * s9;
    }
    for (let t6 = 0; t6 < 5; t6++) {
      const n8 = (l7 + u3) / 2, e4 = o7 > n8 ? 1 : 0;
      f10 |= e4 << 29 - (t6 + 5 * i5), l7 = (1 - e4) * l7 + e4 * n8, u3 = (1 - e4) * n8 + e4 * u3;
    }
  }
  t5[2 * n7] = c9, t5[2 * n7 + 1] = f10;
}

// node_modules/@arcgis/core/geohash/GeohashTree.js
var u = class {
  constructor(e3 = [], s9, i5 = 8096) {
    this.onRelease = (t5) => {
    }, this._nodes = 0, this._root = new d7(this, 0, 0, 0), this._statisticFields = e3, this._pool = i5 ? new s7(8096) : null, this._serviceInfo = s9;
  }
  destroy() {
    this.clear();
  }
  _acquire(t5, s9, i5) {
    this._nodes++;
    let n7 = null;
    return r(this._pool) && (n7 = this._pool.dequeue()), r(n7) ? n7.realloc(t5, s9, i5) : n7 = new d7(this, t5, s9, i5), n7;
  }
  _release(t5) {
    this.onRelease(t5), this._nodes--, r(this._pool) && this._pool.enqueue(t5);
  }
  get count() {
    return this._root.count;
  }
  get size() {
    return this._nodes;
  }
  get poolSize() {
    return g(this._pool, 0, (t5) => t5.size);
  }
  get depth() {
    let t5 = 0;
    return this.forEach((e3) => t5 = Math.max(t5, e3.depth)), t5;
  }
  dropLevels(t5) {
    this.forEach((e3) => {
      if (e3.depth >= t5) for (let t6 = 0; t6 < e3.children.length; t6++) {
        const s9 = e3.children[t6];
        s9 && this._release(s9);
      }
    }), this.forEach((e3) => {
      if (e3.depth >= t5) for (let t6 = 0; t6 < e3.children.length; t6++) e3.children[t6] = null;
    });
  }
  clear() {
    this.forEach((t5) => this._release(t5)), this._root = new d7(this, 0, 0, 0);
  }
  insert(t5, e3, s9 = 0) {
    const i5 = c5.fromOptimizedFeatures([t5], this._serviceInfo).getCursor();
    i5.next();
    const n7 = i5.readGeometry();
    if (!n7) return;
    const [o7, a10] = n7.coords, r6 = t5.geohashX, l7 = t5.geohashY;
    this.insertCursor(i5, t5.displayId, o7, a10, r6, l7, e3, s9);
  }
  insertCursor(t5, e3, s9, i5, n7, o7, a10, r6 = 0) {
    let l7 = this._root, h6 = 0, c9 = 0, u3 = 0;
    for (; null !== l7; ) {
      if (l7.depth >= r6 && (l7.count += 1, l7.xTotal += s9, l7.yTotal += i5, l7.xGeohashTotal += n7, l7.yGeohashTotal += o7, l7.referenceId = e3, this._updateStatisticsCursor(t5, l7, 1)), h6 >= a10) return void l7.add(e3);
      const d9 = Math.ceil((h6 + 1) / 2), f10 = Math.floor((h6 + 1) / 2), x4 = 1 - h6 % 2, m4 = 30 - (3 * d9 + 2 * f10), g8 = 30 - (2 * d9 + 3 * f10), y6 = (n7 & 7 * x4 + 3 * (1 - x4) << m4) >> m4, p5 = (o7 & 3 * x4 + 7 * (1 - x4) << g8) >> g8, _3 = y6 + p5 * (8 * x4 + 4 * (1 - x4));
      c9 = c9 << 3 * x4 + 2 * (1 - x4) | y6, u3 = u3 << 2 * x4 + 3 * (1 - x4) | p5, null == l7.children[_3] && (l7.children[_3] = this._acquire(c9, u3, h6 + 1)), h6 += 1, l7 = l7.children[_3];
    }
  }
  remove(t5, e3) {
    const s9 = c5.fromOptimizedFeatures([t5], this._serviceInfo).getCursor();
    s9.next();
    const i5 = s9.readGeometry();
    if (!i5) return;
    const [n7, o7] = i5.coords, a10 = t5.geohashX, r6 = t5.geohashY;
    this.removeCursor(s9, n7, o7, a10, r6, e3);
  }
  removeCursor(t5, e3, s9, i5, n7, o7) {
    let a10 = this._root, r6 = 0;
    for (; null !== a10; ) {
      if (a10.count -= 1, a10.xTotal -= e3, a10.yTotal -= s9, a10.xGeohashTotal -= i5, a10.yGeohashTotal -= n7, this._updateStatisticsCursor(t5, a10, -1), r6 >= o7) return void a10.remove(t5.getDisplayId());
      const l7 = Math.ceil((r6 + 1) / 2), h6 = Math.floor((r6 + 1) / 2), c9 = 1 - r6 % 2, u3 = 30 - (3 * l7 + 2 * h6), d9 = 30 - (2 * l7 + 3 * h6), f10 = ((i5 & 7 * c9 + 3 * (1 - c9) << u3) >> u3) + ((n7 & 3 * c9 + 7 * (1 - c9) << d9) >> d9) * (8 * c9 + 4 * (1 - c9)), x4 = a10.children[f10];
      1 === (x4 == null ? void 0 : x4.count) && (this._release(x4), a10.children[f10] = null), r6 += 1, a10 = x4;
    }
  }
  forEach(t5) {
    let e3 = this._root;
    for (; null !== e3; ) {
      const s9 = this._linkChildren(e3) || e3.next;
      t5(e3), e3 = s9;
    }
  }
  find(t5, e3, s9) {
    return this._root.find(t5, e3, s9, 0, 0, 0);
  }
  findIf(t5) {
    let e3 = null;
    return this.forEach((s9) => {
      t5(s9) && (e3 = s9);
    }), e3;
  }
  findAllIf(t5) {
    const e3 = [];
    return this.forEach((s9) => {
      t5(s9) && e3.push(s9);
    }), e3;
  }
  findSingleOccupancyNode(t5, e3, s9, i5, n7) {
    let o7 = this._root;
    for (; null !== o7; ) {
      const a10 = o7.depth, r6 = o7.xNode, l7 = o7.yNode, h6 = 1 - a10 % 2, c9 = o7.xGeohashTotal / o7.count, u3 = o7.yGeohashTotal / o7.count;
      if (1 === o7.count && t5 < c9 && c9 <= s9 && e3 < u3 && u3 <= i5) return o7;
      if (a10 >= n7) {
        o7 = o7.next;
        continue;
      }
      const d9 = Math.ceil((a10 + 1) / 2), f10 = Math.floor((a10 + 1) / 2), x4 = 30 - (3 * d9 + 2 * f10), m4 = 30 - (2 * d9 + 3 * f10), g8 = ~((1 << x4) - 1), y6 = ~((1 << m4) - 1), p5 = (t5 & g8) >> x4, _3 = (e3 & y6) >> m4, v6 = (s9 & g8) >> x4, M2 = (i5 & y6) >> m4, T7 = r6 << 3 * h6 + 2 * (1 - h6), b5 = l7 << 2 * h6 + 3 * (1 - h6), k2 = T7 + 8 * h6 + 4 * (1 - h6), N = b5 + 4 * h6 + 8 * (1 - h6), I3 = Math.max(T7, p5), C = Math.max(b5, _3), G5 = Math.min(k2, v6), L3 = Math.min(N, M2);
      let S3 = null, w3 = null;
      for (let t6 = C; t6 <= L3; t6++) for (let e4 = I3; e4 <= G5; e4++) {
        const s10 = e4 - T7 + (t6 - b5) * (8 * h6 + 4 * (1 - h6)), i6 = o7.children[s10];
        i6 && (S3 || (S3 = i6, S3.next = o7.next), w3 && (w3.next = i6), w3 = i6, i6.next = o7.next);
      }
      o7 = S3 || o7.next;
    }
    return null;
  }
  getRegionDisplayIds(t5) {
    let e3 = this._root;
    const { bounds: s9, geohashBounds: i5, level: n7 } = t5, [o7, a10, r6, l7] = s9, h6 = [];
    for (; null !== e3; ) {
      const t6 = e3.depth, s10 = e3.xNode, c9 = e3.yNode;
      if (t6 >= n7) {
        const t7 = e3.xTotal / e3.count, s11 = e3.yTotal / e3.count;
        t7 >= o7 && t7 <= r6 && s11 >= a10 && s11 <= l7 && e3.displayIds.forEach((t8) => h6.push(t8)), e3 = e3.next;
        continue;
      }
      const u3 = Math.ceil((t6 + 1) / 2), d9 = Math.floor((t6 + 1) / 2), f10 = 1 - t6 % 2, x4 = 30 - (3 * u3 + 2 * d9), m4 = 30 - (2 * u3 + 3 * d9), g8 = ~((1 << x4) - 1), y6 = ~((1 << m4) - 1), p5 = (i5.xLL & g8) >> x4, _3 = (i5.yLL & y6) >> m4, v6 = (i5.xTR & g8) >> x4, M2 = (i5.yTR & y6) >> m4, T7 = s10 << 3 * f10 + 2 * (1 - f10), b5 = c9 << 2 * f10 + 3 * (1 - f10), k2 = T7 + 8 * f10 + 4 * (1 - f10), N = b5 + 4 * f10 + 8 * (1 - f10), I3 = Math.max(T7, p5), C = Math.max(b5, _3), G5 = Math.min(k2, v6), L3 = Math.min(N, M2);
      let S3 = null, w3 = null;
      for (let i6 = C; i6 <= L3; i6++) for (let t7 = I3; t7 <= G5; t7++) {
        const s11 = t7 - T7 + (i6 - b5) * (8 * f10 + 4 * (1 - f10)), n8 = e3.children[s11];
        n8 && (S3 || (S3 = n8, S3.next = e3.next), w3 && (w3.next = n8), w3 = n8, n8.next = e3.next);
      }
      e3 = S3 || e3.next;
    }
    return h6;
  }
  getRegionStatistics(t5) {
    let e3 = this._root, s9 = 0, i5 = 0, n7 = 0;
    const o7 = {}, { bounds: a10, geohashBounds: r6, level: l7 } = t5, [h6, c9, u3, d9] = a10;
    let f10 = 0;
    for (; null !== e3; ) {
      const t6 = e3.depth, a11 = e3.xNode, x4 = e3.yNode;
      if (t6 >= l7) {
        const t7 = e3.xTotal / e3.count, a12 = e3.yTotal / e3.count;
        t7 > h6 && t7 <= u3 && a12 > c9 && a12 <= d9 && (s9 += e3.count, i5 += e3.xTotal, n7 += e3.yTotal, 1 === e3.count && (f10 = e3.referenceId), this._aggregateStatistics(o7, e3.statistics)), e3 = e3.next;
        continue;
      }
      const m4 = Math.ceil((t6 + 1) / 2), g8 = Math.floor((t6 + 1) / 2), y6 = 1 - t6 % 2, p5 = 30 - (3 * m4 + 2 * g8), _3 = 30 - (2 * m4 + 3 * g8), v6 = ~((1 << p5) - 1), M2 = ~((1 << _3) - 1), T7 = (r6.xLL & v6) >> p5, b5 = (r6.yLL & M2) >> _3, k2 = (r6.xTR & v6) >> p5, N = (r6.yTR & M2) >> _3, I3 = a11 << 3 * y6 + 2 * (1 - y6), C = x4 << 2 * y6 + 3 * (1 - y6), G5 = I3 + 8 * y6 + 4 * (1 - y6), L3 = C + 4 * y6 + 8 * (1 - y6), S3 = Math.max(I3, T7), w3 = Math.max(C, b5), R4 = Math.min(G5, k2), F4 = Math.min(L3, N);
      let j5 = null, z = null;
      for (let r7 = w3; r7 <= F4; r7++) for (let t7 = S3; t7 <= R4; t7++) {
        const a12 = t7 - I3 + (r7 - C) * (8 * y6 + 4 * (1 - y6)), l8 = e3.children[a12];
        if (l8) {
          if (r7 !== w3 && r7 !== F4 && t7 !== S3 && t7 !== R4) {
            const t8 = l8.xTotal / l8.count, e4 = l8.yTotal / l8.count;
            t8 > h6 && t8 <= u3 && e4 > c9 && e4 <= d9 && (s9 += l8.count, i5 += l8.xTotal, n7 += l8.yTotal, 1 === l8.count && (f10 = l8.referenceId), this._aggregateStatistics(o7, l8.statistics));
            continue;
          }
          j5 || (j5 = l8, j5.next = e3.next), z && (z.next = l8), z = l8, l8.next = e3.next;
        }
      }
      e3 = j5 || e3.next;
    }
    return { count: s9, attributes: this.normalizeStatistics(o7, s9), xTotal: i5, yTotal: n7, referenceId: f10 };
  }
  getBins(t5) {
    const e3 = [], { geohashBounds: s9, level: i5 } = t5;
    let n7 = this._root;
    for (; null !== n7; ) {
      const t6 = n7.depth, o7 = n7.xNode, a10 = n7.yNode;
      if (t6 >= i5) {
        e3.push(n7), n7 = n7.next;
        continue;
      }
      const r6 = Math.ceil((t6 + 1) / 2), l7 = Math.floor((t6 + 1) / 2), h6 = 1 - t6 % 2, c9 = 30 - (3 * r6 + 2 * l7), u3 = 30 - (2 * r6 + 3 * l7), d9 = ~((1 << c9) - 1), f10 = ~((1 << u3) - 1), x4 = (s9.xLL & d9) >> c9, m4 = (s9.yLL & f10) >> u3, g8 = (s9.xTR & d9) >> c9, y6 = (s9.yTR & f10) >> u3, p5 = o7 << 3 * h6 + 2 * (1 - h6), _3 = a10 << 2 * h6 + 3 * (1 - h6), v6 = p5 + 8 * h6 + 4 * (1 - h6), M2 = _3 + 4 * h6 + 8 * (1 - h6), T7 = Math.max(p5, x4), b5 = Math.max(_3, m4), k2 = Math.min(v6, g8), N = Math.min(M2, y6);
      let I3 = null, C = null;
      for (let e4 = b5; e4 <= N; e4++) for (let t7 = T7; t7 <= k2; t7++) {
        const s10 = t7 - p5 + (e4 - _3) * (8 * h6 + 4 * (1 - h6)), i6 = n7.children[s10];
        i6 && (I3 || (I3 = i6, I3.next = n7.next), C && (C.next = i6), C = i6, i6.next = n7.next);
      }
      n7 = I3 || n7.next;
    }
    return e3;
  }
  _linkChildren(t5) {
    let e3 = null, s9 = null;
    for (let i5 = 0; i5 <= t5.children.length; i5++) {
      const n7 = t5.children[i5];
      n7 && (e3 || (e3 = n7, e3.next = t5.next), s9 && (s9.next = n7), s9 = n7, n7.next = t5.next);
    }
    return e3;
  }
  _updateStatisticsCursor(t5, e3, s9) {
    for (const i5 of this._statisticFields) {
      const n7 = i5.name, o7 = i5.inField ? t5.readAttribute(i5.inField) : t5.getComputedNumericAtIndex(i5.inFieldIndex);
      switch (i5.statisticType) {
        case "min": {
          if (isNaN(o7)) break;
          if (!e3.statistics[n7]) {
            e3.statistics[n7] = { value: o7 };
            break;
          }
          const t6 = e3.statistics[n7].value;
          e3.statistics[n7].value = Math.min(t6, o7);
          break;
        }
        case "max": {
          if (isNaN(o7)) break;
          if (!e3.statistics[n7]) {
            e3.statistics[n7] = { value: o7 };
            break;
          }
          const t6 = e3.statistics[n7].value;
          e3.statistics[n7].value = Math.max(t6, o7);
          break;
        }
        case "count":
          break;
        case "sum":
        case "avg": {
          e3.statistics[n7] || (e3.statistics[n7] = { value: 0, nanCount: 0 });
          const t6 = e3.statistics[n7].value, i6 = e3.statistics[n7].nanCount ?? 0;
          null == o7 || isNaN(o7) ? e3.statistics[n7].nanCount = i6 + s9 : e3.statistics[n7].value = t6 + s9 * o7;
          break;
        }
        case "avg_angle": {
          e3.statistics[n7] || (e3.statistics[n7] = { x: 0, y: 0, nanCount: 0 });
          const t6 = e3.statistics[n7].x, i6 = e3.statistics[n7].y, a10 = e3.statistics[n7].nanCount ?? 0, r6 = Math.PI / 180;
          null == o7 || isNaN(o7) ? e3.statistics[n7].nanCount = a10 + s9 : (e3.statistics[n7].x = t6 + s9 * Math.cos(o7 * r6), e3.statistics[n7].y = i6 + s9 * Math.sin(o7 * r6));
          break;
        }
        case "mode": {
          e3.statistics[n7] || (e3.statistics[n7] = {});
          const t6 = e3.statistics[n7][o7] || 0;
          e3.statistics[n7][o7] = t6 + s9;
          break;
        }
      }
    }
  }
  _aggregateStatistics(t5, e3) {
    for (const s9 of this._statisticFields) {
      const i5 = s9.name;
      switch (s9.statisticType) {
        case "min": {
          if (!t5[i5]) {
            t5[i5] = { value: e3[i5].value };
            break;
          }
          const s10 = t5[i5].value;
          t5[i5].value = Math.min(s10, e3[i5].value);
          break;
        }
        case "max": {
          if (!t5[i5]) {
            t5[i5] = { value: e3[i5].value };
            break;
          }
          const s10 = t5[i5].value;
          t5[i5].value = Math.max(s10, e3[i5].value);
          break;
        }
        case "count":
          break;
        case "sum":
        case "avg":
        case "avg_angle":
        case "mode":
          t5[i5] || (t5[i5] = {});
          for (const s10 in e3[i5]) {
            const n7 = t5[i5][s10] || 0;
            t5[i5][s10] = n7 + e3[i5][s10];
          }
      }
    }
  }
  normalizeStatistics(t5, e3) {
    const s9 = {};
    for (const i5 of this._statisticFields) {
      const n7 = i5.name;
      switch (i5.statisticType) {
        case "min":
        case "max": {
          const i6 = t5[n7];
          if (!e3 || !i6) break;
          s9[n7] = i6.value;
          break;
        }
        case "count":
          if (!e3) break;
          s9[n7] = e3;
          break;
        case "sum": {
          if (!e3) break;
          const { value: i6, nanCount: o7 } = t5[n7];
          if (!(e3 - o7)) break;
          s9[n7] = i6;
          break;
        }
        case "avg": {
          if (!e3) break;
          const { value: i6, nanCount: o7 } = t5[n7];
          if (!(e3 - o7)) break;
          s9[n7] = i6 / (e3 - o7);
          break;
        }
        case "avg_angle": {
          if (!e3) break;
          const { x: i6, y: o7, nanCount: a10 } = t5[n7];
          if (!(e3 - a10)) break;
          const r6 = i6 / (e3 - a10), l7 = o7 / (e3 - a10), h6 = 180 / Math.PI, c9 = Math.atan2(l7, r6) * h6;
          s9[n7] = c9;
          break;
        }
        case "mode": {
          const e4 = t5[n7];
          let i6 = 0, o7 = 0, a10 = null;
          for (const t6 in e4) {
            const s10 = e4[t6];
            s10 === i6 ? o7 += 1 : s10 > i6 && (i6 = s10, o7 = 1, a10 = t6);
          }
          s9[n7] = "null" === a10 || o7 > 1 ? null : a10;
          break;
        }
      }
    }
    return s9;
  }
};
var d7 = class {
  constructor(t5, e3, s9, i5) {
    this.count = 0, this.xTotal = 0, this.yTotal = 0, this.statistics = {}, this.displayId = 0, this.referenceId = 0, this.displayIds = /* @__PURE__ */ new Set(), this.next = null, this.depth = 0, this.xNode = 0, this.yNode = 0, this.xGeohashTotal = 0, this.yGeohashTotal = 0, this._tree = t5, this.children = new Array(32);
    for (let n7 = 0; n7 < this.children.length; n7++) this.children[n7] = null;
    this.xNode = e3, this.yNode = s9, this.depth = i5;
  }
  realloc(t5, e3, s9) {
    for (let i5 = 0; i5 < this.children.length; i5++) this.children[i5] = null;
    return this.xNode = t5, this.yNode = e3, this.depth = s9, this.next = null, this.xGeohashTotal = 0, this.yGeohashTotal = 0, this.displayId = 0, this.referenceId = 0, this.xTotal = 0, this.yTotal = 0, this.count = 0, this.statistics = {}, this.displayIds.clear(), this;
  }
  get id() {
    return `${this.xNode}.${this.yNode}`;
  }
  add(t5) {
    this.displayIds.add(t5);
  }
  remove(t5) {
    this.displayIds.delete(t5);
  }
  getAttributes() {
    const t5 = this._tree.normalizeStatistics(this.statistics, this.count);
    return t5.referenceId = null, t5.aggregateId = this.id, t5.aggregateCount = this.count, t5;
  }
  getGeometry(t5, s9) {
    const i5 = this.getLngLatBounds(), [r6, c9, u3, d9] = i5, f10 = g3({ rings: [[[r6, c9], [r6, d9], [u3, d9], [u3, c9], [r6, c9]]] }, f2.WGS84, t5), x4 = W(new t3(), f10, false, false);
    if (r(s9)) {
      return mt(new t3(), x4, false, false, "esriGeometryPolygon", s9, false, false);
    }
    return x4;
  }
  getGeometryCentroid(t5, s9) {
    const i5 = this.getLngLatBounds(), [o7, c9, u3, d9] = i5, f10 = g3({ x: (o7 + u3) / 2, y: (c9 + d9) / 2 }, f2.WGS84, t5), x4 = U2(new t3(), f10);
    if (r(s9)) {
      return mt(new t3(), x4, false, false, "esriGeometryPoint", s9, false, false);
    }
    return x4;
  }
  getLngLatBounds() {
    const t5 = this.depth, e3 = Math.ceil(t5 / 2), s9 = Math.floor(t5 / 2), n7 = 30 - (3 * e3 + 2 * s9), o7 = 30 - (2 * e3 + 3 * s9), a10 = this.xNode << n7, r6 = this.yNode << o7;
    return X({ geohashX: a10, geohashY: r6 }, this.depth);
  }
  find(t5, e3, s9, i5, n7, o7) {
    if (i5 >= s9) return this;
    const a10 = 1 - i5 % 2, r6 = 3 * a10 + 2 * (1 - a10), l7 = 2 * a10 + 3 * (1 - a10), h6 = 30 - n7 - r6, c9 = 30 - o7 - l7, u3 = ((t5 & 7 * a10 + 3 * (1 - a10) << h6) >> h6) + ((e3 & 3 * a10 + 7 * (1 - a10) << c9) >> c9) * (8 * a10 + 4 * (1 - a10)), d9 = this.children[u3];
    return null == d9 ? null : d9.find(t5, e3, s9, i5 + 1, n7 + r6, o7 + l7);
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/support/BinStore.js
var G3 = s.getLogger("esri.view.2d.layers.features.support.BinStore");
var R2 = 12;
var L2 = 64;
var T4 = a3();
var A = 5;
function D2(e3) {
  return 57.29577951308232 * e3;
}
var O = class extends c4 {
  constructor(t5, s9, r6, i5) {
    super(t5, r6), this.type = "bin", this.events = new n(), this.objectIdField = "aggregateId", this.featureAdapter = p, this._geohashLevel = A, this._geohashBuf = [], this._serviceInfo = i5, this.geometryInfo = t5.geometryInfo, this._spatialReference = s9, this._projectionSupportCheck = f4(s9, f2.WGS84), this._bitsets.geohash = r6.getBitset(r6.createBitset()), this._bitsets.inserted = r6.getBitset(r6.createBitset());
  }
  destroy() {
    this._tree && this._tree.destroy();
  }
  get featureSpatialReference() {
    return this._spatialReference;
  }
  get fields() {
    return this._fields;
  }
  async updateSchema(e3, t5) {
    const r6 = this._schema;
    try {
      await super.updateSchema(e3, t5), await this._projectionSupportCheck;
    } catch (n7) {
    }
    this._fields = this._schema.params.fields;
    const i5 = m(r6, t5);
    t5 && (!t(i5) || e3.source || e3.storage.filters) ? ((a4(i5, "params.fields") || a4(i5, "params") || !this._tree || e3.source) && (this._tree && this._tree.destroy(), this._tree = new u(this._statisticFields, this._serviceInfo), this._tree.onRelease = (e4) => e4.displayId && this._storage.releaseDisplayId(e4.displayId), this._geohashLevel = this._schema.params.fixedBinLevel, this._rebuildTree(), has("esri-2d-update-debug") && G3.info("Aggregate mesh needs update due to tree changing")), has("esri-2d-update-debug") && G3.info("Aggregate mesh needs update due to tree changing"), e3.targets[t5.name] = true, e3.mesh = false) : r6 && (e3.mesh = true);
  }
  clear() {
    this._rebuildTree();
  }
  sweepFeatures(e3, t5) {
    this._bitsets.inserted.forEachSet((s9) => {
      if (!e3.has(s9)) {
        const e4 = t5.lookupByDisplayIdUnsafe(s9);
        this._remove(e4);
      }
    });
  }
  sweepAggregates(e3, t5, s9) {
  }
  onTileData(e3, t5, r6, i5, o7 = true) {
    if (!this._schema || t(t5.addOrUpdate)) return t5;
    this.events.emit("changed");
    const a10 = this._getTransforms(e3, this._spatialReference);
    {
      const e4 = t5.addOrUpdate.getCursor();
      for (; e4.next(); ) this._update(e4, i5);
    }
    if (t5.status.mesh || !o7) return t5;
    const h6 = new Array();
    this._getBinsForTile(h6, e3, a10, r6), t5.addOrUpdate = c5.fromOptimizedFeatures(h6, { ...this._serviceInfo, geometryType: "esriGeometryPolygon" }), t5.addOrUpdate.attachStorage(r6), t5.end = true, t5.isRepush || (t5.clear = true);
    {
      const s9 = t5.addOrUpdate.getCursor();
      for (; s9.next(); ) {
        const t6 = s9.getDisplayId();
        this._bitsets.computed.unset(t6), this.setComputedAttributes(r6, s9, t6, e3.scale);
      }
    }
    return t5;
  }
  forEachBin(e3) {
    this._tree.forEach(e3);
  }
  forEach(e3) {
    this._tree.forEach((t5) => {
      if (t5.depth !== this._geohashLevel) return;
      const s9 = this._toFeatureJSON(t5), r6 = c5.fromFeatures([s9], { objectIdField: this.objectIdField, globalIdField: null, geometryType: this.geometryInfo.geometryType, fields: this.fields }).getCursor();
      r6.next(), e3(r6);
    });
  }
  forEachInBounds(e3, t5) {
  }
  forEachBounds(e3, t5) {
    const { hasM: r6, hasZ: i5 } = this.geometryInfo;
    for (const o7 of e3) {
      const e4 = It(T4, o7.readGeometry(), i5, r6);
      t(e4) || t5(e4);
    }
  }
  onTileUpdate(e3) {
  }
  getAggregate(e3) {
    const t5 = s6(e3, true), s9 = this._tree.findIf((e4) => e4.displayId === t5);
    return o(s9, (e4) => this._toFeatureJSON(e4));
  }
  getAggregates() {
    return this._tree.findAllIf((e3) => e3.depth === this._geohashLevel).map(this._toFeatureJSON.bind(this));
  }
  getDisplayId(e3) {
    const t5 = this._tree.findIf((t6) => t6.id === e3);
    return o(t5, (e4) => e4.displayId);
  }
  getFeatureDisplayIdsForAggregate(e3) {
    const t5 = this._tree.findIf((t6) => t6.id === e3);
    return g(t5, [], (e4) => Array.from(e4.displayIds));
  }
  getDisplayIdForReferenceId(e3) {
    const t5 = this._tree.findIf((t6) => 1 === t6.displayIds.size && t6.displayIds.has(e3));
    return o(t5, (e4) => e4.displayId);
  }
  _toFeatureJSON(e3) {
    const t5 = this._spatialReference;
    return { displayId: e3.displayId, attributes: e3.getAttributes(), geometry: ut(e3.getGeometry(t5), "esriGeometryPolygon", false, false), centroid: null };
  }
  _rebuildTree() {
    this._bitsets.computed.clear(), this._bitsets.inserted.clear(), this._tree && this._tree.clear();
  }
  _remove(e3) {
    const t5 = e3.getDisplayId(), s9 = e3.getXHydrated(), r6 = e3.getYHydrated(), i5 = this._geohashBuf[2 * t5], o7 = this._geohashBuf[2 * t5 + 1];
    this._bitsets.inserted.has(t5) && (this._bitsets.inserted.unset(t5), this._tree.removeCursor(e3, s9, r6, i5, o7, this._geohashLevel));
  }
  _update(e3, t5) {
    const s9 = e3.getDisplayId(), r6 = this._bitsets.inserted, i5 = t5.isVisible(s9);
    if (i5 === r6.has(s9)) return;
    if (!i5) return void this._remove(e3);
    const o7 = e3.getXHydrated(), a10 = e3.getYHydrated();
    if (!this._setGeohash(s9, o7, a10)) return;
    const h6 = this._geohashBuf[2 * s9], n7 = this._geohashBuf[2 * s9 + 1];
    this._tree.insertCursor(e3, s9, o7, a10, h6, n7, this._geohashLevel), r6.set(s9);
  }
  _setGeohash(e3, t5, s9) {
    if (this._bitsets.geohash.has(e3)) return true;
    const r6 = this._geohashBuf;
    if (this._spatialReference.isWebMercator) {
      const i5 = D2(t5 / s3.radius), o7 = i5 - 360 * Math.floor((i5 + 180) / 360), a10 = D2(Math.PI / 2 - 2 * Math.atan(Math.exp(-s9 / s3.radius)));
      b4(r6, e3, a10, o7, R2);
    } else {
      const i5 = g3({ x: t5, y: s9 }, this._spatialReference, f2.WGS84);
      if (!i5) return false;
      b4(r6, e3, i5.y, i5.x, R2);
    }
    return this._bitsets.geohash.set(e3), true;
  }
  _getBinsForTile(e3, t5, s9, r6) {
    try {
      const i5 = this._getGeohashBounds(t5), o7 = this._tree.getBins(i5);
      for (const t6 of o7) {
        t6.displayId || (t6.displayId = r6.createDisplayId(true));
        let i6 = null;
        const o8 = t6.getGeometry(this._spatialReference, s9.tile);
        o8 || (i6 = t6.getGeometryCentroid(this._spatialReference, s9.tile));
        const a10 = new s4(o8, t6.getAttributes(), i6);
        a10.objectId = t6.id, a10.displayId = t6.displayId, e3.push(a10);
      }
    } catch (i5) {
      return void G3.error("Unable to get bins for tile", t5.key.id);
    }
  }
  _getGeohash(e3, t5, s9) {
    const r6 = { geohashX: 0, geohashY: 0 };
    return Y(r6, t5, e3, s9), r6;
  }
  _getGeohashBounds(e3) {
    const t5 = this._getGeohashLevel(e3.key.level), s9 = [e3.extent.xmin, e3.extent.ymin, e3.extent.xmax, e3.extent.ymax], r6 = v2.fromExtent(w.fromBounds(s9, this._spatialReference)), i5 = g3(r6, this._spatialReference, f2.WGS84, { densificationStep: e3.resolution * L2 }), o7 = W(new t3(), i5, false, false), a10 = o7.coords.filter((e4, t6) => !(t6 % 2)), h6 = o7.coords.filter((e4, t6) => t6 % 2), n7 = Math.min(...a10), d9 = Math.min(...h6), l7 = Math.max(...a10), g8 = Math.max(...h6), p5 = this._getGeohash(n7, d9, t5), c9 = this._getGeohash(l7, g8, t5);
    return { bounds: s9, geohashBounds: { xLL: p5.geohashX, yLL: p5.geohashY, xTR: c9.geohashX, yTR: c9.geohashY }, level: t5 };
  }
  _getGeohashLevel(e3) {
    return this._schema.params.fixedBinLevel;
  }
  _getTransforms(e3, t5) {
    const s9 = { originPosition: "upperLeft", scale: [e3.resolution, e3.resolution], translate: [e3.bounds[0], e3.bounds[3]] }, r6 = R(t5);
    if (!r6) return { tile: s9, left: null, right: null };
    const [i5, o7] = r6.valid;
    return { tile: s9, left: { ...s9, translate: [o7, e3.bounds[3]] }, right: { ...s9, translate: [i5 - o7 + e3.bounds[0], e3.bounds[3]] } };
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/support/ClusterStore.js
var F3 = 12;
var w2 = 64;
var S2 = 1;
var V = a3();
var T5 = class _T extends o2 {
  constructor(e3, t5, s9, r6, o7) {
    super(new t3([], [t5, s9]), r6, null, e3), this.geohashBoundsInfo = o7;
  }
  get count() {
    return this.attributes.cluster_count;
  }
  static create(e3, t5, s9, r6, o7, a10, i5, h6) {
    const n7 = new _T(t5, s9, r6, a10, i5);
    return n7.displayId = e3.createDisplayId(true), n7.referenceId = h6, n7.tileLevel = o7, n7;
  }
  update(e3, t5, s9, r6, o7, a10) {
    return this.geometry.coords[0] = e3, this.geometry.coords[1] = t5, this.tileLevel = s9, this.attributes = r6, this.geohashBoundsInfo = o7, this.referenceId = null, this.referenceId = a10, this;
  }
  toJSON() {
    return { attributes: { ...this.attributes, aggregateId: this.objectId, referenceId: 1 === this.attributes.cluster_count ? this.referenceId : null }, geometry: { x: this.geometry.coords[0], y: this.geometry.coords[1] } };
  }
};
function D3(e3) {
  return 57.29577951308232 * e3;
}
var G4 = class extends c4 {
  constructor(t5, s9, r6, o7) {
    super(t5, r6), this.type = "cluster", this.events = new n(), this.objectIdField = "aggregateId", this.featureAdapter = p, this._geohashLevel = 0, this._tileLevel = 0, this._aggregateValueRanges = {}, this._aggregateValueRangesChanged = false, this._geohashBuf = [], this._clusters = /* @__PURE__ */ new Map(), this._tiles = /* @__PURE__ */ new Map(), this._serviceInfo = o7, this.geometryInfo = t5.geometryInfo, this._spatialReference = s9, this._projectionSupportCheck = f4(s9, f2.WGS84), this._bitsets.geohash = r6.getBitset(r6.createBitset()), this._bitsets.inserted = r6.getBitset(r6.createBitset());
  }
  destroy() {
    this._tree.destroy();
  }
  get featureSpatialReference() {
    return this._spatialReference;
  }
  get fields() {
    return this._fields;
  }
  async updateSchema(e3, s9) {
    const r6 = this._schema;
    try {
      await super.updateSchema(e3, s9), await this._projectionSupportCheck;
    } catch (n7) {
    }
    this._fields = this._schema.params.fields;
    const h6 = m(r6, s9);
    s9 && (!t(h6) || e3.source || e3.storage.filters) ? ((a4(h6, "params.fields") || !this._tree || e3.source) && (this._tree && this._tree.destroy(), this._tree = new u(this._statisticFields, this._serviceInfo), this._rebuildTree(), has("esri-2d-update-debug") && console.debug("Aggregate mesh needs update due to tree changing")), has("esri-2d-update-debug") && console.debug("Applying Update - ClusterStore:", h6), e3.targets[s9.name] = true, e3.mesh = false, this._aggregateValueRanges = {}) : r6 && (e3.mesh = true);
  }
  clear() {
    this._rebuildTree();
  }
  sweepFeatures(e3, t5) {
    this._bitsets.inserted.forEachSet((s9) => {
      if (!e3.has(s9)) {
        const e4 = t5.lookupByDisplayIdUnsafe(s9);
        this._remove(e4);
      }
    });
  }
  sweepAggregates(e3, t5, s9) {
    this._clusters.forEach((r6, o7) => {
      r6 && r6.tileLevel !== s9 && (e3.releaseDisplayId(r6.displayId), t5.unsetAttributeData(r6.displayId), this._clusters.delete(o7));
    });
  }
  onTileData(e3, s9, r6, o7, a10 = true) {
    if (!this._schema || t(s9.addOrUpdate)) return s9;
    this.events.emit("changed");
    const i5 = this._getTransforms(e3, this._spatialReference);
    {
      const e4 = s9.addOrUpdate.getCursor();
      for (; e4.next(); ) this._update(e4, o7);
    }
    if (s9.status.mesh || !a10) return s9;
    const h6 = new Array(), n7 = this._schema.params.clusterRadius;
    this._getClustersForTile(h6, e3, n7, r6, i5), s9.addOrUpdate = c5.fromOptimizedFeatures(h6, this._serviceInfo), s9.addOrUpdate.attachStorage(r6), s9.clear = true, s9.end = true;
    {
      const t5 = s9.addOrUpdate.getCursor();
      for (; t5.next(); ) {
        const s10 = t5.getDisplayId();
        this._bitsets.computed.unset(s10), this.setComputedAttributes(r6, t5, s10, e3.scale);
      }
    }
    return this._aggregateValueRangesChanged && s9.end && (this.events.emit("valueRangesChanged", { valueRanges: this._aggregateValueRanges }), this._aggregateValueRangesChanged = false), s9;
  }
  onTileUpdate({ added: e3, removed: t5 }) {
    if (e3.length) {
      const t6 = e3[0].level;
      this._tileLevel = t6, this._setGeohashLevel(t6);
    }
    if (!this._schema) return;
    const s9 = this._schema.params.clusterRadius;
    t5.forEach((e4) => {
      this._tiles.delete(e4.key.id), this._markTileClustersForDeletion(e4, s9);
    });
  }
  getAggregate(e3) {
    for (const t5 of this._clusters.values()) if (((t5 == null ? void 0 : t5.displayId) & n3) == (e3 & n3)) return t5.toJSON();
    return null;
  }
  getAggregates() {
    const e3 = [];
    for (const t5 of this._clusters.values()) (t5 == null ? void 0 : t5.tileLevel) === this._tileLevel && e3.push(t5.toJSON());
    return e3;
  }
  getDisplayId(e3) {
    const t5 = this._clusters.get(e3);
    return t5 ? t5.displayId : null;
  }
  getFeatureDisplayIdsForAggregate(e3) {
    const t5 = this._clusters.get(e3);
    return t5 ? this._tree.getRegionDisplayIds(t5.geohashBoundsInfo) : [];
  }
  getDisplayIdForReferenceId(e3) {
    for (const t5 of this._clusters.values()) if ((t5 == null ? void 0 : t5.referenceId) === e3) return t5.displayId;
    return null;
  }
  getAggregateValueRanges() {
    return this._aggregateValueRanges;
  }
  forEach(e3) {
    this._clusters.forEach((t5) => {
      if (!t5) return;
      const s9 = t5.toJSON(), r6 = c5.fromFeatures([s9], { objectIdField: this.objectIdField, globalIdField: null, geometryType: this.geometryInfo.geometryType, fields: this.fields }).getCursor();
      r6.next(), e3(r6);
    });
  }
  forEachInBounds(e3, t5) {
  }
  forEachBounds(e3, s9) {
    const { hasM: r6, hasZ: o7 } = this.geometryInfo;
    for (const a10 of e3) {
      const e4 = It(V, a10.readGeometry(), o7, r6);
      t(e4) || s9(e4);
    }
  }
  size() {
    let e3 = 0;
    return this.forEach((t5) => e3++), e3;
  }
  _rebuildTree() {
    this._bitsets.computed.clear(), this._bitsets.inserted.clear(), this._tree && this._tree.clear();
  }
  _remove(e3) {
    const t5 = e3.getDisplayId(), s9 = e3.getXHydrated(), r6 = e3.getYHydrated(), o7 = this._geohashBuf[2 * t5], a10 = this._geohashBuf[2 * t5 + 1];
    this._bitsets.inserted.has(t5) && (this._bitsets.inserted.unset(t5), this._tree.removeCursor(e3, s9, r6, o7, a10, this._geohashLevel));
  }
  _update(e3, t5) {
    const s9 = e3.getDisplayId(), r6 = this._bitsets.inserted, o7 = t5.isVisible(s9);
    if (o7 === r6.has(s9)) return;
    if (!o7) return void this._remove(e3);
    const a10 = e3.getXHydrated(), i5 = e3.getYHydrated();
    if (!this._setGeohash(s9, a10, i5)) return;
    const h6 = this._geohashBuf[2 * s9], n7 = this._geohashBuf[2 * s9 + 1];
    this._tree.insertCursor(e3, s9, a10, i5, h6, n7, this._geohashLevel), r6.set(s9);
  }
  _setGeohash(e3, t5, s9) {
    if (this._bitsets.geohash.has(e3)) return true;
    const r6 = this._geohashBuf;
    if (this._spatialReference.isWebMercator) {
      const o7 = D3(t5 / s3.radius), a10 = o7 - 360 * Math.floor((o7 + 180) / 360), i5 = D3(Math.PI / 2 - 2 * Math.atan(Math.exp(-s9 / s3.radius)));
      b4(r6, e3, i5, a10, F3);
    } else {
      const o7 = g3({ x: t5, y: s9 }, this._spatialReference, f2.WGS84);
      if (!o7) return false;
      b4(r6, e3, o7.y, o7.x, F3);
    }
    return this._bitsets.geohash.set(e3), true;
  }
  _getClustersForTile(e3, o7, a10, i5, h6, n7 = true) {
    const l7 = this._schema.params.clusterPixelBuffer, u3 = 2 * a10, g8 = Math.ceil(2 ** o7.key.level * o3 / u3) + 1, c9 = Math.ceil(l7 / u3) + 0, p5 = Math.ceil(o3 / u3), { row: m4, col: y6 } = o7.key, I3 = y6 * o3, b5 = m4 * o3, R4 = Math.floor(I3 / u3) - c9, M2 = Math.floor(b5 / u3) - c9, x4 = R4 + p5 + 2 * c9, C = M2 + p5 + 2 * c9, j5 = o7.tileInfoView.getLODInfoAt(o7.key.level);
    for (let v6 = R4; v6 <= x4; v6++) for (let a11 = M2; a11 <= C; a11++) {
      let l8 = v6;
      j5.wrap && (l8 = v6 < 0 ? v6 + g8 : v6 % g8);
      const u4 = j5.wrap && v6 < 0, c10 = j5.wrap && v6 % g8 !== v6, p6 = this._lookupCluster(i5, j5, o7.key.level, l8, a11, o7);
      if (r(p6)) {
        const o8 = o(h6, (e4) => u4 ? e4.left : c10 ? e4.right : e4.tile);
        if (n7 && t(o8)) continue;
        if (!p6.count) continue;
        if (r(o8) && n7) {
          const t5 = p6.geometry.clone();
          let r6 = p6.attributes;
          t5.coords[0] = G(o8, t5.coords[0]), t5.coords[1] = T(o8, t5.coords[1]), 1 === p6.count && r(p6.referenceId) && (r6 = { ...p6.attributes, referenceId: p6.referenceId });
          const a12 = new s4(t5, r6);
          a12.displayId = p6.displayId, e3.push(a12);
        }
      }
    }
  }
  _getGeohashLevel(e3) {
    return Math.min(Math.ceil(e3 / 2 + 2), F3);
  }
  _setGeohashLevel(e3) {
    const t5 = this._getGeohashLevel(e3), s9 = (Math.floor(t5 / S2) + 1) * S2 - 1;
    if (this._geohashLevel !== s9) return this._geohashLevel = s9, this._rebuildTree(), void this._bitsets.geohash.clear();
  }
  _getTransforms(e3, t5) {
    const s9 = { originPosition: "upperLeft", scale: [e3.resolution, e3.resolution], translate: [e3.bounds[0], e3.bounds[3]] }, r6 = R(t5);
    if (!r6) return { tile: s9, left: null, right: null };
    const [o7, a10] = r6.valid;
    return { tile: s9, left: { ...s9, translate: [a10, e3.bounds[3]] }, right: { ...s9, translate: [o7 - a10 + e3.bounds[0], e3.bounds[3]] } };
  }
  _getClusterId(e3, t5, s9) {
    return (15 & e3) << 28 | (16383 & t5) << 14 | 16383 & s9;
  }
  _markForDeletion(e3, t5, s9) {
    const r6 = this._getClusterId(e3, t5, s9);
    this._clusters.delete(r6);
  }
  _getClusterBounds(e3, t5, s9) {
    const r6 = this._schema.params.clusterRadius, o7 = 2 * r6;
    let a10 = s9 % 2 ? t5 * o7 : t5 * o7 - r6;
    const i5 = s9 * o7;
    let h6 = a10 + o7;
    const n7 = i5 - o7, l7 = 2 ** e3.level * o3;
    e3.wrap && a10 < 0 && (a10 = 0), e3.wrap && h6 > l7 && (h6 = l7);
    const u3 = a10 / o3, g8 = i5 / o3, c9 = h6 / o3, d9 = n7 / o3;
    return [e3.getXForColumn(u3), e3.getYForRow(g8), e3.getXForColumn(c9), e3.getYForRow(d9)];
  }
  _getGeohash(e3, t5, s9) {
    const r6 = { geohashX: 0, geohashY: 0 };
    return Y(r6, t5, e3, s9), r6;
  }
  _getGeohashBounds(e3, t5) {
    const s9 = this._getGeohashLevel(e3.key.level);
    if (this._spatialReference.isWebMercator) {
      const [e4, r7, o8, a11] = t5, i6 = { x: e4, y: r7 }, h7 = { x: o8, y: a11 };
      let l8 = 0, g9 = 0, c10 = 0, d10 = 0;
      {
        const e5 = D3(i6.x / s3.radius);
        l8 = e5 - 360 * Math.floor((e5 + 180) / 360), g9 = D3(Math.PI / 2 - 2 * Math.atan(Math.exp(-i6.y / s3.radius)));
      }
      {
        const e5 = D3(h7.x / s3.radius);
        c10 = e5 - 360 * Math.floor((e5 + 180) / 360), d10 = D3(Math.PI / 2 - 2 * Math.atan(Math.exp(-h7.y / s3.radius)));
      }
      const f11 = { geohashX: 0, geohashY: 0 }, p5 = { geohashX: 0, geohashY: 0 };
      Y(f11, g9, l8, s9), Y(p5, d10, c10, s9);
      return { bounds: [e4, r7, o8, a11], geohashBounds: { xLL: f11.geohashX, yLL: f11.geohashY, xTR: p5.geohashX, yTR: p5.geohashY }, level: s9 };
    }
    const r6 = v2.fromExtent(w.fromBounds(t5, this._spatialReference)), o7 = g3(r6, this._spatialReference, f2.WGS84, { densificationStep: e3.resolution * w2 });
    if (!o7) return null;
    const a10 = W(new t3(), o7, false, false), i5 = a10.coords.filter((e4, t6) => !(t6 % 2)), h6 = a10.coords.filter((e4, t6) => t6 % 2), l7 = Math.min(...i5), g8 = Math.min(...h6), c9 = Math.max(...i5), d9 = Math.max(...h6), f10 = this._getGeohash(l7, g8, s9), m4 = this._getGeohash(c9, d9, s9);
    return { bounds: t5, geohashBounds: { xLL: f10.geohashX, yLL: f10.geohashY, xTR: m4.geohashX, yTR: m4.geohashY }, level: s9 };
  }
  _lookupCluster(e3, r6, o7, a10, i5, h6) {
    const n7 = this._getClusterId(o7, a10, i5), l7 = this._clusters.get(n7), u3 = this._getClusterBounds(r6, a10, i5), g8 = this._getGeohashBounds(h6, u3);
    if (t(g8)) return null;
    const c9 = this._tree.getRegionStatistics(g8), { count: d9, xTotal: f10, yTotal: p5, referenceId: m4 } = c9, _3 = d9 ? f10 / d9 : 0, y6 = d9 ? p5 / d9 : 0;
    if (0 === d9) return this._clusters.set(n7, null), null;
    const I3 = { cluster_count: d9, ...c9.attributes }, b5 = r(l7) ? l7.update(_3, y6, o7, I3, g8, m4) : T5.create(e3, n7, _3, y6, o7, I3, g8, m4);
    if (0 === d9) {
      const [e4, t5, s9, r7] = u3;
      b5.geometry.coords[0] = (e4 + s9) / 2, b5.geometry.coords[1] = (t5 + r7) / 2;
    }
    return this._clusters.set(n7, b5), this._updateAggregateValueRangeForCluster(b5, b5.tileLevel), b5;
  }
  _updateAggregateValueRangeForCluster(e3, t5) {
    const s9 = this._aggregateValueRanges[t5] || { minValue: 1 / 0, maxValue: 0 }, r6 = s9.minValue, o7 = s9.maxValue;
    s9.minValue = Math.min(r6, e3.count), s9.maxValue = Math.max(o7, e3.count), this._aggregateValueRanges[t5] = s9, r6 === s9.minValue && o7 === s9.maxValue || (this._aggregateValueRangesChanged = true);
  }
  _markTileClustersForDeletion(e3, t5) {
    const s9 = 2 * t5, r6 = Math.ceil(o3 / s9), { row: o7, col: a10 } = e3.key, i5 = a10 * o3, h6 = o7 * o3, n7 = Math.floor(i5 / s9), l7 = Math.floor(h6 / s9);
    for (let u3 = n7; u3 < n7 + r6; u3++) for (let t6 = l7; t6 < l7 + r6; t6++) this._markForDeletion(e3.key.level, u3, t6);
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/controllers/FeatureController2D.js
var T6 = 5e3;
var k = "tileRenderer.featuresView.attributeView.initialize";
var j4 = "tileRenderer.featuresView.attributeView.requestUpdate";
var x3 = "tileRenderer.featuresView.requestRender";
function R3(e3) {
  return "worker:port-closed" === e3.name;
}
function A2(e3) {
  if (!j(e3) && !R3(e3)) throw e3;
}
function U4(e3) {
  return "feature" === e3.type && "snapshot" === e3.mode;
}
var q5 = class extends v {
  constructor() {
    super(...arguments), this._storage = new r4(), this._markedIdsBufId = this._storage.createBitset(), this._lastCleanup = performance.now(), this._cleanupNeeded = false, this._invalidated = false, this._tileToResolver = /* @__PURE__ */ new Map(), this._didEdit = false, this._updateVersion = 1, this.tileStore = null, this.config = null, this.processor = null, this.remoteClient = null, this.service = null;
  }
  initialize() {
    this._initStores(), this._initSource(), this._updateQueue = new l3({ concurrency: "stream" === this._source.type ? 1 : 4, process: (e3, t5) => this._onTileMessage(e3, { signal: t5 }) }), this.addHandles([this.tileStore.on("update", this.onTileUpdate.bind(this)), f3(() => !this.updating, () => this.onIdle())]), this._checkUpdating = setInterval(() => this.notifyChange("updating"), 300);
  }
  _initSource() {
    const e3 = this.tileStore.tileScheme, t5 = () => this._updateQueue && this._updateQueue.length < 50, r6 = (e4, t6) => (this._invalidated = true, this._patchTile(e4, t6));
    this._source = a9(this.service, this.spatialReference, e3, r6, t5, this.featureStore), this._proxyEvents();
  }
  _proxyEvents() {
    if ("stream" === this._source.type) {
      const e3 = this._source.events, t5 = this._source;
      this.addHandles([l2(() => t5.connectionStatus, (e4) => this.remoteClient.invoke("setProperty", { propertyName: "connectionStatus", value: e4 }).catch(A2), { initial: true }), l2(() => t5.errorString, (e4) => this.remoteClient.invoke("setProperty", { propertyName: "errorString", value: e4 }).catch(A2), { initial: true }), e3.on("data-received", (e4) => this.remoteClient.invoke("emitEvent", { name: "data-received", event: { attributes: e4.attributes, centroid: e4.centroid, geometry: e4.geometry } }).catch(A2)), e3.on("message-received", (e4) => this.remoteClient.invoke("emitEvent", { name: "message-received", event: e4 }).catch(A2)), e3.on("updateRate", (e4) => this.remoteClient.invoke("emitEvent", { name: "update-rate", event: { ...e4 } }).catch(A2))]);
    }
  }
  _initAttributeStore(e3) {
    this.attributeStore || (this.attributeStore = new M({ type: "remote", initialize: (e4, t5) => g2(this.remoteClient.invoke(k, e4, { signal: t5 }).catch(A2)), update: (e4, t5) => g2(this.remoteClient.invoke(j4, e4, { signal: t5 }).catch(A2)), render: (e4) => g2(this.remoteClient.invoke(x3, void 0, { signal: e4 }).catch(A2)) }, e3, () => this.notifyChange("updating")));
  }
  _initStores() {
    const e3 = "snapshot" === this.service.type ? "snapshot" : "on-demand", t5 = { geometryInfo: { geometryType: this.service.geometryType, hasM: false, hasZ: false }, spatialReference: this.spatialReference, fieldsIndex: this.fieldsIndex, fields: this.service.fields };
    this.featureStore = new g5(t5, this._storage, e3);
  }
  _initQueryEngine(e3) {
    var _a;
    const t5 = this;
    (_a = this.featureQueryEngine) == null ? void 0 : _a.destroy(), this.featureQueryEngine = new ee({ definitionExpression: e3.schema.source.definitionExpression ?? void 0, fields: this.service.fields, geometryType: this.service.geometryType, objectIdField: this.service.objectIdField, hasM: false, hasZ: false, spatialReference: this.spatialReference.toJSON(), cacheSpatialQueries: true, featureStore: this.featureStore, aggregateAdapter: { getFeatureObjectIds(e4) {
      if (t(t5.aggregateStore)) return [];
      return t5.aggregateStore.getFeatureDisplayIdsForAggregate(e4).map((e5) => t5.getObjectId(e5));
    } }, timeInfo: this.service.timeInfo });
  }
  _initAggregateQueryEngine(e3, t5) {
    var _a;
    if ((_a = this.aggregateQueryEngine) == null ? void 0 : _a.destroy(), t(e3)) return;
    const s9 = t5.targets.aggregate.params.fields.slice();
    this.aggregateQueryEngine = new ee({ definitionExpression: void 0, fields: s9, geometryType: e3.geometryInfo.geometryType, objectIdField: e3.objectIdField, hasM: e3.geometryInfo.hasM, hasZ: e3.geometryInfo.hasZ, spatialReference: this.spatialReference.toJSON(), cacheSpatialQueries: false, featureStore: e3, aggregateAdapter: { getFeatureObjectIds: (e4) => [] } });
  }
  destroy() {
    var _a, _b, _c;
    this._updateQueue.destroy(), this._source.destroy(), (_a = this.featureQueryEngine) == null ? void 0 : _a.destroy(), (_b = this.aggregateQueryEngine) == null ? void 0 : _b.destroy(), (_c = this.attributeStore) == null ? void 0 : _c.destroy();
    for (const e3 of this.tileStore.tiles) this._source.unsubscribe(e3);
    clearInterval(this._checkUpdating);
  }
  get fieldsIndex() {
    return new r2(this.service.fields);
  }
  get spatialReference() {
    return this.tileStore.tileScheme.spatialReference;
  }
  get updating() {
    return this.isUpdating();
  }
  isUpdating() {
    const e3 = this._source.updating, t5 = !!this._updateQueue.length, r6 = !this.attributeStore || this.attributeStore.isUpdating(), s9 = e3 || t5 || r6;
    return has("esri-2d-log-updating") && console.log(`Updating FeatureController2D: ${s9}
  -> updatingSource ${e3}
  -> updateQueue ${t5}
  -> updatingAttributeStore ${r6}
`), s9;
  }
  updateCustomParameters(e3) {
    "stream" === this._source.type && this._source.updateCustomParameters(e3);
  }
  enableEvent(e3) {
    this._source.enableEvent(e3.name, e3.value);
  }
  pause() {
    this._updateQueue.pause(), this._updateQueue.clear();
  }
  resume() {
    this._updateQueue.resume();
  }
  pauseStream() {
    "stream" === this._source.type && this._source.pauseStream();
  }
  resumeStream() {
    "stream" === this._source.type && this._source.resumeStream();
  }
  sendMessageToSocket(e3) {
    "stream" === this._source.type && this._source.sendMessageToSocket(e3);
  }
  sendMessageToClient(e3) {
    "stream" === this._source.type && this._source.sendMessageToClient(e3);
  }
  _initAggregateStore(e3) {
    var _a, _b;
    const t5 = (_b = (_a = e3.schema.targets) == null ? void 0 : _a.aggregate) == null ? void 0 : _b.type, r6 = o(this.config, (e4) => {
      var _a2, _b2;
      return (_b2 = (_a2 = e4.schema.targets) == null ? void 0 : _a2.aggregate) == null ? void 0 : _b2.type;
    });
    if (r6 !== t5 && (r(this.aggregateStore) && (this.removeHandles("valueRangesChanged"), this.aggregateStore.destroy(), this.aggregateStore = null), t5)) {
      switch (t5) {
        case "cluster": {
          const e4 = { geometryInfo: { geometryType: "esriGeometryPoint", hasM: false, hasZ: false }, spatialReference: this.spatialReference, fieldsIndex: this.fieldsIndex, fields: this.service.fields };
          this.aggregateStore = new G4(e4, this.spatialReference, this._storage, this.service), this.addHandles(this.aggregateStore.events.on("valueRangesChanged", (e5) => {
            this.remoteClient.invoke("emitEvent", { name: "valueRangesChanged", event: { valueRanges: e5.valueRanges } }).catch(A2);
          }), "valueRangesChanged");
          break;
        }
        case "bin": {
          const e4 = { geometryInfo: { geometryType: "esriGeometryPolygon", hasM: false, hasZ: false }, spatialReference: this.spatialReference, fieldsIndex: this.fieldsIndex, fields: this.service.fields };
          this.aggregateStore = new O(e4, this.spatialReference, this._storage, this.service);
          break;
        }
      }
      this.aggregateStore.onTileUpdate({ added: this.tileStore.tiles, removed: [] });
    }
  }
  async update(e3, t5) {
    this._updateVersion++, this._initQueryEngine(t5), this._initAttributeStore(t5), this.pause(), await Promise.all([this._source.update(e3, t5.schema.source), this.featureStore.updateSchema(e3, t5.schema.targets.feature), this.attributeStore.update(e3, t5), this.attributeStore.updateFilters(e3, t5, this)]), this._initAggregateStore(t5), r(this.aggregateStore) && await this.aggregateStore.updateSchema(e3, t5.schema.targets.aggregate), this._initAggregateQueryEngine(this.aggregateStore, t5.schema), has("esri-2d-update-debug") && e3.describe(), this._set("config", t5);
  }
  async applyUpdate(e3) {
    e3.version = this._updateVersion, has("esri-2d-update-debug") && console.debug(`Applying update ${e3.version}`), e3.mesh && this.clearTiles(), this._updateQueue.resume(), await this._source.applyUpdate(e3), this.notifyChange("updating"), await j2(() => !this.updating), r(this.aggregateStore) && (await U(10), await j2(() => !this.updating));
  }
  async onEdits({ edits: e3 }) {
    has("esri-2d-update-debug") && console.debug("Applying Edit:", e3), this._didEdit = true;
    try {
      const t5 = e3.removed.map((e4) => e4.objectId && -1 !== e4.objectId ? e4.objectId : this._lookupObjectIdByGlobalId(e4.globalId)), r6 = e3.addOrModified.map(({ objectId: e4 }) => e4);
      this.featureStore.invalidate(), await this._source.edit(r6, t5), this.clearTiles(), this.notifyChange("updating"), r(this.aggregateStore) && this.aggregateStore.clear(), await this._source.resend(), await j2(() => !this.updating);
    } catch (t5) {
    }
  }
  async refresh(e3) {
    if (!e3.dataChanged) {
      const e4 = t4.empty();
      return e4.storage.filters = true, this.applyUpdate(e4);
    }
    this.featureStore.invalidate(), this.clearTiles(), this._source.refresh(this._updateVersion, e3), this._cleanupNeeded = true, this.notifyChange("updating"), await j2(() => !this.updating);
  }
  clearTiles() {
    for (const e3 of this.tileStore.tiles) this.processor.onTileClear(e3);
  }
  onTileUpdate(e3) {
    r(this.aggregateStore) && this.aggregateStore.onTileUpdate(e3);
    for (const t5 of e3.added) this._source.subscribe(t5, this._updateVersion), this._level = t5.level;
    for (const t5 of e3.removed) this._source.unsubscribe(t5), this._cleanupNeeded = true, this._tileToResolver.has(t5.id) && (this._tileToResolver.get(t5.id).resolve(), this._tileToResolver.delete(t5.id));
    this.notifyChange("updating");
  }
  async onIdle() {
    this._invalidated && (this._invalidated = false, (r(this.aggregateStore) || "heatmap" === this.processor.type) && await this._repushCurrentLevelTiles()), this._markAndSweep();
  }
  async querySummaryStatistics({ query: e3, params: t5 }) {
    return this.featureQueryEngine.executeQueryForSummaryStatistics(e3, t5);
  }
  async queryAggregateSummaryStatistics({ query: e3, params: t5 }) {
    return this.aggregateQueryEngine.executeQueryForSummaryStatistics(e3, t5);
  }
  async queryUniqueValues({ query: e3, params: t5 }) {
    return this.featureQueryEngine.executeQueryForUniqueValues(e3, t5);
  }
  async queryAggregateUniqueValues({ query: e3, params: t5 }) {
    return this.aggregateQueryEngine.executeQueryForUniqueValues(e3, t5);
  }
  async queryClassBreaks({ query: e3, params: t5 }) {
    return this.featureQueryEngine.executeQueryForClassBreaks(e3, t5);
  }
  async queryAggregateClassBreaks({ query: e3, params: t5 }) {
    return this.aggregateQueryEngine.executeQueryForClassBreaks(e3, t5);
  }
  async queryHistogram({ query: e3, params: t5 }) {
    return this.featureQueryEngine.executeQueryForHistogram(e3, t5);
  }
  async queryAggregateHistogram({ query: e3, params: t5 }) {
    return this.aggregateQueryEngine.executeQueryForHistogram(e3, t5);
  }
  queryExtent(e3) {
    return this.featureQueryEngine.executeQueryForExtent(e3);
  }
  queryAggregates(e3) {
    return this.aggregateQueryEngine.executeQuery(e3);
  }
  queryAggregateCount(e3) {
    return this.aggregateQueryEngine.executeQueryForCount(e3);
  }
  queryAggregateIds(e3) {
    return this.aggregateQueryEngine.executeQueryForIds(e3);
  }
  queryFeatures(e3) {
    return this.featureQueryEngine.executeQuery(e3);
  }
  async queryVisibleFeatures(e3) {
    const t5 = await this.featureQueryEngine.executeQuery(e3), r6 = t5.objectIdFieldName;
    return t5.features = t5.features.filter((e4) => {
      const t6 = e4.attributes[r6], i5 = this.getDisplayId(t6);
      return o(i5, (e5) => this.attributeStore.isVisible(e5));
    }), t5;
  }
  queryFeatureCount(e3) {
    return this.featureQueryEngine.executeQueryForCount(e3);
  }
  queryLatestObservations(e3) {
    return this.featureQueryEngine.executeQueryForLatestObservations(e3);
  }
  queryObjectIds(e3) {
    return this.featureQueryEngine.executeQueryForIds(e3);
  }
  async queryStatistics() {
    return this.featureStore.storeStatistics;
  }
  getObjectId(e3) {
    return this.featureStore.lookupObjectId(e3, this._storage);
  }
  getDisplayId(e3) {
    if (r(this.aggregateStore)) {
      const t5 = this.aggregateStore.getDisplayId(e3);
      if (t(t5)) {
        const t6 = this.featureStore.lookupDisplayId(e3);
        return this.aggregateStore.getDisplayIdForReferenceId(t6);
      }
      return t5;
    }
    return this.featureStore.lookupDisplayId(e3);
  }
  getFeatures(e3) {
    const t5 = [], r6 = [];
    for (const s9 of e3) {
      const e4 = r(this.aggregateStore) ? this.getAggregate(s9) : null;
      if (r(e4)) if (r(e4.attributes.referenceId)) {
        const r7 = this.getFeature(e4.attributes.referenceId);
        r(r7) && t5.push(r7);
      } else r6.push(e4);
      else {
        const e5 = this.getFeature(s9);
        r(e5) && t5.push(e5);
      }
    }
    return { features: t5, aggregates: r6 };
  }
  getFeature(e3) {
    const t5 = this.featureStore.lookupFeatureByDisplayId(e3, this._storage);
    if (t(t5)) return null;
    const s9 = t5.readHydratedGeometry(), i5 = ut(s9, t5.geometryType, t5.hasZ, t5.hasM);
    return { attributes: t5.readAttributes(), geometry: i5 };
  }
  getAggregate(e3) {
    return t(this.aggregateStore) ? null : this.aggregateStore.getAggregate(e3);
  }
  getAggregates() {
    return t(this.aggregateStore) ? [] : this.aggregateStore.getAggregates();
  }
  async setHighlight(e3) {
    const t5 = q(e3.map((e4) => this.getDisplayId(e4)));
    return this.attributeStore.setHighlight(e3, t5);
  }
  _lookupObjectIdByGlobalId(e3) {
    const t5 = this.service.globalIdField;
    if (t(t5)) throw new Error("Expected globalIdField to be defined");
    let s9 = null;
    if (this.featureStore.forEach((r6) => {
      e3 === r6.readAttribute(t5) && (s9 = r6.getObjectId());
    }), t(s9)) throw new Error(`Expected to find a feature with globalId ${e3}`);
    return s9;
  }
  async _repushCurrentLevelTiles() {
    const e3 = this.tileStore.tiles.filter((e4) => e4.level === this._level);
    e3.map(async (e4) => this._patchTile({ type: "append", id: e4.key.id, clear: true, addOrUpdate: null, end: false }));
    const t5 = e3.map(async (e4) => this._patchTile({ type: "append", id: e4.key.id, addOrUpdate: c5.fromOptimizedFeatures([], this.service), remove: [], end: true, isRepush: true, status: t4.empty() }));
    await Promise.all(t5);
  }
  _maybeForceCleanup() {
    performance.now() - this._lastCleanup > T6 && this._markAndSweep();
  }
  _patchTile(e3, t5) {
    const r6 = this._updateQueue.push(e3, t5).then(() => {
      this.notifyChange("updating");
    }).catch((e4) => {
      this.notifyChange("updating");
    });
    return this.notifyChange("updating"), r6;
  }
  async _onTileMessage(e3, t5) {
    if (f(t5), has("esri-2d-update-debug")) {
      const t6 = o(e3.addOrUpdate, (e4) => e4.hasFeatures);
      console.debug(e3.id, `FeatureController:onTileMessage: [clear:${e3.clear}, end:${e3.end}, features: ${t6}]`);
    }
    const a10 = this.tileStore.get(e3.id);
    if (!a10) return;
    if (e3.clear) return this.processor.onTileClear(a10);
    const o7 = e3.status;
    this._cleanupNeeded = true;
    const n7 = [];
    for (const r6 of e3.remove ?? []) {
      const e4 = this.featureStore.lookupDisplayId(r6);
      e4 && n7.push(e4);
    }
    e3.remove = n7;
    try {
      if (t(e3.addOrUpdate)) return void this.processor.onTileMessage(a10, { ...e3, addOrUpdate: null }, r(this.aggregateStore), t5).catch(b);
      if (e3.addOrUpdate.setArcadeSpatialReference(this.spatialReference), this.featureStore.hasInstance(e3.addOrUpdate.instance) && o7.targets.feature || (o7.targets.feature = true, this.featureStore.onTileData(a10, e3)), !o7.storage.data || !o7.storage.filters) {
        o7.storage.data = true, o7.storage.filters = true, this.attributeStore.onTileData(a10, e3);
        "stream" === this._source.type || this._didEdit ? (await this.attributeStore.sendUpdates(), f(t5)) : this.attributeStore.sendUpdates();
      }
      if (r(this.aggregateStore) && !o7.targets.aggregate) {
        o7.targets.aggregate = true;
        const t6 = U4(this._source) && this._source.loading, r6 = !U4(this._source) || t6 || e3.end;
        if (this.aggregateStore.onTileData(a10, e3, this._storage, this.attributeStore, r6), !r6) return;
        o7.mesh || (this.attributeStore.onTileData(a10, e3), await this.attributeStore.sendUpdates());
      }
      if (!o7.mesh) {
        o7.mesh = true;
        const r6 = r(this.aggregateStore) && "cluster" === this.aggregateStore.type;
        await this.processor.onTileMessage(a10, e3, r6, t5), f(t5);
      }
      this._maybeForceCleanup();
    } catch (h6) {
      b(h6);
    }
  }
  _mark(e3, t5, r6) {
    const s9 = (4294901760 & this._storage.getInstanceId(e3)) >>> 16;
    e3 && (t5.add(s9), r6.set(e3));
  }
  _markAndSweep() {
    this._lastCleanup = performance.now();
    if (!(!("feature" === this._source.type && "snapshot" === this._source.mode) && ("stream" === this._source.type || this._cleanupNeeded))) return;
    this._cleanupNeeded = false;
    const e3 = this._storage.getBitset(this._markedIdsBufId), t5 = /* @__PURE__ */ new Set();
    e3.clear();
    for (const r6 of this.tileStore.tiles) for (const s9 of this._source.readers(r6.id)) {
      const r7 = s9.getCursor();
      for (; r7.next(); ) {
        let s10 = r7.getDisplayId();
        if (!s10) {
          const e4 = r7.getObjectId();
          s10 = this.featureStore.lookupDisplayId(e4);
        }
        this._mark(s10, t5, e3);
      }
    }
    "symbol" === this.processor.type && this.processor.forEachBufferId((r6) => {
      this._mark(r6, t5, e3);
    }), this._updateQueue.forEach((r6) => {
      for (const s9 of r6.remove ?? []) {
        const r7 = this.featureStore.lookupDisplayId(s9);
        this._mark(r7, t5, e3);
      }
    }), r(this.aggregateStore) && (this.aggregateStore.sweepFeatures(e3, this.featureStore), "sweepAggregates" in this.aggregateStore && this.aggregateStore.sweepAggregates(this._storage, this.attributeStore, this._level)), this.featureStore.sweepFeatures(e3, this._storage, this.attributeStore), this.featureStore.sweepFeatureSets(t5);
  }
};
e2([y({ constructOnly: true })], q5.prototype, "tileStore", void 0), e2([y()], q5.prototype, "config", void 0), e2([y({ readOnly: true })], q5.prototype, "fieldsIndex", null), e2([y()], q5.prototype, "processor", void 0), e2([y({ constructOnly: true })], q5.prototype, "remoteClient", void 0), e2([y({ constructOnly: true })], q5.prototype, "service", void 0), e2([y()], q5.prototype, "spatialReference", null), e2([y()], q5.prototype, "updating", null), q5 = e2([a("esri.views.2d.layers.features.controllers.FeatureController2D")], q5);
var O2 = q5;

// node_modules/@arcgis/core/views/2d/layers/features/Pipeline.js
var d8 = class extends d {
  constructor() {
    super(...arguments), this.controller = null, this.processor = null, this.remoteClient = null, this.tileStore = null, this.service = null, this.viewState = null, this._paused = false, this._pendingTileUpdates = [];
  }
  initialize() {
    this.handles.add(l2(() => this.updating, (e3) => {
      this.remoteClient.invoke("setUpdating", e3).catch((e4) => {
      });
    }));
  }
  destroy() {
    var _a, _b;
    this.stop(), (_a = this.controller) == null ? void 0 : _a.destroy(), (_b = this.processor) == null ? void 0 : _b.destroy(), this.controller = this.processor = this.tileStore = this.remoteClient = null;
  }
  get updating() {
    return !this.controller || this.controller.updating;
  }
  stop() {
    var _a, _b, _c;
    this._paused = true, Array.isArray((_a = this.service) == null ? void 0 : _a.source) && (this.service.source.forEach((e3) => e3.close()), this.service.source.length = 0), (_b = this.tileStore) == null ? void 0 : _b.updateTiles({ added: [], removed: this.tileStore.tiles.map((e3) => e3.id) }), (_c = this.tileStore) == null ? void 0 : _c.destroy(), this.tileStore = null, this._pendingTileUpdates.length = 0;
  }
  async startup({ service: e3, config: t5, tileInfo: r6, tiles: s9 }) {
    var _a, _b, _c;
    if (this._paused = true, Array.isArray((_a = this.service) == null ? void 0 : _a.source) && (this.service.source.forEach((e4) => e4.close()), this.service.source.length = 0), this.service = e3, !this.tileStore || !E2(this.tileStore.tileScheme.spatialReference, r6.spatialReference)) {
      const e4 = new h(j3.fromJSON(r6));
      s9.added.length = s9.removed.length = 0, (_b = this.tileStore) == null ? void 0 : _b.updateTiles({ added: [], removed: this.tileStore.tiles.map((e5) => e5.id) }), (_c = this.tileStore) == null ? void 0 : _c.destroy(), this.tileStore = new d3(e4), this._pendingTileUpdates.length = 0;
    }
    for (await this._createProcessorAndController(t5), await this.update({ config: t5 }), this.controller.resume(), this.tileStore.clear(), this.tileStore.updateTiles(s9), this._paused = false; this._pendingTileUpdates.length; ) this.tileStore.updateTiles(this._pendingTileUpdates.pop());
  }
  async updateTiles(e3) {
    var _a;
    this._paused ? this._pendingTileUpdates.push(e3) : (_a = this.tileStore) == null ? void 0 : _a.updateTiles(e3);
  }
  async update({ config: e3 }) {
    const t5 = t4.empty();
    return await Promise.all([this.processor.update(t5, e3), this.controller.update(t5, e3)]), t5.toJSON();
  }
  async applyUpdate(e3) {
    return this.controller.applyUpdate(t4.create(e3));
  }
  async _createProcessorAndController(e3) {
    await Promise.all([this._handleControllerConfig(e3), this._handleProcessorConfig(e3)]), this.controller.processor = this.processor;
  }
  async _handleControllerConfig(e3) {
    return this._createController(this.service, e3);
  }
  async _handleProcessorConfig(e3) {
    return this._createProcessor(this.service, e3);
  }
  async _createController(e3, t5) {
    this.controller && this.controller.destroy();
    const { tileStore: r6, remoteClient: s9 } = this, o7 = new O2({ service: e3, tileStore: r6, remoteClient: s9 });
    return this.controller = o7, o7;
  }
  async _createProcessor(e3, t5) {
    const r6 = t5.schema.processors[0].type, s9 = (await o5(r6)).default, { remoteClient: o7, tileStore: i5 } = this, l7 = new s9({ service: e3, config: t5, tileStore: i5, remoteClient: o7 });
    return this.processor && this.processor.destroy(), this.processor = l7, l7;
  }
};
e2([y()], d8.prototype, "controller", void 0), e2([y()], d8.prototype, "processor", void 0), e2([y()], d8.prototype, "updating", null), e2([y()], d8.prototype, "viewState", void 0), d8 = e2([a("esri.views.2d.layers.features.Pipeline")], d8);
var u2 = d8;
export {
  u2 as default
};
//# sourceMappingURL=Pipeline-AHZLTUDQ.js.map
