import {
  i
} from "./chunk-OMPEYGMV.js";
import {
  g as g3,
  p as p4
} from "./chunk-IVZBPDHM.js";
import "./chunk-CIOZM2QJ.js";
import "./chunk-ZVJXF3ML.js";
import "./chunk-SKIEIN3S.js";
import {
  L as L2,
  _,
  b,
  r as r3,
  x as x2
} from "./chunk-OISOH7BD.js";
import {
  L as L3,
  M,
  R,
  h,
  j,
  k as k2
} from "./chunk-PSWIICDM.js";
import {
  a as a3,
  g as g2,
  k,
  v as v2,
  x
} from "./chunk-6ESVG4YL.js";
import "./chunk-UQUDWTCY.js";
import {
  c
} from "./chunk-IKOX2HGY.js";
import "./chunk-3KCCETWY.js";
import "./chunk-PWCXATLS.js";
import "./chunk-ZIKXCGU7.js";
import "./chunk-XSQFM27N.js";
import {
  e as e4
} from "./chunk-QYOAH6AO.js";
import {
  e as e3
} from "./chunk-A7PY25IH.js";
import "./chunk-57ER3SHX.js";
import {
  a as a2
} from "./chunk-ST2RRB55.js";
import "./chunk-SROTSYJS.js";
import {
  Zn,
  gn
} from "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import {
  a as a4
} from "./chunk-Q4VCSCSY.js";
import {
  p as p3
} from "./chunk-YEODPCXQ.js";
import "./chunk-FZ7BG3VX.js";
import {
  x as x3
} from "./chunk-N4YJNWPS.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import {
  g as g4
} from "./chunk-TD7BKFJX.js";
import "./chunk-LNGMNZ4I.js";
import "./chunk-24NZLSKM.js";
import "./chunk-BNFBWJYJ.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-23VNIYCK.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-OVIRLBJH.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import {
  m as m2
} from "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import {
  m
} from "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import {
  v
} from "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-4YKNTCHK.js";
import "./chunk-XVA5SA7P.js";
import {
  Ct,
  G
} from "./chunk-U4SVMKOQ.js";
import "./chunk-CGBA4LNQ.js";
import "./chunk-2RZLOKYO.js";
import "./chunk-VIXZ7ZAD.js";
import {
  p as p2,
  w
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  f2
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-HHMRK4FX.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import {
  O,
  S,
  e as e2,
  g,
  o,
  u,
  z
} from "./chunk-MQAXMQFG.js";
import {
  f as f3,
  n,
  r as r2
} from "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  L,
  f,
  p,
  y as y2
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/geometry/support/meshUtils/centerAt.js
var a5 = s.getLogger("esri.geometry.support.meshUtils.centerAt");
function c2(e5, r4, i2) {
  if (!e5.vertexAttributes || !e5.vertexAttributes.position) return;
  const n2 = (i2 == null ? void 0 : i2.origin) ?? e5.origin;
  if (r(e5.transform)) null != (i2 == null ? void 0 : i2.geographic) && i2.geographic !== e5.transform.geographic && a5.warn(`Specifying the 'geographic' parameter (${i2.geographic}) different from the Mesh transform setting (${e5.transform.geographic}) is not supported`), f4(e5.transform, r4, n2);
  else {
    r3(e5.spatialReference, i2) ? p5(e5, r4, n2) : g5(e5, r4, n2);
  }
}
function f4(e5, t2, r4) {
  const i2 = t2.x - r4.x, o2 = t2.y - r4.y, n2 = t2.hasZ && r4.hasZ ? t2.z - r4.z : 0, s3 = e5.origin;
  e5.origin = [s3[0] + i2, s3[1] + o2, s3[2] + n2];
}
function p5(e5, t2, r4) {
  const i2 = b(e5.vertexAttributes, r4, { geographic: true }), { position: o2, normal: a6, tangent: c4 } = x2(i2, t2, { geographic: true });
  e5.vertexAttributes.position = o2, e5.vertexAttributes.normal = a6, e5.vertexAttributes.tangent = c4, e5.vertexAttributesChanged();
}
function g5(e5, t2, r4) {
  const o2 = h2, n2 = l2;
  if (gn(t2, n2, e5.spatialReference)) {
    if (!gn(r4, o2, e5.spatialReference)) {
      const t3 = e5.origin;
      o2[0] = t3.x, o2[1] = t3.y, o2[2] = t3.z, a5.error(`Failed to project specified origin (wkid:${r4.spatialReference.wkid}) to mesh spatial reference (wkid:${e5.spatialReference.wkid}).`);
    }
    m3(e5.vertexAttributes.position, n2, o2), e5.vertexAttributesChanged();
  } else a5.error(`Failed to project centerAt location (wkid:${t2.spatialReference.wkid}) to mesh spatial reference (wkid:${e5.spatialReference.wkid})`);
}
function m3(e5, t2, r4) {
  if (e5) for (let i2 = 0; i2 < e5.length; i2 += 3) for (let o2 = 0; o2 < 3; o2++) e5[i2 + o2] += t2[o2] - r4[o2];
}
var l2 = n();
var h2 = n();

// node_modules/@arcgis/core/geometry/support/meshUtils/loadExternal.js
async function u2(e5, s3, o2) {
  const { loadGLTFMesh: n2 } = await y2(import("./loadGLTFMesh-OITPNWM7.js"), o2), a6 = await m4(s3, o2), i2 = n2(new w({ x: 0, y: 0, z: 0, spatialReference: e5.spatialReference }), a6.url, { resolveFile: f5(a6), useTransform: true, signal: r(o2) ? o2.signal : null });
  i2.then(() => a6.dispose(), () => a6.dispose());
  const { vertexAttributes: l5, components: u5 } = await i2;
  e5.vertexAttributes = l5, e5.components = u5;
}
function f5(e5) {
  const t2 = Ct(e5.url);
  return (s3) => {
    const r4 = G(s3, t2, t2), o2 = r4 ? r4.replace(/^ *\.\//, "") : null;
    return (o2 ? e5.files.get(o2) : null) ?? s3;
  };
}
async function m4(e5, t2) {
  return e5 instanceof Blob ? y3.fromBlob(e5) : "string" == typeof e5 ? new y3(e5) : Array.isArray(e5) ? p6(e5, t2) : w3(e5, t2);
}
async function p6(t2, r4) {
  const i2 = /* @__PURE__ */ new Map();
  let l5 = null;
  const c4 = await L(t2.map(async (e5) => ({ name: e5.name, source: await m4(e5 instanceof Blob ? e5 : e5.source, r4) }))), u5 = [];
  for (const e5 of c4) e5 && (p(r4) ? e5.source.dispose() : u5.push(e5));
  f(r4);
  for (const { name: e5, source: o2 } of u5) (t(l5) || /\.(gltf|glb)/i.test(e5)) && (l5 = o2.url), i2.set(e5, o2.url), o2.files && o2.files.forEach((e6, t3) => i2.set(t3, e6));
  if (t(l5)) throw new s2("mesh-load-external:missing-files", "Missing files to load external mesh source");
  return new y3(l5, () => u5.forEach(({ source: e5 }) => e5.dispose()), i2);
}
async function w3(e5, t2) {
  const { default: s3 } = await y2(import("./@arcgis_core_request__js.js"), t2), o2 = "string" == typeof e5.multipart[0] ? await Promise.all(e5.multipart.map(async (e6) => (await s3(e6, { responseType: "array-buffer" })).data)) : e5.multipart;
  return y3.fromBlob(new Blob(o2));
}
var y3 = class _y {
  constructor(e5, t2 = () => {
  }, s3 = /* @__PURE__ */ new Map()) {
    this.url = e5, this.dispose = t2, this.files = s3;
  }
  static fromBlob(e5) {
    const t2 = URL.createObjectURL(e5);
    return new _y(t2, () => URL.revokeObjectURL(t2));
  }
};

// node_modules/@arcgis/core/geometry/support/meshUtils/offset.js
function x4(r4, o2, i2) {
  if (r4.vertexAttributes && r4.vertexAttributes.position) if (r(r4.transform)) null != (i2 == null ? void 0 : i2.geographic) && i2.geographic !== r4.transform.geographic && s.getLogger("esri.geometry.support.meshUtils.offset").warn(`Specifying the 'geographic' parameter (${i2.geographic}) different from the Mesh transform setting (${r4.transform.geographic}) is not supported`), A(r4.transform, o2);
  else {
    r3(r4.spatialReference, i2) ? v3(r4, o2) : b2(r4, o2);
  }
}
function A(t2, e5) {
  const r4 = t2.origin;
  t2.origin = u(n(), r4, e5);
}
function v3(t2, o2) {
  const i2 = t2.spatialReference, s3 = t2.vertexAttributes.position, a6 = t2.vertexAttributes.normal, c4 = t2.vertexAttributes.tangent, x7 = new Float64Array(s3.length), A4 = r(a6) ? new Float32Array(a6.length) : null, v5 = r(c4) ? new Float32Array(c4.length) : null, b4 = t2.extent.center, F2 = d;
  Zn(i2, [b4.x, b4.y, b4.z], k3, c(i2)), a2(w4, k3), S(F2, o2, w4), M(s3, i2, x7), r(a6) && r(A4) && j(a6, s3, x7, i2, A4), r(c4) && r(v5) && k2(c4, s3, x7, i2, v5), y4(x7, F2), R(x7, s3, i2), r(a6) && r(A4) && h(A4, s3, x7, i2, a6), r(c4) && r(v5) && L3(v5, s3, x7, i2, c4), t2.vertexAttributesChanged();
}
function b2(t2, e5) {
  y4(t2.vertexAttributes.position, e5), t2.vertexAttributesChanged();
}
function y4(t2, e5) {
  if (t2) for (let r4 = 0; r4 < t2.length; r4 += 3) for (let o2 = 0; o2 < 3; o2++) t2[r4 + o2] += e5[o2];
}
var d = n();
var k3 = e4();
var w4 = e3();

// node_modules/@arcgis/core/geometry/support/meshUtils/primitives.js
function c3() {
  const { faceDescriptions: t2, faceVertexOffsets: e5, uvScales: n2 } = d2, r4 = 4 * t2.length, o2 = new Float64Array(3 * r4), s3 = new Float32Array(3 * r4), a6 = new Float32Array(2 * r4), i2 = new Uint32Array(2 * t2.length * 3);
  let c4 = 0, l5 = 0, f8 = 0, u5 = 0;
  for (let h4 = 0; h4 < t2.length; h4++) {
    const r5 = t2[h4], p9 = c4 / 3;
    for (const t3 of e5) i2[u5++] = p9 + t3;
    const m7 = r5.corners;
    for (let t3 = 0; t3 < 4; t3++) {
      const e6 = m7[t3];
      let i3 = 0;
      a6[f8++] = 0.25 * n2[t3][0] + r5.uvOrigin[0], a6[f8++] = r5.uvOrigin[1] - 0.25 * n2[t3][1];
      for (let t4 = 0; t4 < 3; t4++) 0 !== r5.axis[t4] ? (o2[c4++] = 0.5 * r5.axis[t4], s3[l5++] = r5.axis[t4]) : (o2[c4++] = 0.5 * e6[i3++], s3[l5++] = 0);
    }
  }
  return { position: o2, normal: s3, uv: a6, faces: i2 };
}
function l3(e5, n2) {
  const r4 = e5.components[0], o2 = r4.faces, a6 = M2[n2], i2 = 6 * a6, c4 = new Array(6), l5 = new Array(o2.length - 6);
  let f8 = 0, u5 = 0;
  for (let t2 = 0; t2 < o2.length; t2++) t2 >= i2 && t2 < i2 + 6 ? c4[f8++] = o2[t2] : l5[u5++] = o2[t2];
  if (r(e5.vertexAttributes.uv)) {
    const t2 = new Float32Array(e5.vertexAttributes.uv), n3 = 4 * a6 * 2, r5 = [0, 1, 1, 1, 1, 0, 0, 0];
    for (let e6 = 0; e6 < r5.length; e6++) t2[n3 + e6] = r5[e6];
    e5.vertexAttributes.uv = t2;
  }
  return e5.components = [new g3({ faces: c4, material: r4.material }), new g3({ faces: l5 })], e5;
}
function f6(t2 = 0) {
  const e5 = Math.round(8 * 2 ** t2), n2 = 2 * e5, r4 = (e5 - 1) * (n2 + 1) + 2 * n2, o2 = new Float64Array(3 * r4), s3 = new Float32Array(3 * r4), a6 = new Float32Array(2 * r4), i2 = new Uint32Array(3 * ((e5 - 1) * n2 * 2));
  let c4 = 0, l5 = 0, f8 = 0, u5 = 0;
  for (let h4 = 0; h4 <= e5; h4++) {
    const t3 = h4 / e5 * Math.PI + 0.5 * Math.PI, r5 = Math.cos(t3), p9 = Math.sin(t3);
    F[2] = p9;
    const m7 = 0 === h4 || h4 === e5, w7 = m7 ? n2 - 1 : n2;
    for (let v5 = 0; v5 <= w7; v5++) {
      const t4 = v5 / w7 * 2 * Math.PI;
      F[0] = -Math.sin(t4) * r5, F[1] = Math.cos(t4) * r5;
      for (let e6 = 0; e6 < 3; e6++) o2[c4] = 0.5 * F[e6], s3[c4] = F[e6], ++c4;
      a6[l5++] = (v5 + (m7 ? 0.5 : 0)) / n2, a6[l5++] = h4 / e5, 0 !== h4 && v5 !== n2 && (h4 !== e5 && (i2[f8++] = u5, i2[f8++] = u5 + 1, i2[f8++] = u5 - n2), 1 !== h4 && (i2[f8++] = u5, i2[f8++] = u5 - n2, i2[f8++] = u5 - n2 - 1)), u5++;
    }
  }
  return { position: o2, normal: s3, uv: a6, faces: i2 };
}
function u3(t2 = 0) {
  const e5 = 5, n2 = Math.round(16 * 2 ** t2), r4 = (e5 - 1) * (n2 + 1) + 2 * n2, o2 = new Float64Array(3 * r4), s3 = new Float32Array(3 * r4), a6 = new Float32Array(2 * r4), i2 = new Uint32Array(3 * (4 * n2));
  let c4 = 0, l5 = 0, f8 = 0, u5 = 0, h4 = 0;
  for (let p9 = 0; p9 <= e5; p9++) {
    const t3 = 0 === p9 || p9 === e5, r5 = p9 <= 1 || p9 >= e5 - 1, m7 = 2 === p9 || 4 === p9, w7 = t3 ? n2 - 1 : n2;
    for (let v5 = 0; v5 <= w7; v5++) {
      const g8 = v5 / w7 * 2 * Math.PI, A4 = t3 ? 0 : 0.5;
      F[0] = A4 * Math.sin(g8), F[1] = A4 * -Math.cos(g8), F[2] = p9 <= 2 ? 0.5 : -0.5;
      for (let t4 = 0; t4 < 3; t4++) o2[c4++] = F[t4], s3[l5++] = r5 ? 2 === t4 ? p9 <= 1 ? 1 : -1 : 0 : 2 === t4 ? 0 : F[t4] / A4;
      a6[f8++] = (v5 + (t3 ? 0.5 : 0)) / n2, a6[f8++] = p9 <= 1 ? 1 * p9 / 3 : p9 <= 3 ? 1 * (p9 - 2) / 3 + 1 / 3 : 1 * (p9 - 4) / 3 + 2 / 3, m7 || 0 === p9 || v5 === n2 || (p9 !== e5 && (i2[u5++] = h4, i2[u5++] = h4 + 1, i2[u5++] = h4 - n2), 1 !== p9 && (i2[u5++] = h4, i2[u5++] = h4 - n2, i2[u5++] = h4 - n2 - 1)), h4++;
    }
  }
  return { position: o2, normal: s3, uv: a6, faces: i2 };
}
function h3(t2, e5) {
  const n2 = "number" == typeof e5 ? e5 : null != e5 ? e5.width : 1, r4 = "number" == typeof e5 ? e5 : null != e5 ? e5.height : 1;
  switch (t2) {
    case "up":
    case "down":
      return { width: n2, depth: r4 };
    case "north":
    case "south":
      return { width: n2, height: r4 };
    case "east":
    case "west":
      return { depth: n2, height: r4 };
  }
}
function p7(t2) {
  const e5 = g6.facingAxisOrderSwap[t2], n2 = g6.position, r4 = g6.normal, o2 = new Float64Array(n2.length), s3 = new Float32Array(r4.length);
  let a6 = 0;
  for (let i2 = 0; i2 < 4; i2++) {
    const t3 = a6;
    for (let i3 = 0; i3 < 3; i3++) {
      const c4 = e5[i3], l5 = Math.abs(c4) - 1, f8 = c4 >= 0 ? 1 : -1;
      o2[a6] = n2[t3 + l5] * f8, s3[a6] = r4[t3 + l5] * f8, a6++;
    }
  }
  return { position: o2, normal: s3, uv: new Float32Array(g6.uv), faces: new Uint32Array(g6.faces), isPlane: true };
}
var m5 = 1;
var w5 = 2;
var v4 = 3;
var g6 = { position: [-0.5, -0.5, 0, 0.5, -0.5, 0, 0.5, 0.5, 0, -0.5, 0.5, 0], normal: [0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1], uv: [0, 1, 1, 1, 1, 0, 0, 0], faces: [0, 1, 2, 0, 2, 3], facingAxisOrderSwap: { east: [v4, m5, w5], west: [-v4, -m5, w5], north: [-m5, v4, w5], south: [m5, -v4, w5], up: [m5, w5, v4], down: [m5, -w5, -v4] } };
function A2(t2, e5, n2) {
  t2.isPlane || y5(t2), x5(t2, n2 == null ? void 0 : n2.size);
  const { vertexAttributes: r4, transform: o2 } = _(t2, e5, n2);
  return { vertexAttributes: new p4({ ...r4, uv: t2.uv }), transform: o2, components: [new g3({ faces: t2.faces, material: n2 && n2.material || null })], spatialReference: e5.spatialReference };
}
function y5(t2) {
  for (let e5 = 0; e5 < t2.position.length; e5 += 3) t2.position[e5 + 2] += 0.5;
}
function x5(t2, e5) {
  if (null == e5) return;
  const o2 = "number" == typeof e5 ? [e5, e5, e5] : [null != e5.width ? e5.width : 1, null != e5.depth ? e5.depth : 1, null != e5.height ? e5.height : 1];
  O2[0] = o2[0], O2[4] = o2[1], O2[8] = o2[2];
  for (let r4 = 0; r4 < t2.position.length; r4 += 3) {
    for (let e6 = 0; e6 < 3; e6++) F[e6] = t2.position[r4 + e6];
    S(F, F, O2);
    for (let e6 = 0; e6 < 3; e6++) t2.position[r4 + e6] = F[e6];
  }
  if (o2[0] !== o2[1] || o2[1] !== o2[2]) {
    O2[0] = 1 / o2[0], O2[4] = 1 / o2[1], O2[8] = 1 / o2[2];
    for (let e6 = 0; e6 < t2.normal.length; e6 += 3) {
      for (let n2 = 0; n2 < 3; n2++) F[n2] = t2.normal[e6 + n2];
      S(F, F, O2), z(F, F);
      for (let n2 = 0; n2 < 3; n2++) t2.normal[e6 + n2] = F[n2];
    }
  }
}
var d2 = { faceDescriptions: [{ axis: [0, -1, 0], uvOrigin: [0, 0.625], corners: [[-1, -1], [1, -1], [1, 1], [-1, 1]] }, { axis: [1, 0, 0], uvOrigin: [0.25, 0.625], corners: [[-1, -1], [1, -1], [1, 1], [-1, 1]] }, { axis: [0, 1, 0], uvOrigin: [0.5, 0.625], corners: [[1, -1], [-1, -1], [-1, 1], [1, 1]] }, { axis: [-1, 0, 0], uvOrigin: [0.75, 0.625], corners: [[1, -1], [-1, -1], [-1, 1], [1, 1]] }, { axis: [0, 0, 1], uvOrigin: [0, 0.375], corners: [[-1, -1], [1, -1], [1, 1], [-1, 1]] }, { axis: [0, 0, -1], uvOrigin: [0, 0.875], corners: [[-1, 1], [1, 1], [1, -1], [-1, -1]] }], uvScales: [[0, 0], [1, 0], [1, 1], [0, 1]], faceVertexOffsets: [0, 1, 2, 0, 2, 3] };
var M2 = { south: 0, east: 1, north: 2, west: 3, up: 4, down: 5 };
var F = n();
var O2 = e3();

// node_modules/@arcgis/core/geometry/support/meshUtils/rotate.js
var $ = s.getLogger("esri.geometry.support.meshUtils.rotate");
function U(t2, r4, o2) {
  if (!t2.vertexAttributes || !t2.vertexAttributes.position || 0 === r4[3]) return;
  const i2 = t2.spatialReference;
  if (r(t2.transform)) {
    null != (o2 == null ? void 0 : o2.geographic) && o2.geographic !== t2.transform.geographic && $.warn(`Specifying the 'geographic' parameter (${o2.geographic}) different from the Mesh transform setting (${t2.transform.geographic}) is not supported`);
    const e5 = (o2 == null ? void 0 : o2.origin) ?? t2.transform.getOriginPoint(i2);
    z2(t2.transform, r4, e5);
  } else {
    const e5 = (o2 == null ? void 0 : o2.origin) ?? t2.origin;
    r3(t2.spatialReference, o2) ? C(t2, r4, e5) : I(t2, r4, e5);
  }
}
function z2(t2, e5, r4) {
  const o2 = o(E, r4.x, r4.y, r4.z), i2 = e2(E, o2, t2.origin);
  t2.applyLocalInverse(i2, M3), t2.rotation = v2(t2.rotation, e5, a3()), t2.applyLocalInverse(i2, i2), e2(i2, i2, M3), t2.translation = u(n(), t2.translation, i2);
}
function C(t2, r4, i2) {
  const s3 = t2.spatialReference, n2 = c(s3), a6 = Z;
  gn(i2, a6, n2) || gn(t2.origin, a6, n2);
  const c4 = t2.vertexAttributes.position, f8 = t2.vertexAttributes.normal, m7 = t2.vertexAttributes.tangent, g8 = new Float64Array(c4.length), l5 = r(f8) ? new Float32Array(f8.length) : null, v5 = r(m7) ? new Float32Array(m7.length) : null;
  Zn(n2, a6, P, n2), a2(S2, P);
  const x7 = O3;
  S(g2(O3), g2(r4), S2), x7[3] = r4[3], M(c4, s3, g8), r(f8) && r(l5) && j(f8, c4, g8, s3, l5), r(m7) && r(v5) && k2(m7, c4, g8, s3, v5), D(g8, x7, 3, a6), R(g8, c4, s3), r(f8) && r(l5) && (D(l5, x7, 3), h(l5, c4, g8, s3, f8)), r(m7) && r(v5) && (D(v5, x7, 4), L3(v5, c4, g8, s3, m7)), t2.vertexAttributesChanged();
}
function I(t2, e5, r4) {
  const o2 = Z;
  if (!gn(r4, o2, t2.spatialReference)) {
    const e6 = t2.origin;
    o2[0] = e6.x, o2[1] = e6.y, o2[2] = e6.z, $.error(`Failed to project specified origin (wkid:${r4.spatialReference.wkid}) to mesh spatial reference (wkid:${t2.spatialReference.wkid}).`);
  }
  D(t2.vertexAttributes.position, e5, 3, o2), D(t2.vertexAttributes.normal, e5, 3), D(t2.vertexAttributes.tangent, e5, 4), t2.vertexAttributesChanged();
}
function D(t2, e5, o2, i2 = f3) {
  if (!t(t2)) {
    p3(P, x(e5), g2(e5));
    for (let e6 = 0; e6 < t2.length; e6 += o2) {
      for (let r4 = 0; r4 < 3; r4++) E[r4] = t2[e6 + r4] - i2[r4];
      O(E, E, P);
      for (let r4 = 0; r4 < 3; r4++) t2[e6 + r4] = E[r4] + i2[r4];
    }
  }
}
var E = n();
var M3 = n();
var O3 = a3();
var P = e4();
var S2 = e3();
var Z = n();

// node_modules/@arcgis/core/geometry/support/meshUtils/scale.js
var d3 = s.getLogger("esri.geometry.support.meshUtils.scale");
function j2(e5, r4, o2) {
  if (!e5.vertexAttributes || !e5.vertexAttributes.position) return;
  const i2 = e5.spatialReference;
  if (r(e5.transform)) {
    null != (o2 == null ? void 0 : o2.geographic) && o2.geographic !== e5.transform.geographic && d3.warn(`Specifying the 'geographic' parameter (${o2.geographic}) different from the Mesh transform setting (${e5.transform.geographic}) is not supported`);
    const t2 = (o2 == null ? void 0 : o2.origin) ?? e5.transform.getOriginPoint(i2);
    x6(e5.transform, r4, t2);
  } else {
    const t2 = r3(e5.spatialReference, o2), i3 = o2 && o2.origin || e5.origin;
    t2 ? A3(e5, r4, i3) : b3(e5, r4, i3);
  }
}
function x6(e5, t2, a6) {
  const c4 = o(w6, a6.x, a6.y, a6.z), p9 = e2(w6, c4, e5.origin);
  e5.applyLocalInverse(p9, R2);
  const l5 = g(n(), e5.scale, t2);
  e5.scale = l5, e5.applyLocalInverse(p9, p9), e2(p9, p9, R2), e5.translation = u(n(), e5.translation, p9);
}
function A3(e5, r4, o2) {
  const i2 = e5.spatialReference, n2 = c(i2), s3 = k4;
  gn(o2, s3, n2) || gn(e5.origin, s3, n2);
  const a6 = e5.vertexAttributes.position, l5 = e5.vertexAttributes.normal, d4 = e5.vertexAttributes.tangent, j3 = new Float64Array(a6.length), x7 = r(l5) ? new Float32Array(l5.length) : null, A4 = r(d4) ? new Float32Array(d4.length) : null;
  M(a6, i2, j3), r(l5) && r(x7) && j(l5, a6, j3, i2, x7), r(d4) && r(A4) && k2(d4, a6, j3, i2, A4), y6(j3, r4, s3), R(j3, a6, i2), r(l5) && r(x7) && h(x7, a6, j3, i2, l5), r(d4) && r(A4) && L3(A4, a6, j3, i2, d4), e5.vertexAttributesChanged();
}
function b3(e5, t2, r4) {
  const o2 = k4;
  if (!gn(r4, o2, e5.spatialReference)) {
    const t3 = e5.origin;
    o2[0] = t3.x, o2[1] = t3.y, o2[2] = t3.z, d3.error(`Failed to project specified origin (wkid:${r4.spatialReference.wkid}) to mesh spatial reference (wkid:${e5.spatialReference.wkid}).`);
  }
  y6(e5.vertexAttributes.position, t2, o2), e5.vertexAttributesChanged();
}
function y6(e5, t2, r4 = f3) {
  if (e5) for (let o2 = 0; o2 < e5.length; o2 += 3) {
    for (let t3 = 0; t3 < 3; t3++) w6[t3] = e5[o2 + t3] - r4[t3];
    g(w6, w6, t2);
    for (let t3 = 0; t3 < 3; t3++) e5[o2 + t3] = w6[t3] + r4[t3];
  }
}
var w6 = n();
var R2 = n();
var k4 = n();

// node_modules/@arcgis/core/geometry/Mesh.js
var G2;
var I2 = "esri.geometry.Mesh";
var B = G2 = class extends a4(m2.LoadableMixin(m(p2))) {
  constructor(e5) {
    super(e5), this.components = null, this.transform = null, this.external = null, this.hasZ = true, this.hasM = false, this.vertexAttributes = new p4(), this.type = "mesh";
  }
  initialize() {
    (t(this.external) || this.vertexAttributes.position.length) && (this.loadStatus = "loaded"), this.when(() => {
      this.handles.add(l(() => {
        var _a;
        return { vertexAttributes: this.vertexAttributes, components: (_a = this.components) == null ? void 0 : _a.map((e5) => e5.clone()) };
      }, () => this._set("external", null), { once: true, sync: true }));
    });
  }
  get hasExtent() {
    return !this.loaded && r(this.external) && r(this.external.extent) || this.loaded && this.vertexAttributes.position.length > 0 && (!this.components || this.components.length > 0);
  }
  get _boundingInfo() {
    const e5 = this.vertexAttributes.position, t2 = this.spatialReference;
    if (0 === e5.length || this.components && 0 === this.components.length) return { extent: new w2({ xmin: 0, ymin: 0, zmin: 0, xmax: 0, ymax: 0, zmax: 0, spatialReference: t2 }), center: new w({ x: 0, y: 0, z: 0, spatialReference: t2 }) };
    const r4 = r(this.transform) ? this.transform.project(e5, t2) : e5;
    let o2 = 1 / 0, n2 = 1 / 0, s3 = 1 / 0, a6 = -1 / 0, l5 = -1 / 0, c4 = -1 / 0, p9 = 0, m7 = 0, h4 = 0;
    const f8 = r4.length, d4 = 1 / (f8 / 3);
    let x7 = 0;
    for (; x7 < f8; ) {
      const e6 = r4[x7++], t3 = r4[x7++], i2 = r4[x7++];
      o2 = Math.min(o2, e6), n2 = Math.min(n2, t3), s3 = Math.min(s3, i2), a6 = Math.max(a6, e6), l5 = Math.max(l5, t3), c4 = Math.max(c4, i2), p9 += d4 * e6, m7 += d4 * t3, h4 += d4 * i2;
    }
    return { extent: new w2({ xmin: o2, ymin: n2, zmin: s3, xmax: a6, ymax: l5, zmax: c4, spatialReference: t2 }), center: new w({ x: p9, y: m7, z: h4, spatialReference: t2 }) };
  }
  get anchor() {
    if (r(this.transform)) return this.transform.getOriginPoint(this.spatialReference);
    const e5 = this._boundingInfo;
    return new w({ x: e5.center.x, y: e5.center.y, z: e5.extent.zmin, spatialReference: this.spatialReference });
  }
  get origin() {
    return r(this.transform) ? this.transform.getOriginPoint(this.spatialReference) : this._boundingInfo.center;
  }
  get extent() {
    return !this.loaded && r(this.external) && r(this.external.extent) ? this.external.extent.clone() : this._boundingInfo.extent;
  }
  addComponent(e5) {
    this.loaded ? (this.components || (this.components = []), this.components.push(g3.from(e5)), this.notifyChange("components")) : s.getLogger(this.declaredClass).error("addComponent()", "Mesh must be loaded before applying operations");
  }
  removeComponent(e5) {
    if (this.loaded) {
      if (this.components) {
        const t2 = this.components.indexOf(e5);
        if (-1 !== t2) return this.components.splice(t2, 1), void this.notifyChange("components");
      }
      s.getLogger(this.declaredClass).error("removeComponent()", "Provided component is not part of the list of components");
    } else s.getLogger(this.declaredClass).error("removeComponent()", "Mesh must be loaded before applying operations");
  }
  rotate(e5, t2, r4, o2) {
    return k(k5.x, e5, D2), k(k5.y, t2, W), k(k5.z, r4, Z2), v2(D2, W, D2), v2(D2, Z2, D2), U(this, D2, o2), this;
  }
  offset(e5, t2, r4, o2) {
    return this.loaded ? (H[0] = e5, H[1] = t2, H[2] = r4, x4(this, H, o2), this) : (s.getLogger(this.declaredClass).error("offset()", "Mesh must be loaded before applying operations"), this);
  }
  scale(e5, t2) {
    return this.loaded ? (j2(this, e5, t2), this) : (s.getLogger(this.declaredClass).error("scale()", "Mesh must be loaded before applying operations"), this);
  }
  centerAt(e5, t2) {
    return this.loaded ? (c2(this, e5, t2), this) : (s.getLogger(this.declaredClass).error("centerAt()", "Mesh must be loaded before applying operations"), this);
  }
  load(e5) {
    return r(this.external) && this.addResolvingPromise(u2(this, this.external.source, e5)), Promise.resolve(this);
  }
  updateExternalSource(e5) {
    this._set("external", e5);
  }
  clone() {
    let e5 = null;
    if (this.components) {
      const t3 = /* @__PURE__ */ new Map(), r4 = /* @__PURE__ */ new Map();
      e5 = this.components.map((e6) => e6.cloneWithDeduplication(t3, r4));
    }
    const t2 = { components: e5, spatialReference: this.spatialReference, vertexAttributes: this.vertexAttributes.clone(), transform: r(this.transform) ? this.transform.clone() : null, external: r(this.external) ? { source: this.external.source, extent: r(this.external.extent) ? this.external.extent.clone() : null } : null };
    return new G2(t2);
  }
  vertexAttributesChanged() {
    this.notifyChange("vertexAttributes");
  }
  async toBinaryGLTF(e5) {
    const t2 = import("./gltfexport-KCYF7QYZ.js"), r4 = this.load(), o2 = await Promise.all([t2, r4]), { toBinaryGLTF: n2 } = o2[0];
    return n2(this, e5);
  }
  static createBox(e5, t2) {
    if (!(e5 instanceof w)) return s.getLogger(I2).error(".createBox()", "expected location to be a Point instance"), null;
    const r4 = new G2(A2(c3(), e5, t2));
    return t2 && t2.imageFace && "all" !== t2.imageFace ? l3(r4, t2.imageFace) : r4;
  }
  static createSphere(e5, t2) {
    return e5 instanceof w ? new G2(A2(f6(t2 && t2.densificationFactor || 0), e5, t2)) : (s.getLogger(I2).error(".createSphere()", "expected location to be a Point instance"), null);
  }
  static createCylinder(e5, t2) {
    return e5 instanceof w ? new G2(A2(u3(t2 && t2.densificationFactor || 0), e5, t2)) : (s.getLogger(I2).error(".createCylinder()", "expected location to be a Point instance"), null);
  }
  static createPlane(e5, t2) {
    if (!(e5 instanceof w)) return s.getLogger(I2).error(".createPlane()", "expected location to be a Point instance"), null;
    const r4 = (t2 == null ? void 0 : t2.facing) ?? "up", o2 = h3(r4, t2 == null ? void 0 : t2.size);
    return new G2(A2(p7(r4), e5, { ...t2, size: o2 }));
  }
  static createFromPolygon(e5, t2) {
    if (!(e5 instanceof v)) return s.getLogger(I2).error(".createFromPolygon()", "expected polygon to be a Polygon instance"), null;
    const r4 = i(e5);
    return new G2({ vertexAttributes: new p4({ position: r4.position }), components: [new g3({ faces: r4.faces, shading: "flat", material: (t2 == null ? void 0 : t2.material) ?? null })], spatialReference: e5.spatialReference });
  }
  static async createFromGLTF(e5, r4, o2) {
    if (!(e5 instanceof w)) throw s.getLogger(I2).error(".createfromGLTF()", "expected location to be a Point instance"), new s2("invalid-input", "Expected location to be a Point instance");
    const { loadGLTFMesh: s3 } = await y2(import("./loadGLTFMesh-OITPNWM7.js"), o2);
    return new G2(await s3(e5, r4, o2));
  }
  static createWithExternalSource(e5, t2, r4) {
    var _a;
    const o2 = (r4 == null ? void 0 : r4.extent) ?? null, n2 = ((_a = r4 == null ? void 0 : r4.transform) == null ? void 0 : _a.clone()) ?? new L2();
    n2.origin = [e5.x, e5.y, e5.z ?? 0];
    const s3 = e5.spatialReference;
    return new G2({ external: { source: t2, extent: o2 }, transform: n2, spatialReference: s3 });
  }
  static createIncomplete(e5, r4) {
    var _a;
    const o2 = ((_a = r4 == null ? void 0 : r4.transform) == null ? void 0 : _a.clone()) ?? new L2();
    o2.origin = [e5.x, e5.y, e5.z ?? 0];
    const n2 = e5.spatialReference, s3 = new G2({ transform: o2, spatialReference: n2 });
    return s3.addResolvingPromise(Promise.reject(new s2("mesh-incomplete", "Mesh resources are not complete"))), s3;
  }
};
e([y({ type: [g3], json: { write: true } })], B.prototype, "components", void 0), e([y({ type: L2, json: { write: true } })], B.prototype, "transform", void 0), e([y({ constructOnly: true })], B.prototype, "external", void 0), e([y({ readOnly: true })], B.prototype, "hasExtent", null), e([y({ readOnly: true })], B.prototype, "_boundingInfo", null), e([y({ readOnly: true })], B.prototype, "anchor", null), e([y({ readOnly: true })], B.prototype, "origin", null), e([y({ readOnly: true, json: { read: false } })], B.prototype, "extent", null), e([y({ readOnly: true, json: { read: false, write: true, default: true } })], B.prototype, "hasZ", void 0), e([y({ readOnly: true, json: { read: false, write: true, default: false } })], B.prototype, "hasM", void 0), e([y({ type: p4, nonNullable: true, json: { write: true } })], B.prototype, "vertexAttributes", void 0), B = G2 = e([a(I2)], B);
var k5 = { x: r2(1, 0, 0), y: r2(0, 1, 0), z: r2(0, 0, 1) };
var D2 = a3();
var W = a3();
var Z2 = a3();
var H = n();
var N = B;

// node_modules/@arcgis/core/rest/support/meshFeatureSet.js
function u4(r4, n2, o2) {
  const a6 = o2.features;
  o2.features = [], delete o2.geometryType;
  const i2 = x3.fromJSON(o2);
  if (i2.geometryType = "mesh", !o2.assetMaps) return i2;
  const u5 = E2(n2, o2.assetMaps), m7 = i2.spatialReference ?? f2.WGS84, p9 = o2.globalIdFieldName, { outFields: g8 } = r4, D4 = r(g8) && g8.length > 0 ? l4(g8.includes("*") ? null : new Set(g8)) : () => ({});
  for (const s3 of a6) {
    const r5 = f7(s3, p9, m7, n2, u5);
    r(r5) && i2.features.push(new g4({ geometry: r5, attributes: D4(s3) }));
  }
  return i2;
}
function l4(t2) {
  return ({ attributes: e5 }) => {
    if (!e5) return {};
    if (!t2) return e5;
    for (const r4 in e5) t2.has(r4) || delete e5[r4];
    return e5;
  };
}
function f7(t2, e5, o2, s3, a6) {
  const i2 = t2.attributes[e5], c4 = a6.get(i2);
  if (null == c4 || c4.status === g7.FAILED || null == c4.url) return null;
  const u5 = m6(t2, o2, s3), l5 = w2.fromJSON(t2.geometry);
  l5.spatialReference = o2;
  const f8 = p8(t2.attributes, s3, c4.projectVertices);
  return c4.status === g7.PENDING ? N.createIncomplete(u5, { extent: l5, transform: f8 }) : N.createWithExternalSource(u5, [{ name: c4.name, source: c4.url }], { extent: l5, transform: f8 });
}
function m6({ attributes: t2 }, e5, { transformFieldRoles: r4 }) {
  return new w({ x: t2[r4.originX], y: t2[r4.originY], z: t2[r4.originZ], spatialReference: e5 });
}
function p8(t2, { transformFieldRoles: e5 }, r4) {
  return new L2({ translation: [t2[e5.translationX], -t2[e5.translationZ], t2[e5.translationY]], rotation: k([t2[e5.rotationX], t2[e5.rotationZ], t2[e5.rotationY]], t2[e5.rotationDeg]), scale: [t2[e5.scaleX], t2[e5.scaleY], t2[e5.scaleZ]], geographic: r4 });
}
var g7;
function E2(t2, e5) {
  const r4 = /* @__PURE__ */ new Map();
  for (const n2 of e5) {
    const t3 = n2.parentGlobalId;
    if (null == t3) continue;
    const e6 = n2.assetName, o2 = n2.assetURL, s3 = n2.conversionStatus;
    let a6 = r4.get(t3);
    if (null == a6) switch (a6 = { name: e6, status: g7.FAILED, url: o2, projectVertices: D3(n2.flags).projectVertices }, r4.set(t3, a6), s3) {
      case "COMPLETED":
      case "SUBMITTED":
        a6.status = g7.COMPLETED;
        break;
      case "INPROGRESS":
        a6.status = g7.PENDING;
        break;
      default:
        a6.status = g7.FAILED;
    }
    else console.warn(`Multiple asset parts not expected. Ignoring additional parts. conflicting assetname: ${n2.assetName}`);
  }
  return r4;
}
function D3(t2) {
  return { projectVertices: t2.includes("PROJECT_VERTICES") };
}
!function(t2) {
  t2[t2.FAILED = 0] = "FAILED", t2[t2.PENDING = 1] = "PENDING", t2[t2.COMPLETED = 2] = "COMPLETED";
}(g7 || (g7 = {}));
export {
  u4 as meshFeatureSetFromJSON
};
//# sourceMappingURL=meshFeatureSet-YV5GU4LG.js.map
