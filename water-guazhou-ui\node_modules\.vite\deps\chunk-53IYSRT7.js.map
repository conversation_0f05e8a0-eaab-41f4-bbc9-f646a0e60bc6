{"version": 3, "sources": ["../../echarts/lib/data/helper/linkSeriesData.js", "../../echarts/lib/data/Graph.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n/**\n * Link lists and struct (graph or tree)\n */\nimport { curry, each, assert, extend, map, keys } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nfunction linkSeriesData(opt) {\n  var mainData = opt.mainData;\n  var datas = opt.datas;\n  if (!datas) {\n    datas = {\n      main: mainData\n    };\n    opt.datasAttr = {\n      main: 'data'\n    };\n  }\n  opt.datas = opt.mainData = null;\n  linkAll(mainData, datas, opt);\n  // Porxy data original methods.\n  each(datas, function (data) {\n    each(mainData.TRANSFERABLE_METHODS, function (methodName) {\n      data.wrapMethod(methodName, curry(transferInjection, opt));\n    });\n  });\n  // Beyond transfer, additional features should be added to `cloneShallow`.\n  mainData.wrapMethod('cloneShallow', curry(cloneShallowInjection, opt));\n  // Only mainData trigger change, because struct.update may trigger\n  // another changable methods, which may bring about dead lock.\n  each(mainData.CHANGABLE_METHODS, function (methodName) {\n    mainData.wrapMethod(methodName, curry(changeInjection, opt));\n  });\n  // Make sure datas contains mainData.\n  assert(datas[mainData.dataType] === mainData);\n}\nfunction transferInjection(opt, res) {\n  if (isMainData(this)) {\n    // Transfer datas to new main data.\n    var datas = extend({}, inner(this).datas);\n    datas[this.dataType] = res;\n    linkAll(res, datas, opt);\n  } else {\n    // Modify the reference in main data to point newData.\n    linkSingle(res, this.dataType, inner(this).mainData, opt);\n  }\n  return res;\n}\nfunction changeInjection(opt, res) {\n  opt.struct && opt.struct.update();\n  return res;\n}\nfunction cloneShallowInjection(opt, res) {\n  // cloneShallow, which brings about some fragilities, may be inappropriate\n  // to be exposed as an API. So for implementation simplicity we can make\n  // the restriction that cloneShallow of not-mainData should not be invoked\n  // outside, but only be invoked here.\n  each(inner(res).datas, function (data, dataType) {\n    data !== res && linkSingle(data.cloneShallow(), dataType, res, opt);\n  });\n  return res;\n}\n/**\n * Supplement method to List.\n *\n * @public\n * @param [dataType] If not specified, return mainData.\n */\nfunction getLinkedData(dataType) {\n  var mainData = inner(this).mainData;\n  return dataType == null || mainData == null ? mainData : inner(mainData).datas[dataType];\n}\n/**\n * Get list of all linked data\n */\nfunction getLinkedDataAll() {\n  var mainData = inner(this).mainData;\n  return mainData == null ? [{\n    data: mainData\n  }] : map(keys(inner(mainData).datas), function (type) {\n    return {\n      type: type,\n      data: inner(mainData).datas[type]\n    };\n  });\n}\nfunction isMainData(data) {\n  return inner(data).mainData === data;\n}\nfunction linkAll(mainData, datas, opt) {\n  inner(mainData).datas = {};\n  each(datas, function (data, dataType) {\n    linkSingle(data, dataType, mainData, opt);\n  });\n}\nfunction linkSingle(data, dataType, mainData, opt) {\n  inner(mainData).datas[dataType] = data;\n  inner(data).mainData = mainData;\n  data.dataType = dataType;\n  if (opt.struct) {\n    data[opt.structAttr] = opt.struct;\n    opt.struct[opt.datasAttr[dataType]] = data;\n  }\n  // Supplement method.\n  data.getLinkedData = getLinkedData;\n  data.getLinkedDataAll = getLinkedDataAll;\n}\nexport default linkSeriesData;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\n// id may be function name of Object, add a prefix to avoid this problem.\nfunction generateNodeKey(id) {\n  return '_EC_' + id;\n}\nvar Graph = /** @class */function () {\n  function Graph(directed) {\n    this.type = 'graph';\n    this.nodes = [];\n    this.edges = [];\n    this._nodesMap = {};\n    /**\n     * @type {Object.<string, module:echarts/data/Graph.Edge>}\n     * @private\n     */\n    this._edgesMap = {};\n    this._directed = directed || false;\n  }\n  /**\n   * If is directed graph\n   */\n  Graph.prototype.isDirected = function () {\n    return this._directed;\n  };\n  ;\n  /**\n   * Add a new node\n   */\n  Graph.prototype.addNode = function (id, dataIndex) {\n    id = id == null ? '' + dataIndex : '' + id;\n    var nodesMap = this._nodesMap;\n    if (nodesMap[generateNodeKey(id)]) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.error('Graph nodes have duplicate name or id');\n      }\n      return;\n    }\n    var node = new GraphNode(id, dataIndex);\n    node.hostGraph = this;\n    this.nodes.push(node);\n    nodesMap[generateNodeKey(id)] = node;\n    return node;\n  };\n  ;\n  /**\n   * Get node by data index\n   */\n  Graph.prototype.getNodeByIndex = function (dataIndex) {\n    var rawIdx = this.data.getRawIndex(dataIndex);\n    return this.nodes[rawIdx];\n  };\n  ;\n  /**\n   * Get node by id\n   */\n  Graph.prototype.getNodeById = function (id) {\n    return this._nodesMap[generateNodeKey(id)];\n  };\n  ;\n  /**\n   * Add a new edge\n   */\n  Graph.prototype.addEdge = function (n1, n2, dataIndex) {\n    var nodesMap = this._nodesMap;\n    var edgesMap = this._edgesMap;\n    // PENDING\n    if (zrUtil.isNumber(n1)) {\n      n1 = this.nodes[n1];\n    }\n    if (zrUtil.isNumber(n2)) {\n      n2 = this.nodes[n2];\n    }\n    if (!(n1 instanceof GraphNode)) {\n      n1 = nodesMap[generateNodeKey(n1)];\n    }\n    if (!(n2 instanceof GraphNode)) {\n      n2 = nodesMap[generateNodeKey(n2)];\n    }\n    if (!n1 || !n2) {\n      return;\n    }\n    var key = n1.id + '-' + n2.id;\n    var edge = new GraphEdge(n1, n2, dataIndex);\n    edge.hostGraph = this;\n    if (this._directed) {\n      n1.outEdges.push(edge);\n      n2.inEdges.push(edge);\n    }\n    n1.edges.push(edge);\n    if (n1 !== n2) {\n      n2.edges.push(edge);\n    }\n    this.edges.push(edge);\n    edgesMap[key] = edge;\n    return edge;\n  };\n  ;\n  /**\n   * Get edge by data index\n   */\n  Graph.prototype.getEdgeByIndex = function (dataIndex) {\n    var rawIdx = this.edgeData.getRawIndex(dataIndex);\n    return this.edges[rawIdx];\n  };\n  ;\n  /**\n   * Get edge by two linked nodes\n   */\n  Graph.prototype.getEdge = function (n1, n2) {\n    if (n1 instanceof GraphNode) {\n      n1 = n1.id;\n    }\n    if (n2 instanceof GraphNode) {\n      n2 = n2.id;\n    }\n    var edgesMap = this._edgesMap;\n    if (this._directed) {\n      return edgesMap[n1 + '-' + n2];\n    } else {\n      return edgesMap[n1 + '-' + n2] || edgesMap[n2 + '-' + n1];\n    }\n  };\n  ;\n  /**\n   * Iterate all nodes\n   */\n  Graph.prototype.eachNode = function (cb, context) {\n    var nodes = this.nodes;\n    var len = nodes.length;\n    for (var i = 0; i < len; i++) {\n      if (nodes[i].dataIndex >= 0) {\n        cb.call(context, nodes[i], i);\n      }\n    }\n  };\n  ;\n  /**\n   * Iterate all edges\n   */\n  Graph.prototype.eachEdge = function (cb, context) {\n    var edges = this.edges;\n    var len = edges.length;\n    for (var i = 0; i < len; i++) {\n      if (edges[i].dataIndex >= 0 && edges[i].node1.dataIndex >= 0 && edges[i].node2.dataIndex >= 0) {\n        cb.call(context, edges[i], i);\n      }\n    }\n  };\n  ;\n  /**\n   * Breadth first traverse\n   * Return true to stop traversing\n   */\n  Graph.prototype.breadthFirstTraverse = function (cb, startNode, direction, context) {\n    if (!(startNode instanceof GraphNode)) {\n      startNode = this._nodesMap[generateNodeKey(startNode)];\n    }\n    if (!startNode) {\n      return;\n    }\n    var edgeType = direction === 'out' ? 'outEdges' : direction === 'in' ? 'inEdges' : 'edges';\n    for (var i = 0; i < this.nodes.length; i++) {\n      this.nodes[i].__visited = false;\n    }\n    if (cb.call(context, startNode, null)) {\n      return;\n    }\n    var queue = [startNode];\n    while (queue.length) {\n      var currentNode = queue.shift();\n      var edges = currentNode[edgeType];\n      for (var i = 0; i < edges.length; i++) {\n        var e = edges[i];\n        var otherNode = e.node1 === currentNode ? e.node2 : e.node1;\n        if (!otherNode.__visited) {\n          if (cb.call(context, otherNode, currentNode)) {\n            // Stop traversing\n            return;\n          }\n          queue.push(otherNode);\n          otherNode.__visited = true;\n        }\n      }\n    }\n  };\n  ;\n  // TODO\n  // depthFirstTraverse(\n  //     cb, startNode, direction, context\n  // ) {\n  // };\n  // Filter update\n  Graph.prototype.update = function () {\n    var data = this.data;\n    var edgeData = this.edgeData;\n    var nodes = this.nodes;\n    var edges = this.edges;\n    for (var i = 0, len = nodes.length; i < len; i++) {\n      nodes[i].dataIndex = -1;\n    }\n    for (var i = 0, len = data.count(); i < len; i++) {\n      nodes[data.getRawIndex(i)].dataIndex = i;\n    }\n    edgeData.filterSelf(function (idx) {\n      var edge = edges[edgeData.getRawIndex(idx)];\n      return edge.node1.dataIndex >= 0 && edge.node2.dataIndex >= 0;\n    });\n    // Update edge\n    for (var i = 0, len = edges.length; i < len; i++) {\n      edges[i].dataIndex = -1;\n    }\n    for (var i = 0, len = edgeData.count(); i < len; i++) {\n      edges[edgeData.getRawIndex(i)].dataIndex = i;\n    }\n  };\n  ;\n  /**\n   * @return {module:echarts/data/Graph}\n   */\n  Graph.prototype.clone = function () {\n    var graph = new Graph(this._directed);\n    var nodes = this.nodes;\n    var edges = this.edges;\n    for (var i = 0; i < nodes.length; i++) {\n      graph.addNode(nodes[i].id, nodes[i].dataIndex);\n    }\n    for (var i = 0; i < edges.length; i++) {\n      var e = edges[i];\n      graph.addEdge(e.node1.id, e.node2.id, e.dataIndex);\n    }\n    return graph;\n  };\n  ;\n  return Graph;\n}();\nvar GraphNode = /** @class */function () {\n  function GraphNode(id, dataIndex) {\n    this.inEdges = [];\n    this.outEdges = [];\n    this.edges = [];\n    this.dataIndex = -1;\n    this.id = id == null ? '' : id;\n    this.dataIndex = dataIndex == null ? -1 : dataIndex;\n  }\n  /**\n   * @return {number}\n   */\n  GraphNode.prototype.degree = function () {\n    return this.edges.length;\n  };\n  /**\n   * @return {number}\n   */\n  GraphNode.prototype.inDegree = function () {\n    return this.inEdges.length;\n  };\n  /**\n  * @return {number}\n  */\n  GraphNode.prototype.outDegree = function () {\n    return this.outEdges.length;\n  };\n  GraphNode.prototype.getModel = function (path) {\n    if (this.dataIndex < 0) {\n      return;\n    }\n    var graph = this.hostGraph;\n    var itemModel = graph.data.getItemModel(this.dataIndex);\n    return itemModel.getModel(path);\n  };\n  GraphNode.prototype.getAdjacentDataIndices = function () {\n    var dataIndices = {\n      edge: [],\n      node: []\n    };\n    for (var i = 0; i < this.edges.length; i++) {\n      var adjacentEdge = this.edges[i];\n      if (adjacentEdge.dataIndex < 0) {\n        continue;\n      }\n      dataIndices.edge.push(adjacentEdge.dataIndex);\n      dataIndices.node.push(adjacentEdge.node1.dataIndex, adjacentEdge.node2.dataIndex);\n    }\n    return dataIndices;\n  };\n  GraphNode.prototype.getTrajectoryDataIndices = function () {\n    var connectedEdgesMap = zrUtil.createHashMap();\n    var connectedNodesMap = zrUtil.createHashMap();\n    for (var i = 0; i < this.edges.length; i++) {\n      var adjacentEdge = this.edges[i];\n      if (adjacentEdge.dataIndex < 0) {\n        continue;\n      }\n      connectedEdgesMap.set(adjacentEdge.dataIndex, true);\n      var sourceNodesQueue = [adjacentEdge.node1];\n      var targetNodesQueue = [adjacentEdge.node2];\n      var nodeIteratorIndex = 0;\n      while (nodeIteratorIndex < sourceNodesQueue.length) {\n        var sourceNode = sourceNodesQueue[nodeIteratorIndex];\n        nodeIteratorIndex++;\n        connectedNodesMap.set(sourceNode.dataIndex, true);\n        for (var j = 0; j < sourceNode.inEdges.length; j++) {\n          connectedEdgesMap.set(sourceNode.inEdges[j].dataIndex, true);\n          sourceNodesQueue.push(sourceNode.inEdges[j].node1);\n        }\n      }\n      nodeIteratorIndex = 0;\n      while (nodeIteratorIndex < targetNodesQueue.length) {\n        var targetNode = targetNodesQueue[nodeIteratorIndex];\n        nodeIteratorIndex++;\n        connectedNodesMap.set(targetNode.dataIndex, true);\n        for (var j = 0; j < targetNode.outEdges.length; j++) {\n          connectedEdgesMap.set(targetNode.outEdges[j].dataIndex, true);\n          targetNodesQueue.push(targetNode.outEdges[j].node2);\n        }\n      }\n    }\n    return {\n      edge: connectedEdgesMap.keys(),\n      node: connectedNodesMap.keys()\n    };\n  };\n  return GraphNode;\n}();\nvar GraphEdge = /** @class */function () {\n  function GraphEdge(n1, n2, dataIndex) {\n    this.dataIndex = -1;\n    this.node1 = n1;\n    this.node2 = n2;\n    this.dataIndex = dataIndex == null ? -1 : dataIndex;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  GraphEdge.prototype.getModel = function (path) {\n    if (this.dataIndex < 0) {\n      return;\n    }\n    var graph = this.hostGraph;\n    var itemModel = graph.edgeData.getItemModel(this.dataIndex);\n    return itemModel.getModel(path);\n  };\n  GraphEdge.prototype.getAdjacentDataIndices = function () {\n    return {\n      edge: [this.dataIndex],\n      node: [this.node1.dataIndex, this.node2.dataIndex]\n    };\n  };\n  GraphEdge.prototype.getTrajectoryDataIndices = function () {\n    var connectedEdgesMap = zrUtil.createHashMap();\n    var connectedNodesMap = zrUtil.createHashMap();\n    connectedEdgesMap.set(this.dataIndex, true);\n    var sourceNodes = [this.node1];\n    var targetNodes = [this.node2];\n    var nodeIteratorIndex = 0;\n    while (nodeIteratorIndex < sourceNodes.length) {\n      var sourceNode = sourceNodes[nodeIteratorIndex];\n      nodeIteratorIndex++;\n      connectedNodesMap.set(sourceNode.dataIndex, true);\n      for (var j = 0; j < sourceNode.inEdges.length; j++) {\n        connectedEdgesMap.set(sourceNode.inEdges[j].dataIndex, true);\n        sourceNodes.push(sourceNode.inEdges[j].node1);\n      }\n    }\n    nodeIteratorIndex = 0;\n    while (nodeIteratorIndex < targetNodes.length) {\n      var targetNode = targetNodes[nodeIteratorIndex];\n      nodeIteratorIndex++;\n      connectedNodesMap.set(targetNode.dataIndex, true);\n      for (var j = 0; j < targetNode.outEdges.length; j++) {\n        connectedEdgesMap.set(targetNode.outEdges[j].dataIndex, true);\n        targetNodes.push(targetNode.outEdges[j].node2);\n      }\n    }\n    return {\n      edge: connectedEdgesMap.keys(),\n      node: connectedNodesMap.keys()\n    };\n  };\n  return GraphEdge;\n}();\nfunction createGraphDataProxyMixin(hostName, dataName) {\n  return {\n    /**\n     * @param Default 'value'. can be 'a', 'b', 'c', 'd', 'e'.\n     */\n    getValue: function (dimension) {\n      var data = this[hostName][dataName];\n      return data.getStore().get(data.getDimensionIndex(dimension || 'value'), this.dataIndex);\n    },\n    // TODO: TYPE stricter type.\n    setVisual: function (key, value) {\n      this.dataIndex >= 0 && this[hostName][dataName].setItemVisual(this.dataIndex, key, value);\n    },\n    getVisual: function (key) {\n      return this[hostName][dataName].getItemVisual(this.dataIndex, key);\n    },\n    setLayout: function (layout, merge) {\n      this.dataIndex >= 0 && this[hostName][dataName].setItemLayout(this.dataIndex, layout, merge);\n    },\n    getLayout: function () {\n      return this[hostName][dataName].getItemLayout(this.dataIndex);\n    },\n    getGraphicEl: function () {\n      return this[hostName][dataName].getItemGraphicEl(this.dataIndex);\n    },\n    getRawIndex: function () {\n      return this[hostName][dataName].getRawIndex(this.dataIndex);\n    }\n  };\n}\n;\n;\n;\nzrUtil.mixin(GraphNode, createGraphDataProxyMixin('hostGraph', 'data'));\nzrUtil.mixin(GraphEdge, createGraphDataProxyMixin('hostGraph', 'edgeData'));\nexport default Graph;\nexport { GraphNode, GraphEdge };"], "mappings": ";;;;;;;;;;;;;;;;AAgDA,IAAI,QAAQ,UAAU;AACtB,SAAS,eAAe,KAAK;AAC3B,MAAI,WAAW,IAAI;AACnB,MAAI,QAAQ,IAAI;AAChB,MAAI,CAAC,OAAO;AACV,YAAQ;AAAA,MACN,MAAM;AAAA,IACR;AACA,QAAI,YAAY;AAAA,MACd,MAAM;AAAA,IACR;AAAA,EACF;AACA,MAAI,QAAQ,IAAI,WAAW;AAC3B,UAAQ,UAAU,OAAO,GAAG;AAE5B,OAAK,OAAO,SAAU,MAAM;AAC1B,SAAK,SAAS,sBAAsB,SAAU,YAAY;AACxD,WAAK,WAAW,YAAY,MAAM,mBAAmB,GAAG,CAAC;AAAA,IAC3D,CAAC;AAAA,EACH,CAAC;AAED,WAAS,WAAW,gBAAgB,MAAM,uBAAuB,GAAG,CAAC;AAGrE,OAAK,SAAS,mBAAmB,SAAU,YAAY;AACrD,aAAS,WAAW,YAAY,MAAM,iBAAiB,GAAG,CAAC;AAAA,EAC7D,CAAC;AAED,SAAO,MAAM,SAAS,QAAQ,MAAM,QAAQ;AAC9C;AACA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,WAAW,IAAI,GAAG;AAEpB,QAAI,QAAQ,OAAO,CAAC,GAAG,MAAM,IAAI,EAAE,KAAK;AACxC,UAAM,KAAK,QAAQ,IAAI;AACvB,YAAQ,KAAK,OAAO,GAAG;AAAA,EACzB,OAAO;AAEL,eAAW,KAAK,KAAK,UAAU,MAAM,IAAI,EAAE,UAAU,GAAG;AAAA,EAC1D;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,KAAK,KAAK;AACjC,MAAI,UAAU,IAAI,OAAO,OAAO;AAChC,SAAO;AACT;AACA,SAAS,sBAAsB,KAAK,KAAK;AAKvC,OAAK,MAAM,GAAG,EAAE,OAAO,SAAU,MAAM,UAAU;AAC/C,aAAS,OAAO,WAAW,KAAK,aAAa,GAAG,UAAU,KAAK,GAAG;AAAA,EACpE,CAAC;AACD,SAAO;AACT;AAOA,SAAS,cAAc,UAAU;AAC/B,MAAI,WAAW,MAAM,IAAI,EAAE;AAC3B,SAAO,YAAY,QAAQ,YAAY,OAAO,WAAW,MAAM,QAAQ,EAAE,MAAM,QAAQ;AACzF;AAIA,SAAS,mBAAmB;AAC1B,MAAI,WAAW,MAAM,IAAI,EAAE;AAC3B,SAAO,YAAY,OAAO,CAAC;AAAA,IACzB,MAAM;AAAA,EACR,CAAC,IAAI,IAAI,KAAK,MAAM,QAAQ,EAAE,KAAK,GAAG,SAAU,MAAM;AACpD,WAAO;AAAA,MACL;AAAA,MACA,MAAM,MAAM,QAAQ,EAAE,MAAM,IAAI;AAAA,IAClC;AAAA,EACF,CAAC;AACH;AACA,SAAS,WAAW,MAAM;AACxB,SAAO,MAAM,IAAI,EAAE,aAAa;AAClC;AACA,SAAS,QAAQ,UAAU,OAAO,KAAK;AACrC,QAAM,QAAQ,EAAE,QAAQ,CAAC;AACzB,OAAK,OAAO,SAAU,MAAM,UAAU;AACpC,eAAW,MAAM,UAAU,UAAU,GAAG;AAAA,EAC1C,CAAC;AACH;AACA,SAAS,WAAW,MAAM,UAAU,UAAU,KAAK;AACjD,QAAM,QAAQ,EAAE,MAAM,QAAQ,IAAI;AAClC,QAAM,IAAI,EAAE,WAAW;AACvB,OAAK,WAAW;AAChB,MAAI,IAAI,QAAQ;AACd,SAAK,IAAI,UAAU,IAAI,IAAI;AAC3B,QAAI,OAAO,IAAI,UAAU,QAAQ,CAAC,IAAI;AAAA,EACxC;AAEA,OAAK,gBAAgB;AACrB,OAAK,mBAAmB;AAC1B;AACA,IAAO,yBAAQ;;;ACxGf,SAAS,gBAAgB,IAAI;AAC3B,SAAO,SAAS;AAClB;AACA,IAAI;AAAA;AAAA,EAAqB,WAAY;AACnC,aAASA,OAAM,UAAU;AACvB,WAAK,OAAO;AACZ,WAAK,QAAQ,CAAC;AACd,WAAK,QAAQ,CAAC;AACd,WAAK,YAAY,CAAC;AAKlB,WAAK,YAAY,CAAC;AAClB,WAAK,YAAY,YAAY;AAAA,IAC/B;AAIA,IAAAA,OAAM,UAAU,aAAa,WAAY;AACvC,aAAO,KAAK;AAAA,IACd;AACA;AAIA,IAAAA,OAAM,UAAU,UAAU,SAAU,IAAI,WAAW;AACjD,WAAK,MAAM,OAAO,KAAK,YAAY,KAAK;AACxC,UAAI,WAAW,KAAK;AACpB,UAAI,SAAS,gBAAgB,EAAE,CAAC,GAAG;AACjC,YAAI,MAAuC;AACzC,kBAAQ,MAAM,uCAAuC;AAAA,QACvD;AACA;AAAA,MACF;AACA,UAAI,OAAO,IAAI,UAAU,IAAI,SAAS;AACtC,WAAK,YAAY;AACjB,WAAK,MAAM,KAAK,IAAI;AACpB,eAAS,gBAAgB,EAAE,CAAC,IAAI;AAChC,aAAO;AAAA,IACT;AACA;AAIA,IAAAA,OAAM,UAAU,iBAAiB,SAAU,WAAW;AACpD,UAAI,SAAS,KAAK,KAAK,YAAY,SAAS;AAC5C,aAAO,KAAK,MAAM,MAAM;AAAA,IAC1B;AACA;AAIA,IAAAA,OAAM,UAAU,cAAc,SAAU,IAAI;AAC1C,aAAO,KAAK,UAAU,gBAAgB,EAAE,CAAC;AAAA,IAC3C;AACA;AAIA,IAAAA,OAAM,UAAU,UAAU,SAAU,IAAI,IAAI,WAAW;AACrD,UAAI,WAAW,KAAK;AACpB,UAAI,WAAW,KAAK;AAEpB,UAAW,SAAS,EAAE,GAAG;AACvB,aAAK,KAAK,MAAM,EAAE;AAAA,MACpB;AACA,UAAW,SAAS,EAAE,GAAG;AACvB,aAAK,KAAK,MAAM,EAAE;AAAA,MACpB;AACA,UAAI,EAAE,cAAc,YAAY;AAC9B,aAAK,SAAS,gBAAgB,EAAE,CAAC;AAAA,MACnC;AACA,UAAI,EAAE,cAAc,YAAY;AAC9B,aAAK,SAAS,gBAAgB,EAAE,CAAC;AAAA,MACnC;AACA,UAAI,CAAC,MAAM,CAAC,IAAI;AACd;AAAA,MACF;AACA,UAAI,MAAM,GAAG,KAAK,MAAM,GAAG;AAC3B,UAAI,OAAO,IAAI,UAAU,IAAI,IAAI,SAAS;AAC1C,WAAK,YAAY;AACjB,UAAI,KAAK,WAAW;AAClB,WAAG,SAAS,KAAK,IAAI;AACrB,WAAG,QAAQ,KAAK,IAAI;AAAA,MACtB;AACA,SAAG,MAAM,KAAK,IAAI;AAClB,UAAI,OAAO,IAAI;AACb,WAAG,MAAM,KAAK,IAAI;AAAA,MACpB;AACA,WAAK,MAAM,KAAK,IAAI;AACpB,eAAS,GAAG,IAAI;AAChB,aAAO;AAAA,IACT;AACA;AAIA,IAAAA,OAAM,UAAU,iBAAiB,SAAU,WAAW;AACpD,UAAI,SAAS,KAAK,SAAS,YAAY,SAAS;AAChD,aAAO,KAAK,MAAM,MAAM;AAAA,IAC1B;AACA;AAIA,IAAAA,OAAM,UAAU,UAAU,SAAU,IAAI,IAAI;AAC1C,UAAI,cAAc,WAAW;AAC3B,aAAK,GAAG;AAAA,MACV;AACA,UAAI,cAAc,WAAW;AAC3B,aAAK,GAAG;AAAA,MACV;AACA,UAAI,WAAW,KAAK;AACpB,UAAI,KAAK,WAAW;AAClB,eAAO,SAAS,KAAK,MAAM,EAAE;AAAA,MAC/B,OAAO;AACL,eAAO,SAAS,KAAK,MAAM,EAAE,KAAK,SAAS,KAAK,MAAM,EAAE;AAAA,MAC1D;AAAA,IACF;AACA;AAIA,IAAAA,OAAM,UAAU,WAAW,SAAU,IAAI,SAAS;AAChD,UAAI,QAAQ,KAAK;AACjB,UAAI,MAAM,MAAM;AAChB,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAI,MAAM,CAAC,EAAE,aAAa,GAAG;AAC3B,aAAG,KAAK,SAAS,MAAM,CAAC,GAAG,CAAC;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AACA;AAIA,IAAAA,OAAM,UAAU,WAAW,SAAU,IAAI,SAAS;AAChD,UAAI,QAAQ,KAAK;AACjB,UAAI,MAAM,MAAM;AAChB,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAI,MAAM,CAAC,EAAE,aAAa,KAAK,MAAM,CAAC,EAAE,MAAM,aAAa,KAAK,MAAM,CAAC,EAAE,MAAM,aAAa,GAAG;AAC7F,aAAG,KAAK,SAAS,MAAM,CAAC,GAAG,CAAC;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AACA;AAKA,IAAAA,OAAM,UAAU,uBAAuB,SAAU,IAAI,WAAW,WAAW,SAAS;AAClF,UAAI,EAAE,qBAAqB,YAAY;AACrC,oBAAY,KAAK,UAAU,gBAAgB,SAAS,CAAC;AAAA,MACvD;AACA,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AACA,UAAI,WAAW,cAAc,QAAQ,aAAa,cAAc,OAAO,YAAY;AACnF,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,aAAK,MAAM,CAAC,EAAE,YAAY;AAAA,MAC5B;AACA,UAAI,GAAG,KAAK,SAAS,WAAW,IAAI,GAAG;AACrC;AAAA,MACF;AACA,UAAI,QAAQ,CAAC,SAAS;AACtB,aAAO,MAAM,QAAQ;AACnB,YAAI,cAAc,MAAM,MAAM;AAC9B,YAAI,QAAQ,YAAY,QAAQ;AAChC,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAI,IAAI,MAAM,CAAC;AACf,cAAI,YAAY,EAAE,UAAU,cAAc,EAAE,QAAQ,EAAE;AACtD,cAAI,CAAC,UAAU,WAAW;AACxB,gBAAI,GAAG,KAAK,SAAS,WAAW,WAAW,GAAG;AAE5C;AAAA,YACF;AACA,kBAAM,KAAK,SAAS;AACpB,sBAAU,YAAY;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA;AAOA,IAAAA,OAAM,UAAU,SAAS,WAAY;AACnC,UAAI,OAAO,KAAK;AAChB,UAAI,WAAW,KAAK;AACpB,UAAI,QAAQ,KAAK;AACjB,UAAI,QAAQ,KAAK;AACjB,eAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,cAAM,CAAC,EAAE,YAAY;AAAA,MACvB;AACA,eAAS,IAAI,GAAG,MAAM,KAAK,MAAM,GAAG,IAAI,KAAK,KAAK;AAChD,cAAM,KAAK,YAAY,CAAC,CAAC,EAAE,YAAY;AAAA,MACzC;AACA,eAAS,WAAW,SAAU,KAAK;AACjC,YAAI,OAAO,MAAM,SAAS,YAAY,GAAG,CAAC;AAC1C,eAAO,KAAK,MAAM,aAAa,KAAK,KAAK,MAAM,aAAa;AAAA,MAC9D,CAAC;AAED,eAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,cAAM,CAAC,EAAE,YAAY;AAAA,MACvB;AACA,eAAS,IAAI,GAAG,MAAM,SAAS,MAAM,GAAG,IAAI,KAAK,KAAK;AACpD,cAAM,SAAS,YAAY,CAAC,CAAC,EAAE,YAAY;AAAA,MAC7C;AAAA,IACF;AACA;AAIA,IAAAA,OAAM,UAAU,QAAQ,WAAY;AAClC,UAAI,QAAQ,IAAIA,OAAM,KAAK,SAAS;AACpC,UAAI,QAAQ,KAAK;AACjB,UAAI,QAAQ,KAAK;AACjB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,QAAQ,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,SAAS;AAAA,MAC/C;AACA,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,IAAI,MAAM,CAAC;AACf,cAAM,QAAQ,EAAE,MAAM,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS;AAAA,MACnD;AACA,aAAO;AAAA,IACT;AACA;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACF,IAAI;AAAA;AAAA,EAAyB,WAAY;AACvC,aAASC,WAAU,IAAI,WAAW;AAChC,WAAK,UAAU,CAAC;AAChB,WAAK,WAAW,CAAC;AACjB,WAAK,QAAQ,CAAC;AACd,WAAK,YAAY;AACjB,WAAK,KAAK,MAAM,OAAO,KAAK;AAC5B,WAAK,YAAY,aAAa,OAAO,KAAK;AAAA,IAC5C;AAIA,IAAAA,WAAU,UAAU,SAAS,WAAY;AACvC,aAAO,KAAK,MAAM;AAAA,IACpB;AAIA,IAAAA,WAAU,UAAU,WAAW,WAAY;AACzC,aAAO,KAAK,QAAQ;AAAA,IACtB;AAIA,IAAAA,WAAU,UAAU,YAAY,WAAY;AAC1C,aAAO,KAAK,SAAS;AAAA,IACvB;AACA,IAAAA,WAAU,UAAU,WAAW,SAAU,MAAM;AAC7C,UAAI,KAAK,YAAY,GAAG;AACtB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK;AACjB,UAAI,YAAY,MAAM,KAAK,aAAa,KAAK,SAAS;AACtD,aAAO,UAAU,SAAS,IAAI;AAAA,IAChC;AACA,IAAAA,WAAU,UAAU,yBAAyB,WAAY;AACvD,UAAI,cAAc;AAAA,QAChB,MAAM,CAAC;AAAA,QACP,MAAM,CAAC;AAAA,MACT;AACA,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,YAAI,eAAe,KAAK,MAAM,CAAC;AAC/B,YAAI,aAAa,YAAY,GAAG;AAC9B;AAAA,QACF;AACA,oBAAY,KAAK,KAAK,aAAa,SAAS;AAC5C,oBAAY,KAAK,KAAK,aAAa,MAAM,WAAW,aAAa,MAAM,SAAS;AAAA,MAClF;AACA,aAAO;AAAA,IACT;AACA,IAAAA,WAAU,UAAU,2BAA2B,WAAY;AACzD,UAAI,oBAA2B,cAAc;AAC7C,UAAI,oBAA2B,cAAc;AAC7C,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,YAAI,eAAe,KAAK,MAAM,CAAC;AAC/B,YAAI,aAAa,YAAY,GAAG;AAC9B;AAAA,QACF;AACA,0BAAkB,IAAI,aAAa,WAAW,IAAI;AAClD,YAAI,mBAAmB,CAAC,aAAa,KAAK;AAC1C,YAAI,mBAAmB,CAAC,aAAa,KAAK;AAC1C,YAAI,oBAAoB;AACxB,eAAO,oBAAoB,iBAAiB,QAAQ;AAClD,cAAI,aAAa,iBAAiB,iBAAiB;AACnD;AACA,4BAAkB,IAAI,WAAW,WAAW,IAAI;AAChD,mBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,QAAQ,KAAK;AAClD,8BAAkB,IAAI,WAAW,QAAQ,CAAC,EAAE,WAAW,IAAI;AAC3D,6BAAiB,KAAK,WAAW,QAAQ,CAAC,EAAE,KAAK;AAAA,UACnD;AAAA,QACF;AACA,4BAAoB;AACpB,eAAO,oBAAoB,iBAAiB,QAAQ;AAClD,cAAI,aAAa,iBAAiB,iBAAiB;AACnD;AACA,4BAAkB,IAAI,WAAW,WAAW,IAAI;AAChD,mBAAS,IAAI,GAAG,IAAI,WAAW,SAAS,QAAQ,KAAK;AACnD,8BAAkB,IAAI,WAAW,SAAS,CAAC,EAAE,WAAW,IAAI;AAC5D,6BAAiB,KAAK,WAAW,SAAS,CAAC,EAAE,KAAK;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM,kBAAkB,KAAK;AAAA,QAC7B,MAAM,kBAAkB,KAAK;AAAA,MAC/B;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACF,IAAI;AAAA;AAAA,EAAyB,WAAY;AACvC,aAASC,WAAU,IAAI,IAAI,WAAW;AACpC,WAAK,YAAY;AACjB,WAAK,QAAQ;AACb,WAAK,QAAQ;AACb,WAAK,YAAY,aAAa,OAAO,KAAK;AAAA,IAC5C;AAEA,IAAAA,WAAU,UAAU,WAAW,SAAU,MAAM;AAC7C,UAAI,KAAK,YAAY,GAAG;AACtB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK;AACjB,UAAI,YAAY,MAAM,SAAS,aAAa,KAAK,SAAS;AAC1D,aAAO,UAAU,SAAS,IAAI;AAAA,IAChC;AACA,IAAAA,WAAU,UAAU,yBAAyB,WAAY;AACvD,aAAO;AAAA,QACL,MAAM,CAAC,KAAK,SAAS;AAAA,QACrB,MAAM,CAAC,KAAK,MAAM,WAAW,KAAK,MAAM,SAAS;AAAA,MACnD;AAAA,IACF;AACA,IAAAA,WAAU,UAAU,2BAA2B,WAAY;AACzD,UAAI,oBAA2B,cAAc;AAC7C,UAAI,oBAA2B,cAAc;AAC7C,wBAAkB,IAAI,KAAK,WAAW,IAAI;AAC1C,UAAI,cAAc,CAAC,KAAK,KAAK;AAC7B,UAAI,cAAc,CAAC,KAAK,KAAK;AAC7B,UAAI,oBAAoB;AACxB,aAAO,oBAAoB,YAAY,QAAQ;AAC7C,YAAI,aAAa,YAAY,iBAAiB;AAC9C;AACA,0BAAkB,IAAI,WAAW,WAAW,IAAI;AAChD,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,QAAQ,KAAK;AAClD,4BAAkB,IAAI,WAAW,QAAQ,CAAC,EAAE,WAAW,IAAI;AAC3D,sBAAY,KAAK,WAAW,QAAQ,CAAC,EAAE,KAAK;AAAA,QAC9C;AAAA,MACF;AACA,0BAAoB;AACpB,aAAO,oBAAoB,YAAY,QAAQ;AAC7C,YAAI,aAAa,YAAY,iBAAiB;AAC9C;AACA,0BAAkB,IAAI,WAAW,WAAW,IAAI;AAChD,iBAAS,IAAI,GAAG,IAAI,WAAW,SAAS,QAAQ,KAAK;AACnD,4BAAkB,IAAI,WAAW,SAAS,CAAC,EAAE,WAAW,IAAI;AAC5D,sBAAY,KAAK,WAAW,SAAS,CAAC,EAAE,KAAK;AAAA,QAC/C;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM,kBAAkB,KAAK;AAAA,QAC7B,MAAM,kBAAkB,KAAK;AAAA,MAC/B;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACF,SAAS,0BAA0B,UAAU,UAAU;AACrD,SAAO;AAAA;AAAA;AAAA;AAAA,IAIL,UAAU,SAAU,WAAW;AAC7B,UAAI,OAAO,KAAK,QAAQ,EAAE,QAAQ;AAClC,aAAO,KAAK,SAAS,EAAE,IAAI,KAAK,kBAAkB,aAAa,OAAO,GAAG,KAAK,SAAS;AAAA,IACzF;AAAA;AAAA,IAEA,WAAW,SAAU,KAAK,OAAO;AAC/B,WAAK,aAAa,KAAK,KAAK,QAAQ,EAAE,QAAQ,EAAE,cAAc,KAAK,WAAW,KAAK,KAAK;AAAA,IAC1F;AAAA,IACA,WAAW,SAAU,KAAK;AACxB,aAAO,KAAK,QAAQ,EAAE,QAAQ,EAAE,cAAc,KAAK,WAAW,GAAG;AAAA,IACnE;AAAA,IACA,WAAW,SAAU,QAAQ,OAAO;AAClC,WAAK,aAAa,KAAK,KAAK,QAAQ,EAAE,QAAQ,EAAE,cAAc,KAAK,WAAW,QAAQ,KAAK;AAAA,IAC7F;AAAA,IACA,WAAW,WAAY;AACrB,aAAO,KAAK,QAAQ,EAAE,QAAQ,EAAE,cAAc,KAAK,SAAS;AAAA,IAC9D;AAAA,IACA,cAAc,WAAY;AACxB,aAAO,KAAK,QAAQ,EAAE,QAAQ,EAAE,iBAAiB,KAAK,SAAS;AAAA,IACjE;AAAA,IACA,aAAa,WAAY;AACvB,aAAO,KAAK,QAAQ,EAAE,QAAQ,EAAE,YAAY,KAAK,SAAS;AAAA,IAC5D;AAAA,EACF;AACF;AAIO,MAAM,WAAW,0BAA0B,aAAa,MAAM,CAAC;AAC/D,MAAM,WAAW,0BAA0B,aAAa,UAAU,CAAC;AAC1E,IAAO,gBAAQ;", "names": ["Graph", "GraphNode", "GraphEdge"]}