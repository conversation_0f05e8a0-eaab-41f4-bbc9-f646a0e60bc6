import {
  r
} from "./chunk-7VG4CGLX.js";
import {
  o
} from "./chunk-EPJSBV4J.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/chunks/nb_NO.js
function o2(e, r2) {
  for (var o3 = 0; o3 < r2.length; o3++) {
    const a2 = r2[o3];
    if ("string" != typeof a2 && !Array.isArray(a2)) {
      for (const r3 in a2) if ("default" !== r3 && !(r3 in e)) {
        const o4 = Object.getOwnPropertyDescriptor(a2, r3);
        o4 && Object.defineProperty(e, r3, o4.get ? o4 : { enumerable: true, get: () => a2[r3] });
      }
    }
  }
  return Object.freeze(Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }));
}
var a;
var t;
var _ = {};
var n = { get exports() {
  return _;
}, set exports(e) {
  _ = e;
} };
a = n, void 0 !== (t = function(e, r2) {
  Object.defineProperty(r2, "__esModule", { value: true }), r2.default = { _decimalSeparator: ",", _thousandSeparator: " ", _percentPrefix: null, _percentSuffix: "%", _big_number_suffix_3: "k", _big_number_suffix_6: "M", _big_number_suffix_9: "G", _big_number_suffix_12: "T", _big_number_suffix_15: "P", _big_number_suffix_18: "E", _big_number_suffix_21: "Z", _big_number_suffix_24: "Y", _small_number_suffix_3: "m", _small_number_suffix_6: "μ", _small_number_suffix_9: "n", _small_number_suffix_12: "p", _small_number_suffix_15: "f", _small_number_suffix_18: "a", _small_number_suffix_21: "z", _small_number_suffix_24: "y", _byte_suffix_B: "B", _byte_suffix_KB: "KB", _byte_suffix_MB: "MB", _byte_suffix_GB: "GB", _byte_suffix_TB: "TB", _byte_suffix_PB: "PB", _date_millisecond: "mm:ss SSS", _date_second: "HH:mm:ss", _date_minute: "HH:mm", _date_hour: "HH:mm", _date_day: "MMM dd", _date_week: "ww", _date_month: "MMM", _date_year: "yyyy", _duration_millisecond: "SSS", _duration_millisecond_second: "ss.SSS", _duration_millisecond_minute: "mm:ss SSS", _duration_millisecond_hour: "hh:mm:ss SSS", _duration_millisecond_day: "d'd' mm:ss SSS", _duration_millisecond_week: "d'd' mm:ss SSS", _duration_millisecond_month: "M'm' dd'd' mm:ss SSS", _duration_millisecond_year: "y'y' MM'm' dd'd' mm:ss SSS", _duration_second: "ss", _duration_second_minute: "mm:ss", _duration_second_hour: "hh:mm:ss", _duration_second_day: "d'd' hh:mm:ss", _duration_second_week: "d'd' hh:mm:ss", _duration_second_month: "M'm' dd'd' hh:mm:ss", _duration_second_year: "y'y' MM'm' dd'd' hh:mm:ss", _duration_minute: "mm", _duration_minute_hour: "hh:mm", _duration_minute_day: "d'd' hh:mm", _duration_minute_week: "d'd' hh:mm", _duration_minute_month: "M'm' dd'd' hh:mm", _duration_minute_year: "y'y' MM'm' dd'd' hh:mm", _duration_hour: "hh'h'", _duration_hour_day: "d'd' hh'h'", _duration_hour_week: "d'd' hh'h'", _duration_hour_month: "M'm' dd'd' hh'h'", _duration_hour_year: "y'y' MM'm' dd'd' hh'h'", _duration_day: "d'd'", _duration_day_week: "d'd'", _duration_day_month: "M'm' dd'd'", _duration_day_year: "y'y' MM'm' dd'd'", _duration_week: "w'w'", _duration_week_month: "w'w'", _duration_week_year: "w'w'", _duration_month: "M'm'", _duration_month_year: "y'y' MM'm'", _duration_year: "y'y'", _era_ad: "e.Kr.", _era_bc: "f.Kr.", A: "a", P: "p", AM: "a.m.", PM: "p.m.", "A.M.": "a.m.", "P.M.": "p.m.", January: "januar", February: "februar", March: "mars", April: "april", May: "mai", June: "juni", July: "juli", August: "august", September: "september", October: "oktober", November: "november", December: "desember", Jan: "jan.", Feb: "feb.", Mar: "mar.", Apr: "apr.", "May(short)": "mai", Jun: "jun.", Jul: "jul.", Aug: "aug.", Sep: "sep.", Oct: "okt.", Nov: "nov.", Dec: "des.", Sunday: "søndag", Monday: "mandag", Tuesday: "tirsdag", Wednesday: "onsdag", Thursday: "torsdag", Friday: "fredag", Saturday: "lørdag", Sun: "søn.", Mon: "man.", Tue: "tir.", Wed: "ons.", Thu: "tor.", Fri: "fre.", Sat: "lør.", _dateOrd: function(e2) {
    var r3 = "th";
    if (e2 < 11 || e2 > 13) switch (e2 % 10) {
      case 1:
        r3 = "st";
        break;
      case 2:
        r3 = "nd";
        break;
      case 3:
        r3 = "rd";
    }
    return r3;
  }, "Zoom Out": "Zoom", Play: "Spill av", Stop: "Stopp", Legend: "Tegnforklaring", "Click, tap or press ENTER to toggle": "", Loading: "Laster inn", Home: "Hjem", Chart: "", "Serial chart": "", "X/Y chart": "", "Pie chart": "", "Gauge chart": "", "Radar chart": "", "Sankey diagram": "", "Flow diagram": "", "Chord diagram": "", "TreeMap chart": "", "Sliced chart": "", Series: "", "Candlestick Series": "", "OHLC Series": "", "Column Series": "", "Line Series": "", "Pie Slice Series": "", "Funnel Series": "", "Pyramid Series": "", "X/Y Series": "", Map: "", "Press ENTER to zoom in": "", "Press ENTER to zoom out": "", "Use arrow keys to zoom in and out": "", "Use plus and minus keys on your keyboard to zoom in and out": "", Export: "Skriv ut", Image: "Bilde", Data: "Data", Print: "Skriv ut", "Click, tap or press ENTER to open": "", "Click, tap or press ENTER to print.": "", "Click, tap or press ENTER to export as %1.": "", 'To save the image, right-click this link and choose "Save picture as..."': "", 'To save the image, right-click thumbnail on the left and choose "Save picture as..."': "", "(Press ESC to close this message)": "", "Image Export Complete": "", "Export operation took longer than expected. Something might have gone wrong.": "", "Saved from": "", PNG: "", JPG: "", GIF: "", SVG: "", PDF: "", JSON: "", CSV: "", XLSX: "", "Use TAB to select grip buttons or left and right arrows to change selection": "", "Use left and right arrows to move selection": "", "Use left and right arrows to move left selection": "", "Use left and right arrows to move right selection": "", "Use TAB select grip buttons or up and down arrows to change selection": "", "Use up and down arrows to move selection": "", "Use up and down arrows to move lower selection": "", "Use up and down arrows to move upper selection": "", "From %1 to %2": "Fra %1 til %2", "From %1": "Fra %1", "To %1": "Til %1", "No parser available for file: %1": "", "Error parsing file: %1": "", "Unable to load file: %1": "", "Invalid date": "" };
}(r, _)) && (a.exports = t);
var i = o2({ __proto__: null, default: o(_) }, [_]);
export {
  i as n
};
//# sourceMappingURL=nb_NO-2TQANBUV.js.map
