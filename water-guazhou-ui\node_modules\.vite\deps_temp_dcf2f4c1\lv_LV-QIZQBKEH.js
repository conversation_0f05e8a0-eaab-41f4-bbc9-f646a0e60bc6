import {
  r
} from "./chunk-7VG4CGLX.js";
import {
  o
} from "./chunk-EPJSBV4J.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/chunks/lv_LV.js
function t(e, r2) {
  for (var t2 = 0; t2 < r2.length; t2++) {
    const o3 = r2[t2];
    if ("string" != typeof o3 && !Array.isArray(o3)) {
      for (const r3 in o3) if ("default" !== r3 && !(r3 in e)) {
        const t3 = Object.getOwnPropertyDescriptor(o3, r3);
        t3 && Object.defineProperty(e, r3, t3.get ? t3 : { enumerable: true, get: () => o3[r3] });
      }
    }
  }
  return Object.freeze(Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }));
}
var o2;
var a;
var i = {};
var _ = { get exports() {
  return i;
}, set exports(e) {
  i = e;
} };
o2 = _, void 0 !== (a = function(e, r2) {
  Object.defineProperty(r2, "__esModule", { value: true }), r2.default = { _decimalSeparator: ",", _thousandSeparator: " ", _percentPrefix: null, _percentSuffix: "%", _big_number_suffix_3: "k", _big_number_suffix_6: "M", _big_number_suffix_9: "G", _big_number_suffix_12: "T", _big_number_suffix_15: "P", _big_number_suffix_18: "E", _big_number_suffix_21: "Z", _big_number_suffix_24: "Y", _small_number_suffix_3: "m", _small_number_suffix_6: "μ", _small_number_suffix_9: "n", _small_number_suffix_12: "p", _small_number_suffix_15: "f", _small_number_suffix_18: "a", _small_number_suffix_21: "z", _small_number_suffix_24: "y", _byte_suffix_B: "B", _byte_suffix_KB: "KB", _byte_suffix_MB: "MB", _byte_suffix_GB: "GB", _byte_suffix_TB: "TB", _byte_suffix_PB: "PB", _date_millisecond: "mm:ss SSS", _date_second: "HH:mm:ss", _date_minute: "HH:mm", _date_hour: "HH:mm", _date_day: "MMM dd", _date_week: "ww", _date_month: "MMM", _date_year: "yyyy", _duration_millisecond: "SSS", _duration_millisecond_second: "ss.SSS", _duration_millisecond_minute: "mm:ss SSS", _duration_millisecond_hour: "hh:mm:ss SSS", _duration_millisecond_day: "d'd' mm:ss SSS", _duration_millisecond_week: "d'd' mm:ss SSS", _duration_millisecond_month: "M'm' dd'd' mm:ss SSS", _duration_millisecond_year: "y'y' MM'm' dd'd' mm:ss SSS", _duration_second: "ss", _duration_second_minute: "mm:ss", _duration_second_hour: "hh:mm:ss", _duration_second_day: "d'd' hh:mm:ss", _duration_second_week: "d'd' hh:mm:ss", _duration_second_month: "M'm' dd'd' hh:mm:ss", _duration_second_year: "y'y' MM'm' dd'd' hh:mm:ss", _duration_minute: "mm", _duration_minute_hour: "hh:mm", _duration_minute_day: "d'd' hh:mm", _duration_minute_week: "d'd' hh:mm", _duration_minute_month: "M'm' dd'd' hh:mm", _duration_minute_year: "y'y' MM'm' dd'd' hh:mm", _duration_hour: "hh'h'", _duration_hour_day: "d'd' hh'h'", _duration_hour_week: "d'd' hh'h'", _duration_hour_month: "M'm' dd'd' hh'h'", _duration_hour_year: "y'y' MM'm' dd'd' hh'h'", _duration_day: "d'd'", _duration_day_week: "d'd'", _duration_day_month: "M'm' dd'd'", _duration_day_year: "y'y' MM'm' dd'd'", _duration_week: "w'w'", _duration_week_month: "w'w'", _duration_week_year: "w'w'", _duration_month: "M'm'", _duration_month_year: "y'y' MM'm'", _duration_year: "y'y'", _era_ad: "m.ē.", _era_bc: "p.m.ē.", A: "priekšp.", P: "pēcp.", AM: "priekšp.", PM: "pēcp.", "A.M.": "priekšpusdienā", "P.M.": "pēcpusdienā", January: "janvāris", February: "februāris", March: "marts", April: "aprīlis", May: "maijs", June: "jūnijs", July: "jūlijs", August: "augusts", September: "septembris", October: "oktobris", November: "novembris", December: "decembris", Jan: "janv.", Feb: "febr.", Mar: "marts", Apr: "apr.", "May(short)": "maijs", Jun: "jūn.", Jul: "jūl.", Aug: "aug.", Sep: "sept.", Oct: "okt.", Nov: "nov.", Dec: "dec.", Sunday: "svētdiena", Monday: "pirmdiena", Tuesday: "otrdiena", Wednesday: "trešdiena", Thursday: "ceturtdiena", Friday: "piektdiena", Saturday: "sestdiena", Sun: "svētd.", Mon: "pirmd.", Tue: "otrd.", Wed: "trešd.", Thu: "ceturtd.", Fri: "piektd.", Sat: "sestd.", _dateOrd: function(e2) {
    var r3 = "th";
    if (e2 < 11 || e2 > 13) switch (e2 % 10) {
      case 1:
        r3 = "st";
        break;
      case 2:
        r3 = "nd";
        break;
      case 3:
        r3 = "rd";
    }
    return r3;
  }, "Zoom Out": "Tālummaiņa", Play: "Darbināt", Stop: "Apturēt", Legend: "Apzīmējumi", "Click, tap or press ENTER to toggle": "", Loading: "Ielādē", Home: "Sākums", Chart: "", "Serial chart": "", "X/Y chart": "", "Pie chart": "", "Gauge chart": "", "Radar chart": "", "Sankey diagram": "", "Flow diagram": "", "Chord diagram": "", "TreeMap chart": "", "Sliced chart": "", Series: "", "Candlestick Series": "", "OHLC Series": "", "Column Series": "", "Line Series": "", "Pie Slice Series": "", "Funnel Series": "", "Pyramid Series": "", "X/Y Series": "", Map: "", "Press ENTER to zoom in": "", "Press ENTER to zoom out": "", "Use arrow keys to zoom in and out": "", "Use plus and minus keys on your keyboard to zoom in and out": "", Export: "Drukāt", Image: "Attēls", Data: "Dati", Print: "Drukāt", "Click, tap or press ENTER to open": "", "Click, tap or press ENTER to print.": "", "Click, tap or press ENTER to export as %1.": "", 'To save the image, right-click this link and choose "Save picture as..."': "", 'To save the image, right-click thumbnail on the left and choose "Save picture as..."': "", "(Press ESC to close this message)": "", "Image Export Complete": "", "Export operation took longer than expected. Something might have gone wrong.": "", "Saved from": "", PNG: "", JPG: "", GIF: "", SVG: "", PDF: "", JSON: "", CSV: "", XLSX: "", "Use TAB to select grip buttons or left and right arrows to change selection": "", "Use left and right arrows to move selection": "", "Use left and right arrows to move left selection": "", "Use left and right arrows to move right selection": "", "Use TAB select grip buttons or up and down arrows to change selection": "", "Use up and down arrows to move selection": "", "Use up and down arrows to move lower selection": "", "Use up and down arrows to move upper selection": "", "From %1 to %2": "No %1 līdz %2", "From %1": "No %1", "To %1": "Līdz %1", "No parser available for file: %1": "", "Error parsing file: %1": "", "Unable to load file: %1": "", "Invalid date": "" };
}(r, i)) && (o2.exports = a);
var s = t({ __proto__: null, default: o(i) }, [i]);
export {
  s as l
};
//# sourceMappingURL=lv_LV-QIZQBKEH.js.map
