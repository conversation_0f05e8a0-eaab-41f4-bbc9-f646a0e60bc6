{"version": 3, "sources": ["../../element-resize-detector/src/collection-utils.js", "../../element-resize-detector/src/element-utils.js", "../../element-resize-detector/src/listener-handler.js", "../../element-resize-detector/src/id-generator.js", "../../element-resize-detector/src/id-handler.js", "../../element-resize-detector/src/reporter.js", "../../element-resize-detector/src/browser-detector.js", "../../batch-processor/src/utils.js", "../../batch-processor/src/batch-processor.js", "../../element-resize-detector/src/state-handler.js", "../../element-resize-detector/src/detection-strategy/object.js", "../../element-resize-detector/src/detection-strategy/scroll.js", "../../element-resize-detector/src/element-resize-detector.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = module.exports = {};\n\n/**\n * Loops through the collection and calls the callback for each element. if the callback returns truthy, the loop is broken and returns the same value.\n * @public\n * @param {*} collection The collection to loop through. Needs to have a length property set and have indices set from 0 to length - 1.\n * @param {function} callback The callback to be called for each element. The element will be given as a parameter to the callback. If this callback returns truthy, the loop is broken and the same value is returned.\n * @returns {*} The value that a callback has returned (if truthy). Otherwise nothing.\n */\nutils.forEach = function(collection, callback) {\n    for(var i = 0; i < collection.length; i++) {\n        var result = callback(collection[i]);\n        if(result) {\n            return result;\n        }\n    }\n};\n", "\"use strict\";\n\nmodule.exports = function(options) {\n    var getState = options.stateHandler.getState;\n\n    /**\n     * Tells if the element has been made detectable and ready to be listened for resize events.\n     * @public\n     * @param {element} The element to check.\n     * @returns {boolean} True or false depending on if the element is detectable or not.\n     */\n    function isDetectable(element) {\n        var state = getState(element);\n        return state && !!state.isDetectable;\n    }\n\n    /**\n     * Marks the element that it has been made detectable and ready to be listened for resize events.\n     * @public\n     * @param {element} The element to mark.\n     */\n    function markAsDetectable(element) {\n        getState(element).isDetectable = true;\n    }\n\n    /**\n     * Tells if the element is busy or not.\n     * @public\n     * @param {element} The element to check.\n     * @returns {boolean} True or false depending on if the element is busy or not.\n     */\n    function isBusy(element) {\n        return !!getState(element).busy;\n    }\n\n    /**\n     * Marks the object is busy and should not be made detectable.\n     * @public\n     * @param {element} element The element to mark.\n     * @param {boolean} busy If the element is busy or not.\n     */\n    function markBusy(element, busy) {\n        getState(element).busy = !!busy;\n    }\n\n    return {\n        isDetectable: isDetectable,\n        markAsDetectable: markAsDetectable,\n        isBusy: isBusy,\n        markBusy: markBusy\n    };\n};\n", "\"use strict\";\n\nmodule.exports = function(idHandler) {\n    var eventListeners = {};\n\n    /**\n     * Gets all listeners for the given element.\n     * @public\n     * @param {element} element The element to get all listeners for.\n     * @returns All listeners for the given element.\n     */\n    function getListeners(element) {\n        var id = idHandler.get(element);\n\n        if (id === undefined) {\n            return [];\n        }\n\n        return eventListeners[id] || [];\n    }\n\n    /**\n     * Stores the given listener for the given element. Will not actually add the listener to the element.\n     * @public\n     * @param {element} element The element that should have the listener added.\n     * @param {function} listener The callback that the element has added.\n     */\n    function addListener(element, listener) {\n        var id = idHandler.get(element);\n\n        if(!eventListeners[id]) {\n            eventListeners[id] = [];\n        }\n\n        eventListeners[id].push(listener);\n    }\n\n    function removeListener(element, listener) {\n        var listeners = getListeners(element);\n        for (var i = 0, len = listeners.length; i < len; ++i) {\n            if (listeners[i] === listener) {\n              listeners.splice(i, 1);\n              break;\n            }\n        }\n    }\n\n    function removeAllListeners(element) {\n      var listeners = getListeners(element);\n      if (!listeners) { return; }\n      listeners.length = 0;\n    }\n\n    return {\n        get: getListeners,\n        add: addListener,\n        removeListener: removeListener,\n        removeAllListeners: removeAllListeners\n    };\n};\n", "\"use strict\";\n\nmodule.exports = function() {\n    var idCount = 1;\n\n    /**\n     * Generates a new unique id in the context.\n     * @public\n     * @returns {number} A unique id in the context.\n     */\n    function generate() {\n        return idCount++;\n    }\n\n    return {\n        generate: generate\n    };\n};\n", "\"use strict\";\n\nmodule.exports = function(options) {\n    var idGenerator     = options.idGenerator;\n    var getState        = options.stateHandler.getState;\n\n    /**\n     * Gets the resize detector id of the element.\n     * @public\n     * @param {element} element The target element to get the id of.\n     * @returns {string|number|null} The id of the element. Null if it has no id.\n     */\n    function getId(element) {\n        var state = getState(element);\n\n        if (state && state.id !== undefined) {\n            return state.id;\n        }\n\n        return null;\n    }\n\n    /**\n     * Sets the resize detector id of the element. Requires the element to have a resize detector state initialized.\n     * @public\n     * @param {element} element The target element to set the id of.\n     * @returns {string|number|null} The id of the element.\n     */\n    function setId(element) {\n        var state = getState(element);\n\n        if (!state) {\n            throw new Error(\"setId required the element to have a resize detection state.\");\n        }\n\n        var id = idGenerator.generate();\n\n        state.id = id;\n\n        return id;\n    }\n\n    return {\n        get: getId,\n        set: setId\n    };\n};\n", "\"use strict\";\n\n/* global console: false */\n\n/**\n * Reporter that handles the reporting of logs, warnings and errors.\n * @public\n * @param {boolean} quiet Tells if the reporter should be quiet or not.\n */\nmodule.exports = function(quiet) {\n    function noop() {\n        //Does nothing.\n    }\n\n    var reporter = {\n        log: noop,\n        warn: noop,\n        error: noop\n    };\n\n    if(!quiet && window.console) {\n        var attachFunction = function(reporter, name) {\n            //The proxy is needed to be able to call the method with the console context,\n            //since we cannot use bind.\n            reporter[name] = function reporterProxy() {\n                var f = console[name];\n                if (f.apply) { //IE9 does not support console.log.apply :)\n                    f.apply(console, arguments);\n                } else {\n                    for (var i = 0; i < arguments.length; i++) {\n                        f(arguments[i]);\n                    }\n                }\n            };\n        };\n\n        attachFunction(reporter, \"log\");\n        attachFunction(reporter, \"warn\");\n        attachFunction(reporter, \"error\");\n    }\n\n    return reporter;\n};", "\"use strict\";\n\nvar detector = module.exports = {};\n\ndetector.isIE = function(version) {\n    function isAnyIeVersion() {\n        var agent = navigator.userAgent.toLowerCase();\n        return agent.indexOf(\"msie\") !== -1 || agent.indexOf(\"trident\") !== -1 || agent.indexOf(\" edge/\") !== -1;\n    }\n\n    if(!isAnyIeVersion()) {\n        return false;\n    }\n\n    if(!version) {\n        return true;\n    }\n\n    //Shamelessly stolen from https://gist.github.com/padolsey/527683\n    var ieVersion = (function(){\n        var undef,\n            v = 3,\n            div = document.createElement(\"div\"),\n            all = div.getElementsByTagName(\"i\");\n\n        do {\n            div.innerHTML = \"<!--[if gt IE \" + (++v) + \"]><i></i><![endif]-->\";\n        }\n        while (all[0]);\n\n        return v > 4 ? v : undef;\n    }());\n\n    return version === ieVersion;\n};\n\ndetector.isLegacyOpera = function() {\n    return !!window.opera;\n};\n", "\"use strict\";\n\nvar utils = module.exports = {};\n\nutils.getOption = getOption;\n\nfunction getOption(options, name, defaultValue) {\n    var value = options[name];\n\n    if((value === undefined || value === null) && defaultValue !== undefined) {\n        return defaultValue;\n    }\n\n    return value;\n}\n", "\"use strict\";\n\nvar utils = require(\"./utils\");\n\nmodule.exports = function batchProcessorMaker(options) {\n    options             = options || {};\n    var reporter        = options.reporter;\n    var asyncProcess    = utils.getOption(options, \"async\", true);\n    var autoProcess     = utils.getOption(options, \"auto\", true);\n\n    if(autoProcess && !asyncProcess) {\n        reporter && reporter.warn(\"Invalid options combination. auto=true and async=false is invalid. Setting async=true.\");\n        asyncProcess = true;\n    }\n\n    var batch = Batch();\n    var asyncFrameHandler;\n    var isProcessing = false;\n\n    function addFunction(level, fn) {\n        if(!isProcessing && autoProcess && asyncProcess && batch.size() === 0) {\n            // Since this is async, it is guaranteed to be executed after that the fn is added to the batch.\n            // This needs to be done before, since we're checking the size of the batch to be 0.\n            processBatchAsync();\n        }\n\n        batch.add(level, fn);\n    }\n\n    function processBatch() {\n        // Save the current batch, and create a new batch so that incoming functions are not added into the currently processing batch.\n        // Continue processing until the top-level batch is empty (functions may be added to the new batch while processing, and so on).\n        isProcessing = true;\n        while (batch.size()) {\n            var processingBatch = batch;\n            batch = Batch();\n            processingBatch.process();\n        }\n        isProcessing = false;\n    }\n\n    function forceProcessBatch(localAsyncProcess) {\n        if (isProcessing) {\n            return;\n        }\n\n        if(localAsyncProcess === undefined) {\n            localAsyncProcess = asyncProcess;\n        }\n\n        if(asyncFrameHandler) {\n            cancelFrame(asyncFrameHandler);\n            asyncFrameHandler = null;\n        }\n\n        if(localAsyncProcess) {\n            processBatchAsync();\n        } else {\n            processBatch();\n        }\n    }\n\n    function processBatchAsync() {\n        asyncFrameHandler = requestFrame(processBatch);\n    }\n\n    function clearBatch() {\n        batch           = {};\n        batchSize       = 0;\n        topLevel        = 0;\n        bottomLevel     = 0;\n    }\n\n    function cancelFrame(listener) {\n        // var cancel = window.cancelAnimationFrame || window.mozCancelAnimationFrame || window.webkitCancelAnimationFrame || window.clearTimeout;\n        var cancel = clearTimeout;\n        return cancel(listener);\n    }\n\n    function requestFrame(callback) {\n        // var raf = window.requestAnimationFrame || window.mozRequestAnimationFrame || window.webkitRequestAnimationFrame || function(fn) { return window.setTimeout(fn, 20); };\n        var raf = function(fn) { return setTimeout(fn, 0); };\n        return raf(callback);\n    }\n\n    return {\n        add: addFunction,\n        force: forceProcessBatch\n    };\n};\n\nfunction Batch() {\n    var batch       = {};\n    var size        = 0;\n    var topLevel    = 0;\n    var bottomLevel = 0;\n\n    function add(level, fn) {\n        if(!fn) {\n            fn = level;\n            level = 0;\n        }\n\n        if(level > topLevel) {\n            topLevel = level;\n        } else if(level < bottomLevel) {\n            bottomLevel = level;\n        }\n\n        if(!batch[level]) {\n            batch[level] = [];\n        }\n\n        batch[level].push(fn);\n        size++;\n    }\n\n    function process() {\n        for(var level = bottomLevel; level <= topLevel; level++) {\n            var fns = batch[level];\n\n            for(var i = 0; i < fns.length; i++) {\n                var fn = fns[i];\n                fn();\n            }\n        }\n    }\n\n    function getSize() {\n        return size;\n    }\n\n    return {\n        add: add,\n        process: process,\n        size: getSize\n    };\n}\n", "\"use strict\";\n\nvar prop = \"_erd\";\n\nfunction initState(element) {\n    element[prop] = {};\n    return getState(element);\n}\n\nfunction getState(element) {\n    return element[prop];\n}\n\nfunction cleanState(element) {\n    delete element[prop];\n}\n\nmodule.exports = {\n    initState: initState,\n    getState: getState,\n    cleanState: cleanState\n};\n", "/**\n * Resize detection strategy that injects objects to elements in order to detect resize events.\n * Heavily inspired by: http://www.backalleycoder.com/2013/03/18/cross-browser-event-based-element-resize-detection/\n */\n\n\"use strict\";\n\nvar browserDetector = require(\"../browser-detector\");\n\nmodule.exports = function(options) {\n    options             = options || {};\n    var reporter        = options.reporter;\n    var batchProcessor  = options.batchProcessor;\n    var getState        = options.stateHandler.getState;\n\n    if(!reporter) {\n        throw new Error(\"Missing required dependency: reporter.\");\n    }\n\n    /**\n     * Adds a resize event listener to the element.\n     * @public\n     * @param {element} element The element that should have the listener added.\n     * @param {function} listener The listener callback to be called for each resize event of the element. The element will be given as a parameter to the listener callback.\n     */\n    function addListener(element, listener) {\n        function listenerProxy() {\n            listener(element);\n        }\n\n        if(browserDetector.isIE(8)) {\n            //IE 8 does not support object, but supports the resize event directly on elements.\n            getState(element).object = {\n                proxy: listenerProxy\n            };\n            element.attachEvent(\"onresize\", listenerProxy);\n        } else {\n            var object = getObject(element);\n\n            if(!object) {\n                throw new Error(\"Element is not detectable by this strategy.\");\n            }\n\n            object.contentDocument.defaultView.addEventListener(\"resize\", listenerProxy);\n        }\n    }\n\n    function buildCssTextString(rules) {\n        var seperator = options.important ? \" !important; \" : \"; \";\n\n        return (rules.join(seperator) + seperator).trim();\n    }\n\n    /**\n     * Makes an element detectable and ready to be listened for resize events. Will call the callback when the element is ready to be listened for resize changes.\n     * @private\n     * @param {object} options Optional options object.\n     * @param {element} element The element to make detectable\n     * @param {function} callback The callback to be called when the element is ready to be listened for resize changes. Will be called with the element as first parameter.\n     */\n    function makeDetectable(options, element, callback) {\n        if (!callback) {\n            callback = element;\n            element = options;\n            options = null;\n        }\n\n        options = options || {};\n        var debug = options.debug;\n\n        function injectObject(element, callback) {\n            var OBJECT_STYLE = buildCssTextString([\"display: block\", \"position: absolute\", \"top: 0\", \"left: 0\", \"width: 100%\", \"height: 100%\", \"border: none\", \"padding: 0\", \"margin: 0\", \"opacity: 0\", \"z-index: -1000\", \"pointer-events: none\"]);\n\n            //The target element needs to be positioned (everything except static) so the absolute positioned object will be positioned relative to the target element.\n\n            // Position altering may be performed directly or on object load, depending on if style resolution is possible directly or not.\n            var positionCheckPerformed = false;\n\n            // The element may not yet be attached to the DOM, and therefore the style object may be empty in some browsers.\n            // Since the style object is a reference, it will be updated as soon as the element is attached to the DOM.\n            var style = window.getComputedStyle(element);\n            var width = element.offsetWidth;\n            var height = element.offsetHeight;\n\n            getState(element).startSize = {\n                width: width,\n                height: height\n            };\n\n            function mutateDom() {\n                function alterPositionStyles() {\n                    if(style.position === \"static\") {\n                        element.style.setProperty(\"position\", \"relative\", options.important ? \"important\" : \"\");\n\n                        var removeRelativeStyles = function(reporter, element, style, property) {\n                            function getNumericalValue(value) {\n                                return value.replace(/[^-\\d\\.]/g, \"\");\n                            }\n\n                            var value = style[property];\n\n                            if(value !== \"auto\" && getNumericalValue(value) !== \"0\") {\n                                reporter.warn(\"An element that is positioned static has style.\" + property + \"=\" + value + \" which is ignored due to the static positioning. The element will need to be positioned relative, so the style.\" + property + \" will be set to 0. Element: \", element);\n                                element.style.setProperty(property, \"0\", options.important ? \"important\" : \"\");\n                            }\n                        };\n\n                        //Check so that there are no accidental styles that will make the element styled differently now that is is relative.\n                        //If there are any, set them to 0 (this should be okay with the user since the style properties did nothing before [since the element was positioned static] anyway).\n                        removeRelativeStyles(reporter, element, style, \"top\");\n                        removeRelativeStyles(reporter, element, style, \"right\");\n                        removeRelativeStyles(reporter, element, style, \"bottom\");\n                        removeRelativeStyles(reporter, element, style, \"left\");\n                    }\n                }\n\n                function onObjectLoad() {\n                    // The object has been loaded, which means that the element now is guaranteed to be attached to the DOM.\n                    if (!positionCheckPerformed) {\n                        alterPositionStyles();\n                    }\n\n                    /*jshint validthis: true */\n\n                    function getDocument(element, callback) {\n                        //Opera 12 seem to call the object.onload before the actual document has been created.\n                        //So if it is not present, poll it with an timeout until it is present.\n                        //TODO: Could maybe be handled better with object.onreadystatechange or similar.\n                        if(!element.contentDocument) {\n                            var state = getState(element);\n                            if (state.checkForObjectDocumentTimeoutId) {\n                                window.clearTimeout(state.checkForObjectDocumentTimeoutId);\n                            }\n                            state.checkForObjectDocumentTimeoutId = setTimeout(function checkForObjectDocument() {\n                                state.checkForObjectDocumentTimeoutId = 0;\n                                getDocument(element, callback);\n                            }, 100);\n\n                            return;\n                        }\n\n                        callback(element.contentDocument);\n                    }\n\n                    //Mutating the object element here seems to fire another load event.\n                    //Mutating the inner document of the object element is fine though.\n                    var objectElement = this;\n\n                    //Create the style element to be added to the object.\n                    getDocument(objectElement, function onObjectDocumentReady(objectDocument) {\n                        //Notify that the element is ready to be listened to.\n                        callback(element);\n                    });\n                }\n\n                // The element may be detached from the DOM, and some browsers does not support style resolving of detached elements.\n                // The alterPositionStyles needs to be delayed until we know the element has been attached to the DOM (which we are sure of when the onObjectLoad has been fired), if style resolution is not possible.\n                if (style.position !== \"\") {\n                    alterPositionStyles(style);\n                    positionCheckPerformed = true;\n                }\n\n                //Add an object element as a child to the target element that will be listened to for resize events.\n                var object = document.createElement(\"object\");\n                object.style.cssText = OBJECT_STYLE;\n                object.tabIndex = -1;\n                object.type = \"text/html\";\n                object.setAttribute(\"aria-hidden\", \"true\");\n                object.onload = onObjectLoad;\n\n                //Safari: This must occur before adding the object to the DOM.\n                //IE: Does not like that this happens before, even if it is also added after.\n                if(!browserDetector.isIE()) {\n                    object.data = \"about:blank\";\n                }\n\n                if (!getState(element)) {\n                    // The element has been uninstalled before the actual loading happened.\n                    return;\n                }\n\n                element.appendChild(object);\n                getState(element).object = object;\n\n                //IE: This must occur after adding the object to the DOM.\n                if(browserDetector.isIE()) {\n                    object.data = \"about:blank\";\n                }\n            }\n\n            if(batchProcessor) {\n                batchProcessor.add(mutateDom);\n            } else {\n                mutateDom();\n            }\n        }\n\n        if(browserDetector.isIE(8)) {\n            //IE 8 does not support objects properly. Luckily they do support the resize event.\n            //So do not inject the object and notify that the element is already ready to be listened to.\n            //The event handler for the resize event is attached in the utils.addListener instead.\n            callback(element);\n        } else {\n            injectObject(element, callback);\n        }\n    }\n\n    /**\n     * Returns the child object of the target element.\n     * @private\n     * @param {element} element The target element.\n     * @returns The object element of the target.\n     */\n    function getObject(element) {\n        return getState(element).object;\n    }\n\n    function uninstall(element) {\n        if (!getState(element)) {\n            return;\n        }\n\n        var object = getObject(element);\n\n        if (!object) {\n            return;\n        }\n\n        if (browserDetector.isIE(8)) {\n            element.detachEvent(\"onresize\", object.proxy);\n        } else {\n            element.removeChild(object);\n        }\n\n        if (getState(element).checkForObjectDocumentTimeoutId) {\n            window.clearTimeout(getState(element).checkForObjectDocumentTimeoutId);\n        }\n\n        delete getState(element).object;\n    }\n\n    return {\n        makeDetectable: makeDetectable,\n        addListener: addListener,\n        uninstall: uninstall\n    };\n};\n", "/**\n * Resize detection strategy that injects divs to elements in order to detect resize events on scroll events.\n * Heavily inspired by: https://github.com/marcj/css-element-queries/blob/master/src/ResizeSensor.js\n */\n\n\"use strict\";\n\nvar forEach = require(\"../collection-utils\").forEach;\n\nmodule.exports = function(options) {\n    options             = options || {};\n    var reporter        = options.reporter;\n    var batchProcessor  = options.batchProcessor;\n    var getState        = options.stateHandler.getState;\n    var hasState        = options.stateHandler.hasState;\n    var idHandler       = options.idHandler;\n\n    if (!batchProcessor) {\n        throw new Error(\"Missing required dependency: batchProcessor\");\n    }\n\n    if (!reporter) {\n        throw new Error(\"Missing required dependency: reporter.\");\n    }\n\n    //TODO: Could this perhaps be done at installation time?\n    var scrollbarSizes = getScrollbarSizes();\n\n    var styleId = \"erd_scroll_detection_scrollbar_style\";\n    var detectionContainerClass = \"erd_scroll_detection_container\";\n\n    function initDocument(targetDocument) {\n        // Inject the scrollbar styling that prevents them from appearing sometimes in Chrome.\n        // The injected container needs to have a class, so that it may be styled with CSS (pseudo elements).\n        injectScrollStyle(targetDocument, styleId, detectionContainerClass);\n    }\n\n    initDocument(window.document);\n\n    function buildCssTextString(rules) {\n        var seperator = options.important ? \" !important; \" : \"; \";\n\n        return (rules.join(seperator) + seperator).trim();\n    }\n\n    function getScrollbarSizes() {\n        var width = 500;\n        var height = 500;\n\n        var child = document.createElement(\"div\");\n        child.style.cssText = buildCssTextString([\"position: absolute\", \"width: \" + width*2 + \"px\", \"height: \" + height*2 + \"px\", \"visibility: hidden\", \"margin: 0\", \"padding: 0\"]);\n\n        var container = document.createElement(\"div\");\n        container.style.cssText = buildCssTextString([\"position: absolute\", \"width: \" + width + \"px\", \"height: \" + height + \"px\", \"overflow: scroll\", \"visibility: none\", \"top: \" + -width*3 + \"px\", \"left: \" + -height*3 + \"px\", \"visibility: hidden\", \"margin: 0\", \"padding: 0\"]);\n\n        container.appendChild(child);\n\n        document.body.insertBefore(container, document.body.firstChild);\n\n        var widthSize = width - container.clientWidth;\n        var heightSize = height - container.clientHeight;\n\n        document.body.removeChild(container);\n\n        return {\n            width: widthSize,\n            height: heightSize\n        };\n    }\n\n    function injectScrollStyle(targetDocument, styleId, containerClass) {\n        function injectStyle(style, method) {\n            method = method || function (element) {\n                targetDocument.head.appendChild(element);\n            };\n\n            var styleElement = targetDocument.createElement(\"style\");\n            styleElement.innerHTML = style;\n            styleElement.id = styleId;\n            method(styleElement);\n            return styleElement;\n        }\n\n        if (!targetDocument.getElementById(styleId)) {\n            var containerAnimationClass = containerClass + \"_animation\";\n            var containerAnimationActiveClass = containerClass + \"_animation_active\";\n            var style = \"/* Created by the element-resize-detector library. */\\n\";\n            style += \".\" + containerClass + \" > div::-webkit-scrollbar { \" + buildCssTextString([\"display: none\"]) + \" }\\n\\n\";\n            style += \".\" + containerAnimationActiveClass + \" { \" + buildCssTextString([\"-webkit-animation-duration: 0.1s\", \"animation-duration: 0.1s\", \"-webkit-animation-name: \" + containerAnimationClass, \"animation-name: \" + containerAnimationClass]) + \" }\\n\";\n            style += \"@-webkit-keyframes \" + containerAnimationClass +  \" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }\\n\";\n            style += \"@keyframes \" + containerAnimationClass +          \" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }\";\n            injectStyle(style);\n        }\n    }\n\n    function addAnimationClass(element) {\n        element.className += \" \" + detectionContainerClass + \"_animation_active\";\n    }\n\n    function addEvent(el, name, cb) {\n        if (el.addEventListener) {\n            el.addEventListener(name, cb);\n        } else if(el.attachEvent) {\n            el.attachEvent(\"on\" + name, cb);\n        } else {\n            return reporter.error(\"[scroll] Don't know how to add event listeners.\");\n        }\n    }\n\n    function removeEvent(el, name, cb) {\n        if (el.removeEventListener) {\n            el.removeEventListener(name, cb);\n        } else if(el.detachEvent) {\n            el.detachEvent(\"on\" + name, cb);\n        } else {\n            return reporter.error(\"[scroll] Don't know how to remove event listeners.\");\n        }\n    }\n\n    function getExpandElement(element) {\n        return getState(element).container.childNodes[0].childNodes[0].childNodes[0];\n    }\n\n    function getShrinkElement(element) {\n        return getState(element).container.childNodes[0].childNodes[0].childNodes[1];\n    }\n\n    /**\n     * Adds a resize event listener to the element.\n     * @public\n     * @param {element} element The element that should have the listener added.\n     * @param {function} listener The listener callback to be called for each resize event of the element. The element will be given as a parameter to the listener callback.\n     */\n    function addListener(element, listener) {\n        var listeners = getState(element).listeners;\n\n        if (!listeners.push) {\n            throw new Error(\"Cannot add listener to an element that is not detectable.\");\n        }\n\n        getState(element).listeners.push(listener);\n    }\n\n    /**\n     * Makes an element detectable and ready to be listened for resize events. Will call the callback when the element is ready to be listened for resize changes.\n     * @private\n     * @param {object} options Optional options object.\n     * @param {element} element The element to make detectable\n     * @param {function} callback The callback to be called when the element is ready to be listened for resize changes. Will be called with the element as first parameter.\n     */\n    function makeDetectable(options, element, callback) {\n        if (!callback) {\n            callback = element;\n            element = options;\n            options = null;\n        }\n\n        options = options || {};\n\n        function debug() {\n            if (options.debug) {\n                var args = Array.prototype.slice.call(arguments);\n                args.unshift(idHandler.get(element), \"Scroll: \");\n                if (reporter.log.apply) {\n                    reporter.log.apply(null, args);\n                } else {\n                    for (var i = 0; i < args.length; i++) {\n                        reporter.log(args[i]);\n                    }\n                }\n            }\n        }\n\n        function isDetached(element) {\n            function isInDocument(element) {\n                var isInShadowRoot = element.getRootNode && element.getRootNode().contains(element);\n                return element === element.ownerDocument.body || element.ownerDocument.body.contains(element) || isInShadowRoot;\n            }\n\n            if (!isInDocument(element)) {\n                return true;\n            }\n\n            // FireFox returns null style in hidden iframes. See https://github.com/wnr/element-resize-detector/issues/68 and https://bugzilla.mozilla.org/show_bug.cgi?id=795520\n            if (window.getComputedStyle(element) === null) {\n                return true;\n            }\n\n            return false;\n        }\n\n        function isUnrendered(element) {\n            // Check the absolute positioned container since the top level container is display: inline.\n            var container = getState(element).container.childNodes[0];\n            var style = window.getComputedStyle(container);\n            return !style.width || style.width.indexOf(\"px\") === -1; //Can only compute pixel value when rendered.\n        }\n\n        function getStyle() {\n            // Some browsers only force layouts when actually reading the style properties of the style object, so make sure that they are all read here,\n            // so that the user of the function can be sure that it will perform the layout here, instead of later (important for batching).\n            var elementStyle            = window.getComputedStyle(element);\n            var style                   = {};\n            style.position              = elementStyle.position;\n            style.width                 = element.offsetWidth;\n            style.height                = element.offsetHeight;\n            style.top                   = elementStyle.top;\n            style.right                 = elementStyle.right;\n            style.bottom                = elementStyle.bottom;\n            style.left                  = elementStyle.left;\n            style.widthCSS              = elementStyle.width;\n            style.heightCSS             = elementStyle.height;\n            return style;\n        }\n\n        function storeStartSize() {\n            var style = getStyle();\n            getState(element).startSize = {\n                width: style.width,\n                height: style.height\n            };\n            debug(\"Element start size\", getState(element).startSize);\n        }\n\n        function initListeners() {\n            getState(element).listeners = [];\n        }\n\n        function storeStyle() {\n            debug(\"storeStyle invoked.\");\n            if (!getState(element)) {\n                debug(\"Aborting because element has been uninstalled\");\n                return;\n            }\n\n            var style = getStyle();\n            getState(element).style = style;\n        }\n\n        function storeCurrentSize(element, width, height) {\n            getState(element).lastWidth = width;\n            getState(element).lastHeight  = height;\n        }\n\n        function getExpandChildElement(element) {\n            return getExpandElement(element).childNodes[0];\n        }\n\n        function getWidthOffset() {\n            return 2 * scrollbarSizes.width + 1;\n        }\n\n        function getHeightOffset() {\n            return 2 * scrollbarSizes.height + 1;\n        }\n\n        function getExpandWidth(width) {\n            return width + 10 + getWidthOffset();\n        }\n\n        function getExpandHeight(height) {\n            return height + 10 + getHeightOffset();\n        }\n\n        function getShrinkWidth(width) {\n            return width * 2 + getWidthOffset();\n        }\n\n        function getShrinkHeight(height) {\n            return height * 2 + getHeightOffset();\n        }\n\n        function positionScrollbars(element, width, height) {\n            var expand          = getExpandElement(element);\n            var shrink          = getShrinkElement(element);\n            var expandWidth     = getExpandWidth(width);\n            var expandHeight    = getExpandHeight(height);\n            var shrinkWidth     = getShrinkWidth(width);\n            var shrinkHeight    = getShrinkHeight(height);\n            expand.scrollLeft   = expandWidth;\n            expand.scrollTop    = expandHeight;\n            shrink.scrollLeft   = shrinkWidth;\n            shrink.scrollTop    = shrinkHeight;\n        }\n\n        function injectContainerElement() {\n            var container = getState(element).container;\n\n            if (!container) {\n                container                   = document.createElement(\"div\");\n                container.className         = detectionContainerClass;\n                container.style.cssText     = buildCssTextString([\"visibility: hidden\", \"display: inline\", \"width: 0px\", \"height: 0px\", \"z-index: -1\", \"overflow: hidden\", \"margin: 0\", \"padding: 0\"]);\n                getState(element).container = container;\n                addAnimationClass(container);\n                element.appendChild(container);\n\n                var onAnimationStart = function () {\n                    getState(element).onRendered && getState(element).onRendered();\n                };\n\n                addEvent(container, \"animationstart\", onAnimationStart);\n\n                // Store the event handler here so that they may be removed when uninstall is called.\n                // See uninstall function for an explanation why it is needed.\n                getState(element).onAnimationStart = onAnimationStart;\n            }\n\n            return container;\n        }\n\n        function injectScrollElements() {\n            function alterPositionStyles() {\n                var style = getState(element).style;\n\n                if(style.position === \"static\") {\n                    element.style.setProperty(\"position\", \"relative\",options.important ? \"important\" : \"\");\n\n                    var removeRelativeStyles = function(reporter, element, style, property) {\n                        function getNumericalValue(value) {\n                            return value.replace(/[^-\\d\\.]/g, \"\");\n                        }\n\n                        var value = style[property];\n\n                        if(value !== \"auto\" && getNumericalValue(value) !== \"0\") {\n                            reporter.warn(\"An element that is positioned static has style.\" + property + \"=\" + value + \" which is ignored due to the static positioning. The element will need to be positioned relative, so the style.\" + property + \" will be set to 0. Element: \", element);\n                            element.style[property] = 0;\n                        }\n                    };\n\n                    //Check so that there are no accidental styles that will make the element styled differently now that is is relative.\n                    //If there are any, set them to 0 (this should be okay with the user since the style properties did nothing before [since the element was positioned static] anyway).\n                    removeRelativeStyles(reporter, element, style, \"top\");\n                    removeRelativeStyles(reporter, element, style, \"right\");\n                    removeRelativeStyles(reporter, element, style, \"bottom\");\n                    removeRelativeStyles(reporter, element, style, \"left\");\n                }\n            }\n\n            function getLeftTopBottomRightCssText(left, top, bottom, right) {\n                left = (!left ? \"0\" : (left + \"px\"));\n                top = (!top ? \"0\" : (top + \"px\"));\n                bottom = (!bottom ? \"0\" : (bottom + \"px\"));\n                right = (!right ? \"0\" : (right + \"px\"));\n\n                return [\"left: \" + left, \"top: \" + top, \"right: \" + right, \"bottom: \" + bottom];\n            }\n\n            debug(\"Injecting elements\");\n\n            if (!getState(element)) {\n                debug(\"Aborting because element has been uninstalled\");\n                return;\n            }\n\n            alterPositionStyles();\n\n            var rootContainer = getState(element).container;\n\n            if (!rootContainer) {\n                rootContainer = injectContainerElement();\n            }\n\n            // Due to this WebKit bug https://bugs.webkit.org/show_bug.cgi?id=80808 (currently fixed in Blink, but still present in WebKit browsers such as Safari),\n            // we need to inject two containers, one that is width/height 100% and another that is left/top -1px so that the final container always is 1x1 pixels bigger than\n            // the targeted element.\n            // When the bug is resolved, \"containerContainer\" may be removed.\n\n            // The outer container can occasionally be less wide than the targeted when inside inline elements element in WebKit (see https://bugs.webkit.org/show_bug.cgi?id=152980).\n            // This should be no problem since the inner container either way makes sure the injected scroll elements are at least 1x1 px.\n\n            var scrollbarWidth          = scrollbarSizes.width;\n            var scrollbarHeight         = scrollbarSizes.height;\n            var containerContainerStyle = buildCssTextString([\"position: absolute\", \"flex: none\", \"overflow: hidden\", \"z-index: -1\", \"visibility: hidden\", \"width: 100%\", \"height: 100%\", \"left: 0px\", \"top: 0px\"]);\n            var containerStyle          = buildCssTextString([\"position: absolute\", \"flex: none\", \"overflow: hidden\", \"z-index: -1\", \"visibility: hidden\"].concat(getLeftTopBottomRightCssText(-(1 + scrollbarWidth), -(1 + scrollbarHeight), -scrollbarHeight, -scrollbarWidth)));\n            var expandStyle             = buildCssTextString([\"position: absolute\", \"flex: none\", \"overflow: scroll\", \"z-index: -1\", \"visibility: hidden\", \"width: 100%\", \"height: 100%\"]);\n            var shrinkStyle             = buildCssTextString([\"position: absolute\", \"flex: none\", \"overflow: scroll\", \"z-index: -1\", \"visibility: hidden\", \"width: 100%\", \"height: 100%\"]);\n            var expandChildStyle        = buildCssTextString([\"position: absolute\", \"left: 0\", \"top: 0\"]);\n            var shrinkChildStyle        = buildCssTextString([\"position: absolute\", \"width: 200%\", \"height: 200%\"]);\n\n            var containerContainer      = document.createElement(\"div\");\n            var container               = document.createElement(\"div\");\n            var expand                  = document.createElement(\"div\");\n            var expandChild             = document.createElement(\"div\");\n            var shrink                  = document.createElement(\"div\");\n            var shrinkChild             = document.createElement(\"div\");\n\n            // Some browsers choke on the resize system being rtl, so force it to ltr. https://github.com/wnr/element-resize-detector/issues/56\n            // However, dir should not be set on the top level container as it alters the dimensions of the target element in some browsers.\n            containerContainer.dir              = \"ltr\";\n\n            containerContainer.style.cssText    = containerContainerStyle;\n            containerContainer.className        = detectionContainerClass;\n            container.className                 = detectionContainerClass;\n            container.style.cssText             = containerStyle;\n            expand.style.cssText                = expandStyle;\n            expandChild.style.cssText           = expandChildStyle;\n            shrink.style.cssText                = shrinkStyle;\n            shrinkChild.style.cssText           = shrinkChildStyle;\n\n            expand.appendChild(expandChild);\n            shrink.appendChild(shrinkChild);\n            container.appendChild(expand);\n            container.appendChild(shrink);\n            containerContainer.appendChild(container);\n            rootContainer.appendChild(containerContainer);\n\n            function onExpandScroll() {\n                var state = getState(element);\n                if (state && state.onExpand) {\n                    state.onExpand();\n                } else {\n                    debug(\"Aborting expand scroll handler: element has been uninstalled\");\n                }\n            }\n\n            function onShrinkScroll() {\n                var state = getState(element);\n                if (state && state.onShrink) {\n                    state.onShrink();\n                } else {\n                    debug(\"Aborting shrink scroll handler: element has been uninstalled\");\n                }\n            }\n\n            addEvent(expand, \"scroll\", onExpandScroll);\n            addEvent(shrink, \"scroll\", onShrinkScroll);\n\n            // Store the event handlers here so that they may be removed when uninstall is called.\n            // See uninstall function for an explanation why it is needed.\n            getState(element).onExpandScroll = onExpandScroll;\n            getState(element).onShrinkScroll = onShrinkScroll;\n        }\n\n        function registerListenersAndPositionElements() {\n            function updateChildSizes(element, width, height) {\n                var expandChild             = getExpandChildElement(element);\n                var expandWidth             = getExpandWidth(width);\n                var expandHeight            = getExpandHeight(height);\n                expandChild.style.setProperty(\"width\", expandWidth + \"px\", options.important ? \"important\" : \"\");\n                expandChild.style.setProperty(\"height\", expandHeight + \"px\", options.important ? \"important\" : \"\");\n            }\n\n            function updateDetectorElements(done) {\n                var width           = element.offsetWidth;\n                var height          = element.offsetHeight;\n\n                // Check whether the size has actually changed since last time the algorithm ran. If not, some steps may be skipped.\n                var sizeChanged = width !== getState(element).lastWidth || height !== getState(element).lastHeight;\n\n                debug(\"Storing current size\", width, height);\n\n                // Store the size of the element sync here, so that multiple scroll events may be ignored in the event listeners.\n                // Otherwise the if-check in handleScroll is useless.\n                storeCurrentSize(element, width, height);\n\n                // Since we delay the processing of the batch, there is a risk that uninstall has been called before the batch gets to execute.\n                // Since there is no way to cancel the fn executions, we need to add an uninstall guard to all fns of the batch.\n\n                batchProcessor.add(0, function performUpdateChildSizes() {\n                    if (!sizeChanged) {\n                        return;\n                    }\n\n                    if (!getState(element)) {\n                        debug(\"Aborting because element has been uninstalled\");\n                        return;\n                    }\n\n                    if (!areElementsInjected()) {\n                        debug(\"Aborting because element container has not been initialized\");\n                        return;\n                    }\n\n                    if (options.debug) {\n                        var w = element.offsetWidth;\n                        var h = element.offsetHeight;\n\n                        if (w !== width || h !== height) {\n                            reporter.warn(idHandler.get(element), \"Scroll: Size changed before updating detector elements.\");\n                        }\n                    }\n\n                    updateChildSizes(element, width, height);\n                });\n\n                batchProcessor.add(1, function updateScrollbars() {\n                    // This function needs to be invoked event though the size is unchanged. The element could have been resized very quickly and then\n                    // been restored to the original size, which will have changed the scrollbar positions.\n\n                    if (!getState(element)) {\n                        debug(\"Aborting because element has been uninstalled\");\n                        return;\n                    }\n\n                    if (!areElementsInjected()) {\n                        debug(\"Aborting because element container has not been initialized\");\n                        return;\n                    }\n\n                    positionScrollbars(element, width, height);\n                });\n\n                if (sizeChanged && done) {\n                    batchProcessor.add(2, function () {\n                        if (!getState(element)) {\n                            debug(\"Aborting because element has been uninstalled\");\n                            return;\n                        }\n\n                        if (!areElementsInjected()) {\n                          debug(\"Aborting because element container has not been initialized\");\n                          return;\n                        }\n\n                        done();\n                    });\n                }\n            }\n\n            function areElementsInjected() {\n                return !!getState(element).container;\n            }\n\n            function notifyListenersIfNeeded() {\n                function isFirstNotify() {\n                    return getState(element).lastNotifiedWidth === undefined;\n                }\n\n                debug(\"notifyListenersIfNeeded invoked\");\n\n                var state = getState(element);\n\n                // Don't notify if the current size is the start size, and this is the first notification.\n                if (isFirstNotify() && state.lastWidth === state.startSize.width && state.lastHeight === state.startSize.height) {\n                    return debug(\"Not notifying: Size is the same as the start size, and there has been no notification yet.\");\n                }\n\n                // Don't notify if the size already has been notified.\n                if (state.lastWidth === state.lastNotifiedWidth && state.lastHeight === state.lastNotifiedHeight) {\n                    return debug(\"Not notifying: Size already notified\");\n                }\n\n\n                debug(\"Current size not notified, notifying...\");\n                state.lastNotifiedWidth = state.lastWidth;\n                state.lastNotifiedHeight = state.lastHeight;\n                forEach(getState(element).listeners, function (listener) {\n                    listener(element);\n                });\n            }\n\n            function handleRender() {\n                debug(\"startanimation triggered.\");\n\n                if (isUnrendered(element)) {\n                    debug(\"Ignoring since element is still unrendered...\");\n                    return;\n                }\n\n                debug(\"Element rendered.\");\n                var expand = getExpandElement(element);\n                var shrink = getShrinkElement(element);\n                if (expand.scrollLeft === 0 || expand.scrollTop === 0 || shrink.scrollLeft === 0 || shrink.scrollTop === 0) {\n                    debug(\"Scrollbars out of sync. Updating detector elements...\");\n                    updateDetectorElements(notifyListenersIfNeeded);\n                }\n            }\n\n            function handleScroll() {\n                debug(\"Scroll detected.\");\n\n                if (isUnrendered(element)) {\n                    // Element is still unrendered. Skip this scroll event.\n                    debug(\"Scroll event fired while unrendered. Ignoring...\");\n                    return;\n                }\n\n                updateDetectorElements(notifyListenersIfNeeded);\n            }\n\n            debug(\"registerListenersAndPositionElements invoked.\");\n\n            if (!getState(element)) {\n                debug(\"Aborting because element has been uninstalled\");\n                return;\n            }\n\n            getState(element).onRendered = handleRender;\n            getState(element).onExpand = handleScroll;\n            getState(element).onShrink = handleScroll;\n\n            var style = getState(element).style;\n            updateChildSizes(element, style.width, style.height);\n        }\n\n        function finalizeDomMutation() {\n            debug(\"finalizeDomMutation invoked.\");\n\n            if (!getState(element)) {\n                debug(\"Aborting because element has been uninstalled\");\n                return;\n            }\n\n            var style = getState(element).style;\n            storeCurrentSize(element, style.width, style.height);\n            positionScrollbars(element, style.width, style.height);\n        }\n\n        function ready() {\n            callback(element);\n        }\n\n        function install() {\n            debug(\"Installing...\");\n            initListeners();\n            storeStartSize();\n\n            batchProcessor.add(0, storeStyle);\n            batchProcessor.add(1, injectScrollElements);\n            batchProcessor.add(2, registerListenersAndPositionElements);\n            batchProcessor.add(3, finalizeDomMutation);\n            batchProcessor.add(4, ready);\n        }\n\n        debug(\"Making detectable...\");\n\n        if (isDetached(element)) {\n            debug(\"Element is detached\");\n\n            injectContainerElement();\n\n            debug(\"Waiting until element is attached...\");\n\n            getState(element).onRendered = function () {\n                debug(\"Element is now attached\");\n                install();\n            };\n        } else {\n            install();\n        }\n    }\n\n    function uninstall(element) {\n        var state = getState(element);\n\n        if (!state) {\n            // Uninstall has been called on a non-erd element.\n            return;\n        }\n\n        // Uninstall may have been called in the following scenarios:\n        // (1) Right between the sync code and async batch (here state.busy = true, but nothing have been registered or injected).\n        // (2) In the ready callback of the last level of the batch by another element (here, state.busy = true, but all the stuff has been injected).\n        // (3) After the installation process (here, state.busy = false and all the stuff has been injected).\n        // So to be on the safe side, let's check for each thing before removing.\n\n        // We need to remove the event listeners, because otherwise the event might fire on an uninstall element which results in an error when trying to get the state of the element.\n        state.onExpandScroll && removeEvent(getExpandElement(element), \"scroll\", state.onExpandScroll);\n        state.onShrinkScroll && removeEvent(getShrinkElement(element), \"scroll\", state.onShrinkScroll);\n        state.onAnimationStart && removeEvent(state.container, \"animationstart\", state.onAnimationStart);\n\n        state.container && element.removeChild(state.container);\n    }\n\n    return {\n        makeDetectable: makeDetectable,\n        addListener: addListener,\n        uninstall: uninstall,\n        initDocument: initDocument\n    };\n};\n", "\"use strict\";\n\nvar forEach                 = require(\"./collection-utils\").forEach;\nvar elementUtilsMaker       = require(\"./element-utils\");\nvar listenerHandlerMaker    = require(\"./listener-handler\");\nvar idGeneratorMaker        = require(\"./id-generator\");\nvar idHandlerMaker          = require(\"./id-handler\");\nvar reporterMaker           = require(\"./reporter\");\nvar browserDetector         = require(\"./browser-detector\");\nvar batchProcessorMaker     = require(\"batch-processor\");\nvar stateHandler            = require(\"./state-handler\");\n\n//Detection strategies.\nvar objectStrategyMaker     = require(\"./detection-strategy/object.js\");\nvar scrollStrategyMaker     = require(\"./detection-strategy/scroll.js\");\n\nfunction isCollection(obj) {\n    return Array.isArray(obj) || obj.length !== undefined;\n}\n\nfunction toArray(collection) {\n    if (!Array.isArray(collection)) {\n        var array = [];\n        forEach(collection, function (obj) {\n            array.push(obj);\n        });\n        return array;\n    } else {\n        return collection;\n    }\n}\n\nfunction isElement(obj) {\n    return obj && obj.nodeType === 1;\n}\n\n/**\n * @typedef idHandler\n * @type {object}\n * @property {function} get Gets the resize detector id of the element.\n * @property {function} set Generate and sets the resize detector id of the element.\n */\n\n/**\n * @typedef Options\n * @type {object}\n * @property {boolean} callOnAdd    Determines if listeners should be called when they are getting added.\n                                    Default is true. If true, the listener is guaranteed to be called when it has been added.\n                                    If false, the listener will not be guarenteed to be called when it has been added (does not prevent it from being called).\n * @property {idHandler} idHandler  A custom id handler that is responsible for generating, setting and retrieving id's for elements.\n                                    If not provided, a default id handler will be used.\n * @property {reporter} reporter    A custom reporter that handles reporting logs, warnings and errors.\n                                    If not provided, a default id handler will be used.\n                                    If set to false, then nothing will be reported.\n * @property {boolean} debug        If set to true, the the system will report debug messages as default for the listenTo method.\n */\n\n/**\n * Creates an element resize detector instance.\n * @public\n * @param {Options?} options Optional global options object that will decide how this instance will work.\n */\nmodule.exports = function(options) {\n    options = options || {};\n\n    //idHandler is currently not an option to the listenTo function, so it should not be added to globalOptions.\n    var idHandler;\n\n    if (options.idHandler) {\n        // To maintain compatability with idHandler.get(element, readonly), make sure to wrap the given idHandler\n        // so that readonly flag always is true when it's used here. This may be removed next major version bump.\n        idHandler = {\n            get: function (element) { return options.idHandler.get(element, true); },\n            set: options.idHandler.set\n        };\n    } else {\n        var idGenerator = idGeneratorMaker();\n        var defaultIdHandler = idHandlerMaker({\n            idGenerator: idGenerator,\n            stateHandler: stateHandler\n        });\n        idHandler = defaultIdHandler;\n    }\n\n    //reporter is currently not an option to the listenTo function, so it should not be added to globalOptions.\n    var reporter = options.reporter;\n\n    if(!reporter) {\n        //If options.reporter is false, then the reporter should be quiet.\n        var quiet = reporter === false;\n        reporter = reporterMaker(quiet);\n    }\n\n    //batchProcessor is currently not an option to the listenTo function, so it should not be added to globalOptions.\n    var batchProcessor = getOption(options, \"batchProcessor\", batchProcessorMaker({ reporter: reporter }));\n\n    //Options to be used as default for the listenTo function.\n    var globalOptions = {};\n    globalOptions.callOnAdd     = !!getOption(options, \"callOnAdd\", true);\n    globalOptions.debug         = !!getOption(options, \"debug\", false);\n\n    var eventListenerHandler    = listenerHandlerMaker(idHandler);\n    var elementUtils            = elementUtilsMaker({\n        stateHandler: stateHandler\n    });\n\n    //The detection strategy to be used.\n    var detectionStrategy;\n    var desiredStrategy = getOption(options, \"strategy\", \"object\");\n    var importantCssRules = getOption(options, \"important\", false);\n    var strategyOptions = {\n        reporter: reporter,\n        batchProcessor: batchProcessor,\n        stateHandler: stateHandler,\n        idHandler: idHandler,\n        important: importantCssRules\n    };\n\n    if(desiredStrategy === \"scroll\") {\n        if (browserDetector.isLegacyOpera()) {\n            reporter.warn(\"Scroll strategy is not supported on legacy Opera. Changing to object strategy.\");\n            desiredStrategy = \"object\";\n        } else if (browserDetector.isIE(9)) {\n            reporter.warn(\"Scroll strategy is not supported on IE9. Changing to object strategy.\");\n            desiredStrategy = \"object\";\n        }\n    }\n\n    if(desiredStrategy === \"scroll\") {\n        detectionStrategy = scrollStrategyMaker(strategyOptions);\n    } else if(desiredStrategy === \"object\") {\n        detectionStrategy = objectStrategyMaker(strategyOptions);\n    } else {\n        throw new Error(\"Invalid strategy name: \" + desiredStrategy);\n    }\n\n    //Calls can be made to listenTo with elements that are still being installed.\n    //Also, same elements can occur in the elements list in the listenTo function.\n    //With this map, the ready callbacks can be synchronized between the calls\n    //so that the ready callback can always be called when an element is ready - even if\n    //it wasn't installed from the function itself.\n    var onReadyCallbacks = {};\n\n    /**\n     * Makes the given elements resize-detectable and starts listening to resize events on the elements. Calls the event callback for each event for each element.\n     * @public\n     * @param {Options?} options Optional options object. These options will override the global options. Some options may not be overriden, such as idHandler.\n     * @param {element[]|element} elements The given array of elements to detect resize events of. Single element is also valid.\n     * @param {function} listener The callback to be executed for each resize event for each element.\n     */\n    function listenTo(options, elements, listener) {\n        function onResizeCallback(element) {\n            var listeners = eventListenerHandler.get(element);\n            forEach(listeners, function callListenerProxy(listener) {\n                listener(element);\n            });\n        }\n\n        function addListener(callOnAdd, element, listener) {\n            eventListenerHandler.add(element, listener);\n\n            if(callOnAdd) {\n                listener(element);\n            }\n        }\n\n        //Options object may be omitted.\n        if(!listener) {\n            listener = elements;\n            elements = options;\n            options = {};\n        }\n\n        if(!elements) {\n            throw new Error(\"At least one element required.\");\n        }\n\n        if(!listener) {\n            throw new Error(\"Listener required.\");\n        }\n\n        if (isElement(elements)) {\n            // A single element has been passed in.\n            elements = [elements];\n        } else if (isCollection(elements)) {\n            // Convert collection to array for plugins.\n            // TODO: May want to check so that all the elements in the collection are valid elements.\n            elements = toArray(elements);\n        } else {\n            return reporter.error(\"Invalid arguments. Must be a DOM element or a collection of DOM elements.\");\n        }\n\n        var elementsReady = 0;\n\n        var callOnAdd = getOption(options, \"callOnAdd\", globalOptions.callOnAdd);\n        var onReadyCallback = getOption(options, \"onReady\", function noop() {});\n        var debug = getOption(options, \"debug\", globalOptions.debug);\n\n        forEach(elements, function attachListenerToElement(element) {\n            if (!stateHandler.getState(element)) {\n                stateHandler.initState(element);\n                idHandler.set(element);\n            }\n\n            var id = idHandler.get(element);\n\n            debug && reporter.log(\"Attaching listener to element\", id, element);\n\n            if(!elementUtils.isDetectable(element)) {\n                debug && reporter.log(id, \"Not detectable.\");\n                if(elementUtils.isBusy(element)) {\n                    debug && reporter.log(id, \"System busy making it detectable\");\n\n                    //The element is being prepared to be detectable. Do not make it detectable.\n                    //Just add the listener, because the element will soon be detectable.\n                    addListener(callOnAdd, element, listener);\n                    onReadyCallbacks[id] = onReadyCallbacks[id] || [];\n                    onReadyCallbacks[id].push(function onReady() {\n                        elementsReady++;\n\n                        if(elementsReady === elements.length) {\n                            onReadyCallback();\n                        }\n                    });\n                    return;\n                }\n\n                debug && reporter.log(id, \"Making detectable...\");\n                //The element is not prepared to be detectable, so do prepare it and add a listener to it.\n                elementUtils.markBusy(element, true);\n                return detectionStrategy.makeDetectable({ debug: debug, important: importantCssRules }, element, function onElementDetectable(element) {\n                    debug && reporter.log(id, \"onElementDetectable\");\n\n                    if (stateHandler.getState(element)) {\n                        elementUtils.markAsDetectable(element);\n                        elementUtils.markBusy(element, false);\n                        detectionStrategy.addListener(element, onResizeCallback);\n                        addListener(callOnAdd, element, listener);\n\n                        // Since the element size might have changed since the call to \"listenTo\", we need to check for this change,\n                        // so that a resize event may be emitted.\n                        // Having the startSize object is optional (since it does not make sense in some cases such as unrendered elements), so check for its existance before.\n                        // Also, check the state existance before since the element may have been uninstalled in the installation process.\n                        var state = stateHandler.getState(element);\n                        if (state && state.startSize) {\n                            var width = element.offsetWidth;\n                            var height = element.offsetHeight;\n                            if (state.startSize.width !== width || state.startSize.height !== height) {\n                                onResizeCallback(element);\n                            }\n                        }\n\n                        if(onReadyCallbacks[id]) {\n                            forEach(onReadyCallbacks[id], function(callback) {\n                                callback();\n                            });\n                        }\n                    } else {\n                        // The element has been unisntalled before being detectable.\n                        debug && reporter.log(id, \"Element uninstalled before being detectable.\");\n                    }\n\n                    delete onReadyCallbacks[id];\n\n                    elementsReady++;\n                    if(elementsReady === elements.length) {\n                        onReadyCallback();\n                    }\n                });\n            }\n\n            debug && reporter.log(id, \"Already detecable, adding listener.\");\n\n            //The element has been prepared to be detectable and is ready to be listened to.\n            addListener(callOnAdd, element, listener);\n            elementsReady++;\n        });\n\n        if(elementsReady === elements.length) {\n            onReadyCallback();\n        }\n    }\n\n    function uninstall(elements) {\n        if(!elements) {\n            return reporter.error(\"At least one element is required.\");\n        }\n\n        if (isElement(elements)) {\n            // A single element has been passed in.\n            elements = [elements];\n        } else if (isCollection(elements)) {\n            // Convert collection to array for plugins.\n            // TODO: May want to check so that all the elements in the collection are valid elements.\n            elements = toArray(elements);\n        } else {\n            return reporter.error(\"Invalid arguments. Must be a DOM element or a collection of DOM elements.\");\n        }\n\n        forEach(elements, function (element) {\n            eventListenerHandler.removeAllListeners(element);\n            detectionStrategy.uninstall(element);\n            stateHandler.cleanState(element);\n        });\n    }\n\n    function initDocument(targetDocument) {\n        detectionStrategy.initDocument && detectionStrategy.initDocument(targetDocument);\n    }\n\n    return {\n        listenTo: listenTo,\n        removeListener: eventListenerHandler.removeListener,\n        removeAllListeners: eventListenerHandler.removeAllListeners,\n        uninstall: uninstall,\n        initDocument: initDocument\n    };\n};\n\nfunction getOption(options, name, defaultValue) {\n    var value = options[name];\n\n    if((value === undefined || value === null) && defaultValue !== undefined) {\n        return defaultValue;\n    }\n\n    return value;\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,QAAI,QAAQ,OAAO,UAAU,CAAC;AAS9B,UAAM,UAAU,SAAS,YAAY,UAAU;AAC3C,eAAQ,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACvC,YAAI,SAAS,SAAS,WAAW,CAAC,CAAC;AACnC,YAAG,QAAQ;AACP,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;AClBA;AAAA;AAAA;AAEA,WAAO,UAAU,SAAS,SAAS;AAC/B,UAAI,WAAW,QAAQ,aAAa;AAQpC,eAAS,aAAa,SAAS;AAC3B,YAAI,QAAQ,SAAS,OAAO;AAC5B,eAAO,SAAS,CAAC,CAAC,MAAM;AAAA,MAC5B;AAOA,eAAS,iBAAiB,SAAS;AAC/B,iBAAS,OAAO,EAAE,eAAe;AAAA,MACrC;AAQA,eAAS,OAAO,SAAS;AACrB,eAAO,CAAC,CAAC,SAAS,OAAO,EAAE;AAAA,MAC/B;AAQA,eAAS,SAAS,SAAS,MAAM;AAC7B,iBAAS,OAAO,EAAE,OAAO,CAAC,CAAC;AAAA,MAC/B;AAEA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;ACnDA;AAAA;AAAA;AAEA,WAAO,UAAU,SAAS,WAAW;AACjC,UAAI,iBAAiB,CAAC;AAQtB,eAAS,aAAa,SAAS;AAC3B,YAAI,KAAK,UAAU,IAAI,OAAO;AAE9B,YAAI,OAAO,QAAW;AAClB,iBAAO,CAAC;AAAA,QACZ;AAEA,eAAO,eAAe,EAAE,KAAK,CAAC;AAAA,MAClC;AAQA,eAAS,YAAY,SAAS,UAAU;AACpC,YAAI,KAAK,UAAU,IAAI,OAAO;AAE9B,YAAG,CAAC,eAAe,EAAE,GAAG;AACpB,yBAAe,EAAE,IAAI,CAAC;AAAA,QAC1B;AAEA,uBAAe,EAAE,EAAE,KAAK,QAAQ;AAAA,MACpC;AAEA,eAAS,eAAe,SAAS,UAAU;AACvC,YAAI,YAAY,aAAa,OAAO;AACpC,iBAAS,IAAI,GAAG,MAAM,UAAU,QAAQ,IAAI,KAAK,EAAE,GAAG;AAClD,cAAI,UAAU,CAAC,MAAM,UAAU;AAC7B,sBAAU,OAAO,GAAG,CAAC;AACrB;AAAA,UACF;AAAA,QACJ;AAAA,MACJ;AAEA,eAAS,mBAAmB,SAAS;AACnC,YAAI,YAAY,aAAa,OAAO;AACpC,YAAI,CAAC,WAAW;AAAE;AAAA,QAAQ;AAC1B,kBAAU,SAAS;AAAA,MACrB;AAEA,aAAO;AAAA,QACH,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;AC3DA;AAAA;AAAA;AAEA,WAAO,UAAU,WAAW;AACxB,UAAI,UAAU;AAOd,eAAS,WAAW;AAChB,eAAO;AAAA,MACX;AAEA,aAAO;AAAA,QACH;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;ACjBA;AAAA;AAAA;AAEA,WAAO,UAAU,SAAS,SAAS;AAC/B,UAAI,cAAkB,QAAQ;AAC9B,UAAI,WAAkB,QAAQ,aAAa;AAQ3C,eAAS,MAAM,SAAS;AACpB,YAAI,QAAQ,SAAS,OAAO;AAE5B,YAAI,SAAS,MAAM,OAAO,QAAW;AACjC,iBAAO,MAAM;AAAA,QACjB;AAEA,eAAO;AAAA,MACX;AAQA,eAAS,MAAM,SAAS;AACpB,YAAI,QAAQ,SAAS,OAAO;AAE5B,YAAI,CAAC,OAAO;AACR,gBAAM,IAAI,MAAM,8DAA8D;AAAA,QAClF;AAEA,YAAI,KAAK,YAAY,SAAS;AAE9B,cAAM,KAAK;AAEX,eAAO;AAAA,MACX;AAEA,aAAO;AAAA,QACH,KAAK;AAAA,QACL,KAAK;AAAA,MACT;AAAA,IACJ;AAAA;AAAA;;;AC9CA;AAAA;AAAA;AASA,WAAO,UAAU,SAAS,OAAO;AAC7B,eAAS,OAAO;AAAA,MAEhB;AAEA,UAAI,WAAW;AAAA,QACX,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AAEA,UAAG,CAAC,SAAS,OAAO,SAAS;AACzB,YAAI,iBAAiB,SAASA,WAAU,MAAM;AAG1C,UAAAA,UAAS,IAAI,IAAI,SAAS,gBAAgB;AACtC,gBAAI,IAAI,QAAQ,IAAI;AACpB,gBAAI,EAAE,OAAO;AACT,gBAAE,MAAM,SAAS,SAAS;AAAA,YAC9B,OAAO;AACH,uBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,kBAAE,UAAU,CAAC,CAAC;AAAA,cAClB;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAEA,uBAAe,UAAU,KAAK;AAC9B,uBAAe,UAAU,MAAM;AAC/B,uBAAe,UAAU,OAAO;AAAA,MACpC;AAEA,aAAO;AAAA,IACX;AAAA;AAAA;;;AC1CA;AAAA;AAAA;AAEA,QAAI,WAAW,OAAO,UAAU,CAAC;AAEjC,aAAS,OAAO,SAAS,SAAS;AAC9B,eAAS,iBAAiB;AACtB,YAAI,QAAQ,UAAU,UAAU,YAAY;AAC5C,eAAO,MAAM,QAAQ,MAAM,MAAM,MAAM,MAAM,QAAQ,SAAS,MAAM,MAAM,MAAM,QAAQ,QAAQ,MAAM;AAAA,MAC1G;AAEA,UAAG,CAAC,eAAe,GAAG;AAClB,eAAO;AAAA,MACX;AAEA,UAAG,CAAC,SAAS;AACT,eAAO;AAAA,MACX;AAGA,UAAI,YAAa,WAAU;AACvB,YAAI,OACA,IAAI,GACJ,MAAM,SAAS,cAAc,KAAK,GAClC,MAAM,IAAI,qBAAqB,GAAG;AAEtC,WAAG;AACC,cAAI,YAAY,mBAAoB,EAAE,IAAK;AAAA,QAC/C,SACO,IAAI,CAAC;AAEZ,eAAO,IAAI,IAAI,IAAI;AAAA,MACvB,EAAE;AAEF,aAAO,YAAY;AAAA,IACvB;AAEA,aAAS,gBAAgB,WAAW;AAChC,aAAO,CAAC,CAAC,OAAO;AAAA,IACpB;AAAA;AAAA;;;ACtCA;AAAA;AAAA;AAEA,QAAI,QAAQ,OAAO,UAAU,CAAC;AAE9B,UAAM,YAAY;AAElB,aAAS,UAAU,SAAS,MAAM,cAAc;AAC5C,UAAI,QAAQ,QAAQ,IAAI;AAExB,WAAI,UAAU,UAAa,UAAU,SAAS,iBAAiB,QAAW;AACtE,eAAO;AAAA,MACX;AAEA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,QAAQ;AAEZ,WAAO,UAAU,SAAS,oBAAoB,SAAS;AACnD,gBAAsB,WAAW,CAAC;AAClC,UAAI,WAAkB,QAAQ;AAC9B,UAAI,eAAkB,MAAM,UAAU,SAAS,SAAS,IAAI;AAC5D,UAAI,cAAkB,MAAM,UAAU,SAAS,QAAQ,IAAI;AAE3D,UAAG,eAAe,CAAC,cAAc;AAC7B,oBAAY,SAAS,KAAK,wFAAwF;AAClH,uBAAe;AAAA,MACnB;AAEA,UAAI,QAAQ,MAAM;AAClB,UAAI;AACJ,UAAI,eAAe;AAEnB,eAAS,YAAY,OAAO,IAAI;AAC5B,YAAG,CAAC,gBAAgB,eAAe,gBAAgB,MAAM,KAAK,MAAM,GAAG;AAGnE,4BAAkB;AAAA,QACtB;AAEA,cAAM,IAAI,OAAO,EAAE;AAAA,MACvB;AAEA,eAAS,eAAe;AAGpB,uBAAe;AACf,eAAO,MAAM,KAAK,GAAG;AACjB,cAAI,kBAAkB;AACtB,kBAAQ,MAAM;AACd,0BAAgB,QAAQ;AAAA,QAC5B;AACA,uBAAe;AAAA,MACnB;AAEA,eAAS,kBAAkB,mBAAmB;AAC1C,YAAI,cAAc;AACd;AAAA,QACJ;AAEA,YAAG,sBAAsB,QAAW;AAChC,8BAAoB;AAAA,QACxB;AAEA,YAAG,mBAAmB;AAClB,sBAAY,iBAAiB;AAC7B,8BAAoB;AAAA,QACxB;AAEA,YAAG,mBAAmB;AAClB,4BAAkB;AAAA,QACtB,OAAO;AACH,uBAAa;AAAA,QACjB;AAAA,MACJ;AAEA,eAAS,oBAAoB;AACzB,4BAAoB,aAAa,YAAY;AAAA,MACjD;AAEA,eAAS,aAAa;AAClB,gBAAkB,CAAC;AACnB,oBAAkB;AAClB,mBAAkB;AAClB,sBAAkB;AAAA,MACtB;AAEA,eAAS,YAAY,UAAU;AAE3B,YAAI,SAAS;AACb,eAAO,OAAO,QAAQ;AAAA,MAC1B;AAEA,eAAS,aAAa,UAAU;AAE5B,YAAI,MAAM,SAAS,IAAI;AAAE,iBAAO,WAAW,IAAI,CAAC;AAAA,QAAG;AACnD,eAAO,IAAI,QAAQ;AAAA,MACvB;AAEA,aAAO;AAAA,QACH,KAAK;AAAA,QACL,OAAO;AAAA,MACX;AAAA,IACJ;AAEA,aAAS,QAAQ;AACb,UAAI,QAAc,CAAC;AACnB,UAAI,OAAc;AAClB,UAAIC,YAAc;AAClB,UAAIC,eAAc;AAElB,eAAS,IAAI,OAAO,IAAI;AACpB,YAAG,CAAC,IAAI;AACJ,eAAK;AACL,kBAAQ;AAAA,QACZ;AAEA,YAAG,QAAQD,WAAU;AACjB,UAAAA,YAAW;AAAA,QACf,WAAU,QAAQC,cAAa;AAC3B,UAAAA,eAAc;AAAA,QAClB;AAEA,YAAG,CAAC,MAAM,KAAK,GAAG;AACd,gBAAM,KAAK,IAAI,CAAC;AAAA,QACpB;AAEA,cAAM,KAAK,EAAE,KAAK,EAAE;AACpB;AAAA,MACJ;AAEA,eAAS,UAAU;AACf,iBAAQ,QAAQA,cAAa,SAASD,WAAU,SAAS;AACrD,cAAI,MAAM,MAAM,KAAK;AAErB,mBAAQ,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAChC,gBAAI,KAAK,IAAI,CAAC;AACd,eAAG;AAAA,UACP;AAAA,QACJ;AAAA,MACJ;AAEA,eAAS,UAAU;AACf,eAAO;AAAA,MACX;AAEA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACV;AAAA,IACJ;AAAA;AAAA;;;ACzIA;AAAA;AAAA;AAEA,QAAI,OAAO;AAEX,aAAS,UAAU,SAAS;AACxB,cAAQ,IAAI,IAAI,CAAC;AACjB,aAAO,SAAS,OAAO;AAAA,IAC3B;AAEA,aAAS,SAAS,SAAS;AACvB,aAAO,QAAQ,IAAI;AAAA,IACvB;AAEA,aAAS,WAAW,SAAS;AACzB,aAAO,QAAQ,IAAI;AAAA,IACvB;AAEA,WAAO,UAAU;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA;AAAA;;;ACrBA;AAAA;AAAA;AAOA,QAAI,kBAAkB;AAEtB,WAAO,UAAU,SAAS,SAAS;AAC/B,gBAAsB,WAAW,CAAC;AAClC,UAAI,WAAkB,QAAQ;AAC9B,UAAI,iBAAkB,QAAQ;AAC9B,UAAI,WAAkB,QAAQ,aAAa;AAE3C,UAAG,CAAC,UAAU;AACV,cAAM,IAAI,MAAM,wCAAwC;AAAA,MAC5D;AAQA,eAAS,YAAY,SAAS,UAAU;AACpC,iBAAS,gBAAgB;AACrB,mBAAS,OAAO;AAAA,QACpB;AAEA,YAAG,gBAAgB,KAAK,CAAC,GAAG;AAExB,mBAAS,OAAO,EAAE,SAAS;AAAA,YACvB,OAAO;AAAA,UACX;AACA,kBAAQ,YAAY,YAAY,aAAa;AAAA,QACjD,OAAO;AACH,cAAI,SAAS,UAAU,OAAO;AAE9B,cAAG,CAAC,QAAQ;AACR,kBAAM,IAAI,MAAM,6CAA6C;AAAA,UACjE;AAEA,iBAAO,gBAAgB,YAAY,iBAAiB,UAAU,aAAa;AAAA,QAC/E;AAAA,MACJ;AAEA,eAAS,mBAAmB,OAAO;AAC/B,YAAI,YAAY,QAAQ,YAAY,kBAAkB;AAEtD,gBAAQ,MAAM,KAAK,SAAS,IAAI,WAAW,KAAK;AAAA,MACpD;AASA,eAAS,eAAeE,UAAS,SAAS,UAAU;AAChD,YAAI,CAAC,UAAU;AACX,qBAAW;AACX,oBAAUA;AACV,UAAAA,WAAU;AAAA,QACd;AAEA,QAAAA,WAAUA,YAAW,CAAC;AACtB,YAAI,QAAQA,SAAQ;AAEpB,iBAAS,aAAaC,UAASC,WAAU;AACrC,cAAI,eAAe,mBAAmB,CAAC,kBAAkB,sBAAsB,UAAU,WAAW,eAAe,gBAAgB,gBAAgB,cAAc,aAAa,cAAc,kBAAkB,sBAAsB,CAAC;AAKrO,cAAI,yBAAyB;AAI7B,cAAI,QAAQ,OAAO,iBAAiBD,QAAO;AAC3C,cAAI,QAAQA,SAAQ;AACpB,cAAI,SAASA,SAAQ;AAErB,mBAASA,QAAO,EAAE,YAAY;AAAA,YAC1B;AAAA,YACA;AAAA,UACJ;AAEA,mBAAS,YAAY;AACjB,qBAAS,sBAAsB;AAC3B,kBAAG,MAAM,aAAa,UAAU;AAC5B,gBAAAA,SAAQ,MAAM,YAAY,YAAY,YAAYD,SAAQ,YAAY,cAAc,EAAE;AAEtF,oBAAI,uBAAuB,SAASG,WAAUF,UAASG,QAAO,UAAU;AACpE,2BAAS,kBAAkBC,QAAO;AAC9B,2BAAOA,OAAM,QAAQ,aAAa,EAAE;AAAA,kBACxC;AAEA,sBAAI,QAAQD,OAAM,QAAQ;AAE1B,sBAAG,UAAU,UAAU,kBAAkB,KAAK,MAAM,KAAK;AACrD,oBAAAD,UAAS,KAAK,oDAAoD,WAAW,MAAM,QAAQ,oHAAoH,WAAW,gCAAgCF,QAAO;AACjQ,oBAAAA,SAAQ,MAAM,YAAY,UAAU,KAAKD,SAAQ,YAAY,cAAc,EAAE;AAAA,kBACjF;AAAA,gBACJ;AAIA,qCAAqB,UAAUC,UAAS,OAAO,KAAK;AACpD,qCAAqB,UAAUA,UAAS,OAAO,OAAO;AACtD,qCAAqB,UAAUA,UAAS,OAAO,QAAQ;AACvD,qCAAqB,UAAUA,UAAS,OAAO,MAAM;AAAA,cACzD;AAAA,YACJ;AAEA,qBAAS,eAAe;AAEpB,kBAAI,CAAC,wBAAwB;AACzB,oCAAoB;AAAA,cACxB;AAIA,uBAAS,YAAYA,UAASC,WAAU;AAIpC,oBAAG,CAACD,SAAQ,iBAAiB;AACzB,sBAAI,QAAQ,SAASA,QAAO;AAC5B,sBAAI,MAAM,iCAAiC;AACvC,2BAAO,aAAa,MAAM,+BAA+B;AAAA,kBAC7D;AACA,wBAAM,kCAAkC,WAAW,SAAS,yBAAyB;AACjF,0BAAM,kCAAkC;AACxC,gCAAYA,UAASC,SAAQ;AAAA,kBACjC,GAAG,GAAG;AAEN;AAAA,gBACJ;AAEA,gBAAAA,UAASD,SAAQ,eAAe;AAAA,cACpC;AAIA,kBAAI,gBAAgB;AAGpB,0BAAY,eAAe,SAAS,sBAAsB,gBAAgB;AAEtE,gBAAAC,UAASD,QAAO;AAAA,cACpB,CAAC;AAAA,YACL;AAIA,gBAAI,MAAM,aAAa,IAAI;AACvB,kCAAoB,KAAK;AACzB,uCAAyB;AAAA,YAC7B;AAGA,gBAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,mBAAO,MAAM,UAAU;AACvB,mBAAO,WAAW;AAClB,mBAAO,OAAO;AACd,mBAAO,aAAa,eAAe,MAAM;AACzC,mBAAO,SAAS;AAIhB,gBAAG,CAAC,gBAAgB,KAAK,GAAG;AACxB,qBAAO,OAAO;AAAA,YAClB;AAEA,gBAAI,CAAC,SAASA,QAAO,GAAG;AAEpB;AAAA,YACJ;AAEA,YAAAA,SAAQ,YAAY,MAAM;AAC1B,qBAASA,QAAO,EAAE,SAAS;AAG3B,gBAAG,gBAAgB,KAAK,GAAG;AACvB,qBAAO,OAAO;AAAA,YAClB;AAAA,UACJ;AAEA,cAAG,gBAAgB;AACf,2BAAe,IAAI,SAAS;AAAA,UAChC,OAAO;AACH,sBAAU;AAAA,UACd;AAAA,QACJ;AAEA,YAAG,gBAAgB,KAAK,CAAC,GAAG;AAIxB,mBAAS,OAAO;AAAA,QACpB,OAAO;AACH,uBAAa,SAAS,QAAQ;AAAA,QAClC;AAAA,MACJ;AAQA,eAAS,UAAU,SAAS;AACxB,eAAO,SAAS,OAAO,EAAE;AAAA,MAC7B;AAEA,eAAS,UAAU,SAAS;AACxB,YAAI,CAAC,SAAS,OAAO,GAAG;AACpB;AAAA,QACJ;AAEA,YAAI,SAAS,UAAU,OAAO;AAE9B,YAAI,CAAC,QAAQ;AACT;AAAA,QACJ;AAEA,YAAI,gBAAgB,KAAK,CAAC,GAAG;AACzB,kBAAQ,YAAY,YAAY,OAAO,KAAK;AAAA,QAChD,OAAO;AACH,kBAAQ,YAAY,MAAM;AAAA,QAC9B;AAEA,YAAI,SAAS,OAAO,EAAE,iCAAiC;AACnD,iBAAO,aAAa,SAAS,OAAO,EAAE,+BAA+B;AAAA,QACzE;AAEA,eAAO,SAAS,OAAO,EAAE;AAAA,MAC7B;AAEA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;ACtPA;AAAA;AAAA;AAOA,QAAI,UAAU,2BAA+B;AAE7C,WAAO,UAAU,SAAS,SAAS;AAC/B,gBAAsB,WAAW,CAAC;AAClC,UAAI,WAAkB,QAAQ;AAC9B,UAAI,iBAAkB,QAAQ;AAC9B,UAAI,WAAkB,QAAQ,aAAa;AAC3C,UAAI,WAAkB,QAAQ,aAAa;AAC3C,UAAI,YAAkB,QAAQ;AAE9B,UAAI,CAAC,gBAAgB;AACjB,cAAM,IAAI,MAAM,6CAA6C;AAAA,MACjE;AAEA,UAAI,CAAC,UAAU;AACX,cAAM,IAAI,MAAM,wCAAwC;AAAA,MAC5D;AAGA,UAAI,iBAAiB,kBAAkB;AAEvC,UAAI,UAAU;AACd,UAAI,0BAA0B;AAE9B,eAAS,aAAa,gBAAgB;AAGlC,0BAAkB,gBAAgB,SAAS,uBAAuB;AAAA,MACtE;AAEA,mBAAa,OAAO,QAAQ;AAE5B,eAAS,mBAAmB,OAAO;AAC/B,YAAI,YAAY,QAAQ,YAAY,kBAAkB;AAEtD,gBAAQ,MAAM,KAAK,SAAS,IAAI,WAAW,KAAK;AAAA,MACpD;AAEA,eAAS,oBAAoB;AACzB,YAAI,QAAQ;AACZ,YAAI,SAAS;AAEb,YAAI,QAAQ,SAAS,cAAc,KAAK;AACxC,cAAM,MAAM,UAAU,mBAAmB,CAAC,sBAAsB,YAAY,QAAM,IAAI,MAAM,aAAa,SAAO,IAAI,MAAM,sBAAsB,aAAa,YAAY,CAAC;AAE1K,YAAI,YAAY,SAAS,cAAc,KAAK;AAC5C,kBAAU,MAAM,UAAU,mBAAmB,CAAC,sBAAsB,YAAY,QAAQ,MAAM,aAAa,SAAS,MAAM,oBAAoB,oBAAoB,UAAU,CAAC,QAAM,IAAI,MAAM,WAAW,CAAC,SAAO,IAAI,MAAM,sBAAsB,aAAa,YAAY,CAAC;AAE1Q,kBAAU,YAAY,KAAK;AAE3B,iBAAS,KAAK,aAAa,WAAW,SAAS,KAAK,UAAU;AAE9D,YAAI,YAAY,QAAQ,UAAU;AAClC,YAAI,aAAa,SAAS,UAAU;AAEpC,iBAAS,KAAK,YAAY,SAAS;AAEnC,eAAO;AAAA,UACH,OAAO;AAAA,UACP,QAAQ;AAAA,QACZ;AAAA,MACJ;AAEA,eAAS,kBAAkB,gBAAgBK,UAAS,gBAAgB;AAChE,iBAAS,YAAYC,QAAO,QAAQ;AAChC,mBAAS,UAAU,SAAU,SAAS;AAClC,2BAAe,KAAK,YAAY,OAAO;AAAA,UAC3C;AAEA,cAAI,eAAe,eAAe,cAAc,OAAO;AACvD,uBAAa,YAAYA;AACzB,uBAAa,KAAKD;AAClB,iBAAO,YAAY;AACnB,iBAAO;AAAA,QACX;AAEA,YAAI,CAAC,eAAe,eAAeA,QAAO,GAAG;AACzC,cAAI,0BAA0B,iBAAiB;AAC/C,cAAI,gCAAgC,iBAAiB;AACrD,cAAI,QAAQ;AACZ,mBAAS,MAAM,iBAAiB,iCAAiC,mBAAmB,CAAC,eAAe,CAAC,IAAI;AACzG,mBAAS,MAAM,gCAAgC,QAAQ,mBAAmB,CAAC,oCAAoC,4BAA4B,6BAA6B,yBAAyB,qBAAqB,uBAAuB,CAAC,IAAI;AAClP,mBAAS,wBAAwB,0BAA2B;AAC5D,mBAAS,gBAAgB,0BAAmC;AAC5D,sBAAY,KAAK;AAAA,QACrB;AAAA,MACJ;AAEA,eAAS,kBAAkB,SAAS;AAChC,gBAAQ,aAAa,MAAM,0BAA0B;AAAA,MACzD;AAEA,eAAS,SAAS,IAAI,MAAM,IAAI;AAC5B,YAAI,GAAG,kBAAkB;AACrB,aAAG,iBAAiB,MAAM,EAAE;AAAA,QAChC,WAAU,GAAG,aAAa;AACtB,aAAG,YAAY,OAAO,MAAM,EAAE;AAAA,QAClC,OAAO;AACH,iBAAO,SAAS,MAAM,iDAAiD;AAAA,QAC3E;AAAA,MACJ;AAEA,eAAS,YAAY,IAAI,MAAM,IAAI;AAC/B,YAAI,GAAG,qBAAqB;AACxB,aAAG,oBAAoB,MAAM,EAAE;AAAA,QACnC,WAAU,GAAG,aAAa;AACtB,aAAG,YAAY,OAAO,MAAM,EAAE;AAAA,QAClC,OAAO;AACH,iBAAO,SAAS,MAAM,oDAAoD;AAAA,QAC9E;AAAA,MACJ;AAEA,eAAS,iBAAiB,SAAS;AAC/B,eAAO,SAAS,OAAO,EAAE,UAAU,WAAW,CAAC,EAAE,WAAW,CAAC,EAAE,WAAW,CAAC;AAAA,MAC/E;AAEA,eAAS,iBAAiB,SAAS;AAC/B,eAAO,SAAS,OAAO,EAAE,UAAU,WAAW,CAAC,EAAE,WAAW,CAAC,EAAE,WAAW,CAAC;AAAA,MAC/E;AAQA,eAAS,YAAY,SAAS,UAAU;AACpC,YAAI,YAAY,SAAS,OAAO,EAAE;AAElC,YAAI,CAAC,UAAU,MAAM;AACjB,gBAAM,IAAI,MAAM,2DAA2D;AAAA,QAC/E;AAEA,iBAAS,OAAO,EAAE,UAAU,KAAK,QAAQ;AAAA,MAC7C;AASA,eAAS,eAAeE,UAAS,SAAS,UAAU;AAChD,YAAI,CAAC,UAAU;AACX,qBAAW;AACX,oBAAUA;AACV,UAAAA,WAAU;AAAA,QACd;AAEA,QAAAA,WAAUA,YAAW,CAAC;AAEtB,iBAAS,QAAQ;AACb,cAAIA,SAAQ,OAAO;AACf,gBAAI,OAAO,MAAM,UAAU,MAAM,KAAK,SAAS;AAC/C,iBAAK,QAAQ,UAAU,IAAI,OAAO,GAAG,UAAU;AAC/C,gBAAI,SAAS,IAAI,OAAO;AACpB,uBAAS,IAAI,MAAM,MAAM,IAAI;AAAA,YACjC,OAAO;AACH,uBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,yBAAS,IAAI,KAAK,CAAC,CAAC;AAAA,cACxB;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAEA,iBAAS,WAAWC,UAAS;AACzB,mBAAS,aAAaA,UAAS;AAC3B,gBAAI,iBAAiBA,SAAQ,eAAeA,SAAQ,YAAY,EAAE,SAASA,QAAO;AAClF,mBAAOA,aAAYA,SAAQ,cAAc,QAAQA,SAAQ,cAAc,KAAK,SAASA,QAAO,KAAK;AAAA,UACrG;AAEA,cAAI,CAAC,aAAaA,QAAO,GAAG;AACxB,mBAAO;AAAA,UACX;AAGA,cAAI,OAAO,iBAAiBA,QAAO,MAAM,MAAM;AAC3C,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AAEA,iBAAS,aAAaA,UAAS;AAE3B,cAAI,YAAY,SAASA,QAAO,EAAE,UAAU,WAAW,CAAC;AACxD,cAAI,QAAQ,OAAO,iBAAiB,SAAS;AAC7C,iBAAO,CAAC,MAAM,SAAS,MAAM,MAAM,QAAQ,IAAI,MAAM;AAAA,QACzD;AAEA,iBAAS,WAAW;AAGhB,cAAI,eAA0B,OAAO,iBAAiB,OAAO;AAC7D,cAAI,QAA0B,CAAC;AAC/B,gBAAM,WAAwB,aAAa;AAC3C,gBAAM,QAAwB,QAAQ;AACtC,gBAAM,SAAwB,QAAQ;AACtC,gBAAM,MAAwB,aAAa;AAC3C,gBAAM,QAAwB,aAAa;AAC3C,gBAAM,SAAwB,aAAa;AAC3C,gBAAM,OAAwB,aAAa;AAC3C,gBAAM,WAAwB,aAAa;AAC3C,gBAAM,YAAwB,aAAa;AAC3C,iBAAO;AAAA,QACX;AAEA,iBAAS,iBAAiB;AACtB,cAAI,QAAQ,SAAS;AACrB,mBAAS,OAAO,EAAE,YAAY;AAAA,YAC1B,OAAO,MAAM;AAAA,YACb,QAAQ,MAAM;AAAA,UAClB;AACA,gBAAM,sBAAsB,SAAS,OAAO,EAAE,SAAS;AAAA,QAC3D;AAEA,iBAAS,gBAAgB;AACrB,mBAAS,OAAO,EAAE,YAAY,CAAC;AAAA,QACnC;AAEA,iBAAS,aAAa;AAClB,gBAAM,qBAAqB;AAC3B,cAAI,CAAC,SAAS,OAAO,GAAG;AACpB,kBAAM,+CAA+C;AACrD;AAAA,UACJ;AAEA,cAAI,QAAQ,SAAS;AACrB,mBAAS,OAAO,EAAE,QAAQ;AAAA,QAC9B;AAEA,iBAAS,iBAAiBA,UAAS,OAAO,QAAQ;AAC9C,mBAASA,QAAO,EAAE,YAAY;AAC9B,mBAASA,QAAO,EAAE,aAAc;AAAA,QACpC;AAEA,iBAAS,sBAAsBA,UAAS;AACpC,iBAAO,iBAAiBA,QAAO,EAAE,WAAW,CAAC;AAAA,QACjD;AAEA,iBAAS,iBAAiB;AACtB,iBAAO,IAAI,eAAe,QAAQ;AAAA,QACtC;AAEA,iBAAS,kBAAkB;AACvB,iBAAO,IAAI,eAAe,SAAS;AAAA,QACvC;AAEA,iBAAS,eAAe,OAAO;AAC3B,iBAAO,QAAQ,KAAK,eAAe;AAAA,QACvC;AAEA,iBAAS,gBAAgB,QAAQ;AAC7B,iBAAO,SAAS,KAAK,gBAAgB;AAAA,QACzC;AAEA,iBAAS,eAAe,OAAO;AAC3B,iBAAO,QAAQ,IAAI,eAAe;AAAA,QACtC;AAEA,iBAAS,gBAAgB,QAAQ;AAC7B,iBAAO,SAAS,IAAI,gBAAgB;AAAA,QACxC;AAEA,iBAAS,mBAAmBA,UAAS,OAAO,QAAQ;AAChD,cAAI,SAAkB,iBAAiBA,QAAO;AAC9C,cAAI,SAAkB,iBAAiBA,QAAO;AAC9C,cAAI,cAAkB,eAAe,KAAK;AAC1C,cAAI,eAAkB,gBAAgB,MAAM;AAC5C,cAAI,cAAkB,eAAe,KAAK;AAC1C,cAAI,eAAkB,gBAAgB,MAAM;AAC5C,iBAAO,aAAe;AACtB,iBAAO,YAAe;AACtB,iBAAO,aAAe;AACtB,iBAAO,YAAe;AAAA,QAC1B;AAEA,iBAAS,yBAAyB;AAC9B,cAAI,YAAY,SAAS,OAAO,EAAE;AAElC,cAAI,CAAC,WAAW;AACZ,wBAA8B,SAAS,cAAc,KAAK;AAC1D,sBAAU,YAAoB;AAC9B,sBAAU,MAAM,UAAc,mBAAmB,CAAC,sBAAsB,mBAAmB,cAAc,eAAe,eAAe,oBAAoB,aAAa,YAAY,CAAC;AACrL,qBAAS,OAAO,EAAE,YAAY;AAC9B,8BAAkB,SAAS;AAC3B,oBAAQ,YAAY,SAAS;AAE7B,gBAAI,mBAAmB,WAAY;AAC/B,uBAAS,OAAO,EAAE,cAAc,SAAS,OAAO,EAAE,WAAW;AAAA,YACjE;AAEA,qBAAS,WAAW,kBAAkB,gBAAgB;AAItD,qBAAS,OAAO,EAAE,mBAAmB;AAAA,UACzC;AAEA,iBAAO;AAAA,QACX;AAEA,iBAAS,uBAAuB;AAC5B,mBAAS,sBAAsB;AAC3B,gBAAI,QAAQ,SAAS,OAAO,EAAE;AAE9B,gBAAG,MAAM,aAAa,UAAU;AAC5B,sBAAQ,MAAM,YAAY,YAAY,YAAWD,SAAQ,YAAY,cAAc,EAAE;AAErF,kBAAI,uBAAuB,SAASE,WAAUD,UAASF,QAAO,UAAU;AACpE,yBAAS,kBAAkBI,QAAO;AAC9B,yBAAOA,OAAM,QAAQ,aAAa,EAAE;AAAA,gBACxC;AAEA,oBAAI,QAAQJ,OAAM,QAAQ;AAE1B,oBAAG,UAAU,UAAU,kBAAkB,KAAK,MAAM,KAAK;AACrD,kBAAAG,UAAS,KAAK,oDAAoD,WAAW,MAAM,QAAQ,oHAAoH,WAAW,gCAAgCD,QAAO;AACjQ,kBAAAA,SAAQ,MAAM,QAAQ,IAAI;AAAA,gBAC9B;AAAA,cACJ;AAIA,mCAAqB,UAAU,SAAS,OAAO,KAAK;AACpD,mCAAqB,UAAU,SAAS,OAAO,OAAO;AACtD,mCAAqB,UAAU,SAAS,OAAO,QAAQ;AACvD,mCAAqB,UAAU,SAAS,OAAO,MAAM;AAAA,YACzD;AAAA,UACJ;AAEA,mBAAS,6BAA6B,MAAM,KAAK,QAAQ,OAAO;AAC5D,mBAAQ,CAAC,OAAO,MAAO,OAAO;AAC9B,kBAAO,CAAC,MAAM,MAAO,MAAM;AAC3B,qBAAU,CAAC,SAAS,MAAO,SAAS;AACpC,oBAAS,CAAC,QAAQ,MAAO,QAAQ;AAEjC,mBAAO,CAAC,WAAW,MAAM,UAAU,KAAK,YAAY,OAAO,aAAa,MAAM;AAAA,UAClF;AAEA,gBAAM,oBAAoB;AAE1B,cAAI,CAAC,SAAS,OAAO,GAAG;AACpB,kBAAM,+CAA+C;AACrD;AAAA,UACJ;AAEA,8BAAoB;AAEpB,cAAI,gBAAgB,SAAS,OAAO,EAAE;AAEtC,cAAI,CAAC,eAAe;AAChB,4BAAgB,uBAAuB;AAAA,UAC3C;AAUA,cAAI,iBAA0B,eAAe;AAC7C,cAAI,kBAA0B,eAAe;AAC7C,cAAI,0BAA0B,mBAAmB,CAAC,sBAAsB,cAAc,oBAAoB,eAAe,sBAAsB,eAAe,gBAAgB,aAAa,UAAU,CAAC;AACtM,cAAI,iBAA0B,mBAAmB,CAAC,sBAAsB,cAAc,oBAAoB,eAAe,oBAAoB,EAAE,OAAO,6BAA6B,EAAE,IAAI,iBAAiB,EAAE,IAAI,kBAAkB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;AACrQ,cAAI,cAA0B,mBAAmB,CAAC,sBAAsB,cAAc,oBAAoB,eAAe,sBAAsB,eAAe,cAAc,CAAC;AAC7K,cAAI,cAA0B,mBAAmB,CAAC,sBAAsB,cAAc,oBAAoB,eAAe,sBAAsB,eAAe,cAAc,CAAC;AAC7K,cAAI,mBAA0B,mBAAmB,CAAC,sBAAsB,WAAW,QAAQ,CAAC;AAC5F,cAAI,mBAA0B,mBAAmB,CAAC,sBAAsB,eAAe,cAAc,CAAC;AAEtG,cAAI,qBAA0B,SAAS,cAAc,KAAK;AAC1D,cAAI,YAA0B,SAAS,cAAc,KAAK;AAC1D,cAAI,SAA0B,SAAS,cAAc,KAAK;AAC1D,cAAI,cAA0B,SAAS,cAAc,KAAK;AAC1D,cAAI,SAA0B,SAAS,cAAc,KAAK;AAC1D,cAAI,cAA0B,SAAS,cAAc,KAAK;AAI1D,6BAAmB,MAAmB;AAEtC,6BAAmB,MAAM,UAAa;AACtC,6BAAmB,YAAmB;AACtC,oBAAU,YAA4B;AACtC,oBAAU,MAAM,UAAsB;AACtC,iBAAO,MAAM,UAAyB;AACtC,sBAAY,MAAM,UAAoB;AACtC,iBAAO,MAAM,UAAyB;AACtC,sBAAY,MAAM,UAAoB;AAEtC,iBAAO,YAAY,WAAW;AAC9B,iBAAO,YAAY,WAAW;AAC9B,oBAAU,YAAY,MAAM;AAC5B,oBAAU,YAAY,MAAM;AAC5B,6BAAmB,YAAY,SAAS;AACxC,wBAAc,YAAY,kBAAkB;AAE5C,mBAAS,iBAAiB;AACtB,gBAAI,QAAQ,SAAS,OAAO;AAC5B,gBAAI,SAAS,MAAM,UAAU;AACzB,oBAAM,SAAS;AAAA,YACnB,OAAO;AACH,oBAAM,8DAA8D;AAAA,YACxE;AAAA,UACJ;AAEA,mBAAS,iBAAiB;AACtB,gBAAI,QAAQ,SAAS,OAAO;AAC5B,gBAAI,SAAS,MAAM,UAAU;AACzB,oBAAM,SAAS;AAAA,YACnB,OAAO;AACH,oBAAM,8DAA8D;AAAA,YACxE;AAAA,UACJ;AAEA,mBAAS,QAAQ,UAAU,cAAc;AACzC,mBAAS,QAAQ,UAAU,cAAc;AAIzC,mBAAS,OAAO,EAAE,iBAAiB;AACnC,mBAAS,OAAO,EAAE,iBAAiB;AAAA,QACvC;AAEA,iBAAS,uCAAuC;AAC5C,mBAAS,iBAAiBA,UAAS,OAAO,QAAQ;AAC9C,gBAAI,cAA0B,sBAAsBA,QAAO;AAC3D,gBAAI,cAA0B,eAAe,KAAK;AAClD,gBAAI,eAA0B,gBAAgB,MAAM;AACpD,wBAAY,MAAM,YAAY,SAAS,cAAc,MAAMD,SAAQ,YAAY,cAAc,EAAE;AAC/F,wBAAY,MAAM,YAAY,UAAU,eAAe,MAAMA,SAAQ,YAAY,cAAc,EAAE;AAAA,UACrG;AAEA,mBAAS,uBAAuB,MAAM;AAClC,gBAAI,QAAkB,QAAQ;AAC9B,gBAAI,SAAkB,QAAQ;AAG9B,gBAAI,cAAc,UAAU,SAAS,OAAO,EAAE,aAAa,WAAW,SAAS,OAAO,EAAE;AAExF,kBAAM,wBAAwB,OAAO,MAAM;AAI3C,6BAAiB,SAAS,OAAO,MAAM;AAKvC,2BAAe,IAAI,GAAG,SAAS,0BAA0B;AACrD,kBAAI,CAAC,aAAa;AACd;AAAA,cACJ;AAEA,kBAAI,CAAC,SAAS,OAAO,GAAG;AACpB,sBAAM,+CAA+C;AACrD;AAAA,cACJ;AAEA,kBAAI,CAAC,oBAAoB,GAAG;AACxB,sBAAM,6DAA6D;AACnE;AAAA,cACJ;AAEA,kBAAIA,SAAQ,OAAO;AACf,oBAAI,IAAI,QAAQ;AAChB,oBAAI,IAAI,QAAQ;AAEhB,oBAAI,MAAM,SAAS,MAAM,QAAQ;AAC7B,2BAAS,KAAK,UAAU,IAAI,OAAO,GAAG,yDAAyD;AAAA,gBACnG;AAAA,cACJ;AAEA,+BAAiB,SAAS,OAAO,MAAM;AAAA,YAC3C,CAAC;AAED,2BAAe,IAAI,GAAG,SAAS,mBAAmB;AAI9C,kBAAI,CAAC,SAAS,OAAO,GAAG;AACpB,sBAAM,+CAA+C;AACrD;AAAA,cACJ;AAEA,kBAAI,CAAC,oBAAoB,GAAG;AACxB,sBAAM,6DAA6D;AACnE;AAAA,cACJ;AAEA,iCAAmB,SAAS,OAAO,MAAM;AAAA,YAC7C,CAAC;AAED,gBAAI,eAAe,MAAM;AACrB,6BAAe,IAAI,GAAG,WAAY;AAC9B,oBAAI,CAAC,SAAS,OAAO,GAAG;AACpB,wBAAM,+CAA+C;AACrD;AAAA,gBACJ;AAEA,oBAAI,CAAC,oBAAoB,GAAG;AAC1B,wBAAM,6DAA6D;AACnE;AAAA,gBACF;AAEA,qBAAK;AAAA,cACT,CAAC;AAAA,YACL;AAAA,UACJ;AAEA,mBAAS,sBAAsB;AAC3B,mBAAO,CAAC,CAAC,SAAS,OAAO,EAAE;AAAA,UAC/B;AAEA,mBAAS,0BAA0B;AAC/B,qBAAS,gBAAgB;AACrB,qBAAO,SAAS,OAAO,EAAE,sBAAsB;AAAA,YACnD;AAEA,kBAAM,iCAAiC;AAEvC,gBAAI,QAAQ,SAAS,OAAO;AAG5B,gBAAI,cAAc,KAAK,MAAM,cAAc,MAAM,UAAU,SAAS,MAAM,eAAe,MAAM,UAAU,QAAQ;AAC7G,qBAAO,MAAM,4FAA4F;AAAA,YAC7G;AAGA,gBAAI,MAAM,cAAc,MAAM,qBAAqB,MAAM,eAAe,MAAM,oBAAoB;AAC9F,qBAAO,MAAM,sCAAsC;AAAA,YACvD;AAGA,kBAAM,yCAAyC;AAC/C,kBAAM,oBAAoB,MAAM;AAChC,kBAAM,qBAAqB,MAAM;AACjC,oBAAQ,SAAS,OAAO,EAAE,WAAW,SAAU,UAAU;AACrD,uBAAS,OAAO;AAAA,YACpB,CAAC;AAAA,UACL;AAEA,mBAAS,eAAe;AACpB,kBAAM,2BAA2B;AAEjC,gBAAI,aAAa,OAAO,GAAG;AACvB,oBAAM,+CAA+C;AACrD;AAAA,YACJ;AAEA,kBAAM,mBAAmB;AACzB,gBAAI,SAAS,iBAAiB,OAAO;AACrC,gBAAI,SAAS,iBAAiB,OAAO;AACrC,gBAAI,OAAO,eAAe,KAAK,OAAO,cAAc,KAAK,OAAO,eAAe,KAAK,OAAO,cAAc,GAAG;AACxG,oBAAM,uDAAuD;AAC7D,qCAAuB,uBAAuB;AAAA,YAClD;AAAA,UACJ;AAEA,mBAAS,eAAe;AACpB,kBAAM,kBAAkB;AAExB,gBAAI,aAAa,OAAO,GAAG;AAEvB,oBAAM,kDAAkD;AACxD;AAAA,YACJ;AAEA,mCAAuB,uBAAuB;AAAA,UAClD;AAEA,gBAAM,+CAA+C;AAErD,cAAI,CAAC,SAAS,OAAO,GAAG;AACpB,kBAAM,+CAA+C;AACrD;AAAA,UACJ;AAEA,mBAAS,OAAO,EAAE,aAAa;AAC/B,mBAAS,OAAO,EAAE,WAAW;AAC7B,mBAAS,OAAO,EAAE,WAAW;AAE7B,cAAI,QAAQ,SAAS,OAAO,EAAE;AAC9B,2BAAiB,SAAS,MAAM,OAAO,MAAM,MAAM;AAAA,QACvD;AAEA,iBAAS,sBAAsB;AAC3B,gBAAM,8BAA8B;AAEpC,cAAI,CAAC,SAAS,OAAO,GAAG;AACpB,kBAAM,+CAA+C;AACrD;AAAA,UACJ;AAEA,cAAI,QAAQ,SAAS,OAAO,EAAE;AAC9B,2BAAiB,SAAS,MAAM,OAAO,MAAM,MAAM;AACnD,6BAAmB,SAAS,MAAM,OAAO,MAAM,MAAM;AAAA,QACzD;AAEA,iBAAS,QAAQ;AACb,mBAAS,OAAO;AAAA,QACpB;AAEA,iBAAS,UAAU;AACf,gBAAM,eAAe;AACrB,wBAAc;AACd,yBAAe;AAEf,yBAAe,IAAI,GAAG,UAAU;AAChC,yBAAe,IAAI,GAAG,oBAAoB;AAC1C,yBAAe,IAAI,GAAG,oCAAoC;AAC1D,yBAAe,IAAI,GAAG,mBAAmB;AACzC,yBAAe,IAAI,GAAG,KAAK;AAAA,QAC/B;AAEA,cAAM,sBAAsB;AAE5B,YAAI,WAAW,OAAO,GAAG;AACrB,gBAAM,qBAAqB;AAE3B,iCAAuB;AAEvB,gBAAM,sCAAsC;AAE5C,mBAAS,OAAO,EAAE,aAAa,WAAY;AACvC,kBAAM,yBAAyB;AAC/B,oBAAQ;AAAA,UACZ;AAAA,QACJ,OAAO;AACH,kBAAQ;AAAA,QACZ;AAAA,MACJ;AAEA,eAAS,UAAU,SAAS;AACxB,YAAI,QAAQ,SAAS,OAAO;AAE5B,YAAI,CAAC,OAAO;AAER;AAAA,QACJ;AASA,cAAM,kBAAkB,YAAY,iBAAiB,OAAO,GAAG,UAAU,MAAM,cAAc;AAC7F,cAAM,kBAAkB,YAAY,iBAAiB,OAAO,GAAG,UAAU,MAAM,cAAc;AAC7F,cAAM,oBAAoB,YAAY,MAAM,WAAW,kBAAkB,MAAM,gBAAgB;AAE/F,cAAM,aAAa,QAAQ,YAAY,MAAM,SAAS;AAAA,MAC1D;AAEA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;AC/pBA;AAAA;AAEA,QAAI,UAA0B,2BAA8B;AAC5D,QAAI,oBAA0B;AAC9B,QAAI,uBAA0B;AAC9B,QAAI,mBAA0B;AAC9B,QAAI,iBAA0B;AAC9B,QAAI,gBAA0B;AAC9B,QAAI,kBAA0B;AAC9B,QAAI,sBAA0B;AAC9B,QAAI,eAA0B;AAG9B,QAAI,sBAA0B;AAC9B,QAAI,sBAA0B;AAE9B,aAAS,aAAa,KAAK;AACvB,aAAO,MAAM,QAAQ,GAAG,KAAK,IAAI,WAAW;AAAA,IAChD;AAEA,aAAS,QAAQ,YAAY;AACzB,UAAI,CAAC,MAAM,QAAQ,UAAU,GAAG;AAC5B,YAAI,QAAQ,CAAC;AACb,gBAAQ,YAAY,SAAU,KAAK;AAC/B,gBAAM,KAAK,GAAG;AAAA,QAClB,CAAC;AACD,eAAO;AAAA,MACX,OAAO;AACH,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,aAAS,UAAU,KAAK;AACpB,aAAO,OAAO,IAAI,aAAa;AAAA,IACnC;AA4BA,WAAO,UAAU,SAAS,SAAS;AAC/B,gBAAU,WAAW,CAAC;AAGtB,UAAI;AAEJ,UAAI,QAAQ,WAAW;AAGnB,oBAAY;AAAA,UACR,KAAK,SAAU,SAAS;AAAE,mBAAO,QAAQ,UAAU,IAAI,SAAS,IAAI;AAAA,UAAG;AAAA,UACvE,KAAK,QAAQ,UAAU;AAAA,QAC3B;AAAA,MACJ,OAAO;AACH,YAAI,cAAc,iBAAiB;AACnC,YAAI,mBAAmB,eAAe;AAAA,UAClC;AAAA,UACA;AAAA,QACJ,CAAC;AACD,oBAAY;AAAA,MAChB;AAGA,UAAI,WAAW,QAAQ;AAEvB,UAAG,CAAC,UAAU;AAEV,YAAI,QAAQ,aAAa;AACzB,mBAAW,cAAc,KAAK;AAAA,MAClC;AAGA,UAAI,iBAAiB,UAAU,SAAS,kBAAkB,oBAAoB,EAAE,SAAmB,CAAC,CAAC;AAGrG,UAAI,gBAAgB,CAAC;AACrB,oBAAc,YAAgB,CAAC,CAAC,UAAU,SAAS,aAAa,IAAI;AACpE,oBAAc,QAAgB,CAAC,CAAC,UAAU,SAAS,SAAS,KAAK;AAEjE,UAAI,uBAA0B,qBAAqB,SAAS;AAC5D,UAAI,eAA0B,kBAAkB;AAAA,QAC5C;AAAA,MACJ,CAAC;AAGD,UAAI;AACJ,UAAI,kBAAkB,UAAU,SAAS,YAAY,QAAQ;AAC7D,UAAI,oBAAoB,UAAU,SAAS,aAAa,KAAK;AAC7D,UAAI,kBAAkB;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACf;AAEA,UAAG,oBAAoB,UAAU;AAC7B,YAAI,gBAAgB,cAAc,GAAG;AACjC,mBAAS,KAAK,gFAAgF;AAC9F,4BAAkB;AAAA,QACtB,WAAW,gBAAgB,KAAK,CAAC,GAAG;AAChC,mBAAS,KAAK,uEAAuE;AACrF,4BAAkB;AAAA,QACtB;AAAA,MACJ;AAEA,UAAG,oBAAoB,UAAU;AAC7B,4BAAoB,oBAAoB,eAAe;AAAA,MAC3D,WAAU,oBAAoB,UAAU;AACpC,4BAAoB,oBAAoB,eAAe;AAAA,MAC3D,OAAO;AACH,cAAM,IAAI,MAAM,4BAA4B,eAAe;AAAA,MAC/D;AAOA,UAAI,mBAAmB,CAAC;AASxB,eAAS,SAASI,UAAS,UAAU,UAAU;AAC3C,iBAAS,iBAAiB,SAAS;AAC/B,cAAI,YAAY,qBAAqB,IAAI,OAAO;AAChD,kBAAQ,WAAW,SAAS,kBAAkBC,WAAU;AACpD,YAAAA,UAAS,OAAO;AAAA,UACpB,CAAC;AAAA,QACL;AAEA,iBAAS,YAAYC,YAAW,SAASD,WAAU;AAC/C,+BAAqB,IAAI,SAASA,SAAQ;AAE1C,cAAGC,YAAW;AACV,YAAAD,UAAS,OAAO;AAAA,UACpB;AAAA,QACJ;AAGA,YAAG,CAAC,UAAU;AACV,qBAAW;AACX,qBAAWD;AACX,UAAAA,WAAU,CAAC;AAAA,QACf;AAEA,YAAG,CAAC,UAAU;AACV,gBAAM,IAAI,MAAM,gCAAgC;AAAA,QACpD;AAEA,YAAG,CAAC,UAAU;AACV,gBAAM,IAAI,MAAM,oBAAoB;AAAA,QACxC;AAEA,YAAI,UAAU,QAAQ,GAAG;AAErB,qBAAW,CAAC,QAAQ;AAAA,QACxB,WAAW,aAAa,QAAQ,GAAG;AAG/B,qBAAW,QAAQ,QAAQ;AAAA,QAC/B,OAAO;AACH,iBAAO,SAAS,MAAM,2EAA2E;AAAA,QACrG;AAEA,YAAI,gBAAgB;AAEpB,YAAI,YAAY,UAAUA,UAAS,aAAa,cAAc,SAAS;AACvE,YAAI,kBAAkB,UAAUA,UAAS,WAAW,SAAS,OAAO;AAAA,QAAC,CAAC;AACtE,YAAI,QAAQ,UAAUA,UAAS,SAAS,cAAc,KAAK;AAE3D,gBAAQ,UAAU,SAAS,wBAAwB,SAAS;AACxD,cAAI,CAAC,aAAa,SAAS,OAAO,GAAG;AACjC,yBAAa,UAAU,OAAO;AAC9B,sBAAU,IAAI,OAAO;AAAA,UACzB;AAEA,cAAI,KAAK,UAAU,IAAI,OAAO;AAE9B,mBAAS,SAAS,IAAI,iCAAiC,IAAI,OAAO;AAElE,cAAG,CAAC,aAAa,aAAa,OAAO,GAAG;AACpC,qBAAS,SAAS,IAAI,IAAI,iBAAiB;AAC3C,gBAAG,aAAa,OAAO,OAAO,GAAG;AAC7B,uBAAS,SAAS,IAAI,IAAI,kCAAkC;AAI5D,0BAAY,WAAW,SAAS,QAAQ;AACxC,+BAAiB,EAAE,IAAI,iBAAiB,EAAE,KAAK,CAAC;AAChD,+BAAiB,EAAE,EAAE,KAAK,SAAS,UAAU;AACzC;AAEA,oBAAG,kBAAkB,SAAS,QAAQ;AAClC,kCAAgB;AAAA,gBACpB;AAAA,cACJ,CAAC;AACD;AAAA,YACJ;AAEA,qBAAS,SAAS,IAAI,IAAI,sBAAsB;AAEhD,yBAAa,SAAS,SAAS,IAAI;AACnC,mBAAO,kBAAkB,eAAe,EAAE,OAAc,WAAW,kBAAkB,GAAG,SAAS,SAAS,oBAAoBG,UAAS;AACnI,uBAAS,SAAS,IAAI,IAAI,qBAAqB;AAE/C,kBAAI,aAAa,SAASA,QAAO,GAAG;AAChC,6BAAa,iBAAiBA,QAAO;AACrC,6BAAa,SAASA,UAAS,KAAK;AACpC,kCAAkB,YAAYA,UAAS,gBAAgB;AACvD,4BAAY,WAAWA,UAAS,QAAQ;AAMxC,oBAAI,QAAQ,aAAa,SAASA,QAAO;AACzC,oBAAI,SAAS,MAAM,WAAW;AAC1B,sBAAI,QAAQA,SAAQ;AACpB,sBAAI,SAASA,SAAQ;AACrB,sBAAI,MAAM,UAAU,UAAU,SAAS,MAAM,UAAU,WAAW,QAAQ;AACtE,qCAAiBA,QAAO;AAAA,kBAC5B;AAAA,gBACJ;AAEA,oBAAG,iBAAiB,EAAE,GAAG;AACrB,0BAAQ,iBAAiB,EAAE,GAAG,SAAS,UAAU;AAC7C,6BAAS;AAAA,kBACb,CAAC;AAAA,gBACL;AAAA,cACJ,OAAO;AAEH,yBAAS,SAAS,IAAI,IAAI,8CAA8C;AAAA,cAC5E;AAEA,qBAAO,iBAAiB,EAAE;AAE1B;AACA,kBAAG,kBAAkB,SAAS,QAAQ;AAClC,gCAAgB;AAAA,cACpB;AAAA,YACJ,CAAC;AAAA,UACL;AAEA,mBAAS,SAAS,IAAI,IAAI,qCAAqC;AAG/D,sBAAY,WAAW,SAAS,QAAQ;AACxC;AAAA,QACJ,CAAC;AAED,YAAG,kBAAkB,SAAS,QAAQ;AAClC,0BAAgB;AAAA,QACpB;AAAA,MACJ;AAEA,eAAS,UAAU,UAAU;AACzB,YAAG,CAAC,UAAU;AACV,iBAAO,SAAS,MAAM,mCAAmC;AAAA,QAC7D;AAEA,YAAI,UAAU,QAAQ,GAAG;AAErB,qBAAW,CAAC,QAAQ;AAAA,QACxB,WAAW,aAAa,QAAQ,GAAG;AAG/B,qBAAW,QAAQ,QAAQ;AAAA,QAC/B,OAAO;AACH,iBAAO,SAAS,MAAM,2EAA2E;AAAA,QACrG;AAEA,gBAAQ,UAAU,SAAU,SAAS;AACjC,+BAAqB,mBAAmB,OAAO;AAC/C,4BAAkB,UAAU,OAAO;AACnC,uBAAa,WAAW,OAAO;AAAA,QACnC,CAAC;AAAA,MACL;AAEA,eAAS,aAAa,gBAAgB;AAClC,0BAAkB,gBAAgB,kBAAkB,aAAa,cAAc;AAAA,MACnF;AAEA,aAAO;AAAA,QACH;AAAA,QACA,gBAAgB,qBAAqB;AAAA,QACrC,oBAAoB,qBAAqB;AAAA,QACzC;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAEA,aAAS,UAAU,SAAS,MAAM,cAAc;AAC5C,UAAI,QAAQ,QAAQ,IAAI;AAExB,WAAI,UAAU,UAAa,UAAU,SAAS,iBAAiB,QAAW;AACtE,eAAO;AAAA,MACX;AAEA,aAAO;AAAA,IACX;AAAA;AAAA;", "names": ["reporter", "topLevel", "bottomLevel", "options", "element", "callback", "reporter", "style", "value", "styleId", "style", "options", "element", "reporter", "value", "options", "listener", "callOnAdd", "element"]}