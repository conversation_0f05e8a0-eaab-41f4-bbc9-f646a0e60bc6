import {
  F,
  I,
  N,
  S,
  T,
  j,
  k,
  v,
  x as x3
} from "./chunk-EVNRYDNL.js";
import "./chunk-BM3BRFSV.js";
import "./chunk-D7BTYVTV.js";
import "./chunk-JLELSJK5.js";
import "./chunk-DBDYNPQT.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import {
  c as c3
} from "./chunk-LNCHRZJI.js";
import {
  n as n3,
  p as p4
} from "./chunk-O3LPRA7A.js";
import "./chunk-M5RPNIHK.js";
import "./chunk-KYTIKHPN.js";
import {
  s as s2
} from "./chunk-Y7OJSY6H.js";
import {
  i as i2
} from "./chunk-RR74IWZB.js";
import {
  n as n4
} from "./chunk-FWXA4I6D.js";
import "./chunk-NQ3OACUM.js";
import {
  i as i3
} from "./chunk-7UNBPRRZ.js";
import {
  o as o2
} from "./chunk-OQK7L3JR.js";
import {
  p as p5
} from "./chunk-5BWF7URZ.js";
import "./chunk-D3MAF4VS.js";
import {
  a as a2
} from "./chunk-ND4JUK42.js";
import "./chunk-2WMCP27R.js";
import {
  p as p2
} from "./chunk-JV6TBH5W.js";
import {
  _
} from "./chunk-TV6WKLTU.js";
import "./chunk-5VDORDNW.js";
import {
  c as c2
} from "./chunk-FHKOFAQ2.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import "./chunk-3HW44BD3.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-P37TUI4J.js";
import {
  n
} from "./chunk-LAEW33J6.js";
import {
  t as t2
} from "./chunk-NGPCXWDX.js";
import "./chunk-IXMTA2RC.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-67MHB3E3.js";
import {
  x
} from "./chunk-USWRDFDJ.js";
import "./chunk-XM4RHPJM.js";
import "./chunk-6T5FEO66.js";
import "./chunk-MKG56XBH.js";
import "./chunk-FZ7BG3VX.js";
import {
  x as x2
} from "./chunk-N4YJNWPS.js";
import {
  y as y2
} from "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-45UG5A2F.js";
import "./chunk-ORU3OGKZ.js";
import {
  n as n2,
  p as p3
} from "./chunk-FCQRDLBQ.js";
import "./chunk-NUZU7NCS.js";
import "./chunk-LHJ7PAFO.js";
import "./chunk-7MQMIP4J.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import {
  C
} from "./chunk-FRJZOKDL.js";
import "./chunk-DKEAXJKM.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import {
  c,
  d,
  f as f2,
  l,
  m as m2,
  p
} from "./chunk-YJWWP4AU.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-EQYLMJEJ.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-3TXAWGPY.js";
import "./chunk-5EGPPD3R.js";
import "./chunk-L66EKMJP.js";
import "./chunk-3M3FTH72.js";
import "./chunk-XLHYMGQY.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-52L4L36S.js";
import "./chunk-VTOVDEW2.js";
import "./chunk-UKESBP6X.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-TD7BKFJX.js";
import {
  k as k2
} from "./chunk-LNGMNZ4I.js";
import "./chunk-24NZLSKM.js";
import "./chunk-BNFBWJYJ.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-23VNIYCK.js";
import "./chunk-AVKOL7OR.js";
import {
  F as F2,
  x as x4
} from "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-OVIRLBJH.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import {
  m
} from "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import {
  i,
  o
} from "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-4YKNTCHK.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-CGBA4LNQ.js";
import "./chunk-2RZLOKYO.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-HHMRK4FX.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/graphics/sources/OGCFeatureSource.js
var C2 = class extends m {
  constructor() {
    super(...arguments), this.featureDefinition = null, this.type = "ogc-feature";
  }
  load(e2) {
    return this.addResolvingPromise(this._loadOGCServices(this.layer, e2)), this.when();
  }
  getSource() {
    const { featureDefinition: { collection: e2, layerDefinition: t3, spatialReference: s3, supportedCrs: r2 }, layer: { apiKey: o3, customParameters: p6, effectiveMaxRecordCount: i4 } } = this;
    return { type: "ogc-source", collection: e2, layerDefinition: t3, maxRecordCount: i4, queryParameters: { apiKey: o3, customParameters: p6 }, spatialReference: s3, supportedCrs: r2 };
  }
  queryExtent(e2, t3 = {}) {
    return null;
  }
  queryFeatureCount(e2, t3 = {}) {
    return null;
  }
  queryFeatures(e2, t3 = {}) {
    return this.queryFeaturesJSON(e2, t3).then((e3) => x2.fromJSON(e3));
  }
  queryFeaturesJSON(e2, t3 = {}) {
    const s3 = this.getSource();
    return this.load(t3).then(() => N(s3, e2, t3));
  }
  queryObjectIds(e2, t3 = {}) {
    return null;
  }
  serviceSupportsSpatialReference(e2) {
    return !(!e2.isWGS84 && !e2.isWebMercator) || !!this.featureDefinition.supportedCrs[e2.wkid];
  }
  _conformsToType(e2, t3) {
    const s3 = new RegExp(`^${t3}$`, "i");
    return e2.conformsTo.some((e3) => s3.test(e3)) ?? false;
  }
  _getCapabilities(e2, t3) {
    return { analytics: { supportsCacheHint: false }, attachment: null, data: { isVersioned: false, supportsAttachment: false, supportsM: false, supportsZ: e2 }, metadata: { supportsAdvancedFieldProperties: false }, operations: { supportsCalculate: false, supportsTruncate: false, supportsValidateSql: false, supportsAdd: false, supportsDelete: false, supportsEditing: false, supportsChangeTracking: false, supportsQuery: false, supportsQueryAnalytics: false, supportsQueryAttachments: false, supportsQueryTopFeatures: false, supportsResizeAttachments: false, supportsSync: false, supportsUpdate: false, supportsExceedsLimitStatistics: false }, query: { maxRecordCount: t3, maxRecordCountFactor: void 0, standardMaxRecordCount: void 0, supportsCacheHint: false, supportsCentroid: false, supportsDisjointSpatialRelationship: false, supportsDistance: false, supportsDistinct: false, supportsExtent: false, supportsFormatPBF: false, supportsGeometryProperties: false, supportsHavingClause: false, supportsHistoricMoment: false, supportsMaxRecordCountFactor: false, supportsOrderBy: false, supportsPagination: false, supportsPercentileStatistics: false, supportsQuantization: false, supportsQuantizationEditMode: false, supportsQueryByOthers: false, supportsQueryGeometry: false, supportsResultType: false, supportsStandardizedQueriesOnly: false, supportsTopFeaturesQuery: false, supportsStatistics: false, supportsSpatialAggregationStatistics: false, supportedSpatialAggregationStatistics: { envelope: false, centroid: false, convexHull: false }, supportsDefaultSpatialReference: false, supportsFullTextSearch: false, supportsCompactGeometry: false, supportsSqlExpression: false, tileMaxRecordCount: void 0 }, queryRelated: { supportsCount: false, supportsOrderBy: false, supportsPagination: false, supportsCacheHint: false }, queryTopFeatures: { supportsCacheHint: false }, editing: { supportsDeleteByAnonymous: false, supportsDeleteByOthers: false, supportsGeometryUpdate: false, supportsGlobalId: false, supportsReturnServiceEditsInSourceSpatialReference: false, supportsRollbackOnFailure: false, supportsUpdateByAnonymous: false, supportsUpdateByOthers: false, supportsUploadWithItemId: false, supportsUpdateWithoutM: false } };
  }
  _getMaxRecordCount(e2) {
    var _a, _b, _c, _d, _e;
    const t3 = (_a = e2 == null ? void 0 : e2.components) == null ? void 0 : _a.parameters;
    return ((_c = (_b = t3 == null ? void 0 : t3.limit) == null ? void 0 : _b.schema) == null ? void 0 : _c.maximum) ?? ((_e = (_d = t3 == null ? void 0 : t3.limitFeatures) == null ? void 0 : _d.schema) == null ? void 0 : _e.maximum);
  }
  _getStorageSpatialReference(e2) {
    const t3 = e2.storageCrs ?? F, s3 = v(t3);
    return t(s3) ? f.WGS84 : new f({ wkid: s3 });
  }
  _getSupportedSpatialReferences(e2, t3) {
    const s3 = "#/crs", r2 = e2.crs ?? [F], o3 = r2.includes(s3) ? r2.filter((e3) => e3 !== s3).concat(t3.crs ?? []) : r2, p6 = /^http:\/\/www\.opengis.net\/def\/crs\/epsg\/.*\/3785$/i;
    return o3.filter((e3) => !p6.test(e3));
  }
  async _loadOGCServices(e2, s3) {
    const r2 = r(s3) ? s3.signal : null, { apiKey: p6, collectionId: i4, customParameters: a3, fields: u, geometryType: g, hasZ: h, objectIdField: C3, timeInfo: R, url: w2 } = e2, x5 = { fields: u == null ? void 0 : u.map((e3) => e3.toJSON()), geometryType: i.toJSON(g), hasZ: h ?? false, objectIdField: C3, timeInfo: R == null ? void 0 : R.toJSON() }, j2 = { apiKey: p6, customParameters: a3, signal: r2 }, F3 = await x3(w2, j2), [O2, v2] = await Promise.all([k(F3, j2), T(F3, j2)]);
    if (!this._conformsToType(O2, "http://www.opengis.net/spec/ogcapi-features-1/1.0/conf/geojson")) throw new s("ogc-feature-layer:no-geojson-support", "Server does not support geojson");
    const T2 = v2.collections.find((e3) => e3.id === i4);
    if (!T2) throw new s("ogc-feature-layer:collection-not-found", "Server does not contain the named collection");
    const D = this._conformsToType(O2, "http://www.opengis.net/spec/ogcapi-features-1/1.0/conf/oas30") ? await S(F3, j2) : null, _2 = await I(T2, x5, j2), b2 = this._getMaxRecordCount(D), P = this._getCapabilities(_2.hasZ, b2), q = this._getStorageSpatialReference(T2).toJSON(), M2 = this._getSupportedSpatialReferences(T2, v2), A = new RegExp(`^${j}`, "i"), E = {};
    for (const t3 of M2) {
      const e3 = v(t3);
      r(e3) && (E[e3] || (E[e3] = t3.replace(A, "")));
    }
    this.featureDefinition = { capabilities: P, collection: T2, layerDefinition: _2, spatialReference: q, supportedCrs: E };
  }
};
e([y()], C2.prototype, "featureDefinition", void 0), e([y({ constructOnly: true })], C2.prototype, "layer", void 0), e([y()], C2.prototype, "type", void 0), C2 = e([a("esri.layers.graphics.sources.OGCFeatureSource")], C2);

// node_modules/@arcgis/core/layers/OGCFeatureLayer.js
var M = s2();
var B = class extends i2(o2(n3(p4(n(c3(a2(t2(c2(_(p2(O(b)))))))))))) {
  constructor(e2) {
    super(e2), this.capabilities = null, this.collectionId = null, this.copyright = null, this.definitionExpression = null, this.description = null, this.displayField = null, this.elevationInfo = null, this.fields = null, this.fieldsIndex = null, this.fullExtent = null, this.geometryType = null, this.hasZ = void 0, this.labelingInfo = null, this.labelsVisible = true, this.legendEnabled = true, this.maxRecordCount = null, this.objectIdField = null, this.operationalLayerType = "OGCFeatureLayer", this.popupEnabled = true, this.popupTemplate = null, this.screenSizePerspectiveEnabled = true, this.source = new C2({ layer: this }), this.spatialReference = null, this.title = null, this.type = "ogc-feature", this.typeIdField = null, this.types = null, this.url = null;
  }
  destroy() {
    var _a;
    (_a = this.source) == null ? void 0 : _a.destroy();
  }
  load(e2) {
    return this.addResolvingPromise(this.loadFromPortal({ supportedTypes: ["OGCFeatureServer"] }, e2).then(() => this._fetchService(e2))), this.when();
  }
  get defaultPopupTemplate() {
    return this.createPopupTemplate();
  }
  get effectiveMaxRecordCount() {
    var _a;
    return this.maxRecordCount ?? ((_a = this.capabilities) == null ? void 0 : _a.query.maxRecordCount) ?? 5e3;
  }
  get isTable() {
    return this.loaded && null == this.geometryType;
  }
  set renderer(e2) {
    F2(e2, this.fieldsIndex), this._set("renderer", e2);
  }
  on(e2, r2) {
    return super.on(e2, r2);
  }
  createPopupTemplate(e2) {
    return p5(this, e2);
  }
  createQuery() {
    return new x();
  }
  getField(e2) {
    return this.fieldsIndex.get(e2);
  }
  getFieldDomain(e2, r2) {
    var _a;
    let t3, i4 = false;
    const o3 = (_a = r2 == null ? void 0 : r2.feature) == null ? void 0 : _a.attributes, s3 = this.typeIdField && (o3 == null ? void 0 : o3[this.typeIdField]);
    return null != s3 && this.types && (i4 = this.types.some((r3) => {
      var _a2;
      return r3.id == s3 && (t3 = (_a2 = r3.domains) == null ? void 0 : _a2[e2], "inherited" === (t3 == null ? void 0 : t3.type) && (t3 = this._getLayerDomain(e2)), true);
    })), i4 || t3 || (t3 = this._getLayerDomain(e2)), t3;
  }
  queryFeatures(e2, r2) {
    return this.load().then(() => this.source.queryFeatures(x.from(e2) || this.createQuery(), r2)).then((e3) => {
      var _a;
      return (_a = e3 == null ? void 0 : e3.features) == null ? void 0 : _a.forEach((e4) => {
        e4.layer = e4.sourceLayer = this;
      }), e3;
    });
  }
  serviceSupportsSpatialReference(e2) {
    var _a;
    return ((_a = this.source) == null ? void 0 : _a.serviceSupportsSpatialReference(e2)) ?? false;
  }
  async _fetchService(e2) {
    await this.source.load(e2), this.read(this.source.featureDefinition, { origin: "service" }), F2(this.renderer, this.fieldsIndex), x4(this.timeInfo, this.fieldsIndex);
  }
  _getLayerDomain(e2) {
    if (!this.fields) return null;
    for (const r2 of this.fields) if (r2.name === e2 && r2.domain) return r2.domain;
    return null;
  }
};
e([y({ readOnly: true, json: { origins: { service: { read: true } } } })], B.prototype, "capabilities", void 0), e([y({ type: String, json: { write: true } })], B.prototype, "collectionId", void 0), e([y({ type: String })], B.prototype, "copyright", void 0), e([y({ readOnly: true })], B.prototype, "defaultPopupTemplate", null), e([y({ type: String })], B.prototype, "definitionExpression", void 0), e([y({ readOnly: true, type: String, json: { origins: { service: { name: "collection.description" } } } })], B.prototype, "description", void 0), e([y({ type: String })], B.prototype, "displayField", void 0), e([y({ type: Number })], B.prototype, "effectiveMaxRecordCount", null), e([y(d)], B.prototype, "elevationInfo", void 0), e([y({ type: [y2], json: { origins: { service: { name: "layerDefinition.fields" } } } })], B.prototype, "fields", void 0), e([y(M.fieldsIndex)], B.prototype, "fieldsIndex", void 0), e([y({ readOnly: true, type: w, json: { origins: { service: { name: "layerDefinition.extent" } } } })], B.prototype, "fullExtent", void 0), e([y({ type: o.apiValues, json: { origins: { service: { name: "layerDefinition.geometryType", read: { reader: o.read } } } } })], B.prototype, "geometryType", void 0), e([y({ type: Boolean, json: { origins: { service: { name: "layerDefinition.hasZ" } } } })], B.prototype, "hasZ", void 0), e([y({ type: Boolean, readOnly: true })], B.prototype, "isTable", null), e([y({ type: [C], json: { origins: { "web-document": { name: "layerDefinition.drawingInfo.labelingInfo", read: { reader: i3 }, write: true } } } })], B.prototype, "labelingInfo", void 0), e([y(m2)], B.prototype, "labelsVisible", void 0), e([y(c)], B.prototype, "legendEnabled", void 0), e([y({ type: Number })], B.prototype, "maxRecordCount", void 0), e([y({ type: String, json: { origins: { service: { name: "layerDefinition.objectIdField" } } } })], B.prototype, "objectIdField", void 0), e([y({ type: ["OGCFeatureLayer"] })], B.prototype, "operationalLayerType", void 0), e([y(p)], B.prototype, "popupEnabled", void 0), e([y({ type: k2, json: { name: "popupInfo", write: true } })], B.prototype, "popupTemplate", void 0), e([y({ types: p3, json: { origins: { service: { name: "layerDefinition.drawingInfo.renderer", write: false }, "web-scene": { types: n2, name: "layerDefinition.drawingInfo.renderer", write: true } }, name: "layerDefinition.drawingInfo.renderer", write: true } })], B.prototype, "renderer", null), e([y(l)], B.prototype, "screenSizePerspectiveEnabled", void 0), e([y({ readOnly: true })], B.prototype, "source", void 0), e([y({ readOnly: true, type: f, json: { origins: { service: { read: true } } } })], B.prototype, "spatialReference", void 0), e([y({ type: String, json: { write: { enabled: true, ignoreOrigin: true, isRequired: true }, origins: { service: { name: "collection.title" } } } })], B.prototype, "title", void 0), e([y({ readOnly: true, json: { read: false } })], B.prototype, "type", void 0), e([y({ type: String, readOnly: true })], B.prototype, "typeIdField", void 0), e([y({ type: [n4] })], B.prototype, "types", void 0), e([y(f2)], B.prototype, "url", void 0), B = e([a("esri.layers.OGCFeatureLayer")], B);
var V = B;
export {
  V as default
};
//# sourceMappingURL=OGCFeatureLayer-5OKLEECT.js.map
