{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/GeoRSSLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import r from\"../../../PopupTemplate.js\";import s from\"../../../core/Collection.js\";import{watch as i,initial as t}from\"../../../core/reactiveUtils.js\";import\"../../../core/Logger.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import\"../../../core/Error.js\";import\"../../../core/has.js\";import{subclass as o}from\"../../../core/accessorSupport/decorators/subclass.js\";import p from\"../../../renderers/SimpleRenderer.js\";import{fromJSON as a}from\"../../../renderers/support/jsonUtils.js\";import l from\"../../../rest/support/FeatureSet.js\";import{LayerView2DMixin as n}from\"./LayerView2D.js\";import h from\"./graphics/GraphicContainer.js\";import c from\"./graphics/GraphicsView2D.js\";import m from\"../../layers/LayerView.js\";let y=class extends(n(m)){constructor(){super(...arguments),this._graphicsViewMap={},this._popupTemplates=new Map,this.graphicsViews=[]}async hitTest(e,r){if(!this.graphicsViews.length)return null;const s=this.layer;return this.graphicsViews.reverse().map((r=>{const i=this._popupTemplates.get(r),t=r.hitTest(e);for(const e of t)e.layer=s,e.sourceLayer=s,e.popupTemplate=i;return t})).flat().map((r=>({type:\"graphic\",graphic:r,layer:s,mapPoint:e})))}update(e){if(this.graphicsViews)for(const r of this.graphicsViews)r.processUpdate(e)}attach(){this.addAttachHandles([i((()=>this.layer?.featureCollections),(e=>{this._clear();for(const{popupInfo:i,featureSet:t,layerDefinition:o}of e){const e=l.fromJSON(t),p=new s(e.features),n=o.drawingInfo,m=i?r.fromJSON(i):null,y=a(n.renderer),g=new c({requestUpdateCallback:()=>this.requestUpdate(),view:this.view,graphics:p,renderer:y,container:new h(this.view.featuresTilingScheme)});this._graphicsViewMap[e.geometryType]=g,this._popupTemplates.set(g,m),\"polygon\"!==e.geometryType||this.layer.polygonSymbol?\"polyline\"!==e.geometryType||this.layer.lineSymbol?\"point\"!==e.geometryType||this.layer.pointSymbol||(this.layer.pointSymbol=y.symbol):this.layer.lineSymbol=y.symbol:this.layer.polygonSymbol=y.symbol,this.graphicsViews.push(g),this.container.addChild(g.container)}}),t),i((()=>this.layer?.polygonSymbol),(e=>{this._graphicsViewMap.polygon.renderer=new p({symbol:e})}),t),i((()=>this.layer?.lineSymbol),(e=>{this._graphicsViewMap.polyline.renderer=new p({symbol:e})}),t),i((()=>this.layer?.pointSymbol),(e=>{this._graphicsViewMap.point.renderer=new p({symbol:e})}),t)])}detach(){this._clear()}moveStart(){}moveEnd(){}viewChange(){for(const e of this.graphicsViews)e.viewChange()}_clear(){this.container.removeAllChildren();for(const e of this.graphicsViews)e.destroy();this._graphicsViewMap={},this._popupTemplates.clear(),this.graphicsViews.length=0}};y=e([o(\"esri.views.2d.layers.GeoRSSLayerView2D\")],y);const g=y;export{g as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI8yB,IAAI,IAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,mBAAiB,CAAC,GAAE,KAAK,kBAAgB,oBAAI,OAAI,KAAK,gBAAc,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQA,IAAE,GAAE;AAAC,QAAG,CAAC,KAAK,cAAc,OAAO,QAAO;AAAK,UAAM,IAAE,KAAK;AAAM,WAAO,KAAK,cAAc,QAAQ,EAAE,IAAK,CAAAC,OAAG;AAAC,YAAMC,KAAE,KAAK,gBAAgB,IAAID,EAAC,GAAEE,KAAEF,GAAE,QAAQD,EAAC;AAAE,iBAAUA,MAAKG,GAAE,CAAAH,GAAE,QAAM,GAAEA,GAAE,cAAY,GAAEA,GAAE,gBAAcE;AAAE,aAAOC;AAAA,IAAC,CAAE,EAAE,KAAK,EAAE,IAAK,CAAAF,QAAI,EAAC,MAAK,WAAU,SAAQA,IAAE,OAAM,GAAE,UAASD,GAAC,EAAG;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,QAAG,KAAK,cAAc,YAAU,KAAK,KAAK,cAAc,GAAE,cAAcA,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,iBAAiB,CAAC,EAAG,MAAE;AAJ12C;AAI42C,wBAAK,UAAL,mBAAY;AAAA,OAAqB,CAAAA,OAAG;AAAC,WAAK,OAAO;AAAE,iBAAS,EAAC,WAAUE,IAAE,YAAWC,IAAE,iBAAgB,EAAC,KAAIH,IAAE;AAAC,cAAMA,KAAE,EAAE,SAASG,EAAC,GAAEC,KAAE,IAAI,EAAEJ,GAAE,QAAQ,GAAE,IAAE,EAAE,aAAY,IAAEE,KAAE,EAAE,SAASA,EAAC,IAAE,MAAKG,KAAE,EAAE,EAAE,QAAQ,GAAEC,KAAE,IAAI,GAAE,EAAC,uBAAsB,MAAI,KAAK,cAAc,GAAE,MAAK,KAAK,MAAK,UAASF,IAAE,UAASC,IAAE,WAAU,IAAI,EAAE,KAAK,KAAK,oBAAoB,EAAC,CAAC;AAAE,aAAK,iBAAiBL,GAAE,YAAY,IAAEM,IAAE,KAAK,gBAAgB,IAAIA,IAAE,CAAC,GAAE,cAAYN,GAAE,gBAAc,KAAK,MAAM,gBAAc,eAAaA,GAAE,gBAAc,KAAK,MAAM,aAAW,YAAUA,GAAE,gBAAc,KAAK,MAAM,gBAAc,KAAK,MAAM,cAAYK,GAAE,UAAQ,KAAK,MAAM,aAAWA,GAAE,SAAO,KAAK,MAAM,gBAAcA,GAAE,QAAO,KAAK,cAAc,KAAKC,EAAC,GAAE,KAAK,UAAU,SAASA,GAAE,SAAS;AAAA,MAAC;AAAA,IAAC,GAAG,CAAC,GAAE,EAAG,MAAE;AAJxlE;AAI0lE,wBAAK,UAAL,mBAAY;AAAA,OAAgB,CAAAN,OAAG;AAAC,WAAK,iBAAiB,QAAQ,WAAS,IAAI,EAAE,EAAC,QAAOA,GAAC,CAAC;AAAA,IAAC,GAAG,CAAC,GAAE,EAAG,MAAE;AAJ7rE;AAI+rE,wBAAK,UAAL,mBAAY;AAAA,OAAa,CAAAA,OAAG;AAAC,WAAK,iBAAiB,SAAS,WAAS,IAAI,EAAE,EAAC,QAAOA,GAAC,CAAC;AAAA,IAAC,GAAG,CAAC,GAAE,EAAG,MAAE;AAJhyE;AAIkyE,wBAAK,UAAL,mBAAY;AAAA,OAAc,CAAAA,OAAG;AAAC,WAAK,iBAAiB,MAAM,WAAS,IAAI,EAAE,EAAC,QAAOA,GAAC,CAAC;AAAA,IAAC,GAAG,CAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,OAAO;AAAA,EAAC;AAAA,EAAC,YAAW;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,eAAUA,MAAK,KAAK,cAAc,CAAAA,GAAE,WAAW;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,UAAU,kBAAkB;AAAE,eAAUA,MAAK,KAAK,cAAc,CAAAA,GAAE,QAAQ;AAAE,SAAK,mBAAiB,CAAC,GAAE,KAAK,gBAAgB,MAAM,GAAE,KAAK,cAAc,SAAO;AAAA,EAAC;AAAC;AAAE,IAAE,EAAE,CAAC,EAAE,wCAAwC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["e", "r", "i", "t", "p", "y", "g"]}