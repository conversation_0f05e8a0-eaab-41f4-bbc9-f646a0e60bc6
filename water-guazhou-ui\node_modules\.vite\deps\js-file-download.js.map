{"version": 3, "sources": ["../../js-file-download/file-download.js"], "sourcesContent": ["module.exports = function(data, filename, mime, bom) {\n    var blobData = (typeof bom !== 'undefined') ? [bom, data] : [data]\n    var blob = new Blob(blobData, {type: mime || 'application/octet-stream'});\n    if (typeof window.navigator.msSaveBlob !== 'undefined') {\n        // IE workaround for \"HTML7007: One or more blob URLs were\n        // revoked by closing the blob for which they were created.\n        // These URLs will no longer resolve as the data backing\n        // the URL has been freed.\"\n        window.navigator.msSaveBlob(blob, filename);\n    }\n    else {\n        var blobURL = (window.URL && window.URL.createObjectURL) ? window.URL.createObjectURL(blob) : window.webkitURL.createObjectURL(blob);\n        var tempLink = document.createElement('a');\n        tempLink.style.display = 'none';\n        tempLink.href = blobURL;\n        tempLink.setAttribute('download', filename);\n\n        // Safari thinks _blank anchor are pop ups. We only want to set _blank\n        // target if the browser does not support the HTML5 download attribute.\n        // This allows you to download files in desktop safari if pop up blocking\n        // is enabled.\n        if (typeof tempLink.download === 'undefined') {\n            tempLink.setAttribute('target', '_blank');\n        }\n\n        document.body.appendChild(tempLink);\n        tempLink.click();\n\n        // Fixes \"webkit blob resource error 1\"\n        setTimeout(function() {\n            document.body.removeChild(tempLink);\n            window.URL.revokeObjectURL(blobURL);\n        }, 200)\n    }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,WAAO,UAAU,SAAS,MAAM,UAAU,MAAM,KAAK;AACjD,UAAI,WAAY,OAAO,QAAQ,cAAe,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI;AACjE,UAAI,OAAO,IAAI,KAAK,UAAU,EAAC,MAAM,QAAQ,2BAA0B,CAAC;AACxE,UAAI,OAAO,OAAO,UAAU,eAAe,aAAa;AAKpD,eAAO,UAAU,WAAW,MAAM,QAAQ;AAAA,MAC9C,OACK;AACD,YAAI,UAAW,OAAO,OAAO,OAAO,IAAI,kBAAmB,OAAO,IAAI,gBAAgB,IAAI,IAAI,OAAO,UAAU,gBAAgB,IAAI;AACnI,YAAI,WAAW,SAAS,cAAc,GAAG;AACzC,iBAAS,MAAM,UAAU;AACzB,iBAAS,OAAO;AAChB,iBAAS,aAAa,YAAY,QAAQ;AAM1C,YAAI,OAAO,SAAS,aAAa,aAAa;AAC1C,mBAAS,aAAa,UAAU,QAAQ;AAAA,QAC5C;AAEA,iBAAS,KAAK,YAAY,QAAQ;AAClC,iBAAS,MAAM;AAGf,mBAAW,WAAW;AAClB,mBAAS,KAAK,YAAY,QAAQ;AAClC,iBAAO,IAAI,gBAAgB,OAAO;AAAA,QACtC,GAAG,GAAG;AAAA,MACV;AAAA,IACJ;AAAA;AAAA;", "names": []}