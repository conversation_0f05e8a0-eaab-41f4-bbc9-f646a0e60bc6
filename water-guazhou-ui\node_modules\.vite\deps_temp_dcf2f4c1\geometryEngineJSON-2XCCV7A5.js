import {
  A,
  B,
  C,
  D,
  E,
  G,
  H,
  I,
  L,
  M,
  P,
  R,
  S,
  T,
  V,
  _,
  a,
  b,
  c,
  d,
  f,
  g,
  h,
  i,
  j,
  l,
  m,
  o,
  p,
  q,
  r,
  s,
  t,
  u,
  v,
  w,
  x,
  y,
  z
} from "./chunk-PJV4CMYI.js";
import "./chunk-MN2LGVDI.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-H3AJBOWU.js";
export {
  R as buffer,
  G as changeDefaultSpatialReferenceTolerance,
  M as clearDefaultSpatialReferenceTolerance,
  r as clip,
  a as contains,
  y as convexHull,
  o as crosses,
  i as cut,
  V as densify,
  x as difference,
  p as disjoint,
  s as distance,
  u as equals,
  t as extendedSpatialReferenceInfo,
  v as flipHorizontal,
  z as flipVertical,
  I as generalize,
  q as geodesicArea,
  j as geodesicBuffer,
  H as geodesicDensify,
  C as geodesicLength,
  w as intersect,
  P as intersectLinesToPoints,
  c as intersects,
  m as isSimple,
  E as nearestCoordinate,
  L as nearestVertex,
  T as nearestVertices,
  D as offset,
  g as overlaps,
  B as planarArea,
  _ as planarLength,
  d as relate,
  b as rotate,
  h as simplify,
  S as symmetricDifference,
  f as touches,
  A as union,
  l as within
};
//# sourceMappingURL=geometryEngineJSON-2XCCV7A5.js.map
