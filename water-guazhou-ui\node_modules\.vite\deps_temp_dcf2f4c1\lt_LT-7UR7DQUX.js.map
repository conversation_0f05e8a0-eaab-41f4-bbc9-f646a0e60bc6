{"version": 3, "sources": ["../../@arcgis/core/chunks/lt_LT.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{g as a}from\"./_commonjsHelpers.js\";import{c as e}from\"./_commonjs-dynamic-modules.js\";function i(a,e){for(var i=0;i<e.length;i++){const t=e[i];if(\"string\"!=typeof t&&!Array.isArray(t))for(const e in t)if(\"default\"!==e&&!(e in a)){const i=Object.getOwnPropertyDescriptor(t,e);i&&Object.defineProperty(a,e,i.get?i:{enumerable:!0,get:()=>t[e]})}}return Object.freeze(Object.defineProperty(a,Symbol.toStringTag,{value:\"Module\"}))}var t,r,o={},s={get exports(){return o},set exports(a){o=a}};t=s,void 0!==(r=function(a,e){Object.defineProperty(e,\"__esModule\",{value:!0}),e.default={_decimalSeparator:\",\",_thousandSeparator:\" \",_percentPrefix:null,_percentSuffix:\"%\",_date_millisecond:\"mm::ss SSS\",_date_second:\"HH:mm:ss\",_date_minute:\"HH:mm\",_date_hour:\"HH:mm\",_date_day:\"yyyy-MM-dd\",_date_week:\"ww\",_date_month:\"MMM\",_date_year:\"yyyy\",_duration_millisecond:\"SSS\",_duration_second:\"ss\",_duration_minute:\"mm\",_duration_hour:\"hh\",_duration_day:\"dd\",_duration_week:\"ww\",_duration_month:\"MM\",_duration_year:\"yyyy\",_era_ad:\"m.e.\",_era_bc:\"p.m.e.\",A:\"R\",P:\"V\",AM:\"ryto\",PM:\"vakaro\",\"A.M.\":\"ryto\",\"P.M.\":\"vakaro\",January:\"sausio\",February:\"vasario\",March:\"kovo\",April:\"balandžio\",May:\"gegužės\",June:\"birželio\",July:\"liepos\",August:\"rugpjūčio\",September:\"rugsėjo\",October:\"spalio\",November:\"lapkričio\",December:\"gruodžio\",Jan:\"sau.\",Feb:\"vas.\",Mar:\"kov.\",Apr:\"bal.\",\"May(short)\":\"geg.\",Jun:\"bir.\",Jul:\"lie.\",Aug:\"rgp.\",Sep:\"rgs.\",Oct:\"spa.\",Nov:\"lap.\",Dec:\"gruo.\",Sunday:\"sekmadienis\",Monday:\"pirmadienis\",Tuesday:\"antradienis\",Wednesday:\"trečiadienis\",Thursday:\"ketvirtadienis\",Friday:\"penktadienis\",Saturday:\"šeštadienis\",Sun:\"sekm.\",Mon:\"pirm.\",Tue:\"antr.\",Wed:\"treč.\",Thu:\"ketv.\",Fri:\"penk.\",Sat:\"šešt.\",_dateOrd:function(a){return\"-a(s)\"},\"Zoom Out\":\"Rodyti viską\",Play:\"Paleisti\",Stop:\"Sustabdyti\",Legend:\"Legenda\",\"Click, tap or press ENTER to toggle\":\"Spragtelkite, palieskite arba spauskite ENTER, kad perjungtumėte\",Loading:\"Kraunama\",Home:\"Pradžia\",Chart:\"Grafikas\",\"Serial chart\":\"Serijinis grafikas\",\"X/Y chart\":\"X/Y grafikas\",\"Pie chart\":\"Pyrago tipo grafikas\",\"Gauge chart\":\"Daviklio tipo grafikas\",\"Radar chart\":\"Radaro tipo grafikas\",\"Sankey diagram\":\"Sankey diagrama\",\"Chord diagram\":\"Chord diagrama\",\"Flow diagram\":\"Flow diagrama\",\"TreeMap chart\":\"TreeMap grafikas\",Series:\"Serija\",\"Candlestick Series\":'\"Candlestick\" tipo grafiko serija',\"Column Series\":\"Stulpelinio grafiko serija\",\"Line Series\":\"Linijinio grafiko serija\",\"Pie Slice Series\":\"Pyrago tipo serija\",\"X/Y Series\":\"X/Y serija\",Map:\"Žemėlapis\",\"Press ENTER to zoom in\":\"Spauskite ENTER, kad pritrauktumėte vaizdą\",\"Press ENTER to zoom out\":\"Spauskite ENTER, kad atitolintumėte vaizdą\",\"Use arrow keys to zoom in and out\":\"Naudokitės royklėmis vaizdo pritraukimui ar atitolinimui\",\"Use plus and minus keys on your keyboard to zoom in and out\":\"Spauskite pliuso arba minuso klavišus ant klaviatūros, kad pritrautumėte arba atitolintumėte vaizdą\",Export:\"Eksportuoti\",Image:\"Paveiksliukas\",Data:\"Duomenys\",Print:\"Spausdinti\",\"Click, tap or press ENTER to open\":\"Spragtelkite arba spauskite ENTER, kad atidarytumėte\",\"Click, tap or press ENTER to print.\":\"Spragtelkite arba spauskite ENTER, kad spausdintumėte.\",\"Click, tap or press ENTER to export as %1.\":\"Spragtelkite arba spauskite ENTER, kad eksportuotumėte kaip %1.\",'To save the image, right-click this link and choose \"Save picture as...\"':'Kad išsaugotumėte paveiksliuką, spauskite dešinį pelės klavišą ir pasirinkite \"Išsaugoti, kaip paveiksliuką...\"',\"(Press ESC to close this message)\":\"(Spauskite ESC, kad uždarytumėte šį pranešimą)\",\"Image Export Complete\":\"Paveiksliuko eksportas baigtas\",\"Export operation took longer than expected. Something might have gone wrong.\":\"Eksportas užtruko ilgiau negu turėtų. Greičiausiai įvyko klaida.\",\"Saved from\":\"Išsaugota iš\",PNG:\"\",JPG:\"\",GIF:\"\",SVG:\"\",PDF:\"\",JSON:\"\",CSV:\"\",XLSX:\"\",\"Use TAB to select grip buttons or left and right arrows to change selection\":\"Spauskite TAB klavišą, kad pasirinktumėte žymeklius, arba kairė/dešinė klavišus, kad pakeistumėte pasirinkimą\",\"Use left and right arrows to move selection\":\"Naudokitės klavišais kairė/dešinė, kad pajudintumėte pasirinkimą\",\"Use left and right arrows to move left selection\":\"Naudokitės klavišais kairė/dešinė, kad pajudintumėte kairį žymeklį\",\"Use left and right arrows to move right selection\":\"Naudokitės klavišais kairė/dešinė, kad pajudintumėte dešinį žymeklį\",\"Use TAB select grip buttons or up and down arrows to change selection\":\"Spauskite TAB klavišą, kad pasirinktumėte žymeklius, arba aukštyn/žemyn klavišus, kad pakeistumėte pasirinkimą\",\"Use up and down arrows to move selection\":\"Naudokitės klavišais aukštyn/žemyn, kad pajudintumėte pasirinkimą\",\"Use up and down arrows to move lower selection\":\"Naudokitės klavišais aukštyn/žemyn, kad pajudintumėte apatinį žymeklį\",\"Use up and down arrows to move upper selection\":\"Naudokitės klavišais aukštyn/žemyn, kad pajudintumėte viršutinį žymeklį\",\"From %1 to %2\":\"Nuo %1 iki %2\",\"From %1\":\"Nuo %1\",\"To %1\":\"Iki %1\",\"No parser available for file: %1\":\"Failui %1 neturime tinkamo dešifruotojo\",\"Error parsing file: %1\":\"Skaitant failą %1 įvyko klaida\",\"Unable to load file: %1\":\"Nepavyko užkrauti failo %1\",\"Invalid date\":\"Klaidinga data\"}}(e,o))&&(t.exports=r);const n=i({__proto__:null,default:a(o)},[o]);export{n as l};\n"], "mappings": ";;;;;;;;;AAI6F,SAAS,EAAE,GAAE,GAAE;AAAC,WAAQA,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,UAAMC,KAAE,EAAED,EAAC;AAAE,QAAG,YAAU,OAAOC,MAAG,CAAC,MAAM,QAAQA,EAAC;AAAE,iBAAUC,MAAKD,GAAE,KAAG,cAAYC,MAAG,EAAEA,MAAK,IAAG;AAAC,cAAMF,KAAE,OAAO,yBAAyBC,IAAEC,EAAC;AAAE,QAAAF,MAAG,OAAO,eAAe,GAAEE,IAAEF,GAAE,MAAIA,KAAE,EAAC,YAAW,MAAG,KAAI,MAAIC,GAAEC,EAAC,EAAC,CAAC;AAAA,MAAC;AAAA;AAAA,EAAC;AAAC,SAAO,OAAO,OAAO,OAAO,eAAe,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;AAAC;AAAC,IAAI;AAAJ,IAAMC;AAAN,IAAQC,KAAE,CAAC;AAAX,IAAa,IAAE,EAAC,IAAI,UAAS;AAAC,SAAOA;AAAC,GAAE,IAAI,QAAQ,GAAE;AAAC,EAAAA,KAAE;AAAC,EAAC;AAAE,IAAE,GAAE,YAAUD,KAAE,SAAS,GAAE,GAAE;AAAC,SAAO,eAAe,GAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAE,EAAE,UAAQ,EAAC,mBAAkB,KAAI,oBAAmB,KAAI,gBAAe,MAAK,gBAAe,KAAI,mBAAkB,cAAa,cAAa,YAAW,cAAa,SAAQ,YAAW,SAAQ,WAAU,cAAa,YAAW,MAAK,aAAY,OAAM,YAAW,QAAO,uBAAsB,OAAM,kBAAiB,MAAK,kBAAiB,MAAK,gBAAe,MAAK,eAAc,MAAK,gBAAe,MAAK,iBAAgB,MAAK,gBAAe,QAAO,SAAQ,QAAO,SAAQ,UAAS,GAAE,KAAI,GAAE,KAAI,IAAG,QAAO,IAAG,UAAS,QAAO,QAAO,QAAO,UAAS,SAAQ,UAAS,UAAS,WAAU,OAAM,QAAO,OAAM,aAAY,KAAI,WAAU,MAAK,YAAW,MAAK,UAAS,QAAO,aAAY,WAAU,WAAU,SAAQ,UAAS,UAAS,aAAY,UAAS,YAAW,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,cAAa,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,SAAQ,QAAO,eAAc,QAAO,eAAc,SAAQ,eAAc,WAAU,gBAAe,UAAS,kBAAiB,QAAO,gBAAe,UAAS,eAAc,KAAI,SAAQ,KAAI,SAAQ,KAAI,SAAQ,KAAI,SAAQ,KAAI,SAAQ,KAAI,SAAQ,KAAI,SAAQ,UAAS,SAASE,IAAE;AAAC,WAAM;AAAA,EAAO,GAAE,YAAW,gBAAe,MAAK,YAAW,MAAK,cAAa,QAAO,WAAU,uCAAsC,oEAAmE,SAAQ,YAAW,MAAK,WAAU,OAAM,YAAW,gBAAe,sBAAqB,aAAY,gBAAe,aAAY,wBAAuB,eAAc,0BAAyB,eAAc,wBAAuB,kBAAiB,mBAAkB,iBAAgB,kBAAiB,gBAAe,iBAAgB,iBAAgB,oBAAmB,QAAO,UAAS,sBAAqB,qCAAoC,iBAAgB,8BAA6B,eAAc,4BAA2B,oBAAmB,sBAAqB,cAAa,cAAa,KAAI,aAAY,0BAAyB,8CAA6C,2BAA0B,8CAA6C,qCAAoC,4DAA2D,+DAA8D,uGAAsG,QAAO,eAAc,OAAM,iBAAgB,MAAK,YAAW,OAAM,cAAa,qCAAoC,wDAAuD,uCAAsC,0DAAyD,8CAA6C,mEAAkE,4EAA2E,mHAAkH,qCAAoC,kDAAiD,yBAAwB,kCAAiC,gFAA+E,oEAAmE,cAAa,gBAAe,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,MAAK,IAAG,KAAI,IAAG,MAAK,IAAG,+EAA8E,iHAAgH,+CAA8C,oEAAmE,oDAAmD,sEAAqE,qDAAoD,uEAAsE,yEAAwE,kHAAiH,4CAA2C,qEAAoE,kDAAiD,yEAAwE,kDAAiD,2EAA0E,iBAAgB,iBAAgB,WAAU,UAAS,SAAQ,UAAS,oCAAmC,2CAA0C,0BAAyB,kCAAiC,2BAA0B,8BAA6B,gBAAe,iBAAgB;AAAC,EAAE,GAAED,EAAC,OAAK,EAAE,UAAQD;AAAG,IAAM,IAAE,EAAE,EAAC,WAAU,MAAK,SAAQ,EAAEC,EAAC,EAAC,GAAE,CAACA,EAAC,CAAC;", "names": ["i", "t", "e", "r", "o", "a"]}