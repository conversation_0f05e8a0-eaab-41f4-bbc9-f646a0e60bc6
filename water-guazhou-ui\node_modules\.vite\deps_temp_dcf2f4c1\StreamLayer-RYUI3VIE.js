import {
  t
} from "./chunk-NNKS4NNY.js";
import {
  n as n3,
  p as p6
} from "./chunk-O3LPRA7A.js";
import "./chunk-M5RPNIHK.js";
import "./chunk-KYTIKHPN.js";
import {
  s as s3
} from "./chunk-Y7OJSY6H.js";
import {
  p as p5
} from "./chunk-KTB2COPC.js";
import "./chunk-HTXGAKOK.js";
import {
  i
} from "./chunk-7UNBPRRZ.js";
import {
  o as o4
} from "./chunk-OQK7L3JR.js";
import {
  p as p7
} from "./chunk-5BWF7URZ.js";
import "./chunk-D3MAF4VS.js";
import {
  a as a2
} from "./chunk-ND4JUK42.js";
import "./chunk-2WMCP27R.js";
import {
  p as p2
} from "./chunk-JV6TBH5W.js";
import {
  _
} from "./chunk-TV6WKLTU.js";
import "./chunk-5VDORDNW.js";
import {
  c as c2
} from "./chunk-FHKOFAQ2.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import "./chunk-3HW44BD3.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-P37TUI4J.js";
import {
  n
} from "./chunk-LAEW33J6.js";
import {
  t as t2
} from "./chunk-NGPCXWDX.js";
import "./chunk-IXMTA2RC.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-67MHB3E3.js";
import {
  x
} from "./chunk-USWRDFDJ.js";
import "./chunk-XM4RHPJM.js";
import "./chunk-6T5FEO66.js";
import "./chunk-MKG56XBH.js";
import "./chunk-FZ7BG3VX.js";
import {
  y as y2
} from "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-45UG5A2F.js";
import "./chunk-ORU3OGKZ.js";
import {
  n as n2,
  o as o3,
  p as p4
} from "./chunk-FCQRDLBQ.js";
import {
  T as T2
} from "./chunk-NUZU7NCS.js";
import "./chunk-LHJ7PAFO.js";
import "./chunk-7MQMIP4J.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import {
  C
} from "./chunk-FRJZOKDL.js";
import "./chunk-DKEAXJKM.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import {
  D,
  I,
  c,
  f as f2,
  l as l2,
  m,
  p
} from "./chunk-YJWWP4AU.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import {
  x as x2
} from "./chunk-EQYLMJEJ.js";
import "./chunk-3WUI7ZKG.js";
import {
  p as p3
} from "./chunk-3TXAWGPY.js";
import "./chunk-5EGPPD3R.js";
import "./chunk-L66EKMJP.js";
import "./chunk-3M3FTH72.js";
import "./chunk-XLHYMGQY.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-52L4L36S.js";
import "./chunk-VTOVDEW2.js";
import "./chunk-UKESBP6X.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-TD7BKFJX.js";
import {
  k
} from "./chunk-LNGMNZ4I.js";
import "./chunk-24NZLSKM.js";
import {
  M
} from "./chunk-BNFBWJYJ.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-23VNIYCK.js";
import "./chunk-AVKOL7OR.js";
import {
  F,
  x as x3
} from "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-OVIRLBJH.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import {
  U
} from "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import {
  o as o2
} from "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-4YKNTCHK.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-CGBA4LNQ.js";
import "./chunk-2RZLOKYO.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-HHMRK4FX.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  u,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T
} from "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  r as r2
} from "./chunk-2CM7MIII.js";
import {
  w
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/PurgeOptions.js
var t3;
var i2 = t3 = class extends l {
  constructor() {
    super(...arguments), this.age = null, this.ageReceived = null, this.displayCount = null, this.maxObservations = 1;
  }
  clone() {
    return new t3({ age: this.age, ageReceived: this.ageReceived, displayCount: this.displayCount, maxObservations: this.maxObservations });
  }
};
e([y({ type: Number, json: { write: true } })], i2.prototype, "age", void 0), e([y({ type: Number, json: { write: true } })], i2.prototype, "ageReceived", void 0), e([y({ type: Number, json: { write: true } })], i2.prototype, "displayCount", void 0), e([y({ type: Number, json: { write: true } })], i2.prototype, "maxObservations", void 0), i2 = t3 = e([a("esri.layers.support.PurgeOptions")], i2);
var p8 = i2;

// node_modules/@arcgis/core/layers/StreamLayer.js
var Y = s3();
function Z(e2, t4) {
  return new s2("layer:unsupported", `Layer (${e2.title}, ${e2.id}) of type '${e2.declaredClass}' ${t4}`, { layer: e2 });
}
var ee = class extends n3(p6(n(a2(t2(p2(p5(c2(_(O(o4(b))))))))))) {
  constructor(...e2) {
    super(...e2), this.copyright = null, this.definitionExpression = null, this.displayField = null, this.elevationInfo = null, this.fields = null, this.fieldsIndex = null, this.geometryDefinition = null, this.geometryType = null, this.labelsVisible = true, this.labelingInfo = null, this.legendEnabled = true, this.maxReconnectionAttempts = 0, this.maxReconnectionInterval = 20, this.maxScale = 0, this.minScale = 0, this.objectIdField = null, this.operationalLayerType = "ArcGISStreamLayer", this.popupEnabled = true, this.popupTemplate = null, this.purgeOptions = new p8(), this.screenSizePerspectiveEnabled = true, this.sourceJSON = null, this.spatialReference = f.WGS84, this.type = "stream", this.url = null, this.updateInterval = 300, this.webSocketUrl = null;
  }
  normalizeCtorArgs(e2, t4) {
    return "string" == typeof e2 ? { url: e2, ...t4 } : e2;
  }
  load(e2) {
    if (!("WebSocket" in globalThis)) return this.addResolvingPromise(Promise.reject(new s2("stream-layer:websocket-unsupported", "WebSocket is not supported in this browser. StreamLayer will not have real-time connection with the stream service."))), Promise.resolve(this);
    const t4 = r(e2) ? e2.signal : null;
    return this.addResolvingPromise(this.loadFromPortal({ supportedTypes: ["Stream Service", "Feed"] }, e2).catch(w).then(() => this._fetchService(t4))), Promise.resolve(this);
  }
  get defaultPopupTemplate() {
    return this.createPopupTemplate();
  }
  set renderer(e2) {
    F(e2, this.fieldsIndex), this._set("renderer", e2);
  }
  readRenderer(e2, t4, s4) {
    const n4 = (t4 = t4.layerDefinition || t4).drawingInfo && t4.drawingInfo.renderer || void 0;
    if (n4) {
      const e3 = o3(n4, t4, s4) || void 0;
      return e3 || s.getLogger(this.declaredClass).error("Failed to create renderer", { rendererDefinition: t4.drawingInfo.renderer, layer: this, context: s4 }), e3;
    }
    if (t4.defaultSymbol) return t4.types && t4.types.length ? new T2({ defaultSymbol: te(t4.defaultSymbol, t4, s4), field: t4.typeIdField, uniqueValueInfos: t4.types.map((e3) => ({ id: e3.id, symbol: te(e3.symbol, e3, s4) })) }) : new p3({ symbol: te(t4.defaultSymbol, t4, s4) });
  }
  async connect(e2) {
    const [{ createConnection: t4 }] = await Promise.all([import("./createConnection-MNYRDVU2.js"), this.load()]), r3 = this.geometryType ? o2.toJSON(this.geometryType) : null, { customParameters: o5 = null, definitionExpression: i3 = null, geometryDefinition: s4 = null, maxReconnectionAttempts: n4 = 0, maxReconnectionInterval: p9 = 20, spatialReference: a3 = this.spatialReference } = e2 || this.createConnectionParameters(), l3 = t4(this.parsedUrl, this.spatialReference, a3, r3, { geometry: s4, where: i3 }, n4, p9, o5 ?? void 0), d = r2([this.on("send-message-to-socket", (e3) => l3.sendMessageToSocket(e3)), this.on("send-message-to-client", (e3) => l3.sendMessageToClient(e3))]);
    return l3.once("destroy", d.remove), l3;
  }
  createConnectionParameters() {
    return { spatialReference: this.spatialReference, customParameters: this.customParameters, definitionExpression: this.definitionExpression, geometryDefinition: this.geometryDefinition, maxReconnectionAttempts: this.maxReconnectionAttempts, maxReconnectionInterval: this.maxReconnectionInterval };
  }
  createPopupTemplate(e2) {
    return p7(this, e2);
  }
  createQuery() {
    const e2 = new x();
    return e2.returnGeometry = true, e2.outFields = ["*"], e2.where = this.definitionExpression || "1=1", e2;
  }
  getFieldDomain(e2, t4) {
    if (!this.fields) return null;
    let r3 = null;
    return this.fields.some((t5) => (t5.name === e2 && (r3 = t5.domain), !!r3)), r3;
  }
  getField(e2) {
    return this.fieldsIndex.get(e2);
  }
  serviceSupportsSpatialReference(e2) {
    return true;
  }
  sendMessageToSocket(e2) {
    this.emit("send-message-to-socket", e2);
  }
  sendMessageToClient(e2) {
    this.emit("send-message-to-client", e2);
  }
  write(e2, t4) {
    const r3 = t4 == null ? void 0 : t4.messages;
    return this.webSocketUrl ? (r3 == null ? void 0 : r3.push(Z(this, "using a custom websocket connection cannot be written to web scenes and web maps")), null) : this.parsedUrl ? super.write(e2, t4) : (r3 == null ? void 0 : r3.push(Z(this, "using a client-side only connection without a url cannot be written to web scenes and web maps")), null);
  }
  async _fetchService(e2) {
    var _a, _b, _c;
    if (!!!this.webSocketUrl && this.parsedUrl) {
      if (!this.sourceJSON) {
        const { data: t4 } = await U(this.parsedUrl.path, { query: { f: "json", ...this.customParameters, ...this.parsedUrl.query }, responseType: "json", signal: e2 });
        this.sourceJSON = t4;
      }
    } else {
      if (!((_a = this.timeInfo) == null ? void 0 : _a.trackIdField)) throw new s2("stream-layer:missing-metadata", "The stream layer trackIdField must be specified.");
      if (!this.objectIdField) {
        const e3 = (_b = this.fields.find((e4) => "oid" === e4.type)) == null ? void 0 : _b.name;
        if (!e3) throw new s2("stream-layer:missing-metadata", "The stream layer objectIdField must be specified.");
        this.objectIdField = e3;
      }
      if (!this.fields) throw new s2("stream-layer:missing-metadata", "The stream layer fields must be specified.");
      if (this.fields.some((e3) => e3.name === this.objectIdField) || this.fields.push(new y2({ name: this.objectIdField, alias: this.objectIdField, type: "oid" })), !this.geometryType) throw new s2("stream-layer:missing-metadata", "The stream layer geometryType must be specified.");
      this.webSocketUrl && (this.url = this.webSocketUrl);
    }
    return this.read(this.sourceJSON, { origin: "service", portalItem: this.portalItem, portal: (_c = this.portalItem) == null ? void 0 : _c.portal, url: this.parsedUrl }), F(this.renderer, this.fieldsIndex), x3(this.timeInfo, this.fieldsIndex), this.objectIdField || (this.objectIdField = "__esri_stream_id__"), t(this, { origin: "service" });
  }
};
e([y({ type: String })], ee.prototype, "copyright", void 0), e([y({ readOnly: true })], ee.prototype, "defaultPopupTemplate", null), e([y({ type: String, json: { name: "layerDefinition.definitionExpression", write: { enabled: true, allowNull: true } } })], ee.prototype, "definitionExpression", void 0), e([y({ type: String })], ee.prototype, "displayField", void 0), e([y({ type: x2 })], ee.prototype, "elevationInfo", void 0), e([y(Y.fields)], ee.prototype, "fields", void 0), e([y(Y.fieldsIndex)], ee.prototype, "fieldsIndex", void 0), e([y({ type: w2 })], ee.prototype, "geometryDefinition", void 0), e([y({ type: o2.apiValues, json: { read: { reader: o2.read } } })], ee.prototype, "geometryType", void 0), e([y(m)], ee.prototype, "labelsVisible", void 0), e([y({ type: [C], json: { read: { source: "layerDefinition.drawingInfo.labelingInfo", reader: i }, write: { target: "layerDefinition.drawingInfo.labelingInfo" } } })], ee.prototype, "labelingInfo", void 0), e([y(c)], ee.prototype, "legendEnabled", void 0), e([y({ type: ["show", "hide"] })], ee.prototype, "listMode", void 0), e([y({ type: T })], ee.prototype, "maxReconnectionAttempts", void 0), e([y({ type: T })], ee.prototype, "maxReconnectionInterval", void 0), e([y(D)], ee.prototype, "maxScale", void 0), e([y(I)], ee.prototype, "minScale", void 0), e([y({ type: String })], ee.prototype, "objectIdField", void 0), e([y({ value: "ArcGISStreamLayer", type: ["ArcGISStreamLayer"] })], ee.prototype, "operationalLayerType", void 0), e([y(p)], ee.prototype, "popupEnabled", void 0), e([y({ type: k, json: { read: { source: "popupInfo" }, write: { target: "popupInfo" } } })], ee.prototype, "popupTemplate", void 0), e([y({ type: p8 })], ee.prototype, "purgeOptions", void 0), e([y({ types: p4, json: { origins: { service: { write: { target: "drawingInfo.renderer", enabled: false } }, "web-scene": { name: "layerDefinition.drawingInfo.renderer", types: n2, write: true } }, write: { target: "layerDefinition.drawingInfo.renderer" } } })], ee.prototype, "renderer", null), e([o("service", "renderer", ["drawingInfo.renderer", "defaultSymbol"]), o("renderer", ["layerDefinition.drawingInfo.renderer", "layerDefinition.defaultSymbol"])], ee.prototype, "readRenderer", null), e([y(l2)], ee.prototype, "screenSizePerspectiveEnabled", void 0), e([y()], ee.prototype, "sourceJSON", void 0), e([y({ type: f, json: { origins: { service: { read: { source: "spatialReference" } } } } })], ee.prototype, "spatialReference", void 0), e([y({ json: { read: false } })], ee.prototype, "type", void 0), e([y(f2)], ee.prototype, "url", void 0), e([y({ type: Number })], ee.prototype, "updateInterval", void 0), e([y({ type: String })], ee.prototype, "webSocketUrl", void 0), ee = e([a("esri.layers.StreamLayer")], ee);
var te = u({ types: M });
var re = ee;
export {
  re as default
};
//# sourceMappingURL=StreamLayer-RYUI3VIE.js.map
