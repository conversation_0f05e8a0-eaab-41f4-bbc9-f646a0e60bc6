import {
  r
} from "./chunk-7VG4CGLX.js";
import {
  o
} from "./chunk-EPJSBV4J.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/chunks/sl_SL.js
function a(e, i) {
  for (var a2 = 0; a2 < i.length; a2++) {
    const r3 = i[a2];
    if ("string" != typeof r3 && !Array.isArray(r3)) {
      for (const i2 in r3) if ("default" !== i2 && !(i2 in e)) {
        const a3 = Object.getOwnPropertyDescriptor(r3, i2);
        a3 && Object.defineProperty(e, i2, a3.get ? a3 : { enumerable: true, get: () => r3[i2] });
      }
    }
  }
  return Object.freeze(Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }));
}
var r2;
var o2;
var n = {};
var t = { get exports() {
  return n;
}, set exports(e) {
  n = e;
} };
r2 = t, void 0 !== (o2 = function(e, i) {
  Object.defineProperty(i, "__esModule", { value: true }), i.default = { _decimalSeparator: ",", _thousandSeparator: ".", _percentPrefix: null, _percentSuffix: "%", _big_number_suffix_3: "k", _big_number_suffix_6: "M", _big_number_suffix_9: "G", _big_number_suffix_12: "T", _big_number_suffix_15: "P", _big_number_suffix_18: "E", _big_number_suffix_21: "Z", _big_number_suffix_24: "Y", _small_number_suffix_3: "m", _small_number_suffix_6: "μ", _small_number_suffix_9: "n", _small_number_suffix_12: "p", _small_number_suffix_15: "f", _small_number_suffix_18: "a", _small_number_suffix_21: "z", _small_number_suffix_24: "y", _byte_suffix_B: "B", _byte_suffix_KB: "KB", _byte_suffix_MB: "MB", _byte_suffix_GB: "GB", _byte_suffix_TB: "TB", _byte_suffix_PB: "PB", _date_millisecond: "mm:ss SSS", _date_second: "HH:mm:ss", _date_minute: "HH:mm", _date_hour: "HH:mm", _date_day: "MMM dd", _date_week: "ww", _date_month: "MMM", _date_year: "yyyy", _duration_millisecond: "SSS", _duration_millisecond_second: "ss.SSS", _duration_millisecond_minute: "mm:ss SSS", _duration_millisecond_hour: "hh:mm:ss SSS", _duration_millisecond_day: "d'd' mm:ss SSS", _duration_millisecond_week: "d'd' mm:ss SSS", _duration_millisecond_month: "M'm' dd'd' mm:ss SSS", _duration_millisecond_year: "y'y' MM'm' dd'd' mm:ss SSS", _duration_second: "ss", _duration_second_minute: "mm:ss", _duration_second_hour: "hh:mm:ss", _duration_second_day: "d'd' hh:mm:ss", _duration_second_week: "d'd' hh:mm:ss", _duration_second_month: "M'm' dd'd' hh:mm:ss", _duration_second_year: "y'y' MM'm' dd'd' hh:mm:ss", _duration_minute: "mm", _duration_minute_hour: "hh:mm", _duration_minute_day: "d'd' hh:mm", _duration_minute_week: "d'd' hh:mm", _duration_minute_month: "M'm' dd'd' hh:mm", _duration_minute_year: "y'y' MM'm' dd'd' hh:mm", _duration_hour: "hh'h'", _duration_hour_day: "d'd' hh'h'", _duration_hour_week: "d'd' hh'h'", _duration_hour_month: "M'm' dd'd' hh'h'", _duration_hour_year: "y'y' MM'm' dd'd' hh'h'", _duration_day: "d'd'", _duration_day_week: "d'd'", _duration_day_month: "M'm' dd'd'", _duration_day_year: "y'y' MM'm' dd'd'", _duration_week: "w'w'", _duration_week_month: "w'w'", _duration_week_year: "w'w'", _duration_month: "M'm'", _duration_month_year: "y'y' MM'm'", _duration_year: "y'y'", _era_ad: "n. št.", _era_bc: "pr. n. št.", A: "A", P: "P", AM: "AM", PM: "PM", "A.M.": "A.M.", "P.M.": "P.M.", January: "Januar", February: "Februar", March: "Marec", April: "April", May: "Maj", June: "Junij", July: "Julij", August: "Avgust", September: "September", October: "Oktober", November: "November", December: "December", Jan: "Jan", Feb: "Feb", Mar: "Mar", Apr: "Apr", "May(short)": "Maj", Jun: "Jun", Jul: "Jul", Aug: "Avg", Sep: "Sep", Oct: "Okt", Nov: "Nov", Dec: "Dec", Sunday: "Nedelja", Monday: "Ponedeljek", Tuesday: "Torek", Wednesday: "Sreda", Thursday: "Četrtek", Friday: "Petek", Saturday: "Sobota", Sun: "Ned", Mon: "Pon", Tue: "Tor", Wed: "Sre", Thu: "Čet", Fri: "Pet", Sat: "Sob", _dateOrd: function(e2) {
    return ".";
  }, "Zoom Out": "Oddalji pogled", Play: "Zaženi", Stop: "Ustavi", Legend: "Legenda", "Click, tap or press ENTER to toggle": "Klikni, tapni ali pritisni ENTER za preklop", Loading: "Nalagam", Home: "Domov", Chart: "Graf", "Serial chart": "Serijski graf", "X/Y chart": "X/Y graf", "Pie chart": "Tortni graf", "Gauge chart": "Stevčni graf", "Radar chart": "Radar graf", "Sankey diagram": "Sankey diagram", "Flow diagram": "Prikaz poteka", "Chord diagram": "Kolobarni diagram", "TreeMap chart": "Drevesi graf", "Sliced chart": "Sliced graf", Series: "Serija", "Candlestick Series": "Svečna serija", "OHLC Series": "OHLC serija", "Column Series": "Stolpičasta serija", "Line Series": "Črtna serija", "Pie Slice Series": "Tortna serija", "Funnel Series": "Lijak serija", "Pyramid Series": "Piramidna serija", "X/Y Series": "X/Y serija", Map: "Mapa", "Press ENTER to zoom in": "Pritisni ENTER za približevanje", "Press ENTER to zoom out": "Pritisni ENTER za oddaljevanje", "Use arrow keys to zoom in and out": "Uporabi smerne tiple za približevanje in oddaljevanje", "Use plus and minus keys on your keyboard to zoom in and out": "Uporabi plus in minus tipke na tipkovnici za približevanje in oddaljevanje", Export: "Izvozi", Image: "Slika", Data: "Podatki", Print: "Natisni", "Click, tap or press ENTER to open": "Klikni, tapni ali pritisni ENTER da odpreš.", "Click, tap or press ENTER to print.": "Klikni, tapni ali pritisni ENTER za tiskanje.", "Click, tap or press ENTER to export as %1.": "Klikni, tapni ali pritisni ENTER da izvoziš kot %1.", 'To save the image, right-click this link and choose "Save picture as..."': 'Da shraniš sliko, z desnim gumbom miške klikni to povezavo in izberi "Shrani sliko kot..."', 'To save the image, right-click thumbnail on the left and choose "Save picture as..."': 'Da shraniš sliko, z desnim gumbom miške klikni sličico na levi in izberi "Shrani sliko kot..."', "(Press ESC to close this message)": "(Pritisni ESC da zapreš to sporočilo)", "Image Export Complete": "Izvoz slike končan", "Export operation took longer than expected. Something might have gone wrong.": "Operacija izvoza je trajala dlje kot pričakovano. Nekaj je šlo narobe.", "Saved from": "Shranjeno od", PNG: "PNG", JPG: "JPG", GIF: "GIF", SVG: "SVG", PDF: "PDF", JSON: "JSON", CSV: "CSV", XLSX: "XLSX", "Use TAB to select grip buttons or left and right arrows to change selection": "Uporabi TAB za izbiro drsnih gumbov ali levo in desno smerno tipko da spremeniš izbiro", "Use left and right arrows to move selection": "Uporabi levo in desno smerno tipko za premik izbranega", "Use left and right arrows to move left selection": "Uporabi levo in desno smerno tipko za premik leve izbire", "Use left and right arrows to move right selection": "Uporabi levo in desno smerno tipko za premik desne izbire", "Use TAB select grip buttons or up and down arrows to change selection": "Uporabi TAB za izbiro drsnih gumbov ali gor in dol smerno tipko da spremeniš izbiro", "Use up and down arrows to move selection": "Uporabi gor in dol smerne tipke za premik izbire", "Use up and down arrows to move lower selection": "Uporabi gor in dol smerne tipke za premik spodnje izbire", "Use up and down arrows to move upper selection": "Uporabi gor in dol smerne tipke za premik zgornje izbire", "From %1 to %2": "Od %1 do %2", "From %1": "Od %1", "To %1": "Do %1", "No parser available for file: %1": "Nobenega parserja ni na voljo za datoteko: %1", "Error parsing file: %1": "Napaka pri parsanju datoteke: %1", "Unable to load file: %1": "Ni mogoče naložiti datoteke: %1", "Invalid date": "Neveljaven datum" };
}(r, n)) && (r2.exports = o2);
var s = a({ __proto__: null, default: o(n) }, [n]);
export {
  s
};
//# sourceMappingURL=sl_SL-6TWU7P2A.js.map
