import {
  r
} from "./chunk-7VG4CGLX.js";
import {
  o
} from "./chunk-EPJSBV4J.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/chunks/uk_UA.js
function r2(e, o2) {
  for (var r3 = 0; r3 < o2.length; r3++) {
    const _2 = o2[r3];
    if ("string" != typeof _2 && !Array.isArray(_2)) {
      for (const o3 in _2) if ("default" !== o3 && !(o3 in e)) {
        const r4 = Object.getOwnPropertyDescriptor(_2, o3);
        r4 && Object.defineProperty(e, o3, r4.get ? r4 : { enumerable: true, get: () => _2[o3] });
      }
    }
  }
  return Object.freeze(Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }));
}
var _;
var t;
var a = {};
var n = { get exports() {
  return a;
}, set exports(e) {
  a = e;
} };
_ = n, void 0 !== (t = function(e, o2) {
  Object.defineProperty(o2, "__esModule", { value: true }), o2.default = { _decimalSeparator: ",", _thousandSeparator: " ", _percentPrefix: null, _percentSuffix: "%", _big_number_suffix_3: "k", _big_number_suffix_6: "M", _big_number_suffix_9: "G", _big_number_suffix_12: "T", _big_number_suffix_15: "P", _big_number_suffix_18: "E", _big_number_suffix_21: "Z", _big_number_suffix_24: "Y", _small_number_suffix_3: "m", _small_number_suffix_6: "μ", _small_number_suffix_9: "n", _small_number_suffix_12: "p", _small_number_suffix_15: "f", _small_number_suffix_18: "a", _small_number_suffix_21: "z", _small_number_suffix_24: "y", _byte_suffix_B: "B", _byte_suffix_KB: "KB", _byte_suffix_MB: "MB", _byte_suffix_GB: "GB", _byte_suffix_TB: "TB", _byte_suffix_PB: "PB", _date_millisecond: "mm:ss SSS", _date_second: "HH:mm:ss", _date_minute: "HH:mm", _date_hour: "HH:mm", _date_day: "MMM dd", _date_week: "ww", _date_month: "MMM", _date_year: "yyyy", _duration_millisecond: "SSS", _duration_millisecond_second: "ss.SSS", _duration_millisecond_minute: "mm:ss SSS", _duration_millisecond_hour: "hh:mm:ss SSS", _duration_millisecond_day: "d'd' mm:ss SSS", _duration_millisecond_week: "d'd' mm:ss SSS", _duration_millisecond_month: "M'm' dd'd' mm:ss SSS", _duration_millisecond_year: "y'y' MM'm' dd'd' mm:ss SSS", _duration_second: "ss", _duration_second_minute: "mm:ss", _duration_second_hour: "hh:mm:ss", _duration_second_day: "d'd' hh:mm:ss", _duration_second_week: "d'd' hh:mm:ss", _duration_second_month: "M'm' dd'd' hh:mm:ss", _duration_second_year: "y'y' MM'm' dd'd' hh:mm:ss", _duration_minute: "mm", _duration_minute_hour: "hh:mm", _duration_minute_day: "d'd' hh:mm", _duration_minute_week: "d'd' hh:mm", _duration_minute_month: "M'm' dd'd' hh:mm", _duration_minute_year: "y'y' MM'm' dd'd' hh:mm", _duration_hour: "hh'h'", _duration_hour_day: "d'd' hh'h'", _duration_hour_week: "d'd' hh'h'", _duration_hour_month: "M'm' dd'd' hh'h'", _duration_hour_year: "y'y' MM'm' dd'd' hh'h'", _duration_day: "d'd'", _duration_day_week: "d'd'", _duration_day_month: "M'm' dd'd'", _duration_day_year: "y'y' MM'm' dd'd'", _duration_week: "w'w'", _duration_week_month: "w'w'", _duration_week_year: "w'w'", _duration_month: "M'm'", _duration_month_year: "y'y' MM'm'", _duration_year: "y'y'", _era_ad: "н. е.", _era_bc: "до н. е.", A: "дп", P: "пп", AM: "дп", PM: "пп", "A.M.": "дп", "P.M.": "пп", January: "січня", February: "лютого", March: "березня", April: "квітня", May: "травня", June: "червня", July: "липня", August: "серпня", September: "вересня", October: "жовтня", November: "листопада", December: "грудня", Jan: "січ.", Feb: "лют.", Mar: "бер.", Apr: "квіт.", "May(short)": "трав.", Jun: "черв.", Jul: "лип.", Aug: "серп.", Sep: "вер.", Oct: "жовт.", Nov: "лист.", Dec: "груд.", Sunday: "неділя", Monday: "понеділок", Tuesday: "вівторок", Wednesday: "середа", Thursday: "четвер", Friday: "пʼятниця", Saturday: "субота", Sun: "нд", Mon: "пн", Tue: "вт", Wed: "ср", Thu: "чт", Fri: "пт", Sat: "сб", _dateOrd: function(e2) {
    return "";
  }, "Zoom Out": "Масштабування", Play: "Відтворювати", Stop: "Зупинка", Legend: "Легенда", "Click, tap or press ENTER to toggle": "", Loading: "Завантажується", Home: "Головна сторінка", Chart: "", "Serial chart": "", "X/Y chart": "", "Pie chart": "", "Gauge chart": "", "Radar chart": "", "Sankey diagram": "", "Flow diagram": "", "Chord diagram": "", "TreeMap chart": "", "Sliced chart": "", Series: "", "Candlestick Series": "", "OHLC Series": "", "Column Series": "", "Line Series": "", "Pie Slice Series": "", "Funnel Series": "", "Pyramid Series": "", "X/Y Series": "", Map: "карта", "Press ENTER to zoom in": "", "Press ENTER to zoom out": "", "Use arrow keys to zoom in and out": "", "Use plus and minus keys on your keyboard to zoom in and out": "", Export: "Друк", Image: "Зображення", Data: "Дані", Print: "Друк", "Click, tap or press ENTER to open": "", "Click, tap or press ENTER to print.": "", "Click, tap or press ENTER to export as %1.": "", 'To save the image, right-click this link and choose "Save picture as..."': "", 'To save the image, right-click thumbnail on the left and choose "Save picture as..."': "", "(Press ESC to close this message)": "", "Image Export Complete": "", "Export operation took longer than expected. Something might have gone wrong.": "", "Saved from": "", PNG: "", JPG: "", GIF: "", SVG: "", PDF: "", JSON: "", CSV: "", XLSX: "", "Use TAB to select grip buttons or left and right arrows to change selection": "", "Use left and right arrows to move selection": "", "Use left and right arrows to move left selection": "", "Use left and right arrows to move right selection": "", "Use TAB select grip buttons or up and down arrows to change selection": "", "Use up and down arrows to move selection": "", "Use up and down arrows to move lower selection": "", "Use up and down arrows to move upper selection": "", "From %1 to %2": "Від %1 до %2", "From %1": "Від %1", "To %1": "До %1", "No parser available for file: %1": "", "Error parsing file: %1": "", "Unable to load file: %1": "", "Invalid date": "" };
}(r, a)) && (_.exports = t);
var i = r2({ __proto__: null, default: o(a) }, [a]);
export {
  i as u
};
//# sourceMappingURL=uk_UA-QAIFUR2G.js.map
