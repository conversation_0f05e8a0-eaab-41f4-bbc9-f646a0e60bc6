<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.dispatch.OrderRecordMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           type,
                           (select name from sp_order_record_type where id = type) type_name,
                           command_status,
                           complete_status,
                           send_dept_id,
                           department_resolve_multi_id(send_dept_id)               send_dept_name,
                           receive_dept_id,
                           department_resolve_multi_id(receive_dept_id)            receive_dept_name,
                           send_content,
                           execution_time,
                           remark,
                           enable_pumps,
                           disable_pumps,
                           send_user_id,
                           send_time,
                           receive_user_id,
                           receive_time,
                           reply_content,
                           reply_time,
                           reject_remark,
                           creator,
                           create_time,
                           tenant_id<!--@sql from sp_order_record -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecord">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="type_name" property="typeName"/>
        <result column="complete_status" property="completeStatus"/>
        <result column="command_status" property="commandStatus"/>
        <result column="send_dept_id" property="sendDeptId"/>
        <result column="send_dept_name" property="sendDeptName"/>
        <result column="receive_dept_id" property="receiveDeptId"/>
        <result column="receive_dept_name" property="receiveDeptName"/>
        <result column="send_content" property="sendContent"/>
        <result column="execution_time" property="executionTime"/>
        <result column="remark" property="remark"/>
        <result column="enable_pumps" property="enablePumps"/>
        <result column="disable_pumps" property="disablePumps"/>
        <result column="send_user_id" property="sendUserId"/>
        <result column="send_time" property="sendTime"/>
        <result column="receive_user_id" property="receiveUserId"/>
        <result column="receive_time" property="receiveTime"/>
        <result column="reply_content" property="replyContent"/>
        <result column="reply_time" property="replyTime"/>
        <result column="reject_remark" property="rejectRemark"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_order_record
        <where>
            <if test="type != null and type != ''">
                and str_like("type", #{type})
            </if>
            <!--            <if test="completeStatus != null">-->
            <!--                and complete_status=#{completeStatus}-->
            <!--            </if>-->
            <if test="commandStatus != null and commandStatus != ''">
                and command_status = #{commandStatus}
            </if>
            <if test="receiveDeptId != null and receiveDeptId != ''">
                and department_at_department(receive_dept_id, #{receiveDeptId})
            </if>
            <if test="sendContent != null and sendContent != ''">
                and str_like(send_content, #{sendContent})
            </if>
            <if test="sendUserId != null and sendUserId != ''">
                and send_user_id = #{sendUserId}
            </if>
            <if test="receiveUserId != null and receiveUserId != ''">
                and receive_user_id = #{receiveUserId}
            </if>
            <if test="sendTimeFrom != null">
                and send_time >= #{sendTimeFrom}
            </if>
            <if test="sendTimeTo != null">
                and send_time &lt;= #{sendTimeTo}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by send_time is null, send_time desc, create_time
    </select>

    <update id="receive">
        update sp_order_record
        set command_status  = #{status},
            receive_user_id = #{receiveUserId},
            receive_time    = now()
        where id in
        <foreach collection="idList" open="(" close=")" separator="," item="element">
            #{element}
        </foreach>
    </update>

    <update id="reject">
        update sp_order_record
        set command_status = #{status},
            reject_remark  = #{rejectRemark}
        where id in
        <foreach collection="idList" open="(" close=")" separator="," item="element">
            #{element}
        </foreach>
    </update>

    <update id="reply">
        update sp_order_record
        set command_status  = #{status},
            complete_status = #{completeStatus},
            reply_content   = #{replyContent},
            reply_time      = now()

        where id in
        <foreach collection="idList" open="(" close=")" separator="," item="element">
            #{element}
        </foreach>
    </update>

    <update id="execute">
        update sp_order_record
        set execution_time = now()
        where id = #{orderRecordId}
    </update>

    <insert id="saveAll">
        INSERT INTO sp_order_record(id,
                                    type,
                                    complete_status,
                                    command_status,
                                    send_dept_id,
                                    receive_dept_id,
                                    send_content,
                                    execution_time,
                                    remark,
                                    enable_pumps,
                                    disable_pumps,
                                    send_user_id,
                                    send_time,
                                    receive_user_id,
                                    receive_time,
                                    reply_content,
                                    reply_time,
                                    reject_remark,
                                    creator,
                                    create_time,
                                    tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.type},
             #{element.completeStatus},
             #{element.commandStatus},
             #{element.sendDeptId},
             #{element.receiveDeptId},
             #{element.sendContent},
             #{element.executionTime},
             #{element.remark},
             #{element.enablePumps},
             #{element.disablePumps},
             #{element.sendUserId},
             #{element.sendTime},
             #{element.receiveUserId},
             #{element.receiveTime},
             #{element.replyContent},
             #{element.replyTime},
             #{element.rejectRemark},
             #{element.creator},
             #{element.createTime},
             #{element.tenantId})
        </foreach>
    </insert>

    <update id="changeStatusBatch">
        update sp_order_record
        set command_status = #{status}
        where id in
        <foreach collection="list" open="(" close=")" separator="," item="element">
            #{element}
        </foreach>
    </update>

    <update id="sendBatch">
        update sp_order_record
        set command_status = #{status},
            send_time      = now()
        where id in
        <foreach collection="list" open="(" close=")" separator="," item="element">
            #{element}
        </foreach>
    </update>

    <select id="isStatus" resultType="boolean">
        select count(1) > 0
        from sp_order_record
        where id = #{id}
          and command_status = #{status}
    </select>
</mapper>