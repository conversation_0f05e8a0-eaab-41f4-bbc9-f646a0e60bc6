{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/SubtypeGroupLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import has from\"../../../core/has.js\";import{isSome as r}from\"../../../core/maybe.js\";import{watch as s,initial as i}from\"../../../core/reactiveUtils.js\";import\"../../../core/Logger.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import\"../../../core/Error.js\";import{subclass as t}from\"../../../core/accessorSupport/decorators/subclass.js\";import a from\"../../../layers/support/FeatureFilter.js\";import n from\"./FeatureLayerView2D.js\";function l(e,r){return!e.visible||0!==e.minScale&&r>e.minScale||0!==e.maxScale&&r<e.maxScale}let o=class extends n{initialize(){this.addHandles([s((()=>this.view.scale),(()=>this._update()),i)],\"constructor\")}isUpdating(){const e=this.layer.sublayers.some((e=>null!=e.renderer)),r=this._commandsQueue.updating,s=null!=this._updatingRequiredFieldsPromise,i=!this._proxy||!this._proxy.isReady,t=this._pipelineIsUpdating,a=null==this.tileRenderer||this.tileRenderer?.updating,n=e&&(r||s||i||t||a);return has(\"esri-2d-log-updating\")&&console.log(`Updating FLV2D: ${n}\\n  -> hasRenderer ${e}\\n  -> hasPendingCommand ${r}\\n  -> updatingRequiredFields ${s}\\n  -> updatingProxy ${i}\\n  -> updatingPipeline ${t}\\n  -> updatingTileRenderer ${a}\\n`),n}_injectOverrides(e){let s=super._injectOverrides(e);const i=this.view.scale,t=this.layer.sublayers.filter((e=>l(e,i))).map((e=>e.subtypeCode));if(!t.length)return s;s=r(s)?s:(new a).toJSON();const n=`NOT ${this.layer.subtypeField} IN (${t.join(\",\")})`;return s.where=s.where?`(${s.where}) AND (${n})`:n,s}_setLayersForFeature(e){const r=this.layer.fieldsIndex.get(this.layer.subtypeField),s=e.attributes[r.name],i=this.layer.sublayers.find((e=>e.subtypeCode===s));e.layer=e.sourceLayer=i}_createSchemaConfig(){const e={subtypeField:this.layer.subtypeField,sublayers:Array.from(this.layer.sublayers).map((e=>({featureReduction:null,geometryType:this.layer.geometryType,labelingInfo:e.labelingInfo,labelsVisible:e.labelsVisible,renderer:e.renderer,subtypeCode:e.subtypeCode,orderBy:null})))},r=this.layer.sublayers.map((e=>e.subtypeCode)).join(\",\"),s=this.layer.sublayers.length?`${this.layer.subtypeField} IN (${r})`:\"1=2\";let i=this.layer.definitionExpression?this.layer.definitionExpression+\" AND \":\"\";return i+=s,{...super._createSchemaConfig(),...e,definitionExpression:i}}};o=e([t(\"esri.views.2d.layers.SubtypeGroupLayerView2D\")],o);const u=o;export{u as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIihB,SAASA,GAAEC,IAAEC,IAAE;AAAC,SAAM,CAACD,GAAE,WAAS,MAAIA,GAAE,YAAUC,KAAED,GAAE,YAAU,MAAIA,GAAE,YAAUC,KAAED,GAAE;AAAQ;AAAC,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,WAAW,CAAC,EAAG,MAAI,KAAK,KAAK,OAAQ,MAAI,KAAK,QAAQ,GAAG,CAAC,CAAC,GAAE,aAAa;AAAA,EAAC;AAAA,EAAC,aAAY;AAJ9uB;AAI+uB,UAAMA,KAAE,KAAK,MAAM,UAAU,KAAM,CAAAA,OAAG,QAAMA,GAAE,QAAS,GAAEC,KAAE,KAAK,eAAe,UAAS,IAAE,QAAM,KAAK,gCAA+B,IAAE,CAAC,KAAK,UAAQ,CAAC,KAAK,OAAO,SAAQ,IAAE,KAAK,qBAAoBC,KAAE,QAAM,KAAK,kBAAc,UAAK,iBAAL,mBAAmB,WAAS,IAAEF,OAAIC,MAAG,KAAG,KAAG,KAAGC;AAAG,WAAO,IAAI,sBAAsB,KAAG,QAAQ,IAAI,mBAAmB,CAAC;AAAA,mBAAsBF,EAAC;AAAA,yBAA4BC,EAAC;AAAA,8BAAiC,CAAC;AAAA,qBAAwB,CAAC;AAAA,wBAA2B,CAAC;AAAA,4BAA+BC,EAAC;AAAA,CAAI,GAAE;AAAA,EAAC;AAAA,EAAC,iBAAiBF,IAAE;AAAC,QAAI,IAAE,MAAM,iBAAiBA,EAAC;AAAE,UAAM,IAAE,KAAK,KAAK,OAAM,IAAE,KAAK,MAAM,UAAU,OAAQ,CAAAA,OAAGD,GAAEC,IAAE,CAAC,CAAE,EAAE,IAAK,CAAAA,OAAGA,GAAE,WAAY;AAAE,QAAG,CAAC,EAAE,OAAO,QAAO;AAAE,QAAE,EAAE,CAAC,IAAE,IAAG,IAAI,IAAG,OAAO;AAAE,UAAM,IAAE,OAAO,KAAK,MAAM,YAAY,QAAQ,EAAE,KAAK,GAAG,CAAC;AAAI,WAAO,EAAE,QAAM,EAAE,QAAM,IAAI,EAAE,KAAK,UAAU,CAAC,MAAI,GAAE;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAE;AAAC,UAAMC,KAAE,KAAK,MAAM,YAAY,IAAI,KAAK,MAAM,YAAY,GAAE,IAAED,GAAE,WAAWC,GAAE,IAAI,GAAE,IAAE,KAAK,MAAM,UAAU,KAAM,CAAAD,OAAGA,GAAE,gBAAc,CAAE;AAAE,IAAAA,GAAE,QAAMA,GAAE,cAAY;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,UAAMA,KAAE,EAAC,cAAa,KAAK,MAAM,cAAa,WAAU,MAAM,KAAK,KAAK,MAAM,SAAS,EAAE,IAAK,CAAAA,QAAI,EAAC,kBAAiB,MAAK,cAAa,KAAK,MAAM,cAAa,cAAaA,GAAE,cAAa,eAAcA,GAAE,eAAc,UAASA,GAAE,UAAS,aAAYA,GAAE,aAAY,SAAQ,KAAI,EAAG,EAAC,GAAEC,KAAE,KAAK,MAAM,UAAU,IAAK,CAAAD,OAAGA,GAAE,WAAY,EAAE,KAAK,GAAG,GAAE,IAAE,KAAK,MAAM,UAAU,SAAO,GAAG,KAAK,MAAM,YAAY,QAAQC,EAAC,MAAI;AAAM,QAAI,IAAE,KAAK,MAAM,uBAAqB,KAAK,MAAM,uBAAqB,UAAQ;AAAG,WAAO,KAAG,GAAE,EAAC,GAAG,MAAM,oBAAoB,GAAE,GAAGD,IAAE,sBAAqB,EAAC;AAAA,EAAC;AAAC;AAAE,IAAE,EAAE,CAAC,EAAE,8CAA8C,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["l", "e", "r", "a"]}