import {
  a as a2,
  f as f2,
  g as g3,
  m,
  w
} from "./chunk-55OAEKKJ.js";
import {
  g as g2
} from "./chunk-WP7DEIEE.js";
import "./chunk-TZ4YPYDT.js";
import {
  ee
} from "./chunk-KU2IHOQF.js";
import "./chunk-DJASJBTH.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-ZK5O2DLX.js";
import "./chunk-CCFNWAA2.js";
import {
  I,
  L,
  T
} from "./chunk-BM3BRFSV.js";
import {
  a,
  i as i2,
  o
} from "./chunk-D7BTYVTV.js";
import "./chunk-JLELSJK5.js";
import "./chunk-FDLE77SA.js";
import "./chunk-6DAQTVXB.js";
import "./chunk-NQB6UCZ5.js";
import "./chunk-2CLVPBYJ.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-M4ZUXRA3.js";
import "./chunk-WJKHSSMC.js";
import {
  f,
  g
} from "./chunk-J5A2YARY.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-ZQY4DQCR.js";
import "./chunk-FRO3RSRO.js";
import "./chunk-4VO6N7OL.js";
import "./chunk-7VXHHPI3.js";
import "./chunk-2RO3UJ2R.js";
import "./chunk-ONE6GLG5.js";
import {
  ct,
  nt,
  ot,
  st,
  ut
} from "./chunk-DBDYNPQT.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-4DICTWL2.js";
import "./chunk-WGU7CS6R.js";
import "./chunk-4JOAXMIS.js";
import "./chunk-UYHYCNVE.js";
import "./chunk-INCFUNSS.js";
import {
  r as r2
} from "./chunk-D3MAF4VS.js";
import "./chunk-3HW44BD3.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import {
  j as j2
} from "./chunk-P37TUI4J.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-FZ7BG3VX.js";
import {
  i
} from "./chunk-DHWMTT76.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-AVKOL7OR.js";
import {
  M
} from "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-SGIJIEHB.js";
import {
  U
} from "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import {
  c as c2
} from "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  E,
  c
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  j
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/graphics/sources/geojson/GeoJSONSourceWorker.js
var C = { hasAttachments: false, capabilities: "query, editing, create, delete, update", useStandardizedQueries: true, supportsCoordinatesQuantization: true, supportsReturningQueryGeometry: true, advancedQueryCapabilities: { supportsQueryAttachments: false, supportsStatistics: true, supportsPercentileStatistics: true, supportsReturningGeometryCentroid: true, supportsQueryWithDistance: true, supportsDistinct: true, supportsReturningQueryExtent: true, supportsReturningGeometryProperties: false, supportsHavingClause: true, supportsOrderBy: true, supportsPagination: true, supportsQueryWithResultType: false, supportsSqlExpression: true, supportsDisjointSpatialRel: true } };
var D = class {
  constructor() {
    this._queryEngine = null, this._snapshotFeatures = async (e) => {
      const t = await this._fetch(e);
      return this._createFeatures(t);
    };
  }
  destroy() {
    var _a;
    (_a = this._queryEngine) == null ? void 0 : _a.destroy(), this._queryEngine = this._fieldsIndex = this._createDefaultAttributes = null;
  }
  async load(e, t = {}) {
    this._loadOptions = { url: e.url, customParameters: e.customParameters };
    const i3 = [];
    await this._checkProjection(e.spatialReference);
    let r3 = null;
    e.url && (r3 = await this._fetch(t == null ? void 0 : t.signal));
    const n = L(r3, { geometryType: e.geometryType }), a3 = e.fields || n.fields || [], u = null != e.hasZ ? e.hasZ : n.hasZ, l = n.geometryType;
    let d = e.objectIdField || n.objectIdFieldName || "__OBJECTID";
    const p = e.spatialReference || c;
    let c3 = e.timeInfo;
    a3 === n.fields && n.unknownFields.length > 0 && i3.push({ name: "geojson-layer:unknown-field-types", message: "Some fields types couldn't be inferred from the features and were dropped", details: { unknownFields: n.unknownFields } });
    let h = new r2(a3).get(d);
    h ? ("esriFieldTypeString" !== h.type && (h.type = "esriFieldTypeOID"), h.editable = false, h.nullable = false, d = h.name) : (h = { alias: d, name: d, type: "string" === n.objectIdFieldType ? "esriFieldTypeString" : "esriFieldTypeOID", editable: false, nullable: false }, a3.unshift(h));
    const m2 = {};
    for (const o2 of a3) {
      if (null == o2.name && (o2.name = o2.alias), null == o2.alias && (o2.alias = o2.name), !o2.name) throw new s2("geojson-layer:invalid-field-name", "field name is missing", { field: o2 });
      if (!i.jsonValues.includes(o2.type)) throw new s2("geojson-layer:invalid-field-type", `invalid type for field "${o2.name}"`, { field: o2 });
      if (o2.name !== h.name) {
        const e2 = M(o2);
        void 0 !== e2 && (m2[o2.name] = e2);
      }
    }
    this._fieldsIndex = new r2(a3);
    const f3 = this._fieldsIndex.requiredFields.indexOf(h);
    if (f3 > -1 && this._fieldsIndex.requiredFields.splice(f3, 1), c3) {
      if (c3.startTimeField) {
        const e2 = this._fieldsIndex.get(c3.startTimeField);
        e2 ? (c3.startTimeField = e2.name, e2.type = "esriFieldTypeDate") : c3.startTimeField = null;
      }
      if (c3.endTimeField) {
        const e2 = this._fieldsIndex.get(c3.endTimeField);
        e2 ? (c3.endTimeField = e2.name, e2.type = "esriFieldTypeDate") : c3.endTimeField = null;
      }
      if (c3.trackIdField) {
        const e2 = this._fieldsIndex.get(c3.trackIdField);
        e2 ? c3.trackIdField = e2.name : (c3.trackIdField = null, i3.push({ name: "geojson-layer:invalid-timeInfo-trackIdField", message: "trackIdField is missing", details: { timeInfo: c3 } }));
      }
      c3.startTimeField || c3.endTimeField || (i3.push({ name: "geojson-layer:invalid-timeInfo", message: "startTimeField and endTimeField are missing", details: { timeInfo: c3 } }), c3 = null);
    }
    const I2 = l ? o(l) : void 0, j3 = { warnings: i3, featureErrors: [], layerDefinition: { ...C, drawingInfo: I2 ?? void 0, templates: a(m2), extent: void 0, geometryType: l, objectIdField: d, fields: a3, hasZ: !!u, timeInfo: c3 } };
    this._queryEngine = new ee({ fields: a3, geometryType: l, hasM: false, hasZ: u, objectIdField: d, spatialReference: p, timeInfo: c3, featureStore: new g2({ geometryType: l, hasM: false, hasZ: u }), cacheSpatialQueries: true }), this._createDefaultAttributes = i2(m2, d);
    const w2 = await this._createFeatures(r3);
    this._objectIdGenerator = this._createObjectIdGenerator(this._queryEngine, w2);
    const T2 = this._normalizeFeatures(w2, j3.warnings, j3.featureErrors);
    this._queryEngine.featureStore.addMany(T2);
    const { fullExtent: x, timeExtent: q } = await this._queryEngine.fetchRecomputedExtents();
    if (j3.layerDefinition.extent = x, q) {
      const { start: e2, end: t2 } = q;
      j3.layerDefinition.timeInfo.timeExtent = [e2, t2];
    }
    return j3;
  }
  async applyEdits(e) {
    const { spatialReference: t, geometryType: s3 } = this._queryEngine;
    return await Promise.all([w(t, s3), f(e.adds, t), f(e.updates, t)]), await this._waitSnapshotComplete(), this._applyEdits(e);
  }
  async queryFeatures(e = {}, t = {}) {
    return await this._waitSnapshotComplete(), this._queryEngine.executeQuery(e, t.signal);
  }
  async queryFeatureCount(e = {}, t = {}) {
    return await this._waitSnapshotComplete(), this._queryEngine.executeQueryForCount(e, t.signal);
  }
  async queryObjectIds(e = {}, t = {}) {
    return await this._waitSnapshotComplete(), this._queryEngine.executeQueryForIds(e, t.signal);
  }
  async queryExtent(e = {}, t = {}) {
    return await this._waitSnapshotComplete(), this._queryEngine.executeQueryForExtent(e, t.signal);
  }
  async querySnapping(e, t = {}) {
    return await this._waitSnapshotComplete(), this._queryEngine.executeQueryForSnapping(e, t.signal);
  }
  async refresh(e) {
    var _a;
    this._loadOptions.customParameters = e, (_a = this._snapshotTask) == null ? void 0 : _a.abort(), this._snapshotTask = j2(this._snapshotFeatures), this._snapshotTask.promise.then((e2) => {
      this._queryEngine.featureStore.clear(), this._objectIdGenerator = this._createObjectIdGenerator(this._queryEngine, e2);
      const t = this._normalizeFeatures(e2);
      t && this._queryEngine.featureStore.addMany(t);
    }, (e2) => {
      this._queryEngine.featureStore.clear(), j(e2) || s.getLogger("esri.layers.GeoJSONLayer").error(new s2("geojson-layer:refresh", "An error occurred during refresh", { error: e2 }));
    }), await this._waitSnapshotComplete();
    const { fullExtent: r3, timeExtent: a3 } = await this._queryEngine.fetchRecomputedExtents();
    return { extent: r3, timeExtent: a3 };
  }
  async _createFeatures(e) {
    if (null == e) return [];
    const { geometryType: t, hasZ: s3, objectIdField: i3 } = this._queryEngine, n = I(e, { geometryType: t, hasZ: s3, objectIdField: i3 });
    if (!E(this._queryEngine.spatialReference, c)) for (const a3 of n) r(a3.geometry) && (a3.geometry = ct(g(ut(a3.geometry, this._queryEngine.geometryType, this._queryEngine.hasZ, false), c, this._queryEngine.spatialReference)));
    return n;
  }
  async _waitSnapshotComplete() {
    if (this._snapshotTask && !this._snapshotTask.finished) {
      try {
        await this._snapshotTask.promise;
      } catch {
      }
      return this._waitSnapshotComplete();
    }
  }
  async _fetch(t) {
    const { url: s3, customParameters: i3 } = this._loadOptions, r3 = (await U(s3, { responseType: "json", query: { ...i3 }, signal: t })).data;
    return await T(r3), r3;
  }
  _normalizeFeatures(e, t, s3) {
    const { objectIdField: i3 } = this._queryEngine, r3 = [];
    for (const n of e) {
      const e2 = this._createDefaultAttributes(), a3 = m(this._fieldsIndex, e2, n.attributes, true, t);
      a3 ? s3 == null ? void 0 : s3.push(a3) : (this._assignObjectId(e2, n.attributes, true), n.attributes = e2, n.objectId = e2[i3], r3.push(n));
    }
    return r3;
  }
  async _applyEdits(e) {
    const { adds: t, updates: s3, deletes: i3 } = e, r3 = { addResults: [], deleteResults: [], updateResults: [], uidToObjectId: {} };
    if (t && t.length && this._applyAddEdits(r3, t), s3 && s3.length && this._applyUpdateEdits(r3, s3), i3 && i3.length) {
      for (const e2 of i3) r3.deleteResults.push(f2(e2));
      this._queryEngine.featureStore.removeManyById(i3);
    }
    const { fullExtent: n, timeExtent: a3 } = await this._queryEngine.fetchRecomputedExtents();
    return { extent: n, timeExtent: a3, featureEditResults: r3 };
  }
  _applyAddEdits(e, t) {
    const { addResults: s3 } = e, { geometryType: i3, hasM: n, hasZ: o2, objectIdField: u, spatialReference: l, featureStore: d } = this._queryEngine, c3 = [];
    for (const p of t) {
      if (p.geometry && i3 !== c2(p.geometry)) {
        s3.push(a2("Incorrect geometry type."));
        continue;
      }
      const t2 = this._createDefaultAttributes(), n2 = m(this._fieldsIndex, t2, p.attributes);
      if (n2) s3.push(n2);
      else {
        if (this._assignObjectId(t2, p.attributes), p.attributes = t2, null != p.uid) {
          const t3 = p.attributes[u];
          e.uidToObjectId[p.uid] = t3;
        }
        if (r(p.geometry)) {
          const e2 = p.geometry.spatialReference ?? l;
          p.geometry = g(g3(p.geometry, e2), e2, l);
        }
        c3.push(p), s3.push(f2(p.attributes[u]));
      }
    }
    d.addMany(nt([], c3, i3, o2, n, u));
  }
  _applyUpdateEdits({ updateResults: e }, t) {
    const { geometryType: s3, hasM: i3, hasZ: n, objectIdField: o2, spatialReference: u, featureStore: l } = this._queryEngine;
    for (const d of t) {
      const { attributes: t2, geometry: p } = d, y = t2 && t2[o2];
      if (null == y) {
        e.push(a2(`Identifier field ${o2} missing`));
        continue;
      }
      if (!l.has(y)) {
        e.push(a2(`Feature with object id ${y} missing`));
        continue;
      }
      const m2 = st(l.getFeature(y), s3, n, i3);
      if (r(p)) {
        if (s3 !== c2(p)) {
          e.push(a2("Incorrect geometry type."));
          continue;
        }
        const t3 = p.spatialReference ?? u;
        m2.geometry = g(g3(p, t3), t3, u);
      }
      if (t2) {
        const s4 = m(this._fieldsIndex, m2.attributes, t2);
        if (s4) {
          e.push(s4);
          continue;
        }
      }
      l.add(ot(m2, s3, n, i3, o2)), e.push(f2(y));
    }
  }
  _createObjectIdGenerator(e, t) {
    const s3 = e.fieldsIndex.get(e.objectIdField);
    if ("esriFieldTypeString" === s3.type) return () => s3.name + "-" + Date.now().toString(16);
    let i3 = Number.NEGATIVE_INFINITY;
    for (const r3 of t) r3.objectId && (i3 = Math.max(i3, r3.objectId));
    return i3 = Math.max(0, i3) + 1, () => i3++;
  }
  _assignObjectId(e, t, s3 = false) {
    const i3 = this._queryEngine.objectIdField;
    e[i3] = s3 && i3 in t ? t[i3] : this._objectIdGenerator();
  }
  async _checkProjection(e) {
    try {
      await f(c, e);
    } catch {
      throw new s2("geojson-layer", "Projection not supported");
    }
  }
};
export {
  D as default
};
//# sourceMappingURL=GeoJSONSourceWorker-OUNDRGPP.js.map
