{"version": 3, "sources": ["../../echarts/lib/coord/cartesian/Cartesian.js", "../../echarts/lib/coord/geo/fix/textCoord.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar Cartesian = /** @class */function () {\n  function Cartesian(name) {\n    this.type = 'cartesian';\n    this._dimList = [];\n    this._axes = {};\n    this.name = name || '';\n  }\n  Cartesian.prototype.getAxis = function (dim) {\n    return this._axes[dim];\n  };\n  Cartesian.prototype.getAxes = function () {\n    return zrUtil.map(this._dimList, function (dim) {\n      return this._axes[dim];\n    }, this);\n  };\n  Cartesian.prototype.getAxesByScale = function (scaleType) {\n    scaleType = scaleType.toLowerCase();\n    return zrUtil.filter(this.getAxes(), function (axis) {\n      return axis.scale.type === scaleType;\n    });\n  };\n  Cartesian.prototype.addAxis = function (axis) {\n    var dim = axis.dim;\n    this._axes[dim] = axis;\n    this._dimList.push(dim);\n  };\n  return Cartesian;\n}();\n;\nexport default Cartesian;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nvar coordsOffsetMap = {\n  '南海诸岛': [32, 80],\n  // 全国\n  '广东': [0, -10],\n  '香港': [10, 5],\n  '澳门': [-10, 10],\n  // '北京': [-10, 0],\n  '天津': [5, 5]\n};\nexport default function fixTextCoords(mapType, region) {\n  if (mapType === 'china') {\n    var coordFix = coordsOffsetMap[region.name];\n    if (coordFix) {\n      var cp = region.getCenter();\n      cp[0] += coordFix[0] / 10.5;\n      cp[1] += -coordFix[1] / (10.5 / 0.75);\n      region.setCenter(cp);\n    }\n  }\n}"], "mappings": ";;;;;;AA4CA,IAAI;AAAA;AAAA,EAAyB,WAAY;AACvC,aAASA,WAAU,MAAM;AACvB,WAAK,OAAO;AACZ,WAAK,WAAW,CAAC;AACjB,WAAK,QAAQ,CAAC;AACd,WAAK,OAAO,QAAQ;AAAA,IACtB;AACA,IAAAA,WAAU,UAAU,UAAU,SAAU,KAAK;AAC3C,aAAO,KAAK,MAAM,GAAG;AAAA,IACvB;AACA,IAAAA,WAAU,UAAU,UAAU,WAAY;AACxC,aAAc,IAAI,KAAK,UAAU,SAAU,KAAK;AAC9C,eAAO,KAAK,MAAM,GAAG;AAAA,MACvB,GAAG,IAAI;AAAA,IACT;AACA,IAAAA,WAAU,UAAU,iBAAiB,SAAU,WAAW;AACxD,kBAAY,UAAU,YAAY;AAClC,aAAc,OAAO,KAAK,QAAQ,GAAG,SAAU,MAAM;AACnD,eAAO,KAAK,MAAM,SAAS;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,IAAAA,WAAU,UAAU,UAAU,SAAU,MAAM;AAC5C,UAAI,MAAM,KAAK;AACf,WAAK,MAAM,GAAG,IAAI;AAClB,WAAK,SAAS,KAAK,GAAG;AAAA,IACxB;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AAEF,IAAO,oBAAQ;;;AC9Bf,IAAI,kBAAkB;AAAA,EACpB,QAAQ,CAAC,IAAI,EAAE;AAAA;AAAA,EAEf,MAAM,CAAC,GAAG,GAAG;AAAA,EACb,MAAM,CAAC,IAAI,CAAC;AAAA,EACZ,MAAM,CAAC,KAAK,EAAE;AAAA;AAAA,EAEd,MAAM,CAAC,GAAG,CAAC;AACb;AACe,SAAR,cAA+B,SAAS,QAAQ;AACrD,MAAI,YAAY,SAAS;AACvB,QAAI,WAAW,gBAAgB,OAAO,IAAI;AAC1C,QAAI,UAAU;AACZ,UAAI,KAAK,OAAO,UAAU;AAC1B,SAAG,CAAC,KAAK,SAAS,CAAC,IAAI;AACvB,SAAG,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,OAAO;AAChC,aAAO,UAAU,EAAE;AAAA,IACrB;AAAA,EACF;AACF;", "names": ["<PERSON><PERSON><PERSON>"]}