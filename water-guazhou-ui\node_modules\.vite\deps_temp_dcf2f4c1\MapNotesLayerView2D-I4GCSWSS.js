import {
  r as r2
} from "./chunk-NI23UO3J.js";
import {
  i
} from "./chunk-25KH2VMR.js";
import "./chunk-76V27AD5.js";
import {
  ae
} from "./chunk-JWQLMC4D.js";
import "./chunk-Z4F6BT6Q.js";
import "./chunk-4IW3DWDX.js";
import "./chunk-THDEIRBK.js";
import "./chunk-OGWTQT66.js";
import "./chunk-XR4QWT37.js";
import "./chunk-6FMMG4VO.js";
import "./chunk-CUQZFD6D.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-L3FZV3M6.js";
import "./chunk-XAC3PEBY.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-GCZ6JHKQ.js";
import "./chunk-ERH4WAJU.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-CVN5SSWT.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-U6GJBSCL.js";
import "./chunk-RY6ZYWKC.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-223SE4BY.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-LGU2JTOA.js";
import "./chunk-MYYUEN6M.js";
import "./chunk-53FPJYCC.js";
import "./chunk-N73MYEJE.js";
import "./chunk-C65HMCEM.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-6G2NLXT7.js";
import "./chunk-UHA44FM7.js";
import "./chunk-TFWV44LH.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-6OFWBRK2.js";
import {
  f
} from "./chunk-J3EWJTCQ.js";
import {
  u
} from "./chunk-HWB4LNSZ.js";
import "./chunk-JSZR3BUH.js";
import {
  h as h2
} from "./chunk-R6ZFHGHU.js";
import "./chunk-JCXMTMKU.js";
import "./chunk-WAPZ634R.js";
import "./chunk-3CFQMNJK.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-BMTNBZRF.js";
import "./chunk-YDRLAXYR.js";
import "./chunk-RURSJOSG.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-J5A2YARY.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-ZQY4DQCR.js";
import "./chunk-3JR5KBYG.js";
import "./chunk-WZNPTIYX.js";
import "./chunk-FRO3RSRO.js";
import "./chunk-22FAZXOH.js";
import "./chunk-DFGMRI52.js";
import "./chunk-OZZFNS32.js";
import "./chunk-6KNIOA43.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-MSIU52YL.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-ST2RRB55.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-77E52HT5.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-DBDYNPQT.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-4DICTWL2.js";
import "./chunk-WGU7CS6R.js";
import "./chunk-4JOAXMIS.js";
import "./chunk-UYHYCNVE.js";
import "./chunk-INCFUNSS.js";
import "./chunk-KYTIKHPN.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-7UNBPRRZ.js";
import "./chunk-D3MAF4VS.js";
import "./chunk-2WMCP27R.js";
import "./chunk-3HW44BD3.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-ORU3OGKZ.js";
import "./chunk-LHJ7PAFO.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-FRJZOKDL.js";
import "./chunk-DKEAXJKM.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-L66EKMJP.js";
import "./chunk-3M3FTH72.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-52L4L36S.js";
import "./chunk-VTOVDEW2.js";
import "./chunk-UKESBP6X.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import {
  g
} from "./chunk-TD7BKFJX.js";
import "./chunk-LNGMNZ4I.js";
import "./chunk-24NZLSKM.js";
import "./chunk-BNFBWJYJ.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-23VNIYCK.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-OVIRLBJH.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import {
  a as a2,
  h,
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-4YKNTCHK.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-CGBA4LNQ.js";
import "./chunk-2RZLOKYO.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-HHMRK4FX.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/MapNotesLayerView2D.js
var f2 = "sublayers";
var m = "layerView";
var w = Object.freeze({ remove() {
}, pause() {
}, resume() {
} });
var y = class extends f(u) {
  constructor() {
    super(...arguments), this._highlightIds = /* @__PURE__ */ new Map(), this.container = new r2();
  }
  async fetchPopupFeatures(e2) {
    return Array.from(this.graphicsViews(), (i2) => i2.hitTest(e2).filter((e3) => !!e3.popupTemplate)).flat();
  }
  *graphicsViews() {
    r(this._graphicsViewsFeatureCollectionMap) ? yield* this._graphicsViewsFeatureCollectionMap.keys() : r(this._graphicsViews) ? yield* this._graphicsViews : yield* [];
  }
  async hitTest(e2, i2) {
    return Array.from(this.graphicsViews(), (i3) => {
      const s = i3.hitTest(e2);
      if (r(this._graphicsViewsFeatureCollectionMap)) {
        const e3 = this._graphicsViewsFeatureCollectionMap.get(i3);
        for (const i4 of s) !i4.popupTemplate && e3.popupTemplate && (i4.popupTemplate = e3.popupTemplate), i4.sourceLayer = i4.layer = this.layer;
      }
      return s;
    }).flat().map((i3) => ({ type: "graphic", graphic: i3, layer: this.layer, mapPoint: e2 }));
  }
  highlight(e2) {
    let r3;
    "number" == typeof e2 ? r3 = [e2] : e2 instanceof g ? r3 = [e2.uid] : Array.isArray(e2) && e2.length > 0 ? r3 = "number" == typeof e2[0] ? e2 : e2.map((e3) => e3 && e3.uid) : j.isCollection(e2) && (r3 = e2.map((e3) => e3 && e3.uid).toArray());
    const a3 = r3 == null ? void 0 : r3.filter(r);
    return (a3 == null ? void 0 : a3.length) ? (this._addHighlight(a3), { remove: () => {
      this._removeHighlight(a3);
    } }) : w;
  }
  update(e2) {
    for (const i2 of this.graphicsViews()) i2.processUpdate(e2);
  }
  attach() {
    const e2 = this.view, i2 = () => this.requestUpdate(), s = this.layer.featureCollections;
    if (r(s) && s.length) {
      this._graphicsViewsFeatureCollectionMap = /* @__PURE__ */ new Map();
      for (const t2 of s) {
        const s2 = new i(this.view.featuresTilingScheme), r3 = new ae({ view: e2, graphics: t2.source, renderer: t2.renderer, requestUpdateCallback: i2, container: s2 });
        this._graphicsViewsFeatureCollectionMap.set(r3, t2), this.container.addChild(r3.container), this.addHandles([l(() => t2.visible, (e3) => r3.container.visible = e3, h), l(() => r3.updating, () => this.notifyChange("updating"), h)], m);
      }
      this._updateHighlight();
    } else r(this.layer.sublayers) && this.addHandles(a2(() => this.layer.sublayers, "change", () => this._createGraphicsViews(), { onListenerAdd: () => this._createGraphicsViews(), onListenerRemove: () => this._destroyGraphicsViews() }), f2);
  }
  detach() {
    this._destroyGraphicsViews(), this.removeHandles(f2);
  }
  moveStart() {
  }
  moveEnd() {
  }
  viewChange() {
    for (const e2 of this.graphicsViews()) e2.viewChange();
  }
  isUpdating() {
    for (const e2 of this.graphicsViews()) if (e2.updating) return true;
    return false;
  }
  _destroyGraphicsViews() {
    this.container.removeAllChildren(), this.removeHandles(m);
    for (const e2 of this.graphicsViews()) e2.destroy();
    this._graphicsViews = null, this._graphicsViewsFeatureCollectionMap = null;
  }
  _createGraphicsViews() {
    if (this._destroyGraphicsViews(), t(this.layer.sublayers)) return;
    const e2 = [], i2 = this.view, s = () => this.requestUpdate();
    for (const t2 of this.layer.sublayers) {
      const r3 = new h2(), h3 = new i(this.view.featuresTilingScheme);
      h3.fadeTransitionEnabled = true;
      const n = new ae({ view: i2, graphics: t2.graphics, requestUpdateCallback: s, container: h3 });
      this.addHandles([t2.on("graphic-update", n.graphicUpdateHandler), l(() => t2.visible, (e3) => n.container.visible = e3, h), l(() => n.updating, () => this.notifyChange("updating"), h)], m), r3.addChild(n.container), this.container.addChild(r3), e2.push(n);
    }
    this._graphicsViews = e2, this._updateHighlight();
  }
  _addHighlight(e2) {
    for (const i2 of e2) if (this._highlightIds.has(i2)) {
      const e3 = this._highlightIds.get(i2);
      this._highlightIds.set(i2, e3 + 1);
    } else this._highlightIds.set(i2, 1);
    this._updateHighlight();
  }
  _removeHighlight(e2) {
    for (const i2 of e2) if (this._highlightIds.has(i2)) {
      const e3 = this._highlightIds.get(i2) - 1;
      0 === e3 ? this._highlightIds.delete(i2) : this._highlightIds.set(i2, e3);
    }
    this._updateHighlight();
  }
  _updateHighlight() {
    const e2 = Array.from(this._highlightIds.keys());
    for (const i2 of this.graphicsViews()) i2.setHighlight(e2);
  }
};
y = e([a("esri.views.2d.layers.MapNotesLayerView2D")], y);
var _ = y;
export {
  _ as default
};
//# sourceMappingURL=MapNotesLayerView2D-I4GCSWSS.js.map
