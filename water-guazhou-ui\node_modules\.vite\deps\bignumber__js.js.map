{"version": 3, "sources": ["../../bignumber.js/bignumber.mjs"], "sourcesContent": ["/*\r\n *      bignumber.js v9.1.2\r\n *      A JavaScript library for arbitrary-precision arithmetic.\r\n *      https://github.com/MikeMcl/bignumber.js\r\n *      Copyright (c) 2022 <PERSON> <<EMAIL>>\r\n *      MIT Licensed.\r\n *\r\n *      BigNumber.prototype methods     |  BigNumber methods\r\n *                                      |\r\n *      absoluteValue            abs    |  clone\r\n *      comparedTo                      |  config               set\r\n *      decimalPlaces            dp     |      DECIMAL_PLACES\r\n *      dividedBy                div    |      ROUNDING_MODE\r\n *      dividedToIntegerBy       idiv   |      EXPONENTIAL_AT\r\n *      exponentiatedBy          pow    |      RANGE\r\n *      integerValue                    |      CRYPTO\r\n *      isEqualTo                eq     |      MODULO_MODE\r\n *      isFinite                        |      POW_PRECISION\r\n *      isGreaterThan            gt     |      FORMAT\r\n *      isGreaterThanOrEqualTo   gte    |      ALPHABET\r\n *      isInteger                       |  isBigNumber\r\n *      isLessThan               lt     |  maximum              max\r\n *      isLessThanOrEqualTo      lte    |  minimum              min\r\n *      isNaN                           |  random\r\n *      isNegative                      |  sum\r\n *      isPositive                      |\r\n *      isZero                          |\r\n *      minus                           |\r\n *      modulo                   mod    |\r\n *      multipliedBy             times  |\r\n *      negated                         |\r\n *      plus                            |\r\n *      precision                sd     |\r\n *      shiftedBy                       |\r\n *      squareRoot               sqrt   |\r\n *      toExponential                   |\r\n *      toFixed                         |\r\n *      toFormat                        |\r\n *      toFraction                      |\r\n *      toJSON                          |\r\n *      toNumber                        |\r\n *      toPrecision                     |\r\n *      toString                        |\r\n *      valueOf                         |\r\n *\r\n */\r\n\r\n\r\nvar\r\n  isNumeric = /^-?(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?$/i,\r\n  mathceil = Math.ceil,\r\n  mathfloor = Math.floor,\r\n\r\n  bignumberError = '[BigNumber Error] ',\r\n  tooManyDigits = bignumberError + 'Number primitive has more than 15 significant digits: ',\r\n\r\n  BASE = 1e14,\r\n  LOG_BASE = 14,\r\n  MAX_SAFE_INTEGER = 0x1fffffffffffff,         // 2^53 - 1\r\n  // MAX_INT32 = 0x7fffffff,                   // 2^31 - 1\r\n  POWS_TEN = [1, 10, 100, 1e3, 1e4, 1e5, 1e6, 1e7, 1e8, 1e9, 1e10, 1e11, 1e12, 1e13],\r\n  SQRT_BASE = 1e7,\r\n\r\n  // EDITABLE\r\n  // The limit on the value of DECIMAL_PLACES, TO_EXP_NEG, TO_EXP_POS, MIN_EXP, MAX_EXP, and\r\n  // the arguments to toExponential, toFixed, toFormat, and toPrecision.\r\n  MAX = 1E9;                                   // 0 to MAX_INT32\r\n\r\n\r\n/*\r\n * Create and return a BigNumber constructor.\r\n */\r\nfunction clone(configObject) {\r\n  var div, convertBase, parseNumeric,\r\n    P = BigNumber.prototype = { constructor: BigNumber, toString: null, valueOf: null },\r\n    ONE = new BigNumber(1),\r\n\r\n\r\n    //----------------------------- EDITABLE CONFIG DEFAULTS -------------------------------\r\n\r\n\r\n    // The default values below must be integers within the inclusive ranges stated.\r\n    // The values can also be changed at run-time using BigNumber.set.\r\n\r\n    // The maximum number of decimal places for operations involving division.\r\n    DECIMAL_PLACES = 20,                     // 0 to MAX\r\n\r\n    // The rounding mode used when rounding to the above decimal places, and when using\r\n    // toExponential, toFixed, toFormat and toPrecision, and round (default value).\r\n    // UP         0 Away from zero.\r\n    // DOWN       1 Towards zero.\r\n    // CEIL       2 Towards +Infinity.\r\n    // FLOOR      3 Towards -Infinity.\r\n    // HALF_UP    4 Towards nearest neighbour. If equidistant, up.\r\n    // HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\r\n    // HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\r\n    // HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\r\n    // HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\r\n    ROUNDING_MODE = 4,                       // 0 to 8\r\n\r\n    // EXPONENTIAL_AT : [TO_EXP_NEG , TO_EXP_POS]\r\n\r\n    // The exponent value at and beneath which toString returns exponential notation.\r\n    // Number type: -7\r\n    TO_EXP_NEG = -7,                         // 0 to -MAX\r\n\r\n    // The exponent value at and above which toString returns exponential notation.\r\n    // Number type: 21\r\n    TO_EXP_POS = 21,                         // 0 to MAX\r\n\r\n    // RANGE : [MIN_EXP, MAX_EXP]\r\n\r\n    // The minimum exponent value, beneath which underflow to zero occurs.\r\n    // Number type: -324  (5e-324)\r\n    MIN_EXP = -1e7,                          // -1 to -MAX\r\n\r\n    // The maximum exponent value, above which overflow to Infinity occurs.\r\n    // Number type:  308  (1.7976931348623157e+308)\r\n    // For MAX_EXP > 1e7, e.g. new BigNumber('1e100000000').plus(1) may be slow.\r\n    MAX_EXP = 1e7,                           // 1 to MAX\r\n\r\n    // Whether to use cryptographically-secure random number generation, if available.\r\n    CRYPTO = false,                          // true or false\r\n\r\n    // The modulo mode used when calculating the modulus: a mod n.\r\n    // The quotient (q = a / n) is calculated according to the corresponding rounding mode.\r\n    // The remainder (r) is calculated as: r = a - n * q.\r\n    //\r\n    // UP        0 The remainder is positive if the dividend is negative, else is negative.\r\n    // DOWN      1 The remainder has the same sign as the dividend.\r\n    //             This modulo mode is commonly known as 'truncated division' and is\r\n    //             equivalent to (a % n) in JavaScript.\r\n    // FLOOR     3 The remainder has the same sign as the divisor (Python %).\r\n    // HALF_EVEN 6 This modulo mode implements the IEEE 754 remainder function.\r\n    // EUCLID    9 Euclidian division. q = sign(n) * floor(a / abs(n)).\r\n    //             The remainder is always positive.\r\n    //\r\n    // The truncated division, floored division, Euclidian division and IEEE 754 remainder\r\n    // modes are commonly used for the modulus operation.\r\n    // Although the other rounding modes can also be used, they may not give useful results.\r\n    MODULO_MODE = 1,                         // 0 to 9\r\n\r\n    // The maximum number of significant digits of the result of the exponentiatedBy operation.\r\n    // If POW_PRECISION is 0, there will be unlimited significant digits.\r\n    POW_PRECISION = 0,                       // 0 to MAX\r\n\r\n    // The format specification used by the BigNumber.prototype.toFormat method.\r\n    FORMAT = {\r\n      prefix: '',\r\n      groupSize: 3,\r\n      secondaryGroupSize: 0,\r\n      groupSeparator: ',',\r\n      decimalSeparator: '.',\r\n      fractionGroupSize: 0,\r\n      fractionGroupSeparator: '\\xA0',        // non-breaking space\r\n      suffix: ''\r\n    },\r\n\r\n    // The alphabet used for base conversion. It must be at least 2 characters long, with no '+',\r\n    // '-', '.', whitespace, or repeated character.\r\n    // '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_'\r\n    ALPHABET = '0123456789abcdefghijklmnopqrstuvwxyz',\r\n    alphabetHasNormalDecimalDigits = true;\r\n\r\n\r\n  //------------------------------------------------------------------------------------------\r\n\r\n\r\n  // CONSTRUCTOR\r\n\r\n\r\n  /*\r\n   * The BigNumber constructor and exported function.\r\n   * Create and return a new instance of a BigNumber object.\r\n   *\r\n   * v {number|string|BigNumber} A numeric value.\r\n   * [b] {number} The base of v. Integer, 2 to ALPHABET.length inclusive.\r\n   */\r\n  function BigNumber(v, b) {\r\n    var alphabet, c, caseChanged, e, i, isNum, len, str,\r\n      x = this;\r\n\r\n    // Enable constructor call without `new`.\r\n    if (!(x instanceof BigNumber)) return new BigNumber(v, b);\r\n\r\n    if (b == null) {\r\n\r\n      if (v && v._isBigNumber === true) {\r\n        x.s = v.s;\r\n\r\n        if (!v.c || v.e > MAX_EXP) {\r\n          x.c = x.e = null;\r\n        } else if (v.e < MIN_EXP) {\r\n          x.c = [x.e = 0];\r\n        } else {\r\n          x.e = v.e;\r\n          x.c = v.c.slice();\r\n        }\r\n\r\n        return;\r\n      }\r\n\r\n      if ((isNum = typeof v == 'number') && v * 0 == 0) {\r\n\r\n        // Use `1 / n` to handle minus zero also.\r\n        x.s = 1 / v < 0 ? (v = -v, -1) : 1;\r\n\r\n        // Fast path for integers, where n < 2147483648 (2**31).\r\n        if (v === ~~v) {\r\n          for (e = 0, i = v; i >= 10; i /= 10, e++);\r\n\r\n          if (e > MAX_EXP) {\r\n            x.c = x.e = null;\r\n          } else {\r\n            x.e = e;\r\n            x.c = [v];\r\n          }\r\n\r\n          return;\r\n        }\r\n\r\n        str = String(v);\r\n      } else {\r\n\r\n        if (!isNumeric.test(str = String(v))) return parseNumeric(x, str, isNum);\r\n\r\n        x.s = str.charCodeAt(0) == 45 ? (str = str.slice(1), -1) : 1;\r\n      }\r\n\r\n      // Decimal point?\r\n      if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\r\n\r\n      // Exponential form?\r\n      if ((i = str.search(/e/i)) > 0) {\r\n\r\n        // Determine exponent.\r\n        if (e < 0) e = i;\r\n        e += +str.slice(i + 1);\r\n        str = str.substring(0, i);\r\n      } else if (e < 0) {\r\n\r\n        // Integer.\r\n        e = str.length;\r\n      }\r\n\r\n    } else {\r\n\r\n      // '[BigNumber Error] Base {not a primitive number|not an integer|out of range}: {b}'\r\n      intCheck(b, 2, ALPHABET.length, 'Base');\r\n\r\n      // Allow exponential notation to be used with base 10 argument, while\r\n      // also rounding to DECIMAL_PLACES as with other bases.\r\n      if (b == 10 && alphabetHasNormalDecimalDigits) {\r\n        x = new BigNumber(v);\r\n        return round(x, DECIMAL_PLACES + x.e + 1, ROUNDING_MODE);\r\n      }\r\n\r\n      str = String(v);\r\n\r\n      if (isNum = typeof v == 'number') {\r\n\r\n        // Avoid potential interpretation of Infinity and NaN as base 44+ values.\r\n        if (v * 0 != 0) return parseNumeric(x, str, isNum, b);\r\n\r\n        x.s = 1 / v < 0 ? (str = str.slice(1), -1) : 1;\r\n\r\n        // '[BigNumber Error] Number primitive has more than 15 significant digits: {n}'\r\n        if (BigNumber.DEBUG && str.replace(/^0\\.0*|\\./, '').length > 15) {\r\n          throw Error\r\n           (tooManyDigits + v);\r\n        }\r\n      } else {\r\n        x.s = str.charCodeAt(0) === 45 ? (str = str.slice(1), -1) : 1;\r\n      }\r\n\r\n      alphabet = ALPHABET.slice(0, b);\r\n      e = i = 0;\r\n\r\n      // Check that str is a valid base b number.\r\n      // Don't use RegExp, so alphabet can contain special characters.\r\n      for (len = str.length; i < len; i++) {\r\n        if (alphabet.indexOf(c = str.charAt(i)) < 0) {\r\n          if (c == '.') {\r\n\r\n            // If '.' is not the first character and it has not be found before.\r\n            if (i > e) {\r\n              e = len;\r\n              continue;\r\n            }\r\n          } else if (!caseChanged) {\r\n\r\n            // Allow e.g. hexadecimal 'FF' as well as 'ff'.\r\n            if (str == str.toUpperCase() && (str = str.toLowerCase()) ||\r\n                str == str.toLowerCase() && (str = str.toUpperCase())) {\r\n              caseChanged = true;\r\n              i = -1;\r\n              e = 0;\r\n              continue;\r\n            }\r\n          }\r\n\r\n          return parseNumeric(x, String(v), isNum, b);\r\n        }\r\n      }\r\n\r\n      // Prevent later check for length on converted number.\r\n      isNum = false;\r\n      str = convertBase(str, b, 10, x.s);\r\n\r\n      // Decimal point?\r\n      if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\r\n      else e = str.length;\r\n    }\r\n\r\n    // Determine leading zeros.\r\n    for (i = 0; str.charCodeAt(i) === 48; i++);\r\n\r\n    // Determine trailing zeros.\r\n    for (len = str.length; str.charCodeAt(--len) === 48;);\r\n\r\n    if (str = str.slice(i, ++len)) {\r\n      len -= i;\r\n\r\n      // '[BigNumber Error] Number primitive has more than 15 significant digits: {n}'\r\n      if (isNum && BigNumber.DEBUG &&\r\n        len > 15 && (v > MAX_SAFE_INTEGER || v !== mathfloor(v))) {\r\n          throw Error\r\n           (tooManyDigits + (x.s * v));\r\n      }\r\n\r\n       // Overflow?\r\n      if ((e = e - i - 1) > MAX_EXP) {\r\n\r\n        // Infinity.\r\n        x.c = x.e = null;\r\n\r\n      // Underflow?\r\n      } else if (e < MIN_EXP) {\r\n\r\n        // Zero.\r\n        x.c = [x.e = 0];\r\n      } else {\r\n        x.e = e;\r\n        x.c = [];\r\n\r\n        // Transform base\r\n\r\n        // e is the base 10 exponent.\r\n        // i is where to slice str to get the first element of the coefficient array.\r\n        i = (e + 1) % LOG_BASE;\r\n        if (e < 0) i += LOG_BASE;  // i < 1\r\n\r\n        if (i < len) {\r\n          if (i) x.c.push(+str.slice(0, i));\r\n\r\n          for (len -= LOG_BASE; i < len;) {\r\n            x.c.push(+str.slice(i, i += LOG_BASE));\r\n          }\r\n\r\n          i = LOG_BASE - (str = str.slice(i)).length;\r\n        } else {\r\n          i -= len;\r\n        }\r\n\r\n        for (; i--; str += '0');\r\n        x.c.push(+str);\r\n      }\r\n    } else {\r\n\r\n      // Zero.\r\n      x.c = [x.e = 0];\r\n    }\r\n  }\r\n\r\n\r\n  // CONSTRUCTOR PROPERTIES\r\n\r\n\r\n  BigNumber.clone = clone;\r\n\r\n  BigNumber.ROUND_UP = 0;\r\n  BigNumber.ROUND_DOWN = 1;\r\n  BigNumber.ROUND_CEIL = 2;\r\n  BigNumber.ROUND_FLOOR = 3;\r\n  BigNumber.ROUND_HALF_UP = 4;\r\n  BigNumber.ROUND_HALF_DOWN = 5;\r\n  BigNumber.ROUND_HALF_EVEN = 6;\r\n  BigNumber.ROUND_HALF_CEIL = 7;\r\n  BigNumber.ROUND_HALF_FLOOR = 8;\r\n  BigNumber.EUCLID = 9;\r\n\r\n\r\n  /*\r\n   * Configure infrequently-changing library-wide settings.\r\n   *\r\n   * Accept an object with the following optional properties (if the value of a property is\r\n   * a number, it must be an integer within the inclusive range stated):\r\n   *\r\n   *   DECIMAL_PLACES   {number}           0 to MAX\r\n   *   ROUNDING_MODE    {number}           0 to 8\r\n   *   EXPONENTIAL_AT   {number|number[]}  -MAX to MAX  or  [-MAX to 0, 0 to MAX]\r\n   *   RANGE            {number|number[]}  -MAX to MAX (not zero)  or  [-MAX to -1, 1 to MAX]\r\n   *   CRYPTO           {boolean}          true or false\r\n   *   MODULO_MODE      {number}           0 to 9\r\n   *   POW_PRECISION       {number}           0 to MAX\r\n   *   ALPHABET         {string}           A string of two or more unique characters which does\r\n   *                                       not contain '.'.\r\n   *   FORMAT           {object}           An object with some of the following properties:\r\n   *     prefix                 {string}\r\n   *     groupSize              {number}\r\n   *     secondaryGroupSize     {number}\r\n   *     groupSeparator         {string}\r\n   *     decimalSeparator       {string}\r\n   *     fractionGroupSize      {number}\r\n   *     fractionGroupSeparator {string}\r\n   *     suffix                 {string}\r\n   *\r\n   * (The values assigned to the above FORMAT object properties are not checked for validity.)\r\n   *\r\n   * E.g.\r\n   * BigNumber.config({ DECIMAL_PLACES : 20, ROUNDING_MODE : 4 })\r\n   *\r\n   * Ignore properties/parameters set to null or undefined, except for ALPHABET.\r\n   *\r\n   * Return an object with the properties current values.\r\n   */\r\n  BigNumber.config = BigNumber.set = function (obj) {\r\n    var p, v;\r\n\r\n    if (obj != null) {\r\n\r\n      if (typeof obj == 'object') {\r\n\r\n        // DECIMAL_PLACES {number} Integer, 0 to MAX inclusive.\r\n        // '[BigNumber Error] DECIMAL_PLACES {not a primitive number|not an integer|out of range}: {v}'\r\n        if (obj.hasOwnProperty(p = 'DECIMAL_PLACES')) {\r\n          v = obj[p];\r\n          intCheck(v, 0, MAX, p);\r\n          DECIMAL_PLACES = v;\r\n        }\r\n\r\n        // ROUNDING_MODE {number} Integer, 0 to 8 inclusive.\r\n        // '[BigNumber Error] ROUNDING_MODE {not a primitive number|not an integer|out of range}: {v}'\r\n        if (obj.hasOwnProperty(p = 'ROUNDING_MODE')) {\r\n          v = obj[p];\r\n          intCheck(v, 0, 8, p);\r\n          ROUNDING_MODE = v;\r\n        }\r\n\r\n        // EXPONENTIAL_AT {number|number[]}\r\n        // Integer, -MAX to MAX inclusive or\r\n        // [integer -MAX to 0 inclusive, 0 to MAX inclusive].\r\n        // '[BigNumber Error] EXPONENTIAL_AT {not a primitive number|not an integer|out of range}: {v}'\r\n        if (obj.hasOwnProperty(p = 'EXPONENTIAL_AT')) {\r\n          v = obj[p];\r\n          if (v && v.pop) {\r\n            intCheck(v[0], -MAX, 0, p);\r\n            intCheck(v[1], 0, MAX, p);\r\n            TO_EXP_NEG = v[0];\r\n            TO_EXP_POS = v[1];\r\n          } else {\r\n            intCheck(v, -MAX, MAX, p);\r\n            TO_EXP_NEG = -(TO_EXP_POS = v < 0 ? -v : v);\r\n          }\r\n        }\r\n\r\n        // RANGE {number|number[]} Non-zero integer, -MAX to MAX inclusive or\r\n        // [integer -MAX to -1 inclusive, integer 1 to MAX inclusive].\r\n        // '[BigNumber Error] RANGE {not a primitive number|not an integer|out of range|cannot be zero}: {v}'\r\n        if (obj.hasOwnProperty(p = 'RANGE')) {\r\n          v = obj[p];\r\n          if (v && v.pop) {\r\n            intCheck(v[0], -MAX, -1, p);\r\n            intCheck(v[1], 1, MAX, p);\r\n            MIN_EXP = v[0];\r\n            MAX_EXP = v[1];\r\n          } else {\r\n            intCheck(v, -MAX, MAX, p);\r\n            if (v) {\r\n              MIN_EXP = -(MAX_EXP = v < 0 ? -v : v);\r\n            } else {\r\n              throw Error\r\n               (bignumberError + p + ' cannot be zero: ' + v);\r\n            }\r\n          }\r\n        }\r\n\r\n        // CRYPTO {boolean} true or false.\r\n        // '[BigNumber Error] CRYPTO not true or false: {v}'\r\n        // '[BigNumber Error] crypto unavailable'\r\n        if (obj.hasOwnProperty(p = 'CRYPTO')) {\r\n          v = obj[p];\r\n          if (v === !!v) {\r\n            if (v) {\r\n              if (typeof crypto != 'undefined' && crypto &&\r\n               (crypto.getRandomValues || crypto.randomBytes)) {\r\n                CRYPTO = v;\r\n              } else {\r\n                CRYPTO = !v;\r\n                throw Error\r\n                 (bignumberError + 'crypto unavailable');\r\n              }\r\n            } else {\r\n              CRYPTO = v;\r\n            }\r\n          } else {\r\n            throw Error\r\n             (bignumberError + p + ' not true or false: ' + v);\r\n          }\r\n        }\r\n\r\n        // MODULO_MODE {number} Integer, 0 to 9 inclusive.\r\n        // '[BigNumber Error] MODULO_MODE {not a primitive number|not an integer|out of range}: {v}'\r\n        if (obj.hasOwnProperty(p = 'MODULO_MODE')) {\r\n          v = obj[p];\r\n          intCheck(v, 0, 9, p);\r\n          MODULO_MODE = v;\r\n        }\r\n\r\n        // POW_PRECISION {number} Integer, 0 to MAX inclusive.\r\n        // '[BigNumber Error] POW_PRECISION {not a primitive number|not an integer|out of range}: {v}'\r\n        if (obj.hasOwnProperty(p = 'POW_PRECISION')) {\r\n          v = obj[p];\r\n          intCheck(v, 0, MAX, p);\r\n          POW_PRECISION = v;\r\n        }\r\n\r\n        // FORMAT {object}\r\n        // '[BigNumber Error] FORMAT not an object: {v}'\r\n        if (obj.hasOwnProperty(p = 'FORMAT')) {\r\n          v = obj[p];\r\n          if (typeof v == 'object') FORMAT = v;\r\n          else throw Error\r\n           (bignumberError + p + ' not an object: ' + v);\r\n        }\r\n\r\n        // ALPHABET {string}\r\n        // '[BigNumber Error] ALPHABET invalid: {v}'\r\n        if (obj.hasOwnProperty(p = 'ALPHABET')) {\r\n          v = obj[p];\r\n\r\n          // Disallow if less than two characters,\r\n          // or if it contains '+', '-', '.', whitespace, or a repeated character.\r\n          if (typeof v == 'string' && !/^.?$|[+\\-.\\s]|(.).*\\1/.test(v)) {\r\n            alphabetHasNormalDecimalDigits = v.slice(0, 10) == '0123456789';\r\n            ALPHABET = v;\r\n          } else {\r\n            throw Error\r\n             (bignumberError + p + ' invalid: ' + v);\r\n          }\r\n        }\r\n\r\n      } else {\r\n\r\n        // '[BigNumber Error] Object expected: {v}'\r\n        throw Error\r\n         (bignumberError + 'Object expected: ' + obj);\r\n      }\r\n    }\r\n\r\n    return {\r\n      DECIMAL_PLACES: DECIMAL_PLACES,\r\n      ROUNDING_MODE: ROUNDING_MODE,\r\n      EXPONENTIAL_AT: [TO_EXP_NEG, TO_EXP_POS],\r\n      RANGE: [MIN_EXP, MAX_EXP],\r\n      CRYPTO: CRYPTO,\r\n      MODULO_MODE: MODULO_MODE,\r\n      POW_PRECISION: POW_PRECISION,\r\n      FORMAT: FORMAT,\r\n      ALPHABET: ALPHABET\r\n    };\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if v is a BigNumber instance, otherwise return false.\r\n   *\r\n   * If BigNumber.DEBUG is true, throw if a BigNumber instance is not well-formed.\r\n   *\r\n   * v {any}\r\n   *\r\n   * '[BigNumber Error] Invalid BigNumber: {v}'\r\n   */\r\n  BigNumber.isBigNumber = function (v) {\r\n    if (!v || v._isBigNumber !== true) return false;\r\n    if (!BigNumber.DEBUG) return true;\r\n\r\n    var i, n,\r\n      c = v.c,\r\n      e = v.e,\r\n      s = v.s;\r\n\r\n    out: if ({}.toString.call(c) == '[object Array]') {\r\n\r\n      if ((s === 1 || s === -1) && e >= -MAX && e <= MAX && e === mathfloor(e)) {\r\n\r\n        // If the first element is zero, the BigNumber value must be zero.\r\n        if (c[0] === 0) {\r\n          if (e === 0 && c.length === 1) return true;\r\n          break out;\r\n        }\r\n\r\n        // Calculate number of digits that c[0] should have, based on the exponent.\r\n        i = (e + 1) % LOG_BASE;\r\n        if (i < 1) i += LOG_BASE;\r\n\r\n        // Calculate number of digits of c[0].\r\n        //if (Math.ceil(Math.log(c[0] + 1) / Math.LN10) == i) {\r\n        if (String(c[0]).length == i) {\r\n\r\n          for (i = 0; i < c.length; i++) {\r\n            n = c[i];\r\n            if (n < 0 || n >= BASE || n !== mathfloor(n)) break out;\r\n          }\r\n\r\n          // Last element cannot be zero, unless it is the only element.\r\n          if (n !== 0) return true;\r\n        }\r\n      }\r\n\r\n    // Infinity/NaN\r\n    } else if (c === null && e === null && (s === null || s === 1 || s === -1)) {\r\n      return true;\r\n    }\r\n\r\n    throw Error\r\n      (bignumberError + 'Invalid BigNumber: ' + v);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the maximum of the arguments.\r\n   *\r\n   * arguments {number|string|BigNumber}\r\n   */\r\n  BigNumber.maximum = BigNumber.max = function () {\r\n    return maxOrMin(arguments, -1);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the minimum of the arguments.\r\n   *\r\n   * arguments {number|string|BigNumber}\r\n   */\r\n  BigNumber.minimum = BigNumber.min = function () {\r\n    return maxOrMin(arguments, 1);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber with a random value equal to or greater than 0 and less than 1,\r\n   * and with dp, or DECIMAL_PLACES if dp is omitted, decimal places (or less if trailing\r\n   * zeros are produced).\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp}'\r\n   * '[BigNumber Error] crypto unavailable'\r\n   */\r\n  BigNumber.random = (function () {\r\n    var pow2_53 = 0x20000000000000;\r\n\r\n    // Return a 53 bit integer n, where 0 <= n < 9007199254740992.\r\n    // Check if Math.random() produces more than 32 bits of randomness.\r\n    // If it does, assume at least 53 bits are produced, otherwise assume at least 30 bits.\r\n    // 0x40000000 is 2^30, 0x800000 is 2^23, 0x1fffff is 2^21 - 1.\r\n    var random53bitInt = (Math.random() * pow2_53) & 0x1fffff\r\n     ? function () { return mathfloor(Math.random() * pow2_53); }\r\n     : function () { return ((Math.random() * 0x40000000 | 0) * 0x800000) +\r\n       (Math.random() * 0x800000 | 0); };\r\n\r\n    return function (dp) {\r\n      var a, b, e, k, v,\r\n        i = 0,\r\n        c = [],\r\n        rand = new BigNumber(ONE);\r\n\r\n      if (dp == null) dp = DECIMAL_PLACES;\r\n      else intCheck(dp, 0, MAX);\r\n\r\n      k = mathceil(dp / LOG_BASE);\r\n\r\n      if (CRYPTO) {\r\n\r\n        // Browsers supporting crypto.getRandomValues.\r\n        if (crypto.getRandomValues) {\r\n\r\n          a = crypto.getRandomValues(new Uint32Array(k *= 2));\r\n\r\n          for (; i < k;) {\r\n\r\n            // 53 bits:\r\n            // ((Math.pow(2, 32) - 1) * Math.pow(2, 21)).toString(2)\r\n            // 11111 11111111 11111111 11111111 11100000 00000000 00000000\r\n            // ((Math.pow(2, 32) - 1) >>> 11).toString(2)\r\n            //                                     11111 11111111 11111111\r\n            // 0x20000 is 2^21.\r\n            v = a[i] * 0x20000 + (a[i + 1] >>> 11);\r\n\r\n            // Rejection sampling:\r\n            // 0 <= v < 9007199254740992\r\n            // Probability that v >= 9e15, is\r\n            // 7199254740992 / 9007199254740992 ~= 0.0008, i.e. 1 in 1251\r\n            if (v >= 9e15) {\r\n              b = crypto.getRandomValues(new Uint32Array(2));\r\n              a[i] = b[0];\r\n              a[i + 1] = b[1];\r\n            } else {\r\n\r\n              // 0 <= v <= 8999999999999999\r\n              // 0 <= (v % 1e14) <= 99999999999999\r\n              c.push(v % 1e14);\r\n              i += 2;\r\n            }\r\n          }\r\n          i = k / 2;\r\n\r\n        // Node.js supporting crypto.randomBytes.\r\n        } else if (crypto.randomBytes) {\r\n\r\n          // buffer\r\n          a = crypto.randomBytes(k *= 7);\r\n\r\n          for (; i < k;) {\r\n\r\n            // 0x1000000000000 is 2^48, 0x10000000000 is 2^40\r\n            // 0x100000000 is 2^32, 0x1000000 is 2^24\r\n            // 11111 11111111 11111111 11111111 11111111 11111111 11111111\r\n            // 0 <= v < 9007199254740992\r\n            v = ((a[i] & 31) * 0x1000000000000) + (a[i + 1] * 0x10000000000) +\r\n               (a[i + 2] * 0x100000000) + (a[i + 3] * 0x1000000) +\r\n               (a[i + 4] << 16) + (a[i + 5] << 8) + a[i + 6];\r\n\r\n            if (v >= 9e15) {\r\n              crypto.randomBytes(7).copy(a, i);\r\n            } else {\r\n\r\n              // 0 <= (v % 1e14) <= 99999999999999\r\n              c.push(v % 1e14);\r\n              i += 7;\r\n            }\r\n          }\r\n          i = k / 7;\r\n        } else {\r\n          CRYPTO = false;\r\n          throw Error\r\n           (bignumberError + 'crypto unavailable');\r\n        }\r\n      }\r\n\r\n      // Use Math.random.\r\n      if (!CRYPTO) {\r\n\r\n        for (; i < k;) {\r\n          v = random53bitInt();\r\n          if (v < 9e15) c[i++] = v % 1e14;\r\n        }\r\n      }\r\n\r\n      k = c[--i];\r\n      dp %= LOG_BASE;\r\n\r\n      // Convert trailing digits to zeros according to dp.\r\n      if (k && dp) {\r\n        v = POWS_TEN[LOG_BASE - dp];\r\n        c[i] = mathfloor(k / v) * v;\r\n      }\r\n\r\n      // Remove trailing elements which are zero.\r\n      for (; c[i] === 0; c.pop(), i--);\r\n\r\n      // Zero?\r\n      if (i < 0) {\r\n        c = [e = 0];\r\n      } else {\r\n\r\n        // Remove leading elements which are zero and adjust exponent accordingly.\r\n        for (e = -1 ; c[0] === 0; c.splice(0, 1), e -= LOG_BASE);\r\n\r\n        // Count the digits of the first element of c to determine leading zeros, and...\r\n        for (i = 1, v = c[0]; v >= 10; v /= 10, i++);\r\n\r\n        // adjust the exponent accordingly.\r\n        if (i < LOG_BASE) e -= LOG_BASE - i;\r\n      }\r\n\r\n      rand.e = e;\r\n      rand.c = c;\r\n      return rand;\r\n    };\r\n  })();\r\n\r\n\r\n   /*\r\n   * Return a BigNumber whose value is the sum of the arguments.\r\n   *\r\n   * arguments {number|string|BigNumber}\r\n   */\r\n  BigNumber.sum = function () {\r\n    var i = 1,\r\n      args = arguments,\r\n      sum = new BigNumber(args[0]);\r\n    for (; i < args.length;) sum = sum.plus(args[i++]);\r\n    return sum;\r\n  };\r\n\r\n\r\n  // PRIVATE FUNCTIONS\r\n\r\n\r\n  // Called by BigNumber and BigNumber.prototype.toString.\r\n  convertBase = (function () {\r\n    var decimal = '0123456789';\r\n\r\n    /*\r\n     * Convert string of baseIn to an array of numbers of baseOut.\r\n     * Eg. toBaseOut('255', 10, 16) returns [15, 15].\r\n     * Eg. toBaseOut('ff', 16, 10) returns [2, 5, 5].\r\n     */\r\n    function toBaseOut(str, baseIn, baseOut, alphabet) {\r\n      var j,\r\n        arr = [0],\r\n        arrL,\r\n        i = 0,\r\n        len = str.length;\r\n\r\n      for (; i < len;) {\r\n        for (arrL = arr.length; arrL--; arr[arrL] *= baseIn);\r\n\r\n        arr[0] += alphabet.indexOf(str.charAt(i++));\r\n\r\n        for (j = 0; j < arr.length; j++) {\r\n\r\n          if (arr[j] > baseOut - 1) {\r\n            if (arr[j + 1] == null) arr[j + 1] = 0;\r\n            arr[j + 1] += arr[j] / baseOut | 0;\r\n            arr[j] %= baseOut;\r\n          }\r\n        }\r\n      }\r\n\r\n      return arr.reverse();\r\n    }\r\n\r\n    // Convert a numeric string of baseIn to a numeric string of baseOut.\r\n    // If the caller is toString, we are converting from base 10 to baseOut.\r\n    // If the caller is BigNumber, we are converting from baseIn to base 10.\r\n    return function (str, baseIn, baseOut, sign, callerIsToString) {\r\n      var alphabet, d, e, k, r, x, xc, y,\r\n        i = str.indexOf('.'),\r\n        dp = DECIMAL_PLACES,\r\n        rm = ROUNDING_MODE;\r\n\r\n      // Non-integer.\r\n      if (i >= 0) {\r\n        k = POW_PRECISION;\r\n\r\n        // Unlimited precision.\r\n        POW_PRECISION = 0;\r\n        str = str.replace('.', '');\r\n        y = new BigNumber(baseIn);\r\n        x = y.pow(str.length - i);\r\n        POW_PRECISION = k;\r\n\r\n        // Convert str as if an integer, then restore the fraction part by dividing the\r\n        // result by its base raised to a power.\r\n\r\n        y.c = toBaseOut(toFixedPoint(coeffToString(x.c), x.e, '0'),\r\n         10, baseOut, decimal);\r\n        y.e = y.c.length;\r\n      }\r\n\r\n      // Convert the number as integer.\r\n\r\n      xc = toBaseOut(str, baseIn, baseOut, callerIsToString\r\n       ? (alphabet = ALPHABET, decimal)\r\n       : (alphabet = decimal, ALPHABET));\r\n\r\n      // xc now represents str as an integer and converted to baseOut. e is the exponent.\r\n      e = k = xc.length;\r\n\r\n      // Remove trailing zeros.\r\n      for (; xc[--k] == 0; xc.pop());\r\n\r\n      // Zero?\r\n      if (!xc[0]) return alphabet.charAt(0);\r\n\r\n      // Does str represent an integer? If so, no need for the division.\r\n      if (i < 0) {\r\n        --e;\r\n      } else {\r\n        x.c = xc;\r\n        x.e = e;\r\n\r\n        // The sign is needed for correct rounding.\r\n        x.s = sign;\r\n        x = div(x, y, dp, rm, baseOut);\r\n        xc = x.c;\r\n        r = x.r;\r\n        e = x.e;\r\n      }\r\n\r\n      // xc now represents str converted to baseOut.\r\n\r\n      // THe index of the rounding digit.\r\n      d = e + dp + 1;\r\n\r\n      // The rounding digit: the digit to the right of the digit that may be rounded up.\r\n      i = xc[d];\r\n\r\n      // Look at the rounding digits and mode to determine whether to round up.\r\n\r\n      k = baseOut / 2;\r\n      r = r || d < 0 || xc[d + 1] != null;\r\n\r\n      r = rm < 4 ? (i != null || r) && (rm == 0 || rm == (x.s < 0 ? 3 : 2))\r\n            : i > k || i == k &&(rm == 4 || r || rm == 6 && xc[d - 1] & 1 ||\r\n             rm == (x.s < 0 ? 8 : 7));\r\n\r\n      // If the index of the rounding digit is not greater than zero, or xc represents\r\n      // zero, then the result of the base conversion is zero or, if rounding up, a value\r\n      // such as 0.00001.\r\n      if (d < 1 || !xc[0]) {\r\n\r\n        // 1^-dp or 0\r\n        str = r ? toFixedPoint(alphabet.charAt(1), -dp, alphabet.charAt(0)) : alphabet.charAt(0);\r\n      } else {\r\n\r\n        // Truncate xc to the required number of decimal places.\r\n        xc.length = d;\r\n\r\n        // Round up?\r\n        if (r) {\r\n\r\n          // Rounding up may mean the previous digit has to be rounded up and so on.\r\n          for (--baseOut; ++xc[--d] > baseOut;) {\r\n            xc[d] = 0;\r\n\r\n            if (!d) {\r\n              ++e;\r\n              xc = [1].concat(xc);\r\n            }\r\n          }\r\n        }\r\n\r\n        // Determine trailing zeros.\r\n        for (k = xc.length; !xc[--k];);\r\n\r\n        // E.g. [4, 11, 15] becomes 4bf.\r\n        for (i = 0, str = ''; i <= k; str += alphabet.charAt(xc[i++]));\r\n\r\n        // Add leading zeros, decimal point and trailing zeros as required.\r\n        str = toFixedPoint(str, e, alphabet.charAt(0));\r\n      }\r\n\r\n      // The caller will add the sign.\r\n      return str;\r\n    };\r\n  })();\r\n\r\n\r\n  // Perform division in the specified base. Called by div and convertBase.\r\n  div = (function () {\r\n\r\n    // Assume non-zero x and k.\r\n    function multiply(x, k, base) {\r\n      var m, temp, xlo, xhi,\r\n        carry = 0,\r\n        i = x.length,\r\n        klo = k % SQRT_BASE,\r\n        khi = k / SQRT_BASE | 0;\r\n\r\n      for (x = x.slice(); i--;) {\r\n        xlo = x[i] % SQRT_BASE;\r\n        xhi = x[i] / SQRT_BASE | 0;\r\n        m = khi * xlo + xhi * klo;\r\n        temp = klo * xlo + ((m % SQRT_BASE) * SQRT_BASE) + carry;\r\n        carry = (temp / base | 0) + (m / SQRT_BASE | 0) + khi * xhi;\r\n        x[i] = temp % base;\r\n      }\r\n\r\n      if (carry) x = [carry].concat(x);\r\n\r\n      return x;\r\n    }\r\n\r\n    function compare(a, b, aL, bL) {\r\n      var i, cmp;\r\n\r\n      if (aL != bL) {\r\n        cmp = aL > bL ? 1 : -1;\r\n      } else {\r\n\r\n        for (i = cmp = 0; i < aL; i++) {\r\n\r\n          if (a[i] != b[i]) {\r\n            cmp = a[i] > b[i] ? 1 : -1;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      return cmp;\r\n    }\r\n\r\n    function subtract(a, b, aL, base) {\r\n      var i = 0;\r\n\r\n      // Subtract b from a.\r\n      for (; aL--;) {\r\n        a[aL] -= i;\r\n        i = a[aL] < b[aL] ? 1 : 0;\r\n        a[aL] = i * base + a[aL] - b[aL];\r\n      }\r\n\r\n      // Remove leading zeros.\r\n      for (; !a[0] && a.length > 1; a.splice(0, 1));\r\n    }\r\n\r\n    // x: dividend, y: divisor.\r\n    return function (x, y, dp, rm, base) {\r\n      var cmp, e, i, more, n, prod, prodL, q, qc, rem, remL, rem0, xi, xL, yc0,\r\n        yL, yz,\r\n        s = x.s == y.s ? 1 : -1,\r\n        xc = x.c,\r\n        yc = y.c;\r\n\r\n      // Either NaN, Infinity or 0?\r\n      if (!xc || !xc[0] || !yc || !yc[0]) {\r\n\r\n        return new BigNumber(\r\n\r\n         // Return NaN if either NaN, or both Infinity or 0.\r\n         !x.s || !y.s || (xc ? yc && xc[0] == yc[0] : !yc) ? NaN :\r\n\r\n          // Return ±0 if x is ±0 or y is ±Infinity, or return ±Infinity as y is ±0.\r\n          xc && xc[0] == 0 || !yc ? s * 0 : s / 0\r\n       );\r\n      }\r\n\r\n      q = new BigNumber(s);\r\n      qc = q.c = [];\r\n      e = x.e - y.e;\r\n      s = dp + e + 1;\r\n\r\n      if (!base) {\r\n        base = BASE;\r\n        e = bitFloor(x.e / LOG_BASE) - bitFloor(y.e / LOG_BASE);\r\n        s = s / LOG_BASE | 0;\r\n      }\r\n\r\n      // Result exponent may be one less then the current value of e.\r\n      // The coefficients of the BigNumbers from convertBase may have trailing zeros.\r\n      for (i = 0; yc[i] == (xc[i] || 0); i++);\r\n\r\n      if (yc[i] > (xc[i] || 0)) e--;\r\n\r\n      if (s < 0) {\r\n        qc.push(1);\r\n        more = true;\r\n      } else {\r\n        xL = xc.length;\r\n        yL = yc.length;\r\n        i = 0;\r\n        s += 2;\r\n\r\n        // Normalise xc and yc so highest order digit of yc is >= base / 2.\r\n\r\n        n = mathfloor(base / (yc[0] + 1));\r\n\r\n        // Not necessary, but to handle odd bases where yc[0] == (base / 2) - 1.\r\n        // if (n > 1 || n++ == 1 && yc[0] < base / 2) {\r\n        if (n > 1) {\r\n          yc = multiply(yc, n, base);\r\n          xc = multiply(xc, n, base);\r\n          yL = yc.length;\r\n          xL = xc.length;\r\n        }\r\n\r\n        xi = yL;\r\n        rem = xc.slice(0, yL);\r\n        remL = rem.length;\r\n\r\n        // Add zeros to make remainder as long as divisor.\r\n        for (; remL < yL; rem[remL++] = 0);\r\n        yz = yc.slice();\r\n        yz = [0].concat(yz);\r\n        yc0 = yc[0];\r\n        if (yc[1] >= base / 2) yc0++;\r\n        // Not necessary, but to prevent trial digit n > base, when using base 3.\r\n        // else if (base == 3 && yc0 == 1) yc0 = 1 + 1e-15;\r\n\r\n        do {\r\n          n = 0;\r\n\r\n          // Compare divisor and remainder.\r\n          cmp = compare(yc, rem, yL, remL);\r\n\r\n          // If divisor < remainder.\r\n          if (cmp < 0) {\r\n\r\n            // Calculate trial digit, n.\r\n\r\n            rem0 = rem[0];\r\n            if (yL != remL) rem0 = rem0 * base + (rem[1] || 0);\r\n\r\n            // n is how many times the divisor goes into the current remainder.\r\n            n = mathfloor(rem0 / yc0);\r\n\r\n            //  Algorithm:\r\n            //  product = divisor multiplied by trial digit (n).\r\n            //  Compare product and remainder.\r\n            //  If product is greater than remainder:\r\n            //    Subtract divisor from product, decrement trial digit.\r\n            //  Subtract product from remainder.\r\n            //  If product was less than remainder at the last compare:\r\n            //    Compare new remainder and divisor.\r\n            //    If remainder is greater than divisor:\r\n            //      Subtract divisor from remainder, increment trial digit.\r\n\r\n            if (n > 1) {\r\n\r\n              // n may be > base only when base is 3.\r\n              if (n >= base) n = base - 1;\r\n\r\n              // product = divisor * trial digit.\r\n              prod = multiply(yc, n, base);\r\n              prodL = prod.length;\r\n              remL = rem.length;\r\n\r\n              // Compare product and remainder.\r\n              // If product > remainder then trial digit n too high.\r\n              // n is 1 too high about 5% of the time, and is not known to have\r\n              // ever been more than 1 too high.\r\n              while (compare(prod, rem, prodL, remL) == 1) {\r\n                n--;\r\n\r\n                // Subtract divisor from product.\r\n                subtract(prod, yL < prodL ? yz : yc, prodL, base);\r\n                prodL = prod.length;\r\n                cmp = 1;\r\n              }\r\n            } else {\r\n\r\n              // n is 0 or 1, cmp is -1.\r\n              // If n is 0, there is no need to compare yc and rem again below,\r\n              // so change cmp to 1 to avoid it.\r\n              // If n is 1, leave cmp as -1, so yc and rem are compared again.\r\n              if (n == 0) {\r\n\r\n                // divisor < remainder, so n must be at least 1.\r\n                cmp = n = 1;\r\n              }\r\n\r\n              // product = divisor\r\n              prod = yc.slice();\r\n              prodL = prod.length;\r\n            }\r\n\r\n            if (prodL < remL) prod = [0].concat(prod);\r\n\r\n            // Subtract product from remainder.\r\n            subtract(rem, prod, remL, base);\r\n            remL = rem.length;\r\n\r\n             // If product was < remainder.\r\n            if (cmp == -1) {\r\n\r\n              // Compare divisor and new remainder.\r\n              // If divisor < new remainder, subtract divisor from remainder.\r\n              // Trial digit n too low.\r\n              // n is 1 too low about 5% of the time, and very rarely 2 too low.\r\n              while (compare(yc, rem, yL, remL) < 1) {\r\n                n++;\r\n\r\n                // Subtract divisor from remainder.\r\n                subtract(rem, yL < remL ? yz : yc, remL, base);\r\n                remL = rem.length;\r\n              }\r\n            }\r\n          } else if (cmp === 0) {\r\n            n++;\r\n            rem = [0];\r\n          } // else cmp === 1 and n will be 0\r\n\r\n          // Add the next digit, n, to the result array.\r\n          qc[i++] = n;\r\n\r\n          // Update the remainder.\r\n          if (rem[0]) {\r\n            rem[remL++] = xc[xi] || 0;\r\n          } else {\r\n            rem = [xc[xi]];\r\n            remL = 1;\r\n          }\r\n        } while ((xi++ < xL || rem[0] != null) && s--);\r\n\r\n        more = rem[0] != null;\r\n\r\n        // Leading zero?\r\n        if (!qc[0]) qc.splice(0, 1);\r\n      }\r\n\r\n      if (base == BASE) {\r\n\r\n        // To calculate q.e, first get the number of digits of qc[0].\r\n        for (i = 1, s = qc[0]; s >= 10; s /= 10, i++);\r\n\r\n        round(q, dp + (q.e = i + e * LOG_BASE - 1) + 1, rm, more);\r\n\r\n      // Caller is convertBase.\r\n      } else {\r\n        q.e = e;\r\n        q.r = +more;\r\n      }\r\n\r\n      return q;\r\n    };\r\n  })();\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of BigNumber n in fixed-point or exponential\r\n   * notation rounded to the specified decimal places or significant digits.\r\n   *\r\n   * n: a BigNumber.\r\n   * i: the index of the last digit required (i.e. the digit that may be rounded up).\r\n   * rm: the rounding mode.\r\n   * id: 1 (toExponential) or 2 (toPrecision).\r\n   */\r\n  function format(n, i, rm, id) {\r\n    var c0, e, ne, len, str;\r\n\r\n    if (rm == null) rm = ROUNDING_MODE;\r\n    else intCheck(rm, 0, 8);\r\n\r\n    if (!n.c) return n.toString();\r\n\r\n    c0 = n.c[0];\r\n    ne = n.e;\r\n\r\n    if (i == null) {\r\n      str = coeffToString(n.c);\r\n      str = id == 1 || id == 2 && (ne <= TO_EXP_NEG || ne >= TO_EXP_POS)\r\n       ? toExponential(str, ne)\r\n       : toFixedPoint(str, ne, '0');\r\n    } else {\r\n      n = round(new BigNumber(n), i, rm);\r\n\r\n      // n.e may have changed if the value was rounded up.\r\n      e = n.e;\r\n\r\n      str = coeffToString(n.c);\r\n      len = str.length;\r\n\r\n      // toPrecision returns exponential notation if the number of significant digits\r\n      // specified is less than the number of digits necessary to represent the integer\r\n      // part of the value in fixed-point notation.\r\n\r\n      // Exponential notation.\r\n      if (id == 1 || id == 2 && (i <= e || e <= TO_EXP_NEG)) {\r\n\r\n        // Append zeros?\r\n        for (; len < i; str += '0', len++);\r\n        str = toExponential(str, e);\r\n\r\n      // Fixed-point notation.\r\n      } else {\r\n        i -= ne;\r\n        str = toFixedPoint(str, e, '0');\r\n\r\n        // Append zeros?\r\n        if (e + 1 > len) {\r\n          if (--i > 0) for (str += '.'; i--; str += '0');\r\n        } else {\r\n          i += e - len;\r\n          if (i > 0) {\r\n            if (e + 1 == len) str += '.';\r\n            for (; i--; str += '0');\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return n.s < 0 && c0 ? '-' + str : str;\r\n  }\r\n\r\n\r\n  // Handle BigNumber.max and BigNumber.min.\r\n  // If any number is NaN, return NaN.\r\n  function maxOrMin(args, n) {\r\n    var k, y,\r\n      i = 1,\r\n      x = new BigNumber(args[0]);\r\n\r\n    for (; i < args.length; i++) {\r\n      y = new BigNumber(args[i]);\r\n      if (!y.s || (k = compare(x, y)) === n || k === 0 && x.s === n) {\r\n        x = y;\r\n      }\r\n    }\r\n\r\n    return x;\r\n  }\r\n\r\n\r\n  /*\r\n   * Strip trailing zeros, calculate base 10 exponent and check against MIN_EXP and MAX_EXP.\r\n   * Called by minus, plus and times.\r\n   */\r\n  function normalise(n, c, e) {\r\n    var i = 1,\r\n      j = c.length;\r\n\r\n     // Remove trailing zeros.\r\n    for (; !c[--j]; c.pop());\r\n\r\n    // Calculate the base 10 exponent. First get the number of digits of c[0].\r\n    for (j = c[0]; j >= 10; j /= 10, i++);\r\n\r\n    // Overflow?\r\n    if ((e = i + e * LOG_BASE - 1) > MAX_EXP) {\r\n\r\n      // Infinity.\r\n      n.c = n.e = null;\r\n\r\n    // Underflow?\r\n    } else if (e < MIN_EXP) {\r\n\r\n      // Zero.\r\n      n.c = [n.e = 0];\r\n    } else {\r\n      n.e = e;\r\n      n.c = c;\r\n    }\r\n\r\n    return n;\r\n  }\r\n\r\n\r\n  // Handle values that fail the validity test in BigNumber.\r\n  parseNumeric = (function () {\r\n    var basePrefix = /^(-?)0([xbo])(?=\\w[\\w.]*$)/i,\r\n      dotAfter = /^([^.]+)\\.$/,\r\n      dotBefore = /^\\.([^.]+)$/,\r\n      isInfinityOrNaN = /^-?(Infinity|NaN)$/,\r\n      whitespaceOrPlus = /^\\s*\\+(?=[\\w.])|^\\s+|\\s+$/g;\r\n\r\n    return function (x, str, isNum, b) {\r\n      var base,\r\n        s = isNum ? str : str.replace(whitespaceOrPlus, '');\r\n\r\n      // No exception on ±Infinity or NaN.\r\n      if (isInfinityOrNaN.test(s)) {\r\n        x.s = isNaN(s) ? null : s < 0 ? -1 : 1;\r\n      } else {\r\n        if (!isNum) {\r\n\r\n          // basePrefix = /^(-?)0([xbo])(?=\\w[\\w.]*$)/i\r\n          s = s.replace(basePrefix, function (m, p1, p2) {\r\n            base = (p2 = p2.toLowerCase()) == 'x' ? 16 : p2 == 'b' ? 2 : 8;\r\n            return !b || b == base ? p1 : m;\r\n          });\r\n\r\n          if (b) {\r\n            base = b;\r\n\r\n            // E.g. '1.' to '1', '.1' to '0.1'\r\n            s = s.replace(dotAfter, '$1').replace(dotBefore, '0.$1');\r\n          }\r\n\r\n          if (str != s) return new BigNumber(s, base);\r\n        }\r\n\r\n        // '[BigNumber Error] Not a number: {n}'\r\n        // '[BigNumber Error] Not a base {b} number: {n}'\r\n        if (BigNumber.DEBUG) {\r\n          throw Error\r\n            (bignumberError + 'Not a' + (b ? ' base ' + b : '') + ' number: ' + str);\r\n        }\r\n\r\n        // NaN\r\n        x.s = null;\r\n      }\r\n\r\n      x.c = x.e = null;\r\n    }\r\n  })();\r\n\r\n\r\n  /*\r\n   * Round x to sd significant digits using rounding mode rm. Check for over/under-flow.\r\n   * If r is truthy, it is known that there are more digits after the rounding digit.\r\n   */\r\n  function round(x, sd, rm, r) {\r\n    var d, i, j, k, n, ni, rd,\r\n      xc = x.c,\r\n      pows10 = POWS_TEN;\r\n\r\n    // if x is not Infinity or NaN...\r\n    if (xc) {\r\n\r\n      // rd is the rounding digit, i.e. the digit after the digit that may be rounded up.\r\n      // n is a base 1e14 number, the value of the element of array x.c containing rd.\r\n      // ni is the index of n within x.c.\r\n      // d is the number of digits of n.\r\n      // i is the index of rd within n including leading zeros.\r\n      // j is the actual index of rd within n (if < 0, rd is a leading zero).\r\n      out: {\r\n\r\n        // Get the number of digits of the first element of xc.\r\n        for (d = 1, k = xc[0]; k >= 10; k /= 10, d++);\r\n        i = sd - d;\r\n\r\n        // If the rounding digit is in the first element of xc...\r\n        if (i < 0) {\r\n          i += LOG_BASE;\r\n          j = sd;\r\n          n = xc[ni = 0];\r\n\r\n          // Get the rounding digit at index j of n.\r\n          rd = mathfloor(n / pows10[d - j - 1] % 10);\r\n        } else {\r\n          ni = mathceil((i + 1) / LOG_BASE);\r\n\r\n          if (ni >= xc.length) {\r\n\r\n            if (r) {\r\n\r\n              // Needed by sqrt.\r\n              for (; xc.length <= ni; xc.push(0));\r\n              n = rd = 0;\r\n              d = 1;\r\n              i %= LOG_BASE;\r\n              j = i - LOG_BASE + 1;\r\n            } else {\r\n              break out;\r\n            }\r\n          } else {\r\n            n = k = xc[ni];\r\n\r\n            // Get the number of digits of n.\r\n            for (d = 1; k >= 10; k /= 10, d++);\r\n\r\n            // Get the index of rd within n.\r\n            i %= LOG_BASE;\r\n\r\n            // Get the index of rd within n, adjusted for leading zeros.\r\n            // The number of leading zeros of n is given by LOG_BASE - d.\r\n            j = i - LOG_BASE + d;\r\n\r\n            // Get the rounding digit at index j of n.\r\n            rd = j < 0 ? 0 : mathfloor(n / pows10[d - j - 1] % 10);\r\n          }\r\n        }\r\n\r\n        r = r || sd < 0 ||\r\n\r\n        // Are there any non-zero digits after the rounding digit?\r\n        // The expression  n % pows10[d - j - 1]  returns all digits of n to the right\r\n        // of the digit at j, e.g. if n is 908714 and j is 2, the expression gives 714.\r\n         xc[ni + 1] != null || (j < 0 ? n : n % pows10[d - j - 1]);\r\n\r\n        r = rm < 4\r\n         ? (rd || r) && (rm == 0 || rm == (x.s < 0 ? 3 : 2))\r\n         : rd > 5 || rd == 5 && (rm == 4 || r || rm == 6 &&\r\n\r\n          // Check whether the digit to the left of the rounding digit is odd.\r\n          ((i > 0 ? j > 0 ? n / pows10[d - j] : 0 : xc[ni - 1]) % 10) & 1 ||\r\n           rm == (x.s < 0 ? 8 : 7));\r\n\r\n        if (sd < 1 || !xc[0]) {\r\n          xc.length = 0;\r\n\r\n          if (r) {\r\n\r\n            // Convert sd to decimal places.\r\n            sd -= x.e + 1;\r\n\r\n            // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n            xc[0] = pows10[(LOG_BASE - sd % LOG_BASE) % LOG_BASE];\r\n            x.e = -sd || 0;\r\n          } else {\r\n\r\n            // Zero.\r\n            xc[0] = x.e = 0;\r\n          }\r\n\r\n          return x;\r\n        }\r\n\r\n        // Remove excess digits.\r\n        if (i == 0) {\r\n          xc.length = ni;\r\n          k = 1;\r\n          ni--;\r\n        } else {\r\n          xc.length = ni + 1;\r\n          k = pows10[LOG_BASE - i];\r\n\r\n          // E.g. 56700 becomes 56000 if 7 is the rounding digit.\r\n          // j > 0 means i > number of leading zeros of n.\r\n          xc[ni] = j > 0 ? mathfloor(n / pows10[d - j] % pows10[j]) * k : 0;\r\n        }\r\n\r\n        // Round up?\r\n        if (r) {\r\n\r\n          for (; ;) {\r\n\r\n            // If the digit to be rounded up is in the first element of xc...\r\n            if (ni == 0) {\r\n\r\n              // i will be the length of xc[0] before k is added.\r\n              for (i = 1, j = xc[0]; j >= 10; j /= 10, i++);\r\n              j = xc[0] += k;\r\n              for (k = 1; j >= 10; j /= 10, k++);\r\n\r\n              // if i != k the length has increased.\r\n              if (i != k) {\r\n                x.e++;\r\n                if (xc[0] == BASE) xc[0] = 1;\r\n              }\r\n\r\n              break;\r\n            } else {\r\n              xc[ni] += k;\r\n              if (xc[ni] != BASE) break;\r\n              xc[ni--] = 0;\r\n              k = 1;\r\n            }\r\n          }\r\n        }\r\n\r\n        // Remove trailing zeros.\r\n        for (i = xc.length; xc[--i] === 0; xc.pop());\r\n      }\r\n\r\n      // Overflow? Infinity.\r\n      if (x.e > MAX_EXP) {\r\n        x.c = x.e = null;\r\n\r\n      // Underflow? Zero.\r\n      } else if (x.e < MIN_EXP) {\r\n        x.c = [x.e = 0];\r\n      }\r\n    }\r\n\r\n    return x;\r\n  }\r\n\r\n\r\n  function valueOf(n) {\r\n    var str,\r\n      e = n.e;\r\n\r\n    if (e === null) return n.toString();\r\n\r\n    str = coeffToString(n.c);\r\n\r\n    str = e <= TO_EXP_NEG || e >= TO_EXP_POS\r\n      ? toExponential(str, e)\r\n      : toFixedPoint(str, e, '0');\r\n\r\n    return n.s < 0 ? '-' + str : str;\r\n  }\r\n\r\n\r\n  // PROTOTYPE/INSTANCE METHODS\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the absolute value of this BigNumber.\r\n   */\r\n  P.absoluteValue = P.abs = function () {\r\n    var x = new BigNumber(this);\r\n    if (x.s < 0) x.s = 1;\r\n    return x;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return\r\n   *   1 if the value of this BigNumber is greater than the value of BigNumber(y, b),\r\n   *   -1 if the value of this BigNumber is less than the value of BigNumber(y, b),\r\n   *   0 if they have the same value,\r\n   *   or null if the value of either is NaN.\r\n   */\r\n  P.comparedTo = function (y, b) {\r\n    return compare(this, new BigNumber(y, b));\r\n  };\r\n\r\n\r\n  /*\r\n   * If dp is undefined or null or true or false, return the number of decimal places of the\r\n   * value of this BigNumber, or null if the value of this BigNumber is ±Infinity or NaN.\r\n   *\r\n   * Otherwise, if dp is a number, return a new BigNumber whose value is the value of this\r\n   * BigNumber rounded to a maximum of dp decimal places using rounding mode rm, or\r\n   * ROUNDING_MODE if rm is omitted.\r\n   *\r\n   * [dp] {number} Decimal places: integer, 0 to MAX inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n   */\r\n  P.decimalPlaces = P.dp = function (dp, rm) {\r\n    var c, n, v,\r\n      x = this;\r\n\r\n    if (dp != null) {\r\n      intCheck(dp, 0, MAX);\r\n      if (rm == null) rm = ROUNDING_MODE;\r\n      else intCheck(rm, 0, 8);\r\n\r\n      return round(new BigNumber(x), dp + x.e + 1, rm);\r\n    }\r\n\r\n    if (!(c = x.c)) return null;\r\n    n = ((v = c.length - 1) - bitFloor(this.e / LOG_BASE)) * LOG_BASE;\r\n\r\n    // Subtract the number of trailing zeros of the last number.\r\n    if (v = c[v]) for (; v % 10 == 0; v /= 10, n--);\r\n    if (n < 0) n = 0;\r\n\r\n    return n;\r\n  };\r\n\r\n\r\n  /*\r\n   *  n / 0 = I\r\n   *  n / N = N\r\n   *  n / I = 0\r\n   *  0 / n = 0\r\n   *  0 / 0 = N\r\n   *  0 / N = N\r\n   *  0 / I = 0\r\n   *  N / n = N\r\n   *  N / 0 = N\r\n   *  N / N = N\r\n   *  N / I = N\r\n   *  I / n = I\r\n   *  I / 0 = I\r\n   *  I / N = N\r\n   *  I / I = N\r\n   *\r\n   * Return a new BigNumber whose value is the value of this BigNumber divided by the value of\r\n   * BigNumber(y, b), rounded according to DECIMAL_PLACES and ROUNDING_MODE.\r\n   */\r\n  P.dividedBy = P.div = function (y, b) {\r\n    return div(this, new BigNumber(y, b), DECIMAL_PLACES, ROUNDING_MODE);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the integer part of dividing the value of this\r\n   * BigNumber by the value of BigNumber(y, b).\r\n   */\r\n  P.dividedToIntegerBy = P.idiv = function (y, b) {\r\n    return div(this, new BigNumber(y, b), 0, 1);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a BigNumber whose value is the value of this BigNumber exponentiated by n.\r\n   *\r\n   * If m is present, return the result modulo m.\r\n   * If n is negative round according to DECIMAL_PLACES and ROUNDING_MODE.\r\n   * If POW_PRECISION is non-zero and m is not present, round to POW_PRECISION using ROUNDING_MODE.\r\n   *\r\n   * The modular power operation works efficiently when x, n, and m are integers, otherwise it\r\n   * is equivalent to calculating x.exponentiatedBy(n).modulo(m) with a POW_PRECISION of 0.\r\n   *\r\n   * n {number|string|BigNumber} The exponent. An integer.\r\n   * [m] {number|string|BigNumber} The modulus.\r\n   *\r\n   * '[BigNumber Error] Exponent not an integer: {n}'\r\n   */\r\n  P.exponentiatedBy = P.pow = function (n, m) {\r\n    var half, isModExp, i, k, more, nIsBig, nIsNeg, nIsOdd, y,\r\n      x = this;\r\n\r\n    n = new BigNumber(n);\r\n\r\n    // Allow NaN and ±Infinity, but not other non-integers.\r\n    if (n.c && !n.isInteger()) {\r\n      throw Error\r\n        (bignumberError + 'Exponent not an integer: ' + valueOf(n));\r\n    }\r\n\r\n    if (m != null) m = new BigNumber(m);\r\n\r\n    // Exponent of MAX_SAFE_INTEGER is 15.\r\n    nIsBig = n.e > 14;\r\n\r\n    // If x is NaN, ±Infinity, ±0 or ±1, or n is ±Infinity, NaN or ±0.\r\n    if (!x.c || !x.c[0] || x.c[0] == 1 && !x.e && x.c.length == 1 || !n.c || !n.c[0]) {\r\n\r\n      // The sign of the result of pow when x is negative depends on the evenness of n.\r\n      // If +n overflows to ±Infinity, the evenness of n would be not be known.\r\n      y = new BigNumber(Math.pow(+valueOf(x), nIsBig ? n.s * (2 - isOdd(n)) : +valueOf(n)));\r\n      return m ? y.mod(m) : y;\r\n    }\r\n\r\n    nIsNeg = n.s < 0;\r\n\r\n    if (m) {\r\n\r\n      // x % m returns NaN if abs(m) is zero, or m is NaN.\r\n      if (m.c ? !m.c[0] : !m.s) return new BigNumber(NaN);\r\n\r\n      isModExp = !nIsNeg && x.isInteger() && m.isInteger();\r\n\r\n      if (isModExp) x = x.mod(m);\r\n\r\n    // Overflow to ±Infinity: >=2**1e10 or >=1.0000024**1e15.\r\n    // Underflow to ±0: <=0.79**1e10 or <=0.9999975**1e15.\r\n    } else if (n.e > 9 && (x.e > 0 || x.e < -1 || (x.e == 0\r\n      // [1, 240000000]\r\n      ? x.c[0] > 1 || nIsBig && x.c[1] >= 24e7\r\n      // [80000000000000]  [99999750000000]\r\n      : x.c[0] < 8e13 || nIsBig && x.c[0] <= 9999975e7))) {\r\n\r\n      // If x is negative and n is odd, k = -0, else k = 0.\r\n      k = x.s < 0 && isOdd(n) ? -0 : 0;\r\n\r\n      // If x >= 1, k = ±Infinity.\r\n      if (x.e > -1) k = 1 / k;\r\n\r\n      // If n is negative return ±0, else return ±Infinity.\r\n      return new BigNumber(nIsNeg ? 1 / k : k);\r\n\r\n    } else if (POW_PRECISION) {\r\n\r\n      // Truncating each coefficient array to a length of k after each multiplication\r\n      // equates to truncating significant digits to POW_PRECISION + [28, 41],\r\n      // i.e. there will be a minimum of 28 guard digits retained.\r\n      k = mathceil(POW_PRECISION / LOG_BASE + 2);\r\n    }\r\n\r\n    if (nIsBig) {\r\n      half = new BigNumber(0.5);\r\n      if (nIsNeg) n.s = 1;\r\n      nIsOdd = isOdd(n);\r\n    } else {\r\n      i = Math.abs(+valueOf(n));\r\n      nIsOdd = i % 2;\r\n    }\r\n\r\n    y = new BigNumber(ONE);\r\n\r\n    // Performs 54 loop iterations for n of 9007199254740991.\r\n    for (; ;) {\r\n\r\n      if (nIsOdd) {\r\n        y = y.times(x);\r\n        if (!y.c) break;\r\n\r\n        if (k) {\r\n          if (y.c.length > k) y.c.length = k;\r\n        } else if (isModExp) {\r\n          y = y.mod(m);    //y = y.minus(div(y, m, 0, MODULO_MODE).times(m));\r\n        }\r\n      }\r\n\r\n      if (i) {\r\n        i = mathfloor(i / 2);\r\n        if (i === 0) break;\r\n        nIsOdd = i % 2;\r\n      } else {\r\n        n = n.times(half);\r\n        round(n, n.e + 1, 1);\r\n\r\n        if (n.e > 14) {\r\n          nIsOdd = isOdd(n);\r\n        } else {\r\n          i = +valueOf(n);\r\n          if (i === 0) break;\r\n          nIsOdd = i % 2;\r\n        }\r\n      }\r\n\r\n      x = x.times(x);\r\n\r\n      if (k) {\r\n        if (x.c && x.c.length > k) x.c.length = k;\r\n      } else if (isModExp) {\r\n        x = x.mod(m);    //x = x.minus(div(x, m, 0, MODULO_MODE).times(m));\r\n      }\r\n    }\r\n\r\n    if (isModExp) return y;\r\n    if (nIsNeg) y = ONE.div(y);\r\n\r\n    return m ? y.mod(m) : k ? round(y, POW_PRECISION, ROUNDING_MODE, more) : y;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the value of this BigNumber rounded to an integer\r\n   * using rounding mode rm, or ROUNDING_MODE if rm is omitted.\r\n   *\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {rm}'\r\n   */\r\n  P.integerValue = function (rm) {\r\n    var n = new BigNumber(this);\r\n    if (rm == null) rm = ROUNDING_MODE;\r\n    else intCheck(rm, 0, 8);\r\n    return round(n, n.e + 1, rm);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is equal to the value of BigNumber(y, b),\r\n   * otherwise return false.\r\n   */\r\n  P.isEqualTo = P.eq = function (y, b) {\r\n    return compare(this, new BigNumber(y, b)) === 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is a finite number, otherwise return false.\r\n   */\r\n  P.isFinite = function () {\r\n    return !!this.c;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is greater than the value of BigNumber(y, b),\r\n   * otherwise return false.\r\n   */\r\n  P.isGreaterThan = P.gt = function (y, b) {\r\n    return compare(this, new BigNumber(y, b)) > 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is greater than or equal to the value of\r\n   * BigNumber(y, b), otherwise return false.\r\n   */\r\n  P.isGreaterThanOrEqualTo = P.gte = function (y, b) {\r\n    return (b = compare(this, new BigNumber(y, b))) === 1 || b === 0;\r\n\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is an integer, otherwise return false.\r\n   */\r\n  P.isInteger = function () {\r\n    return !!this.c && bitFloor(this.e / LOG_BASE) > this.c.length - 2;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is less than the value of BigNumber(y, b),\r\n   * otherwise return false.\r\n   */\r\n  P.isLessThan = P.lt = function (y, b) {\r\n    return compare(this, new BigNumber(y, b)) < 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is less than or equal to the value of\r\n   * BigNumber(y, b), otherwise return false.\r\n   */\r\n  P.isLessThanOrEqualTo = P.lte = function (y, b) {\r\n    return (b = compare(this, new BigNumber(y, b))) === -1 || b === 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is NaN, otherwise return false.\r\n   */\r\n  P.isNaN = function () {\r\n    return !this.s;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is negative, otherwise return false.\r\n   */\r\n  P.isNegative = function () {\r\n    return this.s < 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is positive, otherwise return false.\r\n   */\r\n  P.isPositive = function () {\r\n    return this.s > 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is 0 or -0, otherwise return false.\r\n   */\r\n  P.isZero = function () {\r\n    return !!this.c && this.c[0] == 0;\r\n  };\r\n\r\n\r\n  /*\r\n   *  n - 0 = n\r\n   *  n - N = N\r\n   *  n - I = -I\r\n   *  0 - n = -n\r\n   *  0 - 0 = 0\r\n   *  0 - N = N\r\n   *  0 - I = -I\r\n   *  N - n = N\r\n   *  N - 0 = N\r\n   *  N - N = N\r\n   *  N - I = N\r\n   *  I - n = I\r\n   *  I - 0 = I\r\n   *  I - N = N\r\n   *  I - I = N\r\n   *\r\n   * Return a new BigNumber whose value is the value of this BigNumber minus the value of\r\n   * BigNumber(y, b).\r\n   */\r\n  P.minus = function (y, b) {\r\n    var i, j, t, xLTy,\r\n      x = this,\r\n      a = x.s;\r\n\r\n    y = new BigNumber(y, b);\r\n    b = y.s;\r\n\r\n    // Either NaN?\r\n    if (!a || !b) return new BigNumber(NaN);\r\n\r\n    // Signs differ?\r\n    if (a != b) {\r\n      y.s = -b;\r\n      return x.plus(y);\r\n    }\r\n\r\n    var xe = x.e / LOG_BASE,\r\n      ye = y.e / LOG_BASE,\r\n      xc = x.c,\r\n      yc = y.c;\r\n\r\n    if (!xe || !ye) {\r\n\r\n      // Either Infinity?\r\n      if (!xc || !yc) return xc ? (y.s = -b, y) : new BigNumber(yc ? x : NaN);\r\n\r\n      // Either zero?\r\n      if (!xc[0] || !yc[0]) {\r\n\r\n        // Return y if y is non-zero, x if x is non-zero, or zero if both are zero.\r\n        return yc[0] ? (y.s = -b, y) : new BigNumber(xc[0] ? x :\r\n\r\n         // IEEE 754 (2008) 6.3: n - n = -0 when rounding to -Infinity\r\n         ROUNDING_MODE == 3 ? -0 : 0);\r\n      }\r\n    }\r\n\r\n    xe = bitFloor(xe);\r\n    ye = bitFloor(ye);\r\n    xc = xc.slice();\r\n\r\n    // Determine which is the bigger number.\r\n    if (a = xe - ye) {\r\n\r\n      if (xLTy = a < 0) {\r\n        a = -a;\r\n        t = xc;\r\n      } else {\r\n        ye = xe;\r\n        t = yc;\r\n      }\r\n\r\n      t.reverse();\r\n\r\n      // Prepend zeros to equalise exponents.\r\n      for (b = a; b--; t.push(0));\r\n      t.reverse();\r\n    } else {\r\n\r\n      // Exponents equal. Check digit by digit.\r\n      j = (xLTy = (a = xc.length) < (b = yc.length)) ? a : b;\r\n\r\n      for (a = b = 0; b < j; b++) {\r\n\r\n        if (xc[b] != yc[b]) {\r\n          xLTy = xc[b] < yc[b];\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    // x < y? Point xc to the array of the bigger number.\r\n    if (xLTy) {\r\n      t = xc;\r\n      xc = yc;\r\n      yc = t;\r\n      y.s = -y.s;\r\n    }\r\n\r\n    b = (j = yc.length) - (i = xc.length);\r\n\r\n    // Append zeros to xc if shorter.\r\n    // No need to add zeros to yc if shorter as subtract only needs to start at yc.length.\r\n    if (b > 0) for (; b--; xc[i++] = 0);\r\n    b = BASE - 1;\r\n\r\n    // Subtract yc from xc.\r\n    for (; j > a;) {\r\n\r\n      if (xc[--j] < yc[j]) {\r\n        for (i = j; i && !xc[--i]; xc[i] = b);\r\n        --xc[i];\r\n        xc[j] += BASE;\r\n      }\r\n\r\n      xc[j] -= yc[j];\r\n    }\r\n\r\n    // Remove leading zeros and adjust exponent accordingly.\r\n    for (; xc[0] == 0; xc.splice(0, 1), --ye);\r\n\r\n    // Zero?\r\n    if (!xc[0]) {\r\n\r\n      // Following IEEE 754 (2008) 6.3,\r\n      // n - n = +0  but  n - n = -0  when rounding towards -Infinity.\r\n      y.s = ROUNDING_MODE == 3 ? -1 : 1;\r\n      y.c = [y.e = 0];\r\n      return y;\r\n    }\r\n\r\n    // No need to check for Infinity as +x - +y != Infinity && -x - -y != Infinity\r\n    // for finite x and y.\r\n    return normalise(y, xc, ye);\r\n  };\r\n\r\n\r\n  /*\r\n   *   n % 0 =  N\r\n   *   n % N =  N\r\n   *   n % I =  n\r\n   *   0 % n =  0\r\n   *  -0 % n = -0\r\n   *   0 % 0 =  N\r\n   *   0 % N =  N\r\n   *   0 % I =  0\r\n   *   N % n =  N\r\n   *   N % 0 =  N\r\n   *   N % N =  N\r\n   *   N % I =  N\r\n   *   I % n =  N\r\n   *   I % 0 =  N\r\n   *   I % N =  N\r\n   *   I % I =  N\r\n   *\r\n   * Return a new BigNumber whose value is the value of this BigNumber modulo the value of\r\n   * BigNumber(y, b). The result depends on the value of MODULO_MODE.\r\n   */\r\n  P.modulo = P.mod = function (y, b) {\r\n    var q, s,\r\n      x = this;\r\n\r\n    y = new BigNumber(y, b);\r\n\r\n    // Return NaN if x is Infinity or NaN, or y is NaN or zero.\r\n    if (!x.c || !y.s || y.c && !y.c[0]) {\r\n      return new BigNumber(NaN);\r\n\r\n    // Return x if y is Infinity or x is zero.\r\n    } else if (!y.c || x.c && !x.c[0]) {\r\n      return new BigNumber(x);\r\n    }\r\n\r\n    if (MODULO_MODE == 9) {\r\n\r\n      // Euclidian division: q = sign(y) * floor(x / abs(y))\r\n      // r = x - qy    where  0 <= r < abs(y)\r\n      s = y.s;\r\n      y.s = 1;\r\n      q = div(x, y, 0, 3);\r\n      y.s = s;\r\n      q.s *= s;\r\n    } else {\r\n      q = div(x, y, 0, MODULO_MODE);\r\n    }\r\n\r\n    y = x.minus(q.times(y));\r\n\r\n    // To match JavaScript %, ensure sign of zero is sign of dividend.\r\n    if (!y.c[0] && MODULO_MODE == 1) y.s = x.s;\r\n\r\n    return y;\r\n  };\r\n\r\n\r\n  /*\r\n   *  n * 0 = 0\r\n   *  n * N = N\r\n   *  n * I = I\r\n   *  0 * n = 0\r\n   *  0 * 0 = 0\r\n   *  0 * N = N\r\n   *  0 * I = N\r\n   *  N * n = N\r\n   *  N * 0 = N\r\n   *  N * N = N\r\n   *  N * I = N\r\n   *  I * n = I\r\n   *  I * 0 = N\r\n   *  I * N = N\r\n   *  I * I = I\r\n   *\r\n   * Return a new BigNumber whose value is the value of this BigNumber multiplied by the value\r\n   * of BigNumber(y, b).\r\n   */\r\n  P.multipliedBy = P.times = function (y, b) {\r\n    var c, e, i, j, k, m, xcL, xlo, xhi, ycL, ylo, yhi, zc,\r\n      base, sqrtBase,\r\n      x = this,\r\n      xc = x.c,\r\n      yc = (y = new BigNumber(y, b)).c;\r\n\r\n    // Either NaN, ±Infinity or ±0?\r\n    if (!xc || !yc || !xc[0] || !yc[0]) {\r\n\r\n      // Return NaN if either is NaN, or one is 0 and the other is Infinity.\r\n      if (!x.s || !y.s || xc && !xc[0] && !yc || yc && !yc[0] && !xc) {\r\n        y.c = y.e = y.s = null;\r\n      } else {\r\n        y.s *= x.s;\r\n\r\n        // Return ±Infinity if either is ±Infinity.\r\n        if (!xc || !yc) {\r\n          y.c = y.e = null;\r\n\r\n        // Return ±0 if either is ±0.\r\n        } else {\r\n          y.c = [0];\r\n          y.e = 0;\r\n        }\r\n      }\r\n\r\n      return y;\r\n    }\r\n\r\n    e = bitFloor(x.e / LOG_BASE) + bitFloor(y.e / LOG_BASE);\r\n    y.s *= x.s;\r\n    xcL = xc.length;\r\n    ycL = yc.length;\r\n\r\n    // Ensure xc points to longer array and xcL to its length.\r\n    if (xcL < ycL) {\r\n      zc = xc;\r\n      xc = yc;\r\n      yc = zc;\r\n      i = xcL;\r\n      xcL = ycL;\r\n      ycL = i;\r\n    }\r\n\r\n    // Initialise the result array with zeros.\r\n    for (i = xcL + ycL, zc = []; i--; zc.push(0));\r\n\r\n    base = BASE;\r\n    sqrtBase = SQRT_BASE;\r\n\r\n    for (i = ycL; --i >= 0;) {\r\n      c = 0;\r\n      ylo = yc[i] % sqrtBase;\r\n      yhi = yc[i] / sqrtBase | 0;\r\n\r\n      for (k = xcL, j = i + k; j > i;) {\r\n        xlo = xc[--k] % sqrtBase;\r\n        xhi = xc[k] / sqrtBase | 0;\r\n        m = yhi * xlo + xhi * ylo;\r\n        xlo = ylo * xlo + ((m % sqrtBase) * sqrtBase) + zc[j] + c;\r\n        c = (xlo / base | 0) + (m / sqrtBase | 0) + yhi * xhi;\r\n        zc[j--] = xlo % base;\r\n      }\r\n\r\n      zc[j] = c;\r\n    }\r\n\r\n    if (c) {\r\n      ++e;\r\n    } else {\r\n      zc.splice(0, 1);\r\n    }\r\n\r\n    return normalise(y, zc, e);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the value of this BigNumber negated,\r\n   * i.e. multiplied by -1.\r\n   */\r\n  P.negated = function () {\r\n    var x = new BigNumber(this);\r\n    x.s = -x.s || null;\r\n    return x;\r\n  };\r\n\r\n\r\n  /*\r\n   *  n + 0 = n\r\n   *  n + N = N\r\n   *  n + I = I\r\n   *  0 + n = n\r\n   *  0 + 0 = 0\r\n   *  0 + N = N\r\n   *  0 + I = I\r\n   *  N + n = N\r\n   *  N + 0 = N\r\n   *  N + N = N\r\n   *  N + I = N\r\n   *  I + n = I\r\n   *  I + 0 = I\r\n   *  I + N = N\r\n   *  I + I = I\r\n   *\r\n   * Return a new BigNumber whose value is the value of this BigNumber plus the value of\r\n   * BigNumber(y, b).\r\n   */\r\n  P.plus = function (y, b) {\r\n    var t,\r\n      x = this,\r\n      a = x.s;\r\n\r\n    y = new BigNumber(y, b);\r\n    b = y.s;\r\n\r\n    // Either NaN?\r\n    if (!a || !b) return new BigNumber(NaN);\r\n\r\n    // Signs differ?\r\n     if (a != b) {\r\n      y.s = -b;\r\n      return x.minus(y);\r\n    }\r\n\r\n    var xe = x.e / LOG_BASE,\r\n      ye = y.e / LOG_BASE,\r\n      xc = x.c,\r\n      yc = y.c;\r\n\r\n    if (!xe || !ye) {\r\n\r\n      // Return ±Infinity if either ±Infinity.\r\n      if (!xc || !yc) return new BigNumber(a / 0);\r\n\r\n      // Either zero?\r\n      // Return y if y is non-zero, x if x is non-zero, or zero if both are zero.\r\n      if (!xc[0] || !yc[0]) return yc[0] ? y : new BigNumber(xc[0] ? x : a * 0);\r\n    }\r\n\r\n    xe = bitFloor(xe);\r\n    ye = bitFloor(ye);\r\n    xc = xc.slice();\r\n\r\n    // Prepend zeros to equalise exponents. Faster to use reverse then do unshifts.\r\n    if (a = xe - ye) {\r\n      if (a > 0) {\r\n        ye = xe;\r\n        t = yc;\r\n      } else {\r\n        a = -a;\r\n        t = xc;\r\n      }\r\n\r\n      t.reverse();\r\n      for (; a--; t.push(0));\r\n      t.reverse();\r\n    }\r\n\r\n    a = xc.length;\r\n    b = yc.length;\r\n\r\n    // Point xc to the longer array, and b to the shorter length.\r\n    if (a - b < 0) {\r\n      t = yc;\r\n      yc = xc;\r\n      xc = t;\r\n      b = a;\r\n    }\r\n\r\n    // Only start adding at yc.length - 1 as the further digits of xc can be ignored.\r\n    for (a = 0; b;) {\r\n      a = (xc[--b] = xc[b] + yc[b] + a) / BASE | 0;\r\n      xc[b] = BASE === xc[b] ? 0 : xc[b] % BASE;\r\n    }\r\n\r\n    if (a) {\r\n      xc = [a].concat(xc);\r\n      ++ye;\r\n    }\r\n\r\n    // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n    // ye = MAX_EXP + 1 possible\r\n    return normalise(y, xc, ye);\r\n  };\r\n\r\n\r\n  /*\r\n   * If sd is undefined or null or true or false, return the number of significant digits of\r\n   * the value of this BigNumber, or null if the value of this BigNumber is ±Infinity or NaN.\r\n   * If sd is true include integer-part trailing zeros in the count.\r\n   *\r\n   * Otherwise, if sd is a number, return a new BigNumber whose value is the value of this\r\n   * BigNumber rounded to a maximum of sd significant digits using rounding mode rm, or\r\n   * ROUNDING_MODE if rm is omitted.\r\n   *\r\n   * sd {number|boolean} number: significant digits: integer, 1 to MAX inclusive.\r\n   *                     boolean: whether to count integer-part trailing zeros: true or false.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {sd|rm}'\r\n   */\r\n  P.precision = P.sd = function (sd, rm) {\r\n    var c, n, v,\r\n      x = this;\r\n\r\n    if (sd != null && sd !== !!sd) {\r\n      intCheck(sd, 1, MAX);\r\n      if (rm == null) rm = ROUNDING_MODE;\r\n      else intCheck(rm, 0, 8);\r\n\r\n      return round(new BigNumber(x), sd, rm);\r\n    }\r\n\r\n    if (!(c = x.c)) return null;\r\n    v = c.length - 1;\r\n    n = v * LOG_BASE + 1;\r\n\r\n    if (v = c[v]) {\r\n\r\n      // Subtract the number of trailing zeros of the last element.\r\n      for (; v % 10 == 0; v /= 10, n--);\r\n\r\n      // Add the number of digits of the first element.\r\n      for (v = c[0]; v >= 10; v /= 10, n++);\r\n    }\r\n\r\n    if (sd && x.e + 1 > n) n = x.e + 1;\r\n\r\n    return n;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the value of this BigNumber shifted by k places\r\n   * (powers of 10). Shift to the right if n > 0, and to the left if n < 0.\r\n   *\r\n   * k {number} Integer, -MAX_SAFE_INTEGER to MAX_SAFE_INTEGER inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {k}'\r\n   */\r\n  P.shiftedBy = function (k) {\r\n    intCheck(k, -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER);\r\n    return this.times('1e' + k);\r\n  };\r\n\r\n\r\n  /*\r\n   *  sqrt(-n) =  N\r\n   *  sqrt(N) =  N\r\n   *  sqrt(-I) =  N\r\n   *  sqrt(I) =  I\r\n   *  sqrt(0) =  0\r\n   *  sqrt(-0) = -0\r\n   *\r\n   * Return a new BigNumber whose value is the square root of the value of this BigNumber,\r\n   * rounded according to DECIMAL_PLACES and ROUNDING_MODE.\r\n   */\r\n  P.squareRoot = P.sqrt = function () {\r\n    var m, n, r, rep, t,\r\n      x = this,\r\n      c = x.c,\r\n      s = x.s,\r\n      e = x.e,\r\n      dp = DECIMAL_PLACES + 4,\r\n      half = new BigNumber('0.5');\r\n\r\n    // Negative/NaN/Infinity/zero?\r\n    if (s !== 1 || !c || !c[0]) {\r\n      return new BigNumber(!s || s < 0 && (!c || c[0]) ? NaN : c ? x : 1 / 0);\r\n    }\r\n\r\n    // Initial estimate.\r\n    s = Math.sqrt(+valueOf(x));\r\n\r\n    // Math.sqrt underflow/overflow?\r\n    // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\r\n    if (s == 0 || s == 1 / 0) {\r\n      n = coeffToString(c);\r\n      if ((n.length + e) % 2 == 0) n += '0';\r\n      s = Math.sqrt(+n);\r\n      e = bitFloor((e + 1) / 2) - (e < 0 || e % 2);\r\n\r\n      if (s == 1 / 0) {\r\n        n = '5e' + e;\r\n      } else {\r\n        n = s.toExponential();\r\n        n = n.slice(0, n.indexOf('e') + 1) + e;\r\n      }\r\n\r\n      r = new BigNumber(n);\r\n    } else {\r\n      r = new BigNumber(s + '');\r\n    }\r\n\r\n    // Check for zero.\r\n    // r could be zero if MIN_EXP is changed after the this value was created.\r\n    // This would cause a division by zero (x/t) and hence Infinity below, which would cause\r\n    // coeffToString to throw.\r\n    if (r.c[0]) {\r\n      e = r.e;\r\n      s = e + dp;\r\n      if (s < 3) s = 0;\r\n\r\n      // Newton-Raphson iteration.\r\n      for (; ;) {\r\n        t = r;\r\n        r = half.times(t.plus(div(x, t, dp, 1)));\r\n\r\n        if (coeffToString(t.c).slice(0, s) === (n = coeffToString(r.c)).slice(0, s)) {\r\n\r\n          // The exponent of r may here be one less than the final result exponent,\r\n          // e.g 0.0009999 (e-4) --> 0.001 (e-3), so adjust s so the rounding digits\r\n          // are indexed correctly.\r\n          if (r.e < e) --s;\r\n          n = n.slice(s - 3, s + 1);\r\n\r\n          // The 4th rounding digit may be in error by -1 so if the 4 rounding digits\r\n          // are 9999 or 4999 (i.e. approaching a rounding boundary) continue the\r\n          // iteration.\r\n          if (n == '9999' || !rep && n == '4999') {\r\n\r\n            // On the first iteration only, check to see if rounding up gives the\r\n            // exact result as the nines may infinitely repeat.\r\n            if (!rep) {\r\n              round(t, t.e + DECIMAL_PLACES + 2, 0);\r\n\r\n              if (t.times(t).eq(x)) {\r\n                r = t;\r\n                break;\r\n              }\r\n            }\r\n\r\n            dp += 4;\r\n            s += 4;\r\n            rep = 1;\r\n          } else {\r\n\r\n            // If rounding digits are null, 0{0,4} or 50{0,3}, check for exact\r\n            // result. If not, then there are further digits and m will be truthy.\r\n            if (!+n || !+n.slice(1) && n.charAt(0) == '5') {\r\n\r\n              // Truncate to the first rounding digit.\r\n              round(r, r.e + DECIMAL_PLACES + 2, 1);\r\n              m = !r.times(r).eq(x);\r\n            }\r\n\r\n            break;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return round(r, r.e + DECIMAL_PLACES + 1, ROUNDING_MODE, m);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this BigNumber in exponential notation and\r\n   * rounded using ROUNDING_MODE to dp fixed decimal places.\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n   */\r\n  P.toExponential = function (dp, rm) {\r\n    if (dp != null) {\r\n      intCheck(dp, 0, MAX);\r\n      dp++;\r\n    }\r\n    return format(this, dp, rm, 1);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this BigNumber in fixed-point notation rounding\r\n   * to dp fixed decimal places using rounding mode rm, or ROUNDING_MODE if rm is omitted.\r\n   *\r\n   * Note: as with JavaScript's number type, (-0).toFixed(0) is '0',\r\n   * but e.g. (-0.00001).toFixed(0) is '-0'.\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n   */\r\n  P.toFixed = function (dp, rm) {\r\n    if (dp != null) {\r\n      intCheck(dp, 0, MAX);\r\n      dp = dp + this.e + 1;\r\n    }\r\n    return format(this, dp, rm);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this BigNumber in fixed-point notation rounded\r\n   * using rm or ROUNDING_MODE to dp decimal places, and formatted according to the properties\r\n   * of the format or FORMAT object (see BigNumber.set).\r\n   *\r\n   * The formatting object may contain some or all of the properties shown below.\r\n   *\r\n   * FORMAT = {\r\n   *   prefix: '',\r\n   *   groupSize: 3,\r\n   *   secondaryGroupSize: 0,\r\n   *   groupSeparator: ',',\r\n   *   decimalSeparator: '.',\r\n   *   fractionGroupSize: 0,\r\n   *   fractionGroupSeparator: '\\xA0',      // non-breaking space\r\n   *   suffix: ''\r\n   * };\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   * [format] {object} Formatting options. See FORMAT pbject above.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n   * '[BigNumber Error] Argument not an object: {format}'\r\n   */\r\n  P.toFormat = function (dp, rm, format) {\r\n    var str,\r\n      x = this;\r\n\r\n    if (format == null) {\r\n      if (dp != null && rm && typeof rm == 'object') {\r\n        format = rm;\r\n        rm = null;\r\n      } else if (dp && typeof dp == 'object') {\r\n        format = dp;\r\n        dp = rm = null;\r\n      } else {\r\n        format = FORMAT;\r\n      }\r\n    } else if (typeof format != 'object') {\r\n      throw Error\r\n        (bignumberError + 'Argument not an object: ' + format);\r\n    }\r\n\r\n    str = x.toFixed(dp, rm);\r\n\r\n    if (x.c) {\r\n      var i,\r\n        arr = str.split('.'),\r\n        g1 = +format.groupSize,\r\n        g2 = +format.secondaryGroupSize,\r\n        groupSeparator = format.groupSeparator || '',\r\n        intPart = arr[0],\r\n        fractionPart = arr[1],\r\n        isNeg = x.s < 0,\r\n        intDigits = isNeg ? intPart.slice(1) : intPart,\r\n        len = intDigits.length;\r\n\r\n      if (g2) {\r\n        i = g1;\r\n        g1 = g2;\r\n        g2 = i;\r\n        len -= i;\r\n      }\r\n\r\n      if (g1 > 0 && len > 0) {\r\n        i = len % g1 || g1;\r\n        intPart = intDigits.substr(0, i);\r\n        for (; i < len; i += g1) intPart += groupSeparator + intDigits.substr(i, g1);\r\n        if (g2 > 0) intPart += groupSeparator + intDigits.slice(i);\r\n        if (isNeg) intPart = '-' + intPart;\r\n      }\r\n\r\n      str = fractionPart\r\n       ? intPart + (format.decimalSeparator || '') + ((g2 = +format.fractionGroupSize)\r\n        ? fractionPart.replace(new RegExp('\\\\d{' + g2 + '}\\\\B', 'g'),\r\n         '$&' + (format.fractionGroupSeparator || ''))\r\n        : fractionPart)\r\n       : intPart;\r\n    }\r\n\r\n    return (format.prefix || '') + str + (format.suffix || '');\r\n  };\r\n\r\n\r\n  /*\r\n   * Return an array of two BigNumbers representing the value of this BigNumber as a simple\r\n   * fraction with an integer numerator and an integer denominator.\r\n   * The denominator will be a positive non-zero value less than or equal to the specified\r\n   * maximum denominator. If a maximum denominator is not specified, the denominator will be\r\n   * the lowest value necessary to represent the number exactly.\r\n   *\r\n   * [md] {number|string|BigNumber} Integer >= 1, or Infinity. The maximum denominator.\r\n   *\r\n   * '[BigNumber Error] Argument {not an integer|out of range} : {md}'\r\n   */\r\n  P.toFraction = function (md) {\r\n    var d, d0, d1, d2, e, exp, n, n0, n1, q, r, s,\r\n      x = this,\r\n      xc = x.c;\r\n\r\n    if (md != null) {\r\n      n = new BigNumber(md);\r\n\r\n      // Throw if md is less than one or is not an integer, unless it is Infinity.\r\n      if (!n.isInteger() && (n.c || n.s !== 1) || n.lt(ONE)) {\r\n        throw Error\r\n          (bignumberError + 'Argument ' +\r\n            (n.isInteger() ? 'out of range: ' : 'not an integer: ') + valueOf(n));\r\n      }\r\n    }\r\n\r\n    if (!xc) return new BigNumber(x);\r\n\r\n    d = new BigNumber(ONE);\r\n    n1 = d0 = new BigNumber(ONE);\r\n    d1 = n0 = new BigNumber(ONE);\r\n    s = coeffToString(xc);\r\n\r\n    // Determine initial denominator.\r\n    // d is a power of 10 and the minimum max denominator that specifies the value exactly.\r\n    e = d.e = s.length - x.e - 1;\r\n    d.c[0] = POWS_TEN[(exp = e % LOG_BASE) < 0 ? LOG_BASE + exp : exp];\r\n    md = !md || n.comparedTo(d) > 0 ? (e > 0 ? d : n1) : n;\r\n\r\n    exp = MAX_EXP;\r\n    MAX_EXP = 1 / 0;\r\n    n = new BigNumber(s);\r\n\r\n    // n0 = d1 = 0\r\n    n0.c[0] = 0;\r\n\r\n    for (; ;)  {\r\n      q = div(n, d, 0, 1);\r\n      d2 = d0.plus(q.times(d1));\r\n      if (d2.comparedTo(md) == 1) break;\r\n      d0 = d1;\r\n      d1 = d2;\r\n      n1 = n0.plus(q.times(d2 = n1));\r\n      n0 = d2;\r\n      d = n.minus(q.times(d2 = d));\r\n      n = d2;\r\n    }\r\n\r\n    d2 = div(md.minus(d0), d1, 0, 1);\r\n    n0 = n0.plus(d2.times(n1));\r\n    d0 = d0.plus(d2.times(d1));\r\n    n0.s = n1.s = x.s;\r\n    e = e * 2;\r\n\r\n    // Determine which fraction is closer to x, n0/d0 or n1/d1\r\n    r = div(n1, d1, e, ROUNDING_MODE).minus(x).abs().comparedTo(\r\n        div(n0, d0, e, ROUNDING_MODE).minus(x).abs()) < 1 ? [n1, d1] : [n0, d0];\r\n\r\n    MAX_EXP = exp;\r\n\r\n    return r;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return the value of this BigNumber converted to a number primitive.\r\n   */\r\n  P.toNumber = function () {\r\n    return +valueOf(this);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this BigNumber rounded to sd significant digits\r\n   * using rounding mode rm or ROUNDING_MODE. If sd is less than the number of digits\r\n   * necessary to represent the integer part of the value in fixed-point notation, then use\r\n   * exponential notation.\r\n   *\r\n   * [sd] {number} Significant digits. Integer, 1 to MAX inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {sd|rm}'\r\n   */\r\n  P.toPrecision = function (sd, rm) {\r\n    if (sd != null) intCheck(sd, 1, MAX);\r\n    return format(this, sd, rm, 2);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this BigNumber in base b, or base 10 if b is\r\n   * omitted. If a base is specified, including base 10, round according to DECIMAL_PLACES and\r\n   * ROUNDING_MODE. If a base is not specified, and this BigNumber has a positive exponent\r\n   * that is equal to or greater than TO_EXP_POS, or a negative exponent equal to or less than\r\n   * TO_EXP_NEG, return exponential notation.\r\n   *\r\n   * [b] {number} Integer, 2 to ALPHABET.length inclusive.\r\n   *\r\n   * '[BigNumber Error] Base {not a primitive number|not an integer|out of range}: {b}'\r\n   */\r\n  P.toString = function (b) {\r\n    var str,\r\n      n = this,\r\n      s = n.s,\r\n      e = n.e;\r\n\r\n    // Infinity or NaN?\r\n    if (e === null) {\r\n      if (s) {\r\n        str = 'Infinity';\r\n        if (s < 0) str = '-' + str;\r\n      } else {\r\n        str = 'NaN';\r\n      }\r\n    } else {\r\n      if (b == null) {\r\n        str = e <= TO_EXP_NEG || e >= TO_EXP_POS\r\n         ? toExponential(coeffToString(n.c), e)\r\n         : toFixedPoint(coeffToString(n.c), e, '0');\r\n      } else if (b === 10 && alphabetHasNormalDecimalDigits) {\r\n        n = round(new BigNumber(n), DECIMAL_PLACES + e + 1, ROUNDING_MODE);\r\n        str = toFixedPoint(coeffToString(n.c), n.e, '0');\r\n      } else {\r\n        intCheck(b, 2, ALPHABET.length, 'Base');\r\n        str = convertBase(toFixedPoint(coeffToString(n.c), e, '0'), 10, b, s, true);\r\n      }\r\n\r\n      if (s < 0 && n.c[0]) str = '-' + str;\r\n    }\r\n\r\n    return str;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return as toString, but do not accept a base argument, and include the minus sign for\r\n   * negative zero.\r\n   */\r\n  P.valueOf = P.toJSON = function () {\r\n    return valueOf(this);\r\n  };\r\n\r\n\r\n  P._isBigNumber = true;\r\n\r\n  P[Symbol.toStringTag] = 'BigNumber';\r\n\r\n  // Node.js v10.12.0+\r\n  P[Symbol.for('nodejs.util.inspect.custom')] = P.valueOf;\r\n\r\n  if (configObject != null) BigNumber.set(configObject);\r\n\r\n  return BigNumber;\r\n}\r\n\r\n\r\n// PRIVATE HELPER FUNCTIONS\r\n\r\n// These functions don't need access to variables,\r\n// e.g. DECIMAL_PLACES, in the scope of the `clone` function above.\r\n\r\n\r\nfunction bitFloor(n) {\r\n  var i = n | 0;\r\n  return n > 0 || n === i ? i : i - 1;\r\n}\r\n\r\n\r\n// Return a coefficient array as a string of base 10 digits.\r\nfunction coeffToString(a) {\r\n  var s, z,\r\n    i = 1,\r\n    j = a.length,\r\n    r = a[0] + '';\r\n\r\n  for (; i < j;) {\r\n    s = a[i++] + '';\r\n    z = LOG_BASE - s.length;\r\n    for (; z--; s = '0' + s);\r\n    r += s;\r\n  }\r\n\r\n  // Determine trailing zeros.\r\n  for (j = r.length; r.charCodeAt(--j) === 48;);\r\n\r\n  return r.slice(0, j + 1 || 1);\r\n}\r\n\r\n\r\n// Compare the value of BigNumbers x and y.\r\nfunction compare(x, y) {\r\n  var a, b,\r\n    xc = x.c,\r\n    yc = y.c,\r\n    i = x.s,\r\n    j = y.s,\r\n    k = x.e,\r\n    l = y.e;\r\n\r\n  // Either NaN?\r\n  if (!i || !j) return null;\r\n\r\n  a = xc && !xc[0];\r\n  b = yc && !yc[0];\r\n\r\n  // Either zero?\r\n  if (a || b) return a ? b ? 0 : -j : i;\r\n\r\n  // Signs differ?\r\n  if (i != j) return i;\r\n\r\n  a = i < 0;\r\n  b = k == l;\r\n\r\n  // Either Infinity?\r\n  if (!xc || !yc) return b ? 0 : !xc ^ a ? 1 : -1;\r\n\r\n  // Compare exponents.\r\n  if (!b) return k > l ^ a ? 1 : -1;\r\n\r\n  j = (k = xc.length) < (l = yc.length) ? k : l;\r\n\r\n  // Compare digit by digit.\r\n  for (i = 0; i < j; i++) if (xc[i] != yc[i]) return xc[i] > yc[i] ^ a ? 1 : -1;\r\n\r\n  // Compare lengths.\r\n  return k == l ? 0 : k > l ^ a ? 1 : -1;\r\n}\r\n\r\n\r\n/*\r\n * Check that n is a primitive number, an integer, and in range, otherwise throw.\r\n */\r\nfunction intCheck(n, min, max, name) {\r\n  if (n < min || n > max || n !== mathfloor(n)) {\r\n    throw Error\r\n     (bignumberError + (name || 'Argument') + (typeof n == 'number'\r\n       ? n < min || n > max ? ' out of range: ' : ' not an integer: '\r\n       : ' not a primitive number: ') + String(n));\r\n  }\r\n}\r\n\r\n\r\n// Assumes finite n.\r\nfunction isOdd(n) {\r\n  var k = n.c.length - 1;\r\n  return bitFloor(n.e / LOG_BASE) == k && n.c[k] % 2 != 0;\r\n}\r\n\r\n\r\nfunction toExponential(str, e) {\r\n  return (str.length > 1 ? str.charAt(0) + '.' + str.slice(1) : str) +\r\n   (e < 0 ? 'e' : 'e+') + e;\r\n}\r\n\r\n\r\nfunction toFixedPoint(str, e, z) {\r\n  var len, zs;\r\n\r\n  // Negative exponent?\r\n  if (e < 0) {\r\n\r\n    // Prepend zeros.\r\n    for (zs = z + '.'; ++e; zs += z);\r\n    str = zs + str;\r\n\r\n  // Positive exponent\r\n  } else {\r\n    len = str.length;\r\n\r\n    // Append zeros.\r\n    if (++e > len) {\r\n      for (zs = z, e -= len; --e; zs += z);\r\n      str += zs;\r\n    } else if (e < len) {\r\n      str = str.slice(0, e) + '.' + str.slice(e);\r\n    }\r\n  }\r\n\r\n  return str;\r\n}\r\n\r\n\r\n// EXPORT\r\n\r\n\r\nexport var BigNumber = clone();\r\n\r\nexport default BigNumber;\r\n"], "mappings": ";;;AAgDA,IACE,YAAY;AADd,IAEE,WAAW,KAAK;AAFlB,IAGE,YAAY,KAAK;AAHnB,IAKE,iBAAiB;AALnB,IAME,gBAAgB,iBAAiB;AANnC,IAQE,OAAO;AART,IASE,WAAW;AATb,IAUE,mBAAmB;AAVrB,IAYE,WAAW,CAAC,GAAG,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,IAAI;AAZnF,IAaE,YAAY;AAbd,IAkBE,MAAM;AAMR,SAAS,MAAM,cAAc;AAC3B,MAAI,KAAK,aAAa,cACpB,IAAIA,WAAU,YAAY,EAAE,aAAaA,YAAW,UAAU,MAAM,SAAS,KAAK,GAClF,MAAM,IAAIA,WAAU,CAAC,GAUrB,iBAAiB,IAajB,gBAAgB,GAMhB,aAAa,IAIb,aAAa,IAMb,UAAU,MAKV,UAAU,KAGV,SAAS,OAkBT,cAAc,GAId,gBAAgB,GAGhB,SAAS;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,wBAAwB;AAAA;AAAA,IACxB,QAAQ;AAAA,EACV,GAKA,WAAW,wCACX,iCAAiC;AAgBnC,WAASA,WAAU,GAAG,GAAG;AACvB,QAAI,UAAU,GAAG,aAAa,GAAG,GAAG,OAAO,KAAK,KAC9C,IAAI;AAGN,QAAI,EAAE,aAAaA,YAAY,QAAO,IAAIA,WAAU,GAAG,CAAC;AAExD,QAAI,KAAK,MAAM;AAEb,UAAI,KAAK,EAAE,iBAAiB,MAAM;AAChC,UAAE,IAAI,EAAE;AAER,YAAI,CAAC,EAAE,KAAK,EAAE,IAAI,SAAS;AACzB,YAAE,IAAI,EAAE,IAAI;AAAA,QACd,WAAW,EAAE,IAAI,SAAS;AACxB,YAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,QAChB,OAAO;AACL,YAAE,IAAI,EAAE;AACR,YAAE,IAAI,EAAE,EAAE,MAAM;AAAA,QAClB;AAEA;AAAA,MACF;AAEA,WAAK,QAAQ,OAAO,KAAK,aAAa,IAAI,KAAK,GAAG;AAGhD,UAAE,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,GAAG,MAAM;AAGjC,YAAI,MAAM,CAAC,CAAC,GAAG;AACb,eAAK,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI;AAEzC,cAAI,IAAI,SAAS;AACf,cAAE,IAAI,EAAE,IAAI;AAAA,UACd,OAAO;AACL,cAAE,IAAI;AACN,cAAE,IAAI,CAAC,CAAC;AAAA,UACV;AAEA;AAAA,QACF;AAEA,cAAM,OAAO,CAAC;AAAA,MAChB,OAAO;AAEL,YAAI,CAAC,UAAU,KAAK,MAAM,OAAO,CAAC,CAAC,EAAG,QAAO,aAAa,GAAG,KAAK,KAAK;AAEvE,UAAE,IAAI,IAAI,WAAW,CAAC,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,GAAG,MAAM;AAAA,MAC7D;AAGA,WAAK,IAAI,IAAI,QAAQ,GAAG,KAAK,GAAI,OAAM,IAAI,QAAQ,KAAK,EAAE;AAG1D,WAAK,IAAI,IAAI,OAAO,IAAI,KAAK,GAAG;AAG9B,YAAI,IAAI,EAAG,KAAI;AACf,aAAK,CAAC,IAAI,MAAM,IAAI,CAAC;AACrB,cAAM,IAAI,UAAU,GAAG,CAAC;AAAA,MAC1B,WAAW,IAAI,GAAG;AAGhB,YAAI,IAAI;AAAA,MACV;AAAA,IAEF,OAAO;AAGL,eAAS,GAAG,GAAG,SAAS,QAAQ,MAAM;AAItC,UAAI,KAAK,MAAM,gCAAgC;AAC7C,YAAI,IAAIA,WAAU,CAAC;AACnB,eAAO,MAAM,GAAG,iBAAiB,EAAE,IAAI,GAAG,aAAa;AAAA,MACzD;AAEA,YAAM,OAAO,CAAC;AAEd,UAAI,QAAQ,OAAO,KAAK,UAAU;AAGhC,YAAI,IAAI,KAAK,EAAG,QAAO,aAAa,GAAG,KAAK,OAAO,CAAC;AAEpD,UAAE,IAAI,IAAI,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,GAAG,MAAM;AAG7C,YAAIA,WAAU,SAAS,IAAI,QAAQ,aAAa,EAAE,EAAE,SAAS,IAAI;AAC/D,gBAAM,MACJ,gBAAgB,CAAC;AAAA,QACrB;AAAA,MACF,OAAO;AACL,UAAE,IAAI,IAAI,WAAW,CAAC,MAAM,MAAM,MAAM,IAAI,MAAM,CAAC,GAAG,MAAM;AAAA,MAC9D;AAEA,iBAAW,SAAS,MAAM,GAAG,CAAC;AAC9B,UAAI,IAAI;AAIR,WAAK,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AACnC,YAAI,SAAS,QAAQ,IAAI,IAAI,OAAO,CAAC,CAAC,IAAI,GAAG;AAC3C,cAAI,KAAK,KAAK;AAGZ,gBAAI,IAAI,GAAG;AACT,kBAAI;AACJ;AAAA,YACF;AAAA,UACF,WAAW,CAAC,aAAa;AAGvB,gBAAI,OAAO,IAAI,YAAY,MAAM,MAAM,IAAI,YAAY,MACnD,OAAO,IAAI,YAAY,MAAM,MAAM,IAAI,YAAY,IAAI;AACzD,4BAAc;AACd,kBAAI;AACJ,kBAAI;AACJ;AAAA,YACF;AAAA,UACF;AAEA,iBAAO,aAAa,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;AAAA,QAC5C;AAAA,MACF;AAGA,cAAQ;AACR,YAAM,YAAY,KAAK,GAAG,IAAI,EAAE,CAAC;AAGjC,WAAK,IAAI,IAAI,QAAQ,GAAG,KAAK,GAAI,OAAM,IAAI,QAAQ,KAAK,EAAE;AAAA,UACrD,KAAI,IAAI;AAAA,IACf;AAGA,SAAK,IAAI,GAAG,IAAI,WAAW,CAAC,MAAM,IAAI,IAAI;AAG1C,SAAK,MAAM,IAAI,QAAQ,IAAI,WAAW,EAAE,GAAG,MAAM,KAAI;AAErD,QAAI,MAAM,IAAI,MAAM,GAAG,EAAE,GAAG,GAAG;AAC7B,aAAO;AAGP,UAAI,SAASA,WAAU,SACrB,MAAM,OAAO,IAAI,oBAAoB,MAAM,UAAU,CAAC,IAAI;AACxD,cAAM,MACJ,gBAAiB,EAAE,IAAI,CAAE;AAAA,MAC/B;AAGA,WAAK,IAAI,IAAI,IAAI,KAAK,SAAS;AAG7B,UAAE,IAAI,EAAE,IAAI;AAAA,MAGd,WAAW,IAAI,SAAS;AAGtB,UAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,MAChB,OAAO;AACL,UAAE,IAAI;AACN,UAAE,IAAI,CAAC;AAMP,aAAK,IAAI,KAAK;AACd,YAAI,IAAI,EAAG,MAAK;AAEhB,YAAI,IAAI,KAAK;AACX,cAAI,EAAG,GAAE,EAAE,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;AAEhC,eAAK,OAAO,UAAU,IAAI,OAAM;AAC9B,cAAE,EAAE,KAAK,CAAC,IAAI,MAAM,GAAG,KAAK,QAAQ,CAAC;AAAA,UACvC;AAEA,cAAI,YAAY,MAAM,IAAI,MAAM,CAAC,GAAG;AAAA,QACtC,OAAO;AACL,eAAK;AAAA,QACP;AAEA,eAAO,KAAK,OAAO,IAAI;AACvB,UAAE,EAAE,KAAK,CAAC,GAAG;AAAA,MACf;AAAA,IACF,OAAO;AAGL,QAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,IAChB;AAAA,EACF;AAMA,EAAAA,WAAU,QAAQ;AAElB,EAAAA,WAAU,WAAW;AACrB,EAAAA,WAAU,aAAa;AACvB,EAAAA,WAAU,aAAa;AACvB,EAAAA,WAAU,cAAc;AACxB,EAAAA,WAAU,gBAAgB;AAC1B,EAAAA,WAAU,kBAAkB;AAC5B,EAAAA,WAAU,kBAAkB;AAC5B,EAAAA,WAAU,kBAAkB;AAC5B,EAAAA,WAAU,mBAAmB;AAC7B,EAAAA,WAAU,SAAS;AAqCnB,EAAAA,WAAU,SAASA,WAAU,MAAM,SAAU,KAAK;AAChD,QAAI,GAAG;AAEP,QAAI,OAAO,MAAM;AAEf,UAAI,OAAO,OAAO,UAAU;AAI1B,YAAI,IAAI,eAAe,IAAI,gBAAgB,GAAG;AAC5C,cAAI,IAAI,CAAC;AACT,mBAAS,GAAG,GAAG,KAAK,CAAC;AACrB,2BAAiB;AAAA,QACnB;AAIA,YAAI,IAAI,eAAe,IAAI,eAAe,GAAG;AAC3C,cAAI,IAAI,CAAC;AACT,mBAAS,GAAG,GAAG,GAAG,CAAC;AACnB,0BAAgB;AAAA,QAClB;AAMA,YAAI,IAAI,eAAe,IAAI,gBAAgB,GAAG;AAC5C,cAAI,IAAI,CAAC;AACT,cAAI,KAAK,EAAE,KAAK;AACd,qBAAS,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC;AACzB,qBAAS,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC;AACxB,yBAAa,EAAE,CAAC;AAChB,yBAAa,EAAE,CAAC;AAAA,UAClB,OAAO;AACL,qBAAS,GAAG,CAAC,KAAK,KAAK,CAAC;AACxB,yBAAa,EAAE,aAAa,IAAI,IAAI,CAAC,IAAI;AAAA,UAC3C;AAAA,QACF;AAKA,YAAI,IAAI,eAAe,IAAI,OAAO,GAAG;AACnC,cAAI,IAAI,CAAC;AACT,cAAI,KAAK,EAAE,KAAK;AACd,qBAAS,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC;AAC1B,qBAAS,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC;AACxB,sBAAU,EAAE,CAAC;AACb,sBAAU,EAAE,CAAC;AAAA,UACf,OAAO;AACL,qBAAS,GAAG,CAAC,KAAK,KAAK,CAAC;AACxB,gBAAI,GAAG;AACL,wBAAU,EAAE,UAAU,IAAI,IAAI,CAAC,IAAI;AAAA,YACrC,OAAO;AACL,oBAAM,MACJ,iBAAiB,IAAI,sBAAsB,CAAC;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AAKA,YAAI,IAAI,eAAe,IAAI,QAAQ,GAAG;AACpC,cAAI,IAAI,CAAC;AACT,cAAI,MAAM,CAAC,CAAC,GAAG;AACb,gBAAI,GAAG;AACL,kBAAI,OAAO,UAAU,eAAe,WAClC,OAAO,mBAAmB,OAAO,cAAc;AAC/C,yBAAS;AAAA,cACX,OAAO;AACL,yBAAS,CAAC;AACV,sBAAM,MACJ,iBAAiB,oBAAoB;AAAA,cACzC;AAAA,YACF,OAAO;AACL,uBAAS;AAAA,YACX;AAAA,UACF,OAAO;AACL,kBAAM,MACJ,iBAAiB,IAAI,yBAAyB,CAAC;AAAA,UACnD;AAAA,QACF;AAIA,YAAI,IAAI,eAAe,IAAI,aAAa,GAAG;AACzC,cAAI,IAAI,CAAC;AACT,mBAAS,GAAG,GAAG,GAAG,CAAC;AACnB,wBAAc;AAAA,QAChB;AAIA,YAAI,IAAI,eAAe,IAAI,eAAe,GAAG;AAC3C,cAAI,IAAI,CAAC;AACT,mBAAS,GAAG,GAAG,KAAK,CAAC;AACrB,0BAAgB;AAAA,QAClB;AAIA,YAAI,IAAI,eAAe,IAAI,QAAQ,GAAG;AACpC,cAAI,IAAI,CAAC;AACT,cAAI,OAAO,KAAK,SAAU,UAAS;AAAA,cAC9B,OAAM,MACT,iBAAiB,IAAI,qBAAqB,CAAC;AAAA,QAC/C;AAIA,YAAI,IAAI,eAAe,IAAI,UAAU,GAAG;AACtC,cAAI,IAAI,CAAC;AAIT,cAAI,OAAO,KAAK,YAAY,CAAC,wBAAwB,KAAK,CAAC,GAAG;AAC5D,6CAAiC,EAAE,MAAM,GAAG,EAAE,KAAK;AACnD,uBAAW;AAAA,UACb,OAAO;AACL,kBAAM,MACJ,iBAAiB,IAAI,eAAe,CAAC;AAAA,UACzC;AAAA,QACF;AAAA,MAEF,OAAO;AAGL,cAAM,MACJ,iBAAiB,sBAAsB,GAAG;AAAA,MAC9C;AAAA,IACF;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,gBAAgB,CAAC,YAAY,UAAU;AAAA,MACvC,OAAO,CAAC,SAAS,OAAO;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAYA,EAAAA,WAAU,cAAc,SAAU,GAAG;AACnC,QAAI,CAAC,KAAK,EAAE,iBAAiB,KAAM,QAAO;AAC1C,QAAI,CAACA,WAAU,MAAO,QAAO;AAE7B,QAAI,GAAG,GACL,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE;AAER,QAAK,KAAI,CAAC,EAAE,SAAS,KAAK,CAAC,KAAK,kBAAkB;AAEhD,WAAK,MAAM,KAAK,MAAM,OAAO,KAAK,CAAC,OAAO,KAAK,OAAO,MAAM,UAAU,CAAC,GAAG;AAGxE,YAAI,EAAE,CAAC,MAAM,GAAG;AACd,cAAI,MAAM,KAAK,EAAE,WAAW,EAAG,QAAO;AACtC,gBAAM;AAAA,QACR;AAGA,aAAK,IAAI,KAAK;AACd,YAAI,IAAI,EAAG,MAAK;AAIhB,YAAI,OAAO,EAAE,CAAC,CAAC,EAAE,UAAU,GAAG;AAE5B,eAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC7B,gBAAI,EAAE,CAAC;AACP,gBAAI,IAAI,KAAK,KAAK,QAAQ,MAAM,UAAU,CAAC,EAAG,OAAM;AAAA,UACtD;AAGA,cAAI,MAAM,EAAG,QAAO;AAAA,QACtB;AAAA,MACF;AAAA,IAGF,WAAW,MAAM,QAAQ,MAAM,SAAS,MAAM,QAAQ,MAAM,KAAK,MAAM,KAAK;AAC1E,aAAO;AAAA,IACT;AAEA,UAAM,MACH,iBAAiB,wBAAwB,CAAC;AAAA,EAC/C;AAQA,EAAAA,WAAU,UAAUA,WAAU,MAAM,WAAY;AAC9C,WAAO,SAAS,WAAW,EAAE;AAAA,EAC/B;AAQA,EAAAA,WAAU,UAAUA,WAAU,MAAM,WAAY;AAC9C,WAAO,SAAS,WAAW,CAAC;AAAA,EAC9B;AAaA,EAAAA,WAAU,SAAU,WAAY;AAC9B,QAAI,UAAU;AAMd,QAAI,iBAAkB,KAAK,OAAO,IAAI,UAAW,UAC9C,WAAY;AAAE,aAAO,UAAU,KAAK,OAAO,IAAI,OAAO;AAAA,IAAG,IACzD,WAAY;AAAE,cAAS,KAAK,OAAO,IAAI,aAAa,KAAK,WACxD,KAAK,OAAO,IAAI,UAAW;AAAA,IAAI;AAEnC,WAAO,SAAU,IAAI;AACnB,UAAI,GAAG,GAAG,GAAG,GAAG,GACd,IAAI,GACJ,IAAI,CAAC,GACL,OAAO,IAAIA,WAAU,GAAG;AAE1B,UAAI,MAAM,KAAM,MAAK;AAAA,UAChB,UAAS,IAAI,GAAG,GAAG;AAExB,UAAI,SAAS,KAAK,QAAQ;AAE1B,UAAI,QAAQ;AAGV,YAAI,OAAO,iBAAiB;AAE1B,cAAI,OAAO,gBAAgB,IAAI,YAAY,KAAK,CAAC,CAAC;AAElD,iBAAO,IAAI,KAAI;AAQb,gBAAI,EAAE,CAAC,IAAI,UAAW,EAAE,IAAI,CAAC,MAAM;AAMnC,gBAAI,KAAK,MAAM;AACb,kBAAI,OAAO,gBAAgB,IAAI,YAAY,CAAC,CAAC;AAC7C,gBAAE,CAAC,IAAI,EAAE,CAAC;AACV,gBAAE,IAAI,CAAC,IAAI,EAAE,CAAC;AAAA,YAChB,OAAO;AAIL,gBAAE,KAAK,IAAI,IAAI;AACf,mBAAK;AAAA,YACP;AAAA,UACF;AACA,cAAI,IAAI;AAAA,QAGV,WAAW,OAAO,aAAa;AAG7B,cAAI,OAAO,YAAY,KAAK,CAAC;AAE7B,iBAAO,IAAI,KAAI;AAMb,iBAAM,EAAE,CAAC,IAAI,MAAM,kBAAoB,EAAE,IAAI,CAAC,IAAI,gBAC9C,EAAE,IAAI,CAAC,IAAI,aAAgB,EAAE,IAAI,CAAC,IAAI,YACtC,EAAE,IAAI,CAAC,KAAK,OAAO,EAAE,IAAI,CAAC,KAAK,KAAK,EAAE,IAAI,CAAC;AAE/C,gBAAI,KAAK,MAAM;AACb,qBAAO,YAAY,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,YACjC,OAAO;AAGL,gBAAE,KAAK,IAAI,IAAI;AACf,mBAAK;AAAA,YACP;AAAA,UACF;AACA,cAAI,IAAI;AAAA,QACV,OAAO;AACL,mBAAS;AACT,gBAAM,MACJ,iBAAiB,oBAAoB;AAAA,QACzC;AAAA,MACF;AAGA,UAAI,CAAC,QAAQ;AAEX,eAAO,IAAI,KAAI;AACb,cAAI,eAAe;AACnB,cAAI,IAAI,KAAM,GAAE,GAAG,IAAI,IAAI;AAAA,QAC7B;AAAA,MACF;AAEA,UAAI,EAAE,EAAE,CAAC;AACT,YAAM;AAGN,UAAI,KAAK,IAAI;AACX,YAAI,SAAS,WAAW,EAAE;AAC1B,UAAE,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI;AAAA,MAC5B;AAGA,aAAO,EAAE,CAAC,MAAM,GAAG,EAAE,IAAI,GAAG,IAAI;AAGhC,UAAI,IAAI,GAAG;AACT,YAAI,CAAC,IAAI,CAAC;AAAA,MACZ,OAAO;AAGL,aAAK,IAAI,IAAK,EAAE,CAAC,MAAM,GAAG,EAAE,OAAO,GAAG,CAAC,GAAG,KAAK,SAAS;AAGxD,aAAK,IAAI,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI;AAG5C,YAAI,IAAI,SAAU,MAAK,WAAW;AAAA,MACpC;AAEA,WAAK,IAAI;AACT,WAAK,IAAI;AACT,aAAO;AAAA,IACT;AAAA,EACF,EAAG;AAQH,EAAAA,WAAU,MAAM,WAAY;AAC1B,QAAI,IAAI,GACN,OAAO,WACP,MAAM,IAAIA,WAAU,KAAK,CAAC,CAAC;AAC7B,WAAO,IAAI,KAAK,SAAS,OAAM,IAAI,KAAK,KAAK,GAAG,CAAC;AACjD,WAAO;AAAA,EACT;AAOA,gBAAe,2BAAY;AACzB,QAAI,UAAU;AAOd,aAAS,UAAU,KAAK,QAAQ,SAAS,UAAU;AACjD,UAAI,GACF,MAAM,CAAC,CAAC,GACR,MACA,IAAI,GACJ,MAAM,IAAI;AAEZ,aAAO,IAAI,OAAM;AACf,aAAK,OAAO,IAAI,QAAQ,QAAQ,IAAI,IAAI,KAAK,OAAO;AAEpD,YAAI,CAAC,KAAK,SAAS,QAAQ,IAAI,OAAO,GAAG,CAAC;AAE1C,aAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAE/B,cAAI,IAAI,CAAC,IAAI,UAAU,GAAG;AACxB,gBAAI,IAAI,IAAI,CAAC,KAAK,KAAM,KAAI,IAAI,CAAC,IAAI;AACrC,gBAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,UAAU;AACjC,gBAAI,CAAC,KAAK;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAEA,aAAO,IAAI,QAAQ;AAAA,IACrB;AAKA,WAAO,SAAU,KAAK,QAAQ,SAAS,MAAM,kBAAkB;AAC7D,UAAI,UAAU,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAC/B,IAAI,IAAI,QAAQ,GAAG,GACnB,KAAK,gBACL,KAAK;AAGP,UAAI,KAAK,GAAG;AACV,YAAI;AAGJ,wBAAgB;AAChB,cAAM,IAAI,QAAQ,KAAK,EAAE;AACzB,YAAI,IAAIA,WAAU,MAAM;AACxB,YAAI,EAAE,IAAI,IAAI,SAAS,CAAC;AACxB,wBAAgB;AAKhB,UAAE,IAAI;AAAA,UAAU,aAAa,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG;AAAA,UACxD;AAAA,UAAI;AAAA,UAAS;AAAA,QAAO;AACrB,UAAE,IAAI,EAAE,EAAE;AAAA,MACZ;AAIA,WAAK,UAAU,KAAK,QAAQ,SAAS,oBACjC,WAAW,UAAU,YACrB,WAAW,SAAS,SAAS;AAGjC,UAAI,IAAI,GAAG;AAGX,aAAO,GAAG,EAAE,CAAC,KAAK,GAAG,GAAG,IAAI,EAAE;AAG9B,UAAI,CAAC,GAAG,CAAC,EAAG,QAAO,SAAS,OAAO,CAAC;AAGpC,UAAI,IAAI,GAAG;AACT,UAAE;AAAA,MACJ,OAAO;AACL,UAAE,IAAI;AACN,UAAE,IAAI;AAGN,UAAE,IAAI;AACN,YAAI,IAAI,GAAG,GAAG,IAAI,IAAI,OAAO;AAC7B,aAAK,EAAE;AACP,YAAI,EAAE;AACN,YAAI,EAAE;AAAA,MACR;AAKA,UAAI,IAAI,KAAK;AAGb,UAAI,GAAG,CAAC;AAIR,UAAI,UAAU;AACd,UAAI,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;AAE/B,UAAI,KAAK,KAAK,KAAK,QAAQ,OAAO,MAAM,KAAK,OAAO,EAAE,IAAI,IAAI,IAAI,MAC1D,IAAI,KAAK,KAAK,MAAK,MAAM,KAAK,KAAK,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,KAC3D,OAAO,EAAE,IAAI,IAAI,IAAI;AAK5B,UAAI,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG;AAGnB,cAAM,IAAI,aAAa,SAAS,OAAO,CAAC,GAAG,CAAC,IAAI,SAAS,OAAO,CAAC,CAAC,IAAI,SAAS,OAAO,CAAC;AAAA,MACzF,OAAO;AAGL,WAAG,SAAS;AAGZ,YAAI,GAAG;AAGL,eAAK,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,WAAU;AACpC,eAAG,CAAC,IAAI;AAER,gBAAI,CAAC,GAAG;AACN,gBAAE;AACF,mBAAK,CAAC,CAAC,EAAE,OAAO,EAAE;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AAGA,aAAK,IAAI,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAG;AAG9B,aAAK,IAAI,GAAG,MAAM,IAAI,KAAK,GAAG,OAAO,SAAS,OAAO,GAAG,GAAG,CAAC,EAAE;AAG9D,cAAM,aAAa,KAAK,GAAG,SAAS,OAAO,CAAC,CAAC;AAAA,MAC/C;AAGA,aAAO;AAAA,IACT;AAAA,EACF,EAAG;AAIH,QAAO,2BAAY;AAGjB,aAAS,SAAS,GAAG,GAAG,MAAM;AAC5B,UAAI,GAAG,MAAM,KAAK,KAChB,QAAQ,GACR,IAAI,EAAE,QACN,MAAM,IAAI,WACV,MAAM,IAAI,YAAY;AAExB,WAAK,IAAI,EAAE,MAAM,GAAG,OAAM;AACxB,cAAM,EAAE,CAAC,IAAI;AACb,cAAM,EAAE,CAAC,IAAI,YAAY;AACzB,YAAI,MAAM,MAAM,MAAM;AACtB,eAAO,MAAM,MAAQ,IAAI,YAAa,YAAa;AACnD,iBAAS,OAAO,OAAO,MAAM,IAAI,YAAY,KAAK,MAAM;AACxD,UAAE,CAAC,IAAI,OAAO;AAAA,MAChB;AAEA,UAAI,MAAO,KAAI,CAAC,KAAK,EAAE,OAAO,CAAC;AAE/B,aAAO;AAAA,IACT;AAEA,aAASC,SAAQ,GAAG,GAAG,IAAI,IAAI;AAC7B,UAAI,GAAG;AAEP,UAAI,MAAM,IAAI;AACZ,cAAM,KAAK,KAAK,IAAI;AAAA,MACtB,OAAO;AAEL,aAAK,IAAI,MAAM,GAAG,IAAI,IAAI,KAAK;AAE7B,cAAI,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AAChB,kBAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI;AACxB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,SAAS,GAAG,GAAG,IAAI,MAAM;AAChC,UAAI,IAAI;AAGR,aAAO,QAAO;AACZ,UAAE,EAAE,KAAK;AACT,YAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,IAAI;AACxB,UAAE,EAAE,IAAI,IAAI,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE;AAAA,MACjC;AAGA,aAAO,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,GAAG,EAAE,OAAO,GAAG,CAAC,EAAE;AAAA,IAC/C;AAGA,WAAO,SAAU,GAAG,GAAG,IAAI,IAAI,MAAM;AACnC,UAAI,KAAK,GAAG,GAAG,MAAM,GAAG,MAAM,OAAO,GAAG,IAAI,KAAK,MAAM,MAAM,IAAI,IAAI,KACnE,IAAI,IACJ,IAAI,EAAE,KAAK,EAAE,IAAI,IAAI,IACrB,KAAK,EAAE,GACP,KAAK,EAAE;AAGT,UAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG;AAElC,eAAO,IAAID;AAAA;AAAA,UAGV,CAAC,EAAE,KAAK,CAAC,EAAE,MAAM,KAAK,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM;AAAA;AAAA,YAGnD,MAAM,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI;AAAA;AAAA,QACzC;AAAA,MACD;AAEA,UAAI,IAAIA,WAAU,CAAC;AACnB,WAAK,EAAE,IAAI,CAAC;AACZ,UAAI,EAAE,IAAI,EAAE;AACZ,UAAI,KAAK,IAAI;AAEb,UAAI,CAAC,MAAM;AACT,eAAO;AACP,YAAI,SAAS,EAAE,IAAI,QAAQ,IAAI,SAAS,EAAE,IAAI,QAAQ;AACtD,YAAI,IAAI,WAAW;AAAA,MACrB;AAIA,WAAK,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,IAAI,IAAI;AAEvC,UAAI,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,GAAI;AAE1B,UAAI,IAAI,GAAG;AACT,WAAG,KAAK,CAAC;AACT,eAAO;AAAA,MACT,OAAO;AACL,aAAK,GAAG;AACR,aAAK,GAAG;AACR,YAAI;AACJ,aAAK;AAIL,YAAI,UAAU,QAAQ,GAAG,CAAC,IAAI,EAAE;AAIhC,YAAI,IAAI,GAAG;AACT,eAAK,SAAS,IAAI,GAAG,IAAI;AACzB,eAAK,SAAS,IAAI,GAAG,IAAI;AACzB,eAAK,GAAG;AACR,eAAK,GAAG;AAAA,QACV;AAEA,aAAK;AACL,cAAM,GAAG,MAAM,GAAG,EAAE;AACpB,eAAO,IAAI;AAGX,eAAO,OAAO,IAAI,IAAI,MAAM,IAAI,EAAE;AAClC,aAAK,GAAG,MAAM;AACd,aAAK,CAAC,CAAC,EAAE,OAAO,EAAE;AAClB,cAAM,GAAG,CAAC;AACV,YAAI,GAAG,CAAC,KAAK,OAAO,EAAG;AAIvB,WAAG;AACD,cAAI;AAGJ,gBAAMC,SAAQ,IAAI,KAAK,IAAI,IAAI;AAG/B,cAAI,MAAM,GAAG;AAIX,mBAAO,IAAI,CAAC;AACZ,gBAAI,MAAM,KAAM,QAAO,OAAO,QAAQ,IAAI,CAAC,KAAK;AAGhD,gBAAI,UAAU,OAAO,GAAG;AAaxB,gBAAI,IAAI,GAAG;AAGT,kBAAI,KAAK,KAAM,KAAI,OAAO;AAG1B,qBAAO,SAAS,IAAI,GAAG,IAAI;AAC3B,sBAAQ,KAAK;AACb,qBAAO,IAAI;AAMX,qBAAOA,SAAQ,MAAM,KAAK,OAAO,IAAI,KAAK,GAAG;AAC3C;AAGA,yBAAS,MAAM,KAAK,QAAQ,KAAK,IAAI,OAAO,IAAI;AAChD,wBAAQ,KAAK;AACb,sBAAM;AAAA,cACR;AAAA,YACF,OAAO;AAML,kBAAI,KAAK,GAAG;AAGV,sBAAM,IAAI;AAAA,cACZ;AAGA,qBAAO,GAAG,MAAM;AAChB,sBAAQ,KAAK;AAAA,YACf;AAEA,gBAAI,QAAQ,KAAM,QAAO,CAAC,CAAC,EAAE,OAAO,IAAI;AAGxC,qBAAS,KAAK,MAAM,MAAM,IAAI;AAC9B,mBAAO,IAAI;AAGX,gBAAI,OAAO,IAAI;AAMb,qBAAOA,SAAQ,IAAI,KAAK,IAAI,IAAI,IAAI,GAAG;AACrC;AAGA,yBAAS,KAAK,KAAK,OAAO,KAAK,IAAI,MAAM,IAAI;AAC7C,uBAAO,IAAI;AAAA,cACb;AAAA,YACF;AAAA,UACF,WAAW,QAAQ,GAAG;AACpB;AACA,kBAAM,CAAC,CAAC;AAAA,UACV;AAGA,aAAG,GAAG,IAAI;AAGV,cAAI,IAAI,CAAC,GAAG;AACV,gBAAI,MAAM,IAAI,GAAG,EAAE,KAAK;AAAA,UAC1B,OAAO;AACL,kBAAM,CAAC,GAAG,EAAE,CAAC;AACb,mBAAO;AAAA,UACT;AAAA,QACF,UAAU,OAAO,MAAM,IAAI,CAAC,KAAK,SAAS;AAE1C,eAAO,IAAI,CAAC,KAAK;AAGjB,YAAI,CAAC,GAAG,CAAC,EAAG,IAAG,OAAO,GAAG,CAAC;AAAA,MAC5B;AAEA,UAAI,QAAQ,MAAM;AAGhB,aAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI;AAE7C,cAAM,GAAG,MAAM,EAAE,IAAI,IAAI,IAAI,WAAW,KAAK,GAAG,IAAI,IAAI;AAAA,MAG1D,OAAO;AACL,UAAE,IAAI;AACN,UAAE,IAAI,CAAC;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAAA,EACF,EAAG;AAYH,WAAS,OAAO,GAAG,GAAG,IAAI,IAAI;AAC5B,QAAI,IAAI,GAAG,IAAI,KAAK;AAEpB,QAAI,MAAM,KAAM,MAAK;AAAA,QAChB,UAAS,IAAI,GAAG,CAAC;AAEtB,QAAI,CAAC,EAAE,EAAG,QAAO,EAAE,SAAS;AAE5B,SAAK,EAAE,EAAE,CAAC;AACV,SAAK,EAAE;AAEP,QAAI,KAAK,MAAM;AACb,YAAM,cAAc,EAAE,CAAC;AACvB,YAAM,MAAM,KAAK,MAAM,MAAM,MAAM,cAAc,MAAM,cACpD,cAAc,KAAK,EAAE,IACrB,aAAa,KAAK,IAAI,GAAG;AAAA,IAC9B,OAAO;AACL,UAAI,MAAM,IAAID,WAAU,CAAC,GAAG,GAAG,EAAE;AAGjC,UAAI,EAAE;AAEN,YAAM,cAAc,EAAE,CAAC;AACvB,YAAM,IAAI;AAOV,UAAI,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,KAAK,aAAa;AAGrD,eAAO,MAAM,GAAG,OAAO,KAAK,MAAM;AAClC,cAAM,cAAc,KAAK,CAAC;AAAA,MAG5B,OAAO;AACL,aAAK;AACL,cAAM,aAAa,KAAK,GAAG,GAAG;AAG9B,YAAI,IAAI,IAAI,KAAK;AACf,cAAI,EAAE,IAAI,EAAG,MAAK,OAAO,KAAK,KAAK,OAAO,IAAI;AAAA,QAChD,OAAO;AACL,eAAK,IAAI;AACT,cAAI,IAAI,GAAG;AACT,gBAAI,IAAI,KAAK,IAAK,QAAO;AACzB,mBAAO,KAAK,OAAO,IAAI;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,EAAE,IAAI,KAAK,KAAK,MAAM,MAAM;AAAA,EACrC;AAKA,WAAS,SAAS,MAAM,GAAG;AACzB,QAAI,GAAG,GACL,IAAI,GACJ,IAAI,IAAIA,WAAU,KAAK,CAAC,CAAC;AAE3B,WAAO,IAAI,KAAK,QAAQ,KAAK;AAC3B,UAAI,IAAIA,WAAU,KAAK,CAAC,CAAC;AACzB,UAAI,CAAC,EAAE,MAAM,IAAI,QAAQ,GAAG,CAAC,OAAO,KAAK,MAAM,KAAK,EAAE,MAAM,GAAG;AAC7D,YAAI;AAAA,MACN;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAOA,WAAS,UAAU,GAAG,GAAG,GAAG;AAC1B,QAAI,IAAI,GACN,IAAI,EAAE;AAGR,WAAO,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE;AAGxB,SAAK,IAAI,EAAE,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI;AAGrC,SAAK,IAAI,IAAI,IAAI,WAAW,KAAK,SAAS;AAGxC,QAAE,IAAI,EAAE,IAAI;AAAA,IAGd,WAAW,IAAI,SAAS;AAGtB,QAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,IAChB,OAAO;AACL,QAAE,IAAI;AACN,QAAE,IAAI;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAIA,iBAAgB,2BAAY;AAC1B,QAAI,aAAa,+BACf,WAAW,eACX,YAAY,eACZ,kBAAkB,sBAClB,mBAAmB;AAErB,WAAO,SAAU,GAAG,KAAK,OAAO,GAAG;AACjC,UAAI,MACF,IAAI,QAAQ,MAAM,IAAI,QAAQ,kBAAkB,EAAE;AAGpD,UAAI,gBAAgB,KAAK,CAAC,GAAG;AAC3B,UAAE,IAAI,MAAM,CAAC,IAAI,OAAO,IAAI,IAAI,KAAK;AAAA,MACvC,OAAO;AACL,YAAI,CAAC,OAAO;AAGV,cAAI,EAAE,QAAQ,YAAY,SAAU,GAAG,IAAI,IAAI;AAC7C,oBAAQ,KAAK,GAAG,YAAY,MAAM,MAAM,KAAK,MAAM,MAAM,IAAI;AAC7D,mBAAO,CAAC,KAAK,KAAK,OAAO,KAAK;AAAA,UAChC,CAAC;AAED,cAAI,GAAG;AACL,mBAAO;AAGP,gBAAI,EAAE,QAAQ,UAAU,IAAI,EAAE,QAAQ,WAAW,MAAM;AAAA,UACzD;AAEA,cAAI,OAAO,EAAG,QAAO,IAAIA,WAAU,GAAG,IAAI;AAAA,QAC5C;AAIA,YAAIA,WAAU,OAAO;AACnB,gBAAM,MACH,iBAAiB,WAAW,IAAI,WAAW,IAAI,MAAM,cAAc,GAAG;AAAA,QAC3E;AAGA,UAAE,IAAI;AAAA,MACR;AAEA,QAAE,IAAI,EAAE,IAAI;AAAA,IACd;AAAA,EACF,EAAG;AAOH,WAAS,MAAM,GAAG,IAAI,IAAI,GAAG;AAC3B,QAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IACrB,KAAK,EAAE,GACP,SAAS;AAGX,QAAI,IAAI;AAQN,WAAK;AAGH,aAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI;AAC7C,YAAI,KAAK;AAGT,YAAI,IAAI,GAAG;AACT,eAAK;AACL,cAAI;AACJ,cAAI,GAAG,KAAK,CAAC;AAGb,eAAK,UAAU,IAAI,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE;AAAA,QAC3C,OAAO;AACL,eAAK,UAAU,IAAI,KAAK,QAAQ;AAEhC,cAAI,MAAM,GAAG,QAAQ;AAEnB,gBAAI,GAAG;AAGL,qBAAO,GAAG,UAAU,IAAI,GAAG,KAAK,CAAC,EAAE;AACnC,kBAAI,KAAK;AACT,kBAAI;AACJ,mBAAK;AACL,kBAAI,IAAI,WAAW;AAAA,YACrB,OAAO;AACL,oBAAM;AAAA,YACR;AAAA,UACF,OAAO;AACL,gBAAI,IAAI,GAAG,EAAE;AAGb,iBAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI;AAGlC,iBAAK;AAIL,gBAAI,IAAI,WAAW;AAGnB,iBAAK,IAAI,IAAI,IAAI,UAAU,IAAI,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE;AAAA,UACvD;AAAA,QACF;AAEA,YAAI,KAAK,KAAK;AAAA;AAAA;AAAA,QAKb,GAAG,KAAK,CAAC,KAAK,SAAS,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC;AAExD,YAAI,KAAK,KACL,MAAM,OAAO,MAAM,KAAK,OAAO,EAAE,IAAI,IAAI,IAAI,MAC9C,KAAK,KAAK,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM;AAAA,SAG3C,IAAI,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,KAAK,KAAM,KAC7D,OAAO,EAAE,IAAI,IAAI,IAAI;AAExB,YAAI,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG;AACpB,aAAG,SAAS;AAEZ,cAAI,GAAG;AAGL,kBAAM,EAAE,IAAI;AAGZ,eAAG,CAAC,IAAI,QAAQ,WAAW,KAAK,YAAY,QAAQ;AACpD,cAAE,IAAI,CAAC,MAAM;AAAA,UACf,OAAO;AAGL,eAAG,CAAC,IAAI,EAAE,IAAI;AAAA,UAChB;AAEA,iBAAO;AAAA,QACT;AAGA,YAAI,KAAK,GAAG;AACV,aAAG,SAAS;AACZ,cAAI;AACJ;AAAA,QACF,OAAO;AACL,aAAG,SAAS,KAAK;AACjB,cAAI,OAAO,WAAW,CAAC;AAIvB,aAAG,EAAE,IAAI,IAAI,IAAI,UAAU,IAAI,OAAO,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI,IAAI;AAAA,QAClE;AAGA,YAAI,GAAG;AAEL,qBAAU;AAGR,gBAAI,MAAM,GAAG;AAGX,mBAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI;AAC7C,kBAAI,GAAG,CAAC,KAAK;AACb,mBAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI;AAGlC,kBAAI,KAAK,GAAG;AACV,kBAAE;AACF,oBAAI,GAAG,CAAC,KAAK,KAAM,IAAG,CAAC,IAAI;AAAA,cAC7B;AAEA;AAAA,YACF,OAAO;AACL,iBAAG,EAAE,KAAK;AACV,kBAAI,GAAG,EAAE,KAAK,KAAM;AACpB,iBAAG,IAAI,IAAI;AACX,kBAAI;AAAA,YACN;AAAA,UACF;AAAA,QACF;AAGA,aAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,MAAM,GAAG,GAAG,IAAI,EAAE;AAAA,MAC9C;AAGA,UAAI,EAAE,IAAI,SAAS;AACjB,UAAE,IAAI,EAAE,IAAI;AAAA,MAGd,WAAW,EAAE,IAAI,SAAS;AACxB,UAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,MAChB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAGA,WAAS,QAAQ,GAAG;AAClB,QAAI,KACF,IAAI,EAAE;AAER,QAAI,MAAM,KAAM,QAAO,EAAE,SAAS;AAElC,UAAM,cAAc,EAAE,CAAC;AAEvB,UAAM,KAAK,cAAc,KAAK,aAC1B,cAAc,KAAK,CAAC,IACpB,aAAa,KAAK,GAAG,GAAG;AAE5B,WAAO,EAAE,IAAI,IAAI,MAAM,MAAM;AAAA,EAC/B;AASA,IAAE,gBAAgB,EAAE,MAAM,WAAY;AACpC,QAAI,IAAI,IAAIA,WAAU,IAAI;AAC1B,QAAI,EAAE,IAAI,EAAG,GAAE,IAAI;AACnB,WAAO;AAAA,EACT;AAUA,IAAE,aAAa,SAAU,GAAG,GAAG;AAC7B,WAAO,QAAQ,MAAM,IAAIA,WAAU,GAAG,CAAC,CAAC;AAAA,EAC1C;AAgBA,IAAE,gBAAgB,EAAE,KAAK,SAAU,IAAI,IAAI;AACzC,QAAI,GAAG,GAAG,GACR,IAAI;AAEN,QAAI,MAAM,MAAM;AACd,eAAS,IAAI,GAAG,GAAG;AACnB,UAAI,MAAM,KAAM,MAAK;AAAA,UAChB,UAAS,IAAI,GAAG,CAAC;AAEtB,aAAO,MAAM,IAAIA,WAAU,CAAC,GAAG,KAAK,EAAE,IAAI,GAAG,EAAE;AAAA,IACjD;AAEA,QAAI,EAAE,IAAI,EAAE,GAAI,QAAO;AACvB,UAAM,IAAI,EAAE,SAAS,KAAK,SAAS,KAAK,IAAI,QAAQ,KAAK;AAGzD,QAAI,IAAI,EAAE,CAAC,EAAG,QAAO,IAAI,MAAM,GAAG,KAAK,IAAI,IAAI;AAC/C,QAAI,IAAI,EAAG,KAAI;AAEf,WAAO;AAAA,EACT;AAuBA,IAAE,YAAY,EAAE,MAAM,SAAU,GAAG,GAAG;AACpC,WAAO,IAAI,MAAM,IAAIA,WAAU,GAAG,CAAC,GAAG,gBAAgB,aAAa;AAAA,EACrE;AAOA,IAAE,qBAAqB,EAAE,OAAO,SAAU,GAAG,GAAG;AAC9C,WAAO,IAAI,MAAM,IAAIA,WAAU,GAAG,CAAC,GAAG,GAAG,CAAC;AAAA,EAC5C;AAkBA,IAAE,kBAAkB,EAAE,MAAM,SAAU,GAAG,GAAG;AAC1C,QAAI,MAAM,UAAU,GAAG,GAAG,MAAM,QAAQ,QAAQ,QAAQ,GACtD,IAAI;AAEN,QAAI,IAAIA,WAAU,CAAC;AAGnB,QAAI,EAAE,KAAK,CAAC,EAAE,UAAU,GAAG;AACzB,YAAM,MACH,iBAAiB,8BAA8B,QAAQ,CAAC,CAAC;AAAA,IAC9D;AAEA,QAAI,KAAK,KAAM,KAAI,IAAIA,WAAU,CAAC;AAGlC,aAAS,EAAE,IAAI;AAGf,QAAI,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,UAAU,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG;AAIhF,UAAI,IAAIA,WAAU,KAAK,IAAI,CAAC,QAAQ,CAAC,GAAG,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;AACpF,aAAO,IAAI,EAAE,IAAI,CAAC,IAAI;AAAA,IACxB;AAEA,aAAS,EAAE,IAAI;AAEf,QAAI,GAAG;AAGL,UAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,EAAG,QAAO,IAAIA,WAAU,GAAG;AAElD,iBAAW,CAAC,UAAU,EAAE,UAAU,KAAK,EAAE,UAAU;AAEnD,UAAI,SAAU,KAAI,EAAE,IAAI,CAAC;AAAA,IAI3B,WAAW,EAAE,IAAI,MAAM,EAAE,IAAI,KAAK,EAAE,IAAI,OAAO,EAAE,KAAK,IAElD,EAAE,EAAE,CAAC,IAAI,KAAK,UAAU,EAAE,EAAE,CAAC,KAAK,OAElC,EAAE,EAAE,CAAC,IAAI,QAAQ,UAAU,EAAE,EAAE,CAAC,KAAK,aAAa;AAGpD,UAAI,EAAE,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK;AAG/B,UAAI,EAAE,IAAI,GAAI,KAAI,IAAI;AAGtB,aAAO,IAAIA,WAAU,SAAS,IAAI,IAAI,CAAC;AAAA,IAEzC,WAAW,eAAe;AAKxB,UAAI,SAAS,gBAAgB,WAAW,CAAC;AAAA,IAC3C;AAEA,QAAI,QAAQ;AACV,aAAO,IAAIA,WAAU,GAAG;AACxB,UAAI,OAAQ,GAAE,IAAI;AAClB,eAAS,MAAM,CAAC;AAAA,IAClB,OAAO;AACL,UAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxB,eAAS,IAAI;AAAA,IACf;AAEA,QAAI,IAAIA,WAAU,GAAG;AAGrB,eAAU;AAER,UAAI,QAAQ;AACV,YAAI,EAAE,MAAM,CAAC;AACb,YAAI,CAAC,EAAE,EAAG;AAEV,YAAI,GAAG;AACL,cAAI,EAAE,EAAE,SAAS,EAAG,GAAE,EAAE,SAAS;AAAA,QACnC,WAAW,UAAU;AACnB,cAAI,EAAE,IAAI,CAAC;AAAA,QACb;AAAA,MACF;AAEA,UAAI,GAAG;AACL,YAAI,UAAU,IAAI,CAAC;AACnB,YAAI,MAAM,EAAG;AACb,iBAAS,IAAI;AAAA,MACf,OAAO;AACL,YAAI,EAAE,MAAM,IAAI;AAChB,cAAM,GAAG,EAAE,IAAI,GAAG,CAAC;AAEnB,YAAI,EAAE,IAAI,IAAI;AACZ,mBAAS,MAAM,CAAC;AAAA,QAClB,OAAO;AACL,cAAI,CAAC,QAAQ,CAAC;AACd,cAAI,MAAM,EAAG;AACb,mBAAS,IAAI;AAAA,QACf;AAAA,MACF;AAEA,UAAI,EAAE,MAAM,CAAC;AAEb,UAAI,GAAG;AACL,YAAI,EAAE,KAAK,EAAE,EAAE,SAAS,EAAG,GAAE,EAAE,SAAS;AAAA,MAC1C,WAAW,UAAU;AACnB,YAAI,EAAE,IAAI,CAAC;AAAA,MACb;AAAA,IACF;AAEA,QAAI,SAAU,QAAO;AACrB,QAAI,OAAQ,KAAI,IAAI,IAAI,CAAC;AAEzB,WAAO,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,MAAM,GAAG,eAAe,eAAe,IAAI,IAAI;AAAA,EAC3E;AAWA,IAAE,eAAe,SAAU,IAAI;AAC7B,QAAI,IAAI,IAAIA,WAAU,IAAI;AAC1B,QAAI,MAAM,KAAM,MAAK;AAAA,QAChB,UAAS,IAAI,GAAG,CAAC;AACtB,WAAO,MAAM,GAAG,EAAE,IAAI,GAAG,EAAE;AAAA,EAC7B;AAOA,IAAE,YAAY,EAAE,KAAK,SAAU,GAAG,GAAG;AACnC,WAAO,QAAQ,MAAM,IAAIA,WAAU,GAAG,CAAC,CAAC,MAAM;AAAA,EAChD;AAMA,IAAE,WAAW,WAAY;AACvB,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAOA,IAAE,gBAAgB,EAAE,KAAK,SAAU,GAAG,GAAG;AACvC,WAAO,QAAQ,MAAM,IAAIA,WAAU,GAAG,CAAC,CAAC,IAAI;AAAA,EAC9C;AAOA,IAAE,yBAAyB,EAAE,MAAM,SAAU,GAAG,GAAG;AACjD,YAAQ,IAAI,QAAQ,MAAM,IAAIA,WAAU,GAAG,CAAC,CAAC,OAAO,KAAK,MAAM;AAAA,EAEjE;AAMA,IAAE,YAAY,WAAY;AACxB,WAAO,CAAC,CAAC,KAAK,KAAK,SAAS,KAAK,IAAI,QAAQ,IAAI,KAAK,EAAE,SAAS;AAAA,EACnE;AAOA,IAAE,aAAa,EAAE,KAAK,SAAU,GAAG,GAAG;AACpC,WAAO,QAAQ,MAAM,IAAIA,WAAU,GAAG,CAAC,CAAC,IAAI;AAAA,EAC9C;AAOA,IAAE,sBAAsB,EAAE,MAAM,SAAU,GAAG,GAAG;AAC9C,YAAQ,IAAI,QAAQ,MAAM,IAAIA,WAAU,GAAG,CAAC,CAAC,OAAO,MAAM,MAAM;AAAA,EAClE;AAMA,IAAE,QAAQ,WAAY;AACpB,WAAO,CAAC,KAAK;AAAA,EACf;AAMA,IAAE,aAAa,WAAY;AACzB,WAAO,KAAK,IAAI;AAAA,EAClB;AAMA,IAAE,aAAa,WAAY;AACzB,WAAO,KAAK,IAAI;AAAA,EAClB;AAMA,IAAE,SAAS,WAAY;AACrB,WAAO,CAAC,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC,KAAK;AAAA,EAClC;AAuBA,IAAE,QAAQ,SAAU,GAAG,GAAG;AACxB,QAAI,GAAG,GAAG,GAAG,MACX,IAAI,MACJ,IAAI,EAAE;AAER,QAAI,IAAIA,WAAU,GAAG,CAAC;AACtB,QAAI,EAAE;AAGN,QAAI,CAAC,KAAK,CAAC,EAAG,QAAO,IAAIA,WAAU,GAAG;AAGtC,QAAI,KAAK,GAAG;AACV,QAAE,IAAI,CAAC;AACP,aAAO,EAAE,KAAK,CAAC;AAAA,IACjB;AAEA,QAAI,KAAK,EAAE,IAAI,UACb,KAAK,EAAE,IAAI,UACX,KAAK,EAAE,GACP,KAAK,EAAE;AAET,QAAI,CAAC,MAAM,CAAC,IAAI;AAGd,UAAI,CAAC,MAAM,CAAC,GAAI,QAAO,MAAM,EAAE,IAAI,CAAC,GAAG,KAAK,IAAIA,WAAU,KAAK,IAAI,GAAG;AAGtE,UAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG;AAGpB,eAAO,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK,IAAIA,WAAU,GAAG,CAAC,IAAI;AAAA;AAAA,UAGpD,iBAAiB,IAAI,KAAK;AAAA,SAAC;AAAA,MAC9B;AAAA,IACF;AAEA,SAAK,SAAS,EAAE;AAChB,SAAK,SAAS,EAAE;AAChB,SAAK,GAAG,MAAM;AAGd,QAAI,IAAI,KAAK,IAAI;AAEf,UAAI,OAAO,IAAI,GAAG;AAChB,YAAI,CAAC;AACL,YAAI;AAAA,MACN,OAAO;AACL,aAAK;AACL,YAAI;AAAA,MACN;AAEA,QAAE,QAAQ;AAGV,WAAK,IAAI,GAAG,KAAK,EAAE,KAAK,CAAC,EAAE;AAC3B,QAAE,QAAQ;AAAA,IACZ,OAAO;AAGL,WAAK,QAAQ,IAAI,GAAG,WAAW,IAAI,GAAG,WAAW,IAAI;AAErD,WAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK;AAE1B,YAAI,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG;AAClB,iBAAO,GAAG,CAAC,IAAI,GAAG,CAAC;AACnB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,QAAI,MAAM;AACR,UAAI;AACJ,WAAK;AACL,WAAK;AACL,QAAE,IAAI,CAAC,EAAE;AAAA,IACX;AAEA,SAAK,IAAI,GAAG,WAAW,IAAI,GAAG;AAI9B,QAAI,IAAI,EAAG,QAAO,KAAK,GAAG,GAAG,IAAI,EAAE;AACnC,QAAI,OAAO;AAGX,WAAO,IAAI,KAAI;AAEb,UAAI,GAAG,EAAE,CAAC,IAAI,GAAG,CAAC,GAAG;AACnB,aAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE;AACrC,UAAE,GAAG,CAAC;AACN,WAAG,CAAC,KAAK;AAAA,MACX;AAEA,SAAG,CAAC,KAAK,GAAG,CAAC;AAAA,IACf;AAGA,WAAO,GAAG,CAAC,KAAK,GAAG,GAAG,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG;AAGzC,QAAI,CAAC,GAAG,CAAC,GAAG;AAIV,QAAE,IAAI,iBAAiB,IAAI,KAAK;AAChC,QAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AACd,aAAO;AAAA,IACT;AAIA,WAAO,UAAU,GAAG,IAAI,EAAE;AAAA,EAC5B;AAwBA,IAAE,SAAS,EAAE,MAAM,SAAU,GAAG,GAAG;AACjC,QAAI,GAAG,GACL,IAAI;AAEN,QAAI,IAAIA,WAAU,GAAG,CAAC;AAGtB,QAAI,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG;AAClC,aAAO,IAAIA,WAAU,GAAG;AAAA,IAG1B,WAAW,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG;AACjC,aAAO,IAAIA,WAAU,CAAC;AAAA,IACxB;AAEA,QAAI,eAAe,GAAG;AAIpB,UAAI,EAAE;AACN,QAAE,IAAI;AACN,UAAI,IAAI,GAAG,GAAG,GAAG,CAAC;AAClB,QAAE,IAAI;AACN,QAAE,KAAK;AAAA,IACT,OAAO;AACL,UAAI,IAAI,GAAG,GAAG,GAAG,WAAW;AAAA,IAC9B;AAEA,QAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAGtB,QAAI,CAAC,EAAE,EAAE,CAAC,KAAK,eAAe,EAAG,GAAE,IAAI,EAAE;AAEzC,WAAO;AAAA,EACT;AAuBA,IAAE,eAAe,EAAE,QAAQ,SAAU,GAAG,GAAG;AACzC,QAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAClD,MAAM,UACN,IAAI,MACJ,KAAK,EAAE,GACP,MAAM,IAAI,IAAIA,WAAU,GAAG,CAAC,GAAG;AAGjC,QAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG;AAGlC,UAAI,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI;AAC9D,UAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAAA,MACpB,OAAO;AACL,UAAE,KAAK,EAAE;AAGT,YAAI,CAAC,MAAM,CAAC,IAAI;AACd,YAAE,IAAI,EAAE,IAAI;AAAA,QAGd,OAAO;AACL,YAAE,IAAI,CAAC,CAAC;AACR,YAAE,IAAI;AAAA,QACR;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,EAAE,IAAI,QAAQ,IAAI,SAAS,EAAE,IAAI,QAAQ;AACtD,MAAE,KAAK,EAAE;AACT,UAAM,GAAG;AACT,UAAM,GAAG;AAGT,QAAI,MAAM,KAAK;AACb,WAAK;AACL,WAAK;AACL,WAAK;AACL,UAAI;AACJ,YAAM;AACN,YAAM;AAAA,IACR;AAGA,SAAK,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,EAAE;AAE7C,WAAO;AACP,eAAW;AAEX,SAAK,IAAI,KAAK,EAAE,KAAK,KAAI;AACvB,UAAI;AACJ,YAAM,GAAG,CAAC,IAAI;AACd,YAAM,GAAG,CAAC,IAAI,WAAW;AAEzB,WAAK,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,KAAI;AAC/B,cAAM,GAAG,EAAE,CAAC,IAAI;AAChB,cAAM,GAAG,CAAC,IAAI,WAAW;AACzB,YAAI,MAAM,MAAM,MAAM;AACtB,cAAM,MAAM,MAAQ,IAAI,WAAY,WAAY,GAAG,CAAC,IAAI;AACxD,aAAK,MAAM,OAAO,MAAM,IAAI,WAAW,KAAK,MAAM;AAClD,WAAG,GAAG,IAAI,MAAM;AAAA,MAClB;AAEA,SAAG,CAAC,IAAI;AAAA,IACV;AAEA,QAAI,GAAG;AACL,QAAE;AAAA,IACJ,OAAO;AACL,SAAG,OAAO,GAAG,CAAC;AAAA,IAChB;AAEA,WAAO,UAAU,GAAG,IAAI,CAAC;AAAA,EAC3B;AAOA,IAAE,UAAU,WAAY;AACtB,QAAI,IAAI,IAAIA,WAAU,IAAI;AAC1B,MAAE,IAAI,CAAC,EAAE,KAAK;AACd,WAAO;AAAA,EACT;AAuBA,IAAE,OAAO,SAAU,GAAG,GAAG;AACvB,QAAI,GACF,IAAI,MACJ,IAAI,EAAE;AAER,QAAI,IAAIA,WAAU,GAAG,CAAC;AACtB,QAAI,EAAE;AAGN,QAAI,CAAC,KAAK,CAAC,EAAG,QAAO,IAAIA,WAAU,GAAG;AAGrC,QAAI,KAAK,GAAG;AACX,QAAE,IAAI,CAAC;AACP,aAAO,EAAE,MAAM,CAAC;AAAA,IAClB;AAEA,QAAI,KAAK,EAAE,IAAI,UACb,KAAK,EAAE,IAAI,UACX,KAAK,EAAE,GACP,KAAK,EAAE;AAET,QAAI,CAAC,MAAM,CAAC,IAAI;AAGd,UAAI,CAAC,MAAM,CAAC,GAAI,QAAO,IAAIA,WAAU,IAAI,CAAC;AAI1C,UAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAG,QAAO,GAAG,CAAC,IAAI,IAAI,IAAIA,WAAU,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC;AAAA,IAC1E;AAEA,SAAK,SAAS,EAAE;AAChB,SAAK,SAAS,EAAE;AAChB,SAAK,GAAG,MAAM;AAGd,QAAI,IAAI,KAAK,IAAI;AACf,UAAI,IAAI,GAAG;AACT,aAAK;AACL,YAAI;AAAA,MACN,OAAO;AACL,YAAI,CAAC;AACL,YAAI;AAAA,MACN;AAEA,QAAE,QAAQ;AACV,aAAO,KAAK,EAAE,KAAK,CAAC,EAAE;AACtB,QAAE,QAAQ;AAAA,IACZ;AAEA,QAAI,GAAG;AACP,QAAI,GAAG;AAGP,QAAI,IAAI,IAAI,GAAG;AACb,UAAI;AACJ,WAAK;AACL,WAAK;AACL,UAAI;AAAA,IACN;AAGA,SAAK,IAAI,GAAG,KAAI;AACd,WAAK,GAAG,EAAE,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,OAAO;AAC3C,SAAG,CAAC,IAAI,SAAS,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI;AAAA,IACvC;AAEA,QAAI,GAAG;AACL,WAAK,CAAC,CAAC,EAAE,OAAO,EAAE;AAClB,QAAE;AAAA,IACJ;AAIA,WAAO,UAAU,GAAG,IAAI,EAAE;AAAA,EAC5B;AAkBA,IAAE,YAAY,EAAE,KAAK,SAAU,IAAI,IAAI;AACrC,QAAI,GAAG,GAAG,GACR,IAAI;AAEN,QAAI,MAAM,QAAQ,OAAO,CAAC,CAAC,IAAI;AAC7B,eAAS,IAAI,GAAG,GAAG;AACnB,UAAI,MAAM,KAAM,MAAK;AAAA,UAChB,UAAS,IAAI,GAAG,CAAC;AAEtB,aAAO,MAAM,IAAIA,WAAU,CAAC,GAAG,IAAI,EAAE;AAAA,IACvC;AAEA,QAAI,EAAE,IAAI,EAAE,GAAI,QAAO;AACvB,QAAI,EAAE,SAAS;AACf,QAAI,IAAI,WAAW;AAEnB,QAAI,IAAI,EAAE,CAAC,GAAG;AAGZ,aAAO,IAAI,MAAM,GAAG,KAAK,IAAI,IAAI;AAGjC,WAAK,IAAI,EAAE,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI;AAAA,IACvC;AAEA,QAAI,MAAM,EAAE,IAAI,IAAI,EAAG,KAAI,EAAE,IAAI;AAEjC,WAAO;AAAA,EACT;AAWA,IAAE,YAAY,SAAU,GAAG;AACzB,aAAS,GAAG,CAAC,kBAAkB,gBAAgB;AAC/C,WAAO,KAAK,MAAM,OAAO,CAAC;AAAA,EAC5B;AAcA,IAAE,aAAa,EAAE,OAAO,WAAY;AAClC,QAAI,GAAG,GAAG,GAAG,KAAK,GAChB,IAAI,MACJ,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE,GACN,KAAK,iBAAiB,GACtB,OAAO,IAAIA,WAAU,KAAK;AAG5B,QAAI,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;AAC1B,aAAO,IAAIA,WAAU,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC;AAAA,IACxE;AAGA,QAAI,KAAK,KAAK,CAAC,QAAQ,CAAC,CAAC;AAIzB,QAAI,KAAK,KAAK,KAAK,IAAI,GAAG;AACxB,UAAI,cAAc,CAAC;AACnB,WAAK,EAAE,SAAS,KAAK,KAAK,EAAG,MAAK;AAClC,UAAI,KAAK,KAAK,CAAC,CAAC;AAChB,UAAI,UAAU,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,IAAI;AAE1C,UAAI,KAAK,IAAI,GAAG;AACd,YAAI,OAAO;AAAA,MACb,OAAO;AACL,YAAI,EAAE,cAAc;AACpB,YAAI,EAAE,MAAM,GAAG,EAAE,QAAQ,GAAG,IAAI,CAAC,IAAI;AAAA,MACvC;AAEA,UAAI,IAAIA,WAAU,CAAC;AAAA,IACrB,OAAO;AACL,UAAI,IAAIA,WAAU,IAAI,EAAE;AAAA,IAC1B;AAMA,QAAI,EAAE,EAAE,CAAC,GAAG;AACV,UAAI,EAAE;AACN,UAAI,IAAI;AACR,UAAI,IAAI,EAAG,KAAI;AAGf,iBAAU;AACR,YAAI;AACJ,YAAI,KAAK,MAAM,EAAE,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;AAEvC,YAAI,cAAc,EAAE,CAAC,EAAE,MAAM,GAAG,CAAC,OAAO,IAAI,cAAc,EAAE,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG;AAK3E,cAAI,EAAE,IAAI,EAAG,GAAE;AACf,cAAI,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC;AAKxB,cAAI,KAAK,UAAU,CAAC,OAAO,KAAK,QAAQ;AAItC,gBAAI,CAAC,KAAK;AACR,oBAAM,GAAG,EAAE,IAAI,iBAAiB,GAAG,CAAC;AAEpC,kBAAI,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG;AACpB,oBAAI;AACJ;AAAA,cACF;AAAA,YACF;AAEA,kBAAM;AACN,iBAAK;AACL,kBAAM;AAAA,UACR,OAAO;AAIL,gBAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,KAAK;AAG7C,oBAAM,GAAG,EAAE,IAAI,iBAAiB,GAAG,CAAC;AACpC,kBAAI,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC;AAAA,YACtB;AAEA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,MAAM,GAAG,EAAE,IAAI,iBAAiB,GAAG,eAAe,CAAC;AAAA,EAC5D;AAYA,IAAE,gBAAgB,SAAU,IAAI,IAAI;AAClC,QAAI,MAAM,MAAM;AACd,eAAS,IAAI,GAAG,GAAG;AACnB;AAAA,IACF;AACA,WAAO,OAAO,MAAM,IAAI,IAAI,CAAC;AAAA,EAC/B;AAeA,IAAE,UAAU,SAAU,IAAI,IAAI;AAC5B,QAAI,MAAM,MAAM;AACd,eAAS,IAAI,GAAG,GAAG;AACnB,WAAK,KAAK,KAAK,IAAI;AAAA,IACrB;AACA,WAAO,OAAO,MAAM,IAAI,EAAE;AAAA,EAC5B;AA4BA,IAAE,WAAW,SAAU,IAAI,IAAIE,SAAQ;AACrC,QAAI,KACF,IAAI;AAEN,QAAIA,WAAU,MAAM;AAClB,UAAI,MAAM,QAAQ,MAAM,OAAO,MAAM,UAAU;AAC7C,QAAAA,UAAS;AACT,aAAK;AAAA,MACP,WAAW,MAAM,OAAO,MAAM,UAAU;AACtC,QAAAA,UAAS;AACT,aAAK,KAAK;AAAA,MACZ,OAAO;AACL,QAAAA,UAAS;AAAA,MACX;AAAA,IACF,WAAW,OAAOA,WAAU,UAAU;AACpC,YAAM,MACH,iBAAiB,6BAA6BA,OAAM;AAAA,IACzD;AAEA,UAAM,EAAE,QAAQ,IAAI,EAAE;AAEtB,QAAI,EAAE,GAAG;AACP,UAAI,GACF,MAAM,IAAI,MAAM,GAAG,GACnB,KAAK,CAACA,QAAO,WACb,KAAK,CAACA,QAAO,oBACb,iBAAiBA,QAAO,kBAAkB,IAC1C,UAAU,IAAI,CAAC,GACf,eAAe,IAAI,CAAC,GACpB,QAAQ,EAAE,IAAI,GACd,YAAY,QAAQ,QAAQ,MAAM,CAAC,IAAI,SACvC,MAAM,UAAU;AAElB,UAAI,IAAI;AACN,YAAI;AACJ,aAAK;AACL,aAAK;AACL,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,KAAK,MAAM,GAAG;AACrB,YAAI,MAAM,MAAM;AAChB,kBAAU,UAAU,OAAO,GAAG,CAAC;AAC/B,eAAO,IAAI,KAAK,KAAK,GAAI,YAAW,iBAAiB,UAAU,OAAO,GAAG,EAAE;AAC3E,YAAI,KAAK,EAAG,YAAW,iBAAiB,UAAU,MAAM,CAAC;AACzD,YAAI,MAAO,WAAU,MAAM;AAAA,MAC7B;AAEA,YAAM,eACH,WAAWA,QAAO,oBAAoB,QAAQ,KAAK,CAACA,QAAO,qBAC1D,aAAa;AAAA,QAAQ,IAAI,OAAO,SAAS,KAAK,QAAQ,GAAG;AAAA,QAC1D,QAAQA,QAAO,0BAA0B;AAAA,MAAG,IAC3C,gBACD;AAAA,IACL;AAEA,YAAQA,QAAO,UAAU,MAAM,OAAOA,QAAO,UAAU;AAAA,EACzD;AAcA,IAAE,aAAa,SAAU,IAAI;AAC3B,QAAI,GAAG,IAAI,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,IAAI,GAAG,GAAG,GAC1C,IAAI,MACJ,KAAK,EAAE;AAET,QAAI,MAAM,MAAM;AACd,UAAI,IAAIF,WAAU,EAAE;AAGpB,UAAI,CAAC,EAAE,UAAU,MAAM,EAAE,KAAK,EAAE,MAAM,MAAM,EAAE,GAAG,GAAG,GAAG;AACrD,cAAM,MACH,iBAAiB,eACf,EAAE,UAAU,IAAI,mBAAmB,sBAAsB,QAAQ,CAAC,CAAC;AAAA,MAC1E;AAAA,IACF;AAEA,QAAI,CAAC,GAAI,QAAO,IAAIA,WAAU,CAAC;AAE/B,QAAI,IAAIA,WAAU,GAAG;AACrB,SAAK,KAAK,IAAIA,WAAU,GAAG;AAC3B,SAAK,KAAK,IAAIA,WAAU,GAAG;AAC3B,QAAI,cAAc,EAAE;AAIpB,QAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI;AAC3B,MAAE,EAAE,CAAC,IAAI,UAAU,MAAM,IAAI,YAAY,IAAI,WAAW,MAAM,GAAG;AACjE,SAAK,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,IAAK,IAAI,IAAI,IAAI,KAAM;AAErD,UAAM;AACN,cAAU,IAAI;AACd,QAAI,IAAIA,WAAU,CAAC;AAGnB,OAAG,EAAE,CAAC,IAAI;AAEV,eAAW;AACT,UAAI,IAAI,GAAG,GAAG,GAAG,CAAC;AAClB,WAAK,GAAG,KAAK,EAAE,MAAM,EAAE,CAAC;AACxB,UAAI,GAAG,WAAW,EAAE,KAAK,EAAG;AAC5B,WAAK;AACL,WAAK;AACL,WAAK,GAAG,KAAK,EAAE,MAAM,KAAK,EAAE,CAAC;AAC7B,WAAK;AACL,UAAI,EAAE,MAAM,EAAE,MAAM,KAAK,CAAC,CAAC;AAC3B,UAAI;AAAA,IACN;AAEA,SAAK,IAAI,GAAG,MAAM,EAAE,GAAG,IAAI,GAAG,CAAC;AAC/B,SAAK,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC;AACzB,SAAK,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC;AACzB,OAAG,IAAI,GAAG,IAAI,EAAE;AAChB,QAAI,IAAI;AAGR,QAAI,IAAI,IAAI,IAAI,GAAG,aAAa,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE;AAAA,MAC7C,IAAI,IAAI,IAAI,GAAG,aAAa,EAAE,MAAM,CAAC,EAAE,IAAI;AAAA,IAAC,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;AAE1E,cAAU;AAEV,WAAO;AAAA,EACT;AAMA,IAAE,WAAW,WAAY;AACvB,WAAO,CAAC,QAAQ,IAAI;AAAA,EACtB;AAcA,IAAE,cAAc,SAAU,IAAI,IAAI;AAChC,QAAI,MAAM,KAAM,UAAS,IAAI,GAAG,GAAG;AACnC,WAAO,OAAO,MAAM,IAAI,IAAI,CAAC;AAAA,EAC/B;AAcA,IAAE,WAAW,SAAU,GAAG;AACxB,QAAI,KACF,IAAI,MACJ,IAAI,EAAE,GACN,IAAI,EAAE;AAGR,QAAI,MAAM,MAAM;AACd,UAAI,GAAG;AACL,cAAM;AACN,YAAI,IAAI,EAAG,OAAM,MAAM;AAAA,MACzB,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF,OAAO;AACL,UAAI,KAAK,MAAM;AACb,cAAM,KAAK,cAAc,KAAK,aAC3B,cAAc,cAAc,EAAE,CAAC,GAAG,CAAC,IACnC,aAAa,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG;AAAA,MAC5C,WAAW,MAAM,MAAM,gCAAgC;AACrD,YAAI,MAAM,IAAIA,WAAU,CAAC,GAAG,iBAAiB,IAAI,GAAG,aAAa;AACjE,cAAM,aAAa,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG;AAAA,MACjD,OAAO;AACL,iBAAS,GAAG,GAAG,SAAS,QAAQ,MAAM;AACtC,cAAM,YAAY,aAAa,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI;AAAA,MAC5E;AAEA,UAAI,IAAI,KAAK,EAAE,EAAE,CAAC,EAAG,OAAM,MAAM;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAOA,IAAE,UAAU,EAAE,SAAS,WAAY;AACjC,WAAO,QAAQ,IAAI;AAAA,EACrB;AAGA,IAAE,eAAe;AAEjB,IAAE,OAAO,WAAW,IAAI;AAGxB,IAAE,OAAO,IAAI,4BAA4B,CAAC,IAAI,EAAE;AAEhD,MAAI,gBAAgB,KAAM,CAAAA,WAAU,IAAI,YAAY;AAEpD,SAAOA;AACT;AASA,SAAS,SAAS,GAAG;AACnB,MAAI,IAAI,IAAI;AACZ,SAAO,IAAI,KAAK,MAAM,IAAI,IAAI,IAAI;AACpC;AAIA,SAAS,cAAc,GAAG;AACxB,MAAI,GAAG,GACL,IAAI,GACJ,IAAI,EAAE,QACN,IAAI,EAAE,CAAC,IAAI;AAEb,SAAO,IAAI,KAAI;AACb,QAAI,EAAE,GAAG,IAAI;AACb,QAAI,WAAW,EAAE;AACjB,WAAO,KAAK,IAAI,MAAM,EAAE;AACxB,SAAK;AAAA,EACP;AAGA,OAAK,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,MAAM,KAAI;AAE7C,SAAO,EAAE,MAAM,GAAG,IAAI,KAAK,CAAC;AAC9B;AAIA,SAAS,QAAQ,GAAG,GAAG;AACrB,MAAI,GAAG,GACL,KAAK,EAAE,GACP,KAAK,EAAE,GACP,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE;AAGR,MAAI,CAAC,KAAK,CAAC,EAAG,QAAO;AAErB,MAAI,MAAM,CAAC,GAAG,CAAC;AACf,MAAI,MAAM,CAAC,GAAG,CAAC;AAGf,MAAI,KAAK,EAAG,QAAO,IAAI,IAAI,IAAI,CAAC,IAAI;AAGpC,MAAI,KAAK,EAAG,QAAO;AAEnB,MAAI,IAAI;AACR,MAAI,KAAK;AAGT,MAAI,CAAC,MAAM,CAAC,GAAI,QAAO,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI;AAG7C,MAAI,CAAC,EAAG,QAAO,IAAI,IAAI,IAAI,IAAI;AAE/B,OAAK,IAAI,GAAG,WAAW,IAAI,GAAG,UAAU,IAAI;AAG5C,OAAK,IAAI,GAAG,IAAI,GAAG,IAAK,KAAI,GAAG,CAAC,KAAK,GAAG,CAAC,EAAG,QAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI;AAG3E,SAAO,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACtC;AAMA,SAAS,SAAS,GAAG,KAAK,KAAK,MAAM;AACnC,MAAI,IAAI,OAAO,IAAI,OAAO,MAAM,UAAU,CAAC,GAAG;AAC5C,UAAM,MACJ,kBAAkB,QAAQ,eAAe,OAAO,KAAK,WAClD,IAAI,OAAO,IAAI,MAAM,oBAAoB,sBACzC,+BAA+B,OAAO,CAAC,CAAC;AAAA,EAC/C;AACF;AAIA,SAAS,MAAM,GAAG;AAChB,MAAI,IAAI,EAAE,EAAE,SAAS;AACrB,SAAO,SAAS,EAAE,IAAI,QAAQ,KAAK,KAAK,EAAE,EAAE,CAAC,IAAI,KAAK;AACxD;AAGA,SAAS,cAAc,KAAK,GAAG;AAC7B,UAAQ,IAAI,SAAS,IAAI,IAAI,OAAO,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,QAC5D,IAAI,IAAI,MAAM,QAAQ;AAC1B;AAGA,SAAS,aAAa,KAAK,GAAG,GAAG;AAC/B,MAAI,KAAK;AAGT,MAAI,IAAI,GAAG;AAGT,SAAK,KAAK,IAAI,KAAK,EAAE,GAAG,MAAM,EAAE;AAChC,UAAM,KAAK;AAAA,EAGb,OAAO;AACL,UAAM,IAAI;AAGV,QAAI,EAAE,IAAI,KAAK;AACb,WAAK,KAAK,GAAG,KAAK,KAAK,EAAE,GAAG,MAAM,EAAE;AACpC,aAAO;AAAA,IACT,WAAW,IAAI,KAAK;AAClB,YAAM,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC;AAAA,IAC3C;AAAA,EACF;AAEA,SAAO;AACT;AAMO,IAAI,YAAY,MAAM;AAE7B,IAAO,oBAAQ;", "names": ["BigNumber", "compare", "format"]}