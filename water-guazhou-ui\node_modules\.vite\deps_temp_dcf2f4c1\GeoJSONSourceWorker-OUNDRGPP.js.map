{"version": 3, "sources": ["../../@arcgis/core/layers/graphics/sources/geojson/GeoJSONSourceWorker.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../request.js\";import{createTask as t}from\"../../../../core/asyncUtils.js\";import s from\"../../../../core/Error.js\";import i from\"../../../../core/Logger.js\";import{isSome as r}from\"../../../../core/maybe.js\";import{isAbortError as n}from\"../../../../core/promiseUtils.js\";import{getJsonType as a}from\"../../../../geometry/support/jsonUtils.js\";import{WGS84 as o,equals as u}from\"../../../../geometry/support/spatialReferenceUtils.js\";import{convertFromGeometry as l,convertToGeometry as d,convertFromFeatures as p,convertToFeature as c,convertFromFeature as h}from\"../../featureConversionUtils.js\";import y from\"../../data/FeatureStore.js\";import{checkProjectionSupport as m,project as f}from\"../../data/projectionSupport.js\";import{QueryEngine as g}from\"../../data/QueryEngine.js\";import{inferLayerProperties as _,createOptimizedFeatures as I,validateGeoJSON as j}from\"./geojson.js\";import{createDrawingInfo as F,createDefaultTemplate as E,createDefaultAttributesFunction as b}from\"../support/clientSideDefaults.js\";import{loadGeometryEngineForSimplify as w,mixAttributes as T,createFeatureEditSuccessResult as x,createFeatureEditErrorResult as q,simplify as S}from\"../support/sourceUtils.js\";import R from\"../../../support/FieldsIndex.js\";import{kebabDict as k}from\"../../../support/fieldType.js\";import{getFieldDefaultValue as O}from\"../../../support/fieldUtils.js\";const C={hasAttachments:!1,capabilities:\"query, editing, create, delete, update\",useStandardizedQueries:!0,supportsCoordinatesQuantization:!0,supportsReturningQueryGeometry:!0,advancedQueryCapabilities:{supportsQueryAttachments:!1,supportsStatistics:!0,supportsPercentileStatistics:!0,supportsReturningGeometryCentroid:!0,supportsQueryWithDistance:!0,supportsDistinct:!0,supportsReturningQueryExtent:!0,supportsReturningGeometryProperties:!1,supportsHavingClause:!0,supportsOrderBy:!0,supportsPagination:!0,supportsQueryWithResultType:!1,supportsSqlExpression:!0,supportsDisjointSpatialRel:!0}};class D{constructor(){this._queryEngine=null,this._snapshotFeatures=async e=>{const t=await this._fetch(e);return this._createFeatures(t)}}destroy(){this._queryEngine?.destroy(),this._queryEngine=this._fieldsIndex=this._createDefaultAttributes=null}async load(e,t={}){this._loadOptions={url:e.url,customParameters:e.customParameters};const i=[];await this._checkProjection(e.spatialReference);let r=null;e.url&&(r=await this._fetch(t?.signal));const n=_(r,{geometryType:e.geometryType}),a=e.fields||n.fields||[],u=null!=e.hasZ?e.hasZ:n.hasZ,l=n.geometryType;let d=e.objectIdField||n.objectIdFieldName||\"__OBJECTID\";const p=e.spatialReference||o;let c=e.timeInfo;a===n.fields&&n.unknownFields.length>0&&i.push({name:\"geojson-layer:unknown-field-types\",message:\"Some fields types couldn't be inferred from the features and were dropped\",details:{unknownFields:n.unknownFields}});let h=new R(a).get(d);h?(\"esriFieldTypeString\"!==h.type&&(h.type=\"esriFieldTypeOID\"),h.editable=!1,h.nullable=!1,d=h.name):(h={alias:d,name:d,type:\"string\"===n.objectIdFieldType?\"esriFieldTypeString\":\"esriFieldTypeOID\",editable:!1,nullable:!1},a.unshift(h));const m={};for(const o of a){if(null==o.name&&(o.name=o.alias),null==o.alias&&(o.alias=o.name),!o.name)throw new s(\"geojson-layer:invalid-field-name\",\"field name is missing\",{field:o});if(!k.jsonValues.includes(o.type))throw new s(\"geojson-layer:invalid-field-type\",`invalid type for field \"${o.name}\"`,{field:o});if(o.name!==h.name){const e=O(o);void 0!==e&&(m[o.name]=e)}}this._fieldsIndex=new R(a);const f=this._fieldsIndex.requiredFields.indexOf(h);if(f>-1&&this._fieldsIndex.requiredFields.splice(f,1),c){if(c.startTimeField){const e=this._fieldsIndex.get(c.startTimeField);e?(c.startTimeField=e.name,e.type=\"esriFieldTypeDate\"):c.startTimeField=null}if(c.endTimeField){const e=this._fieldsIndex.get(c.endTimeField);e?(c.endTimeField=e.name,e.type=\"esriFieldTypeDate\"):c.endTimeField=null}if(c.trackIdField){const e=this._fieldsIndex.get(c.trackIdField);e?c.trackIdField=e.name:(c.trackIdField=null,i.push({name:\"geojson-layer:invalid-timeInfo-trackIdField\",message:\"trackIdField is missing\",details:{timeInfo:c}}))}c.startTimeField||c.endTimeField||(i.push({name:\"geojson-layer:invalid-timeInfo\",message:\"startTimeField and endTimeField are missing\",details:{timeInfo:c}}),c=null)}const I=l?F(l):void 0,j={warnings:i,featureErrors:[],layerDefinition:{...C,drawingInfo:I??void 0,templates:E(m),extent:void 0,geometryType:l,objectIdField:d,fields:a,hasZ:!!u,timeInfo:c}};this._queryEngine=new g({fields:a,geometryType:l,hasM:!1,hasZ:u,objectIdField:d,spatialReference:p,timeInfo:c,featureStore:new y({geometryType:l,hasM:!1,hasZ:u}),cacheSpatialQueries:!0}),this._createDefaultAttributes=b(m,d);const w=await this._createFeatures(r);this._objectIdGenerator=this._createObjectIdGenerator(this._queryEngine,w);const T=this._normalizeFeatures(w,j.warnings,j.featureErrors);this._queryEngine.featureStore.addMany(T);const{fullExtent:x,timeExtent:q}=await this._queryEngine.fetchRecomputedExtents();if(j.layerDefinition.extent=x,q){const{start:e,end:t}=q;j.layerDefinition.timeInfo.timeExtent=[e,t]}return j}async applyEdits(e){const{spatialReference:t,geometryType:s}=this._queryEngine;return await Promise.all([w(t,s),m(e.adds,t),m(e.updates,t)]),await this._waitSnapshotComplete(),this._applyEdits(e)}async queryFeatures(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQuery(e,t.signal)}async queryFeatureCount(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForCount(e,t.signal)}async queryObjectIds(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForIds(e,t.signal)}async queryExtent(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForExtent(e,t.signal)}async querySnapping(e,t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForSnapping(e,t.signal)}async refresh(e){this._loadOptions.customParameters=e,this._snapshotTask?.abort(),this._snapshotTask=t(this._snapshotFeatures),this._snapshotTask.promise.then((e=>{this._queryEngine.featureStore.clear(),this._objectIdGenerator=this._createObjectIdGenerator(this._queryEngine,e);const t=this._normalizeFeatures(e);t&&this._queryEngine.featureStore.addMany(t)}),(e=>{this._queryEngine.featureStore.clear(),n(e)||i.getLogger(\"esri.layers.GeoJSONLayer\").error(new s(\"geojson-layer:refresh\",\"An error occurred during refresh\",{error:e}))})),await this._waitSnapshotComplete();const{fullExtent:r,timeExtent:a}=await this._queryEngine.fetchRecomputedExtents();return{extent:r,timeExtent:a}}async _createFeatures(e){if(null==e)return[];const{geometryType:t,hasZ:s,objectIdField:i}=this._queryEngine,n=I(e,{geometryType:t,hasZ:s,objectIdField:i});if(!u(this._queryEngine.spatialReference,o))for(const a of n)r(a.geometry)&&(a.geometry=l(f(d(a.geometry,this._queryEngine.geometryType,this._queryEngine.hasZ,!1),o,this._queryEngine.spatialReference)));return n}async _waitSnapshotComplete(){if(this._snapshotTask&&!this._snapshotTask.finished){try{await this._snapshotTask.promise}catch{}return this._waitSnapshotComplete()}}async _fetch(t){const{url:s,customParameters:i}=this._loadOptions,r=(await e(s,{responseType:\"json\",query:{...i},signal:t})).data;return await j(r),r}_normalizeFeatures(e,t,s){const{objectIdField:i}=this._queryEngine,r=[];for(const n of e){const e=this._createDefaultAttributes(),a=T(this._fieldsIndex,e,n.attributes,!0,t);a?s?.push(a):(this._assignObjectId(e,n.attributes,!0),n.attributes=e,n.objectId=e[i],r.push(n))}return r}async _applyEdits(e){const{adds:t,updates:s,deletes:i}=e,r={addResults:[],deleteResults:[],updateResults:[],uidToObjectId:{}};if(t&&t.length&&this._applyAddEdits(r,t),s&&s.length&&this._applyUpdateEdits(r,s),i&&i.length){for(const e of i)r.deleteResults.push(x(e));this._queryEngine.featureStore.removeManyById(i)}const{fullExtent:n,timeExtent:a}=await this._queryEngine.fetchRecomputedExtents();return{extent:n,timeExtent:a,featureEditResults:r}}_applyAddEdits(e,t){const{addResults:s}=e,{geometryType:i,hasM:n,hasZ:o,objectIdField:u,spatialReference:l,featureStore:d}=this._queryEngine,c=[];for(const p of t){if(p.geometry&&i!==a(p.geometry)){s.push(q(\"Incorrect geometry type.\"));continue}const t=this._createDefaultAttributes(),n=T(this._fieldsIndex,t,p.attributes);if(n)s.push(n);else{if(this._assignObjectId(t,p.attributes),p.attributes=t,null!=p.uid){const t=p.attributes[u];e.uidToObjectId[p.uid]=t}if(r(p.geometry)){const e=p.geometry.spatialReference??l;p.geometry=f(S(p.geometry,e),e,l)}c.push(p),s.push(x(p.attributes[u]))}}d.addMany(p([],c,i,o,n,u))}_applyUpdateEdits({updateResults:e},t){const{geometryType:s,hasM:i,hasZ:n,objectIdField:o,spatialReference:u,featureStore:l}=this._queryEngine;for(const d of t){const{attributes:t,geometry:p}=d,y=t&&t[o];if(null==y){e.push(q(`Identifier field ${o} missing`));continue}if(!l.has(y)){e.push(q(`Feature with object id ${y} missing`));continue}const m=c(l.getFeature(y),s,n,i);if(r(p)){if(s!==a(p)){e.push(q(\"Incorrect geometry type.\"));continue}const t=p.spatialReference??u;m.geometry=f(S(p,t),t,u)}if(t){const s=T(this._fieldsIndex,m.attributes,t);if(s){e.push(s);continue}}l.add(h(m,s,n,i,o)),e.push(x(y))}}_createObjectIdGenerator(e,t){const s=e.fieldsIndex.get(e.objectIdField);if(\"esriFieldTypeString\"===s.type)return()=>s.name+\"-\"+Date.now().toString(16);let i=Number.NEGATIVE_INFINITY;for(const r of t)r.objectId&&(i=Math.max(i,r.objectId));return i=Math.max(0,i)+1,()=>i++}_assignObjectId(e,t,s=!1){const i=this._queryEngine.objectIdField;e[i]=s&&i in t?t[i]:this._objectIdGenerator()}async _checkProjection(e){try{await m(o,e)}catch{throw new s(\"geojson-layer\",\"Projection not supported\")}}}export{D as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIi3C,IAAM,IAAE,EAAC,gBAAe,OAAG,cAAa,0CAAyC,wBAAuB,MAAG,iCAAgC,MAAG,gCAA+B,MAAG,2BAA0B,EAAC,0BAAyB,OAAG,oBAAmB,MAAG,8BAA6B,MAAG,mCAAkC,MAAG,2BAA0B,MAAG,kBAAiB,MAAG,8BAA6B,MAAG,qCAAoC,OAAG,sBAAqB,MAAG,iBAAgB,MAAG,oBAAmB,MAAG,6BAA4B,OAAG,uBAAsB,MAAG,4BAA2B,KAAE,EAAC;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,eAAa,MAAK,KAAK,oBAAkB,OAAM,MAAG;AAAC,YAAM,IAAE,MAAM,KAAK,OAAO,CAAC;AAAE,aAAO,KAAK,gBAAgB,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAJxlE;AAIylE,eAAK,iBAAL,mBAAmB,WAAU,KAAK,eAAa,KAAK,eAAa,KAAK,2BAAyB;AAAA,EAAI;AAAA,EAAC,MAAM,KAAK,GAAE,IAAE,CAAC,GAAE;AAAC,SAAK,eAAa,EAAC,KAAI,EAAE,KAAI,kBAAiB,EAAE,iBAAgB;AAAE,UAAMA,KAAE,CAAC;AAAE,UAAM,KAAK,iBAAiB,EAAE,gBAAgB;AAAE,QAAIC,KAAE;AAAK,MAAE,QAAMA,KAAE,MAAM,KAAK,OAAO,uBAAG,MAAM;AAAG,UAAM,IAAE,EAAEA,IAAE,EAAC,cAAa,EAAE,aAAY,CAAC,GAAEC,KAAE,EAAE,UAAQ,EAAE,UAAQ,CAAC,GAAE,IAAE,QAAM,EAAE,OAAK,EAAE,OAAK,EAAE,MAAK,IAAE,EAAE;AAAa,QAAI,IAAE,EAAE,iBAAe,EAAE,qBAAmB;AAAa,UAAM,IAAE,EAAE,oBAAkB;AAAE,QAAIC,KAAE,EAAE;AAAS,IAAAD,OAAI,EAAE,UAAQ,EAAE,cAAc,SAAO,KAAGF,GAAE,KAAK,EAAC,MAAK,qCAAoC,SAAQ,6EAA4E,SAAQ,EAAC,eAAc,EAAE,cAAa,EAAC,CAAC;AAAE,QAAI,IAAE,IAAIC,GAAEC,EAAC,EAAE,IAAI,CAAC;AAAE,SAAG,0BAAwB,EAAE,SAAO,EAAE,OAAK,qBAAoB,EAAE,WAAS,OAAG,EAAE,WAAS,OAAG,IAAE,EAAE,SAAO,IAAE,EAAC,OAAM,GAAE,MAAK,GAAE,MAAK,aAAW,EAAE,oBAAkB,wBAAsB,oBAAmB,UAAS,OAAG,UAAS,MAAE,GAAEA,GAAE,QAAQ,CAAC;AAAG,UAAME,KAAE,CAAC;AAAE,eAAUC,MAAKH,IAAE;AAAC,UAAG,QAAMG,GAAE,SAAOA,GAAE,OAAKA,GAAE,QAAO,QAAMA,GAAE,UAAQA,GAAE,QAAMA,GAAE,OAAM,CAACA,GAAE,KAAK,OAAM,IAAIC,GAAE,oCAAmC,yBAAwB,EAAC,OAAMD,GAAC,CAAC;AAAE,UAAG,CAAC,EAAE,WAAW,SAASA,GAAE,IAAI,EAAE,OAAM,IAAIC,GAAE,oCAAmC,2BAA2BD,GAAE,IAAI,KAAI,EAAC,OAAMA,GAAC,CAAC;AAAE,UAAGA,GAAE,SAAO,EAAE,MAAK;AAAC,cAAME,KAAE,EAAEF,EAAC;AAAE,mBAASE,OAAIH,GAAEC,GAAE,IAAI,IAAEE;AAAA,MAAE;AAAA,IAAC;AAAC,SAAK,eAAa,IAAIN,GAAEC,EAAC;AAAE,UAAMM,KAAE,KAAK,aAAa,eAAe,QAAQ,CAAC;AAAE,QAAGA,KAAE,MAAI,KAAK,aAAa,eAAe,OAAOA,IAAE,CAAC,GAAEL,IAAE;AAAC,UAAGA,GAAE,gBAAe;AAAC,cAAMI,KAAE,KAAK,aAAa,IAAIJ,GAAE,cAAc;AAAE,QAAAI,MAAGJ,GAAE,iBAAeI,GAAE,MAAKA,GAAE,OAAK,uBAAqBJ,GAAE,iBAAe;AAAA,MAAI;AAAC,UAAGA,GAAE,cAAa;AAAC,cAAMI,KAAE,KAAK,aAAa,IAAIJ,GAAE,YAAY;AAAE,QAAAI,MAAGJ,GAAE,eAAaI,GAAE,MAAKA,GAAE,OAAK,uBAAqBJ,GAAE,eAAa;AAAA,MAAI;AAAC,UAAGA,GAAE,cAAa;AAAC,cAAMI,KAAE,KAAK,aAAa,IAAIJ,GAAE,YAAY;AAAE,QAAAI,KAAEJ,GAAE,eAAaI,GAAE,QAAMJ,GAAE,eAAa,MAAKH,GAAE,KAAK,EAAC,MAAK,+CAA8C,SAAQ,2BAA0B,SAAQ,EAAC,UAASG,GAAC,EAAC,CAAC;AAAA,MAAE;AAAC,MAAAA,GAAE,kBAAgBA,GAAE,iBAAeH,GAAE,KAAK,EAAC,MAAK,kCAAiC,SAAQ,+CAA8C,SAAQ,EAAC,UAASG,GAAC,EAAC,CAAC,GAAEA,KAAE;AAAA,IAAK;AAAC,UAAMM,KAAE,IAAE,EAAE,CAAC,IAAE,QAAOC,KAAE,EAAC,UAASV,IAAE,eAAc,CAAC,GAAE,iBAAgB,EAAC,GAAG,GAAE,aAAYS,MAAG,QAAO,WAAU,EAAEL,EAAC,GAAE,QAAO,QAAO,cAAa,GAAE,eAAc,GAAE,QAAOF,IAAE,MAAK,CAAC,CAAC,GAAE,UAASC,GAAC,EAAC;AAAE,SAAK,eAAa,IAAI,GAAE,EAAC,QAAOD,IAAE,cAAa,GAAE,MAAK,OAAG,MAAK,GAAE,eAAc,GAAE,kBAAiB,GAAE,UAASC,IAAE,cAAa,IAAIQ,GAAE,EAAC,cAAa,GAAE,MAAK,OAAG,MAAK,EAAC,CAAC,GAAE,qBAAoB,KAAE,CAAC,GAAE,KAAK,2BAAyBX,GAAEI,IAAE,CAAC;AAAE,UAAMQ,KAAE,MAAM,KAAK,gBAAgBX,EAAC;AAAE,SAAK,qBAAmB,KAAK,yBAAyB,KAAK,cAAaW,EAAC;AAAE,UAAMC,KAAE,KAAK,mBAAmBD,IAAEF,GAAE,UAASA,GAAE,aAAa;AAAE,SAAK,aAAa,aAAa,QAAQG,EAAC;AAAE,UAAK,EAAC,YAAW,GAAE,YAAW,EAAC,IAAE,MAAM,KAAK,aAAa,uBAAuB;AAAE,QAAGH,GAAE,gBAAgB,SAAO,GAAE,GAAE;AAAC,YAAK,EAAC,OAAMH,IAAE,KAAIO,GAAC,IAAE;AAAE,MAAAJ,GAAE,gBAAgB,SAAS,aAAW,CAACH,IAAEO,EAAC;AAAA,IAAC;AAAC,WAAOJ;AAAA,EAAC;AAAA,EAAC,MAAM,WAAW,GAAE;AAAC,UAAK,EAAC,kBAAiB,GAAE,cAAaJ,GAAC,IAAE,KAAK;AAAa,WAAO,MAAM,QAAQ,IAAI,CAAC,EAAE,GAAEA,EAAC,GAAE,EAAE,EAAE,MAAK,CAAC,GAAE,EAAE,EAAE,SAAQ,CAAC,CAAC,CAAC,GAAE,MAAM,KAAK,sBAAsB,GAAE,KAAK,YAAY,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,cAAc,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE;AAAC,WAAO,MAAM,KAAK,sBAAsB,GAAE,KAAK,aAAa,aAAa,GAAE,EAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,kBAAkB,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE;AAAC,WAAO,MAAM,KAAK,sBAAsB,GAAE,KAAK,aAAa,qBAAqB,GAAE,EAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,eAAe,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE;AAAC,WAAO,MAAM,KAAK,sBAAsB,GAAE,KAAK,aAAa,mBAAmB,GAAE,EAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,YAAY,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE;AAAC,WAAO,MAAM,KAAK,sBAAsB,GAAE,KAAK,aAAa,sBAAsB,GAAE,EAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,cAAc,GAAE,IAAE,CAAC,GAAE;AAAC,WAAO,MAAM,KAAK,sBAAsB,GAAE,KAAK,aAAa,wBAAwB,GAAE,EAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQ,GAAE;AAJp0L;AAIq0L,SAAK,aAAa,mBAAiB,IAAE,UAAK,kBAAL,mBAAoB,SAAQ,KAAK,gBAAcI,GAAE,KAAK,iBAAiB,GAAE,KAAK,cAAc,QAAQ,KAAM,CAAAH,OAAG;AAAC,WAAK,aAAa,aAAa,MAAM,GAAE,KAAK,qBAAmB,KAAK,yBAAyB,KAAK,cAAaA,EAAC;AAAE,YAAM,IAAE,KAAK,mBAAmBA,EAAC;AAAE,WAAG,KAAK,aAAa,aAAa,QAAQ,CAAC;AAAA,IAAC,GAAI,CAAAA,OAAG;AAAC,WAAK,aAAa,aAAa,MAAM,GAAE,EAAEA,EAAC,KAAG,EAAE,UAAU,0BAA0B,EAAE,MAAM,IAAID,GAAE,yBAAwB,oCAAmC,EAAC,OAAMC,GAAC,CAAC,CAAC;AAAA,IAAC,CAAE,GAAE,MAAM,KAAK,sBAAsB;AAAE,UAAK,EAAC,YAAWN,IAAE,YAAWC,GAAC,IAAE,MAAM,KAAK,aAAa,uBAAuB;AAAE,WAAM,EAAC,QAAOD,IAAE,YAAWC,GAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgB,GAAE;AAAC,QAAG,QAAM,EAAE,QAAM,CAAC;AAAE,UAAK,EAAC,cAAa,GAAE,MAAKI,IAAE,eAAcN,GAAC,IAAE,KAAK,cAAa,IAAE,EAAE,GAAE,EAAC,cAAa,GAAE,MAAKM,IAAE,eAAcN,GAAC,CAAC;AAAE,QAAG,CAAC,EAAE,KAAK,aAAa,kBAAiB,CAAC,EAAE,YAAUE,MAAK,EAAE,GAAEA,GAAE,QAAQ,MAAIA,GAAE,WAAS,GAAE,EAAE,GAAEA,GAAE,UAAS,KAAK,aAAa,cAAa,KAAK,aAAa,MAAK,KAAE,GAAE,GAAE,KAAK,aAAa,gBAAgB,CAAC;AAAG,WAAO;AAAA,EAAC;AAAA,EAAC,MAAM,wBAAuB;AAAC,QAAG,KAAK,iBAAe,CAAC,KAAK,cAAc,UAAS;AAAC,UAAG;AAAC,cAAM,KAAK,cAAc;AAAA,MAAO,QAAM;AAAA,MAAC;AAAC,aAAO,KAAK,sBAAsB;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,OAAO,GAAE;AAAC,UAAK,EAAC,KAAII,IAAE,kBAAiBN,GAAC,IAAE,KAAK,cAAaC,MAAG,MAAM,EAAEK,IAAE,EAAC,cAAa,QAAO,OAAM,EAAC,GAAGN,GAAC,GAAE,QAAO,EAAC,CAAC,GAAG;AAAK,WAAO,MAAM,EAAEC,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,mBAAmB,GAAE,GAAEK,IAAE;AAAC,UAAK,EAAC,eAAcN,GAAC,IAAE,KAAK,cAAaC,KAAE,CAAC;AAAE,eAAU,KAAK,GAAE;AAAC,YAAMM,KAAE,KAAK,yBAAyB,GAAEL,KAAE,EAAE,KAAK,cAAaK,IAAE,EAAE,YAAW,MAAG,CAAC;AAAE,MAAAL,KAAEI,MAAA,gBAAAA,GAAG,KAAKJ,OAAI,KAAK,gBAAgBK,IAAE,EAAE,YAAW,IAAE,GAAE,EAAE,aAAWA,IAAE,EAAE,WAASA,GAAEP,EAAC,GAAEC,GAAE,KAAK,CAAC;AAAA,IAAE;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,MAAM,YAAY,GAAE;AAAC,UAAK,EAAC,MAAK,GAAE,SAAQK,IAAE,SAAQN,GAAC,IAAE,GAAEC,KAAE,EAAC,YAAW,CAAC,GAAE,eAAc,CAAC,GAAE,eAAc,CAAC,GAAE,eAAc,CAAC,EAAC;AAAE,QAAG,KAAG,EAAE,UAAQ,KAAK,eAAeA,IAAE,CAAC,GAAEK,MAAGA,GAAE,UAAQ,KAAK,kBAAkBL,IAAEK,EAAC,GAAEN,MAAGA,GAAE,QAAO;AAAC,iBAAUO,MAAKP,GAAE,CAAAC,GAAE,cAAc,KAAKO,GAAED,EAAC,CAAC;AAAE,WAAK,aAAa,aAAa,eAAeP,EAAC;AAAA,IAAC;AAAC,UAAK,EAAC,YAAW,GAAE,YAAWE,GAAC,IAAE,MAAM,KAAK,aAAa,uBAAuB;AAAE,WAAM,EAAC,QAAO,GAAE,YAAWA,IAAE,oBAAmBD,GAAC;AAAA,EAAC;AAAA,EAAC,eAAe,GAAE,GAAE;AAAC,UAAK,EAAC,YAAWK,GAAC,IAAE,GAAE,EAAC,cAAaN,IAAE,MAAK,GAAE,MAAKK,IAAE,eAAc,GAAE,kBAAiB,GAAE,cAAa,EAAC,IAAE,KAAK,cAAaF,KAAE,CAAC;AAAE,eAAU,KAAK,GAAE;AAAC,UAAG,EAAE,YAAUH,OAAIG,GAAE,EAAE,QAAQ,GAAE;AAAC,QAAAG,GAAE,KAAKJ,GAAE,0BAA0B,CAAC;AAAE;AAAA,MAAQ;AAAC,YAAMY,KAAE,KAAK,yBAAyB,GAAEC,KAAE,EAAE,KAAK,cAAaD,IAAE,EAAE,UAAU;AAAE,UAAGC,GAAE,CAAAT,GAAE,KAAKS,EAAC;AAAA,WAAM;AAAC,YAAG,KAAK,gBAAgBD,IAAE,EAAE,UAAU,GAAE,EAAE,aAAWA,IAAE,QAAM,EAAE,KAAI;AAAC,gBAAMA,KAAE,EAAE,WAAW,CAAC;AAAE,YAAE,cAAc,EAAE,GAAG,IAAEA;AAAA,QAAC;AAAC,YAAG,EAAE,EAAE,QAAQ,GAAE;AAAC,gBAAMP,KAAE,EAAE,SAAS,oBAAkB;AAAE,YAAE,WAAS,EAAEI,GAAE,EAAE,UAASJ,EAAC,GAAEA,IAAE,CAAC;AAAA,QAAC;AAAC,QAAAJ,GAAE,KAAK,CAAC,GAAEG,GAAE,KAAKE,GAAE,EAAE,WAAW,CAAC,CAAC,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,MAAE,QAAQ,GAAE,CAAC,GAAEL,IAAEH,IAAEK,IAAE,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkB,EAAC,eAAc,EAAC,GAAE,GAAE;AAAC,UAAK,EAAC,cAAaC,IAAE,MAAKN,IAAE,MAAK,GAAE,eAAcK,IAAE,kBAAiB,GAAE,cAAa,EAAC,IAAE,KAAK;AAAa,eAAU,KAAK,GAAE;AAAC,YAAK,EAAC,YAAWS,IAAE,UAAS,EAAC,IAAE,GAAE,IAAEA,MAAGA,GAAET,EAAC;AAAE,UAAG,QAAM,GAAE;AAAC,UAAE,KAAKH,GAAE,oBAAoBG,EAAC,UAAU,CAAC;AAAE;AAAA,MAAQ;AAAC,UAAG,CAAC,EAAE,IAAI,CAAC,GAAE;AAAC,UAAE,KAAKH,GAAE,0BAA0B,CAAC,UAAU,CAAC;AAAE;AAAA,MAAQ;AAAC,YAAME,KAAE,GAAE,EAAE,WAAW,CAAC,GAAEE,IAAE,GAAEN,EAAC;AAAE,UAAG,EAAE,CAAC,GAAE;AAAC,YAAGM,OAAIH,GAAE,CAAC,GAAE;AAAC,YAAE,KAAKD,GAAE,0BAA0B,CAAC;AAAE;AAAA,QAAQ;AAAC,cAAMY,KAAE,EAAE,oBAAkB;AAAE,QAAAV,GAAE,WAAS,EAAEO,GAAE,GAAEG,EAAC,GAAEA,IAAE,CAAC;AAAA,MAAC;AAAC,UAAGA,IAAE;AAAC,cAAMR,KAAE,EAAE,KAAK,cAAaF,GAAE,YAAWU,EAAC;AAAE,YAAGR,IAAE;AAAC,YAAE,KAAKA,EAAC;AAAE;AAAA,QAAQ;AAAA,MAAC;AAAC,QAAE,IAAI,GAAEF,IAAEE,IAAE,GAAEN,IAAEK,EAAC,CAAC,GAAE,EAAE,KAAKG,GAAE,CAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,yBAAyB,GAAE,GAAE;AAAC,UAAMF,KAAE,EAAE,YAAY,IAAI,EAAE,aAAa;AAAE,QAAG,0BAAwBA,GAAE,KAAK,QAAM,MAAIA,GAAE,OAAK,MAAI,KAAK,IAAI,EAAE,SAAS,EAAE;AAAE,QAAIN,KAAE,OAAO;AAAkB,eAAUC,MAAK,EAAE,CAAAA,GAAE,aAAWD,KAAE,KAAK,IAAIA,IAAEC,GAAE,QAAQ;AAAG,WAAOD,KAAE,KAAK,IAAI,GAAEA,EAAC,IAAE,GAAE,MAAIA;AAAA,EAAG;AAAA,EAAC,gBAAgB,GAAE,GAAEM,KAAE,OAAG;AAAC,UAAMN,KAAE,KAAK,aAAa;AAAc,MAAEA,EAAC,IAAEM,MAAGN,MAAK,IAAE,EAAEA,EAAC,IAAE,KAAK,mBAAmB;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiB,GAAE;AAAC,QAAG;AAAC,YAAM,EAAE,GAAE,CAAC;AAAA,IAAC,QAAM;AAAC,YAAM,IAAIM,GAAE,iBAAgB,0BAA0B;AAAA,IAAC;AAAA,EAAC;AAAC;", "names": ["i", "r", "a", "c", "m", "o", "s", "e", "f", "I", "j", "g", "w", "T", "t", "n"]}