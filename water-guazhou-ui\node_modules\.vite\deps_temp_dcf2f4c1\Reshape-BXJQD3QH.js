import {
  D,
  h as h4
} from "./chunk-LOVDGM2E.js";
import {
  n as n2,
  r as r3
} from "./chunk-BYONGQSG.js";
import {
  u as u2
} from "./chunk-QH7WY3TL.js";
import {
  h as h3,
  j as j3,
  l2 as l3,
  m as m2
} from "./chunk-OKYYUX33.js";
import "./chunk-DE5D2VUO.js";
import "./chunk-4W7HU754.js";
import "./chunk-7GPM2ZU5.js";
import "./chunk-FQMXSCOG.js";
import "./chunk-IKOX2HGY.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RZFVUJQM.js";
import "./chunk-EFMYS24F.js";
import "./chunk-MN2LGVDI.js";
import {
  u
} from "./chunk-KPVVIQ76.js";
import {
  h as h2
} from "./chunk-TBHJZ2TU.js";
import {
  V,
  e2 as e3
} from "./chunk-2R6RTTHN.js";
import {
  r as r2
} from "./chunk-ZOIBK6WV.js";
import "./chunk-FAMLZKHJ.js";
import "./chunk-6IU6DQRF.js";
import "./chunk-YELYN22P.js";
import "./chunk-XSQFM27N.js";
import "./chunk-QYOAH6AO.js";
import "./chunk-A7PY25IH.js";
import "./chunk-RURSJOSG.js";
import {
  e as e5
} from "./chunk-LOPMKCZI.js";
import "./chunk-PNRJ6Z2Z.js";
import "./chunk-PHEIXDVR.js";
import {
  p
} from "./chunk-WX7B7OKM.js";
import {
  e as e4
} from "./chunk-5S4W3ME5.js";
import "./chunk-CDZ24ELJ.js";
import "./chunk-OY3C7FMJ.js";
import "./chunk-5ZZCQR67.js";
import "./chunk-RVGLVPCD.js";
import "./chunk-4XLMJSSJ.js";
import "./chunk-IZLLLMFE.js";
import "./chunk-K4QGLA2K.js";
import "./chunk-5XZZKPPL.js";
import "./chunk-PCLDCFRI.js";
import {
  l as l2
} from "./chunk-JJ3NE6DY.js";
import "./chunk-HAEVWZ5B.js";
import {
  P as P2
} from "./chunk-HURTVQSL.js";
import "./chunk-TNGCGN7L.js";
import "./chunk-ONE6GLG5.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-P2G4OGHI.js";
import {
  h
} from "./chunk-X5KH47GX.js";
import "./chunk-73T3NEXA.js";
import "./chunk-6RN6WNY4.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import {
  j as j2
} from "./chunk-P37TUI4J.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-LAEW33J6.js";
import "./chunk-NGPCXWDX.js";
import "./chunk-IXMTA2RC.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-67MHB3E3.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-DKEAXJKM.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-EQYLMJEJ.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-3WEGNHPY.js";
import {
  g as g2
} from "./chunk-TD7BKFJX.js";
import "./chunk-LNGMNZ4I.js";
import "./chunk-24NZLSKM.js";
import "./chunk-BNFBWJYJ.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-23VNIYCK.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-OVIRLBJH.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import {
  e as e2,
  f,
  g
} from "./chunk-X7FOCGBC.js";
import {
  m
} from "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import {
  P,
  f as f2,
  l,
  w as w3
} from "./chunk-QUHG7NMD.js";
import "./chunk-4YKNTCHK.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import {
  y as y2
} from "./chunk-CGBA4LNQ.js";
import "./chunk-2RZLOKYO.js";
import "./chunk-VIXZ7ZAD.js";
import {
  w as w2
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-HHMRK4FX.js";
import "./chunk-QMG7GZIF.js";
import {
  c
} from "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e,
  t2
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  a,
  r,
  t,
  w,
  x
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/draw/support/Reshape.js
var B = class {
  constructor(e6, t3, i) {
    this.graphic = e6, this.mover = t3, this.selected = i, this.type = "reshape-start";
  }
};
var J = class {
  constructor(e6, t3, i) {
    this.graphic = e6, this.mover = t3, this.selected = i, this.type = "reshape";
  }
};
var Q = class {
  constructor(e6, t3, i) {
    this.graphic = e6, this.mover = t3, this.selected = i, this.type = "reshape-stop";
  }
};
var W = class {
  constructor(e6, t3, i) {
    this.mover = e6, this.dx = t3, this.dy = i, this.type = "move-start";
  }
};
var X = class {
  constructor(e6, t3, i) {
    this.mover = e6, this.dx = t3, this.dy = i, this.type = "move";
  }
};
var Y = class {
  constructor(e6, t3, i) {
    this.mover = e6, this.dx = t3, this.dy = i, this.type = "move-stop";
  }
};
var Z = class {
  constructor(e6) {
    this.added = e6, this.type = "vertex-select";
  }
};
var $ = class {
  constructor(e6) {
    this.removed = e6, this.type = "vertex-deselect";
  }
};
var ee = class {
  constructor(e6, t3, i, s3) {
    this.added = e6, this.graphic = t3, this.oldGraphic = i, this.vertices = s3, this.type = "vertex-add";
  }
};
var te = class {
  constructor(e6, t3, i, s3) {
    this.removed = e6, this.graphic = t3, this.oldGraphic = i, this.vertices = s3, this.type = "vertex-remove";
  }
};
var ie = h2.reshapeGraphics;
var se = { vertices: { default: new y2({ style: "circle", size: ie.vertex.size, color: ie.vertex.color, outline: { color: ie.vertex.outlineColor, width: 1 } }), hover: new y2({ style: "circle", size: ie.vertex.hoverSize, color: ie.vertex.hoverColor, outline: { color: ie.vertex.hoverOutlineColor, width: 1 } }), selected: new y2({ style: "circle", size: ie.selected.size, color: ie.selected.color, outline: { color: ie.selected.outlineColor, width: 1 } }) }, midpoints: { default: new y2({ style: "circle", size: ie.midpoint.size, color: ie.midpoint.color, outline: { color: ie.midpoint.outlineColor, width: 1 } }), hover: new y2({ style: "circle", size: ie.midpoint.size, color: ie.midpoint.color, outline: { color: ie.midpoint.outlineColor, width: 1 } }) } };
var oe = class extends n.EventedAccessor {
  constructor(e6) {
    super(e6), this._activeOperationInfo = null, this._editGeometryOperations = null, this._handles = new t2(), this._graphicAttributes = { esriSketchTool: "box" }, this._mover = null, this._snappingContext = null, this._snappingTask = null, this._stagedVertex = null, this._tooltip = null, this._viewHandles = new t2(), this.callbacks = { onReshapeStart() {
    }, onReshape() {
    }, onReshapeStop() {
    }, onMoveStart() {
    }, onMove() {
    }, onMoveStop() {
    }, onGraphicClick() {
    } }, this.enableMidpoints = true, this.enableMovement = true, this.enableVertices = true, this.graphic = null, this.layer = null, this.midpointGraphics = new j(), this.midpointSymbol = new y2({ style: "circle", size: 6, color: [200, 200, 200], outline: { color: [100, 100, 100], width: 1 } }), this.selectedVertices = [], this.snappingManager = null, this.tooltipOptions = new p(), this.type = "reshape", this.vertexGraphics = new j(), this.view = null;
  }
  initialize() {
    const e6 = this.view;
    this._highlightHelper = new h4({ view: e6 }), this._setup(), this._handles.add([f2(() => e6 == null ? void 0 : e6.ready, () => {
      const { layer: e7, view: t3 } = this;
      e5(t3, e7), this._viewHandles.add(t3.on("key-down", (e8) => this._keyDownHandler(e8), P2.TOOL));
    }, { once: true, initial: true }), P(() => this.graphic, () => this.refresh()), P(() => this.layer, (e7, t3) => {
      t3 && (this._clearSelection(), this._resetGraphics(t3)), this.refresh();
    }), P(() => this.enableMidpoints, () => this.refresh()), l(() => this.tooltipOptions.enabled, (e7) => {
      this._tooltip = e7 ? new m2({ view: this.view }) : a(this._tooltip);
    }, w3)]);
  }
  destroy() {
    var _a;
    this._reset(), (_a = this._mover) == null ? void 0 : _a.destroy(), this._mover = null, this._tooltip = a(this._tooltip), this._handles = a(this._handles), this._viewHandles = a(this._viewHandles);
  }
  set highlightsEnabled(e6) {
    var _a;
    (_a = this._highlightHelper) == null ? void 0 : _a.removeAll(), this._set("highlightsEnabled", e6), this._setUpHighlights();
  }
  get state() {
    const e6 = !!this.get("view.ready"), t3 = !(!this.get("graphic") || !this.get("layer"));
    return e6 && t3 ? "active" : e6 ? "ready" : "disabled";
  }
  set symbols(e6) {
    const { midpoints: t3 = se.midpoints, vertices: i = se.vertices } = e6 || {};
    this._set("symbols", { midpoints: t3, vertices: i });
  }
  isUIGraphic(e6) {
    const t3 = [];
    return this.graphic && t3.push(this.graphic), t3.concat(this.vertexGraphics.items, this.midpointGraphics.items), t3.length > 0 && t3.includes(e6);
  }
  refresh() {
    this._reset(), this._setup();
  }
  reset() {
    this.graphic = null;
  }
  clearSelection() {
    this._clearSelection();
  }
  removeSelectedVertices() {
    this.selectedVertices.length && this._removeVertices(this.selectedVertices);
  }
  _setup() {
    const { graphic: e6, layer: t3 } = this;
    if (!t3 || !e6 || t(e6.geometry)) return;
    const i = e6.geometry;
    "mesh" !== i.type && "extent" !== i.type ? ("polygon" === i.type && g(i), this._setUpHighlights(), this._setupGraphics(), this._setupMover()) : this._logGeometryTypeError();
  }
  _setUpHighlights() {
    this.highlightsEnabled && this.graphic && this._highlightHelper.add(this.graphic);
  }
  _setUpGeometryHelper() {
    const e6 = this.graphic.geometry;
    if (t(e6) || "mesh" === e6.type || "extent" === e6.type) return void this._logGeometryTypeError();
    const t3 = "multipoint" === e6.type ? new m({ paths: e6.points, spatialReference: e6.spatialReference }) : e6;
    this._editGeometryOperations = V.fromGeometry(t3, l2.Local);
  }
  _saveSnappingContextForHandle(e6, t3) {
    var _a;
    this._snappingGraphicsLayer = new h({ listMode: "hide", internal: true, title: "Reshape snapping layer" }), this.view.map.layers.add(this._snappingGraphicsLayer), this._snappingContext = new e3({ editGeometryOperations: this._editGeometryOperations, elevationInfo: { mode: "on-the-ground", offset: 0 }, pointer: ((_a = t3.viewEvent) == null ? void 0 : _a.pointerType) || "mouse", excludeFeature: this.graphic, visualizer: new u(this._snappingGraphicsLayer), vertexHandle: this._getVertexFromEditGeometry(e6) });
  }
  _reset() {
    this._clearSelection(), this._highlightHelper.removeAll(), this._updateTooltip(), this._resetGraphics(), this._resetSnappingStateVars(), this._activeOperationInfo = null, this._mover && this._mover.destroy(), this._mover = null, this.view.cursor = "default";
  }
  _resetSnappingStateVars() {
    var _a;
    r(this.snappingManager) && this.snappingManager.doneSnapping(), r(this._snappingGraphicsLayer) && (((_a = this.view) == null ? void 0 : _a.map) && this.view.map.layers.remove(this._snappingGraphicsLayer), this._snappingGraphicsLayer.destroy()), this._editGeometryOperations = a(this._editGeometryOperations), this._snappingTask = w(this._snappingTask), this._snappingTask = null, this._snappingContext = null, this._stagedVertex = null;
  }
  _resetGraphics(e6) {
    this._removeMidpointGraphics(e6), this._removeVertexGraphics(e6), this._set("selectedVertices", []);
  }
  _removeMidpointGraphics(e6) {
    const t3 = e6 || this.layer;
    t3 && t3.removeMany(this.midpointGraphics.items), this.midpointGraphics.items.forEach((e7) => e7.destroy()), this.midpointGraphics.removeAll();
  }
  _removeVertexGraphics(e6) {
    const t3 = e6 || this.layer;
    t3 && t3.removeMany(this.vertexGraphics.items), this.vertexGraphics.items.forEach((e7) => e7.destroy()), this.vertexGraphics.removeAll();
  }
  _getCoordinatesForUI(e6) {
    const t3 = e2(e6.clone());
    if ("polygon" === e6.type) for (const i of t3) {
      const e7 = i[i.length - 1];
      i[0][0] === e7[0] && i[0][1] === e7[1] && i.length > 2 && i.pop();
    }
    return t3;
  }
  _setupGraphics() {
    const e6 = this.graphic.geometry;
    if (r(e6) && ("polyline" === e6.type || "polygon" === e6.type)) {
      const t3 = this._getCoordinatesForUI(e6);
      this.enableMidpoints && this._setUpMidpointGraphics(t3), this.enableVertices && this._setUpVertexGraphics(t3);
    }
  }
  _setUpMidpointGraphics(e6) {
    this._removeMidpointGraphics();
    const t3 = this._createMidpointGraphics(e6);
    this.midpointGraphics.addMany(t3), this.layer.addMany(t3);
  }
  _setUpVertexGraphics(e6) {
    this._removeVertexGraphics();
    const t3 = this._createVertexGraphics(e6);
    this.vertexGraphics.addMany(t3), this._storeRelatedVertexIndices(), this.layer.addMany(t3);
  }
  _createVertexGraphics(e6) {
    const { _graphicAttributes: i, symbols: s3, view: { spatialReference: o } } = this, r4 = [];
    return e6 == null ? void 0 : e6.forEach((e7, n3) => {
      e7.forEach((e8, a3) => {
        var _a;
        const [h5, p2] = e8;
        r4.push(new g2({ geometry: new w2({ x: h5, y: p2, spatialReference: o }), symbol: (_a = s3 == null ? void 0 : s3.vertices) == null ? void 0 : _a.default, attributes: { ...i, pathIndex: n3, pointIndex: a3 } }));
      });
    }), r4;
  }
  _createMidpointGraphics(e6) {
    const { _graphicAttributes: i, symbols: s3, view: { spatialReference: o } } = this, r4 = [];
    return e6 == null ? void 0 : e6.forEach((e7, n3) => {
      e7.forEach((a3, h5) => {
        const [p2, c2] = a3, l4 = e7[h5 + 1] ? h5 + 1 : 0;
        if ("polygon" === x(this.graphic.geometry, "type") || 0 !== l4) {
          const [a4, d] = e7[l4], [m3, v] = f([p2, c2], [a4, d]);
          r4.push(new g2({ geometry: new w2({ x: m3, y: v, spatialReference: o }), symbol: s3.midpoints.default, attributes: { ...i, pathIndex: n3, pointIndexStart: h5, pointIndexEnd: l4 } }));
        }
      });
    }), r4;
  }
  _storeRelatedVertexIndices() {
    const e6 = this.vertexGraphics.items;
    if (!e6) return;
    const t3 = e6.map(({ geometry: e7 }) => ({ x: e7.x, y: e7.y }));
    for (let i = 0; i < t3.length; i++) {
      const s3 = [];
      for (let e7 = 0; e7 < t3.length; e7++) {
        if (i === e7) continue;
        const o = t3[i], r4 = t3[e7];
        o.x === r4.x && o.y === r4.y && s3.push(e7);
      }
      e6[i].attributes.relatedGraphicIndices = s3;
    }
  }
  _setupMover() {
    const { enableMovement: e6, graphic: t3, midpointGraphics: i, vertexGraphics: s3, view: o } = this, r4 = s3.concat(i).items;
    e6 && r4.push(t3), this._mover = new D({ enableMoveAllGraphics: false, highlightsEnabled: false, indicatorsEnabled: false, graphics: r4, view: o, callbacks: { onGraphicClick: (e7) => this._onGraphicClickCallback(e7), onGraphicMoveStart: (e7) => this._onGraphicMoveStartCallback(e7), onGraphicMove: (e7) => this._onGraphicMoveCallback(e7), onGraphicMoveStop: (e7) => this._onGraphicMoveStopCallback(e7), onGraphicPointerOver: (e7) => this._onGraphicPointerOverCallback(e7), onGraphicPointerOut: (e7) => this._onGraphicPointerOutCallback(e7) } });
  }
  _onGraphicClickCallback(e6) {
    e6.viewEvent.stopPropagation();
    const t3 = e6.graphic;
    if (t3 === this.graphic) this.clearSelection(), this.emit("graphic-click", e6), this.callbacks.onGraphicClick && this.callbacks.onGraphicClick(e6);
    else if (this._isMidpoint(t3)) {
      if (2 === e6.viewEvent.button) return;
      const i = this.graphic.clone(), s3 = this._createVertexFromMidpoint(t3);
      this.refresh(), this._emitVertexAddEvent([t3], i, s3);
    } else if (this._isVertex(t3)) if (e6.viewEvent.stopPropagation(), 2 === e6.viewEvent.button) this._removeVertices(t3);
    else {
      e6.viewEvent.native.shiftKey || this._clearSelection(), this.selectedVertices.includes(t3) ? this._removeFromSelection(t3, true) : this._addToSelection(t3);
    }
  }
  _setUpOperation(e6) {
    const { graphic: t3, dx: i, dy: s3 } = e6, o = t3 === this.graphic;
    this._resetSnappingStateVars(), this._setUpGeometryHelper(), this._saveSnappingContextForHandle(t3, e6), this._activeOperationInfo = { target: this.graphic, mover: t3, operationType: o ? "move" : "reshape", totalDx: i, totalDy: s3 };
  }
  _onGraphicMoveStartCallback(e6) {
    const { dx: t3, dy: i, graphic: s3 } = e6;
    if (s3 === this.graphic) {
      const { geometry: o } = s3;
      return this._setUpOperation(e6), this._emitMoveStartEvent(t3, i), void (r(o) && "point" === o.type && this._onHandleMove(s3, t3, i, e6, () => {
        this._updateTooltip(this.graphic, e6.viewEvent), this._emitMoveEvent(t3, i);
      }));
    }
    if (!this.selectedVertices.includes(s3)) {
      if (this._clearSelection(), this._isMidpoint(s3)) {
        const e7 = this.graphic.clone(), t4 = this._createVertexFromMidpoint(s3);
        this._emitVertexAddEvent([s3], e7, t4);
      }
      this._addToSelection(s3);
    }
    this._setUpOperation(e6), this._emitReshapeStartEvent(s3), this._onHandleMove(s3, t3, i, e6, () => {
      this._updateTooltip(s3, e6.viewEvent), this._emitReshapeEvent(s3);
    });
  }
  _onGraphicMoveCallback(e6) {
    const t3 = this._activeOperationInfo;
    if (!t3) return;
    const { dx: i, dy: s3, graphic: o } = e6;
    t3.totalDx += i, t3.totalDy += s3;
    const { operationType: r4 } = t3, { geometry: n3 } = o;
    if (!t(n3)) {
      if ("move" !== r4) this._onHandleMove(o, i, s3, e6, () => {
        this._updateTooltip(o, e6.viewEvent), this._emitReshapeEvent(o);
      });
      else if ("point" === n3.type) this._onHandleMove(o, i, s3, e6, () => {
        this._updateTooltip(this.graphic, e6.viewEvent), this._emitMoveEvent(i, s3);
      });
      else if ("polyline" === n3.type || "polygon" === n3.type) {
        const t4 = this._getCoordinatesForUI(n3);
        this._updateVertexGraphicLocations(t4), this._updateTooltip(this.graphic, e6.viewEvent), this._emitMoveEvent(i, s3);
      }
    }
  }
  _onGraphicMoveStopCallback(e6) {
    const t3 = this._activeOperationInfo;
    if (!t3) return;
    const { dx: i, dy: s3, graphic: o } = e6, { operationType: r4 } = t3;
    t3.totalDx += i, t3.totalDy += s3, this._onHandleMove(o, i, s3, e6, () => "move" === r4 ? this._emitMoveStopEvent() : this._emitReshapeStopEvent(o)), this._isMidpoint(o) ? this.refresh() : (this._updateTooltip(this._isVertex(o) ? o : null), this._resetSnappingStateVars(), this._activeOperationInfo = null);
  }
  _updateVertexGraphicLocations(e6) {
    const t3 = this.view.spatialReference;
    for (const i of this.vertexGraphics) {
      const { pathIndex: s3, pointIndex: o } = i.attributes, [r4, n3] = e6[s3][o];
      i.geometry = new w2({ x: r4, y: n3, spatialReference: t3 });
    }
    this._updateMidpointGraphicLocations(e6);
  }
  _updateMidpointGraphicLocations(e6) {
    for (const t3 of this.midpointGraphics) {
      const { pathIndex: i, pointIndexStart: s3, pointIndexEnd: o } = t3.attributes, [r4, n3] = e6[i][s3], [a3, h5] = e6[i][o], [p2, c2] = f([r4, n3], [a3, h5]);
      t3.geometry = new w2({ x: p2, y: c2, spatialReference: this.view.spatialReference });
    }
  }
  _getIndicesForVertexGraphic({ attributes: e6 }) {
    return [(e6 == null ? void 0 : e6.pathIndex) || 0, (e6 == null ? void 0 : e6.pointIndex) || 0];
  }
  _getVertexFromEditGeometry(e6) {
    const [t3, i] = this._getIndicesForVertexGraphic(e6);
    return this._editGeometryOperations.data.components[t3].vertices[i];
  }
  _onHandleMove(e6, t3, s3, o, r4) {
    w(this._snappingTask);
    const n3 = this._snappingContext;
    if (!n3) return;
    const a3 = e6.geometry, h5 = "graphic-move-stop" === o.type;
    if (r(this.snappingManager) && this.selectedVertices.length < 2 && !h5) {
      const o2 = this.snappingManager;
      this._stagedVertex = o2.update({ point: a3, context: n3 }), this._syncGeometryAfterVertexMove(e6, new w2(this._stagedVertex), t3, s3, h5), r4(), this._snappingTask = j2(async (i) => {
        const p2 = await o2.snap({ point: a3, context: n3, signal: i });
        p2.valid && (this._stagedVertex = p2.apply(), this._syncGeometryAfterVertexMove(e6, new w2(this._stagedVertex), t3, s3, h5), r4());
      });
    } else {
      const i = r(this._stagedVertex) ? new w2(this._stagedVertex) : a3;
      this._syncGeometryAfterVertexMove(e6, i, t3, s3, h5), r4();
    }
  }
  async _syncGeometryAfterVertexMove(e6, t3, i, s3, o = false) {
    const r4 = this._editGeometryOperations.data.geometry;
    if ("point" === r4.type) e6.geometry = t3;
    else {
      const { x: n3, y: a3 } = t3, [h5, p2] = this._getIndicesForVertexGraphic(e6);
      let c2 = e2(r4);
      const l4 = c2[h5].length - 1;
      c2[h5][p2] = [n3, a3], "polygon" === r4.type && (0 === p2 ? c2[h5][l4] = [n3, a3] : p2 === l4 && (c2[h5][0] = [n3, a3])), this._isVertex(e6) && (c2 = this._moveRelatedCoordinates(c2, e6, n3, a3), c2 = this._moveSelectedHandleCoordinates(c2, e6, i, s3, "polygon" === r4.type), this._updateMidpointGraphicLocations(c2)), this.graphic.geometry = r4.clone();
      const d = this._getVertexFromEditGeometry(e6), m3 = n3 - d.pos[0], v = a3 - d.pos[1];
      this._editGeometryOperations.moveVertices([d], m3, v, 0), o && (this._mover ? this._mover.updateGeometry(this._mover.graphics.indexOf(e6), t3) : e6.geometry = t3);
    }
  }
  _moveRelatedCoordinates(e6, t3, i, s3) {
    const { relatedGraphicIndices: o } = t3.attributes;
    for (const r4 of o) {
      const o2 = this.vertexGraphics.getItemAt(r4), { pathIndex: n3, pointIndex: a3 } = o2.attributes;
      e6[n3][a3] = [i, s3], o2.geometry = t3.geometry;
    }
    return e6;
  }
  _moveSelectedHandleCoordinates(e6, t3, i, s3, o) {
    for (const r4 of this.selectedVertices) if (r4 !== t3) {
      const { pathIndex: t4, pointIndex: n3, relatedGraphicIndices: a3 } = r4.attributes, h5 = r2(r4.geometry, i, s3, this.view), p2 = e6[t4].length - 1;
      e6[t4][n3] = [h5.x, h5.y], r4.geometry = h5, o && (0 === n3 ? e6[t4][p2] = [h5.x, h5.y] : n3 === p2 && (e6[t4][0] = [h5.x, h5.y]));
      for (const i2 of a3) {
        const t5 = this.vertexGraphics.getItemAt(i2), { pathIndex: s4, pointIndex: o2 } = t5.attributes;
        e6[s4][o2] = [h5.x, h5.y], t5.geometry = h5;
      }
    }
    return e6;
  }
  _onGraphicPointerOverCallback(e6) {
    const t3 = e6.graphic, i = this._isVertex(t3);
    i && !this._isSelected(t3) && (t3.symbol = this.symbols.vertices.hover), this._updateTooltip(i ? t3 : null), this._updateHoverCursor(t3);
  }
  _onGraphicPointerOutCallback(e6) {
    const t3 = e6.graphic;
    this._isVertex(t3) && !this._isSelected(t3) && (t3.symbol = this.symbols.vertices.default), this.view.cursor = "default", this._updateTooltip();
  }
  _createVertexFromMidpoint(e6) {
    const { _graphicAttributes: t3, graphic: i } = this, s3 = i.geometry;
    if (t(s3) || "polygon" !== s3.type && "polyline" !== s3.type) return [];
    const o = s3.clone(), r4 = [], { pathIndex: n3, pointIndexStart: a3, pointIndexEnd: h5 } = e6.attributes, { x: c2, y: l4 } = e6.geometry, d = 0 === h5 ? a3 + 1 : h5, m3 = e2(o);
    return m3[n3].splice(d, 0, [c2, l4]), e6.attributes = { ...t3, pathIndex: n3, pointIndex: d, relatedGraphicIndices: [] }, r4.push({ coordinates: m3[n3][d], componentIndex: n3, vertexIndex: d }), this.graphic.geometry = o, r4;
  }
  _addToSelection(e6) {
    e6 instanceof g2 && (e6 = [e6]);
    for (const t3 of e6) t3.symbol = this.symbols.vertices.selected;
    this._set("selectedVertices", this.selectedVertices.concat(e6)), this._emitSelectEvent(e6);
  }
  _removeFromSelection(e6, i) {
    const { vertices: s3 } = this.symbols, o = i ? s3.hover : s3.default;
    e6 instanceof g2 && (e6 = [e6]);
    for (const t3 of e6) this.selectedVertices.splice(this.selectedVertices.indexOf(t3), 1), this._set("selectedVertices", this.selectedVertices), t3.set("symbol", o);
    this._emitDeselectEvent(e6);
  }
  _clearSelection() {
    if (this.selectedVertices.length) {
      const e6 = this.selectedVertices;
      for (const t3 of this.selectedVertices) t3.set("symbol", this.symbols.vertices.default);
      this._set("selectedVertices", []), this._emitDeselectEvent(e6);
    }
  }
  _keyDownHandler(e6) {
    e4.delete.includes(e6.key) && !e6.repeat && this.selectedVertices.length && this._removeVertices(this.selectedVertices);
  }
  _removeVertices(e6) {
    const i = this.graphic.geometry;
    if (t(i) || "polygon" !== i.type && "polyline" !== i.type) return;
    if ("polygon" === i.type && this.vertexGraphics.length < 4 || this.vertexGraphics.length < 3) return;
    e6 instanceof g2 && (e6 = [e6]);
    const s3 = this.graphic.clone(), o = i.clone();
    let r4 = e2(o);
    const n3 = [];
    e6 instanceof g2 && (e6 = [e6]);
    for (const t3 of e6) {
      const { x: e7, y: i2 } = t3.geometry;
      for (let t4 = 0; t4 < r4.length; t4++) {
        const s4 = r4[t4];
        for (let o2 = 0; o2 < s4.length; o2++) {
          const [a3, h5] = s4[o2];
          e7 === a3 && i2 === h5 && (n3.push({ coordinates: r4[t4][o2], componentIndex: t4, vertexIndex: o2 }), r4[t4].splice(Number(o2), 1));
        }
      }
    }
    if ("polygon" === o.type) r4 = r4.filter((e7) => {
      if (e7.length < 2) return false;
      const [t3, i2] = e7[0], [s4, o2] = e7[e7.length - 1];
      return (2 !== e7.length || t3 !== s4 || i2 !== o2) && (t3 === s4 && i2 === o2 || e7.push(e7[0]), true);
    }), o.rings = r4;
    else {
      for (const e7 of r4) 1 === e7.length && r4.splice(r4.indexOf(e7), 1);
      o.paths = r4;
    }
    this.graphic.geometry = o, this.refresh(), this._emitVertexRemoveEvent(e6, s3, n3);
  }
  _isVertex(e6) {
    return this.vertexGraphics.includes(e6);
  }
  _isSelected(e6) {
    return this._isVertex(e6) && this.selectedVertices.includes(e6);
  }
  _isMidpoint(e6) {
    return this.midpointGraphics.includes(e6);
  }
  _updateHoverCursor(e6) {
    this.view.cursor = this._isMidpoint(e6) ? "copy" : "move";
  }
  _updateTooltip(e6, t3) {
    t(this._tooltip) || (e6 ? e6 === this.graphic ? this._updateMoveGraphicTooltip(t3) : this._updateMoveVertexTooltip(t3) : this._tooltip.clear());
  }
  _updateMoveGraphicTooltip(e6) {
    const { _tooltip: t3, tooltipOptions: i, view: s3 } = this;
    if (t(t3)) return;
    const o = new r3({ tooltipOptions: i });
    if (e6) {
      const { x: t4, y: i2 } = e6.origin, r4 = s3.toMap(e6), n3 = s3.toMap(c(t4, i2)), a3 = h3(n3, r4);
      o.distance = r(a3) ? a3 : j3;
    }
    t3.info = o;
  }
  _updateMoveVertexTooltip(e6) {
    const { _tooltip: t3, graphic: { geometry: i }, tooltipOptions: s3, view: o } = this;
    if (t(t3)) return;
    const r4 = new n2({ tooltipOptions: s3 });
    if (r(i) && ("polygon" === i.type ? r4.area = u2(i) : "polyline" === i.type && (r4.totalLength = l3(i))), e6) {
      const { x: t4, y: i2 } = e6.origin, s4 = o.toMap(e6), n3 = o.toMap(c(t4, i2)), a3 = h3(n3, s4);
      r4.distance = r(a3) ? a3 : j3;
    }
    t3.info = r4;
  }
  _emitMoveStartEvent(e6, t3) {
    const i = new W(this.graphic, e6, t3);
    this.emit("move-start", i), this.callbacks.onMoveStart && this.callbacks.onMoveStart(i);
  }
  _emitMoveEvent(e6, t3) {
    const i = new X(this.graphic, e6, t3);
    this.emit("move", i), this.callbacks.onMove && this.callbacks.onMove(i);
  }
  _emitMoveStopEvent() {
    const e6 = this._activeOperationInfo;
    if (!e6) return;
    const { totalDx: t3, totalDy: i } = e6, s3 = new Y(this.graphic, t3, i);
    this.emit("move-stop", s3), this.callbacks.onMoveStop && this.callbacks.onMoveStop(s3);
  }
  _emitReshapeStartEvent(e6) {
    const t3 = new B(this.graphic, e6, this.selectedVertices);
    this.emit("reshape-start", t3), this.callbacks.onReshapeStart && this.callbacks.onReshapeStart(t3);
  }
  _emitReshapeEvent(e6) {
    const t3 = new J(this.graphic, e6, this.selectedVertices);
    this.emit("reshape", t3), this.callbacks.onReshape && this.callbacks.onReshape(t3);
  }
  _emitReshapeStopEvent(e6) {
    const t3 = new Q(this.graphic, e6, this.selectedVertices);
    this.emit("reshape-stop", t3), this.callbacks.onReshapeStop && this.callbacks.onReshapeStop(t3);
  }
  _emitSelectEvent(e6) {
    const t3 = new Z(e6);
    this.emit("select", t3), this.callbacks.onVertexSelect && this.callbacks.onVertexSelect(t3);
  }
  _emitDeselectEvent(e6) {
    const t3 = new $(e6);
    this.emit("deselect", t3), this.callbacks.onVertexDeselect && this.callbacks.onVertexDeselect(t3);
  }
  _emitVertexAddEvent(e6, t3, i) {
    const s3 = new ee(e6, this.graphic, t3, i);
    this.emit("vertex-add", s3), this.callbacks.onVertexAdd && this.callbacks.onVertexAdd(s3);
  }
  _emitVertexRemoveEvent(e6, t3, i) {
    const s3 = new te(e6, this.graphic, t3, i);
    this.emit("vertex-remove", s3), this.callbacks.onVertexRemove && this.callbacks.onVertexRemove(s3);
  }
  _logGeometryTypeError() {
    s.getLogger(this.declaredClass).error(new s2("reshape:invalid-geometry", "Reshape operation not supported for the provided graphic. The geometry type is not supported."));
  }
};
e([y()], oe.prototype, "_tooltip", void 0), e([y()], oe.prototype, "callbacks", void 0), e([y()], oe.prototype, "enableMidpoints", void 0), e([y()], oe.prototype, "enableMovement", void 0), e([y()], oe.prototype, "enableVertices", void 0), e([y()], oe.prototype, "graphic", void 0), e([y({ value: true })], oe.prototype, "highlightsEnabled", null), e([y()], oe.prototype, "layer", void 0), e([y({ readOnly: true })], oe.prototype, "midpointGraphics", void 0), e([y()], oe.prototype, "midpointSymbol", void 0), e([y({ readOnly: true })], oe.prototype, "selectedVertices", void 0), e([y()], oe.prototype, "snappingManager", void 0), e([y({ readOnly: true })], oe.prototype, "state", null), e([y({ value: se })], oe.prototype, "symbols", null), e([y({ type: p })], oe.prototype, "tooltipOptions", void 0), e([y({ readOnly: true })], oe.prototype, "type", void 0), e([y({ readOnly: true })], oe.prototype, "vertexGraphics", void 0), e([y()], oe.prototype, "view", void 0), oe = e([a2("esri.views.draw.support.Reshape")], oe);
var re = oe;
export {
  re as default
};
//# sourceMappingURL=Reshape-BXJQD3QH.js.map
