import {
  D,
  M,
  S,
  T,
  c,
  d,
  f as f2,
  m as m2,
  x,
  y as y2,
  z
} from "./chunk-M4ZUXRA3.js";
import "./chunk-WJKHSSMC.js";
import {
  O
} from "./chunk-ZQY4DQCR.js";
import {
  m,
  y
} from "./chunk-2ILOD42U.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-2KFL4KXW.js";
import {
  i
} from "./chunk-CHUBHVZP.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import {
  w
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  R,
  f2 as f,
  p
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import {
  u
} from "./chunk-G5KX4JSG.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  e
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/smartMapping/statistics/support/utils.js
var y3 = null;
function $(e2, t, n) {
  return e2.x < 0 ? e2.x += t : e2.x > n && (e2.x -= t), e2;
}
function I(e2, n, r2, i2) {
  const a = p(r2) ? R(r2) : null, u2 = a ? Math.round((a.valid[1] - a.valid[0]) / n.scale[0]) : null;
  return e2.map((e3) => {
    const r3 = new w(e(e3.geometry));
    return O(n, r3, r3, r3.hasZ, r3.hasM), e3.geometry = a ? $(r3, u2, i2[0]) : r3, e3;
  });
}
function g(e2, t = 18, n, i2, o, s2) {
  const l = new Float64Array(o * s2);
  t = Math.round(u(t));
  let a = Number.POSITIVE_INFINITY, u2 = Number.NEGATIVE_INFINITY, m3 = 0, d3 = 0, p3 = 0, h = 0;
  const y4 = y(i2, n);
  for (const { geometry: r2, attributes: c3 } of e2) {
    const { x: e3, y: n2 } = r2, i3 = Math.max(0, e3 - t), $3 = Math.max(0, n2 - t), I2 = Math.min(s2, n2 + t), g2 = Math.min(o, e3 + t), j2 = +y4(c3);
    for (let r3 = $3; r3 < I2; r3++) for (let s3 = i3; s3 < g2; s3++) {
      const i4 = r3 * o + s3, c4 = m(s3 - e3, r3 - n2, t), y5 = l[i4];
      m3 = l[i4] += c4 * j2;
      const $4 = m3 - y5;
      d3 += $4, p3 += $4 * $4, m3 < a && (a = m3), m3 > u2 && (u2 = m3), h++;
    }
  }
  if (!h) return { mean: 0, stddev: 0, min: 0, max: 0, mid: 0, count: 0 };
  const $2 = (u2 - a) / 2;
  return { mean: d3 / h, stdDev: Math.sqrt((p3 - d3 * d3 / h) / h), min: a, max: u2, mid: $2, count: h };
}
async function j(e2, t) {
  if (!t) return [];
  const { field: n, field2: r2, field3: o, fieldDelimiter: s2 } = e2, l = e2.valueExpression, a = e2.normalizationType, u2 = e2.normalizationField, f3 = e2.normalizationTotal, c3 = [], h = e2.viewInfoParams;
  let $2 = null, I2 = null;
  if (l) {
    if (!y3) {
      const { arcadeUtils: e3 } = await i();
      y3 = e3;
    }
    $2 = y3.createFunction(l), I2 = h && y3.getViewInfo({ viewingMode: h.viewingMode, scale: h.scale, spatialReference: new f(h.spatialReference) });
  }
  const g2 = e2.fieldInfos, j2 = !(t[0] && "declaredClass" in t[0] && "esri.Graphic" === t[0].declaredClass) && g2 ? { fields: g2 } : null;
  return t.forEach((e3) => {
    const t2 = e3.attributes;
    let i2;
    if (l) {
      const t3 = j2 ? { ...e3, layer: j2 } : e3, n2 = y3.createExecContext(t3, I2);
      i2 = y3.executeFunction($2, n2);
    } else t2 && (i2 = t2[n], r2 && (i2 = `${c(i2)}${s2}${c(t2[r2])}`, o && (i2 = `${i2}${s2}${c(t2[o])}`)));
    if (a && "number" == typeof i2 && isFinite(i2)) {
      const e4 = t2 && parseFloat(t2[u2]);
      i2 = D(i2, a, e4, f3);
    }
    c3.push(i2);
  }), c3;
}

// node_modules/@arcgis/core/smartMapping/statistics/support/statsWorker.js
async function d2(i2) {
  const { attribute: e2, features: s2 } = i2, { normalizationType: r2, normalizationField: m3, minValue: f3, maxValue: u2, fieldType: d3 } = e2, p3 = await j({ field: e2.field, valueExpression: e2.valueExpression, normalizationType: r2, normalizationField: m3, normalizationTotal: e2.normalizationTotal, viewInfoParams: e2.viewInfoParams, fieldInfos: e2.fieldInfos }, s2), v2 = m2({ normalizationType: r2, normalizationField: m3, minValue: f3, maxValue: u2 }), c3 = { value: 0.5, fieldType: d3 }, z3 = "esriFieldTypeString" === d3 ? f2({ values: p3, supportsNullCount: v2, percentileParams: c3 }) : d({ values: p3, minValue: f3, maxValue: u2, useSampleStdDev: !r2, supportsNullCount: v2, percentileParams: c3 });
  return T(z3, "esriFieldTypeDate" === d3);
}
async function p2(i2) {
  const { attribute: e2, features: n } = i2, l = await j({ field: e2.field, field2: e2.field2, field3: e2.field3, fieldDelimiter: e2.fieldDelimiter, valueExpression: e2.valueExpression, viewInfoParams: e2.viewInfoParams, fieldInfos: e2.fieldInfos }, n), t = y2(l);
  return x(t, e2.domains, e2.returnAllCodedValues, e2.fieldDelimiter);
}
async function v(i2) {
  const { attribute: e2, features: n } = i2, { field: l, normalizationType: t, normalizationField: o, normalizationTotal: s2, classificationMethod: r2 } = e2, u2 = await j({ field: l, valueExpression: e2.valueExpression, normalizationType: t, normalizationField: o, normalizationTotal: s2, viewInfoParams: e2.viewInfoParams, fieldInfos: e2.fieldInfos }, n), d3 = z(u2, { field: l, normalizationType: t, normalizationField: o, normalizationTotal: s2, classificationMethod: r2, standardDeviationInterval: e2.standardDeviationInterval, numClasses: e2.numClasses, minValue: e2.minValue, maxValue: e2.maxValue });
  return S(d3, r2);
}
async function c2(i2) {
  const { attribute: e2, features: n } = i2, { field: l, normalizationType: t, normalizationField: o, normalizationTotal: s2, classificationMethod: r2 } = e2, m3 = await j({ field: l, valueExpression: e2.valueExpression, normalizationType: t, normalizationField: o, normalizationTotal: s2, viewInfoParams: e2.viewInfoParams, fieldInfos: e2.fieldInfos }, n);
  return M(m3, { field: l, normalizationType: t, normalizationField: o, normalizationTotal: s2, classificationMethod: r2, standardDeviationInterval: e2.standardDeviationInterval, numBins: e2.numBins, minValue: e2.minValue, maxValue: e2.maxValue });
}
async function z2(a) {
  const { attribute: n, features: l } = a, { field: t, radius: o, fieldOffset: s2, transform: r2, spatialReference: m3, size: f3 } = n, u2 = I(l, r2, m3, f3), { count: d3, min: p3, max: v2, mean: c3, stdDev: z3 } = g(u2, o, s2, t, f3[0], f3[1]);
  return { count: d3, min: p3, max: v2, avg: c3, stddev: z3 };
}
export {
  v as classBreaks,
  z2 as heatmapStatistics,
  c2 as histogram,
  d2 as summaryStatistics,
  p2 as uniqueValues
};
//# sourceMappingURL=statsWorker-WRGPA47X.js.map
