{"version": 3, "sources": ["../../@arcgis/core/views/2d/engine/imagery/RasterBitmap.js", "../../@arcgis/core/views/2d/engine/imagery/colorizer/utils.js", "../../@arcgis/core/views/2d/engine/imagery/colorizer/lut.js", "../../@arcgis/core/views/2d/engine/imagery/colorizer/shadedrelief.js", "../../@arcgis/core/views/2d/engine/imagery/colorizer/stretch.js", "../../@arcgis/core/views/2d/engine/imagery/colorizer/rasterColorizer.js", "../../@arcgis/core/views/2d/engine/imagery/processor/utils.js", "../../@arcgis/core/views/2d/engine/imagery/processor/aspectProcessor.js", "../../@arcgis/core/views/2d/engine/imagery/processor/bandArithmeticProcessor.js", "../../@arcgis/core/views/2d/engine/imagery/processor/compositeBandProcessor.js", "../../@arcgis/core/views/2d/engine/imagery/processor/convolutionProcessor.js", "../../@arcgis/core/views/2d/engine/imagery/processor/extractBandProcessor.js", "../../@arcgis/core/views/2d/engine/imagery/processor/localProcessor.js", "../../@arcgis/core/views/2d/engine/imagery/processor/maskProcessor.js", "../../@arcgis/core/views/2d/engine/imagery/processor/ndviProcessor.js", "../../@arcgis/core/views/2d/engine/imagery/processor/remapProcessor.js", "../../@arcgis/core/views/2d/engine/imagery/processor/reprojectProcessor.js", "../../@arcgis/core/views/2d/engine/imagery/processor/slopeProcessor.js", "../../@arcgis/core/views/2d/engine/imagery/processor/stretchProcessor.js", "../../@arcgis/core/views/2d/engine/imagery/processor/rasterProcessor.js", "../../@arcgis/core/views/2d/engine/imagery/BrushRasterBitmap.js", "../../@arcgis/core/views/2d/engine/imagery/RasterTile.js", "../../@arcgis/core/views/2d/engine/imagery/RasterTileContainer.js", "../../@arcgis/core/views/2d/layers/imagery/BaseImageryTileSubView2D.js", "../../@arcgis/core/views/2d/layers/imagery/ImageryTileView2D.js", "../../@arcgis/core/views/2d/engine/imagery/RasterVFTile.js", "../../@arcgis/core/views/2d/engine/imagery/RasterVFTileContainer.js", "../../@arcgis/core/views/2d/layers/imagery/VectorFieldTileView2D.js", "../../@arcgis/core/views/layers/ImageryTileLayerView.js", "../../@arcgis/core/views/2d/layers/ImageryTileLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{dispose<PERSON>aybe as e,isSome as t}from\"../../../../core/maybe.js\";import{g as s,h as r,r as i,k as o,m as a}from\"../../../../chunks/mat3.js\";import{c as u}from\"../../../../chunks/mat3f32.js\";import{f as h}from\"../../../../chunks/vec2f32.js\";import{extractBands as n}from\"../../../../layers/support/rasterFunctions/pixelUtils.js\";import{DisplayObject as l}from\"../DisplayObject.js\";import{TextureSamplingMode as d}from\"../../../webgl/enums.js\";import{createTransformTexture as p,createRasterTexture as _,createColormapTexture as x}from\"../../../webgl/rasterUtils.js\";const c={bandCount:3,outMin:0,outMax:1,minCutOff:[0,0,0],maxCutOff:[255,255,255],factor:[1/255,1/255,1/255],useGamma:!1,gamma:[1,1,1],gammaCorrection:[1,1,1],colormap:null,colormapOffset:null,stretchType:\"none\",type:\"stretch\"};class m extends l{constructor(e=null,t=null,s=null){super(),this._textureInvalidated=!0,this._colormapTextureInvalidated=!0,this._rasterTexture=null,this._rasterTextureBandIds=null,this._transformGridTexture=null,this._colormapTexture=null,this._colormap=null,this._supportsBilinearTexture=!0,this._processedTexture=null,this.functionTextures=[],this.projected=!1,this.stencilRef=0,this.coordScale=[1,1],this._processed=!1,this._symbolizerParameters=null,this.height=null,this.isRendereredSource=!1,this.pixelRatio=1,this.resolution=0,this.rotation=0,this._source=null,this.rawPixelData=null,this._suspended=!1,this._bandIds=null,this._interpolation=null,this._transformGrid=null,this.width=null,this.x=0,this.y=0,this.source=e,this.transformGrid=t,this.interpolation=s}destroy(){this._disposeTextures()}get processedTexture(){return this._processedTexture}set processedTexture(e){this._processedTexture!==e&&(this._disposeTextures(!0),this._processedTexture=e)}get rasterTexture(){return this._rasterTexture}set rasterTexture(e){this._rasterTexture!==e&&(this._rasterTexture?.dispose(),this._rasterTexture=e)}get processed(){return this._processed}set processed(t){this._processed=t,t||(e(this.processedTexture),this.invalidateTexture())}get symbolizerParameters(){return this._symbolizerParameters||c}set symbolizerParameters(e){this._symbolizerParameters!==e&&(this._symbolizerParameters=e,this._colormapTextureInvalidated=!0,this.commonUniforms=null)}get source(){return this._source}set source(e){this._source!==e&&(this._source=e,this._rasterTexture&&(this._rasterTexture.dispose(),this._rasterTexture=null,this._rasterTextureBandIds=null),this.projected=!1,this.invalidateTexture())}get suspended(){return this._suspended}set suspended(e){this._suspended&&!e&&this.stage&&(this.ready(),this.requestRender()),this._suspended=e}get bandIds(){return this._bandIds}set bandIds(e){this._bandIds=e,this._isBandIdschanged(e)&&(this.projected=!1,this.invalidateTexture())}get interpolation(){return this._interpolation||\"nearest\"}set interpolation(e){this._interpolation=e,this._rasterTexture&&this._rasterTexture.setSamplingMode(\"bilinear\"===this._getTextureSamplingMethod(e||\"nearest\")?d.LINEAR:d.NEAREST)}get transformGrid(){return this._transformGrid}set transformGrid(t){this._transformGrid=t,this._transformGridTexture=e(this._transformGridTexture)}invalidateTexture(){this._textureInvalidated||(this._textureInvalidated=!0,this.requestRender())}_createTransforms(){return{dvs:u()}}setTransform(e){const t=s(this.transforms.dvs),[u,n]=e.toScreenNoRotation([0,0],[this.x,this.y]),l=this.resolution/this.pixelRatio/e.resolution,d=l*this.width,p=l*this.height,_=Math.PI*this.rotation/180;r(t,t,h(u,n)),r(t,t,h(d/2,p/2)),i(t,t,-_),r(t,t,h(-d/2,-p/2)),o(t,t,h(d,p)),a(this.transforms.dvs,e.displayViewMat3,t)}getTextures({forProcessing:e=!1,useProcessedTexture:t=!1}={}){const s=t?this._processedTexture??this._rasterTexture:this._rasterTexture,r=[],i=[];return s?t?(i.push(s),r.push(\"u_image\"),this._colormapTexture&&(i.push(this._colormapTexture),r.push(\"u_colormap\")),{names:r,textures:i}):(this._transformGridTexture&&(i.push(this._transformGridTexture),r.push(\"u_transformGrid\")),i.push(s),r.push(\"u_image\"),this._colormapTexture&&!e&&(i.push(this._colormapTexture),r.push(\"u_colormap\")),{names:r,textures:i}):{names:r,textures:i}}onAttach(){this.invalidateTexture()}onDetach(){this.invalidateTexture()}updateTexture({context:e}){if(!this.stage)return void this._disposeTextures();const t=this._isValidSource(this.source);t&&this._colormapTextureInvalidated&&(this._colormapTextureInvalidated=!1,this._updateColormapTexture(e)),this._textureInvalidated&&(this._textureInvalidated=!1,this._createOrDestroyRasterTexture(e),this._rasterTexture&&(t?this.transformGrid&&!this._transformGridTexture&&(this._transformGridTexture=p(e,this.transformGrid)):this._rasterTexture.setData(null)),this.suspended||(this.ready(),this.requestRender()))}updateProcessedTexture(){const{functionTextures:e}=this;0!==e.length&&(this.processedTexture=e.shift(),e.forEach((e=>e?.dispose())),e.length=0)}_createOrDestroyRasterTexture(e){const s=t(this.source)?n(this.source,this.bandIds):null;if(!this._isValidSource(s))return void(this._rasterTexture&&(this._rasterTexture.dispose(),this._rasterTextureBandIds=null,this._rasterTexture=null));const r=!this._isBandIdschanged(this.bandIds);if(this._rasterTexture){if(r)return;this._rasterTexture.dispose(),this._rasterTextureBandIds=null,this._rasterTexture=null}this._supportsBilinearTexture=!!e.capabilities.textureFloat?.textureFloatLinear;const i=this._getTextureSamplingMethod(this.interpolation),o=this.isRendereredSource||!e.capabilities.textureFloat?.textureFloat;this._rasterTexture=_(e,s,i,o),this.projected=!1,this._processed=!1,this._rasterTextureBandIds=this.bandIds?[...this.bandIds]:null}_isBandIdschanged(e){const t=this._rasterTextureBandIds;return!(null==t&&null==e||t&&e&&t.join(\"\")===e.join(\"\"))}_isValidSource(e){return t(e)&&e.pixels?.length>0}_getTextureSamplingMethod(e){const{type:s,colormap:r}=this.symbolizerParameters,i=\"lut\"===s||\"stretch\"===s&&t(r);return!this._supportsBilinearTexture||i||\"bilinear\"!==e&&\"cubic\"!==e?\"nearest\":\"bilinear\"}_updateColormapTexture(e){const t=this._colormap,s=this.symbolizerParameters.colormap;return s?t?s.length!==t.length||s.some(((e,s)=>e!==t[s]))?(this._colormapTexture&&(this._colormapTexture.dispose(),this._colormapTexture=null),this._colormapTexture=x(e,s),void(this._colormap=s)):void 0:(this._colormapTexture=x(e,s),void(this._colormap=s)):(this._colormapTexture&&(this._colormapTexture.dispose(),this._colormapTexture=null),void(this._colormap=null))}_disposeTextures(e=!1){this._transformGridTexture&&(this._transformGridTexture.dispose(),this._transformGridTexture=null),!e&&this._colormapTexture&&(this._colormapTexture.dispose(),this._colormapTexture=null,this._colormap=null,this._colormapTextureInvalidated=!0),!e&&this._rasterTexture&&(this._rasterTexture.dispose(),this._rasterTexture=null,this._rasterTextureBandIds=null),this._processedTexture&&(this._processedTexture.dispose(),this._processedTexture=null)}}class T extends m{get source(){return this._source}}function f(e){return t(e.source)}export{m as RasterBitmap,T as RasterBitmapWithSource,f as hasSource};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nfunction i(i){const n=[];return i&&(n.push(\"applyProjection\"),1===i.spacing[0]&&n.push(\"lookupProjection\")),n}function n(i,n,e){const t=!e.capabilities.textureFloat?.textureFloatLinear,u=[];return\"cubic\"===i?u.push(\"bicubic\"):\"bilinear\"===i&&(n?(u.push(\"bilinear\"),u.push(\"nnedge\")):t&&u.push(\"bilinear\")),u}export{n as getInterpolationDefines,i as getProjectionDefines};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{getProjectionDefines as r}from\"./utils.js\";import{setTextures as t,setUniforms as e,getColormapUniforms as o}from\"../../../../webgl/rasterUtils.js\";const s={vsPath:\"raster/common\",fsPath:\"raster/lut\",attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])};function a(t,e,o){const a=o?[]:r(e.transformGrid);return{defines:a,program:t.painter.materialManager.getProgram(s,a)}}function n(r,s,a,n,m=!1){const{names:i,textures:c}=a.getTextures({useProcessedTexture:m});t(r.context,s,i,c),e(s,n,a.commonUniforms),s.setUniformMatrix3fv(\"u_dvsMat3\",a.transforms.dvs);const{colormap:f,colormapOffset:u}=a.symbolizerParameters,d=o(f,u);e(s,n,d)}const m={createProgram:a,bindTextureAndUniforms:n};export{m as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{getProjectionDefines as r,getInterpolationDefines as t}from\"./utils.js\";import{setTextures as o,setUniforms as e,getColormapUniforms as s,getShadedReliefUniforms as a}from\"../../../../webgl/rasterUtils.js\";const n={vsPath:\"raster/common\",fsPath:\"raster/hillshade\",attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])};function m(o,e,s){const{colormap:a}=e.symbolizerParameters,m=[...s?[]:r(e.transformGrid),...t(e.interpolation,null!=a,o.context)];null!=a&&m.push(\"applyColormap\");return{defines:m,program:o.painter.materialManager.getProgram(n,m)}}function i(r,t,n,m,i=!1){const{names:l,textures:c}=n.getTextures({useProcessedTexture:i});o(r.context,t,l,c),e(t,m,n.commonUniforms),t.setUniformMatrix3fv(\"u_dvsMat3\",n.transforms.dvs);const f=n.symbolizerParameters,{colormap:u,colormapOffset:p}=f;if(null!=u){const r=s(u,p);e(t,m,r)}const d=a(f);e(t,m,d)}const l={createProgram:m,bindTextureAndUniforms:i};export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{getProjectionDefines as r,getInterpolationDefines as t}from\"./utils.js\";import{setTextures as e,setUniforms as o,getColormapUniforms as s,getStretchUniforms as n}from\"../../../../webgl/rasterUtils.js\";const a={vsPath:\"raster/common\",fsPath:\"raster/stretch\",attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])};function m(e,o,s){const{colormap:n}=o.symbolizerParameters,m=[...s?[]:r(o.transformGrid),...t(o.interpolation,null!=n,e.context)];o.isRendereredSource&&!s?m.push(\"noop\"):null!=n&&m.push(\"applyColormap\");return{defines:m,program:e.painter.materialManager.getProgram(a,m)}}function i(r,t,a,m,i=!1){const{names:c,textures:l}=a.getTextures({useProcessedTexture:i});e(r.context,t,c,l),o(t,m,a.commonUniforms),t.setUniformMatrix3fv(\"u_dvsMat3\",a.transforms.dvs);const u=a.symbolizerParameters,{colormap:p,colormapOffset:f}=u;if(null!=p){const r=s(p,f);o(t,m,r)}const d=n(u);o(t,m,d)}const c={createProgram:m,bindTextureAndUniforms:i};export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"./lut.js\";import e from\"./shadedrelief.js\";import r from\"./stretch.js\";const s=new Map;function o(t){return s.get(t)}s.set(\"lut\",t),s.set(\"hillshade\",e),s.set(\"stretch\",r);export{o as getColorizer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{setTextures as e}from\"../../../../webgl/rasterUtils.js\";const t=[1,1],n=[2,0,0,0,2,0,-1,-1,0];function a(t,n,a){const{context:r,rasterFunction:s,hasBranches:i}=t,{raster:o}=s.parameters,f=i?o?.id??-1:0,m=a.functionTextures[f]??a.rasterTexture;e(r,n,[\"u_image\"],[m])}function r(t,n,r){const{rasters:s}=t.rasterFunction.parameters;if(!s)return;if(s.length<2)return a(t,n,r);const i=s.filter((e=>\"Constant\"!==e.name)).map((e=>null!=e.id&&\"Identity\"!==e.name?r.functionTextures[e.id]:r.rasterTexture));if(e(t.context,n,[\"u_image\",\"u_image1\",\"u_image2\"].slice(0,i.length),i),i.length!==s.length)if(2===s.length){const e=s.findIndex((e=>\"Constant\"===e.name)),t=0===e?[0,1,0,1,0,0,0,0,0]:[1,0,0,0,1,0,0,0,0],{value:a}=s[e].parameters;n.setUniform1f(\"u_image1Const\",a),n.setUniformMatrix3fv(\"u_imageSwap\",t)}else if(3===s.length){const e=[];if(s.forEach(((t,n)=>\"Constant\"===t.name&&e.push(n))),1===e.length){const{value:t}=s[e[0]].parameters;n.setUniform1f(\"u_image1Const\",t);const a=0===e[0]?[0,1,0,0,0,1,1,0,0]:1===e[0]?[1,0,0,0,0,1,0,1,0]:[1,0,0,0,1,0,0,0,1];n.setUniformMatrix3fv(\"u_imageSwap\",a)}else if(2===e.length){const{value:t}=s[e[0]].parameters;n.setUniform1f(\"u_image1Const\",t);const{value:a}=s[e[1]].parameters;n.setUniform1f(\"u_image2Const\",a);const r=s.findIndex((e=>\"Constant\"!==e.name)),i=0===r?[1,0,0,0,1,0,0,0,1]:1===r?[0,1,0,1,0,0,0,0,1]:[0,0,1,1,0,0,0,1,0];n.setUniformMatrix3fv(\"u_imageSwap\",i)}}}function s(e){e.setUniform2fv(\"u_coordScale\",t),e.setUniformMatrix3fv(\"u_dvsMat3\",n)}export{s as setCoordsAndTransforms,r as setMultipleImageTextures,a as setSingleImageTextures};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{setSingleImageTextures as t,setCoordsAndTransforms as r}from\"./utils.js\";const e={vsPath:\"raster/rfx/vs\",fsPath:\"raster/rfx/aspect\",attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])};function a(t,r){return t.painter.materialManager.getProgram(e,[])}function o(e,a,o){t(e,a,o),r(a);const{width:s,height:i,resolution:n}=o;a.setUniform2fv(\"u_srcImageSize\",[s,i]),a.setUniform2fv(\"u_cellSize\",[n,n])}const s={createProgram:a,bindTextureAndUniforms:o};export{s as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{setSingleImageTextures as t,setCoordsAndTransforms as r}from\"./utils.js\";const a={vsPath:\"raster/rfx/vs\",fsPath:\"raster/rfx/bandarithmetic\",attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])};function e(t,r){const{painter:e,rasterFunction:n}=t,{indexType:s}=n.parameters;return e.materialManager.getProgram(a,[s])}function n(a,e,n){t(a,e,n),r(e);const{bandIndexMat3:s}=a.rasterFunction.parameters;e.setUniformMatrix3fv(\"u_bandIndexMat3\",s)}const s={createProgram:e,bindTextureAndUniforms:n};export{s as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{setMultipleImageTextures as t,setCoordsAndTransforms as r}from\"./utils.js\";const a={vsPath:\"raster/rfx/vs\",fsPath:\"raster/rfx/compositeband\",attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])};function e(t,r){return t.painter.materialManager.getProgram(a,[])}function o(a,e,o){t(a,e,o),r(e)}const n={createProgram:e,bindTextureAndUniforms:o};export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{setSingleImageTextures as e,setCoordsAndTransforms as r}from\"./utils.js\";const t={vsPath:\"raster/rfx/vs\",fsPath:\"raster/rfx/convolution\",attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])};function a(e,r){const{painter:a,rasterFunction:n}=e,{kernelRows:o,kernelCols:s}=n.parameters,i=[{name:\"rows\",value:o},{name:\"cols\",value:s}];return a.materialManager.getProgram(t,i)}function n(t,a,n){e(t,a,n),r(a),a.setUniform2fv(\"u_srcImageSize\",[n.width,n.height]);const{kernel:o,clampRange:s}=t.rasterFunction.parameters;a.setUniform1fv(\"u_kernel\",o),a.setUniform2fv(\"u_clampRange\",s)}const o={createProgram:a,bindTextureAndUniforms:n};export{o as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{setSingleImageTextures as t,setCoordsAndTransforms as r}from\"./utils.js\";const a={vsPath:\"raster/rfx/vs\",fsPath:\"raster/rfx/extractband\",attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])};function e(t,r){return t.painter.materialManager.getProgram(a,[])}function n(a,e,n){t(a,e,n),r(e);const{bandIndexMat3:o}=a.rasterFunction.parameters;e.setUniformMatrix3fv(\"u_bandIndexMat3\",o)}const o={createProgram:e,bindTextureAndUniforms:n};export{o as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{setMultipleImageTextures as t,setCoordsAndTransforms as n}from\"./utils.js\";import{ContextType as a}from\"../../../../webgl/context-util.js\";const e={vsPath:\"raster/rfx/vs\",fsPath:\"raster/rfx/local\",attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])},o=new Set([\"sinh\",\"cosh\",\"tanh\",\"asinh\",\"acosh\",\"atanh\"]);function r(t){const{painter:n,rasterFunction:r}=t,{imageCount:s,operationName:i,rasters:u,isOutputRounded:c}=r.parameters;let m=i.toLowerCase();t.context.type===a.WEBGL1&&o.has(m)&&(m=`polyfill${m}`);const p=[m];2===s&&p.push(\"twoImages\");const h=u.filter((t=>\"Constant\"===t.name));return h.length&&(p.push(\"oneConstant\"),2===h.length&&p.push(\"twoConstant\")),c&&p.push(\"roundOutput\"),n.materialManager.getProgram(e,p)}function s(a,e,o){t(a,e,o),n(e);const{domainRange:r}=a.rasterFunction.parameters;e.setUniform2fv(\"u_domainRange\",r)}const i={createProgram:r,bindTextureAndUniforms:s};export{i as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{setSingleImageTextures as t,setCoordsAndTransforms as a}from\"./utils.js\";const r={vsPath:\"raster/rfx/vs\",fsPath:\"raster/rfx/mask\",attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])};function n(t,a){const{painter:n,rasterFunction:e}=t,s=e.parameters.bandCount>1?[\"multiBand\"]:[];return n.materialManager.getProgram(r,s)}function e(r,n,e){t(r,n,e),a(n);const{includedRanges:s,noDataValues:o}=r.rasterFunction.parameters;n.setUniform1fv(\"u_includedRanges\",s),n.setUniform1fv(\"u_noDataValues\",o)}const s={createProgram:n,bindTextureAndUniforms:e};export{s as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{setSingleImageTextures as t,setCoordsAndTransforms as r}from\"./utils.js\";const a={vsPath:\"raster/rfx/vs\",fsPath:\"raster/rfx/ndvi\",attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])};function e(t,r){const{painter:e,rasterFunction:n}=t,s=n.parameters.scaled?[\"scaled\"]:[];return e.materialManager.getProgram(a,s)}function n(a,e,n){t(a,e,n),r(e);const{bandIndexMat3:s}=a.rasterFunction.parameters;e.setUniformMatrix3fv(\"u_bandIndexMat3\",s)}const s={createProgram:e,bindTextureAndUniforms:n};export{s as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{setSingleImageTextures as a,setCoordsAndTransforms as t}from\"./utils.js\";const r={vsPath:\"raster/rfx/vs\",fsPath:\"raster/rfx/remap\",attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])};function e(a,t){return a.painter.materialManager.getProgram(r,[])}function n(r,e,n){a(r,e,n),t(e);const{noDataRanges:s,rangeMaps:o,allowUnmatched:f,clampRange:i}=r.rasterFunction.parameters;e.setUniform1fv(\"u_noDataRanges\",s),e.setUniform1fv(\"u_rangeMaps\",o),e.setUniform1f(\"u_unmatchMask\",f?1:0),e.setUniform2fv(\"u_clampRange\",i)}const s={createProgram:e,bindTextureAndUniforms:n};export{s as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Z as t}from\"../../../../../chunks/vec2f64.js\";import{setTextures as r}from\"../../../../webgl/rasterUtils.js\";const e={vsPath:\"raster/common\",fsPath:\"raster/reproject\",attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])};function i(t,r){const{painter:i}=t,o=[],s=!t.context.capabilities.textureFloat?.textureFloatLinear,{interpolation:n,transformGrid:a}=r;return\"cubic\"===n?o.push(\"bicubic\"):\"bilinear\"===n&&s&&o.push(\"bilinear\"),a&&(o.push(\"applyProjection\"),1===a.spacing[0]&&o.push(\"lookupProjection\")),i.materialManager.getProgram(e,o)}function o(e,i,o){const{names:s,textures:n}=o.getTextures({forProcessing:!0});r(e.context,i,s,n),i.setUniform1f(\"u_scale\",1),i.setUniform2fv(\"u_offset\",[0,0]),i.setUniform2fv(\"u_coordScale\",[1,1]),i.setUniformMatrix3fv(\"u_dvsMat3\",[2,0,0,0,2,0,-1,-1,0]),i.setUniform1i(\"u_flipY\",0),i.setUniform1f(\"u_opacity\",1);const{width:a,height:f,source:c,transformGrid:u}=o;i.setUniform2fv(\"u_srcImageSize\",[c.width,c.height]),i.setUniform2fv(\"u_targetImageSize\",[a,f]),i.setUniform2fv(\"u_transformSpacing\",u?u.spacing:t),i.setUniform2fv(\"u_transformGridSize\",u?u.size:t)}const s={createProgram:i,bindTextureAndUniforms:o};export{s as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{setSingleImageTextures as e,setCoordsAndTransforms as r}from\"./utils.js\";const t={vsPath:\"raster/rfx/vs\",fsPath:\"raster/rfx/slope\",attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])};function o(e,r){const{painter:o,rasterFunction:i}=e,{slopeType:s}=i.parameters,a=\"percent-rise\"===s?[\"percentRise\"]:[];return o.materialManager.getProgram(t,a)}function i(t,o,i){e(t,o,i),r(o);const{width:s,height:a,resolution:n}=i,{zFactor:c,slopeType:f,pixelSizePower:p,pixelSizeFactor:u}=t.rasterFunction.parameters;o.setUniform2fv(\"u_srcImageSize\",[s,a]),o.setUniform2fv(\"u_cellSize\",[n,n]),o.setUniform1f(\"u_zFactor\",c),o.setUniform1f(\"u_pixelSizePower\",\"adjusted\"===f?p:0),o.setUniform1f(\"u_pixelSizeFactor\",\"adjusted\"===f?u:0)}const s={createProgram:o,bindTextureAndUniforms:i};export{s as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{setSingleImageTextures as t,setCoordsAndTransforms as r}from\"./utils.js\";const a={vsPath:\"raster/rfx/vs\",fsPath:\"raster/rfx/stretch\",attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])};function u(t,r){const{useGamma:u,bandCount:e,isOutputRounded:n}=t.rasterFunction.parameters,o=[];return u&&o.push(\"useGamma\"),e>1&&o.push(\"multiBand\"),n&&o.push(\"roundOutput\"),t.painter.materialManager.getProgram(a,o)}function e(a,u,e){t(a,u,e),r(u);const{width:n,height:o}=e,m=a.rasterFunction.parameters;u.setUniform2fv(\"u_srcImageSize\",[n,o]),u.setUniform1f(\"u_minOutput\",m.outMin),u.setUniform1f(\"u_maxOutput\",m.outMax),u.setUniform1fv(\"u_factor\",m.factor),u.setUniform1fv(\"u_minCutOff\",m.minCutOff),u.setUniform1fv(\"u_maxCutOff\",m.maxCutOff),u.setUniform1fv(\"u_gamma\",m.gamma),u.setUniform1fv(\"u_gammaCorrection\",m.gammaCorrection)}const n={createProgram:u,bindTextureAndUniforms:e};export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as r}from\"../../../../../core/maybe.js\";import e from\"./aspectProcessor.js\";import o from\"./bandArithmeticProcessor.js\";import t from\"./compositeBandProcessor.js\";import s from\"./convolutionProcessor.js\";import n from\"./extractBandProcessor.js\";import i from\"./localProcessor.js\";import c from\"./maskProcessor.js\";import m from\"./ndviProcessor.js\";import a from\"./remapProcessor.js\";import p from\"./reprojectProcessor.js\";import f from\"./slopeProcessor.js\";import u from\"./stretchProcessor.js\";import{ContextType as l}from\"../../../../webgl/context-util.js\";import{TextureType as d,PixelFormat as j,SizedPixelFormat as h,TextureSamplingMode as P,PixelType as w,TextureWrapMode as x}from\"../../../../webgl/enums.js\";import{Texture as T}from\"../../../../webgl/Texture.js\";const b=new Map;function g(r,e,o){const t={width:e,height:o,target:d.TEXTURE_2D,pixelFormat:j.RGBA,internalFormat:r.type===l.WEBGL2?h.RGBA32F:j.RGBA,samplingMode:P.NEAREST,dataType:w.FLOAT,isImmutable:r.type===l.WEBGL2,wrapMode:x.CLAMP_TO_EDGE,flipped:!1};return new T(r,t)}function A(e,o,t,s){const{context:n,requestRender:i,allowDelayedRender:c}=e,m=s.createProgram(e,t);if(c&&r(i)&&!m.compiled)return i(),null;const{width:a,height:p}=t;return n.bindFramebuffer(o),n.setViewport(0,0,a,p),n.useProgram(m),m}function B(r){return b.get(r.toLowerCase())}function E(r,e,o,t){const s=r.rasterFunction.name.toLowerCase(),n=\"reproject\"===s?p:B(s);if(null==n)return;const i=A(r,o,t,n);if(!i)return;n.bindTextureAndUniforms(r,i,t),e.draw();const{width:c,height:m}=t,a=g(r.context,c,m);if(o.copyToTexture(0,0,c,m,0,0,a),\"reproject\"===s)t.rasterTexture=a,t.projected=!0;else{const e=r.hasBranches?r.rasterFunction.id:0;t.functionTextures[e]=a}}b.set(\"aspect\",e),b.set(\"bandarithmetic\",o),b.set(\"compositeband\",t),b.set(\"convolution\",s),b.set(\"extractband\",n),b.set(\"local\",i),b.set(\"mask\",c),b.set(\"ndvi\",m),b.set(\"remap\",a),b.set(\"slope\",f),b.set(\"stretch\",u);export{g as createTexture,E as process};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{disposeMaybe as e,isSome as t}from\"../../../../core/maybe.js\";import{hasSource as r}from\"./RasterBitmap.js\";import{getColorizer as s}from\"./colorizer/rasterColorizer.js\";import{process as i,createTexture as o}from\"./processor/rasterProcessor.js\";import{TILE_SIZE as n}from\"../webgl/definitions.js\";import a from\"../webgl/VertexStream.js\";import d from\"../webgl/brushes/WGLBrush.js\";import{BlendFactor as m,CompareFunction as c,TargetType as f,DepthStencilTargetType as u}from\"../../../webgl/enums.js\";import{FramebufferObject as h}from\"../../../webgl/FramebufferObject.js\";import{getCommonUniforms as p,getUniformLocationInfos as l,getBasicGridUniforms as _}from\"../../../webgl/rasterUtils.js\";class b extends d{constructor(){super(...arguments),this.name=\"raster\",this._quad=null,this._rendererUniformInfos=new Map,this._fbo=null}dispose(){e(this._quad),e(this._fbo)}prepareState(e){const{context:t,renderPass:r}=e,s=\"raster\"===r;t.setBlendingEnabled(!s),t.setBlendFunctionSeparate(m.ONE,m.ONE_MINUS_SRC_ALPHA,m.ONE,m.ONE_MINUS_SRC_ALPHA),t.setColorMask(!0,!0,!0,!0),t.setStencilWriteMask(0),t.setStencilTestEnabled(!s)}draw(e,t){if(!r(t)||t.suspended)return;const{renderPass:s}=e;if(\"raster-bitmap\"!==s)return\"raster\"===s?this._process(e,t):void this._drawBitmap(e,t,!0);this._drawBitmap(e,t)}_process(t,r){const{rasterFunction:s}=t,o=\"Reproject\"===s.name;if(!(o?!(r.rasterTexture&&r.projected):!r.processed))return;const{timeline:n,context:a}=t;n.begin(this.name);const d=a.getBoundFramebufferObject(),m=a.getViewport();o||(r.processedTexture=e(r.processedTexture)),a.setStencilFunction(c.EQUAL,r.stencilRef,255),r.updateTexture(t),this._initQuad(a);const{isStandardRasterTileSize:f,fbo:u}=this._getRasterFBO(a,r.width,r.height);i(t,this._quad,u,r),f||u.dispose(),a.bindFramebuffer(d),a.setViewport(m.x,m.y,m.width,m.height),n.end(this.name)}_drawBitmap(e,r,i=!1){const{timeline:o,context:n}=e;if(o.begin(this.name),n.setStencilFunction(c.EQUAL,r.stencilRef,255),r.updateTexture(e),i&&!r.processedTexture){if(r.updateProcessedTexture(),!r.processedTexture)return void o.end(this.name);r.processed=!0}this._initBitmapCommonUniforms(r);const d=r.symbolizerParameters.type,m=s(d),{requestRender:f,allowDelayedRender:u}=e,{defines:h,program:p}=m.createProgram(e,r,i);if(u&&t(f)&&!p.compiled)return void f();n.useProgram(p);const l=this._getUniformInfos(d,n,p,h);this._quad||(this._quad=new a(n,[0,0,1,0,0,1,1,1])),m.bindTextureAndUniforms(e,p,r,l,i),this._quad.draw(),o.end(this.name)}_initBitmapCommonUniforms(e){if(!e.commonUniforms){const t=_(1,[0,0]),{transformGrid:r,width:s,height:i}=e,o=p(r,[s,i],[e.source.width,e.source.height],1,!1);e.commonUniforms={...t,...o,u_coordScale:e.coordScale}}}_getRasterFBO(e,t,r){const s=t===n||r===n;return s?(this._fbo||(this._fbo=this._createNewFBO(e,t,r)),{isStandardRasterTileSize:s,fbo:this._fbo}):{isStandardRasterTileSize:s,fbo:this._createNewFBO(e,t,r)}}_createNewFBO(e,t,r){const s=o(e,t,r);return new h(e,{colorTarget:f.TEXTURE,depthStencilTarget:u.NONE,width:t,height:r},s)}_initQuad(e){this._quad||(this._quad=new a(e,[0,0,1,0,0,1,1,1]))}_getUniformInfos(e,t,r,s){const i=s.length>0?e+\"-\"+s.join(\"-\"):e;if(this._rendererUniformInfos.has(i))return this._rendererUniformInfos.get(i);const o=l(t,r);return this._rendererUniformInfos.set(i,o),o}}export{b as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{c as t}from\"../../../../chunks/mat3f32.js\";import{RasterBitmap as s}from\"./RasterBitmap.js\";import{TiledDisplayObject as e}from\"../webgl/TiledDisplayObject.js\";class i extends e{constructor(t,e,i,a,r,n,l=null){super(t,e,i,a,r,n),this.bitmap=null,this.bitmap=new s(l,null,null),this.bitmap.coordScale=[r,n],this.bitmap.once(\"isReady\",(()=>this.ready()))}destroy(){super.destroy(),this.bitmap.destroy(),this.bitmap=null,this.stage=null}set stencilRef(t){this.bitmap.stencilRef=t}get stencilRef(){return this.bitmap.stencilRef}setTransform(t){super.setTransform(t),this.bitmap.transforms.dvs=this.transforms.dvs}_createTransforms(){return{dvs:t(),tileMat3:t()}}onAttach(){this.bitmap.stage=this.stage}onDetach(){this.bitmap.stage=null}}export{i as RasterTile};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{create as e}from\"../../../../geometry/support/aaBoundingRect.js\";import{getWorldWidth as s}from\"../../viewpointUtils.js\";import t from\"./BrushRasterBitmap.js\";import{RasterTile as r}from\"./RasterTile.js\";import{WGLDrawPhase as i}from\"../webgl/enums.js\";import n from\"../webgl/TileContainer.js\";class o extends n{constructor(){super(...arguments),this.isCustomTilingScheme=!1}createTile(e){const s=this._getTileBounds(e),[t,i]=this._tileInfoView.tileInfo.size,n=this._tileInfoView.getTileResolution(e.level);return new r(e,n,s[0],s[3],t,i)}prepareRenderPasses(e){const s=e.registerRenderPass({name:\"imagery (tile)\",brushes:[t],target:()=>this.children.map((e=>e.bitmap)),drawPhase:i.MAP});return[...super.prepareRenderPasses(e),s]}doRender(e){if(!this.visible||e.drawPhase!==i.MAP)return;const{rasterFunctionChain:s}=this;if(!s)return e.renderPass=\"raster-bitmap\",void super.doRender(e);const[t,r]=this._tileInfoView.tileInfo.size;if(e.renderPass=\"raster\",e.rasterFunction={name:\"Reproject\",parameters:{targetImageSize:[t,r]},pixelType:\"f32\",id:0,isNoopProcess:!1},super.doRender(e),s?.functions.length){const{functions:t,hasBranches:r}=s;for(let s=0;s<t.length;s++){const i=t[s];\"Constant\"!==i.name&&\"Identity\"!==i.name&&(e.renderPass=\"raster\",e.rasterFunction=i,e.hasBranches=r,super.doRender(e))}}e.rasterFunction=null,e.renderPass=\"bitmap\",super.doRender(e)}_getTileBounds(t){const r=this._tileInfoView.getTileBounds(e(),t);if(this.isCustomTilingScheme&&t.world){const{tileInfo:e}=this._tileInfoView,i=s(e.spatialReference);if(i){const s=e.lodAt(t.level);if(!s)return r;const{resolution:n}=s,o=i/n%e.size[0],a=o?(e.size[0]-o)*n:0;r[0]-=a*t.world,r[2]-=a*t.world}}return r}}export{o as RasterTileContainer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import\"../../../../geometry.js\";import{HandleOwner as t}from\"../../../../core/HandleOwner.js\";import i from\"../../../../core/Logger.js\";import{isSome as s,unwrap as r}from\"../../../../core/maybe.js\";import{debounce as a,isAbortError as o,eachAlways as l}from\"../../../../core/promiseUtils.js\";import{property as n}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as h}from\"../../../../core/accessorSupport/decorators/subclass.js\";import c from\"../../../../geometry/Point.js\";import u from\"../../../../layers/support/PixelBlock.js\";import p from\"../../../../layers/support/TileInfo.js\";import{update as d,unregister as g,register as y,getRasterId as m}from\"../../../../layers/support/rasterDatasets/RawBlockCache.js\";import{extractBands as _}from\"../../../../layers/support/rasterFunctions/pixelUtils.js\";import{getWorldWidth as f,computeProjectedScales as b}from\"../../../../layers/support/rasterFunctions/rasterProjectionHelper.js\";import\"../../tiling/PagedTileQueue.js\";import w from\"../../tiling/TileInfoView.js\";import\"../../tiling/TileKey.js\";import I from\"../../tiling/TileQueue.js\";import P from\"../../tiling/TileStrategy.js\";import{TILE_SIZE as C}from\"../../engine/webgl/definitions.js\";import{getWebGLCapabilities as R}from\"../../../webgl/capabilities.js\";import S from\"../../../../geometry/Extent.js\";const T=[0,0];let v=class extends t{constructor(){super(...arguments),this._emptyTilePixelBlock=null,this._tileStrategy=null,this._tileInfoView=null,this._fetchQueue=null,this._blockCacheRegistryUrl=null,this._blockCacheRegistryId=null,this._srcResolutions=[],this.previousLOD=null,this._needBlockCacheUpdate=!1,this._globalSymbolizerParams=null,this._symbolizerParams=null,this._abortController=null,this._isCustomTilingScheme=!1,this._rasterFunctionState=\"na\",this._globalUpdateRequested=!1,this.attached=!1,this.timeExtent=null,this.redrawOrRefetch=a((async(e={})=>{if(!this.previousLOD||this.layerView.suspended)return;const t=this._rasterFunctionState;e.reprocess&&(await this.updatingHandles.addPromise(this.layer.updateRasterFunction()),this.updateRasterFunctionParameters());const i=this._rasterFunctionState,{type:s}=this;return e.refetch||\"raster\"!==s&&!!e.reprocess||\"cpu\"===i||\"cpu\"===t?this.updatingHandles.addPromise(this.doRefresh()):this.updatingHandles.addPromise(this._redrawImage(e.signal))}))}get useWebGLForProcessing(){return this._get(\"useWebGLForProcessing\")??!0}set useWebGLForProcessing(e){this._set(\"useWebGLForProcessing\",e)}get useProgressiveUpdate(){return null==this._get(\"useProgressiveUpdate\")||this._get(\"useProgressiveUpdate\")}set useProgressiveUpdate(e){if(this._tileStrategy&&this.useProgressiveUpdate!==e){this._tileStrategy.destroy(),this.container.removeAllChildren();const t=this._getCacheSize(e);this._tileStrategy=new P({cachePolicy:\"purge\",acquireTile:e=>this.acquireTile(e),releaseTile:e=>this.releaseTile(e),cacheSize:t,tileInfoView:this._tileInfoView}),this._set(\"useProgressiveUpdate\",e),this.layerView.requestUpdate()}}update(e){this._fetchQueue.pause(),this._fetchQueue.state=e.state,this._tileStrategy.update(e),this._fetchQueue.resume();const{extent:t,resolution:i,scale:s}=e.state,r=this._tileInfoView.getClosestInfoForScale(s);if(this.layer.raster){if(!this.useProgressiveUpdate||this._needBlockCacheUpdate){const e=this._srcResolutions[r.level],s=t.toJSON?t:S.fromJSON(t);d(this._blockCacheRegistryUrl,this._blockCacheRegistryId,s,i,e,this.layer.raster.ioConfig.sampling)}this._needBlockCacheUpdate=!1,this.previousLOD?.level!==r.level&&(this.previousLOD=r,null==this._symbolizerParams||this.layerView.hasTilingEffects||this._updateSymbolizerParams(),this._tileStrategy.updateCacheSize(0))}}moveEnd(){!this.layerView.hasTilingEffects&&this.useProgressiveUpdate||(this._abortController&&this._abortController.abort(),this._abortController=new AbortController,0===this._fetchQueue.length&&this._redrawImage(this._abortController.signal).then((()=>{this._globalUpdateRequested=!1,this.layerView.requestUpdate()})));const e=this._getCacheSize(this.useProgressiveUpdate);this._tileStrategy.updateCacheSize(e),this.layerView.requestUpdate()}get updating(){return this._fetchQueue?.updating||this._globalUpdateRequested||!(!this.updatingHandles||!this.updatingHandles.updating)}attach(){R(\"2d\").supportsTextureFloat||(this.useWebGLForProcessing=!1),this._initializeTileInfo(),this._tileInfoView=new w(this.layerView.tileInfo,this.layerView.fullExtent);const e=this._computeFetchConcurrency();this._fetchQueue=new I({tileInfoView:this._tileInfoView,concurrency:e,process:(e,t)=>this._fetchTile1(e,t)});const t=this._getCacheSize(this.useProgressiveUpdate);this._tileStrategy=new P({cachePolicy:\"purge\",acquireTile:e=>this.acquireTile(e),releaseTile:e=>this.releaseTile(e),cacheSize:t,tileInfoView:this._tileInfoView}),this._updateBlockCacheRegistry()}detach(){this._tileStrategy.destroy(),this._fetchQueue.clear(),this.container.removeAllChildren(),this._fetchQueue=this._tileStrategy=this._tileInfoView=null,g(this._blockCacheRegistryUrl,this._blockCacheRegistryId),this._blockCacheRegistryUrl=this._blockCacheRegistryId=null}acquireTile(e){const t=this.container.createTile(e);return this._enqueueTileFetch(t),this.layerView.requestUpdate(),this._needBlockCacheUpdate=!0,this._globalUpdateRequested=this.layerView.hasTilingEffects||!this.useProgressiveUpdate,t}releaseTile(e){this._fetchQueue.abort(e.key.id),this.container.removeChild(e),e.once(\"detach\",(()=>{e.destroy(),this.layerView.requestUpdate()})),this.layerView.requestUpdate()}createEmptyTilePixelBlock(e=null){const t=null==e||e.join(\",\")===this._tileInfoView.tileInfo.size.join(\",\");if(t&&s(this._emptyTilePixelBlock))return this._emptyTilePixelBlock;e=e||this._tileInfoView.tileInfo.size;const[i,r]=e,a=new u({width:i,height:r,pixels:[new Uint8Array(i*r)],mask:new Uint8Array(i*r),pixelType:\"u8\"});return t&&(this._emptyTilePixelBlock=a),a}_getBandIds(){if(!(\"rasterFunctionChain\"in this.container)||!this.container.rasterFunctionChain)return this.layer.bandIds;const{bandIds:e,raster:t}=this.layer,i=\"rasterFunction\"in t?t.rasterFunction.rawInputBandIds:null;return e?.length&&i?.length&&1!==t.rasterInfo.bandCount?e.map((e=>i[Math.min(e,i.length-1)])):e||i}updateRasterFunctionParameters(){}_fetchTile1(e,t){const i=s(t)?t.signal:null,a=this.canUseWebGLForProcessing(),{layerView:o}=this,{tileInfo:l}=o,n=!l.isWrappable&&s(f(o.view.spatialReference)),h=a&&this.layer.raster.hasUniqueSourceStorageInfo,c={allowPartialFill:!0,datumTransformation:o.datumTransformation,interpolation:a?\"nearest\":this.layer.interpolation,registryId:this._blockCacheRegistryId,requestRawData:h,skipRasterFunction:\"raster\"===this.type&&null!=this.container.rasterFunctionChain,signal:r(i),srcResolution:this._srcResolutions[e.level],timeExtent:o.timeExtent,tileInfo:l,disableWrapAround:n};return this.fetchTile(e,c)}_getCacheSize(e){return e?40:0}_initializeTileInfo(){const{layerView:e}=this,t=e.view.spatialReference,i=new c({x:e.fullExtent.xmin,y:e.fullExtent.ymax,spatialReference:t});if(this._canUseLayerLODs()){const{lods:s}=this.layer.tileInfo,r=s.map((({scale:e})=>e)),a=p.create({spatialReference:t,size:C,scales:r}),o=t.isGeographic?.01*1e-5:.01;if(this._isCustomTilingScheme=Math.abs(a.origin.x-i.x)>o,(0===a.origin.x||a.origin.x>i.x)&&(a.origin=i),!this._isCustomTilingScheme){const e=p.create({spatialReference:t,size:C}).lods.map((({scale:e})=>e));this._isCustomTilingScheme=r.some((t=>!e.some((e=>Math.abs(e-t)<.001))))}return e.set(\"tileInfo\",a),void(this._srcResolutions=s.map((({resolution:e})=>({x:e,y:e}))))}const{scales:s,srcResolutions:r,isCustomTilingScheme:a}=b(this.layer.rasterInfo,t,C),o=p.create({spatialReference:t,size:C,scales:s});(0===o.origin.x||o.origin.x>i.x)&&(o.origin=i),this._isCustomTilingScheme=a,e.set(\"tileInfo\",o),this._srcResolutions=r??[]}_canUseLayerLODs(){const{layer:e,layerView:t}=this;if(\"Map\"!==e.raster.tileType)return!1;const{lods:i}=e.tileInfo,s=t.view.constraints?.effectiveLODs;if(!(s?.length===i.length&&s.every((({scale:e},t)=>Math.abs(e-i[t].scale)<.001))))return!1;const r=[];for(let a=0;a<i.length-1;a++)r.push(Math.round(10*i[a].resolution/i[a+1].resolution)/10);return r.some((e=>e!==e[0]))}_computeFetchConcurrency(){const{blockBoundary:e}=this.layer.rasterInfo.storageInfo,t=e[e.length-1];return(t.maxCol-t.minCol+1)*(t.maxRow-t.minRow+1)>64?2:10}async _enqueueTileFetch(e,t){this.updatingHandles.addPromise(this._enqueueTileFetch1(e,t))}async _enqueueTileFetch1(e,t){if(!this._fetchQueue.has(e.key.id)){try{const t=await this._fetchQueue.push(e.key),r=this._getBandIds();let a=!this.useProgressiveUpdate||this.layerView.hasTilingEffects&&!this._globalSymbolizerParams;if(this._globalUpdateRequested&&!this.layerView.moving&&0===this._fetchQueue.length){a=!1;try{await this._redrawImage(this._abortController?.signal)}catch(s){o(s)&&i.getLogger(this.declaredClass).error(s)}this._globalUpdateRequested=!1}!this.canUseWebGLForProcessing()&&\"rasterVF\"!==this.type||this.layerView.hasTilingEffects||null!=this._symbolizerParams||this._updateSymbolizerParams();const l=this._tileInfoView.getTileCoords(T,e.key),n=this._tileInfoView.getTileResolution(e.key);await this.updateTileSource(e,{source:t,symbolizerParams:this._symbolizerParams,globalSymbolizerParams:this._globalSymbolizerParams,suspended:a,bandIds:r,coords:l,resolution:n}),e.once(\"attach\",(()=>this.layerView.requestUpdate())),this.container.addChild(e)}catch(s){o(s)||i.getLogger(this.declaredClass).error(s)}this.layerView.requestUpdate()}}async _redrawImage(e){if(0===this.container.children.length)return;await this.layer.updateRenderer(),this.layerView.hasTilingEffects?await this._updateGlobalSymbolizerParams(e):(this._updateSymbolizerParams(),this._globalSymbolizerParams=null);const t=this.container.children.map((async e=>this.updateTileSymbolizerParameters(e,{local:this._symbolizerParams,global:this._globalSymbolizerParams})));await l(t),this.container.requestRender()}async _updateGlobalSymbolizerParams(e){const t={srcResolution:this._srcResolutions[this.previousLOD.level],registryId:this._blockCacheRegistryId,signal:e},i=await this.layer.fetchPixels(this.layerView.view.extent,this.layerView.view.width,this.layerView.view.height,t);if(!i||!i.pixelBlock)return;const{resolution:s}=this.previousLOD,r=this._getBandIds(),a=this.layer.symbolizer.generateWebGLParameters({pixelBlock:_(i.pixelBlock,r),isGCS:this.layerView.view.spatialReference.isGeographic,resolution:{x:s,y:s},bandIds:r});!this.canUseWebGLForProcessing()&&a&&\"stretch\"===a.type&&this.layer.renderer&&\"raster-stretch\"===this.layer.renderer.type&&(a.factor=a.factor.map((e=>255*e)),a.outMin=Math.round(255*a.outMin),a.outMax=Math.round(255*a.outMax)),this._globalSymbolizerParams=a}_updateSymbolizerParams(){const{resolution:e}=this.previousLOD,t=this._getBandIds();this._symbolizerParams=this.layer.symbolizer.generateWebGLParameters({pixelBlock:null,isGCS:this.layerView.view.spatialReference.isGeographic,resolution:{x:e,y:e},bandIds:t})}_updateBlockCacheRegistry(e=!1){const{layer:t,layerView:i}=this,{url:s,raster:r}=t,{multidimensionalDefinition:a}=t.normalizeRasterFetchOptions({multidimensionalDefinition:t.multidimensionalDefinition,timeExtent:i.timeExtent}),o=r.rasterInfo.multidimensionalInfo?r.getSliceIndex(a):null,l=m(s,o);if(l!==this._blockCacheRegistryUrl){if(null!=this._blockCacheRegistryUrl&&g(this._blockCacheRegistryUrl,this._blockCacheRegistryId),this._blockCacheRegistryId=y(l,r.rasterInfo),e){const{view:e}=i,t=this._tileInfoView.getClosestInfoForScale(e.scale),s=this._srcResolutions[t.level];d(l,this._blockCacheRegistryId,e.extent,e.resolution,s,r.ioConfig.sampling)}this._blockCacheRegistryUrl=l}}async doRefresh(){if(!this.attached)return;await this.layer.updateRenderer(),this.layerView.hasTilingEffects||this._updateSymbolizerParams(),this._updateBlockCacheRegistry(!0),this._fetchQueue.reset();const e=[];this._globalUpdateRequested=this.layerView.hasTilingEffects||!this.useProgressiveUpdate,this._tileStrategy.tiles.forEach((t=>e.push(this._enqueueTileFetch(t)))),await l(e)}};e([n()],v.prototype,\"_fetchQueue\",void 0),e([n()],v.prototype,\"_globalUpdateRequested\",void 0),e([n()],v.prototype,\"attached\",void 0),e([n()],v.prototype,\"container\",void 0),e([n()],v.prototype,\"layer\",void 0),e([n()],v.prototype,\"layerView\",void 0),e([n()],v.prototype,\"type\",void 0),e([n()],v.prototype,\"useWebGLForProcessing\",null),e([n()],v.prototype,\"useProgressiveUpdate\",null),e([n()],v.prototype,\"timeExtent\",void 0),e([n()],v.prototype,\"updating\",null),v=e([h(\"esri.views.2d.layers.imagery.BaseImageryTileSubView2D\")],v);export{v as BaseImageryTileSubView2D};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import{isSome as r}from\"../../../../core/maybe.js\";import{property as t}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as s}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{RasterTileContainer as i}from\"../../engine/imagery/RasterTileContainer.js\";import{BaseImageryTileSubView2D as o}from\"./BaseImageryTileSubView2D.js\";import{canUseMajorityInterpolationOnDataSource as a}from\"../support/util.js\";let n=class extends o{constructor(){super(...arguments),this.type=\"raster\"}attach(){super.attach(),this.container=new i(this._tileInfoView),this.container.isCustomTilingScheme=this._isCustomTilingScheme,this.updateRasterFunctionParameters()}detach(){super.detach(),this.container.removeAllChildren(),this.container=null}canUseWebGLForProcessing(){return this.useWebGLForProcessing&&this.layer.symbolizer.canRenderInWebGL&&!(\"majority\"===this.layer.interpolation&&a(this.layer))}fetchTile(e,r){return this.layer.fetchTile(e.level,e.row,e.col,r)}updateRasterFunctionParameters(){const{raster:e,type:r}=this.layer,{container:t}=this;if(\"Function\"!==e.datasetFormat||\"wcs\"===r)return t.rasterFunctionChain=null,t.children.forEach((e=>{const{bitmap:r}=e;r&&(r.suspended=!0,r.processed=!1,r.projected&&(r.invalidateTexture(),r.rasterTexture=null))})),void(this._rasterFunctionState=\"na\");const s=this._rasterFunctionState,{rasterFunction:i,primaryRasters:o}=e,a=i.supportsGPU&&(!o||o.rasters.length<=1),n=a?i.getFlatWebGLFunctionChain():null,{renderer:l}=this.layer,c=!a||!n?.functions.length||\"raster-stretch\"===l.type&&l.dynamicRangeAdjustment||!this.canUseWebGLForProcessing();t.rasterFunctionChain=c?null:n;const u=null==i?\"na\":t.rasterFunctionChain?\"gpu\":\"cpu\";t.children.forEach((e=>{const{bitmap:r}=e;r&&(r.suspended=s!==u,r.processed=!1,r.processedTexture=null)})),this._rasterFunctionState=u}async updateTileSource(e,t){const s=this._getBandIds(),i=this._getLayerInterpolation(),o=this.canUseWebGLForProcessing(),{source:a,globalSymbolizerParams:n,suspended:l,coords:c,resolution:u}=t,p=this.layerView.hasTilingEffects?n:t.symbolizerParams,{bitmap:d}=e;if([d.x,d.y]=c,d.resolution=u,a&&r(a)&&r(a.pixelBlock)){const e={extent:a.extent,pixelBlock:a.pixelBlock};if(d.rawPixelData=e,o)d.source=a.pixelBlock,d.isRendereredSource=!1;else{const r=await this.layer.applyRenderer(e,\"stretch\"===n?.type?n:void 0);d.source=r,d.isRendereredSource=!0}d.symbolizerParameters=o?p:null,o?d.transformGrid||(d.transformGrid=a.transformGrid):d.transformGrid=null}else{const e=this.createEmptyTilePixelBlock();d.source=e,d.symbolizerParameters=o?p:null,d.transformGrid=null}d.bandIds=o?s:null,d.width=this._tileInfoView.tileInfo.size[0],d.height=this._tileInfoView.tileInfo.size[1],d.interpolation=i,d.suspended=l,d.invalidateTexture()}async updateTileSymbolizerParameters(e,t){const{local:s,global:i}=t,o=this._getBandIds(),a=this._getLayerInterpolation(),n=this.canUseWebGLForProcessing(),{bitmap:l}=e,{rawPixelData:c}=l;!n&&r(c)?(l.source=await this.layer.applyRenderer(c,\"stretch\"===i?.type?i:void 0),l.isRendereredSource=!0):(l.isRendereredSource&&r(c)&&(l.source=c.pixelBlock),l.isRendereredSource=!1),l.symbolizerParameters=n?this.layerView.hasTilingEffects?i:s:null,l.bandIds=n?o:null,l.interpolation=a,l.suspended=!1}_getLayerInterpolation(){const e=this.layer.renderer.type;if(\"raster-colormap\"===e||\"unique-value\"===e||\"class-breaks\"===e)return\"nearest\";const{interpolation:r}=this.layer,{renderer:t}=this.layer;return\"raster-stretch\"===t.type&&null!=t.colorRamp?\"bilinear\"===r||\"cubic\"===r?\"bilinear\":\"nearest\":r}};e([t()],n.prototype,\"container\",void 0),e([t()],n.prototype,\"layer\",void 0),e([t()],n.prototype,\"type\",void 0),n=e([s(\"esri.views.2d.layers.imagery.ImageryTileView2D\")],n);const l=n;export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{s as t,m as s}from\"../../../../chunks/mat3.js\";import{c as e}from\"../../../../chunks/mat3f32.js\";import{RasterVFDisplayObject as i}from\"./RasterVFDisplayObject.js\";import{TiledDisplayObject as a}from\"../webgl/TiledDisplayObject.js\";class r extends a{constructor(t,s,e,a,r,o,l=null){super(t,s,e,a,r,o),this.tileData=new i(l),this.tileData.coordScale=[r,o],this.tileData.once(\"isReady\",(()=>this.ready()))}destroy(){super.destroy(),this.tileData.destroy(),this.tileData=null,this.stage=null}set stencilRef(t){this.tileData.stencilRef=t}get stencilRef(){return this.tileData.stencilRef}_createTransforms(){return{dvs:e(),tileMat3:e()}}setTransform(e){super.setTransform(e);const i=this.resolution/(e.resolution*e.pixelRatio),a=this.transforms.tileMat3,[r,o]=this.tileData.offset,l=[this.x+r*this.resolution,this.y-o*this.resolution],[h,n]=e.toScreenNoRotation([0,0],l),{symbolTileSize:f}=this.tileData.symbolizerParameters,m=Math.round((this.width-this.tileData.offset[0])/f)*f,c=Math.round((this.height-this.tileData.offset[1])/f)*f,u=m/this.rangeX*i,D=c/this.rangeY*i;t(a,u,0,0,0,D,0,h,n,1),s(this.transforms.dvs,e.displayViewMat3,a),this.tileData.transforms.dvs=this.transforms.dvs}onAttach(){this.tileData.stage=this.stage}onDetach(){this.tileData.stage=null}}export{r as RasterVFTile};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{create as e}from\"../../../../geometry/support/aaBoundingRect.js\";import s from\"./BrushVectorField.js\";import{RasterVFTile as r}from\"./RasterVFTile.js\";import{WGLDrawPhase as t}from\"../webgl/enums.js\";import i from\"../webgl/TileContainer.js\";class o extends i{constructor(){super(...arguments),this.isCustomTilingScheme=!1,this.symbolTypes=[\"triangle\"]}createTile(s){const t=this._tileInfoView.getTileBounds(e(),s),[i,o]=this._tileInfoView.tileInfo.size,n=this._tileInfoView.getTileResolution(s.level);return new r(s,n,t[0],t[3],i,o)}prepareRenderPasses(e){const r=e.registerRenderPass({name:\"imagery (vf tile)\",brushes:[s],target:()=>this.children.map((e=>e.tileData)),drawPhase:t.MAP});return[...super.prepareRenderPasses(e),r]}doRender(e){this.visible&&e.drawPhase===t.MAP&&this.symbolTypes.forEach((s=>{e.renderPass=s,super.doRender(e)}))}}export{o as RasterVFTileContainer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import{isSome as t}from\"../../../../core/maybe.js\";import{watch as i}from\"../../../../core/reactiveUtils.js\";import{property as r}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as o}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{sampleVectorField as s}from\"../../../../layers/support/rasterFunctions/vectorFieldUtils.js\";import{RasterVFTileContainer as l}from\"../../engine/imagery/RasterVFTileContainer.js\";import{BaseImageryTileSubView2D as a}from\"./BaseImageryTileSubView2D.js\";let c=class extends a{constructor(){super(...arguments),this._handle=null,this.type=\"rasterVF\"}canUseWebGLForProcessing(){return!1}async fetchTile(e,t){t={...t,interpolation:\"nearest\",requestProjectedLocalDirections:!0};const i=await this.layer.fetchTile(e.level,e.row,e.col,t);return\"vector-magdir\"===this.layer.rasterInfo.dataType&&i?.pixelBlock&&(i.pixelBlock=await this.layer.convertVectorFieldData(i.pixelBlock,t)),i}updateTileSource(e,i){const r=i.symbolizerParams,{tileData:o}=e;o.key=e.key,o.width=this._tileInfoView.tileInfo.size[0],o.height=this._tileInfoView.tileInfo.size[1];const{symbolTileSize:s}=r,{source:l}=i;if(o.offset=this._getTileSymbolOffset(o.key,s),t(l)&&t(l.pixelBlock)){const e={extent:l.extent,pixelBlock:l.pixelBlock};o.rawPixelData=e,o.symbolizerParameters=r,o.source=this._sampleVectorFieldData(l.pixelBlock,r,o.offset)}else{const e=[Math.round((this._tileInfoView.tileInfo[0]-o.offset[0])/s),Math.round((this._tileInfoView.tileInfo[1]-o.offset[1])/s)],t=this.createEmptyTilePixelBlock(e);o.source=t,o.symbolizerParameters=r}return o.invalidateVAO(),Promise.resolve()}updateTileSymbolizerParameters(e,i){const r=i.local,{symbolTileSize:o}=r,{tileData:s}=e;s.offset=this._getTileSymbolOffset(s.key,o);const l=s.symbolizerParameters.symbolTileSize;s.symbolizerParameters=r;const a=s.rawPixelData?.pixelBlock;return t(a)&&l!==o&&(s.source=this._sampleVectorFieldData(a,s.symbolizerParameters,s.offset)),Promise.resolve()}attach(){super.attach(),this.container=new l(this._tileInfoView),this.container.isCustomTilingScheme=this._isCustomTilingScheme,this._updateSymbolType(this.layer.renderer),this._handle=i((()=>this.layer.renderer),(e=>this._updateSymbolType(e)))}detach(){super.detach(),this.container.removeAllChildren(),this._handle?.remove(),this._handle=null,this.container=null}_getTileSymbolOffset(e,t){const i=e.col*this._tileInfoView.tileInfo.size[0]%t,r=e.row*this._tileInfoView.tileInfo.size[1]%t;return[i>t/2?t-i:-i,r>t/2?t-r:-r]}_sampleVectorFieldData(e,t,i){const{symbolTileSize:r}=t;return s(e,\"vector-uv\",r,i)}_updateSymbolType(e){\"vector-field\"===e.type&&(this.container.symbolTypes=\"wind-barb\"===e.style?[\"scalar\",\"triangle\"]:\"simple-scalar\"===e.style?[\"scalar\"]:[\"triangle\"])}};e([r()],c.prototype,\"container\",void 0),e([r()],c.prototype,\"layer\",void 0),e([r()],c.prototype,\"type\",void 0),c=e([o(\"esri.views.2d.layers.imagery.VectorFieldTileView2D\")],c);const n=c;export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import t from\"../../Graphic.js\";import r from\"../../core/Error.js\";import{unwrap as o,isNone as s,isSome as i}from\"../../core/maybe.js\";import{property as a}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as n}from\"../../core/accessorSupport/decorators/subclass.js\";import{combinedViewLayerTimeExtentProperty as l}from\"../../layers/support/commonProperties.js\";import{getDefaultDatumTransformationForDataset as p,projectExtent as u}from\"../../layers/support/rasterFunctions/rasterProjectionHelper.js\";import{getFetchPopupTemplate as c}from\"./support/popupUtils.js\";const f=f=>{let m=class extends f{constructor(){super(...arguments),this._rasterFieldPrefix=\"Raster.\",this.layer=null,this.view=null,this.tileInfo=null}get fullExtent(){return this._getfullExtent()}_getfullExtent(){return this.projectFullExtent(this.view.spatialReference)}get hasTilingEffects(){return this.layer.renderer&&\"dynamicRangeAdjustment\"in this.layer.renderer&&this.layer.renderer.dynamicRangeAdjustment}get datumTransformation(){return p(o(this.layer.fullExtent),this.view.spatialReference,!0)}supportsSpatialReference(e){return!!this.projectFullExtent(e)}projectFullExtent(e){const t=o(this.layer.fullExtent),r=p(t,e,!1);return u(t,e,r)}async fetchPopupFeatures(e,o){const{layer:a}=this;if(!e)throw new r(\"imageryTileLayerView:fetchPopupFeatures\",\"Nothing to fetch without area\",{layer:a});const{popupEnabled:n}=a,l=c(a,o);if(!n||s(l))throw new r(\"imageryTileLayerView:fetchPopupFeatures\",\"Missing required popupTemplate or popupEnabled\",{popupEnabled:n,popupTemplate:l});const p=[],{value:u,magdirValue:f}=await a.identify(e,{timeExtent:this.timeExtent});let m=\"\";if(u&&u.length){m=\"imagery-tile\"===a.type&&a.hasStandardTime()&&null!=u[0]?u.map((e=>a.getStandardTimeValue(e))).join(\", \"):u.join(\", \");const e={ObjectId:0};e[\"Raster.ServicePixelValue\"]=m;const r=a.rasterInfo.attributeTable;if(i(r)){const{fields:t,features:o}=r,s=t.find((({name:e})=>\"value\"===e.toLowerCase())),i=s?o.find((e=>String(e.attributes[s.name])===m)):null;if(i)for(const r in i.attributes)if(i.attributes.hasOwnProperty(r)){e[this._rasterFieldPrefix+r]=i.attributes[r]}}const o=a.rasterInfo.dataType;\"vector-magdir\"!==o&&\"vector-uv\"!==o||(e[\"Raster.Magnitude\"]=f?.[0],e[\"Raster.Direction\"]=f?.[1]);const s=new t(this.fullExtent.clone(),null,e);s.layer=a,s.sourceLayer=s.layer,p.push(s)}return p}};return e([a()],m.prototype,\"layer\",void 0),e([a(l)],m.prototype,\"timeExtent\",void 0),e([a()],m.prototype,\"view\",void 0),e([a()],m.prototype,\"fullExtent\",null),e([a()],m.prototype,\"tileInfo\",void 0),e([a({readOnly:!0})],m.prototype,\"hasTilingEffects\",null),e([a()],m.prototype,\"datumTransformation\",null),m=e([n(\"esri.views.layers.ImageryTileLayerView\")],m),m};export{f as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import s from\"../../../Graphic.js\";import t from\"../../../core/Logger.js\";import{isSome as i}from\"../../../core/maybe.js\";import{isAbortError as r}from\"../../../core/promiseUtils.js\";import{watch as o,syncAndInitial as a,sync as n,initial as u}from\"../../../core/reactiveUtils.js\";import{property as h}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as l}from\"../../../core/accessorSupport/decorators/subclass.js\";import{hasExcludedVariableOrDimension as c}from\"../../../layers/support/rasterDatasets/multidimensionalUtils.js\";import d from\"../engine/flow/FlowView2D.js\";import{LayerView2DMixin as p}from\"./LayerView2D.js\";import m from\"./imagery/ImageryTileView2D.js\";import b from\"./imagery/VectorFieldTileView2D.js\";import{canUseMajorityInterpolationOnDataSource as v}from\"./support/util.js\";import w from\"../../layers/ImageryTileLayerView.js\";import y from\"../../layers/LayerView.js\";import g from\"../../layers/RefreshableLayerView.js\";let f=class extends(w(g(p(y)))){constructor(){super(...arguments),this._useWebGLForProcessing=!0,this._useProgressiveUpdate=!0,this.subview=null}get useWebGLForProcessing(){return this._useWebGLForProcessing}set useWebGLForProcessing(e){this._useWebGLForProcessing=e,this.subview&&\"useWebGLForProcessing\"in this.subview&&(this.subview.useWebGLForProcessing=e)}get useProgressiveUpdate(){return this._useWebGLForProcessing}set useProgressiveUpdate(e){this._useProgressiveUpdate=e,this.subview&&\"useProgressiveUpdate\"in this.subview&&(this.subview.useProgressiveUpdate=e)}update(e){this.subview?.update(e),this.notifyChange(\"updating\")}isUpdating(){return!this.subview||this.subview.updating}attach(){this.layer.increaseRasterJobHandlerUsage(),this._updateSubview(),this.addAttachHandles([o((()=>{const{layer:e}=this;return{bandIds:e.bandIds,renderer:e.renderer,interpolation:e.interpolation,multidimensionalDefinition:e.multidimensionalDefinition,rasterFunction:\"imagery-tile\"===e.type?e.rasterFunction:null}}),((e,s)=>{const i=e.interpolation!==s?.interpolation&&(\"majority\"===e.interpolation||\"majority\"===s?.interpolation)&&v(this.layer),o=e.renderer!==s?.renderer&&s?.renderer?.type!==e.renderer?.type;o&&this._updateSubview();const a=e.multidimensionalDefinition!==s?.multidimensionalDefinition,n=e.rasterFunction!==s?.rasterFunction,u=n&&!this._useWebGLForProcessing,h=a||i||o||u;this.subview.redrawOrRefetch({refetch:h,reprocess:n}).catch((e=>{r(e)||t.getLogger(this.declaredClass).error(e)})),this.notifyChange(\"updating\")})),o((()=>this.layer.blendMode??\"normal\"),(e=>{this.subview.container.blendMode=e}),a),o((()=>this.layer.effect??null),(e=>{this.subview.container.effect=e}),a),o((()=>this.layer.multidimensionalSubset??null),((e,s)=>{const{multidimensionalDefinition:o}=this.layer;i(o)&&c(o,e)!==c(o,s)&&(this.subview.redrawOrRefetch({refetch:!0}).catch((e=>{r(e)||t.getLogger(this.declaredClass).error(e)})),this.notifyChange(\"updating\"))}),n),o((()=>this.timeExtent),(()=>{this.subview.timeExtent=this.timeExtent,this.subview.redrawOrRefetch({refetch:!0}).catch((e=>{r(e)||t.getLogger(this.declaredClass).error(e)}))}),u)])}detach(){this.layer.decreaseRasterJobHandlerUsage(),this._detachSubview(this.subview),this.subview?.destroy(),this.subview=null}moveStart(){this.requestUpdate()}viewChange(){this.requestUpdate()}moveEnd(){this.subview.moveEnd()}async hitTest(e,t){return[{type:\"graphic\",layer:this.layer,mapPoint:e,graphic:new s({attributes:{},geometry:e.clone()})}]}doRefresh(){return this.subview?this.subview.doRefresh():Promise.resolve()}_updateSubview(){const e=\"vector-field\"===this.layer.renderer.type?\"rasterVF\":\"flow\"===this.layer.renderer.type?\"flow\":\"raster\";if(this.subview){if(this.subview.type===e)return void this._attachSubview(this.subview);this._detachSubview(this.subview),this.subview?.destroy(),this.subview=null}const{layer:s}=this;let t;if(t=\"rasterVF\"===e?new b({layer:s,layerView:this}):\"flow\"===e?new d({layer:s,layerView:this}):new m({layer:s,layerView:this}),\"useWebGLForProcessing\"in t&&(t.useWebGLForProcessing=this._useWebGLForProcessing),\"useProgressiveUpdate\"in t&&(t.useProgressiveUpdate=this._useProgressiveUpdate),\"previousLOD\"in t){const{subview:e}=this;t.previousLOD=e&&\"previousLOD\"in e?e.previousLOD:null}this._attachSubview(t),this.subview=t,this.requestUpdate()}_attachSubview(e){e&&!e.attached&&(e.attach(),e.attached=!0,this.container.addChildAt(e.container,0),e.container.blendMode=this.layer.blendMode,e.container.effect=this.layer.effect)}_detachSubview(e){e?.attached&&(this.container.removeChild(e.container),e.detach(),e.attached=!1)}};e([h()],f.prototype,\"subview\",void 0),e([h()],f.prototype,\"useWebGLForProcessing\",null),e([h()],f.prototype,\"useProgressiveUpdate\",null),f=e([l(\"esri.views.2d.layers.ImageryTileLayerView2D\")],f);const L=f;export{L as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIyjB,IAAMA,KAAE,EAAC,WAAU,GAAE,QAAO,GAAE,QAAO,GAAE,WAAU,CAAC,GAAE,GAAE,CAAC,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,QAAO,CAAC,IAAE,KAAI,IAAE,KAAI,IAAE,GAAG,GAAE,UAAS,OAAG,OAAM,CAAC,GAAE,GAAE,CAAC,GAAE,iBAAgB,CAAC,GAAE,GAAE,CAAC,GAAE,UAAS,MAAK,gBAAe,MAAK,aAAY,QAAO,MAAK,UAAS;AAAE,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,MAAE,MAAKC,KAAE,MAAKC,MAAE,MAAK;AAAC,UAAM,GAAE,KAAK,sBAAoB,MAAG,KAAK,8BAA4B,MAAG,KAAK,iBAAe,MAAK,KAAK,wBAAsB,MAAK,KAAK,wBAAsB,MAAK,KAAK,mBAAiB,MAAK,KAAK,YAAU,MAAK,KAAK,2BAAyB,MAAG,KAAK,oBAAkB,MAAK,KAAK,mBAAiB,CAAC,GAAE,KAAK,YAAU,OAAG,KAAK,aAAW,GAAE,KAAK,aAAW,CAAC,GAAE,CAAC,GAAE,KAAK,aAAW,OAAG,KAAK,wBAAsB,MAAK,KAAK,SAAO,MAAK,KAAK,qBAAmB,OAAG,KAAK,aAAW,GAAE,KAAK,aAAW,GAAE,KAAK,WAAS,GAAE,KAAK,UAAQ,MAAK,KAAK,eAAa,MAAK,KAAK,aAAW,OAAG,KAAK,WAAS,MAAK,KAAK,iBAAe,MAAK,KAAK,iBAAe,MAAK,KAAK,QAAM,MAAK,KAAK,IAAE,GAAE,KAAK,IAAE,GAAE,KAAK,SAAOF,KAAE,KAAK,gBAAcC,IAAE,KAAK,gBAAcC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK;AAAA,EAAiB;AAAA,EAAC,IAAI,iBAAiBF,KAAE;AAAC,SAAK,sBAAoBA,QAAI,KAAK,iBAAiB,IAAE,GAAE,KAAK,oBAAkBA;AAAA,EAAE;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,KAAK;AAAA,EAAc;AAAA,EAAC,IAAI,cAAcA,KAAE;AAJjyD;AAIkyD,SAAK,mBAAiBA,SAAI,UAAK,mBAAL,mBAAqB,WAAU,KAAK,iBAAeA;AAAA,EAAE;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,UAAUC,IAAE;AAAC,SAAK,aAAWA,IAAEA,OAAI,EAAE,KAAK,gBAAgB,GAAE,KAAK,kBAAkB;AAAA,EAAE;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,KAAK,yBAAuBJ;AAAA,EAAC;AAAA,EAAC,IAAI,qBAAqBG,KAAE;AAAC,SAAK,0BAAwBA,QAAI,KAAK,wBAAsBA,KAAE,KAAK,8BAA4B,MAAG,KAAK,iBAAe;AAAA,EAAK;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,IAAI,OAAOA,KAAE;AAAC,SAAK,YAAUA,QAAI,KAAK,UAAQA,KAAE,KAAK,mBAAiB,KAAK,eAAe,QAAQ,GAAE,KAAK,iBAAe,MAAK,KAAK,wBAAsB,OAAM,KAAK,YAAU,OAAG,KAAK,kBAAkB;AAAA,EAAE;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,UAAUA,KAAE;AAAC,SAAK,cAAY,CAACA,OAAG,KAAK,UAAQ,KAAK,MAAM,GAAE,KAAK,cAAc,IAAG,KAAK,aAAWA;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,QAAQA,KAAE;AAAC,SAAK,WAASA,KAAE,KAAK,kBAAkBA,GAAC,MAAI,KAAK,YAAU,OAAG,KAAK,kBAAkB;AAAA,EAAE;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,KAAK,kBAAgB;AAAA,EAAS;AAAA,EAAC,IAAI,cAAcA,KAAE;AAAC,SAAK,iBAAeA,KAAE,KAAK,kBAAgB,KAAK,eAAe,gBAAgB,eAAa,KAAK,0BAA0BA,OAAG,SAAS,IAAE,EAAE,SAAO,EAAE,OAAO;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,KAAK;AAAA,EAAc;AAAA,EAAC,IAAI,cAAcC,IAAE;AAAC,SAAK,iBAAeA,IAAE,KAAK,wBAAsB,EAAE,KAAK,qBAAqB;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,SAAK,wBAAsB,KAAK,sBAAoB,MAAG,KAAK,cAAc;AAAA,EAAE;AAAA,EAAC,oBAAmB;AAAC,WAAM,EAAC,KAAID,GAAE,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,KAAE;AAAC,UAAMC,KAAEF,GAAE,KAAK,WAAW,GAAG,GAAE,CAACI,IAAEC,GAAC,IAAEJ,IAAE,mBAAmB,CAAC,GAAE,CAAC,GAAE,CAAC,KAAK,GAAE,KAAK,CAAC,CAAC,GAAEK,KAAE,KAAK,aAAW,KAAK,aAAWL,IAAE,YAAWM,KAAED,KAAE,KAAK,OAAME,KAAEF,KAAE,KAAK,QAAOG,KAAE,KAAK,KAAG,KAAK,WAAS;AAAI,IAAAC,GAAER,IAAEA,IAAEA,GAAEE,IAAEC,GAAC,CAAC,GAAEK,GAAER,IAAEA,IAAEA,GAAEK,KAAE,GAAEC,KAAE,CAAC,CAAC,GAAEG,GAAET,IAAEA,IAAE,CAACO,EAAC,GAAEC,GAAER,IAAEA,IAAEA,GAAE,CAACK,KAAE,GAAE,CAACC,KAAE,CAAC,CAAC,GAAE,EAAEN,IAAEA,IAAEA,GAAEK,IAAEC,EAAC,CAAC,GAAE,EAAE,KAAK,WAAW,KAAIP,IAAE,iBAAgBC,EAAC;AAAA,EAAC;AAAA,EAAC,YAAY,EAAC,eAAcD,MAAE,OAAG,qBAAoBC,KAAE,MAAE,IAAE,CAAC,GAAE;AAAC,UAAMC,MAAED,KAAE,KAAK,qBAAmB,KAAK,iBAAe,KAAK,gBAAeF,MAAE,CAAC,GAAEY,MAAE,CAAC;AAAE,WAAOT,MAAED,MAAGU,IAAE,KAAKT,GAAC,GAAEH,IAAE,KAAK,SAAS,GAAE,KAAK,qBAAmBY,IAAE,KAAK,KAAK,gBAAgB,GAAEZ,IAAE,KAAK,YAAY,IAAG,EAAC,OAAMA,KAAE,UAASY,IAAC,MAAI,KAAK,0BAAwBA,IAAE,KAAK,KAAK,qBAAqB,GAAEZ,IAAE,KAAK,iBAAiB,IAAGY,IAAE,KAAKT,GAAC,GAAEH,IAAE,KAAK,SAAS,GAAE,KAAK,oBAAkB,CAACC,QAAIW,IAAE,KAAK,KAAK,gBAAgB,GAAEZ,IAAE,KAAK,YAAY,IAAG,EAAC,OAAMA,KAAE,UAASY,IAAC,KAAG,EAAC,OAAMZ,KAAE,UAASY,IAAC;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,SAAK,kBAAkB;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,SAAK,kBAAkB;AAAA,EAAC;AAAA,EAAC,cAAc,EAAC,SAAQX,IAAC,GAAE;AAAC,QAAG,CAAC,KAAK,MAAM,QAAO,KAAK,KAAK,iBAAiB;AAAE,UAAMC,KAAE,KAAK,eAAe,KAAK,MAAM;AAAE,IAAAA,MAAG,KAAK,gCAA8B,KAAK,8BAA4B,OAAG,KAAK,uBAAuBD,GAAC,IAAG,KAAK,wBAAsB,KAAK,sBAAoB,OAAG,KAAK,8BAA8BA,GAAC,GAAE,KAAK,mBAAiBC,KAAE,KAAK,iBAAe,CAAC,KAAK,0BAAwB,KAAK,wBAAsBI,GAAEL,KAAE,KAAK,aAAa,KAAG,KAAK,eAAe,QAAQ,IAAI,IAAG,KAAK,cAAY,KAAK,MAAM,GAAE,KAAK,cAAc;AAAA,EAAG;AAAA,EAAC,yBAAwB;AAAC,UAAK,EAAC,kBAAiBA,IAAC,IAAE;AAAK,UAAIA,IAAE,WAAS,KAAK,mBAAiBA,IAAE,MAAM,GAAEA,IAAE,QAAS,CAAAA,QAAGA,OAAA,gBAAAA,IAAG,SAAU,GAAEA,IAAE,SAAO;AAAA,EAAE;AAAA,EAAC,8BAA8BA,KAAE;AAJhzJ;AAIizJ,UAAME,MAAE,EAAE,KAAK,MAAM,IAAEQ,GAAE,KAAK,QAAO,KAAK,OAAO,IAAE;AAAK,QAAG,CAAC,KAAK,eAAeR,GAAC,EAAE,QAAO,MAAK,KAAK,mBAAiB,KAAK,eAAe,QAAQ,GAAE,KAAK,wBAAsB,MAAK,KAAK,iBAAe;AAAO,UAAMH,MAAE,CAAC,KAAK,kBAAkB,KAAK,OAAO;AAAE,QAAG,KAAK,gBAAe;AAAC,UAAGA,IAAE;AAAO,WAAK,eAAe,QAAQ,GAAE,KAAK,wBAAsB,MAAK,KAAK,iBAAe;AAAA,IAAI;AAAC,SAAK,2BAAyB,CAAC,GAAC,KAAAC,IAAE,aAAa,iBAAf,mBAA6B;AAAmB,UAAMW,MAAE,KAAK,0BAA0B,KAAK,aAAa,GAAEC,MAAE,KAAK,sBAAoB,GAAC,KAAAZ,IAAE,aAAa,iBAAf,mBAA6B;AAAa,SAAK,iBAAe,EAAEA,KAAEE,KAAES,KAAEC,GAAC,GAAE,KAAK,YAAU,OAAG,KAAK,aAAW,OAAG,KAAK,wBAAsB,KAAK,UAAQ,CAAC,GAAG,KAAK,OAAO,IAAE;AAAA,EAAI;AAAA,EAAC,kBAAkBZ,KAAE;AAAC,UAAMC,KAAE,KAAK;AAAsB,WAAM,EAAE,QAAMA,MAAG,QAAMD,OAAGC,MAAGD,OAAGC,GAAE,KAAK,EAAE,MAAID,IAAE,KAAK,EAAE;AAAA,EAAE;AAAA,EAAC,eAAeA,KAAE;AAJ9nL;AAI+nL,WAAO,EAAEA,GAAC,OAAG,KAAAA,IAAE,WAAF,mBAAU,UAAO;AAAA,EAAC;AAAA,EAAC,0BAA0BA,KAAE;AAAC,UAAK,EAAC,MAAKE,KAAE,UAASH,IAAC,IAAE,KAAK,sBAAqBY,MAAE,UAAQT,OAAG,cAAYA,OAAG,EAAEH,GAAC;AAAE,WAAM,CAAC,KAAK,4BAA0BY,OAAG,eAAaX,OAAG,YAAUA,MAAE,YAAU;AAAA,EAAU;AAAA,EAAC,uBAAuBA,KAAE;AAAC,UAAMC,KAAE,KAAK,WAAUC,MAAE,KAAK,qBAAqB;AAAS,WAAOA,MAAED,KAAEC,IAAE,WAASD,GAAE,UAAQC,IAAE,KAAM,CAACF,KAAEE,QAAIF,QAAIC,GAAEC,GAAC,CAAE,KAAG,KAAK,qBAAmB,KAAK,iBAAiB,QAAQ,GAAE,KAAK,mBAAiB,OAAM,KAAK,mBAAiB,EAAEF,KAAEE,GAAC,GAAE,MAAK,KAAK,YAAUA,QAAI,UAAQ,KAAK,mBAAiB,EAAEF,KAAEE,GAAC,GAAE,MAAK,KAAK,YAAUA,SAAK,KAAK,qBAAmB,KAAK,iBAAiB,QAAQ,GAAE,KAAK,mBAAiB,OAAM,MAAK,KAAK,YAAU;AAAA,EAAM;AAAA,EAAC,iBAAiBF,MAAE,OAAG;AAAC,SAAK,0BAAwB,KAAK,sBAAsB,QAAQ,GAAE,KAAK,wBAAsB,OAAM,CAACA,OAAG,KAAK,qBAAmB,KAAK,iBAAiB,QAAQ,GAAE,KAAK,mBAAiB,MAAK,KAAK,YAAU,MAAK,KAAK,8BAA4B,OAAI,CAACA,OAAG,KAAK,mBAAiB,KAAK,eAAe,QAAQ,GAAE,KAAK,iBAAe,MAAK,KAAK,wBAAsB,OAAM,KAAK,sBAAoB,KAAK,kBAAkB,QAAQ,GAAE,KAAK,oBAAkB;AAAA,EAAK;AAAC;AAAqD,SAASa,GAAEC,KAAE;AAAC,SAAO,EAAEA,IAAE,MAAM;AAAC;;;ACAz1N,SAASC,GAAEA,KAAE;AAAC,QAAMC,MAAE,CAAC;AAAE,SAAOD,QAAIC,IAAE,KAAK,iBAAiB,GAAE,MAAID,IAAE,QAAQ,CAAC,KAAGC,IAAE,KAAK,kBAAkB,IAAGA;AAAC;AAAC,SAASA,GAAED,KAAEC,KAAEC,KAAE;AAJ/H;AAIgI,QAAMC,KAAE,GAAC,KAAAD,IAAE,aAAa,iBAAf,mBAA6B,qBAAmBE,KAAE,CAAC;AAAE,SAAM,YAAUJ,MAAEI,GAAE,KAAK,SAAS,IAAE,eAAaJ,QAAIC,OAAGG,GAAE,KAAK,UAAU,GAAEA,GAAE,KAAK,QAAQ,KAAGD,MAAGC,GAAE,KAAK,UAAU,IAAGA;AAAC;;;ACAxJ,IAAMC,KAAE,EAAC,QAAO,iBAAgB,QAAO,cAAa,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAE,SAASC,GAAEC,IAAEC,KAAEC,KAAE;AAAC,QAAMH,MAAEG,MAAE,CAAC,IAAEC,GAAEF,IAAE,aAAa;AAAE,SAAM,EAAC,SAAQF,KAAE,SAAQC,GAAE,QAAQ,gBAAgB,WAAWF,IAAEC,GAAC,EAAC;AAAC;AAAC,SAASK,GAAEC,KAAEP,KAAEC,KAAEK,KAAEE,KAAE,OAAG;AAAC,QAAK,EAAC,OAAMH,KAAE,UAASI,GAAC,IAAER,IAAE,YAAY,EAAC,qBAAoBO,GAAC,CAAC;AAAE,EAAAE,GAAEH,IAAE,SAAQP,KAAEK,KAAEI,EAAC,GAAE,EAAET,KAAEM,KAAEL,IAAE,cAAc,GAAED,IAAE,oBAAoB,aAAYC,IAAE,WAAW,GAAG;AAAE,QAAK,EAAC,UAASU,IAAE,gBAAeC,GAAC,IAAEX,IAAE,sBAAqBS,KAAEG,GAAEF,IAAEC,EAAC;AAAE,IAAEZ,KAAEM,KAAEI,EAAC;AAAC;AAAC,IAAMF,KAAE,EAAC,eAAcP,IAAE,wBAAuBK,GAAC;;;ACA/d,IAAMQ,KAAE,EAAC,QAAO,iBAAgB,QAAO,oBAAmB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAE,SAASC,GAAEC,KAAEC,KAAEC,KAAE;AAAC,QAAK,EAAC,UAASC,IAAC,IAAEF,IAAE,sBAAqBF,KAAE,CAAC,GAAGG,MAAE,CAAC,IAAEE,GAAEH,IAAE,aAAa,GAAE,GAAGH,GAAEG,IAAE,eAAc,QAAME,KAAEH,IAAE,OAAO,CAAC;AAAE,UAAMG,OAAGJ,GAAE,KAAK,eAAe;AAAE,SAAM,EAAC,SAAQA,IAAE,SAAQC,IAAE,QAAQ,gBAAgB,WAAWF,IAAEC,EAAC,EAAC;AAAC;AAAC,SAASK,GAAEC,KAAEC,IAAER,KAAEC,IAAEK,MAAE,OAAG;AAAC,QAAK,EAAC,OAAMG,IAAE,UAASC,GAAC,IAAEV,IAAE,YAAY,EAAC,qBAAoBM,IAAC,CAAC;AAAE,EAAAK,GAAEJ,IAAE,SAAQC,IAAEC,IAAEC,EAAC,GAAE,EAAEF,IAAEP,IAAED,IAAE,cAAc,GAAEQ,GAAE,oBAAoB,aAAYR,IAAE,WAAW,GAAG;AAAE,QAAMY,KAAEZ,IAAE,sBAAqB,EAAC,UAASa,IAAE,gBAAeC,GAAC,IAAEF;AAAE,MAAG,QAAMC,IAAE;AAAC,UAAMN,MAAEQ,GAAEF,IAAEC,EAAC;AAAE,MAAEN,IAAEP,IAAEM,GAAC;AAAA,EAAC;AAAC,QAAMI,KAAE,EAAEC,EAAC;AAAE,IAAEJ,IAAEP,IAAEU,EAAC;AAAC;AAAC,IAAMF,KAAE,EAAC,eAAcR,IAAE,wBAAuBK,GAAC;;;ACAlsB,IAAMU,KAAE,EAAC,QAAO,iBAAgB,QAAO,kBAAiB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAE,SAASC,GAAEC,KAAEC,KAAEC,KAAE;AAAC,QAAK,EAAC,UAASC,IAAC,IAAEF,IAAE,sBAAqBF,KAAE,CAAC,GAAGG,MAAE,CAAC,IAAEE,GAAEH,IAAE,aAAa,GAAE,GAAGE,GAAEF,IAAE,eAAc,QAAME,KAAEH,IAAE,OAAO,CAAC;AAAE,EAAAC,IAAE,sBAAoB,CAACC,MAAEH,GAAE,KAAK,MAAM,IAAE,QAAMI,OAAGJ,GAAE,KAAK,eAAe;AAAE,SAAM,EAAC,SAAQA,IAAE,SAAQC,IAAE,QAAQ,gBAAgB,WAAWF,IAAEC,EAAC,EAAC;AAAC;AAAC,SAASK,GAAEC,KAAEC,IAAER,KAAEC,IAAEK,MAAE,OAAG;AAAC,QAAK,EAAC,OAAMG,IAAE,UAASC,GAAC,IAAEV,IAAE,YAAY,EAAC,qBAAoBM,IAAC,CAAC;AAAE,EAAAK,GAAEJ,IAAE,SAAQC,IAAEC,IAAEC,EAAC,GAAE,EAAEF,IAAEP,IAAED,IAAE,cAAc,GAAEQ,GAAE,oBAAoB,aAAYR,IAAE,WAAW,GAAG;AAAE,QAAMY,KAAEZ,IAAE,sBAAqB,EAAC,UAASa,IAAE,gBAAeC,GAAC,IAAEF;AAAE,MAAG,QAAMC,IAAE;AAAC,UAAMN,MAAEQ,GAAEF,IAAEC,EAAC;AAAE,MAAEN,IAAEP,IAAEM,GAAC;AAAA,EAAC;AAAC,QAAMI,KAAEK,GAAEJ,EAAC;AAAE,IAAEJ,IAAEP,IAAEU,EAAC;AAAC;AAAC,IAAMF,KAAE,EAAC,eAAcR,IAAE,wBAAuBK,GAAC;;;ACA91B,IAAMW,KAAE,oBAAI;AAAI,SAASC,GAAEC,IAAE;AAAC,SAAOF,GAAE,IAAIE,EAAC;AAAC;AAACF,GAAE,IAAI,OAAMG,EAAC,GAAEH,GAAE,IAAI,aAAYI,EAAC,GAAEJ,GAAE,IAAI,WAAUK,EAAC;;;ACAzH,IAAMC,KAAE,CAAC,GAAE,CAAC;AAAZ,IAAcC,KAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,CAAC;AAAE,SAASC,GAAEF,IAAEC,KAAEC,KAAE;AAAC,QAAK,EAAC,SAAQC,KAAE,gBAAeC,KAAE,aAAYC,IAAC,IAAEL,IAAE,EAAC,QAAOM,IAAC,IAAEF,IAAE,YAAWG,KAAEF,OAAEC,OAAA,gBAAAA,IAAG,OAAI,KAAG,GAAEE,KAAEN,IAAE,iBAAiBK,EAAC,KAAGL,IAAE;AAAc,EAAAO,GAAEN,KAAEF,KAAE,CAAC,SAAS,GAAE,CAACO,EAAC,CAAC;AAAC;AAAC,SAASL,GAAEH,IAAEC,KAAEE,KAAE;AAAC,QAAK,EAAC,SAAQC,IAAC,IAAEJ,GAAE,eAAe;AAAW,MAAG,CAACI,IAAE;AAAO,MAAGA,IAAE,SAAO,EAAE,QAAOF,GAAEF,IAAEC,KAAEE,GAAC;AAAE,QAAME,MAAED,IAAE,OAAQ,CAAAM,QAAG,eAAaA,IAAE,IAAK,EAAE,IAAK,CAAAA,QAAG,QAAMA,IAAE,MAAI,eAAaA,IAAE,OAAKP,IAAE,iBAAiBO,IAAE,EAAE,IAAEP,IAAE,aAAc;AAAE,MAAGM,GAAET,GAAE,SAAQC,KAAE,CAAC,WAAU,YAAW,UAAU,EAAE,MAAM,GAAEI,IAAE,MAAM,GAAEA,GAAC,GAAEA,IAAE,WAASD,IAAE;AAAO,QAAG,MAAIA,IAAE,QAAO;AAAC,YAAMM,MAAEN,IAAE,UAAW,CAAAM,QAAG,eAAaA,IAAE,IAAK,GAAEV,KAAE,MAAIU,MAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,EAAC,OAAMR,IAAC,IAAEE,IAAEM,GAAC,EAAE;AAAW,MAAAT,IAAE,aAAa,iBAAgBC,GAAC,GAAED,IAAE,oBAAoB,eAAcD,EAAC;AAAA,IAAC,WAAS,MAAII,IAAE,QAAO;AAAC,YAAMM,MAAE,CAAC;AAAE,UAAGN,IAAE,QAAS,CAACJ,IAAEC,QAAI,eAAaD,GAAE,QAAMU,IAAE,KAAKT,GAAC,CAAE,GAAE,MAAIS,IAAE,QAAO;AAAC,cAAK,EAAC,OAAMV,GAAC,IAAEI,IAAEM,IAAE,CAAC,CAAC,EAAE;AAAW,QAAAT,IAAE,aAAa,iBAAgBD,EAAC;AAAE,cAAME,MAAE,MAAIQ,IAAE,CAAC,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,IAAE,MAAIA,IAAE,CAAC,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAE,QAAAT,IAAE,oBAAoB,eAAcC,GAAC;AAAA,MAAC,WAAS,MAAIQ,IAAE,QAAO;AAAC,cAAK,EAAC,OAAMV,GAAC,IAAEI,IAAEM,IAAE,CAAC,CAAC,EAAE;AAAW,QAAAT,IAAE,aAAa,iBAAgBD,EAAC;AAAE,cAAK,EAAC,OAAME,IAAC,IAAEE,IAAEM,IAAE,CAAC,CAAC,EAAE;AAAW,QAAAT,IAAE,aAAa,iBAAgBC,GAAC;AAAE,cAAMC,MAAEC,IAAE,UAAW,CAAAM,QAAG,eAAaA,IAAE,IAAK,GAAEL,MAAE,MAAIF,MAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,IAAE,MAAIA,MAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAE,QAAAF,IAAE,oBAAoB,eAAcI,GAAC;AAAA,MAAC;AAAA,IAAC;AAAA;AAAC;AAAC,SAASD,GAAEM,KAAE;AAAC,EAAAA,IAAE,cAAc,gBAAeV,EAAC,GAAEU,IAAE,oBAAoB,aAAYT,EAAC;AAAC;;;ACAh5C,IAAMU,KAAE,EAAC,QAAO,iBAAgB,QAAO,qBAAoB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAE,SAASC,GAAEC,IAAEC,KAAE;AAAC,SAAOD,GAAE,QAAQ,gBAAgB,WAAWF,IAAE,CAAC,CAAC;AAAC;AAAC,SAASI,GAAEJ,KAAEC,KAAEG,KAAE;AAAC,EAAAH,GAAED,KAAEC,KAAEG,GAAC,GAAEC,GAAEJ,GAAC;AAAE,QAAK,EAAC,OAAMI,KAAE,QAAOC,KAAE,YAAWC,IAAC,IAAEH;AAAE,EAAAH,IAAE,cAAc,kBAAiB,CAACI,KAAEC,GAAC,CAAC,GAAEL,IAAE,cAAc,cAAa,CAACM,KAAEA,GAAC,CAAC;AAAC;AAAC,IAAMF,KAAE,EAAC,eAAcJ,IAAE,wBAAuBG,GAAC;;;ACA1X,IAAMI,KAAE,EAAC,QAAO,iBAAgB,QAAO,6BAA4B,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAE,SAASC,GAAEC,IAAEC,KAAE;AAAC,QAAK,EAAC,SAAQF,KAAE,gBAAeG,IAAC,IAAEF,IAAE,EAAC,WAAUG,IAAC,IAAED,IAAE;AAAW,SAAOH,IAAE,gBAAgB,WAAWD,IAAE,CAACK,GAAC,CAAC;AAAC;AAAC,SAASD,GAAEJ,KAAEC,KAAEG,KAAE;AAAC,EAAAJ,GAAEA,KAAEC,KAAEG,GAAC,GAAEC,GAAEJ,GAAC;AAAE,QAAK,EAAC,eAAcI,IAAC,IAAEL,IAAE,eAAe;AAAW,EAAAC,IAAE,oBAAoB,mBAAkBI,GAAC;AAAC;AAAC,IAAMA,KAAE,EAAC,eAAcJ,IAAE,wBAAuBG,GAAC;;;ACAna,IAAME,KAAE,EAAC,QAAO,iBAAgB,QAAO,4BAA2B,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAE,SAASC,GAAEC,IAAEC,KAAE;AAAC,SAAOD,GAAE,QAAQ,gBAAgB,WAAWF,IAAE,CAAC,CAAC;AAAC;AAAC,SAASI,GAAEJ,KAAEC,KAAEG,KAAE;AAAC,EAAAD,GAAEH,KAAEC,KAAEG,GAAC,GAAEC,GAAEJ,GAAC;AAAC;AAAC,IAAMK,KAAE,EAAC,eAAcL,IAAE,wBAAuBG,GAAC;;;ACAhR,IAAMG,KAAE,EAAC,QAAO,iBAAgB,QAAO,0BAAyB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAE,SAASC,GAAEC,KAAEC,KAAE;AAAC,QAAK,EAAC,SAAQF,KAAE,gBAAeG,IAAC,IAAEF,KAAE,EAAC,YAAWG,KAAE,YAAWC,IAAC,IAAEF,IAAE,YAAWG,MAAE,CAAC,EAAC,MAAK,QAAO,OAAMF,IAAC,GAAE,EAAC,MAAK,QAAO,OAAMC,IAAC,CAAC;AAAE,SAAOL,IAAE,gBAAgB,WAAWD,IAAEO,GAAC;AAAC;AAAC,SAASH,GAAEJ,IAAEC,KAAEG,KAAE;AAAC,EAAAH,GAAED,IAAEC,KAAEG,GAAC,GAAEE,GAAEL,GAAC,GAAEA,IAAE,cAAc,kBAAiB,CAACG,IAAE,OAAMA,IAAE,MAAM,CAAC;AAAE,QAAK,EAAC,QAAOC,KAAE,YAAWC,IAAC,IAAEN,GAAE,eAAe;AAAW,EAAAC,IAAE,cAAc,YAAWI,GAAC,GAAEJ,IAAE,cAAc,gBAAeK,GAAC;AAAC;AAAC,IAAMD,KAAE,EAAC,eAAcJ,IAAE,wBAAuBG,GAAC;;;ACA9iB,IAAMI,MAAE,EAAC,QAAO,iBAAgB,QAAO,0BAAyB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAE,SAASC,GAAEC,IAAEC,KAAE;AAAC,SAAOD,GAAE,QAAQ,gBAAgB,WAAWF,KAAE,CAAC,CAAC;AAAC;AAAC,SAASI,GAAEJ,KAAEC,KAAEG,KAAE;AAAC,EAAAJ,GAAEA,KAAEC,KAAEG,GAAC,GAAEC,GAAEJ,GAAC;AAAE,QAAK,EAAC,eAAcK,IAAC,IAAEN,IAAE,eAAe;AAAW,EAAAC,IAAE,oBAAoB,mBAAkBK,GAAC;AAAC;AAAC,IAAMA,KAAE,EAAC,eAAcL,IAAE,wBAAuBG,GAAC;;;ACAxS,IAAMG,KAAE,EAAC,QAAO,iBAAgB,QAAO,oBAAmB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAjH,IAAmHC,KAAE,oBAAI,IAAI,CAAC,QAAO,QAAO,QAAO,SAAQ,SAAQ,OAAO,CAAC;AAAE,SAASC,GAAEC,IAAE;AAAC,QAAK,EAAC,SAAQC,KAAE,gBAAeF,IAAC,IAAEC,IAAE,EAAC,YAAWE,KAAE,eAAcC,KAAE,SAAQC,IAAE,iBAAgBC,GAAC,IAAEN,IAAE;AAAW,MAAIO,KAAEH,IAAE,YAAY;AAAE,EAAAH,GAAE,QAAQ,SAAOD,GAAE,UAAQD,GAAE,IAAIQ,EAAC,MAAIA,KAAE,WAAWA,EAAC;AAAI,QAAMC,KAAE,CAACD,EAAC;AAAE,QAAIJ,OAAGK,GAAE,KAAK,WAAW;AAAE,QAAMC,KAAEJ,GAAE,OAAQ,CAAAJ,OAAG,eAAaA,GAAE,IAAK;AAAE,SAAOQ,GAAE,WAASD,GAAE,KAAK,aAAa,GAAE,MAAIC,GAAE,UAAQD,GAAE,KAAK,aAAa,IAAGF,MAAGE,GAAE,KAAK,aAAa,GAAEN,IAAE,gBAAgB,WAAWJ,IAAEU,EAAC;AAAC;AAAC,SAASL,IAAEO,KAAEZ,KAAEC,KAAE;AAAC,EAAAC,GAAEU,KAAEZ,KAAEC,GAAC,GAAEI,GAAEL,GAAC;AAAE,QAAK,EAAC,aAAYE,IAAC,IAAEU,IAAE,eAAe;AAAW,EAAAZ,IAAE,cAAc,iBAAgBE,GAAC;AAAC;AAAC,IAAMI,KAAE,EAAC,eAAcJ,IAAE,wBAAuBG,IAAC;;;ACAtzB,IAAMQ,MAAE,EAAC,QAAO,iBAAgB,QAAO,mBAAkB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAE,SAASC,IAAEC,IAAEC,KAAE;AAAC,QAAK,EAAC,SAAQF,KAAE,gBAAeG,IAAC,IAAEF,IAAEG,MAAED,IAAE,WAAW,YAAU,IAAE,CAAC,WAAW,IAAE,CAAC;AAAE,SAAOH,IAAE,gBAAgB,WAAWD,KAAEK,GAAC;AAAC;AAAC,SAASD,GAAEJ,KAAEC,KAAEG,KAAE;AAAC,EAAAD,GAAEH,KAAEC,KAAEG,GAAC,GAAEC,GAAEJ,GAAC;AAAE,QAAK,EAAC,gBAAeI,KAAE,cAAaC,IAAC,IAAEN,IAAE,eAAe;AAAW,EAAAC,IAAE,cAAc,oBAAmBI,GAAC,GAAEJ,IAAE,cAAc,kBAAiBK,GAAC;AAAC;AAAC,IAAMD,MAAE,EAAC,eAAcJ,KAAE,wBAAuBG,GAAC;;;ACAzd,IAAMG,MAAE,EAAC,QAAO,iBAAgB,QAAO,mBAAkB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAE,SAASC,IAAEC,IAAEC,KAAE;AAAC,QAAK,EAAC,SAAQF,KAAE,gBAAeG,IAAC,IAAEF,IAAEG,MAAED,IAAE,WAAW,SAAO,CAAC,QAAQ,IAAE,CAAC;AAAE,SAAOH,IAAE,gBAAgB,WAAWD,KAAEK,GAAC;AAAC;AAAC,SAASD,IAAEJ,KAAEC,KAAEG,KAAE;AAAC,EAAAJ,GAAEA,KAAEC,KAAEG,GAAC,GAAEC,GAAEJ,GAAC;AAAE,QAAK,EAAC,eAAcI,IAAC,IAAEL,IAAE,eAAe;AAAW,EAAAC,IAAE,oBAAoB,mBAAkBI,GAAC;AAAC;AAAC,IAAMA,MAAE,EAAC,eAAcJ,KAAE,wBAAuBG,IAAC;;;ACAla,IAAME,MAAE,EAAC,QAAO,iBAAgB,QAAO,oBAAmB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAE,SAASC,IAAEC,KAAEC,IAAE;AAAC,SAAOD,IAAE,QAAQ,gBAAgB,WAAWF,KAAE,CAAC,CAAC;AAAC;AAAC,SAASI,IAAEJ,KAAEC,KAAEG,KAAE;AAAC,EAAAF,GAAEF,KAAEC,KAAEG,GAAC,GAAEC,GAAEJ,GAAC;AAAE,QAAK,EAAC,cAAaI,KAAE,WAAUC,KAAE,gBAAeC,IAAE,YAAWC,IAAC,IAAER,IAAE,eAAe;AAAW,EAAAC,IAAE,cAAc,kBAAiBI,GAAC,GAAEJ,IAAE,cAAc,eAAcK,GAAC,GAAEL,IAAE,aAAa,iBAAgBM,KAAE,IAAE,CAAC,GAAEN,IAAE,cAAc,gBAAeO,GAAC;AAAC;AAAC,IAAMH,MAAE,EAAC,eAAcJ,KAAE,wBAAuBG,IAAC;;;ACA3c,IAAMK,MAAE,EAAC,QAAO,iBAAgB,QAAO,oBAAmB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAE,SAASC,GAAEC,IAAEC,KAAE;AAJtP;AAIuP,QAAK,EAAC,SAAQF,IAAC,IAAEC,IAAEE,MAAE,CAAC,GAAEC,MAAE,GAAC,KAAAH,GAAE,QAAQ,aAAa,iBAAvB,mBAAqC,qBAAmB,EAAC,eAAcI,KAAE,eAAcC,IAAC,IAAEJ;AAAE,SAAM,YAAUG,MAAEF,IAAE,KAAK,SAAS,IAAE,eAAaE,OAAGD,OAAGD,IAAE,KAAK,UAAU,GAAEG,QAAIH,IAAE,KAAK,iBAAiB,GAAE,MAAIG,IAAE,QAAQ,CAAC,KAAGH,IAAE,KAAK,kBAAkB,IAAGH,IAAE,gBAAgB,WAAWD,KAAEI,GAAC;AAAC;AAAC,SAASA,GAAEJ,KAAEC,KAAEG,KAAE;AAAC,QAAK,EAAC,OAAMC,KAAE,UAASC,IAAC,IAAEF,IAAE,YAAY,EAAC,eAAc,KAAE,CAAC;AAAE,EAAAI,GAAER,IAAE,SAAQC,KAAEI,KAAEC,GAAC,GAAEL,IAAE,aAAa,WAAU,CAAC,GAAEA,IAAE,cAAc,YAAW,CAAC,GAAE,CAAC,CAAC,GAAEA,IAAE,cAAc,gBAAe,CAAC,GAAE,CAAC,CAAC,GAAEA,IAAE,oBAAoB,aAAY,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,CAAC,CAAC,GAAEA,IAAE,aAAa,WAAU,CAAC,GAAEA,IAAE,aAAa,aAAY,CAAC;AAAE,QAAK,EAAC,OAAMM,KAAE,QAAOE,IAAE,QAAOC,IAAE,eAAcC,GAAC,IAAEP;AAAE,EAAAH,IAAE,cAAc,kBAAiB,CAACS,GAAE,OAAMA,GAAE,MAAM,CAAC,GAAET,IAAE,cAAc,qBAAoB,CAACM,KAAEE,EAAC,CAAC,GAAER,IAAE,cAAc,sBAAqBU,KAAEA,GAAE,UAAQ,CAAC,GAAEV,IAAE,cAAc,uBAAsBU,KAAEA,GAAE,OAAK,CAAC;AAAC;AAAC,IAAMN,MAAE,EAAC,eAAcJ,IAAE,wBAAuBG,GAAC;;;ACAxjC,IAAMQ,KAAE,EAAC,QAAO,iBAAgB,QAAO,oBAAmB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAE,SAASC,GAAEC,KAAEC,KAAE;AAAC,QAAK,EAAC,SAAQF,KAAE,gBAAeG,IAAC,IAAEF,KAAE,EAAC,WAAUG,IAAC,IAAED,IAAE,YAAWE,MAAE,mBAAiBD,MAAE,CAAC,aAAa,IAAE,CAAC;AAAE,SAAOJ,IAAE,gBAAgB,WAAWD,IAAEM,GAAC;AAAC;AAAC,SAASF,GAAEJ,IAAEC,KAAEG,KAAE;AAAC,EAAAE,GAAEN,IAAEC,KAAEG,GAAC,GAAEC,GAAEJ,GAAC;AAAE,QAAK,EAAC,OAAMI,KAAE,QAAOC,KAAE,YAAWC,IAAC,IAAEH,KAAE,EAAC,SAAQI,IAAE,WAAUC,IAAE,gBAAeC,IAAE,iBAAgBC,GAAC,IAAEX,GAAE,eAAe;AAAW,EAAAC,IAAE,cAAc,kBAAiB,CAACI,KAAEC,GAAC,CAAC,GAAEL,IAAE,cAAc,cAAa,CAACM,KAAEA,GAAC,CAAC,GAAEN,IAAE,aAAa,aAAYO,EAAC,GAAEP,IAAE,aAAa,oBAAmB,eAAaQ,KAAEC,KAAE,CAAC,GAAET,IAAE,aAAa,qBAAoB,eAAaQ,KAAEE,KAAE,CAAC;AAAC;AAAC,IAAMN,MAAE,EAAC,eAAcJ,IAAE,wBAAuBG,GAAC;;;ACAzrB,IAAMQ,MAAE,EAAC,QAAO,iBAAgB,QAAO,sBAAqB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAE,SAASC,GAAEC,IAAEC,KAAE;AAAC,QAAK,EAAC,UAASF,IAAE,WAAUG,KAAE,iBAAgBC,IAAC,IAAEH,GAAE,eAAe,YAAWI,MAAE,CAAC;AAAE,SAAOL,MAAGK,IAAE,KAAK,UAAU,GAAEF,MAAE,KAAGE,IAAE,KAAK,WAAW,GAAED,OAAGC,IAAE,KAAK,aAAa,GAAEJ,GAAE,QAAQ,gBAAgB,WAAWF,KAAEM,GAAC;AAAC;AAAC,SAASF,IAAEJ,KAAEC,IAAEG,KAAE;AAAC,EAAAJ,GAAEA,KAAEC,IAAEG,GAAC,GAAEG,GAAEN,EAAC;AAAE,QAAK,EAAC,OAAMI,KAAE,QAAOC,IAAC,IAAEF,KAAEI,KAAER,IAAE,eAAe;AAAW,EAAAC,GAAE,cAAc,kBAAiB,CAACI,KAAEC,GAAC,CAAC,GAAEL,GAAE,aAAa,eAAcO,GAAE,MAAM,GAAEP,GAAE,aAAa,eAAcO,GAAE,MAAM,GAAEP,GAAE,cAAc,YAAWO,GAAE,MAAM,GAAEP,GAAE,cAAc,eAAcO,GAAE,SAAS,GAAEP,GAAE,cAAc,eAAcO,GAAE,SAAS,GAAEP,GAAE,cAAc,WAAUO,GAAE,KAAK,GAAEP,GAAE,cAAc,qBAAoBO,GAAE,eAAe;AAAC;AAAC,IAAMH,MAAE,EAAC,eAAcJ,IAAE,wBAAuBG,IAAC;;;ACAnG,IAAMK,KAAE,oBAAI;AAAI,SAASC,GAAEC,KAAEC,KAAEC,KAAE;AAAC,QAAMC,KAAE,EAAC,OAAMF,KAAE,QAAOC,KAAE,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,gBAAeF,IAAE,SAAOA,GAAE,SAAOI,GAAE,UAAQ,EAAE,MAAK,cAAa,EAAE,SAAQ,UAAS,EAAE,OAAM,aAAYJ,IAAE,SAAOA,GAAE,QAAO,UAAS,EAAE,eAAc,SAAQ,MAAE;AAAE,SAAO,IAAIK,GAAEL,KAAEG,EAAC;AAAC;AAAC,SAASG,GAAEL,KAAEC,KAAEC,IAAEI,KAAE;AAAC,QAAK,EAAC,SAAQC,KAAE,eAAcC,KAAE,oBAAmBC,GAAC,IAAET,KAAEU,KAAEJ,IAAE,cAAcN,KAAEE,EAAC;AAAE,MAAGO,MAAG,EAAED,GAAC,KAAG,CAACE,GAAE,SAAS,QAAOF,IAAE,GAAE;AAAK,QAAK,EAAC,OAAMG,KAAE,QAAOC,GAAC,IAAEV;AAAE,SAAOK,IAAE,gBAAgBN,GAAC,GAAEM,IAAE,YAAY,GAAE,GAAEI,KAAEC,EAAC,GAAEL,IAAE,WAAWG,EAAC,GAAEA;AAAC;AAAC,SAAS,EAAEX,KAAE;AAAC,SAAOF,GAAE,IAAIE,IAAE,YAAY,CAAC;AAAC;AAAC,SAASK,GAAEL,KAAEC,KAAEC,KAAEC,IAAE;AAAC,QAAMI,MAAEP,IAAE,eAAe,KAAK,YAAY,GAAEQ,MAAE,gBAAcD,MAAEA,MAAE,EAAEA,GAAC;AAAE,MAAG,QAAMC,IAAE;AAAO,QAAMC,MAAEH,GAAEN,KAAEE,KAAEC,IAAEK,GAAC;AAAE,MAAG,CAACC,IAAE;AAAO,EAAAD,IAAE,uBAAuBR,KAAES,KAAEN,EAAC,GAAEF,IAAE,KAAK;AAAE,QAAK,EAAC,OAAMS,IAAE,QAAOC,GAAC,IAAER,IAAES,MAAEb,GAAEC,IAAE,SAAQU,IAAEC,EAAC;AAAE,MAAGT,IAAE,cAAc,GAAE,GAAEQ,IAAEC,IAAE,GAAE,GAAEC,GAAC,GAAE,gBAAcL,IAAE,CAAAJ,GAAE,gBAAcS,KAAET,GAAE,YAAU;AAAA,OAAO;AAAC,UAAMF,MAAED,IAAE,cAAYA,IAAE,eAAe,KAAG;AAAE,IAAAG,GAAE,iBAAiBF,GAAC,IAAEW;AAAA,EAAC;AAAC;AAACd,GAAE,IAAI,UAASS,EAAC,GAAET,GAAE,IAAI,kBAAiBS,EAAC,GAAET,GAAE,IAAI,iBAAgBU,EAAC,GAAEV,GAAE,IAAI,eAAcI,EAAC,GAAEJ,GAAE,IAAI,eAAcI,EAAC,GAAEJ,GAAE,IAAI,SAAQW,EAAC,GAAEX,GAAE,IAAI,QAAOS,GAAC,GAAET,GAAE,IAAI,QAAOS,GAAC,GAAET,GAAE,IAAI,SAAQS,GAAC,GAAET,GAAE,IAAI,SAAQS,GAAC,GAAET,GAAE,IAAI,WAAUU,GAAC;;;ACAhtC,IAAMM,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK,UAAS,KAAK,QAAM,MAAK,KAAK,wBAAsB,oBAAI,OAAI,KAAK,OAAK;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,MAAE,KAAK,KAAK,GAAE,EAAE,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,aAAaC,KAAE;AAAC,UAAK,EAAC,SAAQD,IAAE,YAAWE,IAAC,IAAED,KAAEE,MAAE,aAAWD;AAAE,IAAAF,GAAE,mBAAmB,CAACG,GAAC,GAAEH,GAAE,yBAAyB,EAAE,KAAI,EAAE,qBAAoB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,GAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,GAAE,oBAAoB,CAAC,GAAEA,GAAE,sBAAsB,CAACG,GAAC;AAAA,EAAC;AAAA,EAAC,KAAKF,KAAED,IAAE;AAAC,QAAG,CAACI,GAAEJ,EAAC,KAAGA,GAAE,UAAU;AAAO,UAAK,EAAC,YAAWG,IAAC,IAAEF;AAAE,QAAG,oBAAkBE,IAAE,QAAM,aAAWA,MAAE,KAAK,SAASF,KAAED,EAAC,IAAE,KAAK,KAAK,YAAYC,KAAED,IAAE,IAAE;AAAE,SAAK,YAAYC,KAAED,EAAC;AAAA,EAAC;AAAA,EAAC,SAASA,IAAEE,KAAE;AAAC,UAAK,EAAC,gBAAeC,IAAC,IAAEH,IAAEK,MAAE,gBAAcF,IAAE;AAAK,QAAG,EAAEE,MAAE,EAAEH,IAAE,iBAAeA,IAAE,aAAW,CAACA,IAAE,WAAW;AAAO,UAAK,EAAC,UAASI,KAAE,SAAQC,IAAC,IAAEP;AAAE,IAAAM,IAAE,MAAM,KAAK,IAAI;AAAE,UAAME,KAAED,IAAE,0BAA0B,GAAEE,KAAEF,IAAE,YAAY;AAAE,IAAAF,QAAIH,IAAE,mBAAiB,EAAEA,IAAE,gBAAgB,IAAGK,IAAE,mBAAmB,EAAE,OAAML,IAAE,YAAW,GAAG,GAAEA,IAAE,cAAcF,EAAC,GAAE,KAAK,UAAUO,GAAC;AAAE,UAAK,EAAC,0BAAyBH,IAAE,KAAIM,GAAC,IAAE,KAAK,cAAcH,KAAEL,IAAE,OAAMA,IAAE,MAAM;AAAE,IAAAS,GAAEX,IAAE,KAAK,OAAMU,IAAER,GAAC,GAAEE,MAAGM,GAAE,QAAQ,GAAEH,IAAE,gBAAgBC,EAAC,GAAED,IAAE,YAAYE,GAAE,GAAEA,GAAE,GAAEA,GAAE,OAAMA,GAAE,MAAM,GAAEH,IAAE,IAAI,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,YAAYL,KAAEC,KAAEU,MAAE,OAAG;AAAC,UAAK,EAAC,UAASP,KAAE,SAAQC,IAAC,IAAEL;AAAE,QAAGI,IAAE,MAAM,KAAK,IAAI,GAAEC,IAAE,mBAAmB,EAAE,OAAMJ,IAAE,YAAW,GAAG,GAAEA,IAAE,cAAcD,GAAC,GAAEW,OAAG,CAACV,IAAE,kBAAiB;AAAC,UAAGA,IAAE,uBAAuB,GAAE,CAACA,IAAE,iBAAiB,QAAO,KAAKG,IAAE,IAAI,KAAK,IAAI;AAAE,MAAAH,IAAE,YAAU;AAAA,IAAE;AAAC,SAAK,0BAA0BA,GAAC;AAAE,UAAMM,KAAEN,IAAE,qBAAqB,MAAKO,KAAEJ,GAAEG,EAAC,GAAE,EAAC,eAAcJ,IAAE,oBAAmBM,GAAC,IAAET,KAAE,EAAC,SAAQY,IAAE,SAAQC,GAAC,IAAEL,GAAE,cAAcR,KAAEC,KAAEU,GAAC;AAAE,QAAGF,MAAG,EAAEN,EAAC,KAAG,CAACU,GAAE,SAAS,QAAO,KAAKV,GAAE;AAAE,IAAAE,IAAE,WAAWQ,EAAC;AAAE,UAAMC,KAAE,KAAK,iBAAiBP,IAAEF,KAAEQ,IAAED,EAAC;AAAE,SAAK,UAAQ,KAAK,QAAM,IAAI,EAAEP,KAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,IAAGG,GAAE,uBAAuBR,KAAEa,IAAEZ,KAAEa,IAAEH,GAAC,GAAE,KAAK,MAAM,KAAK,GAAEP,IAAE,IAAI,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,0BAA0BJ,KAAE;AAAC,QAAG,CAACA,IAAE,gBAAe;AAAC,YAAMD,KAAEgB,GAAE,GAAE,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,eAAcd,KAAE,OAAMC,KAAE,QAAOS,IAAC,IAAEX,KAAEI,MAAE,EAAEH,KAAE,CAACC,KAAES,GAAC,GAAE,CAACX,IAAE,OAAO,OAAMA,IAAE,OAAO,MAAM,GAAE,GAAE,KAAE;AAAE,MAAAA,IAAE,iBAAe,EAAC,GAAGD,IAAE,GAAGK,KAAE,cAAaJ,IAAE,WAAU;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,KAAED,IAAEE,KAAE;AAAC,UAAMC,MAAEH,OAAI,KAAGE,QAAI;AAAE,WAAOC,OAAG,KAAK,SAAO,KAAK,OAAK,KAAK,cAAcF,KAAED,IAAEE,GAAC,IAAG,EAAC,0BAAyBC,KAAE,KAAI,KAAK,KAAI,KAAG,EAAC,0BAAyBA,KAAE,KAAI,KAAK,cAAcF,KAAED,IAAEE,GAAC,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,KAAED,IAAEE,KAAE;AAAC,UAAMC,MAAEc,GAAEhB,KAAED,IAAEE,GAAC;AAAE,WAAO,IAAIgB,GAAEjB,KAAE,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,MAAK,OAAMD,IAAE,QAAOE,IAAC,GAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,UAAUF,KAAE;AAAC,SAAK,UAAQ,KAAK,QAAM,IAAI,EAAEA,KAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAA,EAAE;AAAA,EAAC,iBAAiBA,KAAED,IAAEE,KAAEC,KAAE;AAAC,UAAMS,MAAET,IAAE,SAAO,IAAEF,MAAE,MAAIE,IAAE,KAAK,GAAG,IAAEF;AAAE,QAAG,KAAK,sBAAsB,IAAIW,GAAC,EAAE,QAAO,KAAK,sBAAsB,IAAIA,GAAC;AAAE,UAAMP,MAAEQ,GAAEb,IAAEE,GAAC;AAAE,WAAO,KAAK,sBAAsB,IAAIU,KAAEP,GAAC,GAAEA;AAAA,EAAC;AAAC;;;ACA7jG,IAAMc,MAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,IAAEC,KAAEH,KAAEI,KAAEH,KAAEI,KAAEC,KAAE,MAAK;AAAC,UAAMJ,IAAEC,KAAEH,KAAEI,KAAEH,KAAEI,GAAC,GAAE,KAAK,SAAO,MAAK,KAAK,SAAO,IAAIE,GAAED,IAAE,MAAK,IAAI,GAAE,KAAK,OAAO,aAAW,CAACL,KAAEI,GAAC,GAAE,KAAK,OAAO,KAAK,WAAW,MAAI,KAAK,MAAM,CAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAM,QAAQ,GAAE,KAAK,OAAO,QAAQ,GAAE,KAAK,SAAO,MAAK,KAAK,QAAM;AAAA,EAAI;AAAA,EAAC,IAAI,WAAWH,IAAE;AAAC,SAAK,OAAO,aAAWA;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,OAAO;AAAA,EAAU;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAM,aAAaA,EAAC,GAAE,KAAK,OAAO,WAAW,MAAI,KAAK,WAAW;AAAA,EAAG;AAAA,EAAC,oBAAmB;AAAC,WAAM,EAAC,KAAIC,GAAE,GAAE,UAASA,GAAE,EAAC;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,SAAK,OAAO,QAAM,KAAK;AAAA,EAAK;AAAA,EAAC,WAAU;AAAC,SAAK,OAAO,QAAM;AAAA,EAAI;AAAC;;;ACAtb,IAAMK,MAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,uBAAqB;AAAA,EAAE;AAAA,EAAC,WAAWC,KAAE;AAAC,UAAMC,MAAE,KAAK,eAAeD,GAAC,GAAE,CAACE,IAAEH,GAAC,IAAE,KAAK,cAAc,SAAS,MAAKI,MAAE,KAAK,cAAc,kBAAkBH,IAAE,KAAK;AAAE,WAAO,IAAID,IAAEC,KAAEG,KAAEF,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEC,IAAEH,GAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBC,KAAE;AAAC,UAAMC,MAAED,IAAE,mBAAmB,EAAC,MAAK,kBAAiB,SAAQ,CAACI,EAAC,GAAE,QAAO,MAAI,KAAK,SAAS,IAAK,CAAAJ,QAAGA,IAAE,MAAO,GAAE,WAAU,EAAE,IAAG,CAAC;AAAE,WAAM,CAAC,GAAG,MAAM,oBAAoBA,GAAC,GAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,SAASD,KAAE;AAAC,QAAG,CAAC,KAAK,WAASA,IAAE,cAAY,EAAE,IAAI;AAAO,UAAK,EAAC,qBAAoBC,IAAC,IAAE;AAAK,QAAG,CAACA,IAAE,QAAOD,IAAE,aAAW,iBAAgB,KAAK,MAAM,SAASA,GAAC;AAAE,UAAK,CAACE,IAAEG,GAAC,IAAE,KAAK,cAAc,SAAS;AAAK,QAAGL,IAAE,aAAW,UAASA,IAAE,iBAAe,EAAC,MAAK,aAAY,YAAW,EAAC,iBAAgB,CAACE,IAAEG,GAAC,EAAC,GAAE,WAAU,OAAM,IAAG,GAAE,eAAc,MAAE,GAAE,MAAM,SAASL,GAAC,GAAEC,OAAA,gBAAAA,IAAG,UAAU,QAAO;AAAC,YAAK,EAAC,WAAUC,IAAE,aAAYG,IAAC,IAAEJ;AAAE,eAAQA,MAAE,GAAEA,MAAEC,GAAE,QAAOD,OAAI;AAAC,cAAMF,MAAEG,GAAED,GAAC;AAAE,uBAAaF,IAAE,QAAM,eAAaA,IAAE,SAAOC,IAAE,aAAW,UAASA,IAAE,iBAAeD,KAAEC,IAAE,cAAYK,KAAE,MAAM,SAASL,GAAC;AAAA,MAAE;AAAA,IAAC;AAAC,IAAAA,IAAE,iBAAe,MAAKA,IAAE,aAAW,UAAS,MAAM,SAASA,GAAC;AAAA,EAAC;AAAA,EAAC,eAAeE,IAAE;AAAC,UAAMG,MAAE,KAAK,cAAc,cAAc,EAAE,GAAEH,EAAC;AAAE,QAAG,KAAK,wBAAsBA,GAAE,OAAM;AAAC,YAAK,EAAC,UAASF,IAAC,IAAE,KAAK,eAAcD,MAAE,GAAEC,IAAE,gBAAgB;AAAE,UAAGD,KAAE;AAAC,cAAME,MAAED,IAAE,MAAME,GAAE,KAAK;AAAE,YAAG,CAACD,IAAE,QAAOI;AAAE,cAAK,EAAC,YAAWF,IAAC,IAAEF,KAAEH,MAAEC,MAAEI,MAAEH,IAAE,KAAK,CAAC,GAAEM,MAAER,OAAGE,IAAE,KAAK,CAAC,IAAEF,OAAGK,MAAE;AAAE,QAAAE,IAAE,CAAC,KAAGC,MAAEJ,GAAE,OAAMG,IAAE,CAAC,KAAGC,MAAEJ,GAAE;AAAA,MAAK;AAAA,IAAC;AAAC,WAAOG;AAAA,EAAC;AAAC;;;ACApM,IAAME,KAAE,CAAC,GAAE,CAAC;AAAE,IAAIC,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,uBAAqB,MAAK,KAAK,gBAAc,MAAK,KAAK,gBAAc,MAAK,KAAK,cAAY,MAAK,KAAK,yBAAuB,MAAK,KAAK,wBAAsB,MAAK,KAAK,kBAAgB,CAAC,GAAE,KAAK,cAAY,MAAK,KAAK,wBAAsB,OAAG,KAAK,0BAAwB,MAAK,KAAK,oBAAkB,MAAK,KAAK,mBAAiB,MAAK,KAAK,wBAAsB,OAAG,KAAK,uBAAqB,MAAK,KAAK,yBAAuB,OAAG,KAAK,WAAS,OAAG,KAAK,aAAW,MAAK,KAAK,kBAAgB,EAAG,OAAMC,MAAE,CAAC,MAAI;AAAC,UAAG,CAAC,KAAK,eAAa,KAAK,UAAU,UAAU;AAAO,YAAMC,KAAE,KAAK;AAAqB,MAAAD,IAAE,cAAY,MAAM,KAAK,gBAAgB,WAAW,KAAK,MAAM,qBAAqB,CAAC,GAAE,KAAK,+BAA+B;AAAG,YAAME,MAAE,KAAK,sBAAqB,EAAC,MAAKC,IAAC,IAAE;AAAK,aAAOH,IAAE,WAAS,aAAWG,OAAG,CAAC,CAACH,IAAE,aAAW,UAAQE,OAAG,UAAQD,KAAE,KAAK,gBAAgB,WAAW,KAAK,UAAU,CAAC,IAAE,KAAK,gBAAgB,WAAW,KAAK,aAAaD,IAAE,MAAM,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,IAAI,wBAAuB;AAAC,WAAO,KAAK,KAAK,uBAAuB,KAAG;AAAA,EAAE;AAAA,EAAC,IAAI,sBAAsBA,KAAE;AAAC,SAAK,KAAK,yBAAwBA,GAAC;AAAA,EAAC;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,QAAM,KAAK,KAAK,sBAAsB,KAAG,KAAK,KAAK,sBAAsB;AAAA,EAAC;AAAA,EAAC,IAAI,qBAAqBA,KAAE;AAAC,QAAG,KAAK,iBAAe,KAAK,yBAAuBA,KAAE;AAAC,WAAK,cAAc,QAAQ,GAAE,KAAK,UAAU,kBAAkB;AAAE,YAAMC,KAAE,KAAK,cAAcD,GAAC;AAAE,WAAK,gBAAc,IAAII,GAAE,EAAC,aAAY,SAAQ,aAAY,CAAAJ,QAAG,KAAK,YAAYA,GAAC,GAAE,aAAY,CAAAA,QAAG,KAAK,YAAYA,GAAC,GAAE,WAAUC,IAAE,cAAa,KAAK,cAAa,CAAC,GAAE,KAAK,KAAK,wBAAuBD,GAAC,GAAE,KAAK,UAAU,cAAc;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,KAAE;AAJ3lG;AAI4lG,SAAK,YAAY,MAAM,GAAE,KAAK,YAAY,QAAMA,IAAE,OAAM,KAAK,cAAc,OAAOA,GAAC,GAAE,KAAK,YAAY,OAAO;AAAE,UAAK,EAAC,QAAOC,IAAE,YAAWC,KAAE,OAAMC,IAAC,IAAEH,IAAE,OAAMI,MAAE,KAAK,cAAc,uBAAuBD,GAAC;AAAE,QAAG,KAAK,MAAM,QAAO;AAAC,UAAG,CAAC,KAAK,wBAAsB,KAAK,uBAAsB;AAAC,cAAMH,MAAE,KAAK,gBAAgBI,IAAE,KAAK,GAAED,MAAEF,GAAE,SAAOA,KAAEI,GAAE,SAASJ,EAAC;AAAE,UAAE,KAAK,wBAAuB,KAAK,uBAAsBE,KAAED,KAAEF,KAAE,KAAK,MAAM,OAAO,SAAS,QAAQ;AAAA,MAAC;AAAC,WAAK,wBAAsB,SAAG,UAAK,gBAAL,mBAAkB,WAAQI,IAAE,UAAQ,KAAK,cAAYA,KAAE,QAAM,KAAK,qBAAmB,KAAK,UAAU,oBAAkB,KAAK,wBAAwB,GAAE,KAAK,cAAc,gBAAgB,CAAC;AAAA,IAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,KAAC,KAAK,UAAU,oBAAkB,KAAK,yBAAuB,KAAK,oBAAkB,KAAK,iBAAiB,MAAM,GAAE,KAAK,mBAAiB,IAAI,mBAAgB,MAAI,KAAK,YAAY,UAAQ,KAAK,aAAa,KAAK,iBAAiB,MAAM,EAAE,KAAM,MAAI;AAAC,WAAK,yBAAuB,OAAG,KAAK,UAAU,cAAc;AAAA,IAAC,CAAE;AAAG,UAAMJ,MAAE,KAAK,cAAc,KAAK,oBAAoB;AAAE,SAAK,cAAc,gBAAgBA,GAAC,GAAE,KAAK,UAAU,cAAc;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAJlsI;AAImsI,aAAO,UAAK,gBAAL,mBAAkB,aAAU,KAAK,0BAAwB,EAAE,CAAC,KAAK,mBAAiB,CAAC,KAAK,gBAAgB;AAAA,EAAS;AAAA,EAAC,SAAQ;AAAC,IAAAM,GAAE,IAAI,EAAE,yBAAuB,KAAK,wBAAsB,QAAI,KAAK,oBAAoB,GAAE,KAAK,gBAAc,IAAIC,GAAE,KAAK,UAAU,UAAS,KAAK,UAAU,UAAU;AAAE,UAAMP,MAAE,KAAK,yBAAyB;AAAE,SAAK,cAAY,IAAIQ,GAAE,EAAC,cAAa,KAAK,eAAc,aAAYR,KAAE,SAAQ,CAACA,KAAEC,OAAI,KAAK,YAAYD,KAAEC,EAAC,EAAC,CAAC;AAAE,UAAMA,KAAE,KAAK,cAAc,KAAK,oBAAoB;AAAE,SAAK,gBAAc,IAAIG,GAAE,EAAC,aAAY,SAAQ,aAAY,CAAAJ,QAAG,KAAK,YAAYA,GAAC,GAAE,aAAY,CAAAA,QAAG,KAAK,YAAYA,GAAC,GAAE,WAAUC,IAAE,cAAa,KAAK,cAAa,CAAC,GAAE,KAAK,0BAA0B;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,cAAc,QAAQ,GAAE,KAAK,YAAY,MAAM,GAAE,KAAK,UAAU,kBAAkB,GAAE,KAAK,cAAY,KAAK,gBAAc,KAAK,gBAAc,MAAKQ,GAAE,KAAK,wBAAuB,KAAK,qBAAqB,GAAE,KAAK,yBAAuB,KAAK,wBAAsB;AAAA,EAAI;AAAA,EAAC,YAAYT,KAAE;AAAC,UAAMC,KAAE,KAAK,UAAU,WAAWD,GAAC;AAAE,WAAO,KAAK,kBAAkBC,EAAC,GAAE,KAAK,UAAU,cAAc,GAAE,KAAK,wBAAsB,MAAG,KAAK,yBAAuB,KAAK,UAAU,oBAAkB,CAAC,KAAK,sBAAqBA;AAAA,EAAC;AAAA,EAAC,YAAYD,KAAE;AAAC,SAAK,YAAY,MAAMA,IAAE,IAAI,EAAE,GAAE,KAAK,UAAU,YAAYA,GAAC,GAAEA,IAAE,KAAK,UAAU,MAAI;AAAC,MAAAA,IAAE,QAAQ,GAAE,KAAK,UAAU,cAAc;AAAA,IAAC,CAAE,GAAE,KAAK,UAAU,cAAc;AAAA,EAAC;AAAA,EAAC,0BAA0BA,MAAE,MAAK;AAAC,UAAMC,KAAE,QAAMD,OAAGA,IAAE,KAAK,GAAG,MAAI,KAAK,cAAc,SAAS,KAAK,KAAK,GAAG;AAAE,QAAGC,MAAG,EAAE,KAAK,oBAAoB,EAAE,QAAO,KAAK;AAAqB,IAAAD,MAAEA,OAAG,KAAK,cAAc,SAAS;AAAK,UAAK,CAACE,KAAEE,GAAC,IAAEJ,KAAEU,MAAE,IAAI,EAAE,EAAC,OAAMR,KAAE,QAAOE,KAAE,QAAO,CAAC,IAAI,WAAWF,MAAEE,GAAC,CAAC,GAAE,MAAK,IAAI,WAAWF,MAAEE,GAAC,GAAE,WAAU,KAAI,CAAC;AAAE,WAAOH,OAAI,KAAK,uBAAqBS,MAAGA;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,QAAG,EAAE,yBAAwB,KAAK,cAAY,CAAC,KAAK,UAAU,oBAAoB,QAAO,KAAK,MAAM;AAAQ,UAAK,EAAC,SAAQV,KAAE,QAAOC,GAAC,IAAE,KAAK,OAAMC,MAAE,oBAAmBD,KAAEA,GAAE,eAAe,kBAAgB;AAAK,YAAOD,OAAA,gBAAAA,IAAG,YAAQE,OAAA,gBAAAA,IAAG,WAAQ,MAAID,GAAE,WAAW,YAAUD,IAAE,IAAK,CAAAA,QAAGE,IAAE,KAAK,IAAIF,KAAEE,IAAE,SAAO,CAAC,CAAC,CAAE,IAAEF,OAAGE;AAAA,EAAC;AAAA,EAAC,iCAAgC;AAAA,EAAC;AAAA,EAAC,YAAYF,KAAEC,IAAE;AAAC,UAAMC,MAAE,EAAED,EAAC,IAAEA,GAAE,SAAO,MAAKS,MAAE,KAAK,yBAAyB,GAAE,EAAC,WAAUC,IAAC,IAAE,MAAK,EAAC,UAASL,GAAC,IAAEK,KAAEC,MAAE,CAACN,GAAE,eAAa,EAAEO,GAAEF,IAAE,KAAK,gBAAgB,CAAC,GAAEJ,KAAEG,OAAG,KAAK,MAAM,OAAO,4BAA2BI,KAAE,EAAC,kBAAiB,MAAG,qBAAoBH,IAAE,qBAAoB,eAAcD,MAAE,YAAU,KAAK,MAAM,eAAc,YAAW,KAAK,uBAAsB,gBAAeH,IAAE,oBAAmB,aAAW,KAAK,QAAM,QAAM,KAAK,UAAU,qBAAoB,QAAO,EAAEL,GAAC,GAAE,eAAc,KAAK,gBAAgBF,IAAE,KAAK,GAAE,YAAWW,IAAE,YAAW,UAASL,IAAE,mBAAkBM,IAAC;AAAE,WAAO,KAAK,UAAUZ,KAAEc,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcd,KAAE;AAAC,WAAOA,MAAE,KAAG;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,UAAK,EAAC,WAAUA,IAAC,IAAE,MAAKC,KAAED,IAAE,KAAK,kBAAiBE,MAAE,IAAI,EAAE,EAAC,GAAEF,IAAE,WAAW,MAAK,GAAEA,IAAE,WAAW,MAAK,kBAAiBC,GAAC,CAAC;AAAE,QAAG,KAAK,iBAAiB,GAAE;AAAC,YAAK,EAAC,MAAKE,IAAC,IAAE,KAAK,MAAM,UAASC,MAAED,IAAE,IAAK,CAAC,EAAC,OAAMH,IAAC,MAAIA,GAAE,GAAEU,MAAEK,GAAE,OAAO,EAAC,kBAAiBd,IAAE,MAAK,GAAE,QAAOG,IAAC,CAAC,GAAEO,MAAEV,GAAE,eAAa,OAAI,OAAK;AAAI,UAAG,KAAK,wBAAsB,KAAK,IAAIS,IAAE,OAAO,IAAER,IAAE,CAAC,IAAES,MAAG,MAAID,IAAE,OAAO,KAAGA,IAAE,OAAO,IAAER,IAAE,OAAKQ,IAAE,SAAOR,MAAG,CAAC,KAAK,uBAAsB;AAAC,cAAMF,MAAEe,GAAE,OAAO,EAAC,kBAAiBd,IAAE,MAAK,EAAC,CAAC,EAAE,KAAK,IAAK,CAAC,EAAC,OAAMD,IAAC,MAAIA,GAAE;AAAE,aAAK,wBAAsBI,IAAE,KAAM,CAAAH,OAAG,CAACD,IAAE,KAAM,CAAAA,QAAG,KAAK,IAAIA,MAAEC,EAAC,IAAE,IAAK,CAAE;AAAA,MAAC;AAAC,aAAOD,IAAE,IAAI,YAAWU,GAAC,GAAE,MAAK,KAAK,kBAAgBP,IAAE,IAAK,CAAC,EAAC,YAAWH,IAAC,OAAK,EAAC,GAAEA,KAAE,GAAEA,IAAC,EAAG;AAAA,IAAE;AAAC,UAAK,EAAC,QAAOG,KAAE,gBAAeC,KAAE,sBAAqBM,IAAC,IAAE,GAAE,KAAK,MAAM,YAAWT,IAAE,CAAC,GAAEU,MAAEI,GAAE,OAAO,EAAC,kBAAiBd,IAAE,MAAK,GAAE,QAAOE,IAAC,CAAC;AAAE,KAAC,MAAIQ,IAAE,OAAO,KAAGA,IAAE,OAAO,IAAET,IAAE,OAAKS,IAAE,SAAOT,MAAG,KAAK,wBAAsBQ,KAAEV,IAAE,IAAI,YAAWW,GAAC,GAAE,KAAK,kBAAgBP,OAAG,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAJ7yP;AAI8yP,UAAK,EAAC,OAAMJ,KAAE,WAAUC,GAAC,IAAE;AAAK,QAAG,UAAQD,IAAE,OAAO,SAAS,QAAM;AAAG,UAAK,EAAC,MAAKE,IAAC,IAAEF,IAAE,UAASG,OAAE,KAAAF,GAAE,KAAK,gBAAP,mBAAoB;AAAc,QAAG,GAAEE,OAAA,gBAAAA,IAAG,YAASD,IAAE,UAAQC,IAAE,MAAO,CAAC,EAAC,OAAMH,IAAC,GAAEC,OAAI,KAAK,IAAID,MAAEE,IAAED,EAAC,EAAE,KAAK,IAAE,IAAK,GAAG,QAAM;AAAG,UAAMG,MAAE,CAAC;AAAE,aAAQM,MAAE,GAAEA,MAAER,IAAE,SAAO,GAAEQ,MAAI,CAAAN,IAAE,KAAK,KAAK,MAAM,KAAGF,IAAEQ,GAAC,EAAE,aAAWR,IAAEQ,MAAE,CAAC,EAAE,UAAU,IAAE,EAAE;AAAE,WAAON,IAAE,KAAM,CAAAJ,QAAGA,QAAIA,IAAE,CAAC,CAAE;AAAA,EAAC;AAAA,EAAC,2BAA0B;AAAC,UAAK,EAAC,eAAcA,IAAC,IAAE,KAAK,MAAM,WAAW,aAAYC,KAAED,IAAEA,IAAE,SAAO,CAAC;AAAE,YAAOC,GAAE,SAAOA,GAAE,SAAO,MAAIA,GAAE,SAAOA,GAAE,SAAO,KAAG,KAAG,IAAE;AAAA,EAAE;AAAA,EAAC,MAAM,kBAAkBD,KAAEC,IAAE;AAAC,SAAK,gBAAgB,WAAW,KAAK,mBAAmBD,KAAEC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,mBAAmBD,KAAEC,IAAE;AAJn6Q;AAIo6Q,QAAG,CAAC,KAAK,YAAY,IAAID,IAAE,IAAI,EAAE,GAAE;AAAC,UAAG;AAAC,cAAMC,KAAE,MAAM,KAAK,YAAY,KAAKD,IAAE,GAAG,GAAEI,MAAE,KAAK,YAAY;AAAE,YAAIM,MAAE,CAAC,KAAK,wBAAsB,KAAK,UAAU,oBAAkB,CAAC,KAAK;AAAwB,YAAG,KAAK,0BAAwB,CAAC,KAAK,UAAU,UAAQ,MAAI,KAAK,YAAY,QAAO;AAAC,UAAAA,MAAE;AAAG,cAAG;AAAC,kBAAM,KAAK,cAAa,UAAK,qBAAL,mBAAuB,MAAM;AAAA,UAAC,SAAOP,KAAE;AAAC,cAAEA,GAAC,KAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAMA,GAAC;AAAA,UAAC;AAAC,eAAK,yBAAuB;AAAA,QAAE;AAAC,SAAC,KAAK,yBAAyB,KAAG,eAAa,KAAK,QAAM,KAAK,UAAU,oBAAkB,QAAM,KAAK,qBAAmB,KAAK,wBAAwB;AAAE,cAAMG,KAAE,KAAK,cAAc,cAAcR,IAAEE,IAAE,GAAG,GAAEY,MAAE,KAAK,cAAc,kBAAkBZ,IAAE,GAAG;AAAE,cAAM,KAAK,iBAAiBA,KAAE,EAAC,QAAOC,IAAE,kBAAiB,KAAK,mBAAkB,wBAAuB,KAAK,yBAAwB,WAAUS,KAAE,SAAQN,KAAE,QAAOE,IAAE,YAAWM,IAAC,CAAC,GAAEZ,IAAE,KAAK,UAAU,MAAI,KAAK,UAAU,cAAc,CAAE,GAAE,KAAK,UAAU,SAASA,GAAC;AAAA,MAAC,SAAOG,KAAE;AAAC,UAAEA,GAAC,KAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAMA,GAAC;AAAA,MAAC;AAAC,WAAK,UAAU,cAAc;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,aAAaH,KAAE;AAAC,QAAG,MAAI,KAAK,UAAU,SAAS,OAAO;AAAO,UAAM,KAAK,MAAM,eAAe,GAAE,KAAK,UAAU,mBAAiB,MAAM,KAAK,8BAA8BA,GAAC,KAAG,KAAK,wBAAwB,GAAE,KAAK,0BAAwB;AAAM,UAAMC,KAAE,KAAK,UAAU,SAAS,IAAK,OAAMD,QAAG,KAAK,+BAA+BA,KAAE,EAAC,OAAM,KAAK,mBAAkB,QAAO,KAAK,wBAAuB,CAAC,CAAE;AAAE,UAAM,EAAEC,EAAC,GAAE,KAAK,UAAU,cAAc;AAAA,EAAC;AAAA,EAAC,MAAM,8BAA8BD,KAAE;AAAC,UAAMC,KAAE,EAAC,eAAc,KAAK,gBAAgB,KAAK,YAAY,KAAK,GAAE,YAAW,KAAK,uBAAsB,QAAOD,IAAC,GAAEE,MAAE,MAAM,KAAK,MAAM,YAAY,KAAK,UAAU,KAAK,QAAO,KAAK,UAAU,KAAK,OAAM,KAAK,UAAU,KAAK,QAAOD,EAAC;AAAE,QAAG,CAACC,OAAG,CAACA,IAAE,WAAW;AAAO,UAAK,EAAC,YAAWC,IAAC,IAAE,KAAK,aAAYC,MAAE,KAAK,YAAY,GAAEM,MAAE,KAAK,MAAM,WAAW,wBAAwB,EAAC,YAAWH,GAAEL,IAAE,YAAWE,GAAC,GAAE,OAAM,KAAK,UAAU,KAAK,iBAAiB,cAAa,YAAW,EAAC,GAAED,KAAE,GAAEA,IAAC,GAAE,SAAQC,IAAC,CAAC;AAAE,KAAC,KAAK,yBAAyB,KAAGM,OAAG,cAAYA,IAAE,QAAM,KAAK,MAAM,YAAU,qBAAmB,KAAK,MAAM,SAAS,SAAOA,IAAE,SAAOA,IAAE,OAAO,IAAK,CAAAV,QAAG,MAAIA,GAAE,GAAEU,IAAE,SAAO,KAAK,MAAM,MAAIA,IAAE,MAAM,GAAEA,IAAE,SAAO,KAAK,MAAM,MAAIA,IAAE,MAAM,IAAG,KAAK,0BAAwBA;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,UAAK,EAAC,YAAWV,IAAC,IAAE,KAAK,aAAYC,KAAE,KAAK,YAAY;AAAE,SAAK,oBAAkB,KAAK,MAAM,WAAW,wBAAwB,EAAC,YAAW,MAAK,OAAM,KAAK,UAAU,KAAK,iBAAiB,cAAa,YAAW,EAAC,GAAED,KAAE,GAAEA,IAAC,GAAE,SAAQC,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BD,MAAE,OAAG;AAAC,UAAK,EAAC,OAAMC,IAAE,WAAUC,IAAC,IAAE,MAAK,EAAC,KAAIC,KAAE,QAAOC,IAAC,IAAEH,IAAE,EAAC,4BAA2BS,IAAC,IAAET,GAAE,4BAA4B,EAAC,4BAA2BA,GAAE,4BAA2B,YAAWC,IAAE,WAAU,CAAC,GAAES,MAAEP,IAAE,WAAW,uBAAqBA,IAAE,cAAcM,GAAC,IAAE,MAAKJ,KAAEI,GAAEP,KAAEQ,GAAC;AAAE,QAAGL,OAAI,KAAK,wBAAuB;AAAC,UAAG,QAAM,KAAK,0BAAwBG,GAAE,KAAK,wBAAuB,KAAK,qBAAqB,GAAE,KAAK,wBAAsBO,GAAEV,IAAEF,IAAE,UAAU,GAAEJ,KAAE;AAAC,cAAK,EAAC,MAAKA,IAAC,IAAEE,KAAED,KAAE,KAAK,cAAc,uBAAuBD,IAAE,KAAK,GAAEG,MAAE,KAAK,gBAAgBF,GAAE,KAAK;AAAE,UAAEK,IAAE,KAAK,uBAAsBN,IAAE,QAAOA,IAAE,YAAWG,KAAEC,IAAE,SAAS,QAAQ;AAAA,MAAC;AAAC,WAAK,yBAAuBE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,YAAW;AAAC,QAAG,CAAC,KAAK,SAAS;AAAO,UAAM,KAAK,MAAM,eAAe,GAAE,KAAK,UAAU,oBAAkB,KAAK,wBAAwB,GAAE,KAAK,0BAA0B,IAAE,GAAE,KAAK,YAAY,MAAM;AAAE,UAAMN,MAAE,CAAC;AAAE,SAAK,yBAAuB,KAAK,UAAU,oBAAkB,CAAC,KAAK,sBAAqB,KAAK,cAAc,MAAM,QAAS,CAAAC,OAAGD,IAAE,KAAK,KAAK,kBAAkBC,EAAC,CAAC,CAAE,GAAE,MAAM,EAAED,GAAC;AAAA,EAAC;AAAC;AAAEA,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,eAAc,MAAM,GAAEC,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,0BAAyB,MAAM,GAAEC,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,YAAW,MAAM,GAAEC,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,aAAY,MAAM,GAAEC,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,SAAQ,MAAM,GAAEC,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,aAAY,MAAM,GAAEC,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,QAAO,MAAM,GAAEC,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,yBAAwB,IAAI,GAAEC,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,wBAAuB,IAAI,GAAEC,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,cAAa,MAAM,GAAEC,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,YAAW,IAAI,GAAEA,KAAEC,GAAE,CAAC,EAAE,uDAAuD,CAAC,GAAED,EAAC;;;ACA91X,IAAIkB,MAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK;AAAA,EAAQ;AAAA,EAAC,SAAQ;AAAC,UAAM,OAAO,GAAE,KAAK,YAAU,IAAIC,IAAE,KAAK,aAAa,GAAE,KAAK,UAAU,uBAAqB,KAAK,uBAAsB,KAAK,+BAA+B;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAM,OAAO,GAAE,KAAK,UAAU,kBAAkB,GAAE,KAAK,YAAU;AAAA,EAAI;AAAA,EAAC,2BAA0B;AAAC,WAAO,KAAK,yBAAuB,KAAK,MAAM,WAAW,oBAAkB,EAAE,eAAa,KAAK,MAAM,iBAAeC,GAAE,KAAK,KAAK;AAAA,EAAE;AAAA,EAAC,UAAUC,KAAED,KAAE;AAAC,WAAO,KAAK,MAAM,UAAUC,IAAE,OAAMA,IAAE,KAAIA,IAAE,KAAID,GAAC;AAAA,EAAC;AAAA,EAAC,iCAAgC;AAAC,UAAK,EAAC,QAAOC,KAAE,MAAKD,IAAC,IAAE,KAAK,OAAM,EAAC,WAAUE,GAAC,IAAE;AAAK,QAAG,eAAaD,IAAE,iBAAe,UAAQD,IAAE,QAAOE,GAAE,sBAAoB,MAAKA,GAAE,SAAS,QAAS,CAAAD,QAAG;AAAC,YAAK,EAAC,QAAOD,IAAC,IAAEC;AAAE,MAAAD,QAAIA,IAAE,YAAU,MAAGA,IAAE,YAAU,OAAGA,IAAE,cAAYA,IAAE,kBAAkB,GAAEA,IAAE,gBAAc;AAAA,IAAM,CAAE,GAAE,MAAK,KAAK,uBAAqB;AAAM,UAAMG,MAAE,KAAK,sBAAqB,EAAC,gBAAeC,KAAE,gBAAeL,IAAC,IAAEE,KAAEI,MAAED,IAAE,gBAAc,CAACL,OAAGA,IAAE,QAAQ,UAAQ,IAAGF,MAAEQ,MAAED,IAAE,0BAA0B,IAAE,MAAK,EAAC,UAASE,GAAC,IAAE,KAAK,OAAMC,KAAE,CAACF,OAAG,EAACR,OAAA,gBAAAA,IAAG,UAAU,WAAQ,qBAAmBS,GAAE,QAAMA,GAAE,0BAAwB,CAAC,KAAK,yBAAyB;AAAE,IAAAJ,GAAE,sBAAoBK,KAAE,OAAKV;AAAE,UAAMW,KAAE,QAAMJ,MAAE,OAAKF,GAAE,sBAAoB,QAAM;AAAM,IAAAA,GAAE,SAAS,QAAS,CAAAD,QAAG;AAAC,YAAK,EAAC,QAAOD,IAAC,IAAEC;AAAE,MAAAD,QAAIA,IAAE,YAAUG,QAAIK,IAAER,IAAE,YAAU,OAAGA,IAAE,mBAAiB;AAAA,IAAK,CAAE,GAAE,KAAK,uBAAqBQ;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiBP,KAAEC,IAAE;AAAC,UAAMC,MAAE,KAAK,YAAY,GAAEC,MAAE,KAAK,uBAAuB,GAAEL,MAAE,KAAK,yBAAyB,GAAE,EAAC,QAAOM,KAAE,wBAAuBR,KAAE,WAAUS,IAAE,QAAOC,IAAE,YAAWC,GAAC,IAAEN,IAAEO,KAAE,KAAK,UAAU,mBAAiBZ,MAAEK,GAAE,kBAAiB,EAAC,QAAOQ,GAAC,IAAET;AAAE,QAAG,CAACS,GAAE,GAAEA,GAAE,CAAC,IAAEH,IAAEG,GAAE,aAAWF,IAAEH,OAAG,EAAEA,GAAC,KAAG,EAAEA,IAAE,UAAU,GAAE;AAAC,YAAMJ,MAAE,EAAC,QAAOI,IAAE,QAAO,YAAWA,IAAE,WAAU;AAAE,UAAGK,GAAE,eAAaT,KAAEF,IAAE,CAAAW,GAAE,SAAOL,IAAE,YAAWK,GAAE,qBAAmB;AAAA,WAAO;AAAC,cAAMV,MAAE,MAAM,KAAK,MAAM,cAAcC,KAAE,eAAYJ,OAAA,gBAAAA,IAAG,QAAKA,MAAE,MAAM;AAAE,QAAAa,GAAE,SAAOV,KAAEU,GAAE,qBAAmB;AAAA,MAAE;AAAC,MAAAA,GAAE,uBAAqBX,MAAEU,KAAE,MAAKV,MAAEW,GAAE,kBAAgBA,GAAE,gBAAcL,IAAE,iBAAeK,GAAE,gBAAc;AAAA,IAAI,OAAK;AAAC,YAAMT,MAAE,KAAK,0BAA0B;AAAE,MAAAS,GAAE,SAAOT,KAAES,GAAE,uBAAqBX,MAAEU,KAAE,MAAKC,GAAE,gBAAc;AAAA,IAAI;AAAC,IAAAA,GAAE,UAAQX,MAAEI,MAAE,MAAKO,GAAE,QAAM,KAAK,cAAc,SAAS,KAAK,CAAC,GAAEA,GAAE,SAAO,KAAK,cAAc,SAAS,KAAK,CAAC,GAAEA,GAAE,gBAAcN,KAAEM,GAAE,YAAUJ,IAAEI,GAAE,kBAAkB;AAAA,EAAC;AAAA,EAAC,MAAM,+BAA+BT,KAAEC,IAAE;AAAC,UAAK,EAAC,OAAMC,KAAE,QAAOC,IAAC,IAAEF,IAAEH,MAAE,KAAK,YAAY,GAAEM,MAAE,KAAK,uBAAuB,GAAER,MAAE,KAAK,yBAAyB,GAAE,EAAC,QAAOS,GAAC,IAAEL,KAAE,EAAC,cAAaM,GAAC,IAAED;AAAE,KAACT,OAAG,EAAEU,EAAC,KAAGD,GAAE,SAAO,MAAM,KAAK,MAAM,cAAcC,IAAE,eAAYH,OAAA,gBAAAA,IAAG,QAAKA,MAAE,MAAM,GAAEE,GAAE,qBAAmB,SAAKA,GAAE,sBAAoB,EAAEC,EAAC,MAAID,GAAE,SAAOC,GAAE,aAAYD,GAAE,qBAAmB,QAAIA,GAAE,uBAAqBT,MAAE,KAAK,UAAU,mBAAiBO,MAAED,MAAE,MAAKG,GAAE,UAAQT,MAAEE,MAAE,MAAKO,GAAE,gBAAcD,KAAEC,GAAE,YAAU;AAAA,EAAE;AAAA,EAAC,yBAAwB;AAAC,UAAML,MAAE,KAAK,MAAM,SAAS;AAAK,QAAG,sBAAoBA,OAAG,mBAAiBA,OAAG,mBAAiBA,IAAE,QAAM;AAAU,UAAK,EAAC,eAAcD,IAAC,IAAE,KAAK,OAAM,EAAC,UAASE,GAAC,IAAE,KAAK;AAAM,WAAM,qBAAmBA,GAAE,QAAM,QAAMA,GAAE,YAAU,eAAaF,OAAG,YAAUA,MAAE,aAAW,YAAUA;AAAA,EAAC;AAAC;AAAEC,GAAE,CAAC,EAAE,CAAC,GAAEJ,IAAE,WAAU,aAAY,MAAM,GAAEI,GAAE,CAAC,EAAE,CAAC,GAAEJ,IAAE,WAAU,SAAQ,MAAM,GAAEI,GAAE,CAAC,EAAE,CAAC,GAAEJ,IAAE,WAAU,QAAO,MAAM,GAAEA,MAAEI,GAAE,CAAC,EAAE,gDAAgD,CAAC,GAAEJ,GAAC;AAAE,IAAMS,KAAET;;;ACA9jH,IAAMc,MAAN,cAAgBA,GAAC;AAAA,EAAC,YAAYC,IAAEC,KAAEC,KAAEC,KAAEJ,KAAEK,KAAEC,KAAE,MAAK;AAAC,UAAML,IAAEC,KAAEC,KAAEC,KAAEJ,KAAEK,GAAC,GAAE,KAAK,WAAS,IAAIE,GAAED,EAAC,GAAE,KAAK,SAAS,aAAW,CAACN,KAAEK,GAAC,GAAE,KAAK,SAAS,KAAK,WAAW,MAAI,KAAK,MAAM,CAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAM,QAAQ,GAAE,KAAK,SAAS,QAAQ,GAAE,KAAK,WAAS,MAAK,KAAK,QAAM;AAAA,EAAI;AAAA,EAAC,IAAI,WAAWJ,IAAE;AAAC,SAAK,SAAS,aAAWA;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,SAAS;AAAA,EAAU;AAAA,EAAC,oBAAmB;AAAC,WAAM,EAAC,KAAIE,GAAE,GAAE,UAASA,GAAE,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,KAAE;AAAC,UAAM,aAAaA,GAAC;AAAE,UAAMK,MAAE,KAAK,cAAYL,IAAE,aAAWA,IAAE,aAAYC,MAAE,KAAK,WAAW,UAAS,CAACJ,KAAEK,GAAC,IAAE,KAAK,SAAS,QAAOC,KAAE,CAAC,KAAK,IAAEN,MAAE,KAAK,YAAW,KAAK,IAAEK,MAAE,KAAK,UAAU,GAAE,CAACI,IAAEC,GAAC,IAAEP,IAAE,mBAAmB,CAAC,GAAE,CAAC,GAAEG,EAAC,GAAE,EAAC,gBAAeK,GAAC,IAAE,KAAK,SAAS,sBAAqBC,KAAE,KAAK,OAAO,KAAK,QAAM,KAAK,SAAS,OAAO,CAAC,KAAGD,EAAC,IAAEA,IAAEE,KAAE,KAAK,OAAO,KAAK,SAAO,KAAK,SAAS,OAAO,CAAC,KAAGF,EAAC,IAAEA,IAAEG,KAAEF,KAAE,KAAK,SAAOJ,KAAEO,KAAEF,KAAE,KAAK,SAAOL;AAAE,IAAAN,GAAEE,KAAEU,IAAE,GAAE,GAAE,GAAEC,IAAE,GAAEN,IAAEC,KAAE,CAAC,GAAE,EAAE,KAAK,WAAW,KAAIP,IAAE,iBAAgBC,GAAC,GAAE,KAAK,SAAS,WAAW,MAAI,KAAK,WAAW;AAAA,EAAG;AAAA,EAAC,WAAU;AAAC,SAAK,SAAS,QAAM,KAAK;AAAA,EAAK;AAAA,EAAC,WAAU;AAAC,SAAK,SAAS,QAAM;AAAA,EAAI;AAAC;;;ACA3/B,IAAMY,MAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,uBAAqB,OAAG,KAAK,cAAY,CAAC,UAAU;AAAA,EAAC;AAAA,EAAC,WAAWC,KAAE;AAAC,UAAMC,KAAE,KAAK,cAAc,cAAc,EAAE,GAAED,GAAC,GAAE,CAACD,KAAED,GAAC,IAAE,KAAK,cAAc,SAAS,MAAKI,MAAE,KAAK,cAAc,kBAAkBF,IAAE,KAAK;AAAE,WAAO,IAAIG,IAAEH,KAAEE,KAAED,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEF,KAAED,GAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBM,KAAE;AAAC,UAAMD,MAAEC,IAAE,mBAAmB,EAAC,MAAK,qBAAoB,SAAQ,CAACC,EAAC,GAAE,QAAO,MAAI,KAAK,SAAS,IAAK,CAAAD,QAAGA,IAAE,QAAS,GAAE,WAAU,EAAE,IAAG,CAAC;AAAE,WAAM,CAAC,GAAG,MAAM,oBAAoBA,GAAC,GAAED,GAAC;AAAA,EAAC;AAAA,EAAC,SAASC,KAAE;AAAC,SAAK,WAASA,IAAE,cAAY,EAAE,OAAK,KAAK,YAAY,QAAS,CAAAJ,QAAG;AAAC,MAAAI,IAAE,aAAWJ,KAAE,MAAM,SAASI,GAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;;;ACA1K,IAAIE,KAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,UAAQ,MAAK,KAAK,OAAK;AAAA,EAAU;AAAA,EAAC,2BAA0B;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,MAAM,UAAUC,KAAEC,IAAE;AAAC,IAAAA,KAAE,EAAC,GAAGA,IAAE,eAAc,WAAU,iCAAgC,KAAE;AAAE,UAAMC,MAAE,MAAM,KAAK,MAAM,UAAUF,IAAE,OAAMA,IAAE,KAAIA,IAAE,KAAIC,EAAC;AAAE,WAAM,oBAAkB,KAAK,MAAM,WAAW,aAAUC,OAAA,gBAAAA,IAAG,gBAAaA,IAAE,aAAW,MAAM,KAAK,MAAM,uBAAuBA,IAAE,YAAWD,EAAC,IAAGC;AAAA,EAAC;AAAA,EAAC,iBAAiBF,KAAEE,KAAE;AAAC,UAAMC,MAAED,IAAE,kBAAiB,EAAC,UAASE,IAAC,IAAEJ;AAAE,IAAAI,IAAE,MAAIJ,IAAE,KAAII,IAAE,QAAM,KAAK,cAAc,SAAS,KAAK,CAAC,GAAEA,IAAE,SAAO,KAAK,cAAc,SAAS,KAAK,CAAC;AAAE,UAAK,EAAC,gBAAeC,IAAC,IAAEF,KAAE,EAAC,QAAOG,GAAC,IAAEJ;AAAE,QAAGE,IAAE,SAAO,KAAK,qBAAqBA,IAAE,KAAIC,GAAC,GAAE,EAAEC,EAAC,KAAG,EAAEA,GAAE,UAAU,GAAE;AAAC,YAAMN,MAAE,EAAC,QAAOM,GAAE,QAAO,YAAWA,GAAE,WAAU;AAAE,MAAAF,IAAE,eAAaJ,KAAEI,IAAE,uBAAqBD,KAAEC,IAAE,SAAO,KAAK,uBAAuBE,GAAE,YAAWH,KAAEC,IAAE,MAAM;AAAA,IAAC,OAAK;AAAC,YAAMJ,MAAE,CAAC,KAAK,OAAO,KAAK,cAAc,SAAS,CAAC,IAAEI,IAAE,OAAO,CAAC,KAAGC,GAAC,GAAE,KAAK,OAAO,KAAK,cAAc,SAAS,CAAC,IAAED,IAAE,OAAO,CAAC,KAAGC,GAAC,CAAC,GAAEJ,KAAE,KAAK,0BAA0BD,GAAC;AAAE,MAAAI,IAAE,SAAOH,IAAEG,IAAE,uBAAqBD;AAAA,IAAC;AAAC,WAAOC,IAAE,cAAc,GAAE,QAAQ,QAAQ;AAAA,EAAC;AAAA,EAAC,+BAA+BJ,KAAEE,KAAE;AAJpxD;AAIqxD,UAAMC,MAAED,IAAE,OAAM,EAAC,gBAAeE,IAAC,IAAED,KAAE,EAAC,UAASE,IAAC,IAAEL;AAAE,IAAAK,IAAE,SAAO,KAAK,qBAAqBA,IAAE,KAAID,GAAC;AAAE,UAAME,KAAED,IAAE,qBAAqB;AAAe,IAAAA,IAAE,uBAAqBF;AAAE,UAAMI,OAAE,KAAAF,IAAE,iBAAF,mBAAgB;AAAW,WAAO,EAAEE,GAAC,KAAGD,OAAIF,QAAIC,IAAE,SAAO,KAAK,uBAAuBE,KAAEF,IAAE,sBAAqBA,IAAE,MAAM,IAAG,QAAQ,QAAQ;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAM,OAAO,GAAE,KAAK,YAAU,IAAID,IAAE,KAAK,aAAa,GAAE,KAAK,UAAU,uBAAqB,KAAK,uBAAsB,KAAK,kBAAkB,KAAK,MAAM,QAAQ,GAAE,KAAK,UAAQ,EAAG,MAAI,KAAK,MAAM,UAAW,CAAAJ,QAAG,KAAK,kBAAkBA,GAAC,CAAE;AAAA,EAAC;AAAA,EAAC,SAAQ;AAJ50E;AAI60E,UAAM,OAAO,GAAE,KAAK,UAAU,kBAAkB,IAAE,UAAK,YAAL,mBAAc,UAAS,KAAK,UAAQ,MAAK,KAAK,YAAU;AAAA,EAAI;AAAA,EAAC,qBAAqBA,KAAEC,IAAE;AAAC,UAAMC,MAAEF,IAAE,MAAI,KAAK,cAAc,SAAS,KAAK,CAAC,IAAEC,IAAEE,MAAEH,IAAE,MAAI,KAAK,cAAc,SAAS,KAAK,CAAC,IAAEC;AAAE,WAAM,CAACC,MAAED,KAAE,IAAEA,KAAEC,MAAE,CAACA,KAAEC,MAAEF,KAAE,IAAEA,KAAEE,MAAE,CAACA,GAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBH,KAAEC,IAAEC,KAAE;AAAC,UAAK,EAAC,gBAAeC,IAAC,IAAEF;AAAE,WAAOO,GAAER,KAAE,aAAYG,KAAED,GAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBF,KAAE;AAAC,uBAAiBA,IAAE,SAAO,KAAK,UAAU,cAAY,gBAAcA,IAAE,QAAM,CAAC,UAAS,UAAU,IAAE,oBAAkBA,IAAE,QAAM,CAAC,QAAQ,IAAE,CAAC,UAAU;AAAA,EAAE;AAAC;AAAEA,GAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,aAAY,MAAM,GAAEE,GAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,SAAQ,MAAM,GAAEE,GAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAEE,GAAE,CAAC,EAAE,oDAAoD,CAAC,GAAEF,EAAC;AAAE,IAAMW,MAAEX;;;ACAp0E,IAAMY,KAAE,CAAAA,OAAG;AAAC,MAAIC,KAAE,cAAcD,GAAC;AAAA,IAAC,cAAa;AAAC,YAAM,GAAG,SAAS,GAAE,KAAK,qBAAmB,WAAU,KAAK,QAAM,MAAK,KAAK,OAAK,MAAK,KAAK,WAAS;AAAA,IAAI;AAAA,IAAC,IAAI,aAAY;AAAC,aAAO,KAAK,eAAe;AAAA,IAAC;AAAA,IAAC,iBAAgB;AAAC,aAAO,KAAK,kBAAkB,KAAK,KAAK,gBAAgB;AAAA,IAAC;AAAA,IAAC,IAAI,mBAAkB;AAAC,aAAO,KAAK,MAAM,YAAU,4BAA2B,KAAK,MAAM,YAAU,KAAK,MAAM,SAAS;AAAA,IAAsB;AAAA,IAAC,IAAI,sBAAqB;AAAC,aAAO,EAAE,EAAE,KAAK,MAAM,UAAU,GAAE,KAAK,KAAK,kBAAiB,IAAE;AAAA,IAAC;AAAA,IAAC,yBAAyBE,KAAE;AAAC,aAAM,CAAC,CAAC,KAAK,kBAAkBA,GAAC;AAAA,IAAC;AAAA,IAAC,kBAAkBA,KAAE;AAAC,YAAMC,KAAE,EAAE,KAAK,MAAM,UAAU,GAAEC,MAAE,EAAED,IAAED,KAAE,KAAE;AAAE,aAAO,EAAEC,IAAED,KAAEE,GAAC;AAAA,IAAC;AAAA,IAAC,MAAM,mBAAmBF,KAAEG,KAAE;AAAC,YAAK,EAAC,OAAMC,IAAC,IAAE;AAAK,UAAG,CAACJ,IAAE,OAAM,IAAIK,GAAE,2CAA0C,iCAAgC,EAAC,OAAMD,IAAC,CAAC;AAAE,YAAK,EAAC,cAAaE,IAAC,IAAEF,KAAEG,KAAEF,GAAED,KAAED,GAAC;AAAE,UAAG,CAACG,OAAG,EAAEC,EAAC,EAAE,OAAM,IAAIF,GAAE,2CAA0C,kDAAiD,EAAC,cAAaC,KAAE,eAAcC,GAAC,CAAC;AAAE,YAAMC,KAAE,CAAC,GAAE,EAAC,OAAMC,IAAE,aAAYX,GAAC,IAAE,MAAMM,IAAE,SAASJ,KAAE,EAAC,YAAW,KAAK,WAAU,CAAC;AAAE,UAAID,KAAE;AAAG,UAAGU,MAAGA,GAAE,QAAO;AAAC,QAAAV,KAAE,mBAAiBK,IAAE,QAAMA,IAAE,gBAAgB,KAAG,QAAMK,GAAE,CAAC,IAAEA,GAAE,IAAK,CAAAT,QAAGI,IAAE,qBAAqBJ,GAAC,CAAE,EAAE,KAAK,IAAI,IAAES,GAAE,KAAK,IAAI;AAAE,cAAMT,MAAE,EAAC,UAAS,EAAC;AAAE,QAAAA,IAAE,0BAA0B,IAAED;AAAE,cAAMG,MAAEE,IAAE,WAAW;AAAe,YAAG,EAAEF,GAAC,GAAE;AAAC,gBAAK,EAAC,QAAOD,IAAE,UAASE,IAAC,IAAED,KAAEG,MAAEJ,GAAE,KAAM,CAAC,EAAC,MAAKD,IAAC,MAAI,YAAUA,IAAE,YAAY,CAAE,GAAEU,MAAEL,MAAEF,IAAE,KAAM,CAAAH,QAAG,OAAOA,IAAE,WAAWK,IAAE,IAAI,CAAC,MAAIN,EAAE,IAAE;AAAK,cAAGW;AAAE,uBAAUR,OAAKQ,IAAE,WAAW,KAAGA,IAAE,WAAW,eAAeR,GAAC,GAAE;AAAC,cAAAF,IAAE,KAAK,qBAAmBE,GAAC,IAAEQ,IAAE,WAAWR,GAAC;AAAA,YAAC;AAAA;AAAA,QAAC;AAAC,cAAMC,MAAEC,IAAE,WAAW;AAAS,4BAAkBD,OAAG,gBAAcA,QAAIH,IAAE,kBAAkB,IAAEF,MAAA,gBAAAA,GAAI,IAAGE,IAAE,kBAAkB,IAAEF,MAAA,gBAAAA,GAAI;AAAI,cAAMO,MAAE,IAAIM,GAAE,KAAK,WAAW,MAAM,GAAE,MAAKX,GAAC;AAAE,QAAAK,IAAE,QAAMD,KAAEC,IAAE,cAAYA,IAAE,OAAMG,GAAE,KAAKH,GAAC;AAAA,MAAC;AAAC,aAAOG;AAAA,IAAC;AAAA,EAAC;AAAE,SAAOR,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,SAAQ,MAAM,GAAEC,GAAE,CAAC,EAAEY,EAAC,CAAC,GAAEb,GAAE,WAAU,cAAa,MAAM,GAAEC,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,QAAO,MAAM,GAAEC,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,cAAa,IAAI,GAAEC,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,YAAW,MAAM,GAAEC,GAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAED,GAAE,WAAU,oBAAmB,IAAI,GAAEC,GAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,uBAAsB,IAAI,GAAEA,KAAEC,GAAE,CAAC,EAAE,wCAAwC,CAAC,GAAED,EAAC,GAAEA;AAAC;;;ACA/tD,IAAIc,KAAE,cAAcA,GAAEC,GAAED,GAAEE,EAAC,CAAC,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,yBAAuB,MAAG,KAAK,wBAAsB,MAAG,KAAK,UAAQ;AAAA,EAAI;AAAA,EAAC,IAAI,wBAAuB;AAAC,WAAO,KAAK;AAAA,EAAsB;AAAA,EAAC,IAAI,sBAAsBC,KAAE;AAAC,SAAK,yBAAuBA,KAAE,KAAK,WAAS,2BAA0B,KAAK,YAAU,KAAK,QAAQ,wBAAsBA;AAAA,EAAE;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,KAAK;AAAA,EAAsB;AAAA,EAAC,IAAI,qBAAqBA,KAAE;AAAC,SAAK,wBAAsBA,KAAE,KAAK,WAAS,0BAAyB,KAAK,YAAU,KAAK,QAAQ,uBAAqBA;AAAA,EAAE;AAAA,EAAC,OAAOA,KAAE;AAJnpD;AAIopD,eAAK,YAAL,mBAAc,OAAOA,MAAG,KAAK,aAAa,UAAU;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAM,CAAC,KAAK,WAAS,KAAK,QAAQ;AAAA,EAAQ;AAAA,EAAC,SAAQ;AAAC,SAAK,MAAM,8BAA8B,GAAE,KAAK,eAAe,GAAE,KAAK,iBAAiB,CAAC,EAAG,MAAI;AAAC,YAAK,EAAC,OAAMA,IAAC,IAAE;AAAK,aAAM,EAAC,SAAQA,IAAE,SAAQ,UAASA,IAAE,UAAS,eAAcA,IAAE,eAAc,4BAA2BA,IAAE,4BAA2B,gBAAe,mBAAiBA,IAAE,OAAKA,IAAE,iBAAe,KAAI;AAAA,IAAC,GAAI,CAACA,KAAEC,QAAI;AAJ1kE;AAI2kE,YAAMH,MAAEE,IAAE,mBAAgBC,OAAA,gBAAAA,IAAG,mBAAgB,eAAaD,IAAE,iBAAe,gBAAaC,OAAA,gBAAAA,IAAG,mBAAgBC,GAAE,KAAK,KAAK,GAAEC,MAAEH,IAAE,cAAWC,OAAA,gBAAAA,IAAG,eAAU,KAAAA,OAAA,gBAAAA,IAAG,aAAH,mBAAa,YAAO,KAAAD,IAAE,aAAF,mBAAY;AAAK,MAAAG,OAAG,KAAK,eAAe;AAAE,YAAMC,MAAEJ,IAAE,gCAA6BC,OAAA,gBAAAA,IAAG,6BAA2BI,MAAEL,IAAE,oBAAiBC,OAAA,gBAAAA,IAAG,iBAAeF,KAAEM,OAAG,CAAC,KAAK,wBAAuBC,KAAEF,OAAGN,OAAGK,OAAGJ;AAAE,WAAK,QAAQ,gBAAgB,EAAC,SAAQO,IAAE,WAAUD,IAAC,CAAC,EAAE,MAAO,CAAAL,QAAG;AAAC,UAAEA,GAAC,KAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAMA,GAAC;AAAA,MAAC,CAAE,GAAE,KAAK,aAAa,UAAU;AAAA,IAAC,CAAE,GAAE,EAAG,MAAI,KAAK,MAAM,aAAW,UAAW,CAAAA,QAAG;AAAC,WAAK,QAAQ,UAAU,YAAUA;AAAA,IAAC,GAAGO,EAAC,GAAE,EAAG,MAAI,KAAK,MAAM,UAAQ,MAAO,CAAAP,QAAG;AAAC,WAAK,QAAQ,UAAU,SAAOA;AAAA,IAAC,GAAGO,EAAC,GAAE,EAAG,MAAI,KAAK,MAAM,0BAAwB,MAAO,CAACP,KAAEC,QAAI;AAAC,YAAK,EAAC,4BAA2BE,IAAC,IAAE,KAAK;AAAM,QAAEA,GAAC,KAAGK,GAAEL,KAAEH,GAAC,MAAIQ,GAAEL,KAAEF,GAAC,MAAI,KAAK,QAAQ,gBAAgB,EAAC,SAAQ,KAAE,CAAC,EAAE,MAAO,CAAAD,QAAG;AAAC,UAAEA,GAAC,KAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAMA,GAAC;AAAA,MAAC,CAAE,GAAE,KAAK,aAAa,UAAU;AAAA,IAAE,GAAG,CAAC,GAAE,EAAG,MAAI,KAAK,YAAa,MAAI;AAAC,WAAK,QAAQ,aAAW,KAAK,YAAW,KAAK,QAAQ,gBAAgB,EAAC,SAAQ,KAAE,CAAC,EAAE,MAAO,CAAAA,QAAG;AAAC,UAAEA,GAAC,KAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAMA,GAAC;AAAA,MAAC,CAAE;AAAA,IAAC,GAAGM,EAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAJprG;AAIqrG,SAAK,MAAM,8BAA8B,GAAE,KAAK,eAAe,KAAK,OAAO,IAAE,UAAK,YAAL,mBAAc,WAAU,KAAK,UAAQ;AAAA,EAAI;AAAA,EAAC,YAAW;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,QAAQ,QAAQ;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQN,KAAES,IAAE;AAAC,WAAM,CAAC,EAAC,MAAK,WAAU,OAAM,KAAK,OAAM,UAAST,KAAE,SAAQ,IAAIU,GAAE,EAAC,YAAW,CAAC,GAAE,UAASV,IAAE,MAAM,EAAC,CAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,UAAQ,KAAK,QAAQ,UAAU,IAAE,QAAQ,QAAQ;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAJrmH;AAIsmH,UAAMA,MAAE,mBAAiB,KAAK,MAAM,SAAS,OAAK,aAAW,WAAS,KAAK,MAAM,SAAS,OAAK,SAAO;AAAS,QAAG,KAAK,SAAQ;AAAC,UAAG,KAAK,QAAQ,SAAOA,IAAE,QAAO,KAAK,KAAK,eAAe,KAAK,OAAO;AAAE,WAAK,eAAe,KAAK,OAAO,IAAE,UAAK,YAAL,mBAAc,WAAU,KAAK,UAAQ;AAAA,IAAI;AAAC,UAAK,EAAC,OAAMC,IAAC,IAAE;AAAK,QAAIQ;AAAE,QAAGA,KAAE,eAAaT,MAAE,IAAIK,IAAE,EAAC,OAAMJ,KAAE,WAAU,KAAI,CAAC,IAAE,WAASD,MAAE,IAAIM,GAAE,EAAC,OAAML,KAAE,WAAU,KAAI,CAAC,IAAE,IAAIU,GAAE,EAAC,OAAMV,KAAE,WAAU,KAAI,CAAC,GAAE,2BAA0BQ,OAAIA,GAAE,wBAAsB,KAAK,yBAAwB,0BAAyBA,OAAIA,GAAE,uBAAqB,KAAK,wBAAuB,iBAAgBA,IAAE;AAAC,YAAK,EAAC,SAAQT,IAAC,IAAE;AAAK,MAAAS,GAAE,cAAYT,OAAG,iBAAgBA,MAAEA,IAAE,cAAY;AAAA,IAAI;AAAC,SAAK,eAAeS,EAAC,GAAE,KAAK,UAAQA,IAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,eAAeT,KAAE;AAAC,IAAAA,OAAG,CAACA,IAAE,aAAWA,IAAE,OAAO,GAAEA,IAAE,WAAS,MAAG,KAAK,UAAU,WAAWA,IAAE,WAAU,CAAC,GAAEA,IAAE,UAAU,YAAU,KAAK,MAAM,WAAUA,IAAE,UAAU,SAAO,KAAK,MAAM;AAAA,EAAO;AAAA,EAAC,eAAeA,KAAE;AAAC,KAAAA,OAAA,gBAAAA,IAAG,cAAW,KAAK,UAAU,YAAYA,IAAE,SAAS,GAAEA,IAAE,OAAO,GAAEA,IAAE,WAAS;AAAA,EAAG;AAAC;AAAEA,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,WAAU,MAAM,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,yBAAwB,IAAI,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,wBAAuB,IAAI,GAAEA,KAAEG,GAAE,CAAC,EAAE,6CAA6C,CAAC,GAAEH,EAAC;AAAE,IAAMe,KAAEf;", "names": ["c", "m", "r", "e", "t", "s", "u", "n", "l", "d", "p", "_", "M", "h", "i", "o", "f", "e", "i", "n", "e", "t", "u", "s", "a", "t", "e", "o", "i", "n", "r", "m", "c", "d", "f", "u", "g", "n", "m", "o", "e", "s", "a", "i", "r", "t", "l", "c", "d", "f", "u", "p", "g", "a", "m", "e", "o", "s", "n", "i", "r", "t", "c", "l", "d", "u", "p", "f", "g", "E", "s", "o", "t", "m", "l", "c", "t", "n", "a", "r", "s", "i", "o", "f", "m", "d", "e", "e", "a", "t", "r", "o", "s", "i", "n", "a", "e", "t", "r", "n", "s", "a", "e", "t", "r", "o", "s", "n", "t", "a", "e", "r", "n", "o", "s", "i", "a", "e", "t", "r", "n", "s", "o", "e", "o", "r", "t", "n", "s", "i", "u", "c", "m", "p", "h", "a", "r", "n", "t", "a", "e", "s", "o", "a", "e", "t", "r", "n", "s", "r", "e", "a", "t", "n", "s", "o", "f", "i", "e", "i", "t", "r", "o", "s", "n", "a", "d", "f", "c", "u", "t", "o", "e", "r", "i", "s", "a", "n", "c", "f", "p", "u", "a", "u", "t", "r", "e", "n", "o", "s", "m", "b", "g", "r", "e", "o", "t", "U", "E", "A", "s", "n", "i", "c", "m", "a", "p", "b", "t", "e", "r", "s", "f", "o", "n", "a", "d", "m", "u", "E", "i", "h", "p", "l", "T", "g", "x", "i", "r", "t", "e", "a", "n", "l", "m", "o", "i", "e", "s", "t", "n", "b", "r", "a", "T", "v", "e", "t", "i", "s", "r", "w", "l", "h", "y", "f", "a", "o", "n", "U", "c", "j", "u", "n", "v", "o", "r", "e", "t", "s", "i", "a", "l", "c", "u", "p", "d", "r", "t", "s", "e", "a", "o", "l", "y", "i", "h", "n", "f", "m", "c", "u", "D", "o", "i", "s", "t", "n", "r", "e", "m", "c", "v", "e", "t", "i", "r", "o", "s", "l", "a", "j", "n", "f", "m", "e", "t", "r", "o", "a", "s", "n", "l", "p", "u", "i", "g", "b", "f", "i", "u", "e", "s", "r", "o", "a", "n", "h", "w", "m", "t", "g", "l", "L"]}