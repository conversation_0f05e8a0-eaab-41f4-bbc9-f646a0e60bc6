<!-- 指令发送 -->
<template>
  <div class="wrapper">
    <CardTable
      class="card-table"
      :config="TableConfig"
    />
    <el-card
      class="box-card mag_Top_10"
      style="height: 300px;overflow: auto;"
    >
      <Form
        ref="refForm"
        :config="addOrUpdateConfig"
      ></Form>
      <br>
      <br>
      <br>
      <el-button-group class="ml-4">
        <el-button
          :icon="Plus"
          type="primary"
          @click="addCommand"
        >
          添加
        </el-button>
        <el-button @click="sendCommand()">
          发送
        </el-button>
        <el-button @click="reset">
          重置
        </el-button>
        <el-button @click="handleDelete()">
          撤销
        </el-button>
      </el-button-group>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { Plus } from '@element-plus/icons-vue'
import Cookies from 'js-cookie'
import { ElMessage } from 'element-plus'
import { commandStatus } from '../data'
import { ICONS } from '@/common/constans/common'
import { IFormIns } from '@/components/type'
import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm } from '@/utils/Message'
import { getOrderRecordList, postOrderRecord, postSendCommand, deleteOrderRecord, getorderRecordType } from '@/api/productionScheduling/schedulingInstructions'
import { getWaterSupplyTree } from '@/api/company_org'
import { pumpManageList } from '@/api/server/index'
import { objectLookup, traverse } from '@/utils/GlobalHelper'
import { removeSlash } from '@/utils/removeIdSlash'

const departmentId = ref(Cookies.get('departmentId') || '')

const { $btnPerms } = useGlobal()

const refForm = ref<IFormIns>()

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  handleSelectChange: val => {
    TableConfig.selectList = val
  },
  selectList: [],
  rowKey: 'id',
  columns: [
    { label: '指令类型', prop: 'typeName' },
    { label: '发送站点', prop: 'sendDeptName' },
    { label: '接收站点', prop: 'receiveDeptName' },
    { label: '指令内容', prop: 'sendContent' },
    { label: '启用泵组', prop: 'enablePumpNames', width: '120px' },
    { label: '关闭泵组', prop: 'disablePumpNames', width: '120px' },
    { label: '执行时间', prop: 'executionTime' },
    { label: '指令备注', prop: 'remark' },
    { label: '指令状态',
      prop: 'commandStatus',
      tag: true,
      tagColor: (row): string => commandStatus[row.commandStatus]?.color || '',
      formatter: val => commandStatus[val.commandStatus]?.value || '' }
  ],
  operationWidth: '160px',
  operations: [
    {
      type: 'primary',
      text: '发送',
      icon: ICONS.SEND,
      perm: $btnPerms('RoleManageEdit'),
      click: row => sendCommand(row)
    },
    {
      type: 'danger',
      text: '撤销',
      perm: $btnPerms('RoleManageDelete'),
      icon: ICONS.DELETE,
      click: row => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const addOrUpdateConfig = reactive<IFormConfig>({
  labelWidth: '100px',
  submit: (params: any) => {
    params.receiveDeptId = params.receiveDeptId.join(',')
    // 处理泵组数据
    if (params.enablePumps && Array.isArray(params.enablePumps)) {
      params.enablePumps = params.enablePumps.join(',')
    }
    if (params.disablePumps && Array.isArray(params.disablePumps)) {
      params.disablePumps = params.disablePumps.join(',')
    }
    postOrderRecord([params]).then(() => {
      ElMessage.success('新增成功')
      refForm.value?.resetForm()
      refreshData()
    }).catch(error => {
      ElMessage.warning(error)
    })
  },
  defaultValue: { sendDeptId: departmentId.value, receiveDeptId: [], enablePumps: [], disablePumps: [] },
  group: [
    {
      fields: [
        {
          xl: 6,
          type: 'select',
          label: '指令类型',
          field: 'type',
          options: computed(() => data.types) as any,
          rules: [{ required: true, message: '请选择指令类型' }],
          onChange: val => {
            const value = objectLookup(data.types, 'children', 'id', val) || {}
            refForm.value?.dataForm && (refForm.value.dataForm = { ...refForm.value.dataForm, receiveDeptId: value.deptIdList.split(',') || [] })
            // 当选择泵组调度类型时，获取泵组数据
            if (value.name === '泵组调度' || value.name?.includes('泵组')) {
              if (data.pumpList.length === 0) {
                data.getPumpList()
              }
            }
          }
        },
        {
          xl: 6,
          type: 'select-tree',
          label: '接收站点',
          field: 'receiveDeptId',
          multiple: true,
          defaultExpandAll: true,
          checkStrictly: true,
          options: computed(() => data.WaterSupplyTree) as any,
          rules: [{ required: true, message: '请输入接收站点' }]
        }, {
          xl: 6,
          type: 'input',
          label: '指令内容',
          field: 'sendContent'
        }, {
          xl: 6,
          type: 'select',
          label: '启用泵组',
          field: 'enablePumps',
          multiple: true,
          options: computed(() => data.pumpList) as any,
          handleHidden: (params: any) => {
            if (!params.type) return true
            const typeInfo = objectLookup(data.types, 'children', 'id', params.type) || {}
            return !(typeInfo.name === '泵组调度' || typeInfo.name?.includes('泵组'))
          }
        }, {
          xl: 6,
          type: 'select',
          label: '关闭泵组',
          field: 'disablePumps',
          multiple: true,
          options: computed(() => data.pumpList) as any,
          handleHidden: (params: any) => {
            if (!params.type) return true
            const typeInfo = objectLookup(data.types, 'children', 'id', params.type) || {}
            return !(typeInfo.name === '泵组调度' || typeInfo.name?.includes('泵组'))
          }
        }, {
          readonly: true,
          xl: 6,
          type: 'select-tree',
          label: '发送站点',
          field: 'sendDeptId',
          checkStrictly: true,
          options: computed(() => data.WaterSupplyTree) as any,
          rules: [{ required: true, message: '请输入发送站点' }]
        }, {
          xl: 6,
          type: 'datetime',
          label: '执行时间',
          field: 'executionTime',
          rules: [{ required: true, message: '请输入执行时间' }]
        }, {
          xl: 24,
          type: 'textarea',
          label: '备注',
          field: 'remark',
          rules: [{ required: true, message: '请输入备注' }]
        }

      ]
    }
  ]
})

const handleDelete = (row?: any) => {
  SLConfirm('确定撤销该指令', '撤销提示').then(() => {
    let ids:string[] = []
    if (row) ids = [row.id]
    else {
      TableConfig.selectList?.forEach(item => {
        ids.push(item.id)
      })
    }
    deleteOrderRecord(ids).then(() => {
      ElMessage.success('撤销成功')
      TableConfig.selectList = []
      refreshData()
    }).catch(error => {
      ElMessage.error(error.toString())
    })
  })
}

function addCommand() {
  refForm.value?.Submit()
}

function sendCommand(params?:any) {
  let ids:string[] = []
  if (params) ids = [params.id]
  else {
    TableConfig.selectList?.forEach(item => {
      ids.push(item.id)
    })
  }
  postSendCommand(ids).then(() => {
    ElMessage.success('发送成功')
    TableConfig.selectList = []
    refreshData()
  }).catch(error => {
    ElMessage.warning(error)
  })
}

function reset() {
  refForm.value?.resetForm()
}

const data = reactive({
  // 部门
  WaterSupplyTree: [],
  getWaterSupplyTreeValue: () => {
    const depth = 2
    getWaterSupplyTree(depth).then(res => {
      data.WaterSupplyTree = traverse(res.data.data || [])
    })
  },
  // 指令类型
  types: [] as any[],
  getTypes: () => {
    const params = {
      size: -1,
      page: 1
    }
    getorderRecordType(params).then(res => {
      data.types = traverse(res.data.data.data || [], 'children', { label: 'name', value: 'id' })
    })
  },
  // 泵组数据
  pumpList: [] as any[],
  showPumpFields: false,
  getPumpList: () => {
    const params = {
      page: 1,
      size: -1
    }
    pumpManageList(params).then(res => {
      data.pumpList = (res.data.data || []).map(item => ({
        label: item.name || item.nickname,
        value: item.id
      }))
      data.showPumpFields = true
    }).catch(() => {
      data.pumpList = []
      data.showPumpFields = false
    })
  }
})

const refreshData = async () => {
  const params: any = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    sendUserId: removeSlash(Cookies.get('userId') || ''),
    commandStatus: 'PENDING'
  }
  getOrderRecordList(params).then(res => {
    const dataList = res.data.data.data || []
    // 处理泵组名称显示
    dataList.forEach((item: any) => {
      if (item.enablePumps) {
        const enablePumpIds = item.enablePumps.split(',')
        item.enablePumpNames = enablePumpIds.map((id: string) => {
          const pump = data.pumpList.find(p => p.value === id)
          return pump ? pump.label : id
        }).join(', ')
      }
      if (item.disablePumps) {
        const disablePumpIds = item.disablePumps.split(',')
        item.disablePumpNames = disablePumpIds.map((id: string) => {
          const pump = data.pumpList.find(p => p.value === id)
          return pump ? pump.label : id
        }).join(', ')
      }
    })
    TableConfig.dataList = dataList
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

onMounted(async () => {
  refreshData()
  data.getWaterSupplyTreeValue()
  data.getTypes()
  // 预加载泵组数据
  data.getPumpList()
})

</script>
<style lang="scss" scoped>
.card-table {
  height: calc(100% - 300px);
}

.mag_Top_10 {
  margin-top: 10px;
}
</style>

<style scoped>
.card-table :deep(.el-table__expand-icon--expanded) {
  width: 6px;
}
</style>
