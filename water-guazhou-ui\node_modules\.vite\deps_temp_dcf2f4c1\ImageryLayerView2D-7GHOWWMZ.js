import {
  h as h2,
  m,
  y as y2
} from "./chunk-NWQ5RYVM.js";
import {
  n
} from "./chunk-J7MPN4DF.js";
import {
  v as v2
} from "./chunk-SHWEBLF5.js";
import {
  a as a3
} from "./chunk-WTEJLTAF.js";
import {
  l as l2
} from "./chunk-5TS3LYE7.js";
import "./chunk-76V27AD5.js";
import {
  ae
} from "./chunk-JWQLMC4D.js";
import "./chunk-Z4F6BT6Q.js";
import "./chunk-4IW3DWDX.js";
import "./chunk-THDEIRBK.js";
import "./chunk-OGWTQT66.js";
import "./chunk-XR4QWT37.js";
import "./chunk-6FMMG4VO.js";
import {
  a as a2
} from "./chunk-CUQZFD6D.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-L3FZV3M6.js";
import "./chunk-XAC3PEBY.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-GCZ6JHKQ.js";
import "./chunk-ERH4WAJU.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-CVN5SSWT.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-U6GJBSCL.js";
import "./chunk-RY6ZYWKC.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-223SE4BY.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-LGU2JTOA.js";
import "./chunk-MYYUEN6M.js";
import "./chunk-53FPJYCC.js";
import "./chunk-N73MYEJE.js";
import "./chunk-C65HMCEM.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import {
  J
} from "./chunk-OGHY3MGX.js";
import "./chunk-6G2NLXT7.js";
import {
  T as T2,
  d as d2,
  x2 as x3
} from "./chunk-7UML52SE.js";
import {
  s as s3
} from "./chunk-DVCBUZVC.js";
import "./chunk-UHA44FM7.js";
import "./chunk-TFWV44LH.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-6OFWBRK2.js";
import {
  i as i2
} from "./chunk-5JDQNIY4.js";
import {
  f
} from "./chunk-J3EWJTCQ.js";
import {
  u
} from "./chunk-HWB4LNSZ.js";
import "./chunk-JSZR3BUH.js";
import {
  h
} from "./chunk-R6ZFHGHU.js";
import "./chunk-JCXMTMKU.js";
import {
  T
} from "./chunk-WAPZ634R.js";
import "./chunk-3CFQMNJK.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-YACF4WM5.js";
import "./chunk-BMTNBZRF.js";
import "./chunk-YDRLAXYR.js";
import "./chunk-RURSJOSG.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-J5A2YARY.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-ZQY4DQCR.js";
import "./chunk-3JR5KBYG.js";
import "./chunk-WZNPTIYX.js";
import "./chunk-FRO3RSRO.js";
import "./chunk-22FAZXOH.js";
import "./chunk-DFGMRI52.js";
import "./chunk-OZZFNS32.js";
import "./chunk-6KNIOA43.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-MSIU52YL.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-ST2RRB55.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-IEIKQ72S.js";
import "./chunk-3IDKVHSA.js";
import "./chunk-77E52HT5.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import {
  i
} from "./chunk-73T3NEXA.js";
import "./chunk-DBDYNPQT.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-4DICTWL2.js";
import "./chunk-WGU7CS6R.js";
import "./chunk-4JOAXMIS.js";
import "./chunk-UYHYCNVE.js";
import "./chunk-INCFUNSS.js";
import "./chunk-KYTIKHPN.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-7UNBPRRZ.js";
import "./chunk-D3MAF4VS.js";
import "./chunk-2WMCP27R.js";
import "./chunk-3HW44BD3.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import {
  d
} from "./chunk-Q4VCSCSY.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  x as x2
} from "./chunk-USWRDFDJ.js";
import "./chunk-XM4RHPJM.js";
import "./chunk-6T5FEO66.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-ORU3OGKZ.js";
import "./chunk-LHJ7PAFO.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-FRJZOKDL.js";
import "./chunk-DKEAXJKM.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import {
  b
} from "./chunk-YJWWP4AU.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-EQYLMJEJ.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-L66EKMJP.js";
import "./chunk-3M3FTH72.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-52L4L36S.js";
import "./chunk-VTOVDEW2.js";
import "./chunk-UKESBP6X.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-N7ADFPOO.js";
import {
  g
} from "./chunk-TD7BKFJX.js";
import "./chunk-LNGMNZ4I.js";
import "./chunk-24NZLSKM.js";
import "./chunk-BNFBWJYJ.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-23VNIYCK.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-OVIRLBJH.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import {
  j as j2
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import {
  U as U2
} from "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  U,
  l,
  w as w3
} from "./chunk-QUHG7NMD.js";
import "./chunk-4YKNTCHK.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-CGBA4LNQ.js";
import "./chunk-2RZLOKYO.js";
import "./chunk-VIXZ7ZAD.js";
import {
  w
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-HHMRK4FX.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e as e2,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  j,
  x
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  e,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/imagery/ImageryView2D.js
var d3 = class extends v {
  constructor() {
    super(...arguments), this.attached = false, this.container = new h(), this.updateRequested = false, this.type = "imagery", this._bitmapView = new a3();
  }
  destroy() {
    this.attached && (this.detach(), this.attached = false), this.updateRequested = false;
  }
  get updating() {
    return !this.attached || this.isUpdating();
  }
  update(e3) {
    this.strategy.update(e3).catch((e4) => {
      j(e4) || s.getLogger(this.declaredClass).error(e4);
    });
  }
  hitTest(e3) {
    return new g({ attributes: {}, geometry: e3.clone(), layer: this.layer });
  }
  attach() {
    this.container.addChild(this._bitmapView);
    const e3 = this.layer.version >= 10, t3 = this.layer.version >= 10.1 ? this.layer.imageMaxHeight : 2048, i3 = this.layer.version >= 10.1 ? this.layer.imageMaxWidth : 2048;
    this.strategy = new v2({ container: this._bitmapView, imageNormalizationSupported: e3, imageMaxHeight: t3, imageMaxWidth: i3, fetchSource: this._fetchImage.bind(this), requestUpdate: () => this.requestUpdate() });
  }
  detach() {
    this.strategy.destroy(), this._bitmapView.removeAllChildren(), this.container.removeAllChildren(), this.updateRequested = false;
  }
  redraw() {
    this.strategy.updateExports(async (e3) => {
      const { source: t3 } = e3;
      if (!t3 || t3 instanceof ImageBitmap) return;
      const i3 = await this.layer.applyRenderer({ extent: t3.extent, pixelBlock: t3.originalPixelBlock ?? t3.pixelBlock });
      t3.filter = (e4) => this.layer.pixelFilter ? this.layer.applyFilter(e4) : { ...i3, extent: t3.extent };
    }).catch((e3) => {
      j(e3) || s.getLogger(this.declaredClass).error(e3);
    });
  }
  requestUpdate() {
    this.updateRequested || (this.updateRequested = true, this.view.requestUpdate());
  }
  isUpdating() {
    return this.strategy.updating || this.updateRequested;
  }
  getPixelData() {
    if (this.updating) return null;
    const e3 = this.strategy.bitmaps;
    if (1 === e3.length && e3[0].source) return { extent: e3[0].source.extent, pixelBlock: e3[0].source.originalPixelBlock };
    if (e3.length > 1) {
      const t3 = this.view.extent, i3 = e3.map((e4) => e4.source).filter((e4) => e4.extent && e4.extent.intersects(t3)).map((e4) => ({ extent: e4.extent, pixelBlock: e4.originalPixelBlock })), r2 = T2(i3, t3);
      return r(r2) ? { extent: r2.extent, pixelBlock: r2.pixelBlock } : null;
    }
    return null;
  }
  async _fetchImage(e3, t3, i3, r2) {
    var _a;
    (r2 = r2 || {}).timeExtent = this.timeExtent, r2.requestAsImageElement = true, r2.returnImageBitmap = true;
    const s4 = await this.layer.fetchImage(e3, t3, i3, r2);
    if (s4.imageBitmap) return s4.imageBitmap;
    const a4 = await this.layer.applyRenderer(s4.pixelData, { signal: r2.signal }), o = new l2(a4.pixelBlock, (_a = a4.extent) == null ? void 0 : _a.clone(), s4.pixelData.pixelBlock);
    return o.filter = (e4) => this.layer.applyFilter(e4), o;
  }
};
e2([y()], d3.prototype, "attached", void 0), e2([y()], d3.prototype, "container", void 0), e2([y()], d3.prototype, "layer", void 0), e2([y()], d3.prototype, "strategy", void 0), e2([y()], d3.prototype, "timeExtent", void 0), e2([y()], d3.prototype, "view", void 0), e2([y()], d3.prototype, "updateRequested", void 0), e2([y()], d3.prototype, "updating", null), e2([y()], d3.prototype, "type", void 0), d3 = e2([a("esri.views.2d.layers.imagery.ImageryView2D")], d3);
var u2 = d3;

// node_modules/@arcgis/core/views/2d/engine/imagery/RasterVFContainer.js
var t2 = class extends a2 {
  constructor() {
    super(...arguments), this.symbolTypes = ["triangle"];
  }
  get requiresDedicatedFBO() {
    return false;
  }
  prepareRenderPasses(s4) {
    const t3 = s4.registerRenderPass({ name: "imagery (vf)", brushes: [m], target: () => this.children, drawPhase: T.MAP });
    return [...super.prepareRenderPasses(s4), t3];
  }
  doRender(e3) {
    this.visible && e3.drawPhase === T.MAP && this.symbolTypes.forEach((r2) => {
      e3.renderPass = r2, super.doRender(e3);
    });
  }
};

// node_modules/@arcgis/core/views/2d/layers/imagery/ImageryVFStrategy.js
var p = class extends v {
  constructor(e3) {
    super(e3), this._loading = null, this.update = x((e4, t3) => this._update(e4, t3).catch((e5) => {
      j(e5) || s.getLogger(this.declaredClass).error(e5);
    }));
  }
  get updating() {
    return !!this._loading;
  }
  redraw(e3) {
    if (!this.container.children.length) return;
    const t3 = this.container.children[0];
    t3.symbolizerParameters = e3, t3.invalidateVAO(), this.container.symbolTypes = "wind_speed" === e3.style ? ["scalar", "triangle"] : "simple_scalar" === e3.style ? ["scalar"] : ["triangle"], this.container.requestRender();
  }
  async _update(e3, t3, r2) {
    if (!e3.stationary) return;
    const { extent: i3, spatialReference: s4 } = e3.state, o = new w2({ xmin: i3.xmin, ymin: i3.ymin, xmax: i3.xmax, ymax: i3.ymax, spatialReference: s4 }), [a4, n2] = e3.state.size;
    this._loading = this.fetchPixels(o, a4, n2, r2);
    const c = await this._loading;
    this._addToDisplay(c, t3, e3.state), this._loading = null;
  }
  _addToDisplay(e3, t3, r2) {
    if (t(e3.pixelBlock)) return this.container.children.forEach((e4) => e4.destroy()), void this.container.removeAllChildren();
    const { extent: s4, pixelBlock: o } = e3, a4 = new y2(o);
    a4.offset = [0, 0], a4.symbolizerParameters = t3, a4.rawPixelData = e3, a4.invalidateVAO(), a4.x = s4.xmin, a4.y = s4.ymax, a4.pixelRatio = r2.pixelRatio, a4.rotation = r2.rotation, a4.resolution = r2.resolution, a4.width = o.width * t3.symbolTileSize, a4.height = o.height * t3.symbolTileSize, this.container.children.forEach((e4) => e4.destroy()), this.container.removeAllChildren(), this.container.symbolTypes = "wind_speed" === t3.style ? ["scalar", "triangle"] : "simple_scalar" === t3.style ? ["scalar"] : ["triangle"], this.container.addChild(a4);
  }
};
e2([y()], p.prototype, "fetchPixels", void 0), e2([y()], p.prototype, "container", void 0), e2([y()], p.prototype, "_loading", void 0), e2([y()], p.prototype, "updating", null), p = e2([a("esri.views.2d.layers.imagery.ImageryVFStrategy")], p);
var m2 = p;

// node_modules/@arcgis/core/views/2d/layers/imagery/VectorFieldView2D.js
var f2 = class extends d {
  constructor() {
    super(...arguments), this.attached = false, this.container = new t2(), this.type = "imageryVF", this._dataParameters = { exportParametersVersion: 0, bbox: "", symbolTileSize: 0, time: "" }, this._fetchpixels = async (t3, e3, r2, i3) => {
      const n2 = await this._projectFullExtentPromise, { symbolTileSize: l3 } = this.layer.renderer, { extent: m4, width: c, height: p2 } = x3(t3, e3, r2, l3, n2);
      if (r(n2) && !n2.intersects(t3)) return { extent: m4, pixelBlock: null };
      const h3 = { bbox: `${m4.xmin}, ${m4.ymin}, ${m4.xmax}, ${m4.ymax}`, exportParametersVersion: this.layer.exportImageServiceParameters.version, symbolTileSize: l3, time: JSON.stringify(this.timeExtent || "") };
      if (this._canReuseVectorFieldData(h3)) {
        const t4 = this.getPixelData();
        if (r(t4)) {
          if (`${t4.extent.xmin}, ${t4.extent.ymin}, ${t4.extent.xmax}, ${t4.extent.ymax}` === h3.bbox) return t4;
        }
      }
      const { pixelData: d4 } = await this.layer.fetchImage(m4, c, p2, { timeExtent: this.timeExtent, requestAsImageElement: false, signal: i3 });
      this._dataParameters = h3;
      const x4 = d4 == null ? void 0 : d4.pixelBlock;
      if (t(x4)) return { extent: m4, pixelBlock: null };
      return { extent: m4, pixelBlock: "vector-uv" === this.layer.rasterInfo.dataType ? e(d2(x4, "vector-uv")) : x4 };
    };
  }
  get updating() {
    return !this.attached || this._strategy.updating;
  }
  attach() {
    this._projectFullExtentPromise = this._getProjectedFullExtent(this.view.spatialReference), this._strategy = new m2({ container: this.container, fetchPixels: this._fetchpixels }), this.handles.add(l(() => this.layer.renderer, (t3) => this._updateSymbolizerParams(t3), w3), "attach");
  }
  detach() {
    this._strategy.destroy(), this.container.children.forEach((t3) => t3.destroy()), this.container.removeAllChildren(), this.handles.remove("attach"), this._strategy = this.container = this._projectFullExtentPromise = null;
  }
  getPixelData() {
    var _a;
    const t3 = (_a = this.container.children[0]) == null ? void 0 : _a.rawPixelData;
    if (this.updating || !t3) return null;
    const { extent: e3, pixelBlock: r2 } = t3;
    return { extent: e3, pixelBlock: r2 };
  }
  hitTest(t3) {
    return new g({ attributes: {}, geometry: t3.clone(), layer: this.layer });
  }
  update(t3) {
    this._strategy.update(t3, this._symbolizerParams);
  }
  redraw() {
    const { renderer: t3 } = this.layer;
    t3 && (this._updateSymbolizerParams(t3), this._strategy.redraw(this._symbolizerParams));
  }
  _canReuseVectorFieldData(t3) {
    const e3 = this._dataParameters.exportParametersVersion === t3.exportParametersVersion, r2 = this._dataParameters.time === t3.time, i3 = this._dataParameters.symbolTileSize === t3.symbolTileSize, s4 = this._dataParameters.bbox === t3.bbox;
    return e3 && r2 && i3 && s4;
  }
  async _getProjectedFullExtent(t3) {
    try {
      return await J(this.layer.fullExtent, t3);
    } catch (e3) {
      try {
        const e4 = (await U2(this.layer.url, { query: { option: "footprints", outSR: t3.wkid || JSON.stringify(t3.toJSON()), f: "json" } })).data.featureCollection.layers[0].layerDefinition.extent;
        return e4 ? w2.fromJSON(e4) : null;
      } catch {
        return null;
      }
    }
  }
  _updateSymbolizerParams(t3) {
    "vector-field" === t3.type && (this._symbolizerParams = this.layer.symbolizer.generateWebGLParameters({ pixelBlock: null }));
  }
};
e2([y()], f2.prototype, "attached", void 0), e2([y()], f2.prototype, "container", void 0), e2([y()], f2.prototype, "layer", void 0), e2([y()], f2.prototype, "timeExtent", void 0), e2([y()], f2.prototype, "type", void 0), e2([y()], f2.prototype, "view", void 0), e2([y()], f2.prototype, "updating", null), f2 = e2([a("esri.views.2d.layers.imagery.VectorFieldView2D")], f2);
var g2 = f2;

// node_modules/@arcgis/core/views/layers/ImageryLayerView.js
var m3 = (m4) => {
  let c = class extends m4 {
    constructor() {
      super(...arguments), this.view = null;
    }
    async fetchPopupFeatures(e3, s4) {
      const { layer: p2 } = this;
      if (!e3) throw new s2("imagerylayerview:fetchPopupFeatures", "Nothing to fetch without area", { layer: p2 });
      const { popupEnabled: a4 } = p2, m5 = s3(p2, s4);
      if (!a4 || t(m5)) throw new s2("imagerylayerview:fetchPopupFeatures", "Missing required popupTemplate or popupEnabled", { popupEnabled: a4, popupTemplate: m5 });
      const c2 = await m5.getRequiredFields(), l3 = new x2();
      l3.timeExtent = this.timeExtent, l3.geometry = e3, l3.outFields = c2, l3.outSpatialReference = e3.spatialReference;
      const { resolution: y3, spatialReference: d4 } = this.view, w4 = "2d" === this.view.type ? new w(y3, y3, d4) : new w(0.5 * y3, 0.5 * y3, d4), { returnTopmostRaster: f3, showNoDataRecords: h3 } = m5.layerOptions || { returnTopmostRaster: true, showNoDataRecords: false }, R = { returnDomainValues: true, returnTopmostRaster: f3, pixelSize: w4, showNoDataRecords: h3, signal: r(s4) ? s4.signal : null };
      return p2.queryVisibleRasters(l3, R).then((e4) => e4);
    }
    canResume() {
      var _a;
      return !!super.canResume() && !((_a = this.timeExtent) == null ? void 0 : _a.isEmpty);
    }
  };
  return e2([y()], c.prototype, "layer", void 0), e2([y()], c.prototype, "suspended", void 0), e2([y(b)], c.prototype, "timeExtent", void 0), e2([y()], c.prototype, "view", void 0), c = e2([a("esri.views.layers.ImageryLayerView")], c), c;
};

// node_modules/@arcgis/core/views/2d/layers/ImageryLayerView2D.js
var g3 = class extends m3(i2(f(u))) {
  constructor() {
    super(...arguments), this._exportImageVersion = -1, this._highlightGraphics = new i(), this._highlightView = void 0, this.layer = null, this.subview = null;
  }
  get pixelData() {
    const { subview: e3 } = this;
    return this.updating || !e3 ? null : "getPixelData" in e3 ? e3.getPixelData() : null;
  }
  async hitTest(e3, t3) {
    return this.subview ? [{ type: "graphic", graphic: this.subview.hitTest(e3), layer: this.layer, mapPoint: e3 }] : null;
  }
  update(e3) {
    var _a;
    (_a = this.subview) == null ? void 0 : _a.update(e3);
  }
  attach() {
    this.layer.increaseRasterJobHandlerUsage(), this._setSubView(), this.view && (this._highlightView = new ae({ view: this.view, graphics: this._highlightGraphics, requestUpdateCallback: () => this.requestUpdate(), container: new n(this.view.featuresTilingScheme) }), this.container.addChild(this._highlightView.container)), this.addAttachHandles([l(() => this.layer.blendMode ?? "normal", (e3) => this.subview && (this.subview.container.blendMode = e3), w3), l(() => this.layer.effect ?? null, (e3) => this.subview && (this.subview.container.effect = e3), w3), l(() => this.layer.exportImageServiceParameters.version, (e3) => {
      e3 && this._exportImageVersion !== e3 && (this._exportImageVersion = e3, this.requestUpdate());
    }, U), l(() => this.timeExtent, (e3) => {
      const { subview: t3 } = this;
      t3 && (t3.timeExtent = e3, "redraw" in t3 ? this.requestUpdate() : t3.redrawOrRefetch());
    }, U), this.layer.on("redraw", () => {
      const { subview: e3 } = this;
      e3 && ("redraw" in e3 ? e3.redraw() : e3.redrawOrRefetch());
    }), l(() => this.layer.renderer, () => this._setSubView())]);
  }
  detach() {
    var _a, _b;
    this.layer.decreaseRasterJobHandlerUsage(), this.container.removeAllChildren(), this._detachSubview(this.subview), (_a = this.subview) == null ? void 0 : _a.destroy(), this.subview = null, (_b = this._highlightView) == null ? void 0 : _b.destroy(), this._exportImageVersion = -1;
  }
  moveStart() {
  }
  viewChange() {
  }
  moveEnd() {
    this.requestUpdate();
  }
  highlight(e3, r2) {
    if (!((Array.isArray(e3) ? e3[0] : j2.isCollection(e3) ? e3.getItemAt(0) : e3) instanceof g)) return { remove: () => {
    } };
    let s4 = [];
    return Array.isArray(e3) || j2.isCollection(e3) ? s4 = e3.map((e4) => e4.clone()) : e3 instanceof g && (s4 = [e3.clone()]), this._highlightGraphics.addMany(s4), { remove: () => {
      this._highlightGraphics.removeMany(s4);
    } };
  }
  async doRefresh() {
    this.requestUpdate();
  }
  isUpdating() {
    return !this.subview || this.subview.updating;
  }
  _setSubView() {
    var _a, _b;
    if (!this.view) return;
    const e3 = (_a = this.layer.renderer) == null ? void 0 : _a.type;
    let t3 = "imagery";
    if ("vector-field" === e3 ? t3 = "imageryVF" : "flow" === e3 && (t3 = "flow"), this.subview) {
      const { type: e4 } = this.subview;
      if (e4 === t3) return this._attachSubview(this.subview), void ("flow" === e4 ? this.subview.redrawOrRefetch() : "imagery" === e4 && "lerc" === this.layer.format ? this.subview.redraw() : this.requestUpdate());
      this._detachSubview(this.subview), (_b = this.subview) == null ? void 0 : _b.destroy();
    }
    this.subview = "imagery" === t3 ? new u2({ layer: this.layer, view: this.view, timeExtent: this.timeExtent }) : "imageryVF" === t3 ? new g2({ layer: this.layer, view: this.view, timeExtent: this.timeExtent }) : new h2({ layer: this.layer, layerView: this }), this._attachSubview(this.subview), this.requestUpdate();
  }
  _attachSubview(e3) {
    e3 && !e3.attached && (e3.attach(), e3.attached = true, this.container.addChildAt(e3.container, 0), e3.container.blendMode = this.layer.blendMode, e3.container.effect = this.layer.effect);
  }
  _detachSubview(e3) {
    (e3 == null ? void 0 : e3.attached) && (this.container.removeChild(e3.container), e3.detach(), e3.attached = false);
  }
};
e2([y()], g3.prototype, "pixelData", null), e2([y()], g3.prototype, "subview", void 0), g3 = e2([a("esri.views.2d.layers.ImageryLayerView2D")], g3);
var b2 = g3;
export {
  b2 as default
};
//# sourceMappingURL=ImageryLayerView2D-7GHOWWMZ.js.map
