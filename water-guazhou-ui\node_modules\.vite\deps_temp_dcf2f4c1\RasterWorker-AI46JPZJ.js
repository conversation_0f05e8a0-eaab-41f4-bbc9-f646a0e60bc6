import {
  C,
  i
} from "./chunk-6SD5VHPQ.js";
import {
  $,
  M,
  T as T2
} from "./chunk-OGHY3MGX.js";
import {
  S,
  T,
  p
} from "./chunk-XOTZS7TA.js";
import {
  D,
  F,
  R,
  b,
  d,
  f3 as f,
  m,
  m3 as m2
} from "./chunk-7UML52SE.js";
import "./chunk-YACF4WM5.js";
import "./chunk-O2BYTJI4.js";
import {
  s2 as s
} from "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-3M3FTH72.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  e,
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/RasterWorker.js
var N = class {
  convertVectorFieldData(e2) {
    const t = m.fromJSON(e2.pixelBlock), o = d(t, e2.type);
    return Promise.resolve(r(o) ? o.toJSON() : null);
  }
  async decode(r2) {
    const e2 = await S(r2.data, r2.options);
    return e2 && e2.toJSON();
  }
  symbolize(e2) {
    e2.pixelBlock = m.fromJSON(e2.pixelBlock), e2.extent = e2.extent ? w.fromJSON(e2.extent) : null;
    const t = this.symbolizer.symbolize(e2);
    return Promise.resolve(r(t) ? t.toJSON() : null);
  }
  async updateSymbolizer(r2) {
    var _a;
    this.symbolizer = T.fromJSON(r2.symbolizerJSON), r2.histograms && "rasterStretch" === ((_a = this.symbolizer) == null ? void 0 : _a.rendererJSON.type) && (this.symbolizer.rendererJSON.histograms = r2.histograms);
  }
  async updateRasterFunction(r2) {
    this.rasterFunction = C(r2.rasterFunctionJSON);
  }
  async process(e2) {
    const t = this.rasterFunction.process({ extent: w.fromJSON(e2.extent), primaryPixelBlocks: e2.primaryPixelBlocks.map((e3) => r(e3) ? m.fromJSON(e3) : null), primaryRasterIds: e2.primaryRasterIds });
    return r(t) ? t.toJSON() : null;
  }
  stretch(e2) {
    const t = this.symbolizer.simpleStretch(m.fromJSON(e2.srcPixelBlock), e2.stretchParams);
    return Promise.resolve(r(t) && t.toJSON());
  }
  estimateStatisticsHistograms(r2) {
    const e2 = p(m.fromJSON(r2.srcPixelBlock));
    return Promise.resolve(e2);
  }
  split(r2) {
    const e2 = R(m.fromJSON(r2.srcPixelBlock), r2.tileSize, r2.maximumPyramidLevel);
    return e2 && e2.forEach((r3, t) => {
      e2.set(t, r3 == null ? void 0 : r3.toJSON());
    }), Promise.resolve(e2);
  }
  async mosaicAndTransform(r2) {
    const t = r2.srcPixelBlocks.map((r3) => r3 ? new m(r3) : null), o = b(t, r2.srcMosaicSize, { blockWidths: r2.blockWidths, alignmentInfo: r2.alignmentInfo, clipOffset: r2.clipOffset, clipSize: r2.clipSize });
    let i2, l = o;
    return r2.coefs && (l = F(o, r2.destDimension, r2.coefs, r2.sampleSpacing, r2.interpolation)), r2.projectDirections && r2.gcsGrid && (i2 = D(r2.destDimension, r2.gcsGrid), l = e(m2(l, r2.isUV ? "vector-uv" : "vector-magdir", i2))), { pixelBlock: l == null ? void 0 : l.toJSON(), localNorthDirections: i2 };
  }
  async createFlowMesh(r2, e2) {
    const t = { data: new Float32Array(r2.flowData.buffer), mask: new Uint8Array(r2.flowData.maskBuffer), width: r2.flowData.width, height: r2.flowData.height }, { vertexData: s2, indexData: o } = await f(r2.meshType, r2.simulationSettings, t, e2.signal);
    return { result: { vertexBuffer: s2.buffer, indexBuffer: o.buffer }, transferList: [s2.buffer, o.buffer] };
  }
  async getProjectionOffsetGrid(r2) {
    const e2 = w.fromJSON(r2.projectedExtent), s2 = w.fromJSON(r2.srcBufferExtent);
    let o = null;
    r2.datumTransformationSteps && (o = new s({ steps: r2.datumTransformationSteps })), (r2.includeGCSGrid || M(e2.spatialReference, s2.spatialReference, o)) && await T2();
    const i2 = r2.rasterTransform ? i(r2.rasterTransform) : null;
    return $({ ...r2, projectedExtent: e2, srcBufferExtent: s2, datumTransformation: o, rasterTransform: i2 });
  }
};
export {
  N as default
};
//# sourceMappingURL=RasterWorker-AI46JPZJ.js.map
