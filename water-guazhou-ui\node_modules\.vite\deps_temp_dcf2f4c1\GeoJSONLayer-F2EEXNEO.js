import {
  l
} from "./chunk-D7BTYVTV.js";
import "./chunk-JLELSJK5.js";
import {
  c as c3
} from "./chunk-LNCHRZJI.js";
import {
  n as n3,
  p as p4
} from "./chunk-O3LPRA7A.js";
import "./chunk-M5RPNIHK.js";
import "./chunk-KYTIKHPN.js";
import {
  s as s3
} from "./chunk-Y7OJSY6H.js";
import {
  u as u2
} from "./chunk-6JIS2R4B.js";
import "./chunk-7BSY2CUN.js";
import "./chunk-6RN6WNY4.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-DTQ34PEY.js";
import {
  n as n4
} from "./chunk-FWXA4I6D.js";
import {
  p as p5
} from "./chunk-NQ3OACUM.js";
import {
  i
} from "./chunk-7UNBPRRZ.js";
import {
  o as o2
} from "./chunk-OQK7L3JR.js";
import {
  p as p6
} from "./chunk-5BWF7URZ.js";
import "./chunk-D3MAF4VS.js";
import {
  a as a2
} from "./chunk-ND4JUK42.js";
import "./chunk-2WMCP27R.js";
import {
  p as p2
} from "./chunk-JV6TBH5W.js";
import {
  _
} from "./chunk-TV6WKLTU.js";
import "./chunk-5VDORDNW.js";
import {
  c as c2
} from "./chunk-FHKOFAQ2.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import "./chunk-3HW44BD3.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-P37TUI4J.js";
import {
  n
} from "./chunk-LAEW33J6.js";
import {
  t as t2
} from "./chunk-NGPCXWDX.js";
import "./chunk-IXMTA2RC.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-67MHB3E3.js";
import {
  x as x2
} from "./chunk-USWRDFDJ.js";
import "./chunk-XM4RHPJM.js";
import "./chunk-6T5FEO66.js";
import "./chunk-MKG56XBH.js";
import "./chunk-FZ7BG3VX.js";
import {
  x as x3
} from "./chunk-N4YJNWPS.js";
import {
  y as y2
} from "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-45UG5A2F.js";
import "./chunk-ORU3OGKZ.js";
import {
  n as n2,
  p as p3
} from "./chunk-FCQRDLBQ.js";
import "./chunk-NUZU7NCS.js";
import "./chunk-LHJ7PAFO.js";
import "./chunk-7MQMIP4J.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import {
  C
} from "./chunk-FRJZOKDL.js";
import "./chunk-DKEAXJKM.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import {
  c,
  d,
  f as f2,
  l as l2,
  m as m2,
  p,
  u,
  v as v2
} from "./chunk-YJWWP4AU.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-EQYLMJEJ.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-3TXAWGPY.js";
import "./chunk-5EGPPD3R.js";
import "./chunk-L66EKMJP.js";
import "./chunk-3M3FTH72.js";
import "./chunk-XLHYMGQY.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-52L4L36S.js";
import "./chunk-VTOVDEW2.js";
import "./chunk-UKESBP6X.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-TD7BKFJX.js";
import {
  k
} from "./chunk-LNGMNZ4I.js";
import "./chunk-24NZLSKM.js";
import "./chunk-BNFBWJYJ.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-23VNIYCK.js";
import "./chunk-AVKOL7OR.js";
import {
  F,
  x as x4
} from "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-OVIRLBJH.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import {
  m
} from "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import {
  o
} from "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import {
  v
} from "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-4YKNTCHK.js";
import "./chunk-XVA5SA7P.js";
import {
  L
} from "./chunk-U4SVMKOQ.js";
import "./chunk-CGBA4LNQ.js";
import "./chunk-2RZLOKYO.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-HHMRK4FX.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  w,
  x
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  has
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/graphics/sources/GeoJSONSource.js
var y3 = "esri.layers.graphics.sources.GeoJSONSource";
var f3 = s.getLogger(y3);
var g = class extends m {
  constructor() {
    super(...arguments), this.type = "geojson", this.refresh = x(async (e2) => {
      await this.load();
      const { extent: t3, timeExtent: r2 } = await this._connection.invoke("refresh", e2);
      return this.sourceJSON.extent = t3, r2 && (this.sourceJSON.timeInfo.timeExtent = [r2.start, r2.end]), { dataChanged: true, updates: { extent: this.sourceJSON.extent, timeInfo: this.sourceJSON.timeInfo } };
    });
  }
  load(e2) {
    const t3 = r(e2) ? e2.signal : null;
    return this.addResolvingPromise(this._startWorker(t3)), Promise.resolve(this);
  }
  destroy() {
    var _a;
    (_a = this._connection) == null ? void 0 : _a.close(), this._connection = null;
  }
  applyEdits(e2) {
    return this.load().then(() => this._applyEdits(e2));
  }
  openPorts() {
    return this.load().then(() => this._connection.openPorts());
  }
  queryFeatures(e2, t3 = {}) {
    return this.load(t3).then(() => this._connection.invoke("queryFeatures", e2 ? e2.toJSON() : null, t3)).then((e3) => x3.fromJSON(e3));
  }
  queryFeaturesJSON(e2, t3 = {}) {
    return this.load(t3).then(() => this._connection.invoke("queryFeatures", e2 ? e2.toJSON() : null, t3));
  }
  queryFeatureCount(e2, t3 = {}) {
    return this.load(t3).then(() => this._connection.invoke("queryFeatureCount", e2 ? e2.toJSON() : null, t3));
  }
  queryObjectIds(e2, t3 = {}) {
    return this.load(t3).then(() => this._connection.invoke("queryObjectIds", e2 ? e2.toJSON() : null, t3));
  }
  queryExtent(e2, t3 = {}) {
    return this.load(t3).then(() => this._connection.invoke("queryExtent", e2 ? e2.toJSON() : null, t3)).then((e3) => ({ count: e3.count, extent: w2.fromJSON(e3.extent) }));
  }
  querySnapping(e2, t3 = {}) {
    return this.load(t3).then(() => this._connection.invoke("querySnapping", e2, t3));
  }
  _applyEdits(e2) {
    if (!this._connection) throw new s2("geojson-layer-source:edit-failure", "Memory source not loaded");
    const r2 = this.layer.objectIdField, o3 = [], s4 = [], i2 = [];
    if (e2.addFeatures) for (const t3 of e2.addFeatures) o3.push(this._serializeFeature(t3));
    if (e2.deleteFeatures) for (const t3 of e2.deleteFeatures) "objectId" in t3 && null != t3.objectId ? s4.push(t3.objectId) : "attributes" in t3 && null != t3.attributes[r2] && s4.push(t3.attributes[r2]);
    if (e2.updateFeatures) for (const t3 of e2.updateFeatures) i2.push(this._serializeFeature(t3));
    return this._connection.invoke("applyEdits", { adds: o3, updates: i2, deletes: s4 }).then(({ extent: e3, timeExtent: t3, featureEditResults: r3 }) => (this.sourceJSON.extent = e3, t3 && (this.sourceJSON.timeInfo.timeExtent = [t3.start, t3.end]), this._createEditsResult(r3)));
  }
  _createEditsResult(e2) {
    return { addFeatureResults: e2.addResults ? e2.addResults.map(this._createFeatureEditResult, this) : [], updateFeatureResults: e2.updateResults ? e2.updateResults.map(this._createFeatureEditResult, this) : [], deleteFeatureResults: e2.deleteResults ? e2.deleteResults.map(this._createFeatureEditResult, this) : [], addAttachmentResults: [], updateAttachmentResults: [], deleteAttachmentResults: [] };
  }
  _createFeatureEditResult(e2) {
    const r2 = true === e2.success ? null : e2.error || { code: void 0, description: void 0 };
    return { objectId: e2.objectId, globalId: e2.globalId, error: r2 ? new s2("geojson-layer-source:edit-failure", r2.description, { code: r2.code }) : null };
  }
  _serializeFeature(e2) {
    const { attributes: t3 } = e2, r2 = this._geometryForSerialization(e2);
    return r2 ? { geometry: r2.toJSON(), attributes: t3 } : { attributes: t3 };
  }
  _geometryForSerialization(e2) {
    const { geometry: t3 } = e2;
    return t(t3) ? null : "mesh" === t3.type || "extent" === t3.type ? v.fromExtent(t3.extent) : t3;
  }
  async _startWorker(e2) {
    this._connection = await u2("GeoJSONSourceWorker", { strategy: has("feature-layers-workers") ? "dedicated" : "local", signal: e2 });
    const { fields: t3, spatialReference: r2, hasZ: o3, geometryType: s4, objectIdField: i2, url: n5, timeInfo: u3, customParameters: l3 } = this.layer, d2 = "defaults" === this.layer.originOf("spatialReference"), p7 = { url: n5, customParameters: l3, fields: t3 && t3.map((e3) => e3.toJSON()), geometryType: o.toJSON(s4), hasZ: o3, objectIdField: i2, timeInfo: u3 ? u3.toJSON() : null, spatialReference: d2 ? null : r2 && r2.toJSON() }, h = await this._connection.invoke("load", p7, { signal: e2 });
    for (const a3 of h.warnings) f3.warn(a3.message, { layer: this.layer, warning: a3 });
    h.featureErrors.length && f3.warn(`Encountered ${h.featureErrors.length} validation errors while loading features`, h.featureErrors), this.sourceJSON = h.layerDefinition, this.capabilities = l(this.sourceJSON.hasZ, true);
  }
};
e([y()], g.prototype, "capabilities", void 0), e([y()], g.prototype, "type", void 0), e([y({ constructOnly: true })], g.prototype, "layer", void 0), e([y()], g.prototype, "sourceJSON", void 0), g = e([a(y3)], g);

// node_modules/@arcgis/core/layers/GeoJSONLayer.js
var Z = s3();
var M = class extends c3(o2(n3(p4(n(a2(t2(p2(c2(_(O(b))))))))))) {
  constructor(e2) {
    super(e2), this.copyright = null, this.definitionExpression = null, this.displayField = null, this.editingEnabled = false, this.elevationInfo = null, this.fields = null, this.fieldsIndex = null, this.fullExtent = null, this.geometryType = null, this.hasZ = void 0, this.labelsVisible = true, this.labelingInfo = null, this.legendEnabled = true, this.objectIdField = null, this.operationalLayerType = "GeoJSON", this.popupEnabled = true, this.popupTemplate = null, this.screenSizePerspectiveEnabled = true, this.source = new g({ layer: this }), this.spatialReference = f.WGS84, this.templates = null, this.title = "GeoJSON", this.type = "geojson", this.typeIdField = null, this.types = null;
  }
  destroy() {
    var _a;
    (_a = this.source) == null ? void 0 : _a.destroy();
  }
  load(e2) {
    const t3 = this.loadFromPortal({ supportedTypes: ["GeoJson"], supportsData: false }, e2).catch(w).then(() => this.source.load(e2)).then(() => {
      this.read(this.source.sourceJSON, { origin: "service", url: this.parsedUrl }), this.revert(["objectIdField", "fields", "timeInfo"], "service"), F(this.renderer, this.fieldsIndex), x4(this.timeInfo, this.fieldsIndex);
    });
    return this.addResolvingPromise(t3), Promise.resolve(this);
  }
  get capabilities() {
    return this.source ? this.source.capabilities : null;
  }
  get createQueryVersion() {
    return this.commitProperty("definitionExpression"), this.commitProperty("timeExtent"), this.commitProperty("timeOffset"), this.commitProperty("geometryType"), this.commitProperty("capabilities"), (this._get("createQueryVersion") || 0) + 1;
  }
  get defaultPopupTemplate() {
    return this.createPopupTemplate();
  }
  get isTable() {
    return this.loaded && null == this.geometryType;
  }
  get parsedUrl() {
    return this.url ? L(this.url) : null;
  }
  set renderer(e2) {
    F(e2, this.fieldsIndex), this._set("renderer", e2);
  }
  set url(e2) {
    if (!e2) return void this._set("url", e2);
    const t3 = L(e2);
    this._set("url", t3.path), t3.query && (this.customParameters = { ...this.customParameters, ...t3.query });
  }
  async applyEdits(e2, t3) {
    const r2 = await import("./editingSupport-ZIFNPSI4.js");
    await this.load();
    const i2 = await r2.applyEdits(this, this.source, e2, t3);
    return this.read({ extent: this.source.sourceJSON.extent, timeInfo: this.source.sourceJSON.timeInfo }, { origin: "service", ignoreDefaults: true }), i2;
  }
  on(e2, t3) {
    return super.on(e2, t3);
  }
  createPopupTemplate(e2) {
    return p6(this, e2);
  }
  createQuery() {
    const e2 = new x2(), t3 = this.get("capabilities.data");
    e2.returnGeometry = true, t3 && t3.supportsZ && (e2.returnZ = true), e2.outFields = ["*"], e2.where = this.definitionExpression || "1=1";
    const { timeOffset: r2, timeExtent: i2 } = this;
    return e2.timeExtent = null != r2 && null != i2 ? i2.offset(-r2.value, r2.unit) : i2 || null, e2;
  }
  getFieldDomain(e2, t3) {
    let r2, i2 = false;
    const o3 = t3 && t3.feature, s4 = o3 && o3.attributes, p7 = this.typeIdField && s4 && s4[this.typeIdField];
    return null != p7 && this.types && (i2 = this.types.some((t4) => t4.id == p7 && (r2 = t4.domains && t4.domains[e2], r2 && "inherited" === r2.type && (r2 = this._getLayerDomain(e2)), true))), i2 || r2 || (r2 = this._getLayerDomain(e2)), r2;
  }
  getField(e2) {
    return this.fieldsIndex.get(e2);
  }
  queryFeatures(e2, t3) {
    return this.load().then(() => this.source.queryFeatures(x2.from(e2) || this.createQuery(), t3)).then((e3) => {
      if (e3 == null ? void 0 : e3.features) for (const t4 of e3.features) t4.layer = t4.sourceLayer = this;
      return e3;
    });
  }
  queryObjectIds(e2, t3) {
    return this.load().then(() => this.source.queryObjectIds(x2.from(e2) || this.createQuery(), t3));
  }
  queryFeatureCount(e2, t3) {
    return this.load().then(() => this.source.queryFeatureCount(x2.from(e2) || this.createQuery(), t3));
  }
  queryExtent(e2, t3) {
    return this.load().then(() => this.source.queryExtent(x2.from(e2) || this.createQuery(), t3));
  }
  async hasDataChanged() {
    try {
      const { dataChanged: e2, updates: t3 } = await this.source.refresh(this.customParameters);
      return r(t3) && this.read(t3, { origin: "service", url: this.parsedUrl, ignoreDefaults: true }), e2;
    } catch {
    }
    return false;
  }
  _getLayerDomain(e2) {
    if (!this.fields) return null;
    let t3 = null;
    return this.fields.some((r2) => (r2.name === e2 && (t3 = r2.domain), !!t3)), t3;
  }
};
e([y({ readOnly: true, json: { read: false, write: false } })], M.prototype, "capabilities", null), e([y({ type: String })], M.prototype, "copyright", void 0), e([y({ readOnly: true })], M.prototype, "createQueryVersion", null), e([y({ readOnly: true })], M.prototype, "defaultPopupTemplate", null), e([y({ type: String, json: { name: "layerDefinition.definitionExpression", write: { enabled: true, allowNull: true } } })], M.prototype, "definitionExpression", void 0), e([y({ type: String })], M.prototype, "displayField", void 0), e([y({ type: Boolean })], M.prototype, "editingEnabled", void 0), e([y(d)], M.prototype, "elevationInfo", void 0), e([y({ type: [y2], json: { name: "layerDefinition.fields", write: { ignoreOrigin: true, isRequired: true }, origins: { service: { name: "fields" } } } })], M.prototype, "fields", void 0), e([y(Z.fieldsIndex)], M.prototype, "fieldsIndex", void 0), e([y({ type: w2, json: { name: "extent" } })], M.prototype, "fullExtent", void 0), e([y({ type: ["point", "polygon", "polyline", "multipoint"], json: { read: { reader: o.read } } })], M.prototype, "geometryType", void 0), e([y({ type: Boolean })], M.prototype, "hasZ", void 0), e([y(v2)], M.prototype, "id", void 0), e([y({ type: Boolean, readOnly: true })], M.prototype, "isTable", null), e([y(m2)], M.prototype, "labelsVisible", void 0), e([y({ type: [C], json: { name: "layerDefinition.drawingInfo.labelingInfo", read: { reader: i }, write: true } })], M.prototype, "labelingInfo", void 0), e([y(c)], M.prototype, "legendEnabled", void 0), e([y({ type: ["show", "hide"] })], M.prototype, "listMode", void 0), e([y({ type: String, json: { name: "layerDefinition.objectIdField", write: { ignoreOrigin: true, isRequired: true }, origins: { service: { name: "objectIdField" } } } })], M.prototype, "objectIdField", void 0), e([y(u)], M.prototype, "opacity", void 0), e([y({ type: ["GeoJSON"] })], M.prototype, "operationalLayerType", void 0), e([y({ readOnly: true })], M.prototype, "parsedUrl", null), e([y(p)], M.prototype, "popupEnabled", void 0), e([y({ type: k, json: { name: "popupInfo", write: true } })], M.prototype, "popupTemplate", void 0), e([y({ types: p3, json: { name: "layerDefinition.drawingInfo.renderer", write: true, origins: { service: { name: "drawingInfo.renderer" }, "web-scene": { types: n2 } } } })], M.prototype, "renderer", null), e([y(l2)], M.prototype, "screenSizePerspectiveEnabled", void 0), e([y({ readOnly: true })], M.prototype, "source", void 0), e([y({ type: f })], M.prototype, "spatialReference", void 0), e([y({ type: [p5] })], M.prototype, "templates", void 0), e([y()], M.prototype, "title", void 0), e([y({ json: { read: false }, readOnly: true })], M.prototype, "type", void 0), e([y({ type: String, readOnly: true })], M.prototype, "typeIdField", void 0), e([y({ type: [n4] })], M.prototype, "types", void 0), e([y(f2)], M.prototype, "url", null), M = e([a("esri.layers.GeoJSONLayer")], M);
var k2 = M;
export {
  k2 as default
};
//# sourceMappingURL=GeoJSONLayer-F2EEXNEO.js.map
