import {
  c,
  d,
  f,
  h,
  i,
  l,
  u,
  w
} from "./chunk-FJ3BZQJK.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";
export {
  u as addOrUpdateResource,
  h as contentToBlob,
  c as fetchResources,
  f as getSiblingOfSameType,
  w as getSiblingOfSameTypeI,
  l as removeAllResources,
  i as removeResource,
  d as splitPrefixFileNameAndExtension
};
//# sourceMappingURL=resourceUtils-PDWAQ4XN.js.map
