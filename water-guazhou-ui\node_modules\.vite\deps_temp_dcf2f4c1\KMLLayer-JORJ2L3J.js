import {
  S,
  d,
  g,
  j as j3
} from "./chunk-LRIDBBAY.js";
import {
  l as l2
} from "./chunk-TDC6MNNF.js";
import {
  p
} from "./chunk-JV6TBH5W.js";
import {
  _
} from "./chunk-TV6WKLTU.js";
import "./chunk-5VDORDNW.js";
import {
  c
} from "./chunk-FHKOFAQ2.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-P37TUI4J.js";
import {
  n as n2
} from "./chunk-LAEW33J6.js";
import {
  t
} from "./chunk-NGPCXWDX.js";
import "./chunk-IXMTA2RC.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  b as b2
} from "./chunk-67MHB3E3.js";
import "./chunk-MKG56XBH.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-N4YJNWPS.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-FCQRDLBQ.js";
import "./chunk-NUZU7NCS.js";
import "./chunk-LHJ7PAFO.js";
import "./chunk-7MQMIP4J.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import {
  f as f2
} from "./chunk-YJWWP4AU.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-EQYLMJEJ.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-3TXAWGPY.js";
import "./chunk-5EGPPD3R.js";
import "./chunk-L66EKMJP.js";
import "./chunk-3M3FTH72.js";
import "./chunk-XLHYMGQY.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-52L4L36S.js";
import "./chunk-VTOVDEW2.js";
import "./chunk-UKESBP6X.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-TD7BKFJX.js";
import "./chunk-LNGMNZ4I.js";
import "./chunk-24NZLSKM.js";
import "./chunk-BNFBWJYJ.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-23VNIYCK.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-OVIRLBJH.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import {
  m
} from "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  U,
  a as a2,
  j as j2,
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-4YKNTCHK.js";
import "./chunk-XVA5SA7P.js";
import {
  qt
} from "./chunk-U4SVMKOQ.js";
import "./chunk-CGBA4LNQ.js";
import "./chunk-2RZLOKYO.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-HHMRK4FX.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  u2
} from "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  b
} from "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  w
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import {
  u
} from "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/KMLSublayer.js
var S2;
var k = S2 = class extends n.EventedMixin(u2(m)) {
  constructor(...e2) {
    super(...e2), this.description = null, this.id = null, this.networkLink = null, this.sublayers = null, this.title = null, this.sourceJSON = null, this.fullExtent = null, this.addHandles([a2(() => this.sublayers, "after-add", ({ item: e3 }) => {
      e3.parent = this, e3.layer = this.layer;
    }, U), a2(() => this.sublayers, "after-remove", ({ item: e3 }) => {
      e3.layer = e3.parent = null;
    }, U), l(() => this.sublayers, (e3, t2) => {
      if (t2) for (const r3 of t2) r3.layer = r3.parent = null;
      if (e3) for (const r3 of e3) r3.parent = this, r3.layer = this.layer;
    }, U)]);
  }
  initialize() {
    j2(() => this.networkLink).then(() => j2(() => true === this.visible)).then(() => this.load());
  }
  load(e2) {
    var _a;
    if (!this.networkLink) return;
    if (this.networkLink.viewFormat) return;
    const r3 = r(e2) ? e2.signal : null, s = this._fetchService(((_a = this._get("networkLink")) == null ? void 0 : _a.href) ?? "", r3).then((e3) => {
      var _a2;
      const r4 = j3(e3.sublayers);
      this.fullExtent = w2.fromJSON(r4), this.sourceJSON = e3;
      const s2 = b(j.ofType(S2), S(S2, e3));
      this.sublayers ? this.sublayers.addMany(s2) : this.sublayers = s2, (_a2 = this.layer) == null ? void 0 : _a2.emit("sublayer-update"), this.layer && this.layer.notifyChange("visibleSublayers");
    });
    return this.addResolvingPromise(s), Promise.resolve(this);
  }
  get visible() {
    return this._get("visible");
  }
  set visible(e2) {
    this._get("visible") !== e2 && (this._set("visible", e2), this.layer && this.layer.notifyChange("visibleSublayers"));
  }
  readVisible(e2, t2) {
    return !!t2.visibility;
  }
  set layer(e2) {
    this._set("layer", e2), this.sublayers && this.sublayers.forEach((t2) => t2.layer = e2);
  }
  _fetchService(e2, t2) {
    return g(e2, this.layer.outSpatialReference, this.layer.refreshInterval, t2).then((e3) => d(e3.data));
  }
};
e([y()], k.prototype, "description", void 0), e([y()], k.prototype, "id", void 0), e([y({ readOnly: true, value: null })], k.prototype, "networkLink", void 0), e([y({ json: { write: { allowNull: true } } })], k.prototype, "parent", void 0), e([y({ type: j.ofType(S2), json: { write: { allowNull: true } } })], k.prototype, "sublayers", void 0), e([y({ value: null, json: { read: { source: "name", reader: (e2) => u(e2) } } })], k.prototype, "title", void 0), e([y({ value: true })], k.prototype, "visible", null), e([o("visible", ["visibility"])], k.prototype, "readVisible", null), e([y()], k.prototype, "sourceJSON", void 0), e([y({ value: null })], k.prototype, "layer", null), e([y({ type: w2 })], k.prototype, "fullExtent", void 0), k = S2 = e([a("esri.layers.support.KMLSublayer")], k);
var g2 = k;

// node_modules/@arcgis/core/layers/KMLLayer.js
var F = ["kml", "xml"];
var O2 = class extends n2(p(t(c(_(O(b2)))))) {
  constructor(...e2) {
    super(...e2), this._visibleFolders = [], this.allSublayers = new l2({ getCollections: () => [this.sublayers], getChildrenFunction: (e3) => e3.sublayers }), this.outSpatialReference = f.WGS84, this.path = null, this.legendEnabled = false, this.operationalLayerType = "KML", this.sublayers = null, this.type = "kml", this.url = null;
  }
  initialize() {
    this.addHandles([l(() => this.sublayers, (e2, r3) => {
      r3 && r3.forEach((e3) => {
        e3.parent = null, e3.layer = null;
      }), e2 && e2.forEach((e3) => {
        e3.parent = this, e3.layer = this;
      });
    }, U), this.on("sublayer-update", () => this.notifyChange("fullExtent"))]);
  }
  normalizeCtorArgs(e2, r3) {
    return "string" == typeof e2 ? { url: e2, ...r3 } : e2;
  }
  readSublayersFromItemOrWebMap(e2, r3) {
    this._visibleFolders = r3.visibleFolders;
  }
  readSublayers(e2, r3, t2) {
    return S(g2, r3, t2, this._visibleFolders);
  }
  writeSublayers(e2, r3) {
    const t2 = [], o2 = e2.toArray();
    for (; o2.length; ) {
      const e3 = o2[0];
      e3.networkLink || (e3.visible && t2.push(e3.id), e3.sublayers && o2.push(...e3.sublayers.toArray())), o2.shift();
    }
    r3.visibleFolders = t2;
  }
  get title() {
    const e2 = this._get("title");
    return e2 && "defaults" !== this.originOf("title") ? e2 : this.url ? qt(this.url, F) || "KML" : e2 || "";
  }
  set title(e2) {
    this._set("title", e2);
  }
  get visibleSublayers() {
    const e2 = this.sublayers, r3 = [], t2 = (e3) => {
      e3.visible && (r3.push(e3), e3.sublayers && e3.sublayers.forEach(t2));
    };
    return e2 && e2.forEach(t2), r3;
  }
  get fullExtent() {
    return this._recomputeFullExtent();
  }
  load(e2) {
    const r3 = r(e2) ? e2.signal : null;
    return this.addResolvingPromise(this.loadFromPortal({ supportedTypes: ["KML"], supportsData: false }, e2).catch(w).then(() => this._fetchService(r3))), Promise.resolve(this);
  }
  destroy() {
    super.destroy(), this.allSublayers.destroy();
  }
  async _fetchService(e2) {
    const r3 = await Promise.resolve().then(() => this.resourceInfo ? { ssl: false, data: this.resourceInfo } : g(this.url ?? "", this.outSpatialReference, this.refreshInterval, e2)), t2 = d(r3.data);
    t2 && this.read(t2, { origin: "service" });
  }
  _recomputeFullExtent() {
    let e2 = null;
    r(this.extent) && (e2 = this.extent.clone());
    const r3 = (t2) => {
      if (t2.sublayers) for (const s of t2.sublayers.items) r3(s), s.visible && s.fullExtent && (r(e2) ? e2.union(s.fullExtent) : e2 = s.fullExtent.clone());
    };
    return r3(this), e2;
  }
};
e([y({ readOnly: true })], O2.prototype, "allSublayers", void 0), e([y({ type: f })], O2.prototype, "outSpatialReference", void 0), e([y({ type: String, json: { origins: { "web-scene": { read: true, write: true } }, read: false } })], O2.prototype, "path", void 0), e([y({ readOnly: true, json: { read: false, write: false } })], O2.prototype, "legendEnabled", void 0), e([y({ type: ["show", "hide", "hide-children"] })], O2.prototype, "listMode", void 0), e([y({ type: ["KML"] })], O2.prototype, "operationalLayerType", void 0), e([y({})], O2.prototype, "resourceInfo", void 0), e([y({ type: j.ofType(g2), json: { write: { ignoreOrigin: true } } })], O2.prototype, "sublayers", void 0), e([o(["web-map", "portal-item"], "sublayers", ["visibleFolders"])], O2.prototype, "readSublayersFromItemOrWebMap", null), e([o("service", "sublayers", ["sublayers"])], O2.prototype, "readSublayers", null), e([r2("sublayers")], O2.prototype, "writeSublayers", null), e([y({ readOnly: true, json: { read: false } })], O2.prototype, "type", void 0), e([y({ json: { origins: { "web-map": { read: { source: "title" } } }, write: { ignoreOrigin: true } } })], O2.prototype, "title", null), e([y(f2)], O2.prototype, "url", void 0), e([y({ readOnly: true })], O2.prototype, "visibleSublayers", null), e([y({ type: w2 })], O2.prototype, "extent", void 0), e([y()], O2.prototype, "fullExtent", null), O2 = e([a("esri.layers.KMLLayer")], O2);
var M = O2;
export {
  M as default
};
//# sourceMappingURL=KMLLayer-JORJ2L3J.js.map
