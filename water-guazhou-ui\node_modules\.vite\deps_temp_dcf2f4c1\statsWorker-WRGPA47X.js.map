{"version": 3, "sources": ["../../@arcgis/core/smartMapping/statistics/support/utils.js", "../../@arcgis/core/smartMapping/statistics/support/statsWorker.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../geometry.js\";import e from\"../../../core/Error.js\";import{unwrap as t,isSome as n}from\"../../../core/maybe.js\";import{pt2px as r}from\"../../../core/screenUtils.js\";import i from\"../../../geometry/SpatialReference.js\";import{quantizePoint as o}from\"../../../geometry/support/quantizationUtils.js\";import{isWrappable as s,getInfo as l}from\"../../../geometry/support/spatialReferenceUtils.js\";import{isNumericField as a,numericTypes as u}from\"../../../layers/support/fieldUtils.js\";import{evaluateDensityKernel as f,createValueFunction as c}from\"../../../renderers/support/heatmapUtils.js\";import{processNullValue as m,getNormalizedValue as d}from\"../../../statistics/utils.js\";import{loadArcade as p}from\"../../../support/arcadeOnDemand.js\";import h from\"../../../geometry/Point.js\";let y=null;function $(e,t,n){return e.x<0?e.x+=t:e.x>n&&(e.x-=t),e}function I(e,n,r,i){const a=s(r)?l(r):null,u=a?Math.round((a.valid[1]-a.valid[0])/n.scale[0]):null;return e.map((e=>{const r=new h(t(e.geometry));return o(n,r,r,r.hasZ,r.hasM),e.geometry=a?$(r,u,i[0]):r,e}))}function g(e,t=18,n,i,o,s){const l=new Float64Array(o*s);t=Math.round(r(t));let a=Number.POSITIVE_INFINITY,u=Number.NEGATIVE_INFINITY,m=0,d=0,p=0,h=0;const y=c(i,n);for(const{geometry:r,attributes:c}of e){const{x:e,y:n}=r,i=Math.max(0,e-t),$=Math.max(0,n-t),I=Math.min(s,n+t),g=Math.min(o,e+t),j=+y(c);for(let r=$;r<I;r++)for(let s=i;s<g;s++){const i=r*o+s,c=f(s-e,r-n,t),y=l[i];m=l[i]+=c*j;const $=m-y;d+=$,p+=$*$,m<a&&(a=m),m>u&&(u=m),h++}}if(!h)return{mean:0,stddev:0,min:0,max:0,mid:0,count:0};const $=(u-a)/2;return{mean:d/h,stdDev:Math.sqrt((p-d*d/h)/h),min:a,max:u,mid:$,count:h}}async function j(e,t){if(!t)return[];const{field:n,field2:r,field3:o,fieldDelimiter:s}=e,l=e.valueExpression,a=e.normalizationType,u=e.normalizationField,f=e.normalizationTotal,c=[],h=e.viewInfoParams;let $=null,I=null;if(l){if(!y){const{arcadeUtils:e}=await p();y=e}$=y.createFunction(l),I=h&&y.getViewInfo({viewingMode:h.viewingMode,scale:h.scale,spatialReference:new i(h.spatialReference)})}const g=e.fieldInfos,j=!(t[0]&&\"declaredClass\"in t[0]&&\"esri.Graphic\"===t[0].declaredClass)&&g?{fields:g}:null;return t.forEach((e=>{const t=e.attributes;let i;if(l){const t=j?{...e,layer:j}:e,n=y.createExecContext(t,I);i=y.executeFunction($,n)}else t&&(i=t[n],r&&(i=`${m(i)}${s}${m(t[r])}`,o&&(i=`${i}${s}${m(t[o])}`)));if(a&&\"number\"==typeof i&&isFinite(i)){const e=t&&parseFloat(t[u]);i=d(i,a,e,f)}c.push(i)})),c}function x(e){const t=e.field,n=e.normalizationType,r=e.normalizationField;let i;return\"field\"===n?i=\"(NOT \"+r+\" = 0)\":\"log\"!==n&&\"natural-log\"!==n&&\"square-root\"!==n||(i=`(${t} > 0)`),i}function F(t,n){return new e(t,n)}function b(e,t,n){const r=null!=t?e+\" >= \"+t:\"\",i=null!=n?e+\" <= \"+n:\"\";let o=\"\";return o=r&&i?N(r,i):r||i,o?\"(\"+o+\")\":\"\"}function v(e,t,n,r){let i=null;return t?t.name!==e.objectIdField&&r.includes(t.type)||(i=F(n,\"'field' should be one of these types: \"+r.join(\",\"))):i=F(n,\"'field' is not defined in the layer schema\"),i}function E(e,t,n){let r;return t?t.name!==e.objectIdField&&a(t)||(r=F(n,\"'field' should be one of these numeric types: \"+u.join(\",\"))):r=F(n,\"'field' is not defined in the layer schema\"),r}function N(e,t){let r=n(e)?e:\"\";return n(t)&&t&&(r=r?\"(\"+r+\") AND (\"+t+\")\":t),r}function w(e,t){if(e&&\"intersects\"!==e.spatialRelationship)return F(t,\"Only 'intersects' spatialRelationship is supported for featureFilter\")}function M(e,t,n){const r=U({layer:e,fields:t});if(r.length)return F(n,\"Unknown fields: \"+r.join(\", \")+\". You can only use fields defined in the layer schema\");const i=T({layer:e,fields:t});return i.length?F(n,\"Unsupported fields: \"+i.join(\", \")+\". You can only use fields that can be fetched i.e. AdapterFieldUsageInfo.supportsStatistics must be true\"):void 0}function U(e){const t=e.layer;return e.fields.filter((e=>!t.getField(e)))}function T(e){const t=e.layer;return e.fields.filter((e=>{const n=t.getFieldUsageInfo(e);return!n||!n.supportsStatistics}))}function z(e,t,n){const r=[],i=[],o=[],s=[],l=[];e.forEach(((e,t)=>{const a=e.field?\"field\":\"expression\",u=e.field||e.valueExpression;e.field?(l.push(u),i.push(`var ${a}${t} = Number($feature[\"${u}\"]);`)):(r.push(`function getValueForExpr${t}() {\\n  ${u} \\n}`),i.push(`var ${a}${t} = Number(getValueForExpr${t}());`)),n||o.push(`${a}${t} = IIf(${a}${t} < 0, 0, ${a}${t});`),s.push(`${a}${t}`)}));let a=\"return sum;\";const u=r.length?null:l.reduce(((e,t)=>`${e} + ${t}`));let f=null;t||n?t?n||(a=\"return IIf(sum >= 0, sum, null);\",u&&(f=`(( ${u} ) >= 0)`)):(a=\"return IIf(sum != 0, sum, null);\",u&&(f=`(( ${u} ) <> 0)`)):(a=\"return IIf(sum > 0, sum, null);\",u&&(f=`(( ${u} ) > 0)`));return{valueExpression:[r.length?r.join(\"\\n\"):\"\",i.join(\"\\n\"),o.join(\"\\n\"),`var sum = ${s.join(\" + \")};`,a].filter(Boolean).join(\"\\n\\n\"),sqlExpression:u,sqlWhere:f}}export{g as calculateHeatmapStats,F as createError,j as getDataValues,b as getRangeExpr,x as getSQLFilterForNormalization,z as getSumOfAttributesExpr,N as mergeWhereClauses,I as quantizeFeatures,M as verifyBasicFieldValidity,v as verifyFieldType,w as verifyFilterValidty,E as verifyNumericField};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{getDataValues as a,quantizeFeatures as i,calculateHeatmapStats as e}from\"./utils.js\";import{isNullCountSupported as n,calculateStringStatistics as l,calculateStatistics as t,processSummaryStatisticsResult as o,calculateUniqueValuesCount as s,createUVResult as r,calculateClassBreaks as m,resolveCBResult as f,calculateHistogram as u}from\"../../../statistics/utils.js\";async function d(i){const{attribute:e,features:s}=i,{normalizationType:r,normalizationField:m,minValue:f,maxValue:u,fieldType:d}=e,p=await a({field:e.field,valueExpression:e.valueExpression,normalizationType:r,normalizationField:m,normalizationTotal:e.normalizationTotal,viewInfoParams:e.viewInfoParams,fieldInfos:e.fieldInfos},s),v=n({normalizationType:r,normalizationField:m,minValue:f,maxValue:u}),c={value:.5,fieldType:d},z=\"esriFieldTypeString\"===d?l({values:p,supportsNullCount:v,percentileParams:c}):t({values:p,minValue:f,maxValue:u,useSampleStdDev:!r,supportsNullCount:v,percentileParams:c});return o(z,\"esriFieldTypeDate\"===d)}async function p(i){const{attribute:e,features:n}=i,l=await a({field:e.field,field2:e.field2,field3:e.field3,fieldDelimiter:e.fieldDelimiter,valueExpression:e.valueExpression,viewInfoParams:e.viewInfoParams,fieldInfos:e.fieldInfos},n),t=s(l);return r(t,e.domains,e.returnAllCodedValues,e.fieldDelimiter)}async function v(i){const{attribute:e,features:n}=i,{field:l,normalizationType:t,normalizationField:o,normalizationTotal:s,classificationMethod:r}=e,u=await a({field:l,valueExpression:e.valueExpression,normalizationType:t,normalizationField:o,normalizationTotal:s,viewInfoParams:e.viewInfoParams,fieldInfos:e.fieldInfos},n),d=m(u,{field:l,normalizationType:t,normalizationField:o,normalizationTotal:s,classificationMethod:r,standardDeviationInterval:e.standardDeviationInterval,numClasses:e.numClasses,minValue:e.minValue,maxValue:e.maxValue});return f(d,r)}async function c(i){const{attribute:e,features:n}=i,{field:l,normalizationType:t,normalizationField:o,normalizationTotal:s,classificationMethod:r}=e,m=await a({field:l,valueExpression:e.valueExpression,normalizationType:t,normalizationField:o,normalizationTotal:s,viewInfoParams:e.viewInfoParams,fieldInfos:e.fieldInfos},n);return u(m,{field:l,normalizationType:t,normalizationField:o,normalizationTotal:s,classificationMethod:r,standardDeviationInterval:e.standardDeviationInterval,numBins:e.numBins,minValue:e.minValue,maxValue:e.maxValue})}async function z(a){const{attribute:n,features:l}=a,{field:t,radius:o,fieldOffset:s,transform:r,spatialReference:m,size:f}=n,u=i(l,r,m,f),{count:d,min:p,max:v,mean:c,stdDev:z}=e(u,o,s,t,f[0],f[1]);return{count:d,min:p,max:v,avg:c,stddev:z}}export{v as classBreaks,z as heatmapStatistics,c as histogram,d as summaryStatistics,p as uniqueValues};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI6xB,IAAIA,KAAE;AAAK,SAAS,EAAEC,IAAE,GAAE,GAAE;AAAC,SAAOA,GAAE,IAAE,IAAEA,GAAE,KAAG,IAAEA,GAAE,IAAE,MAAIA,GAAE,KAAG,IAAGA;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAEC,IAAEC,IAAE;AAAC,QAAM,IAAE,EAAED,EAAC,IAAE,EAAEA,EAAC,IAAE,MAAKE,KAAE,IAAE,KAAK,OAAO,EAAE,MAAM,CAAC,IAAE,EAAE,MAAM,CAAC,KAAG,EAAE,MAAM,CAAC,CAAC,IAAE;AAAK,SAAOH,GAAE,IAAK,CAAAA,OAAG;AAAC,UAAMC,KAAE,IAAI,EAAE,EAAED,GAAE,QAAQ,CAAC;AAAE,WAAO,EAAE,GAAEC,IAAEA,IAAEA,GAAE,MAAKA,GAAE,IAAI,GAAED,GAAE,WAAS,IAAE,EAAEC,IAAEE,IAAED,GAAE,CAAC,CAAC,IAAED,IAAED;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE,IAAE,IAAG,GAAEE,IAAE,GAAEE,IAAE;AAAC,QAAM,IAAE,IAAI,aAAa,IAAEA,EAAC;AAAE,MAAE,KAAK,MAAM,EAAE,CAAC,CAAC;AAAE,MAAI,IAAE,OAAO,mBAAkBD,KAAE,OAAO,mBAAkBE,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAE,IAAE;AAAE,QAAMR,KAAE,EAAEG,IAAE,CAAC;AAAE,aAAS,EAAC,UAASD,IAAE,YAAWO,GAAC,KAAIR,IAAE;AAAC,UAAK,EAAC,GAAEA,IAAE,GAAES,GAAC,IAAER,IAAEC,KAAE,KAAK,IAAI,GAAEF,KAAE,CAAC,GAAEU,KAAE,KAAK,IAAI,GAAED,KAAE,CAAC,GAAEE,KAAE,KAAK,IAAIP,IAAEK,KAAE,CAAC,GAAEG,KAAE,KAAK,IAAI,GAAEZ,KAAE,CAAC,GAAEa,KAAE,CAACd,GAAES,EAAC;AAAE,aAAQP,KAAES,IAAET,KAAEU,IAAEV,KAAI,UAAQG,KAAEF,IAAEE,KAAEQ,IAAER,MAAI;AAAC,YAAMF,KAAED,KAAE,IAAEG,IAAEI,KAAE,EAAEJ,KAAEJ,IAAEC,KAAEQ,IAAE,CAAC,GAAEV,KAAE,EAAEG,EAAC;AAAE,MAAAG,KAAE,EAAEH,EAAC,KAAGM,KAAEK;AAAE,YAAMH,KAAEL,KAAEN;AAAE,MAAAO,MAAGI,IAAEH,MAAGG,KAAEA,IAAEL,KAAE,MAAI,IAAEA,KAAGA,KAAEF,OAAIA,KAAEE,KAAG;AAAA,IAAG;AAAA,EAAC;AAAC,MAAG,CAAC,EAAE,QAAM,EAAC,MAAK,GAAE,QAAO,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,OAAM,EAAC;AAAE,QAAMK,MAAGP,KAAE,KAAG;AAAE,SAAM,EAAC,MAAKG,KAAE,GAAE,QAAO,KAAK,MAAMC,KAAED,KAAEA,KAAE,KAAG,CAAC,GAAE,KAAI,GAAE,KAAIH,IAAE,KAAIO,IAAE,OAAM,EAAC;AAAC;AAAC,eAAe,EAAEV,IAAE,GAAE;AAAC,MAAG,CAAC,EAAE,QAAM,CAAC;AAAE,QAAK,EAAC,OAAM,GAAE,QAAOC,IAAE,QAAO,GAAE,gBAAeG,GAAC,IAAEJ,IAAE,IAAEA,GAAE,iBAAgB,IAAEA,GAAE,mBAAkBG,KAAEH,GAAE,oBAAmBc,KAAEd,GAAE,oBAAmBQ,KAAE,CAAC,GAAE,IAAER,GAAE;AAAe,MAAIU,KAAE,MAAKC,KAAE;AAAK,MAAG,GAAE;AAAC,QAAG,CAACZ,IAAE;AAAC,YAAK,EAAC,aAAYC,GAAC,IAAE,MAAM,EAAE;AAAE,MAAAD,KAAEC;AAAA,IAAC;AAAC,IAAAU,KAAEX,GAAE,eAAe,CAAC,GAAEY,KAAE,KAAGZ,GAAE,YAAY,EAAC,aAAY,EAAE,aAAY,OAAM,EAAE,OAAM,kBAAiB,IAAI,EAAE,EAAE,gBAAgB,EAAC,CAAC;AAAA,EAAC;AAAC,QAAMa,KAAEZ,GAAE,YAAWa,KAAE,EAAE,EAAE,CAAC,KAAG,mBAAkB,EAAE,CAAC,KAAG,mBAAiB,EAAE,CAAC,EAAE,kBAAgBD,KAAE,EAAC,QAAOA,GAAC,IAAE;AAAK,SAAO,EAAE,QAAS,CAAAZ,OAAG;AAAC,UAAMe,KAAEf,GAAE;AAAW,QAAIE;AAAE,QAAG,GAAE;AAAC,YAAMa,KAAEF,KAAE,EAAC,GAAGb,IAAE,OAAMa,GAAC,IAAEb,IAAES,KAAEV,GAAE,kBAAkBgB,IAAEJ,EAAC;AAAE,MAAAT,KAAEH,GAAE,gBAAgBW,IAAED,EAAC;AAAA,IAAC,MAAM,CAAAM,OAAIb,KAAEa,GAAE,CAAC,GAAEd,OAAIC,KAAE,GAAG,EAAEA,EAAC,CAAC,GAAGE,EAAC,GAAG,EAAEW,GAAEd,EAAC,CAAC,CAAC,IAAG,MAAIC,KAAE,GAAGA,EAAC,GAAGE,EAAC,GAAG,EAAEW,GAAE,CAAC,CAAC,CAAC;AAAM,QAAG,KAAG,YAAU,OAAOb,MAAG,SAASA,EAAC,GAAE;AAAC,YAAMF,KAAEe,MAAG,WAAWA,GAAEZ,EAAC,CAAC;AAAE,MAAAD,KAAE,EAAEA,IAAE,GAAEF,IAAEc,EAAC;AAAA,IAAC;AAAC,IAAAN,GAAE,KAAKN,EAAC;AAAA,EAAC,CAAE,GAAEM;AAAC;;;ACA7iE,eAAeQ,GAAEC,IAAE;AAAC,QAAK,EAAC,WAAUC,IAAE,UAASC,GAAC,IAAEF,IAAE,EAAC,mBAAkBG,IAAE,oBAAmBC,IAAE,UAASC,IAAE,UAASC,IAAE,WAAUP,GAAC,IAAEE,IAAEM,KAAE,MAAM,EAAE,EAAC,OAAMN,GAAE,OAAM,iBAAgBA,GAAE,iBAAgB,mBAAkBE,IAAE,oBAAmBC,IAAE,oBAAmBH,GAAE,oBAAmB,gBAAeA,GAAE,gBAAe,YAAWA,GAAE,WAAU,GAAEC,EAAC,GAAEM,KAAEJ,GAAE,EAAC,mBAAkBD,IAAE,oBAAmBC,IAAE,UAASC,IAAE,UAASC,GAAC,CAAC,GAAEG,KAAE,EAAC,OAAM,KAAG,WAAUV,GAAC,GAAEW,KAAE,0BAAwBX,KAAEM,GAAE,EAAC,QAAOE,IAAE,mBAAkBC,IAAE,kBAAiBC,GAAC,CAAC,IAAE,EAAE,EAAC,QAAOF,IAAE,UAASF,IAAE,UAASC,IAAE,iBAAgB,CAACH,IAAE,mBAAkBK,IAAE,kBAAiBC,GAAC,CAAC;AAAE,SAAO,EAAEC,IAAE,wBAAsBX,EAAC;AAAC;AAAC,eAAeQ,GAAEP,IAAE;AAAC,QAAK,EAAC,WAAUC,IAAE,UAAS,EAAC,IAAED,IAAE,IAAE,MAAM,EAAE,EAAC,OAAMC,GAAE,OAAM,QAAOA,GAAE,QAAO,QAAOA,GAAE,QAAO,gBAAeA,GAAE,gBAAe,iBAAgBA,GAAE,iBAAgB,gBAAeA,GAAE,gBAAe,YAAWA,GAAE,WAAU,GAAE,CAAC,GAAE,IAAEU,GAAE,CAAC;AAAE,SAAO,EAAE,GAAEV,GAAE,SAAQA,GAAE,sBAAqBA,GAAE,cAAc;AAAC;AAAC,eAAe,EAAED,IAAE;AAAC,QAAK,EAAC,WAAUC,IAAE,UAAS,EAAC,IAAED,IAAE,EAAC,OAAM,GAAE,mBAAkB,GAAE,oBAAmB,GAAE,oBAAmBE,IAAE,sBAAqBC,GAAC,IAAEF,IAAEK,KAAE,MAAM,EAAE,EAAC,OAAM,GAAE,iBAAgBL,GAAE,iBAAgB,mBAAkB,GAAE,oBAAmB,GAAE,oBAAmBC,IAAE,gBAAeD,GAAE,gBAAe,YAAWA,GAAE,WAAU,GAAE,CAAC,GAAEF,KAAE,EAAEO,IAAE,EAAC,OAAM,GAAE,mBAAkB,GAAE,oBAAmB,GAAE,oBAAmBJ,IAAE,sBAAqBC,IAAE,2BAA0BF,GAAE,2BAA0B,YAAWA,GAAE,YAAW,UAASA,GAAE,UAAS,UAASA,GAAE,SAAQ,CAAC;AAAE,SAAO,EAAEF,IAAEI,EAAC;AAAC;AAAC,eAAeM,GAAET,IAAE;AAAC,QAAK,EAAC,WAAUC,IAAE,UAAS,EAAC,IAAED,IAAE,EAAC,OAAM,GAAE,mBAAkB,GAAE,oBAAmB,GAAE,oBAAmBE,IAAE,sBAAqBC,GAAC,IAAEF,IAAEG,KAAE,MAAM,EAAE,EAAC,OAAM,GAAE,iBAAgBH,GAAE,iBAAgB,mBAAkB,GAAE,oBAAmB,GAAE,oBAAmBC,IAAE,gBAAeD,GAAE,gBAAe,YAAWA,GAAE,WAAU,GAAE,CAAC;AAAE,SAAO,EAAEG,IAAE,EAAC,OAAM,GAAE,mBAAkB,GAAE,oBAAmB,GAAE,oBAAmBF,IAAE,sBAAqBC,IAAE,2BAA0BF,GAAE,2BAA0B,SAAQA,GAAE,SAAQ,UAASA,GAAE,UAAS,UAASA,GAAE,SAAQ,CAAC;AAAC;AAAC,eAAeS,GAAE,GAAE;AAAC,QAAK,EAAC,WAAU,GAAE,UAAS,EAAC,IAAE,GAAE,EAAC,OAAM,GAAE,QAAO,GAAE,aAAYR,IAAE,WAAUC,IAAE,kBAAiBC,IAAE,MAAKC,GAAC,IAAE,GAAEC,KAAE,EAAE,GAAEH,IAAEC,IAAEC,EAAC,GAAE,EAAC,OAAMN,IAAE,KAAIQ,IAAE,KAAIC,IAAE,MAAKC,IAAE,QAAOC,GAAC,IAAE,EAAEJ,IAAE,GAAEJ,IAAE,GAAEG,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE,SAAM,EAAC,OAAMN,IAAE,KAAIQ,IAAE,KAAIC,IAAE,KAAIC,IAAE,QAAOC,GAAC;AAAC;", "names": ["y", "e", "r", "i", "u", "s", "m", "d", "p", "c", "n", "$", "I", "g", "j", "f", "t", "d", "i", "e", "s", "r", "m", "f", "u", "p", "v", "c", "z", "y"]}