import {
  D
} from "./chunk-RYDDWHB5.js";
import "./chunk-XR4QWT37.js";
import "./chunk-6FMMG4VO.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-XAC3PEBY.js";
import "./chunk-GCZ6JHKQ.js";
import "./chunk-ERH4WAJU.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-N73MYEJE.js";
import "./chunk-C65HMCEM.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-DVCBUZVC.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-5JDQNIY4.js";
import "./chunk-J3EWJTCQ.js";
import "./chunk-HWB4LNSZ.js";
import {
  e as e2
} from "./chunk-JSZR3BUH.js";
import "./chunk-R6ZFHGHU.js";
import "./chunk-WAPZ634R.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-BMTNBZRF.js";
import "./chunk-YDRLAXYR.js";
import "./chunk-RURSJOSG.js";
import "./chunk-ZQY4DQCR.js";
import "./chunk-WZNPTIYX.js";
import "./chunk-22FAZXOH.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-77E52HT5.js";
import "./chunk-SROTSYJS.js";
import "./chunk-YBNKNHCD.js";
import "./chunk-M5RPNIHK.js";
import "./chunk-KYTIKHPN.js";
import "./chunk-6JIS2R4B.js";
import "./chunk-7BSY2CUN.js";
import "./chunk-6RN6WNY4.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-7UNBPRRZ.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-IXMTA2RC.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-USWRDFDJ.js";
import "./chunk-XM4RHPJM.js";
import "./chunk-6T5FEO66.js";
import "./chunk-FZ7BG3VX.js";
import {
  x
} from "./chunk-N4YJNWPS.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-ORU3OGKZ.js";
import "./chunk-LHJ7PAFO.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-FRJZOKDL.js";
import "./chunk-DKEAXJKM.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-YJWWP4AU.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-EQYLMJEJ.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-L66EKMJP.js";
import "./chunk-3M3FTH72.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-52L4L36S.js";
import "./chunk-VTOVDEW2.js";
import "./chunk-UKESBP6X.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-TD7BKFJX.js";
import "./chunk-LNGMNZ4I.js";
import "./chunk-24NZLSKM.js";
import "./chunk-BNFBWJYJ.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-23VNIYCK.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-OVIRLBJH.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import {
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-4YKNTCHK.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-CGBA4LNQ.js";
import "./chunk-2RZLOKYO.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-HHMRK4FX.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  r as r2
} from "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/StreamLayerView2D.js
function u(e3, t2) {
  if (t(e3) && t(t2)) return null;
  const r3 = {};
  return r(t2) && (r3.geometry = t2.toJSON()), r(e3) && (r3.where = e3), r3;
}
var m = class extends D {
  constructor() {
    super(...arguments), this._enabledEventTypes = /* @__PURE__ */ new Set(), this._isUserPaused = false, this.errorString = null, this.connectionStatus = "disconnected";
  }
  initialize() {
    this.addHandles([l(() => this.layer.customParameters, (e3) => this._proxy.updateCustomParameters(e3)), this.layer.on("send-message-to-socket", (e3) => this._proxy.sendMessageToSocket(e3)), this.layer.on("send-message-to-client", (e3) => this._proxy.sendMessageToClient(e3)), l(() => this.layer.purgeOptions, () => this._update()), l(() => this.suspended, (e3) => {
      e3 ? this._proxy.pauseStream() : this._isUserPaused || this._proxy.resumeStream();
    })], "constructor");
  }
  get connectionError() {
    if (this.errorString) return new s("stream-controller", this.errorString);
  }
  pause() {
    this._isUserPaused = true, this._proxy.pauseStream();
  }
  resume() {
    this._isUserPaused = false, this._proxy.resumeStream();
  }
  on(e3, t2) {
    if (Array.isArray(e3)) return r2(e3.map((e4) => this.on(e4, t2)));
    const s2 = ["data-received", "message-received"].includes(e3);
    s2 && (this._enabledEventTypes.add(e3), this._proxy.enableEvent(e3, true));
    const o = super.on(e3, t2), i = this;
    return { remove() {
      o.remove(), s2 && (i._proxy.closed || i.hasEventListener(e3) || i._proxy.enableEvent(e3, false));
    } };
  }
  queryLatestObservations(e3, r3) {
    var _a, _b, _c;
    if (!(((_a = this.layer.timeInfo) == null ? void 0 : _a.endField) || ((_b = this.layer.timeInfo) == null ? void 0 : _b.startField) || ((_c = this.layer.timeInfo) == null ? void 0 : _c.trackIdField))) throw new s("streamlayer-no-timeField", "queryLatestObservation can only be used with services that define a TrackIdField");
    return this._proxy.queryLatestObservations(this._cleanUpQuery(e3), r3).then((e4) => {
      const t2 = x.fromJSON(e4);
      return t2.features.forEach((e5) => {
        e5.layer = this.layer, e5.sourceLayer = this.layer;
      }), t2;
    });
  }
  detach() {
    super.detach(), this.connectionStatus = "disconnected";
  }
  _createClientOptions() {
    return { ...super._createClientOptions(), setProperty: (e3) => {
      this.set(e3.propertyName, e3.value);
    } };
  }
  _createTileRendererHash(e3) {
    const t2 = `${JSON.stringify(this.layer.purgeOptions)}.${JSON.stringify(u(this.layer.definitionExpression, this.layer.geometryDefinition))})`;
    return super._createTileRendererHash(e3) + t2;
  }
  async _createServiceOptions() {
    const e3 = this.layer, { objectIdField: t2 } = e3, r3 = e3.fields.map((e4) => e4.toJSON()), s2 = e2(e3.geometryType), o = e3.timeInfo && e3.timeInfo.toJSON() || null, i = e3.spatialReference ? e3.spatialReference.toJSON() : null;
    return { type: "stream", fields: r3, geometryType: s2, objectIdField: t2, timeInfo: o, source: this.layer.parsedUrl, serviceFilter: u(this.layer.definitionExpression, this.layer.geometryDefinition), purgeOptions: this.layer.purgeOptions.toJSON(), enabledEventTypes: Array.from(this._enabledEventTypes.values()), spatialReference: i, maxReconnectionAttempts: this.layer.maxReconnectionAttempts, maxReconnectionInterval: this.layer.maxReconnectionInterval, updateInterval: this.layer.updateInterval, customParameters: e3.customParameters };
  }
};
e([y()], m.prototype, "errorString", void 0), e([y({ readOnly: true })], m.prototype, "connectionError", null), e([y()], m.prototype, "connectionStatus", void 0), m = e([a("esri.views.2d.layers.StreamLayerView2D")], m);
var y2 = m;
export {
  y2 as default
};
//# sourceMappingURL=StreamLayerView2D-77K2O2YM.js.map
