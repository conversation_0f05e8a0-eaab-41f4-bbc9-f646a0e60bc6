{"version": 3, "sources": ["../../@codemirror/commands/dist/index.js", "../../crelt/index.js", "../../@codemirror/search/dist/index.js", "../../@codemirror/lint/dist/index.js", "../../codemirror/dist/index.js", "../../vue-codeMirror/src/config.ts", "../../vue-codeMirror/src/codemirror.ts", "../../vue-codeMirror/src/events.ts", "../../vue-codeMirror/src/props.ts", "../../vue-codeMirror/src/component.ts", "../../vue-codeMirror/src/index.ts"], "sourcesContent": ["import { Annotation, Facet, combineConfig, StateField, Transaction, ChangeSet, ChangeDesc, EditorSelection, StateEffect, Text, findClusterBreak, countColumn, CharCategory } from '@codemirror/state';\nimport { EditorView, Direction } from '@codemirror/view';\nimport { IndentContext, getIndentation, indentString, matchBrackets, syntaxTree, getIndentUnit, indentUnit } from '@codemirror/language';\nimport { NodeProp } from '@lezer/common';\n\n/**\nComment or uncomment the current selection. Will use line comments\nif available, otherwise falling back to block comments.\n*/\nconst toggleComment = target => {\n    let { state } = target, line = state.doc.lineAt(state.selection.main.from), config = getConfig(target.state, line.from);\n    return config.line ? toggleLineComment(target) : config.block ? toggleBlockCommentByLine(target) : false;\n};\nfunction command(f, option) {\n    return ({ state, dispatch }) => {\n        if (state.readOnly)\n            return false;\n        let tr = f(option, state);\n        if (!tr)\n            return false;\n        dispatch(state.update(tr));\n        return true;\n    };\n}\n/**\nComment or uncomment the current selection using line comments.\nThe line comment syntax is taken from the\n[`commentTokens`](https://codemirror.net/6/docs/ref/#commands.CommentTokens) [language\ndata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt).\n*/\nconst toggleLineComment = /*@__PURE__*/command(changeLineComment, 0 /* CommentOption.Toggle */);\n/**\nComment the current selection using line comments.\n*/\nconst lineComment = /*@__PURE__*/command(changeLineComment, 1 /* CommentOption.Comment */);\n/**\nUncomment the current selection using line comments.\n*/\nconst lineUncomment = /*@__PURE__*/command(changeLineComment, 2 /* CommentOption.Uncomment */);\n/**\nComment or uncomment the current selection using block comments.\nThe block comment syntax is taken from the\n[`commentTokens`](https://codemirror.net/6/docs/ref/#commands.CommentTokens) [language\ndata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt).\n*/\nconst toggleBlockComment = /*@__PURE__*/command(changeBlockComment, 0 /* CommentOption.Toggle */);\n/**\nComment the current selection using block comments.\n*/\nconst blockComment = /*@__PURE__*/command(changeBlockComment, 1 /* CommentOption.Comment */);\n/**\nUncomment the current selection using block comments.\n*/\nconst blockUncomment = /*@__PURE__*/command(changeBlockComment, 2 /* CommentOption.Uncomment */);\n/**\nComment or uncomment the lines around the current selection using\nblock comments.\n*/\nconst toggleBlockCommentByLine = /*@__PURE__*/command((o, s) => changeBlockComment(o, s, selectedLineRanges(s)), 0 /* CommentOption.Toggle */);\nfunction getConfig(state, pos) {\n    let data = state.languageDataAt(\"commentTokens\", pos);\n    return data.length ? data[0] : {};\n}\nconst SearchMargin = 50;\n/**\nDetermines if the given range is block-commented in the given\nstate.\n*/\nfunction findBlockComment(state, { open, close }, from, to) {\n    let textBefore = state.sliceDoc(from - SearchMargin, from);\n    let textAfter = state.sliceDoc(to, to + SearchMargin);\n    let spaceBefore = /\\s*$/.exec(textBefore)[0].length, spaceAfter = /^\\s*/.exec(textAfter)[0].length;\n    let beforeOff = textBefore.length - spaceBefore;\n    if (textBefore.slice(beforeOff - open.length, beforeOff) == open &&\n        textAfter.slice(spaceAfter, spaceAfter + close.length) == close) {\n        return { open: { pos: from - spaceBefore, margin: spaceBefore && 1 },\n            close: { pos: to + spaceAfter, margin: spaceAfter && 1 } };\n    }\n    let startText, endText;\n    if (to - from <= 2 * SearchMargin) {\n        startText = endText = state.sliceDoc(from, to);\n    }\n    else {\n        startText = state.sliceDoc(from, from + SearchMargin);\n        endText = state.sliceDoc(to - SearchMargin, to);\n    }\n    let startSpace = /^\\s*/.exec(startText)[0].length, endSpace = /\\s*$/.exec(endText)[0].length;\n    let endOff = endText.length - endSpace - close.length;\n    if (startText.slice(startSpace, startSpace + open.length) == open &&\n        endText.slice(endOff, endOff + close.length) == close) {\n        return { open: { pos: from + startSpace + open.length,\n                margin: /\\s/.test(startText.charAt(startSpace + open.length)) ? 1 : 0 },\n            close: { pos: to - endSpace - close.length,\n                margin: /\\s/.test(endText.charAt(endOff - 1)) ? 1 : 0 } };\n    }\n    return null;\n}\nfunction selectedLineRanges(state) {\n    let ranges = [];\n    for (let r of state.selection.ranges) {\n        let fromLine = state.doc.lineAt(r.from);\n        let toLine = r.to <= fromLine.to ? fromLine : state.doc.lineAt(r.to);\n        let last = ranges.length - 1;\n        if (last >= 0 && ranges[last].to > fromLine.from)\n            ranges[last].to = toLine.to;\n        else\n            ranges.push({ from: fromLine.from + /^\\s*/.exec(fromLine.text)[0].length, to: toLine.to });\n    }\n    return ranges;\n}\n// Performs toggle, comment and uncomment of block comments in\n// languages that support them.\nfunction changeBlockComment(option, state, ranges = state.selection.ranges) {\n    let tokens = ranges.map(r => getConfig(state, r.from).block);\n    if (!tokens.every(c => c))\n        return null;\n    let comments = ranges.map((r, i) => findBlockComment(state, tokens[i], r.from, r.to));\n    if (option != 2 /* CommentOption.Uncomment */ && !comments.every(c => c)) {\n        return { changes: state.changes(ranges.map((range, i) => {\n                if (comments[i])\n                    return [];\n                return [{ from: range.from, insert: tokens[i].open + \" \" }, { from: range.to, insert: \" \" + tokens[i].close }];\n            })) };\n    }\n    else if (option != 1 /* CommentOption.Comment */ && comments.some(c => c)) {\n        let changes = [];\n        for (let i = 0, comment; i < comments.length; i++)\n            if (comment = comments[i]) {\n                let token = tokens[i], { open, close } = comment;\n                changes.push({ from: open.pos - token.open.length, to: open.pos + open.margin }, { from: close.pos - close.margin, to: close.pos + token.close.length });\n            }\n        return { changes };\n    }\n    return null;\n}\n// Performs toggle, comment and uncomment of line comments.\nfunction changeLineComment(option, state, ranges = state.selection.ranges) {\n    let lines = [];\n    let prevLine = -1;\n    for (let { from, to } of ranges) {\n        let startI = lines.length, minIndent = 1e9;\n        let token = getConfig(state, from).line;\n        if (!token)\n            continue;\n        for (let pos = from; pos <= to;) {\n            let line = state.doc.lineAt(pos);\n            if (line.from > prevLine && (from == to || to > line.from)) {\n                prevLine = line.from;\n                let indent = /^\\s*/.exec(line.text)[0].length;\n                let empty = indent == line.length;\n                let comment = line.text.slice(indent, indent + token.length) == token ? indent : -1;\n                if (indent < line.text.length && indent < minIndent)\n                    minIndent = indent;\n                lines.push({ line, comment, token, indent, empty, single: false });\n            }\n            pos = line.to + 1;\n        }\n        if (minIndent < 1e9)\n            for (let i = startI; i < lines.length; i++)\n                if (lines[i].indent < lines[i].line.text.length)\n                    lines[i].indent = minIndent;\n        if (lines.length == startI + 1)\n            lines[startI].single = true;\n    }\n    if (option != 2 /* CommentOption.Uncomment */ && lines.some(l => l.comment < 0 && (!l.empty || l.single))) {\n        let changes = [];\n        for (let { line, token, indent, empty, single } of lines)\n            if (single || !empty)\n                changes.push({ from: line.from + indent, insert: token + \" \" });\n        let changeSet = state.changes(changes);\n        return { changes: changeSet, selection: state.selection.map(changeSet, 1) };\n    }\n    else if (option != 1 /* CommentOption.Comment */ && lines.some(l => l.comment >= 0)) {\n        let changes = [];\n        for (let { line, comment, token } of lines)\n            if (comment >= 0) {\n                let from = line.from + comment, to = from + token.length;\n                if (line.text[to - line.from] == \" \")\n                    to++;\n                changes.push({ from, to });\n            }\n        return { changes };\n    }\n    return null;\n}\n\nconst fromHistory = /*@__PURE__*/Annotation.define();\n/**\nTransaction annotation that will prevent that transaction from\nbeing combined with other transactions in the undo history. Given\n`\"before\"`, it'll prevent merging with previous transactions. With\n`\"after\"`, subsequent transactions won't be combined with this\none. With `\"full\"`, the transaction is isolated on both sides.\n*/\nconst isolateHistory = /*@__PURE__*/Annotation.define();\n/**\nThis facet provides a way to register functions that, given a\ntransaction, provide a set of effects that the history should\nstore when inverting the transaction. This can be used to\nintegrate some kinds of effects in the history, so that they can\nbe undone (and redone again).\n*/\nconst invertedEffects = /*@__PURE__*/Facet.define();\nconst historyConfig = /*@__PURE__*/Facet.define({\n    combine(configs) {\n        return combineConfig(configs, {\n            minDepth: 100,\n            newGroupDelay: 500,\n            joinToEvent: (_t, isAdjacent) => isAdjacent,\n        }, {\n            minDepth: Math.max,\n            newGroupDelay: Math.min,\n            joinToEvent: (a, b) => (tr, adj) => a(tr, adj) || b(tr, adj)\n        });\n    }\n});\nconst historyField_ = /*@__PURE__*/StateField.define({\n    create() {\n        return HistoryState.empty;\n    },\n    update(state, tr) {\n        let config = tr.state.facet(historyConfig);\n        let fromHist = tr.annotation(fromHistory);\n        if (fromHist) {\n            let item = HistEvent.fromTransaction(tr, fromHist.selection), from = fromHist.side;\n            let other = from == 0 /* BranchName.Done */ ? state.undone : state.done;\n            if (item)\n                other = updateBranch(other, other.length, config.minDepth, item);\n            else\n                other = addSelection(other, tr.startState.selection);\n            return new HistoryState(from == 0 /* BranchName.Done */ ? fromHist.rest : other, from == 0 /* BranchName.Done */ ? other : fromHist.rest);\n        }\n        let isolate = tr.annotation(isolateHistory);\n        if (isolate == \"full\" || isolate == \"before\")\n            state = state.isolate();\n        if (tr.annotation(Transaction.addToHistory) === false)\n            return !tr.changes.empty ? state.addMapping(tr.changes.desc) : state;\n        let event = HistEvent.fromTransaction(tr);\n        let time = tr.annotation(Transaction.time), userEvent = tr.annotation(Transaction.userEvent);\n        if (event)\n            state = state.addChanges(event, time, userEvent, config, tr);\n        else if (tr.selection)\n            state = state.addSelection(tr.startState.selection, time, userEvent, config.newGroupDelay);\n        if (isolate == \"full\" || isolate == \"after\")\n            state = state.isolate();\n        return state;\n    },\n    toJSON(value) {\n        return { done: value.done.map(e => e.toJSON()), undone: value.undone.map(e => e.toJSON()) };\n    },\n    fromJSON(json) {\n        return new HistoryState(json.done.map(HistEvent.fromJSON), json.undone.map(HistEvent.fromJSON));\n    }\n});\n/**\nCreate a history extension with the given configuration.\n*/\nfunction history(config = {}) {\n    return [\n        historyField_,\n        historyConfig.of(config),\n        EditorView.domEventHandlers({\n            beforeinput(e, view) {\n                let command = e.inputType == \"historyUndo\" ? undo : e.inputType == \"historyRedo\" ? redo : null;\n                if (!command)\n                    return false;\n                e.preventDefault();\n                return command(view);\n            }\n        })\n    ];\n}\n/**\nThe state field used to store the history data. Should probably\nonly be used when you want to\n[serialize](https://codemirror.net/6/docs/ref/#state.EditorState.toJSON) or\n[deserialize](https://codemirror.net/6/docs/ref/#state.EditorState^fromJSON) state objects in a way\nthat preserves history.\n*/\nconst historyField = historyField_;\nfunction cmd(side, selection) {\n    return function ({ state, dispatch }) {\n        if (!selection && state.readOnly)\n            return false;\n        let historyState = state.field(historyField_, false);\n        if (!historyState)\n            return false;\n        let tr = historyState.pop(side, state, selection);\n        if (!tr)\n            return false;\n        dispatch(tr);\n        return true;\n    };\n}\n/**\nUndo a single group of history events. Returns false if no group\nwas available.\n*/\nconst undo = /*@__PURE__*/cmd(0 /* BranchName.Done */, false);\n/**\nRedo a group of history events. Returns false if no group was\navailable.\n*/\nconst redo = /*@__PURE__*/cmd(1 /* BranchName.Undone */, false);\n/**\nUndo a change or selection change.\n*/\nconst undoSelection = /*@__PURE__*/cmd(0 /* BranchName.Done */, true);\n/**\nRedo a change or selection change.\n*/\nconst redoSelection = /*@__PURE__*/cmd(1 /* BranchName.Undone */, true);\nfunction depth(side) {\n    return function (state) {\n        let histState = state.field(historyField_, false);\n        if (!histState)\n            return 0;\n        let branch = side == 0 /* BranchName.Done */ ? histState.done : histState.undone;\n        return branch.length - (branch.length && !branch[0].changes ? 1 : 0);\n    };\n}\n/**\nThe amount of undoable change events available in a given state.\n*/\nconst undoDepth = /*@__PURE__*/depth(0 /* BranchName.Done */);\n/**\nThe amount of redoable change events available in a given state.\n*/\nconst redoDepth = /*@__PURE__*/depth(1 /* BranchName.Undone */);\n// History events store groups of changes or effects that need to be\n// undone/redone together.\nclass HistEvent {\n    constructor(\n    // The changes in this event. Normal events hold at least one\n    // change or effect. But it may be necessary to store selection\n    // events before the first change, in which case a special type of\n    // instance is created which doesn't hold any changes, with\n    // changes == startSelection == undefined\n    changes, \n    // The effects associated with this event\n    effects, \n    // Accumulated mapping (from addToHistory==false) that should be\n    // applied to events below this one.\n    mapped, \n    // The selection before this event\n    startSelection, \n    // Stores selection changes after this event, to be used for\n    // selection undo/redo.\n    selectionsAfter) {\n        this.changes = changes;\n        this.effects = effects;\n        this.mapped = mapped;\n        this.startSelection = startSelection;\n        this.selectionsAfter = selectionsAfter;\n    }\n    setSelAfter(after) {\n        return new HistEvent(this.changes, this.effects, this.mapped, this.startSelection, after);\n    }\n    toJSON() {\n        var _a, _b, _c;\n        return {\n            changes: (_a = this.changes) === null || _a === void 0 ? void 0 : _a.toJSON(),\n            mapped: (_b = this.mapped) === null || _b === void 0 ? void 0 : _b.toJSON(),\n            startSelection: (_c = this.startSelection) === null || _c === void 0 ? void 0 : _c.toJSON(),\n            selectionsAfter: this.selectionsAfter.map(s => s.toJSON())\n        };\n    }\n    static fromJSON(json) {\n        return new HistEvent(json.changes && ChangeSet.fromJSON(json.changes), [], json.mapped && ChangeDesc.fromJSON(json.mapped), json.startSelection && EditorSelection.fromJSON(json.startSelection), json.selectionsAfter.map(EditorSelection.fromJSON));\n    }\n    // This does not check `addToHistory` and such, it assumes the\n    // transaction needs to be converted to an item. Returns null when\n    // there are no changes or effects in the transaction.\n    static fromTransaction(tr, selection) {\n        let effects = none;\n        for (let invert of tr.startState.facet(invertedEffects)) {\n            let result = invert(tr);\n            if (result.length)\n                effects = effects.concat(result);\n        }\n        if (!effects.length && tr.changes.empty)\n            return null;\n        return new HistEvent(tr.changes.invert(tr.startState.doc), effects, undefined, selection || tr.startState.selection, none);\n    }\n    static selection(selections) {\n        return new HistEvent(undefined, none, undefined, undefined, selections);\n    }\n}\nfunction updateBranch(branch, to, maxLen, newEvent) {\n    let start = to + 1 > maxLen + 20 ? to - maxLen - 1 : 0;\n    let newBranch = branch.slice(start, to);\n    newBranch.push(newEvent);\n    return newBranch;\n}\nfunction isAdjacent(a, b) {\n    let ranges = [], isAdjacent = false;\n    a.iterChangedRanges((f, t) => ranges.push(f, t));\n    b.iterChangedRanges((_f, _t, f, t) => {\n        for (let i = 0; i < ranges.length;) {\n            let from = ranges[i++], to = ranges[i++];\n            if (t >= from && f <= to)\n                isAdjacent = true;\n        }\n    });\n    return isAdjacent;\n}\nfunction eqSelectionShape(a, b) {\n    return a.ranges.length == b.ranges.length &&\n        a.ranges.filter((r, i) => r.empty != b.ranges[i].empty).length === 0;\n}\nfunction conc(a, b) {\n    return !a.length ? b : !b.length ? a : a.concat(b);\n}\nconst none = [];\nconst MaxSelectionsPerEvent = 200;\nfunction addSelection(branch, selection) {\n    if (!branch.length) {\n        return [HistEvent.selection([selection])];\n    }\n    else {\n        let lastEvent = branch[branch.length - 1];\n        let sels = lastEvent.selectionsAfter.slice(Math.max(0, lastEvent.selectionsAfter.length - MaxSelectionsPerEvent));\n        if (sels.length && sels[sels.length - 1].eq(selection))\n            return branch;\n        sels.push(selection);\n        return updateBranch(branch, branch.length - 1, 1e9, lastEvent.setSelAfter(sels));\n    }\n}\n// Assumes the top item has one or more selectionAfter values\nfunction popSelection(branch) {\n    let last = branch[branch.length - 1];\n    let newBranch = branch.slice();\n    newBranch[branch.length - 1] = last.setSelAfter(last.selectionsAfter.slice(0, last.selectionsAfter.length - 1));\n    return newBranch;\n}\n// Add a mapping to the top event in the given branch. If this maps\n// away all the changes and effects in that item, drop it and\n// propagate the mapping to the next item.\nfunction addMappingToBranch(branch, mapping) {\n    if (!branch.length)\n        return branch;\n    let length = branch.length, selections = none;\n    while (length) {\n        let event = mapEvent(branch[length - 1], mapping, selections);\n        if (event.changes && !event.changes.empty || event.effects.length) { // Event survived mapping\n            let result = branch.slice(0, length);\n            result[length - 1] = event;\n            return result;\n        }\n        else { // Drop this event, since there's no changes or effects left\n            mapping = event.mapped;\n            length--;\n            selections = event.selectionsAfter;\n        }\n    }\n    return selections.length ? [HistEvent.selection(selections)] : none;\n}\nfunction mapEvent(event, mapping, extraSelections) {\n    let selections = conc(event.selectionsAfter.length ? event.selectionsAfter.map(s => s.map(mapping)) : none, extraSelections);\n    // Change-less events don't store mappings (they are always the last event in a branch)\n    if (!event.changes)\n        return HistEvent.selection(selections);\n    let mappedChanges = event.changes.map(mapping), before = mapping.mapDesc(event.changes, true);\n    let fullMapping = event.mapped ? event.mapped.composeDesc(before) : before;\n    return new HistEvent(mappedChanges, StateEffect.mapEffects(event.effects, mapping), fullMapping, event.startSelection.map(before), selections);\n}\nconst joinableUserEvent = /^(input\\.type|delete)($|\\.)/;\nclass HistoryState {\n    constructor(done, undone, prevTime = 0, prevUserEvent = undefined) {\n        this.done = done;\n        this.undone = undone;\n        this.prevTime = prevTime;\n        this.prevUserEvent = prevUserEvent;\n    }\n    isolate() {\n        return this.prevTime ? new HistoryState(this.done, this.undone) : this;\n    }\n    addChanges(event, time, userEvent, config, tr) {\n        let done = this.done, lastEvent = done[done.length - 1];\n        if (lastEvent && lastEvent.changes && !lastEvent.changes.empty && event.changes &&\n            (!userEvent || joinableUserEvent.test(userEvent)) &&\n            ((!lastEvent.selectionsAfter.length &&\n                time - this.prevTime < config.newGroupDelay &&\n                config.joinToEvent(tr, isAdjacent(lastEvent.changes, event.changes))) ||\n                // For compose (but not compose.start) events, always join with previous event\n                userEvent == \"input.type.compose\")) {\n            done = updateBranch(done, done.length - 1, config.minDepth, new HistEvent(event.changes.compose(lastEvent.changes), conc(StateEffect.mapEffects(event.effects, lastEvent.changes), lastEvent.effects), lastEvent.mapped, lastEvent.startSelection, none));\n        }\n        else {\n            done = updateBranch(done, done.length, config.minDepth, event);\n        }\n        return new HistoryState(done, none, time, userEvent);\n    }\n    addSelection(selection, time, userEvent, newGroupDelay) {\n        let last = this.done.length ? this.done[this.done.length - 1].selectionsAfter : none;\n        if (last.length > 0 &&\n            time - this.prevTime < newGroupDelay &&\n            userEvent == this.prevUserEvent && userEvent && /^select($|\\.)/.test(userEvent) &&\n            eqSelectionShape(last[last.length - 1], selection))\n            return this;\n        return new HistoryState(addSelection(this.done, selection), this.undone, time, userEvent);\n    }\n    addMapping(mapping) {\n        return new HistoryState(addMappingToBranch(this.done, mapping), addMappingToBranch(this.undone, mapping), this.prevTime, this.prevUserEvent);\n    }\n    pop(side, state, onlySelection) {\n        let branch = side == 0 /* BranchName.Done */ ? this.done : this.undone;\n        if (branch.length == 0)\n            return null;\n        let event = branch[branch.length - 1], selection = event.selectionsAfter[0] || state.selection;\n        if (onlySelection && event.selectionsAfter.length) {\n            return state.update({\n                selection: event.selectionsAfter[event.selectionsAfter.length - 1],\n                annotations: fromHistory.of({ side, rest: popSelection(branch), selection }),\n                userEvent: side == 0 /* BranchName.Done */ ? \"select.undo\" : \"select.redo\",\n                scrollIntoView: true\n            });\n        }\n        else if (!event.changes) {\n            return null;\n        }\n        else {\n            let rest = branch.length == 1 ? none : branch.slice(0, branch.length - 1);\n            if (event.mapped)\n                rest = addMappingToBranch(rest, event.mapped);\n            return state.update({\n                changes: event.changes,\n                selection: event.startSelection,\n                effects: event.effects,\n                annotations: fromHistory.of({ side, rest, selection }),\n                filter: false,\n                userEvent: side == 0 /* BranchName.Done */ ? \"undo\" : \"redo\",\n                scrollIntoView: true\n            });\n        }\n    }\n}\nHistoryState.empty = /*@__PURE__*/new HistoryState(none, none);\n/**\nDefault key bindings for the undo history.\n\n- Mod-z: [`undo`](https://codemirror.net/6/docs/ref/#commands.undo).\n- Mod-y (Mod-Shift-z on macOS) + Ctrl-Shift-z on Linux: [`redo`](https://codemirror.net/6/docs/ref/#commands.redo).\n- Mod-u: [`undoSelection`](https://codemirror.net/6/docs/ref/#commands.undoSelection).\n- Alt-u (Mod-Shift-u on macOS): [`redoSelection`](https://codemirror.net/6/docs/ref/#commands.redoSelection).\n*/\nconst historyKeymap = [\n    { key: \"Mod-z\", run: undo, preventDefault: true },\n    { key: \"Mod-y\", mac: \"Mod-Shift-z\", run: redo, preventDefault: true },\n    { linux: \"Ctrl-Shift-z\", run: redo, preventDefault: true },\n    { key: \"Mod-u\", run: undoSelection, preventDefault: true },\n    { key: \"Alt-u\", mac: \"Mod-Shift-u\", run: redoSelection, preventDefault: true }\n];\n\nfunction updateSel(sel, by) {\n    return EditorSelection.create(sel.ranges.map(by), sel.mainIndex);\n}\nfunction setSel(state, selection) {\n    return state.update({ selection, scrollIntoView: true, userEvent: \"select\" });\n}\nfunction moveSel({ state, dispatch }, how) {\n    let selection = updateSel(state.selection, how);\n    if (selection.eq(state.selection, true))\n        return false;\n    dispatch(setSel(state, selection));\n    return true;\n}\nfunction rangeEnd(range, forward) {\n    return EditorSelection.cursor(forward ? range.to : range.from);\n}\nfunction cursorByChar(view, forward) {\n    return moveSel(view, range => range.empty ? view.moveByChar(range, forward) : rangeEnd(range, forward));\n}\nfunction ltrAtCursor(view) {\n    return view.textDirectionAt(view.state.selection.main.head) == Direction.LTR;\n}\n/**\nMove the selection one character to the left (which is backward in\nleft-to-right text, forward in right-to-left text).\n*/\nconst cursorCharLeft = view => cursorByChar(view, !ltrAtCursor(view));\n/**\nMove the selection one character to the right.\n*/\nconst cursorCharRight = view => cursorByChar(view, ltrAtCursor(view));\n/**\nMove the selection one character forward.\n*/\nconst cursorCharForward = view => cursorByChar(view, true);\n/**\nMove the selection one character backward.\n*/\nconst cursorCharBackward = view => cursorByChar(view, false);\nfunction cursorByGroup(view, forward) {\n    return moveSel(view, range => range.empty ? view.moveByGroup(range, forward) : rangeEnd(range, forward));\n}\n/**\nMove the selection to the left across one group of word or\nnon-word (but also non-space) characters.\n*/\nconst cursorGroupLeft = view => cursorByGroup(view, !ltrAtCursor(view));\n/**\nMove the selection one group to the right.\n*/\nconst cursorGroupRight = view => cursorByGroup(view, ltrAtCursor(view));\n/**\nMove the selection one group forward.\n*/\nconst cursorGroupForward = view => cursorByGroup(view, true);\n/**\nMove the selection one group backward.\n*/\nconst cursorGroupBackward = view => cursorByGroup(view, false);\nconst segmenter = typeof Intl != \"undefined\" && Intl.Segmenter ?\n    /*@__PURE__*/new (Intl.Segmenter)(undefined, { granularity: \"word\" }) : null;\nfunction moveBySubword(view, range, forward) {\n    let categorize = view.state.charCategorizer(range.from);\n    let cat = CharCategory.Space, pos = range.from, steps = 0;\n    let done = false, sawUpper = false, sawLower = false;\n    let step = (next) => {\n        if (done)\n            return false;\n        pos += forward ? next.length : -next.length;\n        let nextCat = categorize(next), ahead;\n        if (nextCat == CharCategory.Word && next.charCodeAt(0) < 128 && /[\\W_]/.test(next))\n            nextCat = -1; // Treat word punctuation specially\n        if (cat == CharCategory.Space)\n            cat = nextCat;\n        if (cat != nextCat)\n            return false;\n        if (cat == CharCategory.Word) {\n            if (next.toLowerCase() == next) {\n                if (!forward && sawUpper)\n                    return false;\n                sawLower = true;\n            }\n            else if (sawLower) {\n                if (forward)\n                    return false;\n                done = true;\n            }\n            else {\n                if (sawUpper && forward && categorize(ahead = view.state.sliceDoc(pos, pos + 1)) == CharCategory.Word &&\n                    ahead.toLowerCase() == ahead)\n                    return false;\n                sawUpper = true;\n            }\n        }\n        steps++;\n        return true;\n    };\n    let end = view.moveByChar(range, forward, start => {\n        step(start);\n        return step;\n    });\n    if (segmenter && cat == CharCategory.Word && end.from == range.from + steps * (forward ? 1 : -1)) {\n        let from = Math.min(range.head, end.head), to = Math.max(range.head, end.head);\n        let skipped = view.state.sliceDoc(from, to);\n        if (skipped.length > 1 && /[\\u4E00-\\uffff]/.test(skipped)) {\n            let segments = Array.from(segmenter.segment(skipped));\n            if (segments.length > 1) {\n                if (forward)\n                    return EditorSelection.cursor(range.head + segments[1].index, -1);\n                return EditorSelection.cursor(end.head + segments[segments.length - 1].index, 1);\n            }\n        }\n    }\n    return end;\n}\nfunction cursorBySubword(view, forward) {\n    return moveSel(view, range => range.empty ? moveBySubword(view, range, forward) : rangeEnd(range, forward));\n}\n/**\nMove the selection one group or camel-case subword forward.\n*/\nconst cursorSubwordForward = view => cursorBySubword(view, true);\n/**\nMove the selection one group or camel-case subword backward.\n*/\nconst cursorSubwordBackward = view => cursorBySubword(view, false);\nfunction interestingNode(state, node, bracketProp) {\n    if (node.type.prop(bracketProp))\n        return true;\n    let len = node.to - node.from;\n    return len && (len > 2 || /[^\\s,.;:]/.test(state.sliceDoc(node.from, node.to))) || node.firstChild;\n}\nfunction moveBySyntax(state, start, forward) {\n    let pos = syntaxTree(state).resolveInner(start.head);\n    let bracketProp = forward ? NodeProp.closedBy : NodeProp.openedBy;\n    // Scan forward through child nodes to see if there's an interesting\n    // node ahead.\n    for (let at = start.head;;) {\n        let next = forward ? pos.childAfter(at) : pos.childBefore(at);\n        if (!next)\n            break;\n        if (interestingNode(state, next, bracketProp))\n            pos = next;\n        else\n            at = forward ? next.to : next.from;\n    }\n    let bracket = pos.type.prop(bracketProp), match, newPos;\n    if (bracket && (match = forward ? matchBrackets(state, pos.from, 1) : matchBrackets(state, pos.to, -1)) && match.matched)\n        newPos = forward ? match.end.to : match.end.from;\n    else\n        newPos = forward ? pos.to : pos.from;\n    return EditorSelection.cursor(newPos, forward ? -1 : 1);\n}\n/**\nMove the cursor over the next syntactic element to the left.\n*/\nconst cursorSyntaxLeft = view => moveSel(view, range => moveBySyntax(view.state, range, !ltrAtCursor(view)));\n/**\nMove the cursor over the next syntactic element to the right.\n*/\nconst cursorSyntaxRight = view => moveSel(view, range => moveBySyntax(view.state, range, ltrAtCursor(view)));\nfunction cursorByLine(view, forward) {\n    return moveSel(view, range => {\n        if (!range.empty)\n            return rangeEnd(range, forward);\n        let moved = view.moveVertically(range, forward);\n        return moved.head != range.head ? moved : view.moveToLineBoundary(range, forward);\n    });\n}\n/**\nMove the selection one line up.\n*/\nconst cursorLineUp = view => cursorByLine(view, false);\n/**\nMove the selection one line down.\n*/\nconst cursorLineDown = view => cursorByLine(view, true);\nfunction pageInfo(view) {\n    let selfScroll = view.scrollDOM.clientHeight < view.scrollDOM.scrollHeight - 2;\n    let marginTop = 0, marginBottom = 0, height;\n    if (selfScroll) {\n        for (let source of view.state.facet(EditorView.scrollMargins)) {\n            let margins = source(view);\n            if (margins === null || margins === void 0 ? void 0 : margins.top)\n                marginTop = Math.max(margins === null || margins === void 0 ? void 0 : margins.top, marginTop);\n            if (margins === null || margins === void 0 ? void 0 : margins.bottom)\n                marginBottom = Math.max(margins === null || margins === void 0 ? void 0 : margins.bottom, marginBottom);\n        }\n        height = view.scrollDOM.clientHeight - marginTop - marginBottom;\n    }\n    else {\n        height = (view.dom.ownerDocument.defaultView || window).innerHeight;\n    }\n    return { marginTop, marginBottom, selfScroll,\n        height: Math.max(view.defaultLineHeight, height - 5) };\n}\nfunction cursorByPage(view, forward) {\n    let page = pageInfo(view);\n    let { state } = view, selection = updateSel(state.selection, range => {\n        return range.empty ? view.moveVertically(range, forward, page.height)\n            : rangeEnd(range, forward);\n    });\n    if (selection.eq(state.selection))\n        return false;\n    let effect;\n    if (page.selfScroll) {\n        let startPos = view.coordsAtPos(state.selection.main.head);\n        let scrollRect = view.scrollDOM.getBoundingClientRect();\n        let scrollTop = scrollRect.top + page.marginTop, scrollBottom = scrollRect.bottom - page.marginBottom;\n        if (startPos && startPos.top > scrollTop && startPos.bottom < scrollBottom)\n            effect = EditorView.scrollIntoView(selection.main.head, { y: \"start\", yMargin: startPos.top - scrollTop });\n    }\n    view.dispatch(setSel(state, selection), { effects: effect });\n    return true;\n}\n/**\nMove the selection one page up.\n*/\nconst cursorPageUp = view => cursorByPage(view, false);\n/**\nMove the selection one page down.\n*/\nconst cursorPageDown = view => cursorByPage(view, true);\nfunction moveByLineBoundary(view, start, forward) {\n    let line = view.lineBlockAt(start.head), moved = view.moveToLineBoundary(start, forward);\n    if (moved.head == start.head && moved.head != (forward ? line.to : line.from))\n        moved = view.moveToLineBoundary(start, forward, false);\n    if (!forward && moved.head == line.from && line.length) {\n        let space = /^\\s*/.exec(view.state.sliceDoc(line.from, Math.min(line.from + 100, line.to)))[0].length;\n        if (space && start.head != line.from + space)\n            moved = EditorSelection.cursor(line.from + space);\n    }\n    return moved;\n}\n/**\nMove the selection to the next line wrap point, or to the end of\nthe line if there isn't one left on this line.\n*/\nconst cursorLineBoundaryForward = view => moveSel(view, range => moveByLineBoundary(view, range, true));\n/**\nMove the selection to previous line wrap point, or failing that to\nthe start of the line. If the line is indented, and the cursor\nisn't already at the end of the indentation, this will move to the\nend of the indentation instead of the start of the line.\n*/\nconst cursorLineBoundaryBackward = view => moveSel(view, range => moveByLineBoundary(view, range, false));\n/**\nMove the selection one line wrap point to the left.\n*/\nconst cursorLineBoundaryLeft = view => moveSel(view, range => moveByLineBoundary(view, range, !ltrAtCursor(view)));\n/**\nMove the selection one line wrap point to the right.\n*/\nconst cursorLineBoundaryRight = view => moveSel(view, range => moveByLineBoundary(view, range, ltrAtCursor(view)));\n/**\nMove the selection to the start of the line.\n*/\nconst cursorLineStart = view => moveSel(view, range => EditorSelection.cursor(view.lineBlockAt(range.head).from, 1));\n/**\nMove the selection to the end of the line.\n*/\nconst cursorLineEnd = view => moveSel(view, range => EditorSelection.cursor(view.lineBlockAt(range.head).to, -1));\nfunction toMatchingBracket(state, dispatch, extend) {\n    let found = false, selection = updateSel(state.selection, range => {\n        let matching = matchBrackets(state, range.head, -1)\n            || matchBrackets(state, range.head, 1)\n            || (range.head > 0 && matchBrackets(state, range.head - 1, 1))\n            || (range.head < state.doc.length && matchBrackets(state, range.head + 1, -1));\n        if (!matching || !matching.end)\n            return range;\n        found = true;\n        let head = matching.start.from == range.head ? matching.end.to : matching.end.from;\n        return extend ? EditorSelection.range(range.anchor, head) : EditorSelection.cursor(head);\n    });\n    if (!found)\n        return false;\n    dispatch(setSel(state, selection));\n    return true;\n}\n/**\nMove the selection to the bracket matching the one it is currently\non, if any.\n*/\nconst cursorMatchingBracket = ({ state, dispatch }) => toMatchingBracket(state, dispatch, false);\n/**\nExtend the selection to the bracket matching the one the selection\nhead is currently on, if any.\n*/\nconst selectMatchingBracket = ({ state, dispatch }) => toMatchingBracket(state, dispatch, true);\nfunction extendSel(view, how) {\n    let selection = updateSel(view.state.selection, range => {\n        let head = how(range);\n        return EditorSelection.range(range.anchor, head.head, head.goalColumn, head.bidiLevel || undefined);\n    });\n    if (selection.eq(view.state.selection))\n        return false;\n    view.dispatch(setSel(view.state, selection));\n    return true;\n}\nfunction selectByChar(view, forward) {\n    return extendSel(view, range => view.moveByChar(range, forward));\n}\n/**\nMove the selection head one character to the left, while leaving\nthe anchor in place.\n*/\nconst selectCharLeft = view => selectByChar(view, !ltrAtCursor(view));\n/**\nMove the selection head one character to the right.\n*/\nconst selectCharRight = view => selectByChar(view, ltrAtCursor(view));\n/**\nMove the selection head one character forward.\n*/\nconst selectCharForward = view => selectByChar(view, true);\n/**\nMove the selection head one character backward.\n*/\nconst selectCharBackward = view => selectByChar(view, false);\nfunction selectByGroup(view, forward) {\n    return extendSel(view, range => view.moveByGroup(range, forward));\n}\n/**\nMove the selection head one [group](https://codemirror.net/6/docs/ref/#commands.cursorGroupLeft) to\nthe left.\n*/\nconst selectGroupLeft = view => selectByGroup(view, !ltrAtCursor(view));\n/**\nMove the selection head one group to the right.\n*/\nconst selectGroupRight = view => selectByGroup(view, ltrAtCursor(view));\n/**\nMove the selection head one group forward.\n*/\nconst selectGroupForward = view => selectByGroup(view, true);\n/**\nMove the selection head one group backward.\n*/\nconst selectGroupBackward = view => selectByGroup(view, false);\nfunction selectBySubword(view, forward) {\n    return extendSel(view, range => moveBySubword(view, range, forward));\n}\n/**\nMove the selection head one group or camel-case subword forward.\n*/\nconst selectSubwordForward = view => selectBySubword(view, true);\n/**\nMove the selection head one group or subword backward.\n*/\nconst selectSubwordBackward = view => selectBySubword(view, false);\n/**\nMove the selection head over the next syntactic element to the left.\n*/\nconst selectSyntaxLeft = view => extendSel(view, range => moveBySyntax(view.state, range, !ltrAtCursor(view)));\n/**\nMove the selection head over the next syntactic element to the right.\n*/\nconst selectSyntaxRight = view => extendSel(view, range => moveBySyntax(view.state, range, ltrAtCursor(view)));\nfunction selectByLine(view, forward) {\n    return extendSel(view, range => view.moveVertically(range, forward));\n}\n/**\nMove the selection head one line up.\n*/\nconst selectLineUp = view => selectByLine(view, false);\n/**\nMove the selection head one line down.\n*/\nconst selectLineDown = view => selectByLine(view, true);\nfunction selectByPage(view, forward) {\n    return extendSel(view, range => view.moveVertically(range, forward, pageInfo(view).height));\n}\n/**\nMove the selection head one page up.\n*/\nconst selectPageUp = view => selectByPage(view, false);\n/**\nMove the selection head one page down.\n*/\nconst selectPageDown = view => selectByPage(view, true);\n/**\nMove the selection head to the next line boundary.\n*/\nconst selectLineBoundaryForward = view => extendSel(view, range => moveByLineBoundary(view, range, true));\n/**\nMove the selection head to the previous line boundary.\n*/\nconst selectLineBoundaryBackward = view => extendSel(view, range => moveByLineBoundary(view, range, false));\n/**\nMove the selection head one line boundary to the left.\n*/\nconst selectLineBoundaryLeft = view => extendSel(view, range => moveByLineBoundary(view, range, !ltrAtCursor(view)));\n/**\nMove the selection head one line boundary to the right.\n*/\nconst selectLineBoundaryRight = view => extendSel(view, range => moveByLineBoundary(view, range, ltrAtCursor(view)));\n/**\nMove the selection head to the start of the line.\n*/\nconst selectLineStart = view => extendSel(view, range => EditorSelection.cursor(view.lineBlockAt(range.head).from));\n/**\nMove the selection head to the end of the line.\n*/\nconst selectLineEnd = view => extendSel(view, range => EditorSelection.cursor(view.lineBlockAt(range.head).to));\n/**\nMove the selection to the start of the document.\n*/\nconst cursorDocStart = ({ state, dispatch }) => {\n    dispatch(setSel(state, { anchor: 0 }));\n    return true;\n};\n/**\nMove the selection to the end of the document.\n*/\nconst cursorDocEnd = ({ state, dispatch }) => {\n    dispatch(setSel(state, { anchor: state.doc.length }));\n    return true;\n};\n/**\nMove the selection head to the start of the document.\n*/\nconst selectDocStart = ({ state, dispatch }) => {\n    dispatch(setSel(state, { anchor: state.selection.main.anchor, head: 0 }));\n    return true;\n};\n/**\nMove the selection head to the end of the document.\n*/\nconst selectDocEnd = ({ state, dispatch }) => {\n    dispatch(setSel(state, { anchor: state.selection.main.anchor, head: state.doc.length }));\n    return true;\n};\n/**\nSelect the entire document.\n*/\nconst selectAll = ({ state, dispatch }) => {\n    dispatch(state.update({ selection: { anchor: 0, head: state.doc.length }, userEvent: \"select\" }));\n    return true;\n};\n/**\nExpand the selection to cover entire lines.\n*/\nconst selectLine = ({ state, dispatch }) => {\n    let ranges = selectedLineBlocks(state).map(({ from, to }) => EditorSelection.range(from, Math.min(to + 1, state.doc.length)));\n    dispatch(state.update({ selection: EditorSelection.create(ranges), userEvent: \"select\" }));\n    return true;\n};\n/**\nSelect the next syntactic construct that is larger than the\nselection. Note that this will only work insofar as the language\n[provider](https://codemirror.net/6/docs/ref/#language.language) you use builds up a full\nsyntax tree.\n*/\nconst selectParentSyntax = ({ state, dispatch }) => {\n    let selection = updateSel(state.selection, range => {\n        var _a;\n        let stack = syntaxTree(state).resolveStack(range.from, 1);\n        for (let cur = stack; cur; cur = cur.next) {\n            let { node } = cur;\n            if (((node.from < range.from && node.to >= range.to) ||\n                (node.to > range.to && node.from <= range.from)) &&\n                ((_a = node.parent) === null || _a === void 0 ? void 0 : _a.parent))\n                return EditorSelection.range(node.to, node.from);\n        }\n        return range;\n    });\n    dispatch(setSel(state, selection));\n    return true;\n};\n/**\nSimplify the current selection. When multiple ranges are selected,\nreduce it to its main range. Otherwise, if the selection is\nnon-empty, convert it to a cursor selection.\n*/\nconst simplifySelection = ({ state, dispatch }) => {\n    let cur = state.selection, selection = null;\n    if (cur.ranges.length > 1)\n        selection = EditorSelection.create([cur.main]);\n    else if (!cur.main.empty)\n        selection = EditorSelection.create([EditorSelection.cursor(cur.main.head)]);\n    if (!selection)\n        return false;\n    dispatch(setSel(state, selection));\n    return true;\n};\nfunction deleteBy(target, by) {\n    if (target.state.readOnly)\n        return false;\n    let event = \"delete.selection\", { state } = target;\n    let changes = state.changeByRange(range => {\n        let { from, to } = range;\n        if (from == to) {\n            let towards = by(range);\n            if (towards < from) {\n                event = \"delete.backward\";\n                towards = skipAtomic(target, towards, false);\n            }\n            else if (towards > from) {\n                event = \"delete.forward\";\n                towards = skipAtomic(target, towards, true);\n            }\n            from = Math.min(from, towards);\n            to = Math.max(to, towards);\n        }\n        else {\n            from = skipAtomic(target, from, false);\n            to = skipAtomic(target, to, true);\n        }\n        return from == to ? { range } : { changes: { from, to }, range: EditorSelection.cursor(from, from < range.head ? -1 : 1) };\n    });\n    if (changes.changes.empty)\n        return false;\n    target.dispatch(state.update(changes, {\n        scrollIntoView: true,\n        userEvent: event,\n        effects: event == \"delete.selection\" ? EditorView.announce.of(state.phrase(\"Selection deleted\")) : undefined\n    }));\n    return true;\n}\nfunction skipAtomic(target, pos, forward) {\n    if (target instanceof EditorView)\n        for (let ranges of target.state.facet(EditorView.atomicRanges).map(f => f(target)))\n            ranges.between(pos, pos, (from, to) => {\n                if (from < pos && to > pos)\n                    pos = forward ? to : from;\n            });\n    return pos;\n}\nconst deleteByChar = (target, forward, byIndentUnit) => deleteBy(target, range => {\n    let pos = range.from, { state } = target, line = state.doc.lineAt(pos), before, targetPos;\n    if (byIndentUnit && !forward && pos > line.from && pos < line.from + 200 &&\n        !/[^ \\t]/.test(before = line.text.slice(0, pos - line.from))) {\n        if (before[before.length - 1] == \"\\t\")\n            return pos - 1;\n        let col = countColumn(before, state.tabSize), drop = col % getIndentUnit(state) || getIndentUnit(state);\n        for (let i = 0; i < drop && before[before.length - 1 - i] == \" \"; i++)\n            pos--;\n        targetPos = pos;\n    }\n    else {\n        targetPos = findClusterBreak(line.text, pos - line.from, forward, forward) + line.from;\n        if (targetPos == pos && line.number != (forward ? state.doc.lines : 1))\n            targetPos += forward ? 1 : -1;\n        else if (!forward && /[\\ufe00-\\ufe0f]/.test(line.text.slice(targetPos - line.from, pos - line.from)))\n            targetPos = findClusterBreak(line.text, targetPos - line.from, false, false) + line.from;\n    }\n    return targetPos;\n});\n/**\nDelete the selection, or, for cursor selections, the character or\nindentation unit before the cursor.\n*/\nconst deleteCharBackward = view => deleteByChar(view, false, true);\n/**\nDelete the selection or the character before the cursor. Does not\nimplement any extended behavior like deleting whole indentation\nunits in one go.\n*/\nconst deleteCharBackwardStrict = view => deleteByChar(view, false, false);\n/**\nDelete the selection or the character after the cursor.\n*/\nconst deleteCharForward = view => deleteByChar(view, true, false);\nconst deleteByGroup = (target, forward) => deleteBy(target, range => {\n    let pos = range.head, { state } = target, line = state.doc.lineAt(pos);\n    let categorize = state.charCategorizer(pos);\n    for (let cat = null;;) {\n        if (pos == (forward ? line.to : line.from)) {\n            if (pos == range.head && line.number != (forward ? state.doc.lines : 1))\n                pos += forward ? 1 : -1;\n            break;\n        }\n        let next = findClusterBreak(line.text, pos - line.from, forward) + line.from;\n        let nextChar = line.text.slice(Math.min(pos, next) - line.from, Math.max(pos, next) - line.from);\n        let nextCat = categorize(nextChar);\n        if (cat != null && nextCat != cat)\n            break;\n        if (nextChar != \" \" || pos != range.head)\n            cat = nextCat;\n        pos = next;\n    }\n    return pos;\n});\n/**\nDelete the selection or backward until the end of the next\n[group](https://codemirror.net/6/docs/ref/#view.EditorView.moveByGroup), only skipping groups of\nwhitespace when they consist of a single space.\n*/\nconst deleteGroupBackward = target => deleteByGroup(target, false);\n/**\nDelete the selection or forward until the end of the next group.\n*/\nconst deleteGroupForward = target => deleteByGroup(target, true);\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe end of the line. If the cursor is directly at the end of the\nline, delete the line break after it.\n*/\nconst deleteToLineEnd = view => deleteBy(view, range => {\n    let lineEnd = view.lineBlockAt(range.head).to;\n    return range.head < lineEnd ? lineEnd : Math.min(view.state.doc.length, range.head + 1);\n});\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe start of the line. If the cursor is directly at the start of the\nline, delete the line break before it.\n*/\nconst deleteToLineStart = view => deleteBy(view, range => {\n    let lineStart = view.lineBlockAt(range.head).from;\n    return range.head > lineStart ? lineStart : Math.max(0, range.head - 1);\n});\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe start of the line or the next line wrap before the cursor.\n*/\nconst deleteLineBoundaryBackward = view => deleteBy(view, range => {\n    let lineStart = view.moveToLineBoundary(range, false).head;\n    return range.head > lineStart ? lineStart : Math.max(0, range.head - 1);\n});\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe end of the line or the next line wrap after the cursor.\n*/\nconst deleteLineBoundaryForward = view => deleteBy(view, range => {\n    let lineStart = view.moveToLineBoundary(range, true).head;\n    return range.head < lineStart ? lineStart : Math.min(view.state.doc.length, range.head + 1);\n});\n/**\nDelete all whitespace directly before a line end from the\ndocument.\n*/\nconst deleteTrailingWhitespace = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    let changes = [];\n    for (let pos = 0, prev = \"\", iter = state.doc.iter();;) {\n        iter.next();\n        if (iter.lineBreak || iter.done) {\n            let trailing = prev.search(/\\s+$/);\n            if (trailing > -1)\n                changes.push({ from: pos - (prev.length - trailing), to: pos });\n            if (iter.done)\n                break;\n            prev = \"\";\n        }\n        else {\n            prev = iter.value;\n        }\n        pos += iter.value.length;\n    }\n    if (!changes.length)\n        return false;\n    dispatch(state.update({ changes, userEvent: \"delete\" }));\n    return true;\n};\n/**\nReplace each selection range with a line break, leaving the cursor\non the line before the break.\n*/\nconst splitLine = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    let changes = state.changeByRange(range => {\n        return { changes: { from: range.from, to: range.to, insert: Text.of([\"\", \"\"]) },\n            range: EditorSelection.cursor(range.from) };\n    });\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\n/**\nFlip the characters before and after the cursor(s).\n*/\nconst transposeChars = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    let changes = state.changeByRange(range => {\n        if (!range.empty || range.from == 0 || range.from == state.doc.length)\n            return { range };\n        let pos = range.from, line = state.doc.lineAt(pos);\n        let from = pos == line.from ? pos - 1 : findClusterBreak(line.text, pos - line.from, false) + line.from;\n        let to = pos == line.to ? pos + 1 : findClusterBreak(line.text, pos - line.from, true) + line.from;\n        return { changes: { from, to, insert: state.doc.slice(pos, to).append(state.doc.slice(from, pos)) },\n            range: EditorSelection.cursor(to) };\n    });\n    if (changes.changes.empty)\n        return false;\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"move.character\" }));\n    return true;\n};\nfunction selectedLineBlocks(state) {\n    let blocks = [], upto = -1;\n    for (let range of state.selection.ranges) {\n        let startLine = state.doc.lineAt(range.from), endLine = state.doc.lineAt(range.to);\n        if (!range.empty && range.to == endLine.from)\n            endLine = state.doc.lineAt(range.to - 1);\n        if (upto >= startLine.number) {\n            let prev = blocks[blocks.length - 1];\n            prev.to = endLine.to;\n            prev.ranges.push(range);\n        }\n        else {\n            blocks.push({ from: startLine.from, to: endLine.to, ranges: [range] });\n        }\n        upto = endLine.number + 1;\n    }\n    return blocks;\n}\nfunction moveLine(state, dispatch, forward) {\n    if (state.readOnly)\n        return false;\n    let changes = [], ranges = [];\n    for (let block of selectedLineBlocks(state)) {\n        if (forward ? block.to == state.doc.length : block.from == 0)\n            continue;\n        let nextLine = state.doc.lineAt(forward ? block.to + 1 : block.from - 1);\n        let size = nextLine.length + 1;\n        if (forward) {\n            changes.push({ from: block.to, to: nextLine.to }, { from: block.from, insert: nextLine.text + state.lineBreak });\n            for (let r of block.ranges)\n                ranges.push(EditorSelection.range(Math.min(state.doc.length, r.anchor + size), Math.min(state.doc.length, r.head + size)));\n        }\n        else {\n            changes.push({ from: nextLine.from, to: block.from }, { from: block.to, insert: state.lineBreak + nextLine.text });\n            for (let r of block.ranges)\n                ranges.push(EditorSelection.range(r.anchor - size, r.head - size));\n        }\n    }\n    if (!changes.length)\n        return false;\n    dispatch(state.update({\n        changes,\n        scrollIntoView: true,\n        selection: EditorSelection.create(ranges, state.selection.mainIndex),\n        userEvent: \"move.line\"\n    }));\n    return true;\n}\n/**\nMove the selected lines up one line.\n*/\nconst moveLineUp = ({ state, dispatch }) => moveLine(state, dispatch, false);\n/**\nMove the selected lines down one line.\n*/\nconst moveLineDown = ({ state, dispatch }) => moveLine(state, dispatch, true);\nfunction copyLine(state, dispatch, forward) {\n    if (state.readOnly)\n        return false;\n    let changes = [];\n    for (let block of selectedLineBlocks(state)) {\n        if (forward)\n            changes.push({ from: block.from, insert: state.doc.slice(block.from, block.to) + state.lineBreak });\n        else\n            changes.push({ from: block.to, insert: state.lineBreak + state.doc.slice(block.from, block.to) });\n    }\n    dispatch(state.update({ changes, scrollIntoView: true, userEvent: \"input.copyline\" }));\n    return true;\n}\n/**\nCreate a copy of the selected lines. Keep the selection in the top copy.\n*/\nconst copyLineUp = ({ state, dispatch }) => copyLine(state, dispatch, false);\n/**\nCreate a copy of the selected lines. Keep the selection in the bottom copy.\n*/\nconst copyLineDown = ({ state, dispatch }) => copyLine(state, dispatch, true);\n/**\nDelete selected lines.\n*/\nconst deleteLine = view => {\n    if (view.state.readOnly)\n        return false;\n    let { state } = view, changes = state.changes(selectedLineBlocks(state).map(({ from, to }) => {\n        if (from > 0)\n            from--;\n        else if (to < state.doc.length)\n            to++;\n        return { from, to };\n    }));\n    let selection = updateSel(state.selection, range => {\n        let dist = undefined;\n        if (view.lineWrapping) {\n            let block = view.lineBlockAt(range.head), pos = view.coordsAtPos(range.head, range.assoc || 1);\n            if (pos)\n                dist = (block.bottom + view.documentTop) - pos.bottom + view.defaultLineHeight / 2;\n        }\n        return view.moveVertically(range, true, dist);\n    }).map(changes);\n    view.dispatch({ changes, selection, scrollIntoView: true, userEvent: \"delete.line\" });\n    return true;\n};\n/**\nReplace the selection with a newline.\n*/\nconst insertNewline = ({ state, dispatch }) => {\n    dispatch(state.update(state.replaceSelection(state.lineBreak), { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\n/**\nReplace the selection with a newline and the same amount of\nindentation as the line above.\n*/\nconst insertNewlineKeepIndent = ({ state, dispatch }) => {\n    dispatch(state.update(state.changeByRange(range => {\n        let indent = /^\\s*/.exec(state.doc.lineAt(range.from).text)[0];\n        return {\n            changes: { from: range.from, to: range.to, insert: state.lineBreak + indent },\n            range: EditorSelection.cursor(range.from + indent.length + 1)\n        };\n    }), { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\nfunction isBetweenBrackets(state, pos) {\n    if (/\\(\\)|\\[\\]|\\{\\}/.test(state.sliceDoc(pos - 1, pos + 1)))\n        return { from: pos, to: pos };\n    let context = syntaxTree(state).resolveInner(pos);\n    let before = context.childBefore(pos), after = context.childAfter(pos), closedBy;\n    if (before && after && before.to <= pos && after.from >= pos &&\n        (closedBy = before.type.prop(NodeProp.closedBy)) && closedBy.indexOf(after.name) > -1 &&\n        state.doc.lineAt(before.to).from == state.doc.lineAt(after.from).from &&\n        !/\\S/.test(state.sliceDoc(before.to, after.from)))\n        return { from: before.to, to: after.from };\n    return null;\n}\n/**\nReplace the selection with a newline and indent the newly created\nline(s). If the current line consists only of whitespace, this\nwill also delete that whitespace. When the cursor is between\nmatching brackets, an additional newline will be inserted after\nthe cursor.\n*/\nconst insertNewlineAndIndent = /*@__PURE__*/newlineAndIndent(false);\n/**\nCreate a blank, indented line below the current line.\n*/\nconst insertBlankLine = /*@__PURE__*/newlineAndIndent(true);\nfunction newlineAndIndent(atEof) {\n    return ({ state, dispatch }) => {\n        if (state.readOnly)\n            return false;\n        let changes = state.changeByRange(range => {\n            let { from, to } = range, line = state.doc.lineAt(from);\n            let explode = !atEof && from == to && isBetweenBrackets(state, from);\n            if (atEof)\n                from = to = (to <= line.to ? line : state.doc.lineAt(to)).to;\n            let cx = new IndentContext(state, { simulateBreak: from, simulateDoubleBreak: !!explode });\n            let indent = getIndentation(cx, from);\n            if (indent == null)\n                indent = countColumn(/^\\s*/.exec(state.doc.lineAt(from).text)[0], state.tabSize);\n            while (to < line.to && /\\s/.test(line.text[to - line.from]))\n                to++;\n            if (explode)\n                ({ from, to } = explode);\n            else if (from > line.from && from < line.from + 100 && !/\\S/.test(line.text.slice(0, from)))\n                from = line.from;\n            let insert = [\"\", indentString(state, indent)];\n            if (explode)\n                insert.push(indentString(state, cx.lineIndent(line.from, -1)));\n            return { changes: { from, to, insert: Text.of(insert) },\n                range: EditorSelection.cursor(from + 1 + insert[1].length) };\n        });\n        dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"input\" }));\n        return true;\n    };\n}\nfunction changeBySelectedLine(state, f) {\n    let atLine = -1;\n    return state.changeByRange(range => {\n        let changes = [];\n        for (let pos = range.from; pos <= range.to;) {\n            let line = state.doc.lineAt(pos);\n            if (line.number > atLine && (range.empty || range.to > line.from)) {\n                f(line, changes, range);\n                atLine = line.number;\n            }\n            pos = line.to + 1;\n        }\n        let changeSet = state.changes(changes);\n        return { changes,\n            range: EditorSelection.range(changeSet.mapPos(range.anchor, 1), changeSet.mapPos(range.head, 1)) };\n    });\n}\n/**\nAuto-indent the selected lines. This uses the [indentation service\nfacet](https://codemirror.net/6/docs/ref/#language.indentService) as source for auto-indent\ninformation.\n*/\nconst indentSelection = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    let updated = Object.create(null);\n    let context = new IndentContext(state, { overrideIndentation: start => {\n            let found = updated[start];\n            return found == null ? -1 : found;\n        } });\n    let changes = changeBySelectedLine(state, (line, changes, range) => {\n        let indent = getIndentation(context, line.from);\n        if (indent == null)\n            return;\n        if (!/\\S/.test(line.text))\n            indent = 0;\n        let cur = /^\\s*/.exec(line.text)[0];\n        let norm = indentString(state, indent);\n        if (cur != norm || range.from < line.from + cur.length) {\n            updated[line.from] = indent;\n            changes.push({ from: line.from, to: line.from + cur.length, insert: norm });\n        }\n    });\n    if (!changes.changes.empty)\n        dispatch(state.update(changes, { userEvent: \"indent\" }));\n    return true;\n};\n/**\nAdd a [unit](https://codemirror.net/6/docs/ref/#language.indentUnit) of indentation to all selected\nlines.\n*/\nconst indentMore = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    dispatch(state.update(changeBySelectedLine(state, (line, changes) => {\n        changes.push({ from: line.from, insert: state.facet(indentUnit) });\n    }), { userEvent: \"input.indent\" }));\n    return true;\n};\n/**\nRemove a [unit](https://codemirror.net/6/docs/ref/#language.indentUnit) of indentation from all\nselected lines.\n*/\nconst indentLess = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    dispatch(state.update(changeBySelectedLine(state, (line, changes) => {\n        let space = /^\\s*/.exec(line.text)[0];\n        if (!space)\n            return;\n        let col = countColumn(space, state.tabSize), keep = 0;\n        let insert = indentString(state, Math.max(0, col - getIndentUnit(state)));\n        while (keep < space.length && keep < insert.length && space.charCodeAt(keep) == insert.charCodeAt(keep))\n            keep++;\n        changes.push({ from: line.from + keep, to: line.from + space.length, insert: insert.slice(keep) });\n    }), { userEvent: \"delete.dedent\" }));\n    return true;\n};\n/**\nEnables or disables\n[tab-focus mode](https://codemirror.net/6/docs/ref/#view.EditorView.setTabFocusMode). While on, this\nprevents the editor's key bindings from capturing Tab or\nShift-Tab, making it possible for the user to move focus out of\nthe editor with the keyboard.\n*/\nconst toggleTabFocusMode = view => {\n    view.setTabFocusMode();\n    return true;\n};\n/**\nTemporarily enables [tab-focus\nmode](https://codemirror.net/6/docs/ref/#view.EditorView.setTabFocusMode) for two seconds or until\nanother key is pressed.\n*/\nconst temporarilySetTabFocusMode = view => {\n    view.setTabFocusMode(2000);\n    return true;\n};\n/**\nInsert a tab character at the cursor or, if something is selected,\nuse [`indentMore`](https://codemirror.net/6/docs/ref/#commands.indentMore) to indent the entire\nselection.\n*/\nconst insertTab = ({ state, dispatch }) => {\n    if (state.selection.ranges.some(r => !r.empty))\n        return indentMore({ state, dispatch });\n    dispatch(state.update(state.replaceSelection(\"\\t\"), { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\n/**\nArray of key bindings containing the Emacs-style bindings that are\navailable on macOS by default.\n\n - Ctrl-b: [`cursorCharLeft`](https://codemirror.net/6/docs/ref/#commands.cursorCharLeft) ([`selectCharLeft`](https://codemirror.net/6/docs/ref/#commands.selectCharLeft) with Shift)\n - Ctrl-f: [`cursorCharRight`](https://codemirror.net/6/docs/ref/#commands.cursorCharRight) ([`selectCharRight`](https://codemirror.net/6/docs/ref/#commands.selectCharRight) with Shift)\n - Ctrl-p: [`cursorLineUp`](https://codemirror.net/6/docs/ref/#commands.cursorLineUp) ([`selectLineUp`](https://codemirror.net/6/docs/ref/#commands.selectLineUp) with Shift)\n - Ctrl-n: [`cursorLineDown`](https://codemirror.net/6/docs/ref/#commands.cursorLineDown) ([`selectLineDown`](https://codemirror.net/6/docs/ref/#commands.selectLineDown) with Shift)\n - Ctrl-a: [`cursorLineStart`](https://codemirror.net/6/docs/ref/#commands.cursorLineStart) ([`selectLineStart`](https://codemirror.net/6/docs/ref/#commands.selectLineStart) with Shift)\n - Ctrl-e: [`cursorLineEnd`](https://codemirror.net/6/docs/ref/#commands.cursorLineEnd) ([`selectLineEnd`](https://codemirror.net/6/docs/ref/#commands.selectLineEnd) with Shift)\n - Ctrl-d: [`deleteCharForward`](https://codemirror.net/6/docs/ref/#commands.deleteCharForward)\n - Ctrl-h: [`deleteCharBackward`](https://codemirror.net/6/docs/ref/#commands.deleteCharBackward)\n - Ctrl-k: [`deleteToLineEnd`](https://codemirror.net/6/docs/ref/#commands.deleteToLineEnd)\n - Ctrl-Alt-h: [`deleteGroupBackward`](https://codemirror.net/6/docs/ref/#commands.deleteGroupBackward)\n - Ctrl-o: [`splitLine`](https://codemirror.net/6/docs/ref/#commands.splitLine)\n - Ctrl-t: [`transposeChars`](https://codemirror.net/6/docs/ref/#commands.transposeChars)\n - Ctrl-v: [`cursorPageDown`](https://codemirror.net/6/docs/ref/#commands.cursorPageDown)\n - Alt-v: [`cursorPageUp`](https://codemirror.net/6/docs/ref/#commands.cursorPageUp)\n*/\nconst emacsStyleKeymap = [\n    { key: \"Ctrl-b\", run: cursorCharLeft, shift: selectCharLeft, preventDefault: true },\n    { key: \"Ctrl-f\", run: cursorCharRight, shift: selectCharRight },\n    { key: \"Ctrl-p\", run: cursorLineUp, shift: selectLineUp },\n    { key: \"Ctrl-n\", run: cursorLineDown, shift: selectLineDown },\n    { key: \"Ctrl-a\", run: cursorLineStart, shift: selectLineStart },\n    { key: \"Ctrl-e\", run: cursorLineEnd, shift: selectLineEnd },\n    { key: \"Ctrl-d\", run: deleteCharForward },\n    { key: \"Ctrl-h\", run: deleteCharBackward },\n    { key: \"Ctrl-k\", run: deleteToLineEnd },\n    { key: \"Ctrl-Alt-h\", run: deleteGroupBackward },\n    { key: \"Ctrl-o\", run: splitLine },\n    { key: \"Ctrl-t\", run: transposeChars },\n    { key: \"Ctrl-v\", run: cursorPageDown },\n];\n/**\nAn array of key bindings closely sticking to platform-standard or\nwidely used bindings. (This includes the bindings from\n[`emacsStyleKeymap`](https://codemirror.net/6/docs/ref/#commands.emacsStyleKeymap), with their `key`\nproperty changed to `mac`.)\n\n - ArrowLeft: [`cursorCharLeft`](https://codemirror.net/6/docs/ref/#commands.cursorCharLeft) ([`selectCharLeft`](https://codemirror.net/6/docs/ref/#commands.selectCharLeft) with Shift)\n - ArrowRight: [`cursorCharRight`](https://codemirror.net/6/docs/ref/#commands.cursorCharRight) ([`selectCharRight`](https://codemirror.net/6/docs/ref/#commands.selectCharRight) with Shift)\n - Ctrl-ArrowLeft (Alt-ArrowLeft on macOS): [`cursorGroupLeft`](https://codemirror.net/6/docs/ref/#commands.cursorGroupLeft) ([`selectGroupLeft`](https://codemirror.net/6/docs/ref/#commands.selectGroupLeft) with Shift)\n - Ctrl-ArrowRight (Alt-ArrowRight on macOS): [`cursorGroupRight`](https://codemirror.net/6/docs/ref/#commands.cursorGroupRight) ([`selectGroupRight`](https://codemirror.net/6/docs/ref/#commands.selectGroupRight) with Shift)\n - Cmd-ArrowLeft (on macOS): [`cursorLineStart`](https://codemirror.net/6/docs/ref/#commands.cursorLineStart) ([`selectLineStart`](https://codemirror.net/6/docs/ref/#commands.selectLineStart) with Shift)\n - Cmd-ArrowRight (on macOS): [`cursorLineEnd`](https://codemirror.net/6/docs/ref/#commands.cursorLineEnd) ([`selectLineEnd`](https://codemirror.net/6/docs/ref/#commands.selectLineEnd) with Shift)\n - ArrowUp: [`cursorLineUp`](https://codemirror.net/6/docs/ref/#commands.cursorLineUp) ([`selectLineUp`](https://codemirror.net/6/docs/ref/#commands.selectLineUp) with Shift)\n - ArrowDown: [`cursorLineDown`](https://codemirror.net/6/docs/ref/#commands.cursorLineDown) ([`selectLineDown`](https://codemirror.net/6/docs/ref/#commands.selectLineDown) with Shift)\n - Cmd-ArrowUp (on macOS): [`cursorDocStart`](https://codemirror.net/6/docs/ref/#commands.cursorDocStart) ([`selectDocStart`](https://codemirror.net/6/docs/ref/#commands.selectDocStart) with Shift)\n - Cmd-ArrowDown (on macOS): [`cursorDocEnd`](https://codemirror.net/6/docs/ref/#commands.cursorDocEnd) ([`selectDocEnd`](https://codemirror.net/6/docs/ref/#commands.selectDocEnd) with Shift)\n - Ctrl-ArrowUp (on macOS): [`cursorPageUp`](https://codemirror.net/6/docs/ref/#commands.cursorPageUp) ([`selectPageUp`](https://codemirror.net/6/docs/ref/#commands.selectPageUp) with Shift)\n - Ctrl-ArrowDown (on macOS): [`cursorPageDown`](https://codemirror.net/6/docs/ref/#commands.cursorPageDown) ([`selectPageDown`](https://codemirror.net/6/docs/ref/#commands.selectPageDown) with Shift)\n - PageUp: [`cursorPageUp`](https://codemirror.net/6/docs/ref/#commands.cursorPageUp) ([`selectPageUp`](https://codemirror.net/6/docs/ref/#commands.selectPageUp) with Shift)\n - PageDown: [`cursorPageDown`](https://codemirror.net/6/docs/ref/#commands.cursorPageDown) ([`selectPageDown`](https://codemirror.net/6/docs/ref/#commands.selectPageDown) with Shift)\n - Home: [`cursorLineBoundaryBackward`](https://codemirror.net/6/docs/ref/#commands.cursorLineBoundaryBackward) ([`selectLineBoundaryBackward`](https://codemirror.net/6/docs/ref/#commands.selectLineBoundaryBackward) with Shift)\n - End: [`cursorLineBoundaryForward`](https://codemirror.net/6/docs/ref/#commands.cursorLineBoundaryForward) ([`selectLineBoundaryForward`](https://codemirror.net/6/docs/ref/#commands.selectLineBoundaryForward) with Shift)\n - Ctrl-Home (Cmd-Home on macOS): [`cursorDocStart`](https://codemirror.net/6/docs/ref/#commands.cursorDocStart) ([`selectDocStart`](https://codemirror.net/6/docs/ref/#commands.selectDocStart) with Shift)\n - Ctrl-End (Cmd-Home on macOS): [`cursorDocEnd`](https://codemirror.net/6/docs/ref/#commands.cursorDocEnd) ([`selectDocEnd`](https://codemirror.net/6/docs/ref/#commands.selectDocEnd) with Shift)\n - Enter: [`insertNewlineAndIndent`](https://codemirror.net/6/docs/ref/#commands.insertNewlineAndIndent)\n - Ctrl-a (Cmd-a on macOS): [`selectAll`](https://codemirror.net/6/docs/ref/#commands.selectAll)\n - Backspace: [`deleteCharBackward`](https://codemirror.net/6/docs/ref/#commands.deleteCharBackward)\n - Delete: [`deleteCharForward`](https://codemirror.net/6/docs/ref/#commands.deleteCharForward)\n - Ctrl-Backspace (Alt-Backspace on macOS): [`deleteGroupBackward`](https://codemirror.net/6/docs/ref/#commands.deleteGroupBackward)\n - Ctrl-Delete (Alt-Delete on macOS): [`deleteGroupForward`](https://codemirror.net/6/docs/ref/#commands.deleteGroupForward)\n - Cmd-Backspace (macOS): [`deleteLineBoundaryBackward`](https://codemirror.net/6/docs/ref/#commands.deleteLineBoundaryBackward).\n - Cmd-Delete (macOS): [`deleteLineBoundaryForward`](https://codemirror.net/6/docs/ref/#commands.deleteLineBoundaryForward).\n*/\nconst standardKeymap = /*@__PURE__*/[\n    { key: \"ArrowLeft\", run: cursorCharLeft, shift: selectCharLeft, preventDefault: true },\n    { key: \"Mod-ArrowLeft\", mac: \"Alt-ArrowLeft\", run: cursorGroupLeft, shift: selectGroupLeft, preventDefault: true },\n    { mac: \"Cmd-ArrowLeft\", run: cursorLineBoundaryLeft, shift: selectLineBoundaryLeft, preventDefault: true },\n    { key: \"ArrowRight\", run: cursorCharRight, shift: selectCharRight, preventDefault: true },\n    { key: \"Mod-ArrowRight\", mac: \"Alt-ArrowRight\", run: cursorGroupRight, shift: selectGroupRight, preventDefault: true },\n    { mac: \"Cmd-ArrowRight\", run: cursorLineBoundaryRight, shift: selectLineBoundaryRight, preventDefault: true },\n    { key: \"ArrowUp\", run: cursorLineUp, shift: selectLineUp, preventDefault: true },\n    { mac: \"Cmd-ArrowUp\", run: cursorDocStart, shift: selectDocStart },\n    { mac: \"Ctrl-ArrowUp\", run: cursorPageUp, shift: selectPageUp },\n    { key: \"ArrowDown\", run: cursorLineDown, shift: selectLineDown, preventDefault: true },\n    { mac: \"Cmd-ArrowDown\", run: cursorDocEnd, shift: selectDocEnd },\n    { mac: \"Ctrl-ArrowDown\", run: cursorPageDown, shift: selectPageDown },\n    { key: \"PageUp\", run: cursorPageUp, shift: selectPageUp },\n    { key: \"PageDown\", run: cursorPageDown, shift: selectPageDown },\n    { key: \"Home\", run: cursorLineBoundaryBackward, shift: selectLineBoundaryBackward, preventDefault: true },\n    { key: \"Mod-Home\", run: cursorDocStart, shift: selectDocStart },\n    { key: \"End\", run: cursorLineBoundaryForward, shift: selectLineBoundaryForward, preventDefault: true },\n    { key: \"Mod-End\", run: cursorDocEnd, shift: selectDocEnd },\n    { key: \"Enter\", run: insertNewlineAndIndent },\n    { key: \"Mod-a\", run: selectAll },\n    { key: \"Backspace\", run: deleteCharBackward, shift: deleteCharBackward },\n    { key: \"Delete\", run: deleteCharForward },\n    { key: \"Mod-Backspace\", mac: \"Alt-Backspace\", run: deleteGroupBackward },\n    { key: \"Mod-Delete\", mac: \"Alt-Delete\", run: deleteGroupForward },\n    { mac: \"Mod-Backspace\", run: deleteLineBoundaryBackward },\n    { mac: \"Mod-Delete\", run: deleteLineBoundaryForward }\n].concat(/*@__PURE__*/emacsStyleKeymap.map(b => ({ mac: b.key, run: b.run, shift: b.shift })));\n/**\nThe default keymap. Includes all bindings from\n[`standardKeymap`](https://codemirror.net/6/docs/ref/#commands.standardKeymap) plus the following:\n\n- Alt-ArrowLeft (Ctrl-ArrowLeft on macOS): [`cursorSyntaxLeft`](https://codemirror.net/6/docs/ref/#commands.cursorSyntaxLeft) ([`selectSyntaxLeft`](https://codemirror.net/6/docs/ref/#commands.selectSyntaxLeft) with Shift)\n- Alt-ArrowRight (Ctrl-ArrowRight on macOS): [`cursorSyntaxRight`](https://codemirror.net/6/docs/ref/#commands.cursorSyntaxRight) ([`selectSyntaxRight`](https://codemirror.net/6/docs/ref/#commands.selectSyntaxRight) with Shift)\n- Alt-ArrowUp: [`moveLineUp`](https://codemirror.net/6/docs/ref/#commands.moveLineUp)\n- Alt-ArrowDown: [`moveLineDown`](https://codemirror.net/6/docs/ref/#commands.moveLineDown)\n- Shift-Alt-ArrowUp: [`copyLineUp`](https://codemirror.net/6/docs/ref/#commands.copyLineUp)\n- Shift-Alt-ArrowDown: [`copyLineDown`](https://codemirror.net/6/docs/ref/#commands.copyLineDown)\n- Escape: [`simplifySelection`](https://codemirror.net/6/docs/ref/#commands.simplifySelection)\n- Ctrl-Enter (Cmd-Enter on macOS): [`insertBlankLine`](https://codemirror.net/6/docs/ref/#commands.insertBlankLine)\n- Alt-l (Ctrl-l on macOS): [`selectLine`](https://codemirror.net/6/docs/ref/#commands.selectLine)\n- Ctrl-i (Cmd-i on macOS): [`selectParentSyntax`](https://codemirror.net/6/docs/ref/#commands.selectParentSyntax)\n- Ctrl-[ (Cmd-[ on macOS): [`indentLess`](https://codemirror.net/6/docs/ref/#commands.indentLess)\n- Ctrl-] (Cmd-] on macOS): [`indentMore`](https://codemirror.net/6/docs/ref/#commands.indentMore)\n- Ctrl-Alt-\\\\ (Cmd-Alt-\\\\ on macOS): [`indentSelection`](https://codemirror.net/6/docs/ref/#commands.indentSelection)\n- Shift-Ctrl-k (Shift-Cmd-k on macOS): [`deleteLine`](https://codemirror.net/6/docs/ref/#commands.deleteLine)\n- Shift-Ctrl-\\\\ (Shift-Cmd-\\\\ on macOS): [`cursorMatchingBracket`](https://codemirror.net/6/docs/ref/#commands.cursorMatchingBracket)\n- Ctrl-/ (Cmd-/ on macOS): [`toggleComment`](https://codemirror.net/6/docs/ref/#commands.toggleComment).\n- Shift-Alt-a: [`toggleBlockComment`](https://codemirror.net/6/docs/ref/#commands.toggleBlockComment).\n- Ctrl-m (Alt-Shift-m on macOS): [`toggleTabFocusMode`](https://codemirror.net/6/docs/ref/#commands.toggleTabFocusMode).\n*/\nconst defaultKeymap = /*@__PURE__*/[\n    { key: \"Alt-ArrowLeft\", mac: \"Ctrl-ArrowLeft\", run: cursorSyntaxLeft, shift: selectSyntaxLeft },\n    { key: \"Alt-ArrowRight\", mac: \"Ctrl-ArrowRight\", run: cursorSyntaxRight, shift: selectSyntaxRight },\n    { key: \"Alt-ArrowUp\", run: moveLineUp },\n    { key: \"Shift-Alt-ArrowUp\", run: copyLineUp },\n    { key: \"Alt-ArrowDown\", run: moveLineDown },\n    { key: \"Shift-Alt-ArrowDown\", run: copyLineDown },\n    { key: \"Escape\", run: simplifySelection },\n    { key: \"Mod-Enter\", run: insertBlankLine },\n    { key: \"Alt-l\", mac: \"Ctrl-l\", run: selectLine },\n    { key: \"Mod-i\", run: selectParentSyntax, preventDefault: true },\n    { key: \"Mod-[\", run: indentLess },\n    { key: \"Mod-]\", run: indentMore },\n    { key: \"Mod-Alt-\\\\\", run: indentSelection },\n    { key: \"Shift-Mod-k\", run: deleteLine },\n    { key: \"Shift-Mod-\\\\\", run: cursorMatchingBracket },\n    { key: \"Mod-/\", run: toggleComment },\n    { key: \"Alt-A\", run: toggleBlockComment },\n    { key: \"Ctrl-m\", mac: \"Shift-Alt-m\", run: toggleTabFocusMode },\n].concat(standardKeymap);\n/**\nA binding that binds Tab to [`indentMore`](https://codemirror.net/6/docs/ref/#commands.indentMore) and\nShift-Tab to [`indentLess`](https://codemirror.net/6/docs/ref/#commands.indentLess).\nPlease see the [Tab example](../../examples/tab/) before using\nthis.\n*/\nconst indentWithTab = { key: \"Tab\", run: indentMore, shift: indentLess };\n\nexport { blockComment, blockUncomment, copyLineDown, copyLineUp, cursorCharBackward, cursorCharForward, cursorCharLeft, cursorCharRight, cursorDocEnd, cursorDocStart, cursorGroupBackward, cursorGroupForward, cursorGroupLeft, cursorGroupRight, cursorLineBoundaryBackward, cursorLineBoundaryForward, cursorLineBoundaryLeft, cursorLineBoundaryRight, cursorLineDown, cursorLineEnd, cursorLineStart, cursorLineUp, cursorMatchingBracket, cursorPageDown, cursorPageUp, cursorSubwordBackward, cursorSubwordForward, cursorSyntaxLeft, cursorSyntaxRight, defaultKeymap, deleteCharBackward, deleteCharBackwardStrict, deleteCharForward, deleteGroupBackward, deleteGroupForward, deleteLine, deleteLineBoundaryBackward, deleteLineBoundaryForward, deleteToLineEnd, deleteToLineStart, deleteTrailingWhitespace, emacsStyleKeymap, history, historyField, historyKeymap, indentLess, indentMore, indentSelection, indentWithTab, insertBlankLine, insertNewline, insertNewlineAndIndent, insertNewlineKeepIndent, insertTab, invertedEffects, isolateHistory, lineComment, lineUncomment, moveLineDown, moveLineUp, redo, redoDepth, redoSelection, selectAll, selectCharBackward, selectCharForward, selectCharLeft, selectCharRight, selectDocEnd, selectDocStart, selectGroupBackward, selectGroupForward, selectGroupLeft, selectGroupRight, selectLine, selectLineBoundaryBackward, selectLineBoundaryForward, selectLineBoundaryLeft, selectLineBoundaryRight, selectLineDown, selectLineEnd, selectLineStart, selectLineUp, selectMatchingBracket, selectPageDown, selectPageUp, selectParentSyntax, selectSubwordBackward, selectSubwordForward, selectSyntaxLeft, selectSyntaxRight, simplifySelection, splitLine, standardKeymap, temporarilySetTabFocusMode, toggleBlockComment, toggleBlockCommentByLine, toggleComment, toggleLineComment, toggleTabFocusMode, transposeChars, undo, undoDepth, undoSelection };\n", "export default function crelt() {\n  var elt = arguments[0]\n  if (typeof elt == \"string\") elt = document.createElement(elt)\n  var i = 1, next = arguments[1]\n  if (next && typeof next == \"object\" && next.nodeType == null && !Array.isArray(next)) {\n    for (var name in next) if (Object.prototype.hasOwnProperty.call(next, name)) {\n      var value = next[name]\n      if (typeof value == \"string\") elt.setAttribute(name, value)\n      else if (value != null) elt[name] = value\n    }\n    i++\n  }\n  for (; i < arguments.length; i++) add(elt, arguments[i])\n  return elt\n}\n\nfunction add(elt, child) {\n  if (typeof child == \"string\") {\n    elt.appendChild(document.createTextNode(child))\n  } else if (child == null) {\n  } else if (child.nodeType != null) {\n    elt.appendChild(child)\n  } else if (Array.isArray(child)) {\n    for (var i = 0; i < child.length; i++) add(elt, child[i])\n  } else {\n    throw new RangeError(\"Unsupported child node: \" + child)\n  }\n}\n", "import { show<PERSON><PERSON>l, Editor<PERSON>iew, getPanel, Decoration, ViewPlugin, runScopeHandlers } from '@codemirror/view';\nimport { codePointAt, fromCodePoint, codePointSize, StateEffect, StateField, EditorSelection, Facet, combineConfig, CharCategory, RangeSetBuilder, Prec, EditorState, findClusterBreak } from '@codemirror/state';\nimport elt from 'crelt';\n\nconst basicNormalize = typeof String.prototype.normalize == \"function\"\n    ? x => x.normalize(\"NFKD\") : x => x;\n/**\nA search cursor provides an iterator over text matches in a\ndocument.\n*/\nclass SearchCursor {\n    /**\n    Create a text cursor. The query is the search string, `from` to\n    `to` provides the region to search.\n    \n    When `normalize` is given, it will be called, on both the query\n    string and the content it is matched against, before comparing.\n    You can, for example, create a case-insensitive search by\n    passing `s => s.toLowerCase()`.\n    \n    Text is always normalized with\n    [`.normalize(\"NFKD\")`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/normalize)\n    (when supported).\n    */\n    constructor(text, query, from = 0, to = text.length, normalize, test) {\n        this.test = test;\n        /**\n        The current match (only holds a meaningful value after\n        [`next`](https://codemirror.net/6/docs/ref/#search.SearchCursor.next) has been called and when\n        `done` is false).\n        */\n        this.value = { from: 0, to: 0 };\n        /**\n        Whether the end of the iterated region has been reached.\n        */\n        this.done = false;\n        this.matches = [];\n        this.buffer = \"\";\n        this.bufferPos = 0;\n        this.iter = text.iterRange(from, to);\n        this.bufferStart = from;\n        this.normalize = normalize ? x => normalize(basicNormalize(x)) : basicNormalize;\n        this.query = this.normalize(query);\n    }\n    peek() {\n        if (this.bufferPos == this.buffer.length) {\n            this.bufferStart += this.buffer.length;\n            this.iter.next();\n            if (this.iter.done)\n                return -1;\n            this.bufferPos = 0;\n            this.buffer = this.iter.value;\n        }\n        return codePointAt(this.buffer, this.bufferPos);\n    }\n    /**\n    Look for the next match. Updates the iterator's\n    [`value`](https://codemirror.net/6/docs/ref/#search.SearchCursor.value) and\n    [`done`](https://codemirror.net/6/docs/ref/#search.SearchCursor.done) properties. Should be called\n    at least once before using the cursor.\n    */\n    next() {\n        while (this.matches.length)\n            this.matches.pop();\n        return this.nextOverlapping();\n    }\n    /**\n    The `next` method will ignore matches that partially overlap a\n    previous match. This method behaves like `next`, but includes\n    such matches.\n    */\n    nextOverlapping() {\n        for (;;) {\n            let next = this.peek();\n            if (next < 0) {\n                this.done = true;\n                return this;\n            }\n            let str = fromCodePoint(next), start = this.bufferStart + this.bufferPos;\n            this.bufferPos += codePointSize(next);\n            let norm = this.normalize(str);\n            for (let i = 0, pos = start;; i++) {\n                let code = norm.charCodeAt(i);\n                let match = this.match(code, pos, this.bufferPos + this.bufferStart);\n                if (i == norm.length - 1) {\n                    if (match) {\n                        this.value = match;\n                        return this;\n                    }\n                    break;\n                }\n                if (pos == start && i < str.length && str.charCodeAt(i) == code)\n                    pos++;\n            }\n        }\n    }\n    match(code, pos, end) {\n        let match = null;\n        for (let i = 0; i < this.matches.length; i += 2) {\n            let index = this.matches[i], keep = false;\n            if (this.query.charCodeAt(index) == code) {\n                if (index == this.query.length - 1) {\n                    match = { from: this.matches[i + 1], to: end };\n                }\n                else {\n                    this.matches[i]++;\n                    keep = true;\n                }\n            }\n            if (!keep) {\n                this.matches.splice(i, 2);\n                i -= 2;\n            }\n        }\n        if (this.query.charCodeAt(0) == code) {\n            if (this.query.length == 1)\n                match = { from: pos, to: end };\n            else\n                this.matches.push(1, pos);\n        }\n        if (match && this.test && !this.test(match.from, match.to, this.buffer, this.bufferStart))\n            match = null;\n        return match;\n    }\n}\nif (typeof Symbol != \"undefined\")\n    SearchCursor.prototype[Symbol.iterator] = function () { return this; };\n\nconst empty = { from: -1, to: -1, match: /*@__PURE__*//.*/.exec(\"\") };\nconst baseFlags = \"gm\" + (/x/.unicode == null ? \"\" : \"u\");\n/**\nThis class is similar to [`SearchCursor`](https://codemirror.net/6/docs/ref/#search.SearchCursor)\nbut searches for a regular expression pattern instead of a plain\nstring.\n*/\nclass RegExpCursor {\n    /**\n    Create a cursor that will search the given range in the given\n    document. `query` should be the raw pattern (as you'd pass it to\n    `new RegExp`).\n    */\n    constructor(text, query, options, from = 0, to = text.length) {\n        this.text = text;\n        this.to = to;\n        this.curLine = \"\";\n        /**\n        Set to `true` when the cursor has reached the end of the search\n        range.\n        */\n        this.done = false;\n        /**\n        Will contain an object with the extent of the match and the\n        match object when [`next`](https://codemirror.net/6/docs/ref/#search.RegExpCursor.next)\n        sucessfully finds a match.\n        */\n        this.value = empty;\n        if (/\\\\[sWDnr]|\\n|\\r|\\[\\^/.test(query))\n            return new MultilineRegExpCursor(text, query, options, from, to);\n        this.re = new RegExp(query, baseFlags + ((options === null || options === void 0 ? void 0 : options.ignoreCase) ? \"i\" : \"\"));\n        this.test = options === null || options === void 0 ? void 0 : options.test;\n        this.iter = text.iter();\n        let startLine = text.lineAt(from);\n        this.curLineStart = startLine.from;\n        this.matchPos = toCharEnd(text, from);\n        this.getLine(this.curLineStart);\n    }\n    getLine(skip) {\n        this.iter.next(skip);\n        if (this.iter.lineBreak) {\n            this.curLine = \"\";\n        }\n        else {\n            this.curLine = this.iter.value;\n            if (this.curLineStart + this.curLine.length > this.to)\n                this.curLine = this.curLine.slice(0, this.to - this.curLineStart);\n            this.iter.next();\n        }\n    }\n    nextLine() {\n        this.curLineStart = this.curLineStart + this.curLine.length + 1;\n        if (this.curLineStart > this.to)\n            this.curLine = \"\";\n        else\n            this.getLine(0);\n    }\n    /**\n    Move to the next match, if there is one.\n    */\n    next() {\n        for (let off = this.matchPos - this.curLineStart;;) {\n            this.re.lastIndex = off;\n            let match = this.matchPos <= this.to && this.re.exec(this.curLine);\n            if (match) {\n                let from = this.curLineStart + match.index, to = from + match[0].length;\n                this.matchPos = toCharEnd(this.text, to + (from == to ? 1 : 0));\n                if (from == this.curLineStart + this.curLine.length)\n                    this.nextLine();\n                if ((from < to || from > this.value.to) && (!this.test || this.test(from, to, match))) {\n                    this.value = { from, to, match };\n                    return this;\n                }\n                off = this.matchPos - this.curLineStart;\n            }\n            else if (this.curLineStart + this.curLine.length < this.to) {\n                this.nextLine();\n                off = 0;\n            }\n            else {\n                this.done = true;\n                return this;\n            }\n        }\n    }\n}\nconst flattened = /*@__PURE__*/new WeakMap();\n// Reusable (partially) flattened document strings\nclass FlattenedDoc {\n    constructor(from, text) {\n        this.from = from;\n        this.text = text;\n    }\n    get to() { return this.from + this.text.length; }\n    static get(doc, from, to) {\n        let cached = flattened.get(doc);\n        if (!cached || cached.from >= to || cached.to <= from) {\n            let flat = new FlattenedDoc(from, doc.sliceString(from, to));\n            flattened.set(doc, flat);\n            return flat;\n        }\n        if (cached.from == from && cached.to == to)\n            return cached;\n        let { text, from: cachedFrom } = cached;\n        if (cachedFrom > from) {\n            text = doc.sliceString(from, cachedFrom) + text;\n            cachedFrom = from;\n        }\n        if (cached.to < to)\n            text += doc.sliceString(cached.to, to);\n        flattened.set(doc, new FlattenedDoc(cachedFrom, text));\n        return new FlattenedDoc(from, text.slice(from - cachedFrom, to - cachedFrom));\n    }\n}\nclass MultilineRegExpCursor {\n    constructor(text, query, options, from, to) {\n        this.text = text;\n        this.to = to;\n        this.done = false;\n        this.value = empty;\n        this.matchPos = toCharEnd(text, from);\n        this.re = new RegExp(query, baseFlags + ((options === null || options === void 0 ? void 0 : options.ignoreCase) ? \"i\" : \"\"));\n        this.test = options === null || options === void 0 ? void 0 : options.test;\n        this.flat = FlattenedDoc.get(text, from, this.chunkEnd(from + 5000 /* Chunk.Base */));\n    }\n    chunkEnd(pos) {\n        return pos >= this.to ? this.to : this.text.lineAt(pos).to;\n    }\n    next() {\n        for (;;) {\n            let off = this.re.lastIndex = this.matchPos - this.flat.from;\n            let match = this.re.exec(this.flat.text);\n            // Skip empty matches directly after the last match\n            if (match && !match[0] && match.index == off) {\n                this.re.lastIndex = off + 1;\n                match = this.re.exec(this.flat.text);\n            }\n            if (match) {\n                let from = this.flat.from + match.index, to = from + match[0].length;\n                // If a match goes almost to the end of a noncomplete chunk, try\n                // again, since it'll likely be able to match more\n                if ((this.flat.to >= this.to || match.index + match[0].length <= this.flat.text.length - 10) &&\n                    (!this.test || this.test(from, to, match))) {\n                    this.value = { from, to, match };\n                    this.matchPos = toCharEnd(this.text, to + (from == to ? 1 : 0));\n                    return this;\n                }\n            }\n            if (this.flat.to == this.to) {\n                this.done = true;\n                return this;\n            }\n            // Grow the flattened doc\n            this.flat = FlattenedDoc.get(this.text, this.flat.from, this.chunkEnd(this.flat.from + this.flat.text.length * 2));\n        }\n    }\n}\nif (typeof Symbol != \"undefined\") {\n    RegExpCursor.prototype[Symbol.iterator] = MultilineRegExpCursor.prototype[Symbol.iterator] =\n        function () { return this; };\n}\nfunction validRegExp(source) {\n    try {\n        new RegExp(source, baseFlags);\n        return true;\n    }\n    catch (_a) {\n        return false;\n    }\n}\nfunction toCharEnd(text, pos) {\n    if (pos >= text.length)\n        return pos;\n    let line = text.lineAt(pos), next;\n    while (pos < line.to && (next = line.text.charCodeAt(pos - line.from)) >= 0xDC00 && next < 0xE000)\n        pos++;\n    return pos;\n}\n\nfunction createLineDialog(view) {\n    let line = String(view.state.doc.lineAt(view.state.selection.main.head).number);\n    let input = elt(\"input\", { class: \"cm-textfield\", name: \"line\", value: line });\n    let dom = elt(\"form\", {\n        class: \"cm-gotoLine\",\n        onkeydown: (event) => {\n            if (event.keyCode == 27) { // Escape\n                event.preventDefault();\n                view.dispatch({ effects: dialogEffect.of(false) });\n                view.focus();\n            }\n            else if (event.keyCode == 13) { // Enter\n                event.preventDefault();\n                go();\n            }\n        },\n        onsubmit: (event) => {\n            event.preventDefault();\n            go();\n        }\n    }, elt(\"label\", view.state.phrase(\"Go to line\"), \": \", input), \" \", elt(\"button\", { class: \"cm-button\", type: \"submit\" }, view.state.phrase(\"go\")));\n    function go() {\n        let match = /^([+-])?(\\d+)?(:\\d+)?(%)?$/.exec(input.value);\n        if (!match)\n            return;\n        let { state } = view, startLine = state.doc.lineAt(state.selection.main.head);\n        let [, sign, ln, cl, percent] = match;\n        let col = cl ? +cl.slice(1) : 0;\n        let line = ln ? +ln : startLine.number;\n        if (ln && percent) {\n            let pc = line / 100;\n            if (sign)\n                pc = pc * (sign == \"-\" ? -1 : 1) + (startLine.number / state.doc.lines);\n            line = Math.round(state.doc.lines * pc);\n        }\n        else if (ln && sign) {\n            line = line * (sign == \"-\" ? -1 : 1) + startLine.number;\n        }\n        let docLine = state.doc.line(Math.max(1, Math.min(state.doc.lines, line)));\n        let selection = EditorSelection.cursor(docLine.from + Math.max(0, Math.min(col, docLine.length)));\n        view.dispatch({\n            effects: [dialogEffect.of(false), EditorView.scrollIntoView(selection.from, { y: 'center' })],\n            selection,\n        });\n        view.focus();\n    }\n    return { dom };\n}\nconst dialogEffect = /*@__PURE__*/StateEffect.define();\nconst dialogField = /*@__PURE__*/StateField.define({\n    create() { return true; },\n    update(value, tr) {\n        for (let e of tr.effects)\n            if (e.is(dialogEffect))\n                value = e.value;\n        return value;\n    },\n    provide: f => showPanel.from(f, val => val ? createLineDialog : null)\n});\n/**\nCommand that shows a dialog asking the user for a line number, and\nwhen a valid position is provided, moves the cursor to that line.\n\nSupports line numbers, relative line offsets prefixed with `+` or\n`-`, document percentages suffixed with `%`, and an optional\ncolumn position by adding `:` and a second number after the line\nnumber.\n*/\nconst gotoLine = view => {\n    let panel = getPanel(view, createLineDialog);\n    if (!panel) {\n        let effects = [dialogEffect.of(true)];\n        if (view.state.field(dialogField, false) == null)\n            effects.push(StateEffect.appendConfig.of([dialogField, baseTheme$1]));\n        view.dispatch({ effects });\n        panel = getPanel(view, createLineDialog);\n    }\n    if (panel)\n        panel.dom.querySelector(\"input\").select();\n    return true;\n};\nconst baseTheme$1 = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-panel.cm-gotoLine\": {\n        padding: \"2px 6px 4px\",\n        \"& label\": { fontSize: \"80%\" }\n    }\n});\n\nconst defaultHighlightOptions = {\n    highlightWordAroundCursor: false,\n    minSelectionLength: 1,\n    maxMatches: 100,\n    wholeWords: false\n};\nconst highlightConfig = /*@__PURE__*/Facet.define({\n    combine(options) {\n        return combineConfig(options, defaultHighlightOptions, {\n            highlightWordAroundCursor: (a, b) => a || b,\n            minSelectionLength: Math.min,\n            maxMatches: Math.min\n        });\n    }\n});\n/**\nThis extension highlights text that matches the selection. It uses\nthe `\"cm-selectionMatch\"` class for the highlighting. When\n`highlightWordAroundCursor` is enabled, the word at the cursor\nitself will be highlighted with `\"cm-selectionMatch-main\"`.\n*/\nfunction highlightSelectionMatches(options) {\n    let ext = [defaultTheme, matchHighlighter];\n    if (options)\n        ext.push(highlightConfig.of(options));\n    return ext;\n}\nconst matchDeco = /*@__PURE__*/Decoration.mark({ class: \"cm-selectionMatch\" });\nconst mainMatchDeco = /*@__PURE__*/Decoration.mark({ class: \"cm-selectionMatch cm-selectionMatch-main\" });\n// Whether the characters directly outside the given positions are non-word characters\nfunction insideWordBoundaries(check, state, from, to) {\n    return (from == 0 || check(state.sliceDoc(from - 1, from)) != CharCategory.Word) &&\n        (to == state.doc.length || check(state.sliceDoc(to, to + 1)) != CharCategory.Word);\n}\n// Whether the characters directly at the given positions are word characters\nfunction insideWord(check, state, from, to) {\n    return check(state.sliceDoc(from, from + 1)) == CharCategory.Word\n        && check(state.sliceDoc(to - 1, to)) == CharCategory.Word;\n}\nconst matchHighlighter = /*@__PURE__*/ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.decorations = this.getDeco(view);\n    }\n    update(update) {\n        if (update.selectionSet || update.docChanged || update.viewportChanged)\n            this.decorations = this.getDeco(update.view);\n    }\n    getDeco(view) {\n        let conf = view.state.facet(highlightConfig);\n        let { state } = view, sel = state.selection;\n        if (sel.ranges.length > 1)\n            return Decoration.none;\n        let range = sel.main, query, check = null;\n        if (range.empty) {\n            if (!conf.highlightWordAroundCursor)\n                return Decoration.none;\n            let word = state.wordAt(range.head);\n            if (!word)\n                return Decoration.none;\n            check = state.charCategorizer(range.head);\n            query = state.sliceDoc(word.from, word.to);\n        }\n        else {\n            let len = range.to - range.from;\n            if (len < conf.minSelectionLength || len > 200)\n                return Decoration.none;\n            if (conf.wholeWords) {\n                query = state.sliceDoc(range.from, range.to); // TODO: allow and include leading/trailing space?\n                check = state.charCategorizer(range.head);\n                if (!(insideWordBoundaries(check, state, range.from, range.to) &&\n                    insideWord(check, state, range.from, range.to)))\n                    return Decoration.none;\n            }\n            else {\n                query = state.sliceDoc(range.from, range.to);\n                if (!query)\n                    return Decoration.none;\n            }\n        }\n        let deco = [];\n        for (let part of view.visibleRanges) {\n            let cursor = new SearchCursor(state.doc, query, part.from, part.to);\n            while (!cursor.next().done) {\n                let { from, to } = cursor.value;\n                if (!check || insideWordBoundaries(check, state, from, to)) {\n                    if (range.empty && from <= range.from && to >= range.to)\n                        deco.push(mainMatchDeco.range(from, to));\n                    else if (from >= range.to || to <= range.from)\n                        deco.push(matchDeco.range(from, to));\n                    if (deco.length > conf.maxMatches)\n                        return Decoration.none;\n                }\n            }\n        }\n        return Decoration.set(deco);\n    }\n}, {\n    decorations: v => v.decorations\n});\nconst defaultTheme = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-selectionMatch\": { backgroundColor: \"#99ff7780\" },\n    \".cm-searchMatch .cm-selectionMatch\": { backgroundColor: \"transparent\" }\n});\n// Select the words around the cursors.\nconst selectWord = ({ state, dispatch }) => {\n    let { selection } = state;\n    let newSel = EditorSelection.create(selection.ranges.map(range => state.wordAt(range.head) || EditorSelection.cursor(range.head)), selection.mainIndex);\n    if (newSel.eq(selection))\n        return false;\n    dispatch(state.update({ selection: newSel }));\n    return true;\n};\n// Find next occurrence of query relative to last cursor. Wrap around\n// the document if there are no more matches.\nfunction findNextOccurrence(state, query) {\n    let { main, ranges } = state.selection;\n    let word = state.wordAt(main.head), fullWord = word && word.from == main.from && word.to == main.to;\n    for (let cycled = false, cursor = new SearchCursor(state.doc, query, ranges[ranges.length - 1].to);;) {\n        cursor.next();\n        if (cursor.done) {\n            if (cycled)\n                return null;\n            cursor = new SearchCursor(state.doc, query, 0, Math.max(0, ranges[ranges.length - 1].from - 1));\n            cycled = true;\n        }\n        else {\n            if (cycled && ranges.some(r => r.from == cursor.value.from))\n                continue;\n            if (fullWord) {\n                let word = state.wordAt(cursor.value.from);\n                if (!word || word.from != cursor.value.from || word.to != cursor.value.to)\n                    continue;\n            }\n            return cursor.value;\n        }\n    }\n}\n/**\nSelect next occurrence of the current selection. Expand selection\nto the surrounding word when the selection is empty.\n*/\nconst selectNextOccurrence = ({ state, dispatch }) => {\n    let { ranges } = state.selection;\n    if (ranges.some(sel => sel.from === sel.to))\n        return selectWord({ state, dispatch });\n    let searchedText = state.sliceDoc(ranges[0].from, ranges[0].to);\n    if (state.selection.ranges.some(r => state.sliceDoc(r.from, r.to) != searchedText))\n        return false;\n    let range = findNextOccurrence(state, searchedText);\n    if (!range)\n        return false;\n    dispatch(state.update({\n        selection: state.selection.addRange(EditorSelection.range(range.from, range.to), false),\n        effects: EditorView.scrollIntoView(range.to)\n    }));\n    return true;\n};\n\nconst searchConfigFacet = /*@__PURE__*/Facet.define({\n    combine(configs) {\n        return combineConfig(configs, {\n            top: false,\n            caseSensitive: false,\n            literal: false,\n            regexp: false,\n            wholeWord: false,\n            createPanel: view => new SearchPanel(view),\n            scrollToMatch: range => EditorView.scrollIntoView(range)\n        });\n    }\n});\n/**\nAdd search state to the editor configuration, and optionally\nconfigure the search extension.\n([`openSearchPanel`](https://codemirror.net/6/docs/ref/#search.openSearchPanel) will automatically\nenable this if it isn't already on).\n*/\nfunction search(config) {\n    return config ? [searchConfigFacet.of(config), searchExtensions] : searchExtensions;\n}\n/**\nA search query. Part of the editor's search state.\n*/\nclass SearchQuery {\n    /**\n    Create a query object.\n    */\n    constructor(config) {\n        this.search = config.search;\n        this.caseSensitive = !!config.caseSensitive;\n        this.literal = !!config.literal;\n        this.regexp = !!config.regexp;\n        this.replace = config.replace || \"\";\n        this.valid = !!this.search && (!this.regexp || validRegExp(this.search));\n        this.unquoted = this.unquote(this.search);\n        this.wholeWord = !!config.wholeWord;\n    }\n    /**\n    @internal\n    */\n    unquote(text) {\n        return this.literal ? text :\n            text.replace(/\\\\([nrt\\\\])/g, (_, ch) => ch == \"n\" ? \"\\n\" : ch == \"r\" ? \"\\r\" : ch == \"t\" ? \"\\t\" : \"\\\\\");\n    }\n    /**\n    Compare this query to another query.\n    */\n    eq(other) {\n        return this.search == other.search && this.replace == other.replace &&\n            this.caseSensitive == other.caseSensitive && this.regexp == other.regexp &&\n            this.wholeWord == other.wholeWord;\n    }\n    /**\n    @internal\n    */\n    create() {\n        return this.regexp ? new RegExpQuery(this) : new StringQuery(this);\n    }\n    /**\n    Get a search cursor for this query, searching through the given\n    range in the given state.\n    */\n    getCursor(state, from = 0, to) {\n        let st = state.doc ? state : EditorState.create({ doc: state });\n        if (to == null)\n            to = st.doc.length;\n        return this.regexp ? regexpCursor(this, st, from, to) : stringCursor(this, st, from, to);\n    }\n}\nclass QueryType {\n    constructor(spec) {\n        this.spec = spec;\n    }\n}\nfunction stringCursor(spec, state, from, to) {\n    return new SearchCursor(state.doc, spec.unquoted, from, to, spec.caseSensitive ? undefined : x => x.toLowerCase(), spec.wholeWord ? stringWordTest(state.doc, state.charCategorizer(state.selection.main.head)) : undefined);\n}\nfunction stringWordTest(doc, categorizer) {\n    return (from, to, buf, bufPos) => {\n        if (bufPos > from || bufPos + buf.length < to) {\n            bufPos = Math.max(0, from - 2);\n            buf = doc.sliceString(bufPos, Math.min(doc.length, to + 2));\n        }\n        return (categorizer(charBefore(buf, from - bufPos)) != CharCategory.Word ||\n            categorizer(charAfter(buf, from - bufPos)) != CharCategory.Word) &&\n            (categorizer(charAfter(buf, to - bufPos)) != CharCategory.Word ||\n                categorizer(charBefore(buf, to - bufPos)) != CharCategory.Word);\n    };\n}\nclass StringQuery extends QueryType {\n    constructor(spec) {\n        super(spec);\n    }\n    nextMatch(state, curFrom, curTo) {\n        let cursor = stringCursor(this.spec, state, curTo, state.doc.length).nextOverlapping();\n        if (cursor.done)\n            cursor = stringCursor(this.spec, state, 0, curFrom).nextOverlapping();\n        return cursor.done ? null : cursor.value;\n    }\n    // Searching in reverse is, rather than implementing an inverted search\n    // cursor, done by scanning chunk after chunk forward.\n    prevMatchInRange(state, from, to) {\n        for (let pos = to;;) {\n            let start = Math.max(from, pos - 10000 /* FindPrev.ChunkSize */ - this.spec.unquoted.length);\n            let cursor = stringCursor(this.spec, state, start, pos), range = null;\n            while (!cursor.nextOverlapping().done)\n                range = cursor.value;\n            if (range)\n                return range;\n            if (start == from)\n                return null;\n            pos -= 10000 /* FindPrev.ChunkSize */;\n        }\n    }\n    prevMatch(state, curFrom, curTo) {\n        return this.prevMatchInRange(state, 0, curFrom) ||\n            this.prevMatchInRange(state, curTo, state.doc.length);\n    }\n    getReplacement(_result) { return this.spec.unquote(this.spec.replace); }\n    matchAll(state, limit) {\n        let cursor = stringCursor(this.spec, state, 0, state.doc.length), ranges = [];\n        while (!cursor.next().done) {\n            if (ranges.length >= limit)\n                return null;\n            ranges.push(cursor.value);\n        }\n        return ranges;\n    }\n    highlight(state, from, to, add) {\n        let cursor = stringCursor(this.spec, state, Math.max(0, from - this.spec.unquoted.length), Math.min(to + this.spec.unquoted.length, state.doc.length));\n        while (!cursor.next().done)\n            add(cursor.value.from, cursor.value.to);\n    }\n}\nfunction regexpCursor(spec, state, from, to) {\n    return new RegExpCursor(state.doc, spec.search, {\n        ignoreCase: !spec.caseSensitive,\n        test: spec.wholeWord ? regexpWordTest(state.charCategorizer(state.selection.main.head)) : undefined\n    }, from, to);\n}\nfunction charBefore(str, index) {\n    return str.slice(findClusterBreak(str, index, false), index);\n}\nfunction charAfter(str, index) {\n    return str.slice(index, findClusterBreak(str, index));\n}\nfunction regexpWordTest(categorizer) {\n    return (_from, _to, match) => !match[0].length ||\n        (categorizer(charBefore(match.input, match.index)) != CharCategory.Word ||\n            categorizer(charAfter(match.input, match.index)) != CharCategory.Word) &&\n            (categorizer(charAfter(match.input, match.index + match[0].length)) != CharCategory.Word ||\n                categorizer(charBefore(match.input, match.index + match[0].length)) != CharCategory.Word);\n}\nclass RegExpQuery extends QueryType {\n    nextMatch(state, curFrom, curTo) {\n        let cursor = regexpCursor(this.spec, state, curTo, state.doc.length).next();\n        if (cursor.done)\n            cursor = regexpCursor(this.spec, state, 0, curFrom).next();\n        return cursor.done ? null : cursor.value;\n    }\n    prevMatchInRange(state, from, to) {\n        for (let size = 1;; size++) {\n            let start = Math.max(from, to - size * 10000 /* FindPrev.ChunkSize */);\n            let cursor = regexpCursor(this.spec, state, start, to), range = null;\n            while (!cursor.next().done)\n                range = cursor.value;\n            if (range && (start == from || range.from > start + 10))\n                return range;\n            if (start == from)\n                return null;\n        }\n    }\n    prevMatch(state, curFrom, curTo) {\n        return this.prevMatchInRange(state, 0, curFrom) ||\n            this.prevMatchInRange(state, curTo, state.doc.length);\n    }\n    getReplacement(result) {\n        return this.spec.unquote(this.spec.replace).replace(/\\$([$&\\d+])/g, (m, i) => i == \"$\" ? \"$\"\n            : i == \"&\" ? result.match[0]\n                : i != \"0\" && +i < result.match.length ? result.match[i]\n                    : m);\n    }\n    matchAll(state, limit) {\n        let cursor = regexpCursor(this.spec, state, 0, state.doc.length), ranges = [];\n        while (!cursor.next().done) {\n            if (ranges.length >= limit)\n                return null;\n            ranges.push(cursor.value);\n        }\n        return ranges;\n    }\n    highlight(state, from, to, add) {\n        let cursor = regexpCursor(this.spec, state, Math.max(0, from - 250 /* RegExp.HighlightMargin */), Math.min(to + 250 /* RegExp.HighlightMargin */, state.doc.length));\n        while (!cursor.next().done)\n            add(cursor.value.from, cursor.value.to);\n    }\n}\n/**\nA state effect that updates the current search query. Note that\nthis only has an effect if the search state has been initialized\n(by including [`search`](https://codemirror.net/6/docs/ref/#search.search) in your configuration or\nby running [`openSearchPanel`](https://codemirror.net/6/docs/ref/#search.openSearchPanel) at least\nonce).\n*/\nconst setSearchQuery = /*@__PURE__*/StateEffect.define();\nconst togglePanel = /*@__PURE__*/StateEffect.define();\nconst searchState = /*@__PURE__*/StateField.define({\n    create(state) {\n        return new SearchState(defaultQuery(state).create(), null);\n    },\n    update(value, tr) {\n        for (let effect of tr.effects) {\n            if (effect.is(setSearchQuery))\n                value = new SearchState(effect.value.create(), value.panel);\n            else if (effect.is(togglePanel))\n                value = new SearchState(value.query, effect.value ? createSearchPanel : null);\n        }\n        return value;\n    },\n    provide: f => showPanel.from(f, val => val.panel)\n});\n/**\nGet the current search query from an editor state.\n*/\nfunction getSearchQuery(state) {\n    let curState = state.field(searchState, false);\n    return curState ? curState.query.spec : defaultQuery(state);\n}\n/**\nQuery whether the search panel is open in the given editor state.\n*/\nfunction searchPanelOpen(state) {\n    var _a;\n    return ((_a = state.field(searchState, false)) === null || _a === void 0 ? void 0 : _a.panel) != null;\n}\nclass SearchState {\n    constructor(query, panel) {\n        this.query = query;\n        this.panel = panel;\n    }\n}\nconst matchMark = /*@__PURE__*/Decoration.mark({ class: \"cm-searchMatch\" }), selectedMatchMark = /*@__PURE__*/Decoration.mark({ class: \"cm-searchMatch cm-searchMatch-selected\" });\nconst searchHighlighter = /*@__PURE__*/ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.view = view;\n        this.decorations = this.highlight(view.state.field(searchState));\n    }\n    update(update) {\n        let state = update.state.field(searchState);\n        if (state != update.startState.field(searchState) || update.docChanged || update.selectionSet || update.viewportChanged)\n            this.decorations = this.highlight(state);\n    }\n    highlight({ query, panel }) {\n        if (!panel || !query.spec.valid)\n            return Decoration.none;\n        let { view } = this;\n        let builder = new RangeSetBuilder();\n        for (let i = 0, ranges = view.visibleRanges, l = ranges.length; i < l; i++) {\n            let { from, to } = ranges[i];\n            while (i < l - 1 && to > ranges[i + 1].from - 2 * 250 /* RegExp.HighlightMargin */)\n                to = ranges[++i].to;\n            query.highlight(view.state, from, to, (from, to) => {\n                let selected = view.state.selection.ranges.some(r => r.from == from && r.to == to);\n                builder.add(from, to, selected ? selectedMatchMark : matchMark);\n            });\n        }\n        return builder.finish();\n    }\n}, {\n    decorations: v => v.decorations\n});\nfunction searchCommand(f) {\n    return view => {\n        let state = view.state.field(searchState, false);\n        return state && state.query.spec.valid ? f(view, state) : openSearchPanel(view);\n    };\n}\n/**\nOpen the search panel if it isn't already open, and move the\nselection to the first match after the current main selection.\nWill wrap around to the start of the document when it reaches the\nend.\n*/\nconst findNext = /*@__PURE__*/searchCommand((view, { query }) => {\n    let { to } = view.state.selection.main;\n    let next = query.nextMatch(view.state, to, to);\n    if (!next)\n        return false;\n    let selection = EditorSelection.single(next.from, next.to);\n    let config = view.state.facet(searchConfigFacet);\n    view.dispatch({\n        selection,\n        effects: [announceMatch(view, next), config.scrollToMatch(selection.main, view)],\n        userEvent: \"select.search\"\n    });\n    selectSearchInput(view);\n    return true;\n});\n/**\nMove the selection to the previous instance of the search query,\nbefore the current main selection. Will wrap past the start\nof the document to start searching at the end again.\n*/\nconst findPrevious = /*@__PURE__*/searchCommand((view, { query }) => {\n    let { state } = view, { from } = state.selection.main;\n    let prev = query.prevMatch(state, from, from);\n    if (!prev)\n        return false;\n    let selection = EditorSelection.single(prev.from, prev.to);\n    let config = view.state.facet(searchConfigFacet);\n    view.dispatch({\n        selection,\n        effects: [announceMatch(view, prev), config.scrollToMatch(selection.main, view)],\n        userEvent: \"select.search\"\n    });\n    selectSearchInput(view);\n    return true;\n});\n/**\nSelect all instances of the search query.\n*/\nconst selectMatches = /*@__PURE__*/searchCommand((view, { query }) => {\n    let ranges = query.matchAll(view.state, 1000);\n    if (!ranges || !ranges.length)\n        return false;\n    view.dispatch({\n        selection: EditorSelection.create(ranges.map(r => EditorSelection.range(r.from, r.to))),\n        userEvent: \"select.search.matches\"\n    });\n    return true;\n});\n/**\nSelect all instances of the currently selected text.\n*/\nconst selectSelectionMatches = ({ state, dispatch }) => {\n    let sel = state.selection;\n    if (sel.ranges.length > 1 || sel.main.empty)\n        return false;\n    let { from, to } = sel.main;\n    let ranges = [], main = 0;\n    for (let cur = new SearchCursor(state.doc, state.sliceDoc(from, to)); !cur.next().done;) {\n        if (ranges.length > 1000)\n            return false;\n        if (cur.value.from == from)\n            main = ranges.length;\n        ranges.push(EditorSelection.range(cur.value.from, cur.value.to));\n    }\n    dispatch(state.update({\n        selection: EditorSelection.create(ranges, main),\n        userEvent: \"select.search.matches\"\n    }));\n    return true;\n};\n/**\nReplace the current match of the search query.\n*/\nconst replaceNext = /*@__PURE__*/searchCommand((view, { query }) => {\n    let { state } = view, { from, to } = state.selection.main;\n    if (state.readOnly)\n        return false;\n    let next = query.nextMatch(state, from, from);\n    if (!next)\n        return false;\n    let changes = [], selection, replacement;\n    let effects = [];\n    if (next.from == from && next.to == to) {\n        replacement = state.toText(query.getReplacement(next));\n        changes.push({ from: next.from, to: next.to, insert: replacement });\n        next = query.nextMatch(state, next.from, next.to);\n        effects.push(EditorView.announce.of(state.phrase(\"replaced match on line $\", state.doc.lineAt(from).number) + \".\"));\n    }\n    if (next) {\n        let off = changes.length == 0 || changes[0].from >= next.to ? 0 : next.to - next.from - replacement.length;\n        selection = EditorSelection.single(next.from - off, next.to - off);\n        effects.push(announceMatch(view, next));\n        effects.push(state.facet(searchConfigFacet).scrollToMatch(selection.main, view));\n    }\n    view.dispatch({\n        changes, selection, effects,\n        userEvent: \"input.replace\"\n    });\n    return true;\n});\n/**\nReplace all instances of the search query with the given\nreplacement.\n*/\nconst replaceAll = /*@__PURE__*/searchCommand((view, { query }) => {\n    if (view.state.readOnly)\n        return false;\n    let changes = query.matchAll(view.state, 1e9).map(match => {\n        let { from, to } = match;\n        return { from, to, insert: query.getReplacement(match) };\n    });\n    if (!changes.length)\n        return false;\n    let announceText = view.state.phrase(\"replaced $ matches\", changes.length) + \".\";\n    view.dispatch({\n        changes,\n        effects: EditorView.announce.of(announceText),\n        userEvent: \"input.replace.all\"\n    });\n    return true;\n});\nfunction createSearchPanel(view) {\n    return view.state.facet(searchConfigFacet).createPanel(view);\n}\nfunction defaultQuery(state, fallback) {\n    var _a, _b, _c, _d, _e;\n    let sel = state.selection.main;\n    let selText = sel.empty || sel.to > sel.from + 100 ? \"\" : state.sliceDoc(sel.from, sel.to);\n    if (fallback && !selText)\n        return fallback;\n    let config = state.facet(searchConfigFacet);\n    return new SearchQuery({\n        search: ((_a = fallback === null || fallback === void 0 ? void 0 : fallback.literal) !== null && _a !== void 0 ? _a : config.literal) ? selText : selText.replace(/\\n/g, \"\\\\n\"),\n        caseSensitive: (_b = fallback === null || fallback === void 0 ? void 0 : fallback.caseSensitive) !== null && _b !== void 0 ? _b : config.caseSensitive,\n        literal: (_c = fallback === null || fallback === void 0 ? void 0 : fallback.literal) !== null && _c !== void 0 ? _c : config.literal,\n        regexp: (_d = fallback === null || fallback === void 0 ? void 0 : fallback.regexp) !== null && _d !== void 0 ? _d : config.regexp,\n        wholeWord: (_e = fallback === null || fallback === void 0 ? void 0 : fallback.wholeWord) !== null && _e !== void 0 ? _e : config.wholeWord\n    });\n}\nfunction getSearchInput(view) {\n    let panel = getPanel(view, createSearchPanel);\n    return panel && panel.dom.querySelector(\"[main-field]\");\n}\nfunction selectSearchInput(view) {\n    let input = getSearchInput(view);\n    if (input && input == view.root.activeElement)\n        input.select();\n}\n/**\nMake sure the search panel is open and focused.\n*/\nconst openSearchPanel = view => {\n    let state = view.state.field(searchState, false);\n    if (state && state.panel) {\n        let searchInput = getSearchInput(view);\n        if (searchInput && searchInput != view.root.activeElement) {\n            let query = defaultQuery(view.state, state.query.spec);\n            if (query.valid)\n                view.dispatch({ effects: setSearchQuery.of(query) });\n            searchInput.focus();\n            searchInput.select();\n        }\n    }\n    else {\n        view.dispatch({ effects: [\n                togglePanel.of(true),\n                state ? setSearchQuery.of(defaultQuery(view.state, state.query.spec)) : StateEffect.appendConfig.of(searchExtensions)\n            ] });\n    }\n    return true;\n};\n/**\nClose the search panel.\n*/\nconst closeSearchPanel = view => {\n    let state = view.state.field(searchState, false);\n    if (!state || !state.panel)\n        return false;\n    let panel = getPanel(view, createSearchPanel);\n    if (panel && panel.dom.contains(view.root.activeElement))\n        view.focus();\n    view.dispatch({ effects: togglePanel.of(false) });\n    return true;\n};\n/**\nDefault search-related key bindings.\n\n - Mod-f: [`openSearchPanel`](https://codemirror.net/6/docs/ref/#search.openSearchPanel)\n - F3, Mod-g: [`findNext`](https://codemirror.net/6/docs/ref/#search.findNext)\n - Shift-F3, Shift-Mod-g: [`findPrevious`](https://codemirror.net/6/docs/ref/#search.findPrevious)\n - Mod-Alt-g: [`gotoLine`](https://codemirror.net/6/docs/ref/#search.gotoLine)\n - Mod-d: [`selectNextOccurrence`](https://codemirror.net/6/docs/ref/#search.selectNextOccurrence)\n*/\nconst searchKeymap = [\n    { key: \"Mod-f\", run: openSearchPanel, scope: \"editor search-panel\" },\n    { key: \"F3\", run: findNext, shift: findPrevious, scope: \"editor search-panel\", preventDefault: true },\n    { key: \"Mod-g\", run: findNext, shift: findPrevious, scope: \"editor search-panel\", preventDefault: true },\n    { key: \"Escape\", run: closeSearchPanel, scope: \"editor search-panel\" },\n    { key: \"Mod-Shift-l\", run: selectSelectionMatches },\n    { key: \"Mod-Alt-g\", run: gotoLine },\n    { key: \"Mod-d\", run: selectNextOccurrence, preventDefault: true },\n];\nclass SearchPanel {\n    constructor(view) {\n        this.view = view;\n        let query = this.query = view.state.field(searchState).query.spec;\n        this.commit = this.commit.bind(this);\n        this.searchField = elt(\"input\", {\n            value: query.search,\n            placeholder: phrase(view, \"Find\"),\n            \"aria-label\": phrase(view, \"Find\"),\n            class: \"cm-textfield\",\n            name: \"search\",\n            form: \"\",\n            \"main-field\": \"true\",\n            onchange: this.commit,\n            onkeyup: this.commit\n        });\n        this.replaceField = elt(\"input\", {\n            value: query.replace,\n            placeholder: phrase(view, \"Replace\"),\n            \"aria-label\": phrase(view, \"Replace\"),\n            class: \"cm-textfield\",\n            name: \"replace\",\n            form: \"\",\n            onchange: this.commit,\n            onkeyup: this.commit\n        });\n        this.caseField = elt(\"input\", {\n            type: \"checkbox\",\n            name: \"case\",\n            form: \"\",\n            checked: query.caseSensitive,\n            onchange: this.commit\n        });\n        this.reField = elt(\"input\", {\n            type: \"checkbox\",\n            name: \"re\",\n            form: \"\",\n            checked: query.regexp,\n            onchange: this.commit\n        });\n        this.wordField = elt(\"input\", {\n            type: \"checkbox\",\n            name: \"word\",\n            form: \"\",\n            checked: query.wholeWord,\n            onchange: this.commit\n        });\n        function button(name, onclick, content) {\n            return elt(\"button\", { class: \"cm-button\", name, onclick, type: \"button\" }, content);\n        }\n        this.dom = elt(\"div\", { onkeydown: (e) => this.keydown(e), class: \"cm-search\" }, [\n            this.searchField,\n            button(\"next\", () => findNext(view), [phrase(view, \"next\")]),\n            button(\"prev\", () => findPrevious(view), [phrase(view, \"previous\")]),\n            button(\"select\", () => selectMatches(view), [phrase(view, \"all\")]),\n            elt(\"label\", null, [this.caseField, phrase(view, \"match case\")]),\n            elt(\"label\", null, [this.reField, phrase(view, \"regexp\")]),\n            elt(\"label\", null, [this.wordField, phrase(view, \"by word\")]),\n            ...view.state.readOnly ? [] : [\n                elt(\"br\"),\n                this.replaceField,\n                button(\"replace\", () => replaceNext(view), [phrase(view, \"replace\")]),\n                button(\"replaceAll\", () => replaceAll(view), [phrase(view, \"replace all\")])\n            ],\n            elt(\"button\", {\n                name: \"close\",\n                onclick: () => closeSearchPanel(view),\n                \"aria-label\": phrase(view, \"close\"),\n                type: \"button\"\n            }, [\"×\"])\n        ]);\n    }\n    commit() {\n        let query = new SearchQuery({\n            search: this.searchField.value,\n            caseSensitive: this.caseField.checked,\n            regexp: this.reField.checked,\n            wholeWord: this.wordField.checked,\n            replace: this.replaceField.value,\n        });\n        if (!query.eq(this.query)) {\n            this.query = query;\n            this.view.dispatch({ effects: setSearchQuery.of(query) });\n        }\n    }\n    keydown(e) {\n        if (runScopeHandlers(this.view, e, \"search-panel\")) {\n            e.preventDefault();\n        }\n        else if (e.keyCode == 13 && e.target == this.searchField) {\n            e.preventDefault();\n            (e.shiftKey ? findPrevious : findNext)(this.view);\n        }\n        else if (e.keyCode == 13 && e.target == this.replaceField) {\n            e.preventDefault();\n            replaceNext(this.view);\n        }\n    }\n    update(update) {\n        for (let tr of update.transactions)\n            for (let effect of tr.effects) {\n                if (effect.is(setSearchQuery) && !effect.value.eq(this.query))\n                    this.setQuery(effect.value);\n            }\n    }\n    setQuery(query) {\n        this.query = query;\n        this.searchField.value = query.search;\n        this.replaceField.value = query.replace;\n        this.caseField.checked = query.caseSensitive;\n        this.reField.checked = query.regexp;\n        this.wordField.checked = query.wholeWord;\n    }\n    mount() {\n        this.searchField.select();\n    }\n    get pos() { return 80; }\n    get top() { return this.view.state.facet(searchConfigFacet).top; }\n}\nfunction phrase(view, phrase) { return view.state.phrase(phrase); }\nconst AnnounceMargin = 30;\nconst Break = /[\\s\\.,:;?!]/;\nfunction announceMatch(view, { from, to }) {\n    let line = view.state.doc.lineAt(from), lineEnd = view.state.doc.lineAt(to).to;\n    let start = Math.max(line.from, from - AnnounceMargin), end = Math.min(lineEnd, to + AnnounceMargin);\n    let text = view.state.sliceDoc(start, end);\n    if (start != line.from) {\n        for (let i = 0; i < AnnounceMargin; i++)\n            if (!Break.test(text[i + 1]) && Break.test(text[i])) {\n                text = text.slice(i);\n                break;\n            }\n    }\n    if (end != lineEnd) {\n        for (let i = text.length - 1; i > text.length - AnnounceMargin; i--)\n            if (!Break.test(text[i - 1]) && Break.test(text[i])) {\n                text = text.slice(0, i);\n                break;\n            }\n    }\n    return EditorView.announce.of(`${view.state.phrase(\"current match\")}. ${text} ${view.state.phrase(\"on line\")} ${line.number}.`);\n}\nconst baseTheme = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-panel.cm-search\": {\n        padding: \"2px 6px 4px\",\n        position: \"relative\",\n        \"& [name=close]\": {\n            position: \"absolute\",\n            top: \"0\",\n            right: \"4px\",\n            backgroundColor: \"inherit\",\n            border: \"none\",\n            font: \"inherit\",\n            padding: 0,\n            margin: 0\n        },\n        \"& input, & button, & label\": {\n            margin: \".2em .6em .2em 0\"\n        },\n        \"& input[type=checkbox]\": {\n            marginRight: \".2em\"\n        },\n        \"& label\": {\n            fontSize: \"80%\",\n            whiteSpace: \"pre\"\n        }\n    },\n    \"&light .cm-searchMatch\": { backgroundColor: \"#ffff0054\" },\n    \"&dark .cm-searchMatch\": { backgroundColor: \"#00ffff8a\" },\n    \"&light .cm-searchMatch-selected\": { backgroundColor: \"#ff6a0054\" },\n    \"&dark .cm-searchMatch-selected\": { backgroundColor: \"#ff00ff8a\" }\n});\nconst searchExtensions = [\n    searchState,\n    /*@__PURE__*/Prec.low(searchHighlighter),\n    baseTheme\n];\n\nexport { RegExpCursor, SearchCursor, SearchQuery, closeSearchPanel, findNext, findPrevious, getSearchQuery, gotoLine, highlightSelectionMatches, openSearchPanel, replaceAll, replaceNext, search, searchKeymap, searchPanelOpen, selectMatches, selectNextOccurrence, selectSelectionMatches, setSearchQuery };\n", "import { Decoration, showPanel, Editor<PERSON>iew, ViewPlugin, logException, gutter, showTooltip, hoverTooltip, getPanel, WidgetType, GutterMarker } from '@codemirror/view';\nimport { StateEffect, StateField, Facet, combineConfig, RangeSet } from '@codemirror/state';\nimport elt from 'crelt';\n\nclass SelectedDiagnostic {\n    constructor(from, to, diagnostic) {\n        this.from = from;\n        this.to = to;\n        this.diagnostic = diagnostic;\n    }\n}\nclass LintState {\n    constructor(diagnostics, panel, selected) {\n        this.diagnostics = diagnostics;\n        this.panel = panel;\n        this.selected = selected;\n    }\n    static init(diagnostics, panel, state) {\n        // Filter the list of diagnostics for which to create markers\n        let markedDiagnostics = diagnostics;\n        let diagnosticFilter = state.facet(lintConfig).markerFilter;\n        if (diagnosticFilter)\n            markedDiagnostics = diagnosticFilter(markedDiagnostics, state);\n        let ranges = Decoration.set(markedDiagnostics.map((d) => {\n            // For zero-length ranges or ranges covering only a line break, create a widget\n            return d.from == d.to || (d.from == d.to - 1 && state.doc.lineAt(d.from).to == d.from)\n                ? Decoration.widget({\n                    widget: new DiagnosticWidget(d),\n                    diagnostic: d\n                }).range(d.from)\n                : Decoration.mark({\n                    attributes: { class: \"cm-lintRange cm-lintRange-\" + d.severity + (d.markClass ? \" \" + d.markClass : \"\") },\n                    diagnostic: d\n                }).range(d.from, d.to);\n        }), true);\n        return new LintState(ranges, panel, findDiagnostic(ranges));\n    }\n}\nfunction findDiagnostic(diagnostics, diagnostic = null, after = 0) {\n    let found = null;\n    diagnostics.between(after, 1e9, (from, to, { spec }) => {\n        if (diagnostic && spec.diagnostic != diagnostic)\n            return;\n        found = new SelectedDiagnostic(from, to, spec.diagnostic);\n        return false;\n    });\n    return found;\n}\nfunction hideTooltip(tr, tooltip) {\n    let from = tooltip.pos, to = tooltip.end || from;\n    let result = tr.state.facet(lintConfig).hideOn(tr, from, to);\n    if (result != null)\n        return result;\n    let line = tr.startState.doc.lineAt(tooltip.pos);\n    return !!(tr.effects.some(e => e.is(setDiagnosticsEffect)) || tr.changes.touchesRange(line.from, Math.max(line.to, to)));\n}\nfunction maybeEnableLint(state, effects) {\n    return state.field(lintState, false) ? effects : effects.concat(StateEffect.appendConfig.of(lintExtensions));\n}\n/**\nReturns a transaction spec which updates the current set of\ndiagnostics, and enables the lint extension if if wasn't already\nactive.\n*/\nfunction setDiagnostics(state, diagnostics) {\n    return {\n        effects: maybeEnableLint(state, [setDiagnosticsEffect.of(diagnostics)])\n    };\n}\n/**\nThe state effect that updates the set of active diagnostics. Can\nbe useful when writing an extension that needs to track these.\n*/\nconst setDiagnosticsEffect = /*@__PURE__*/StateEffect.define();\nconst togglePanel = /*@__PURE__*/StateEffect.define();\nconst movePanelSelection = /*@__PURE__*/StateEffect.define();\nconst lintState = /*@__PURE__*/StateField.define({\n    create() {\n        return new LintState(Decoration.none, null, null);\n    },\n    update(value, tr) {\n        if (tr.docChanged && value.diagnostics.size) {\n            let mapped = value.diagnostics.map(tr.changes), selected = null, panel = value.panel;\n            if (value.selected) {\n                let selPos = tr.changes.mapPos(value.selected.from, 1);\n                selected = findDiagnostic(mapped, value.selected.diagnostic, selPos) || findDiagnostic(mapped, null, selPos);\n            }\n            if (!mapped.size && panel && tr.state.facet(lintConfig).autoPanel)\n                panel = null;\n            value = new LintState(mapped, panel, selected);\n        }\n        for (let effect of tr.effects) {\n            if (effect.is(setDiagnosticsEffect)) {\n                let panel = !tr.state.facet(lintConfig).autoPanel ? value.panel : effect.value.length ? LintPanel.open : null;\n                value = LintState.init(effect.value, panel, tr.state);\n            }\n            else if (effect.is(togglePanel)) {\n                value = new LintState(value.diagnostics, effect.value ? LintPanel.open : null, value.selected);\n            }\n            else if (effect.is(movePanelSelection)) {\n                value = new LintState(value.diagnostics, value.panel, effect.value);\n            }\n        }\n        return value;\n    },\n    provide: f => [showPanel.from(f, val => val.panel),\n        EditorView.decorations.from(f, s => s.diagnostics)]\n});\n/**\nReturns the number of active lint diagnostics in the given state.\n*/\nfunction diagnosticCount(state) {\n    let lint = state.field(lintState, false);\n    return lint ? lint.diagnostics.size : 0;\n}\nconst activeMark = /*@__PURE__*/Decoration.mark({ class: \"cm-lintRange cm-lintRange-active\" });\nfunction lintTooltip(view, pos, side) {\n    let { diagnostics } = view.state.field(lintState);\n    let found = [], stackStart = 2e8, stackEnd = 0;\n    diagnostics.between(pos - (side < 0 ? 1 : 0), pos + (side > 0 ? 1 : 0), (from, to, { spec }) => {\n        if (pos >= from && pos <= to &&\n            (from == to || ((pos > from || side > 0) && (pos < to || side < 0)))) {\n            found.push(spec.diagnostic);\n            stackStart = Math.min(from, stackStart);\n            stackEnd = Math.max(to, stackEnd);\n        }\n    });\n    let diagnosticFilter = view.state.facet(lintConfig).tooltipFilter;\n    if (diagnosticFilter)\n        found = diagnosticFilter(found, view.state);\n    if (!found.length)\n        return null;\n    return {\n        pos: stackStart,\n        end: stackEnd,\n        above: view.state.doc.lineAt(stackStart).to < stackEnd,\n        create() {\n            return { dom: diagnosticsTooltip(view, found) };\n        }\n    };\n}\nfunction diagnosticsTooltip(view, diagnostics) {\n    return elt(\"ul\", { class: \"cm-tooltip-lint\" }, diagnostics.map(d => renderDiagnostic(view, d, false)));\n}\n/**\nCommand to open and focus the lint panel.\n*/\nconst openLintPanel = (view) => {\n    let field = view.state.field(lintState, false);\n    if (!field || !field.panel)\n        view.dispatch({ effects: maybeEnableLint(view.state, [togglePanel.of(true)]) });\n    let panel = getPanel(view, LintPanel.open);\n    if (panel)\n        panel.dom.querySelector(\".cm-panel-lint ul\").focus();\n    return true;\n};\n/**\nCommand to close the lint panel, when open.\n*/\nconst closeLintPanel = (view) => {\n    let field = view.state.field(lintState, false);\n    if (!field || !field.panel)\n        return false;\n    view.dispatch({ effects: togglePanel.of(false) });\n    return true;\n};\n/**\nMove the selection to the next diagnostic.\n*/\nconst nextDiagnostic = (view) => {\n    let field = view.state.field(lintState, false);\n    if (!field)\n        return false;\n    let sel = view.state.selection.main, next = field.diagnostics.iter(sel.to + 1);\n    if (!next.value) {\n        next = field.diagnostics.iter(0);\n        if (!next.value || next.from == sel.from && next.to == sel.to)\n            return false;\n    }\n    view.dispatch({ selection: { anchor: next.from, head: next.to }, scrollIntoView: true });\n    return true;\n};\n/**\nMove the selection to the previous diagnostic.\n*/\nconst previousDiagnostic = (view) => {\n    let { state } = view, field = state.field(lintState, false);\n    if (!field)\n        return false;\n    let sel = state.selection.main;\n    let prevFrom, prevTo, lastFrom, lastTo;\n    field.diagnostics.between(0, state.doc.length, (from, to) => {\n        if (to < sel.to && (prevFrom == null || prevFrom < from)) {\n            prevFrom = from;\n            prevTo = to;\n        }\n        if (lastFrom == null || from > lastFrom) {\n            lastFrom = from;\n            lastTo = to;\n        }\n    });\n    if (lastFrom == null || prevFrom == null && lastFrom == sel.from)\n        return false;\n    view.dispatch({ selection: { anchor: prevFrom !== null && prevFrom !== void 0 ? prevFrom : lastFrom, head: prevTo !== null && prevTo !== void 0 ? prevTo : lastTo }, scrollIntoView: true });\n    return true;\n};\n/**\nA set of default key bindings for the lint functionality.\n\n- Ctrl-Shift-m (Cmd-Shift-m on macOS): [`openLintPanel`](https://codemirror.net/6/docs/ref/#lint.openLintPanel)\n- F8: [`nextDiagnostic`](https://codemirror.net/6/docs/ref/#lint.nextDiagnostic)\n*/\nconst lintKeymap = [\n    { key: \"Mod-Shift-m\", run: openLintPanel, preventDefault: true },\n    { key: \"F8\", run: nextDiagnostic }\n];\nconst lintPlugin = /*@__PURE__*/ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.view = view;\n        this.timeout = -1;\n        this.set = true;\n        let { delay } = view.state.facet(lintConfig);\n        this.lintTime = Date.now() + delay;\n        this.run = this.run.bind(this);\n        this.timeout = setTimeout(this.run, delay);\n    }\n    run() {\n        clearTimeout(this.timeout);\n        let now = Date.now();\n        if (now < this.lintTime - 10) {\n            this.timeout = setTimeout(this.run, this.lintTime - now);\n        }\n        else {\n            this.set = false;\n            let { state } = this.view, { sources } = state.facet(lintConfig);\n            if (sources.length)\n                Promise.all(sources.map(source => Promise.resolve(source(this.view)))).then(annotations => {\n                    let all = annotations.reduce((a, b) => a.concat(b));\n                    if (this.view.state.doc == state.doc)\n                        this.view.dispatch(setDiagnostics(this.view.state, all));\n                }, error => { logException(this.view.state, error); });\n        }\n    }\n    update(update) {\n        let config = update.state.facet(lintConfig);\n        if (update.docChanged || config != update.startState.facet(lintConfig) ||\n            config.needsRefresh && config.needsRefresh(update)) {\n            this.lintTime = Date.now() + config.delay;\n            if (!this.set) {\n                this.set = true;\n                this.timeout = setTimeout(this.run, config.delay);\n            }\n        }\n    }\n    force() {\n        if (this.set) {\n            this.lintTime = Date.now();\n            this.run();\n        }\n    }\n    destroy() {\n        clearTimeout(this.timeout);\n    }\n});\nconst lintConfig = /*@__PURE__*/Facet.define({\n    combine(input) {\n        return Object.assign({ sources: input.map(i => i.source).filter(x => x != null) }, combineConfig(input.map(i => i.config), {\n            delay: 750,\n            markerFilter: null,\n            tooltipFilter: null,\n            needsRefresh: null,\n            hideOn: () => null,\n        }, {\n            needsRefresh: (a, b) => !a ? b : !b ? a : u => a(u) || b(u)\n        }));\n    }\n});\n/**\nGiven a diagnostic source, this function returns an extension that\nenables linting with that source. It will be called whenever the\neditor is idle (after its content changed). If `null` is given as\nsource, this only configures the lint extension.\n*/\nfunction linter(source, config = {}) {\n    return [\n        lintConfig.of({ source, config }),\n        lintPlugin,\n        lintExtensions\n    ];\n}\n/**\nForces any linters [configured](https://codemirror.net/6/docs/ref/#lint.linter) to run when the\neditor is idle to run right away.\n*/\nfunction forceLinting(view) {\n    let plugin = view.plugin(lintPlugin);\n    if (plugin)\n        plugin.force();\n}\nfunction assignKeys(actions) {\n    let assigned = [];\n    if (actions)\n        actions: for (let { name } of actions) {\n            for (let i = 0; i < name.length; i++) {\n                let ch = name[i];\n                if (/[a-zA-Z]/.test(ch) && !assigned.some(c => c.toLowerCase() == ch.toLowerCase())) {\n                    assigned.push(ch);\n                    continue actions;\n                }\n            }\n            assigned.push(\"\");\n        }\n    return assigned;\n}\nfunction renderDiagnostic(view, diagnostic, inPanel) {\n    var _a;\n    let keys = inPanel ? assignKeys(diagnostic.actions) : [];\n    return elt(\"li\", { class: \"cm-diagnostic cm-diagnostic-\" + diagnostic.severity }, elt(\"span\", { class: \"cm-diagnosticText\" }, diagnostic.renderMessage ? diagnostic.renderMessage(view) : diagnostic.message), (_a = diagnostic.actions) === null || _a === void 0 ? void 0 : _a.map((action, i) => {\n        let fired = false, click = (e) => {\n            e.preventDefault();\n            if (fired)\n                return;\n            fired = true;\n            let found = findDiagnostic(view.state.field(lintState).diagnostics, diagnostic);\n            if (found)\n                action.apply(view, found.from, found.to);\n        };\n        let { name } = action, keyIndex = keys[i] ? name.indexOf(keys[i]) : -1;\n        let nameElt = keyIndex < 0 ? name : [name.slice(0, keyIndex),\n            elt(\"u\", name.slice(keyIndex, keyIndex + 1)),\n            name.slice(keyIndex + 1)];\n        return elt(\"button\", {\n            type: \"button\",\n            class: \"cm-diagnosticAction\",\n            onclick: click,\n            onmousedown: click,\n            \"aria-label\": ` Action: ${name}${keyIndex < 0 ? \"\" : ` (access key \"${keys[i]})\"`}.`\n        }, nameElt);\n    }), diagnostic.source && elt(\"div\", { class: \"cm-diagnosticSource\" }, diagnostic.source));\n}\nclass DiagnosticWidget extends WidgetType {\n    constructor(diagnostic) {\n        super();\n        this.diagnostic = diagnostic;\n    }\n    eq(other) { return other.diagnostic == this.diagnostic; }\n    toDOM() {\n        return elt(\"span\", { class: \"cm-lintPoint cm-lintPoint-\" + this.diagnostic.severity });\n    }\n}\nclass PanelItem {\n    constructor(view, diagnostic) {\n        this.diagnostic = diagnostic;\n        this.id = \"item_\" + Math.floor(Math.random() * 0xffffffff).toString(16);\n        this.dom = renderDiagnostic(view, diagnostic, true);\n        this.dom.id = this.id;\n        this.dom.setAttribute(\"role\", \"option\");\n    }\n}\nclass LintPanel {\n    constructor(view) {\n        this.view = view;\n        this.items = [];\n        let onkeydown = (event) => {\n            if (event.keyCode == 27) { // Escape\n                closeLintPanel(this.view);\n                this.view.focus();\n            }\n            else if (event.keyCode == 38 || event.keyCode == 33) { // ArrowUp, PageUp\n                this.moveSelection((this.selectedIndex - 1 + this.items.length) % this.items.length);\n            }\n            else if (event.keyCode == 40 || event.keyCode == 34) { // ArrowDown, PageDown\n                this.moveSelection((this.selectedIndex + 1) % this.items.length);\n            }\n            else if (event.keyCode == 36) { // Home\n                this.moveSelection(0);\n            }\n            else if (event.keyCode == 35) { // End\n                this.moveSelection(this.items.length - 1);\n            }\n            else if (event.keyCode == 13) { // Enter\n                this.view.focus();\n            }\n            else if (event.keyCode >= 65 && event.keyCode <= 90 && this.selectedIndex >= 0) { // A-Z\n                let { diagnostic } = this.items[this.selectedIndex], keys = assignKeys(diagnostic.actions);\n                for (let i = 0; i < keys.length; i++)\n                    if (keys[i].toUpperCase().charCodeAt(0) == event.keyCode) {\n                        let found = findDiagnostic(this.view.state.field(lintState).diagnostics, diagnostic);\n                        if (found)\n                            diagnostic.actions[i].apply(view, found.from, found.to);\n                    }\n            }\n            else {\n                return;\n            }\n            event.preventDefault();\n        };\n        let onclick = (event) => {\n            for (let i = 0; i < this.items.length; i++) {\n                if (this.items[i].dom.contains(event.target))\n                    this.moveSelection(i);\n            }\n        };\n        this.list = elt(\"ul\", {\n            tabIndex: 0,\n            role: \"listbox\",\n            \"aria-label\": this.view.state.phrase(\"Diagnostics\"),\n            onkeydown,\n            onclick\n        });\n        this.dom = elt(\"div\", { class: \"cm-panel-lint\" }, this.list, elt(\"button\", {\n            type: \"button\",\n            name: \"close\",\n            \"aria-label\": this.view.state.phrase(\"close\"),\n            onclick: () => closeLintPanel(this.view)\n        }, \"×\"));\n        this.update();\n    }\n    get selectedIndex() {\n        let selected = this.view.state.field(lintState).selected;\n        if (!selected)\n            return -1;\n        for (let i = 0; i < this.items.length; i++)\n            if (this.items[i].diagnostic == selected.diagnostic)\n                return i;\n        return -1;\n    }\n    update() {\n        let { diagnostics, selected } = this.view.state.field(lintState);\n        let i = 0, needsSync = false, newSelectedItem = null;\n        diagnostics.between(0, this.view.state.doc.length, (_start, _end, { spec }) => {\n            let found = -1, item;\n            for (let j = i; j < this.items.length; j++)\n                if (this.items[j].diagnostic == spec.diagnostic) {\n                    found = j;\n                    break;\n                }\n            if (found < 0) {\n                item = new PanelItem(this.view, spec.diagnostic);\n                this.items.splice(i, 0, item);\n                needsSync = true;\n            }\n            else {\n                item = this.items[found];\n                if (found > i) {\n                    this.items.splice(i, found - i);\n                    needsSync = true;\n                }\n            }\n            if (selected && item.diagnostic == selected.diagnostic) {\n                if (!item.dom.hasAttribute(\"aria-selected\")) {\n                    item.dom.setAttribute(\"aria-selected\", \"true\");\n                    newSelectedItem = item;\n                }\n            }\n            else if (item.dom.hasAttribute(\"aria-selected\")) {\n                item.dom.removeAttribute(\"aria-selected\");\n            }\n            i++;\n        });\n        while (i < this.items.length && !(this.items.length == 1 && this.items[0].diagnostic.from < 0)) {\n            needsSync = true;\n            this.items.pop();\n        }\n        if (this.items.length == 0) {\n            this.items.push(new PanelItem(this.view, {\n                from: -1, to: -1,\n                severity: \"info\",\n                message: this.view.state.phrase(\"No diagnostics\")\n            }));\n            needsSync = true;\n        }\n        if (newSelectedItem) {\n            this.list.setAttribute(\"aria-activedescendant\", newSelectedItem.id);\n            this.view.requestMeasure({\n                key: this,\n                read: () => ({ sel: newSelectedItem.dom.getBoundingClientRect(), panel: this.list.getBoundingClientRect() }),\n                write: ({ sel, panel }) => {\n                    let scaleY = panel.height / this.list.offsetHeight;\n                    if (sel.top < panel.top)\n                        this.list.scrollTop -= (panel.top - sel.top) / scaleY;\n                    else if (sel.bottom > panel.bottom)\n                        this.list.scrollTop += (sel.bottom - panel.bottom) / scaleY;\n                }\n            });\n        }\n        else if (this.selectedIndex < 0) {\n            this.list.removeAttribute(\"aria-activedescendant\");\n        }\n        if (needsSync)\n            this.sync();\n    }\n    sync() {\n        let domPos = this.list.firstChild;\n        function rm() {\n            let prev = domPos;\n            domPos = prev.nextSibling;\n            prev.remove();\n        }\n        for (let item of this.items) {\n            if (item.dom.parentNode == this.list) {\n                while (domPos != item.dom)\n                    rm();\n                domPos = item.dom.nextSibling;\n            }\n            else {\n                this.list.insertBefore(item.dom, domPos);\n            }\n        }\n        while (domPos)\n            rm();\n    }\n    moveSelection(selectedIndex) {\n        if (this.selectedIndex < 0)\n            return;\n        let field = this.view.state.field(lintState);\n        let selection = findDiagnostic(field.diagnostics, this.items[selectedIndex].diagnostic);\n        if (!selection)\n            return;\n        this.view.dispatch({\n            selection: { anchor: selection.from, head: selection.to },\n            scrollIntoView: true,\n            effects: movePanelSelection.of(selection)\n        });\n    }\n    static open(view) { return new LintPanel(view); }\n}\nfunction svg(content, attrs = `viewBox=\"0 0 40 40\"`) {\n    return `url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" ${attrs}>${encodeURIComponent(content)}</svg>')`;\n}\nfunction underline(color) {\n    return svg(`<path d=\"m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0\" stroke=\"${color}\" fill=\"none\" stroke-width=\".7\"/>`, `width=\"6\" height=\"3\"`);\n}\nconst baseTheme = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-diagnostic\": {\n        padding: \"3px 6px 3px 8px\",\n        marginLeft: \"-1px\",\n        display: \"block\",\n        whiteSpace: \"pre-wrap\"\n    },\n    \".cm-diagnostic-error\": { borderLeft: \"5px solid #d11\" },\n    \".cm-diagnostic-warning\": { borderLeft: \"5px solid orange\" },\n    \".cm-diagnostic-info\": { borderLeft: \"5px solid #999\" },\n    \".cm-diagnostic-hint\": { borderLeft: \"5px solid #66d\" },\n    \".cm-diagnosticAction\": {\n        font: \"inherit\",\n        border: \"none\",\n        padding: \"2px 4px\",\n        backgroundColor: \"#444\",\n        color: \"white\",\n        borderRadius: \"3px\",\n        marginLeft: \"8px\",\n        cursor: \"pointer\"\n    },\n    \".cm-diagnosticSource\": {\n        fontSize: \"70%\",\n        opacity: .7\n    },\n    \".cm-lintRange\": {\n        backgroundPosition: \"left bottom\",\n        backgroundRepeat: \"repeat-x\",\n        paddingBottom: \"0.7px\",\n    },\n    \".cm-lintRange-error\": { backgroundImage: /*@__PURE__*/underline(\"#d11\") },\n    \".cm-lintRange-warning\": { backgroundImage: /*@__PURE__*/underline(\"orange\") },\n    \".cm-lintRange-info\": { backgroundImage: /*@__PURE__*/underline(\"#999\") },\n    \".cm-lintRange-hint\": { backgroundImage: /*@__PURE__*/underline(\"#66d\") },\n    \".cm-lintRange-active\": { backgroundColor: \"#ffdd9980\" },\n    \".cm-tooltip-lint\": {\n        padding: 0,\n        margin: 0\n    },\n    \".cm-lintPoint\": {\n        position: \"relative\",\n        \"&:after\": {\n            content: '\"\"',\n            position: \"absolute\",\n            bottom: 0,\n            left: \"-2px\",\n            borderLeft: \"3px solid transparent\",\n            borderRight: \"3px solid transparent\",\n            borderBottom: \"4px solid #d11\"\n        }\n    },\n    \".cm-lintPoint-warning\": {\n        \"&:after\": { borderBottomColor: \"orange\" }\n    },\n    \".cm-lintPoint-info\": {\n        \"&:after\": { borderBottomColor: \"#999\" }\n    },\n    \".cm-lintPoint-hint\": {\n        \"&:after\": { borderBottomColor: \"#66d\" }\n    },\n    \".cm-panel.cm-panel-lint\": {\n        position: \"relative\",\n        \"& ul\": {\n            maxHeight: \"100px\",\n            overflowY: \"auto\",\n            \"& [aria-selected]\": {\n                backgroundColor: \"#ddd\",\n                \"& u\": { textDecoration: \"underline\" }\n            },\n            \"&:focus [aria-selected]\": {\n                background_fallback: \"#bdf\",\n                backgroundColor: \"Highlight\",\n                color_fallback: \"white\",\n                color: \"HighlightText\"\n            },\n            \"& u\": { textDecoration: \"none\" },\n            padding: 0,\n            margin: 0\n        },\n        \"& [name=close]\": {\n            position: \"absolute\",\n            top: \"0\",\n            right: \"2px\",\n            background: \"inherit\",\n            border: \"none\",\n            font: \"inherit\",\n            padding: 0,\n            margin: 0\n        }\n    }\n});\nfunction severityWeight(sev) {\n    return sev == \"error\" ? 4 : sev == \"warning\" ? 3 : sev == \"info\" ? 2 : 1;\n}\nclass LintGutterMarker extends GutterMarker {\n    constructor(diagnostics) {\n        super();\n        this.diagnostics = diagnostics;\n        this.severity = diagnostics.reduce((max, d) => severityWeight(max) < severityWeight(d.severity) ? d.severity : max, \"hint\");\n    }\n    toDOM(view) {\n        let elt = document.createElement(\"div\");\n        elt.className = \"cm-lint-marker cm-lint-marker-\" + this.severity;\n        let diagnostics = this.diagnostics;\n        let diagnosticsFilter = view.state.facet(lintGutterConfig).tooltipFilter;\n        if (diagnosticsFilter)\n            diagnostics = diagnosticsFilter(diagnostics, view.state);\n        if (diagnostics.length)\n            elt.onmouseover = () => gutterMarkerMouseOver(view, elt, diagnostics);\n        return elt;\n    }\n}\nfunction trackHoverOn(view, marker) {\n    let mousemove = (event) => {\n        let rect = marker.getBoundingClientRect();\n        if (event.clientX > rect.left - 10 /* Hover.Margin */ && event.clientX < rect.right + 10 /* Hover.Margin */ &&\n            event.clientY > rect.top - 10 /* Hover.Margin */ && event.clientY < rect.bottom + 10 /* Hover.Margin */)\n            return;\n        for (let target = event.target; target; target = target.parentNode) {\n            if (target.nodeType == 1 && target.classList.contains(\"cm-tooltip-lint\"))\n                return;\n        }\n        window.removeEventListener(\"mousemove\", mousemove);\n        if (view.state.field(lintGutterTooltip))\n            view.dispatch({ effects: setLintGutterTooltip.of(null) });\n    };\n    window.addEventListener(\"mousemove\", mousemove);\n}\nfunction gutterMarkerMouseOver(view, marker, diagnostics) {\n    function hovered() {\n        let line = view.elementAtHeight(marker.getBoundingClientRect().top + 5 - view.documentTop);\n        const linePos = view.coordsAtPos(line.from);\n        if (linePos) {\n            view.dispatch({ effects: setLintGutterTooltip.of({\n                    pos: line.from,\n                    above: false,\n                    create() {\n                        return {\n                            dom: diagnosticsTooltip(view, diagnostics),\n                            getCoords: () => marker.getBoundingClientRect()\n                        };\n                    }\n                }) });\n        }\n        marker.onmouseout = marker.onmousemove = null;\n        trackHoverOn(view, marker);\n    }\n    let { hoverTime } = view.state.facet(lintGutterConfig);\n    let hoverTimeout = setTimeout(hovered, hoverTime);\n    marker.onmouseout = () => {\n        clearTimeout(hoverTimeout);\n        marker.onmouseout = marker.onmousemove = null;\n    };\n    marker.onmousemove = () => {\n        clearTimeout(hoverTimeout);\n        hoverTimeout = setTimeout(hovered, hoverTime);\n    };\n}\nfunction markersForDiagnostics(doc, diagnostics) {\n    let byLine = Object.create(null);\n    for (let diagnostic of diagnostics) {\n        let line = doc.lineAt(diagnostic.from);\n        (byLine[line.from] || (byLine[line.from] = [])).push(diagnostic);\n    }\n    let markers = [];\n    for (let line in byLine) {\n        markers.push(new LintGutterMarker(byLine[line]).range(+line));\n    }\n    return RangeSet.of(markers, true);\n}\nconst lintGutterExtension = /*@__PURE__*/gutter({\n    class: \"cm-gutter-lint\",\n    markers: view => view.state.field(lintGutterMarkers),\n});\nconst lintGutterMarkers = /*@__PURE__*/StateField.define({\n    create() {\n        return RangeSet.empty;\n    },\n    update(markers, tr) {\n        markers = markers.map(tr.changes);\n        let diagnosticFilter = tr.state.facet(lintGutterConfig).markerFilter;\n        for (let effect of tr.effects) {\n            if (effect.is(setDiagnosticsEffect)) {\n                let diagnostics = effect.value;\n                if (diagnosticFilter)\n                    diagnostics = diagnosticFilter(diagnostics || [], tr.state);\n                markers = markersForDiagnostics(tr.state.doc, diagnostics.slice(0));\n            }\n        }\n        return markers;\n    }\n});\nconst setLintGutterTooltip = /*@__PURE__*/StateEffect.define();\nconst lintGutterTooltip = /*@__PURE__*/StateField.define({\n    create() { return null; },\n    update(tooltip, tr) {\n        if (tooltip && tr.docChanged)\n            tooltip = hideTooltip(tr, tooltip) ? null : Object.assign(Object.assign({}, tooltip), { pos: tr.changes.mapPos(tooltip.pos) });\n        return tr.effects.reduce((t, e) => e.is(setLintGutterTooltip) ? e.value : t, tooltip);\n    },\n    provide: field => showTooltip.from(field)\n});\nconst lintGutterTheme = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-gutter-lint\": {\n        width: \"1.4em\",\n        \"& .cm-gutterElement\": {\n            padding: \".2em\"\n        }\n    },\n    \".cm-lint-marker\": {\n        width: \"1em\",\n        height: \"1em\"\n    },\n    \".cm-lint-marker-info\": {\n        content: /*@__PURE__*/svg(`<path fill=\"#aaf\" stroke=\"#77e\" stroke-width=\"6\" stroke-linejoin=\"round\" d=\"M5 5L35 5L35 35L5 35Z\"/>`)\n    },\n    \".cm-lint-marker-warning\": {\n        content: /*@__PURE__*/svg(`<path fill=\"#fe8\" stroke=\"#fd7\" stroke-width=\"6\" stroke-linejoin=\"round\" d=\"M20 6L37 35L3 35Z\"/>`),\n    },\n    \".cm-lint-marker-error\": {\n        content: /*@__PURE__*/svg(`<circle cx=\"20\" cy=\"20\" r=\"15\" fill=\"#f87\" stroke=\"#f43\" stroke-width=\"6\"/>`)\n    },\n});\nconst lintExtensions = [\n    lintState,\n    /*@__PURE__*/EditorView.decorations.compute([lintState], state => {\n        let { selected, panel } = state.field(lintState);\n        return !selected || !panel || selected.from == selected.to ? Decoration.none : Decoration.set([\n            activeMark.range(selected.from, selected.to)\n        ]);\n    }),\n    /*@__PURE__*/hoverTooltip(lintTooltip, { hideOn: hideTooltip }),\n    baseTheme\n];\nconst lintGutterConfig = /*@__PURE__*/Facet.define({\n    combine(configs) {\n        return combineConfig(configs, {\n            hoverTime: 300 /* Hover.Time */,\n            markerFilter: null,\n            tooltipFilter: null\n        });\n    }\n});\n/**\nReturns an extension that installs a gutter showing markers for\neach line that has diagnostics, which can be hovered over to see\nthe diagnostics.\n*/\nfunction lintGutter(config = {}) {\n    return [lintGutterConfig.of(config), lintGutterMarkers, lintGutterExtension, lintGutterTheme, lintGutterTooltip];\n}\n/**\nIterate over the marked diagnostics for the given editor state,\ncalling `f` for each of them. Note that, if the document changed\nsince the diagnostics were created, the `Diagnostic` object will\nhold the original outdated position, whereas the `to` and `from`\narguments hold the diagnostic's current position.\n*/\nfunction forEachDiagnostic(state, f) {\n    let lState = state.field(lintState, false);\n    if (lState && lState.diagnostics.size)\n        for (let iter = RangeSet.iter([lState.diagnostics]); iter.value; iter.next())\n            f(iter.value.spec.diagnostic, iter.from, iter.to);\n}\n\nexport { closeLintPanel, diagnosticCount, forEachDiagnostic, forceLinting, lintGutter, lintKeymap, linter, nextDiagnostic, openLintPanel, previousDiagnostic, setDiagnostics, setDiagnosticsEffect };\n", "import { lineNumbers, highlightActiveLineGutter, highlightSpecialChars, drawSelection, dropCursor, rectangularSelection, crosshairCursor, highlightActiveLine, keymap } from '@codemirror/view';\nexport { EditorView } from '@codemirror/view';\nimport { EditorState } from '@codemirror/state';\nimport { foldGutter, indentOnInput, syntaxHighlighting, defaultHighlightStyle, bracketMatching, foldKeymap } from '@codemirror/language';\nimport { history, defaultKeymap, historyKeymap } from '@codemirror/commands';\nimport { highlightSelectionMatches, searchKeymap } from '@codemirror/search';\nimport { closeBrackets, autocompletion, closeBracketsKeymap, completionKeymap } from '@codemirror/autocomplete';\nimport { lintKeymap } from '@codemirror/lint';\n\n// (The superfluous function calls around the list of extensions work\n// around current limitations in tree-shaking software.)\n/**\nThis is an extension value that just pulls together a number of\nextensions that you might want in a basic editor. It is meant as a\nconvenient helper to quickly set up CodeMirror without installing\nand importing a lot of separate packages.\n\nSpecifically, it includes...\n\n - [the default command bindings](https://codemirror.net/6/docs/ref/#commands.defaultKeymap)\n - [line numbers](https://codemirror.net/6/docs/ref/#view.lineNumbers)\n - [special character highlighting](https://codemirror.net/6/docs/ref/#view.highlightSpecialChars)\n - [the undo history](https://codemirror.net/6/docs/ref/#commands.history)\n - [a fold gutter](https://codemirror.net/6/docs/ref/#language.foldGutter)\n - [custom selection drawing](https://codemirror.net/6/docs/ref/#view.drawSelection)\n - [drop cursor](https://codemirror.net/6/docs/ref/#view.dropCursor)\n - [multiple selections](https://codemirror.net/6/docs/ref/#state.EditorState^allowMultipleSelections)\n - [reindentation on input](https://codemirror.net/6/docs/ref/#language.indentOnInput)\n - [the default highlight style](https://codemirror.net/6/docs/ref/#language.defaultHighlightStyle) (as fallback)\n - [bracket matching](https://codemirror.net/6/docs/ref/#language.bracketMatching)\n - [bracket closing](https://codemirror.net/6/docs/ref/#autocomplete.closeBrackets)\n - [autocompletion](https://codemirror.net/6/docs/ref/#autocomplete.autocompletion)\n - [rectangular selection](https://codemirror.net/6/docs/ref/#view.rectangularSelection) and [crosshair cursor](https://codemirror.net/6/docs/ref/#view.crosshairCursor)\n - [active line highlighting](https://codemirror.net/6/docs/ref/#view.highlightActiveLine)\n - [active line gutter highlighting](https://codemirror.net/6/docs/ref/#view.highlightActiveLineGutter)\n - [selection match highlighting](https://codemirror.net/6/docs/ref/#search.highlightSelectionMatches)\n - [search](https://codemirror.net/6/docs/ref/#search.searchKeymap)\n - [linting](https://codemirror.net/6/docs/ref/#lint.lintKeymap)\n\n(You'll probably want to add some language package to your setup\ntoo.)\n\nThis extension does not allow customization. The idea is that,\nonce you decide you want to configure your editor more precisely,\nyou take this package's source (which is just a bunch of imports\nand an array literal), copy it into your own code, and adjust it\nas desired.\n*/\nconst basicSetup = /*@__PURE__*/(() => [\n    lineNumbers(),\n    highlightActiveLineGutter(),\n    highlightSpecialChars(),\n    history(),\n    foldGutter(),\n    drawSelection(),\n    dropCursor(),\n    EditorState.allowMultipleSelections.of(true),\n    indentOnInput(),\n    syntaxHighlighting(defaultHighlightStyle, { fallback: true }),\n    bracketMatching(),\n    closeBrackets(),\n    autocompletion(),\n    rectangularSelection(),\n    crosshairCursor(),\n    highlightActiveLine(),\n    highlightSelectionMatches(),\n    keymap.of([\n        ...closeBracketsKeymap,\n        ...defaultKeymap,\n        ...searchKeymap,\n        ...historyKeymap,\n        ...foldKeymap,\n        ...completionKeymap,\n        ...lintKeymap\n    ])\n])();\n/**\nA minimal set of extensions to create a functional editor. Only\nincludes [the default keymap](https://codemirror.net/6/docs/ref/#commands.defaultKeymap), [undo\nhistory](https://codemirror.net/6/docs/ref/#commands.history), [special character\nhighlighting](https://codemirror.net/6/docs/ref/#view.highlightSpecialChars), [custom selection\ndrawing](https://codemirror.net/6/docs/ref/#view.drawSelection), and [default highlight\nstyle](https://codemirror.net/6/docs/ref/#language.defaultHighlightStyle).\n*/\nconst minimalSetup = /*@__PURE__*/(() => [\n    highlightSpecialChars(),\n    history(),\n    drawSelection(),\n    syntaxHighlighting(defaultHighlightStyle, { fallback: true }),\n    keymap.of([\n        ...defaultKeymap,\n        ...historyKeymap,\n    ])\n])();\n\nexport { basicSetup, minimalSetup };\n", "import { App, inject } from 'vue'\nimport { basicSetup } from 'codemirror'\nimport { ConfigProps } from './props'\n\nexport const DEFAULT_CONFIG: Readonly<Partial<ConfigProps>> = Object.freeze({\n  autofocus: false,\n  disabled: false,\n  indentWithTab: true,\n  tabSize: 2,\n  placeholder: '',\n  autoDestroy: true,\n  extensions: [basicSetup]\n})\n\nconst CONFIG_SYMBOL = Symbol('vue-codemirror-global-config')\nexport const injectGlobalConfig = (app: App, config?: ConfigProps) => {\n  app.provide(CONFIG_SYMBOL, config)\n}\n\nexport const useGlobalConfig = () => {\n  return inject<ConfigProps>(CONFIG_SYMBOL, {} as ConfigProps)\n}\n", "import type { CSSProperties } from 'vue'\nimport { EditorState, EditorStateConfig, Compartment, Extension, StateEffect } from '@codemirror/state'\nimport { <PERSON><PERSON><PERSON><PERSON>, EditorViewConfig, ViewUpdate, keymap, placeholder } from '@codemirror/view'\nimport { indentWithTab } from '@codemirror/commands'\nimport { indentUnit } from '@codemirror/language'\n\nexport interface CreateStateOptions extends EditorStateConfig {\n  onChange(doc: string, viewUpdate: ViewUpdate): void\n  onUpdate(viewUpdate: ViewUpdate): void\n  onFocus(viewUpdate: ViewUpdate): void\n  onBlur(viewUpdate: ViewUpdate): void\n}\n\nexport const createEditorState = ({ onUpdate, onChange, onFocus, onBlur, ...config }: CreateStateOptions) => {\n  return EditorState.create({\n    doc: config.doc,\n    selection: config.selection,\n    extensions: [\n      ...(Array.isArray(config.extensions) ? config.extensions : [config.extensions]),\n      EditorView.updateListener.of((viewUpdate) => {\n        // https://discuss.codemirror.net/t/codemirror-6-proper-way-to-listen-for-changes/2395/11\n        onUpdate(viewUpdate)\n        // doc changed\n        if (viewUpdate.docChanged) {\n          onChange(viewUpdate.state.doc.toString(), viewUpdate)\n        }\n        // focus state change\n        if (viewUpdate.focusChanged) {\n          viewUpdate.view.hasFocus ? onFocus(viewUpdate) : onBlur(viewUpdate)\n        }\n      })\n    ]\n  })\n}\n\nexport const createEditorView = (config: EditorViewConfig) => new EditorView({ ...config })\nexport const destroyEditorView = (view: EditorView) => view.destroy()\n\n// https://codemirror.net/examples/config/\n// https://github.com/uiwjs/react-codemirror/blob/22cc81971a/src/useCodeMirror.ts#L144\n// https://gist.github.com/s-cork/e7104bace090702f6acbc3004228f2cb\nexport const createEditorCompartment = (view: EditorView) => {\n  const compartment = new Compartment()\n  const run = (extension: Extension) => {\n    compartment.get(view.state)\n      ? view.dispatch({ effects: compartment.reconfigure(extension) }) // reconfigure\n      : view.dispatch({ effects: StateEffect.appendConfig.of(compartment.of(extension)) }) // inject\n  }\n  return { compartment, run }\n}\n\n// https://codemirror.net/examples/reconfigure/\nexport const createEditorExtensionToggler = (view: EditorView, extension: Extension) => {\n  const { compartment, run } = createEditorCompartment(view)\n  return (targetApply?: boolean) => {\n    const exExtension = compartment.get(view.state)\n    const apply = targetApply ?? exExtension !== extension\n    run(apply ? extension : [])\n  }\n}\n\nexport const getEditorTools = (view: EditorView) => {\n  // doc state\n  const getDoc = () => view.state.doc.toString()\n  const setDoc = (newDoc: string) => {\n    if (newDoc !== getDoc()) {\n      view.dispatch({\n        changes: {\n          from: 0,\n          to: view.state.doc.length,\n          insert: newDoc\n        }\n      })\n    }\n  }\n\n  // UX operations\n  const focus = () => view.focus()\n\n  // reconfigure extension\n  const { run: reExtensions } = createEditorCompartment(view)\n\n  // disabled editor\n  const toggleDisabled = createEditorExtensionToggler(view, [\n    EditorView.editable.of(false),\n    EditorState.readOnly.of(true)\n  ])\n\n  // https://codemirror.net/examples/tab/\n  const toggleIndentWithTab = createEditorExtensionToggler(view, keymap.of([indentWithTab]))\n\n  // tab size\n  // https://gist.github.com/s-cork/e7104bace090702f6acbc3004228f2cb\n  const { run: reTabSize } = createEditorCompartment(view)\n  const setTabSize = (tabSize: number) => {\n    reTabSize([EditorState.tabSize.of(tabSize), indentUnit.of(' '.repeat(tabSize))])\n  }\n\n  // phrases\n  // https://codemirror.net/examples/translate/\n  const { run: rePhrases } = createEditorCompartment(view)\n  const setPhrases = (phrases: Record<string, string>) => {\n    rePhrases([EditorState.phrases.of(phrases)])\n  }\n\n  // set editor's placeholder\n  const { run: rePlaceholder } = createEditorCompartment(view)\n  const setPlaceholder = (value: string) => {\n    rePlaceholder(placeholder(value))\n  }\n\n  // set style to editor element\n  // https://codemirror.net/examples/styling/\n  const { run: reStyle } = createEditorCompartment(view)\n  const setStyle = (style: CSSProperties = {}) => {\n    reStyle(EditorView.theme({ '&': { ...(style as any) } }))\n  }\n\n  return {\n    focus,\n    getDoc,\n    setDoc,\n    reExtensions,\n    toggleDisabled,\n    toggleIndentWithTab,\n    setTabSize,\n    setPhrases,\n    setPlaceholder,\n    setStyle\n  }\n}\n", "import { EditorState } from '@codemirror/state'\nimport { EditorView, ViewUpdate } from '@codemirror/view'\n\nexport enum EventKey {\n  Change = 'change',\n  Update = 'update',\n  Focus = 'focus',\n  Blur = 'blur',\n  Ready = 'ready',\n  ModelUpdate = 'update:modelValue'\n}\n\nexport const editorEvents = {\n  // when content(doc) change only\n  [EventKey.Change]: (value: string, viewUpdate: ViewUpdate) => true,\n  // when codemirror state change\n  [EventKey.Update]: (viewUpdate: ViewUpdate) => true,\n  [EventKey.Focus]: (viewUpdate: ViewUpdate) => true,\n  [EventKey.Blur]: (viewUpdate: ViewUpdate) => true,\n  // when component mounted\n  [EventKey.Ready]: (payload: { view: EditorView; state: EditorState; container: HTMLDivElement }) => true\n}\n\nexport const modelUpdateEvent = {\n  [EventKey.ModelUpdate]: editorEvents[EventKey.Change]\n}\n\nexport const events = {\n  ...editorEvents,\n  ...modelUpdateEvent\n}\n\nexport type EditorEvents = typeof editorEvents\nexport type Events = typeof events\n", "import type { CSSProperties } from 'vue'\nimport { PropType, ExtractPropTypes } from 'vue'\nimport { EditorStateConfig } from '@codemirror/state'\n\nconst UNDEFINED = void 0\nconst NonDefaultBooleanType = {\n  type: Boolean,\n  default: UNDEFINED\n}\n\nexport const configProps = {\n  autofocus: NonDefaultBooleanType,\n  disabled: NonDefaultBooleanType,\n  indentWithTab: NonDefaultBooleanType,\n  tabSize: Number,\n  placeholder: String,\n  style: Object as PropType<CSSProperties>,\n  autoDestroy: NonDefaultBooleanType,\n  phrases: Object as PropType<Record<string, string>>,\n  // codemirror options\n  root: Object as PropType<ShadowRoot | Document>,\n  extensions: Array as PropType<EditorStateConfig['extensions']>,\n  selection: Object as PropType<EditorStateConfig['selection']>\n}\n\nexport const modelValueProp = {\n  modelValue: {\n    type: String,\n    default: ''\n  }\n}\n\nexport const props = {\n  ...configProps,\n  ...modelValueProp\n}\n\nexport type ConfigProps = ExtractPropTypes<typeof configProps>\nexport type Props = ExtractPropTypes<typeof props>\nexport type PropKey = keyof Props\n", "import { defineComponent, shallowRef, computed, watch, toRaw, onMounted, onBeforeUnmount, h } from 'vue'\nimport { EditorState } from '@codemirror/state'\nimport { EditorView } from '@codemirror/view'\nimport { createEditorState, createEditorView, destroyEditorView, getEditorTools } from './codemirror'\nimport { useGlobalConfig, DEFAULT_CONFIG } from './config'\nimport { props, ConfigProps } from './props'\nimport { events, EventKey } from './events'\n\nexport default defineComponent({\n  name: 'VueCodemirror',\n  props: { ...props },\n  emits: { ...events },\n  setup(props, context) {\n    const container = shallowRef<HTMLDivElement>()\n    const state = shallowRef<EditorState>()\n    const view = shallowRef<EditorView>()\n\n    const defaultConfig: ConfigProps = {\n      ...DEFAULT_CONFIG,\n      ...useGlobalConfig()\n    }\n\n    const config = computed<ConfigProps>(() => {\n      const result = {} as Required<ConfigProps>\n      Object.keys(toRaw(props)).forEach((key: any) => {\n        if (key !== 'modelValue') {\n          // @ts-ignore\n          // MARK: ensure access to `prop[key]` original object\n          result[key] = props[key] ?? defaultConfig[key]\n        }\n      })\n      return result\n    })\n\n    onMounted(() => {\n      state.value = createEditorState({\n        doc: props.modelValue,\n        selection: config.value.selection,\n        // The extensions are split into two parts, global and component prop.\n        // Only the global part is initialized here.\n        // The prop part is dynamically reconfigured after the component is mounted.\n        extensions: defaultConfig.extensions ?? [],\n        onFocus: (viewUpdate) => context.emit(EventKey.Focus, viewUpdate),\n        onBlur: (viewUpdate) => context.emit(EventKey.Blur, viewUpdate),\n        onUpdate: (viewUpdate) => context.emit(EventKey.Update, viewUpdate),\n        onChange: (newDoc, viewUpdate) => {\n          if (newDoc !== props.modelValue) {\n            context.emit(EventKey.Change, newDoc, viewUpdate)\n            context.emit(EventKey.ModelUpdate, newDoc, viewUpdate)\n          }\n        }\n      })\n\n      view.value = createEditorView({\n        state: state.value,\n        parent: container.value!,\n        root: config.value.root\n      })\n\n      const editorTools = getEditorTools(view.value)\n\n      // watch prop.modelValue\n      watch(\n        () => props.modelValue,\n        (newValue) => {\n          if (newValue !== editorTools.getDoc()) {\n            editorTools.setDoc(newValue)\n          }\n        }\n      )\n\n      // watch prop.extensions\n      watch(\n        () => props.extensions,\n        (extensions) => editorTools.reExtensions(extensions || []),\n        { immediate: true }\n      )\n\n      // watch prop.disabled\n      watch(\n        () => config.value.disabled,\n        (disabled) => editorTools.toggleDisabled(disabled),\n        { immediate: true }\n      )\n\n      // watch prop.indentWithTab\n      watch(\n        () => config.value.indentWithTab,\n        (iwt) => editorTools.toggleIndentWithTab(iwt),\n        { immediate: true }\n      )\n\n      // watch prop.tabSize\n      watch(\n        () => config.value.tabSize,\n        (tabSize) => editorTools.setTabSize(tabSize!),\n        { immediate: true }\n      )\n\n      // watch prop.phrases\n      watch(\n        () => config.value.phrases,\n        (phrases) => editorTools.setPhrases(phrases || {}),\n        { immediate: true }\n      )\n\n      // watch prop.placeholder\n      watch(\n        () => config.value.placeholder,\n        (placeholder) => editorTools.setPlaceholder(placeholder!),\n        { immediate: true }\n      )\n\n      // watch prop.style\n      watch(\n        () => config.value.style,\n        (style) => editorTools.setStyle(style),\n        { immediate: true }\n      )\n\n      // immediate autofocus\n      if (config.value.autofocus) {\n        editorTools.focus()\n      }\n\n      context.emit(EventKey.Ready, {\n        state: state.value!,\n        view: view.value!,\n        container: container.value!\n      })\n    })\n\n    onBeforeUnmount(() => {\n      if (config.value.autoDestroy && view.value) {\n        destroyEditorView(view.value)\n      }\n    })\n\n    return () => {\n      return h('div', {\n        class: 'v-codemirror',\n        style: { display: 'contents' },\n        ref: container\n      })\n    }\n  }\n})\n", "import type { Plugin } from 'vue'\nimport type { Props } from './props'\nimport { injectGlobalConfig } from './config'\nimport Component from './component'\n\nexport type { Props } from './props'\nexport type { Events } from './events'\nexport { DEFAULT_CONFIG } from './config'\n\nexport const Codemirror = Component\nexport const install: Plugin = (app, defaultConfig?: Props) => {\n  app.component(Component.name, Component)\n  app.component('Codemirror', Component)\n  injectGlobalConfig(app, defaultConfig)\n}\n\nexport default {\n  Codemirror,\n  install\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,gBAAgB,YAAU;AAC5B,MAAI,EAAE,MAAM,IAAI,QAAQ,OAAO,MAAM,IAAI,OAAO,MAAM,UAAU,KAAK,IAAI,GAAG,SAAS,UAAU,OAAO,OAAO,KAAK,IAAI;AACtH,SAAO,OAAO,OAAO,kBAAkB,MAAM,IAAI,OAAO,QAAQ,yBAAyB,MAAM,IAAI;AACvG;AACA,SAAS,QAAQ,GAAG,QAAQ;AACxB,SAAO,CAAC,EAAE,OAAO,SAAS,MAAM;AAC5B,QAAI,MAAM;AACN,aAAO;AACX,QAAI,KAAK,EAAE,QAAQ,KAAK;AACxB,QAAI,CAAC;AACD,aAAO;AACX,aAAS,MAAM,OAAO,EAAE,CAAC;AACzB,WAAO;AAAA,EACX;AACJ;AAOA,IAAM,oBAAiC;AAAA,EAAQ;AAAA,EAAmB;AAAA;AAA4B;AAI9F,IAAM,cAA2B;AAAA,EAAQ;AAAA,EAAmB;AAAA;AAA6B;AAIzF,IAAM,gBAA6B;AAAA,EAAQ;AAAA,EAAmB;AAAA;AAA+B;AAO7F,IAAM,qBAAkC;AAAA,EAAQ;AAAA,EAAoB;AAAA;AAA4B;AAIhG,IAAM,eAA4B;AAAA,EAAQ;AAAA,EAAoB;AAAA;AAA6B;AAI3F,IAAM,iBAA8B;AAAA,EAAQ;AAAA,EAAoB;AAAA;AAA+B;AAK/F,IAAM,2BAAwC;AAAA,EAAQ,CAAC,GAAG,MAAM,mBAAmB,GAAG,GAAG,mBAAmB,CAAC,CAAC;AAAA,EAAG;AAAA;AAA4B;AAC7I,SAAS,UAAU,OAAO,KAAK;AAC3B,MAAI,OAAO,MAAM,eAAe,iBAAiB,GAAG;AACpD,SAAO,KAAK,SAAS,KAAK,CAAC,IAAI,CAAC;AACpC;AACA,IAAM,eAAe;AAKrB,SAAS,iBAAiB,OAAO,EAAE,MAAM,MAAM,GAAG,MAAM,IAAI;AACxD,MAAI,aAAa,MAAM,SAAS,OAAO,cAAc,IAAI;AACzD,MAAI,YAAY,MAAM,SAAS,IAAI,KAAK,YAAY;AACpD,MAAI,cAAc,OAAO,KAAK,UAAU,EAAE,CAAC,EAAE,QAAQ,aAAa,OAAO,KAAK,SAAS,EAAE,CAAC,EAAE;AAC5F,MAAI,YAAY,WAAW,SAAS;AACpC,MAAI,WAAW,MAAM,YAAY,KAAK,QAAQ,SAAS,KAAK,QACxD,UAAU,MAAM,YAAY,aAAa,MAAM,MAAM,KAAK,OAAO;AACjE,WAAO;AAAA,MAAE,MAAM,EAAE,KAAK,OAAO,aAAa,QAAQ,eAAe,EAAE;AAAA,MAC/D,OAAO,EAAE,KAAK,KAAK,YAAY,QAAQ,cAAc,EAAE;AAAA,IAAE;AAAA,EACjE;AACA,MAAI,WAAW;AACf,MAAI,KAAK,QAAQ,IAAI,cAAc;AAC/B,gBAAY,UAAU,MAAM,SAAS,MAAM,EAAE;AAAA,EACjD,OACK;AACD,gBAAY,MAAM,SAAS,MAAM,OAAO,YAAY;AACpD,cAAU,MAAM,SAAS,KAAK,cAAc,EAAE;AAAA,EAClD;AACA,MAAI,aAAa,OAAO,KAAK,SAAS,EAAE,CAAC,EAAE,QAAQ,WAAW,OAAO,KAAK,OAAO,EAAE,CAAC,EAAE;AACtF,MAAI,SAAS,QAAQ,SAAS,WAAW,MAAM;AAC/C,MAAI,UAAU,MAAM,YAAY,aAAa,KAAK,MAAM,KAAK,QACzD,QAAQ,MAAM,QAAQ,SAAS,MAAM,MAAM,KAAK,OAAO;AACvD,WAAO;AAAA,MAAE,MAAM;AAAA,QAAE,KAAK,OAAO,aAAa,KAAK;AAAA,QACvC,QAAQ,KAAK,KAAK,UAAU,OAAO,aAAa,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,MAAE;AAAA,MAC1E,OAAO;AAAA,QAAE,KAAK,KAAK,WAAW,MAAM;AAAA,QAChC,QAAQ,KAAK,KAAK,QAAQ,OAAO,SAAS,CAAC,CAAC,IAAI,IAAI;AAAA,MAAE;AAAA,IAAE;AAAA,EACpE;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,OAAO;AAC/B,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,MAAM,UAAU,QAAQ;AAClC,QAAI,WAAW,MAAM,IAAI,OAAO,EAAE,IAAI;AACtC,QAAI,SAAS,EAAE,MAAM,SAAS,KAAK,WAAW,MAAM,IAAI,OAAO,EAAE,EAAE;AACnE,QAAI,OAAO,OAAO,SAAS;AAC3B,QAAI,QAAQ,KAAK,OAAO,IAAI,EAAE,KAAK,SAAS;AACxC,aAAO,IAAI,EAAE,KAAK,OAAO;AAAA;AAEzB,aAAO,KAAK,EAAE,MAAM,SAAS,OAAO,OAAO,KAAK,SAAS,IAAI,EAAE,CAAC,EAAE,QAAQ,IAAI,OAAO,GAAG,CAAC;AAAA,EACjG;AACA,SAAO;AACX;AAGA,SAAS,mBAAmB,QAAQ,OAAO,SAAS,MAAM,UAAU,QAAQ;AACxE,MAAI,SAAS,OAAO,IAAI,OAAK,UAAU,OAAO,EAAE,IAAI,EAAE,KAAK;AAC3D,MAAI,CAAC,OAAO,MAAM,OAAK,CAAC;AACpB,WAAO;AACX,MAAI,WAAW,OAAO,IAAI,CAAC,GAAG,MAAM,iBAAiB,OAAO,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC;AACpF,MAAI,UAAU,KAAmC,CAAC,SAAS,MAAM,OAAK,CAAC,GAAG;AACtE,WAAO,EAAE,SAAS,MAAM,QAAQ,OAAO,IAAI,CAAC,OAAO,MAAM;AACjD,UAAI,SAAS,CAAC;AACV,eAAO,CAAC;AACZ,aAAO,CAAC,EAAE,MAAM,MAAM,MAAM,QAAQ,OAAO,CAAC,EAAE,OAAO,IAAI,GAAG,EAAE,MAAM,MAAM,IAAI,QAAQ,MAAM,OAAO,CAAC,EAAE,MAAM,CAAC;AAAA,IACjH,CAAC,CAAC,EAAE;AAAA,EACZ,WACS,UAAU,KAAiC,SAAS,KAAK,OAAK,CAAC,GAAG;AACvE,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,GAAG,SAAS,IAAI,SAAS,QAAQ;AAC1C,UAAI,UAAU,SAAS,CAAC,GAAG;AACvB,YAAI,QAAQ,OAAO,CAAC,GAAG,EAAE,MAAM,MAAM,IAAI;AACzC,gBAAQ,KAAK,EAAE,MAAM,KAAK,MAAM,MAAM,KAAK,QAAQ,IAAI,KAAK,MAAM,KAAK,OAAO,GAAG,EAAE,MAAM,MAAM,MAAM,MAAM,QAAQ,IAAI,MAAM,MAAM,MAAM,MAAM,OAAO,CAAC;AAAA,MAC3J;AACJ,WAAO,EAAE,QAAQ;AAAA,EACrB;AACA,SAAO;AACX;AAEA,SAAS,kBAAkB,QAAQ,OAAO,SAAS,MAAM,UAAU,QAAQ;AACvE,MAAI,QAAQ,CAAC;AACb,MAAI,WAAW;AACf,WAAS,EAAE,MAAM,GAAG,KAAK,QAAQ;AAC7B,QAAI,SAAS,MAAM,QAAQ,YAAY;AACvC,QAAI,QAAQ,UAAU,OAAO,IAAI,EAAE;AACnC,QAAI,CAAC;AACD;AACJ,aAAS,MAAM,MAAM,OAAO,MAAK;AAC7B,UAAI,OAAO,MAAM,IAAI,OAAO,GAAG;AAC/B,UAAI,KAAK,OAAO,aAAa,QAAQ,MAAM,KAAK,KAAK,OAAO;AACxD,mBAAW,KAAK;AAChB,YAAI,SAAS,OAAO,KAAK,KAAK,IAAI,EAAE,CAAC,EAAE;AACvC,YAAIA,SAAQ,UAAU,KAAK;AAC3B,YAAI,UAAU,KAAK,KAAK,MAAM,QAAQ,SAAS,MAAM,MAAM,KAAK,QAAQ,SAAS;AACjF,YAAI,SAAS,KAAK,KAAK,UAAU,SAAS;AACtC,sBAAY;AAChB,cAAM,KAAK,EAAE,MAAM,SAAS,OAAO,QAAQ,OAAAA,QAAO,QAAQ,MAAM,CAAC;AAAA,MACrE;AACA,YAAM,KAAK,KAAK;AAAA,IACpB;AACA,QAAI,YAAY;AACZ,eAAS,IAAI,QAAQ,IAAI,MAAM,QAAQ;AACnC,YAAI,MAAM,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,KAAK,KAAK;AACrC,gBAAM,CAAC,EAAE,SAAS;AAAA;AAC9B,QAAI,MAAM,UAAU,SAAS;AACzB,YAAM,MAAM,EAAE,SAAS;AAAA,EAC/B;AACA,MAAI,UAAU,KAAmC,MAAM,KAAK,OAAK,EAAE,UAAU,MAAM,CAAC,EAAE,SAAS,EAAE,OAAO,GAAG;AACvG,QAAI,UAAU,CAAC;AACf,aAAS,EAAE,MAAM,OAAO,QAAQ,OAAAA,QAAO,OAAO,KAAK;AAC/C,UAAI,UAAU,CAACA;AACX,gBAAQ,KAAK,EAAE,MAAM,KAAK,OAAO,QAAQ,QAAQ,QAAQ,IAAI,CAAC;AACtE,QAAI,YAAY,MAAM,QAAQ,OAAO;AACrC,WAAO,EAAE,SAAS,WAAW,WAAW,MAAM,UAAU,IAAI,WAAW,CAAC,EAAE;AAAA,EAC9E,WACS,UAAU,KAAiC,MAAM,KAAK,OAAK,EAAE,WAAW,CAAC,GAAG;AACjF,QAAI,UAAU,CAAC;AACf,aAAS,EAAE,MAAM,SAAS,MAAM,KAAK;AACjC,UAAI,WAAW,GAAG;AACd,YAAI,OAAO,KAAK,OAAO,SAAS,KAAK,OAAO,MAAM;AAClD,YAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK;AAC7B;AACJ,gBAAQ,KAAK,EAAE,MAAM,GAAG,CAAC;AAAA,MAC7B;AACJ,WAAO,EAAE,QAAQ;AAAA,EACrB;AACA,SAAO;AACX;AAEA,IAAM,cAA2B,WAAW,OAAO;AAQnD,IAAM,iBAA8B,WAAW,OAAO;AAQtD,IAAM,kBAA+B,MAAM,OAAO;AAClD,IAAM,gBAA6B,MAAM,OAAO;AAAA,EAC5C,QAAQ,SAAS;AACb,WAAO,cAAc,SAAS;AAAA,MAC1B,UAAU;AAAA,MACV,eAAe;AAAA,MACf,aAAa,CAAC,IAAIC,gBAAeA;AAAA,IACrC,GAAG;AAAA,MACC,UAAU,KAAK;AAAA,MACf,eAAe,KAAK;AAAA,MACpB,aAAa,CAAC,GAAG,MAAM,CAAC,IAAI,QAAQ,EAAE,IAAI,GAAG,KAAK,EAAE,IAAI,GAAG;AAAA,IAC/D,CAAC;AAAA,EACL;AACJ,CAAC;AACD,IAAM,gBAA6B,WAAW,OAAO;AAAA,EACjD,SAAS;AACL,WAAO,aAAa;AAAA,EACxB;AAAA,EACA,OAAO,OAAO,IAAI;AACd,QAAI,SAAS,GAAG,MAAM,MAAM,aAAa;AACzC,QAAI,WAAW,GAAG,WAAW,WAAW;AACxC,QAAI,UAAU;AACV,UAAI,OAAO,UAAU,gBAAgB,IAAI,SAAS,SAAS,GAAG,OAAO,SAAS;AAC9E,UAAI,QAAQ,QAAQ,IAA0B,MAAM,SAAS,MAAM;AACnE,UAAI;AACA,gBAAQ,aAAa,OAAO,MAAM,QAAQ,OAAO,UAAU,IAAI;AAAA;AAE/D,gBAAQ,aAAa,OAAO,GAAG,WAAW,SAAS;AACvD,aAAO,IAAI,aAAa,QAAQ,IAA0B,SAAS,OAAO,OAAO,QAAQ,IAA0B,QAAQ,SAAS,IAAI;AAAA,IAC5I;AACA,QAAI,UAAU,GAAG,WAAW,cAAc;AAC1C,QAAI,WAAW,UAAU,WAAW;AAChC,cAAQ,MAAM,QAAQ;AAC1B,QAAI,GAAG,WAAW,YAAY,YAAY,MAAM;AAC5C,aAAO,CAAC,GAAG,QAAQ,QAAQ,MAAM,WAAW,GAAG,QAAQ,IAAI,IAAI;AACnE,QAAI,QAAQ,UAAU,gBAAgB,EAAE;AACxC,QAAI,OAAO,GAAG,WAAW,YAAY,IAAI,GAAG,YAAY,GAAG,WAAW,YAAY,SAAS;AAC3F,QAAI;AACA,cAAQ,MAAM,WAAW,OAAO,MAAM,WAAW,QAAQ,EAAE;AAAA,aACtD,GAAG;AACR,cAAQ,MAAM,aAAa,GAAG,WAAW,WAAW,MAAM,WAAW,OAAO,aAAa;AAC7F,QAAI,WAAW,UAAU,WAAW;AAChC,cAAQ,MAAM,QAAQ;AAC1B,WAAO;AAAA,EACX;AAAA,EACA,OAAO,OAAO;AACV,WAAO,EAAE,MAAM,MAAM,KAAK,IAAI,OAAK,EAAE,OAAO,CAAC,GAAG,QAAQ,MAAM,OAAO,IAAI,OAAK,EAAE,OAAO,CAAC,EAAE;AAAA,EAC9F;AAAA,EACA,SAAS,MAAM;AACX,WAAO,IAAI,aAAa,KAAK,KAAK,IAAI,UAAU,QAAQ,GAAG,KAAK,OAAO,IAAI,UAAU,QAAQ,CAAC;AAAA,EAClG;AACJ,CAAC;AAID,SAAS,QAAQ,SAAS,CAAC,GAAG;AAC1B,SAAO;AAAA,IACH;AAAA,IACA,cAAc,GAAG,MAAM;AAAA,IACvB,WAAW,iBAAiB;AAAA,MACxB,YAAY,GAAG,MAAM;AACjB,YAAIC,WAAU,EAAE,aAAa,gBAAgB,OAAO,EAAE,aAAa,gBAAgB,OAAO;AAC1F,YAAI,CAACA;AACD,iBAAO;AACX,UAAE,eAAe;AACjB,eAAOA,SAAQ,IAAI;AAAA,MACvB;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AASA,SAAS,IAAI,MAAM,WAAW;AAC1B,SAAO,SAAU,EAAE,OAAO,SAAS,GAAG;AAClC,QAAI,CAAC,aAAa,MAAM;AACpB,aAAO;AACX,QAAI,eAAe,MAAM,MAAM,eAAe,KAAK;AACnD,QAAI,CAAC;AACD,aAAO;AACX,QAAI,KAAK,aAAa,IAAI,MAAM,OAAO,SAAS;AAChD,QAAI,CAAC;AACD,aAAO;AACX,aAAS,EAAE;AACX,WAAO;AAAA,EACX;AACJ;AAKA,IAAM,OAAoB,IAAI,GAAyB,KAAK;AAK5D,IAAM,OAAoB,IAAI,GAA2B,KAAK;AAI9D,IAAM,gBAA6B,IAAI,GAAyB,IAAI;AAIpE,IAAM,gBAA6B,IAAI,GAA2B,IAAI;AACtE,SAAS,MAAM,MAAM;AACjB,SAAO,SAAU,OAAO;AACpB,QAAI,YAAY,MAAM,MAAM,eAAe,KAAK;AAChD,QAAI,CAAC;AACD,aAAO;AACX,QAAI,SAAS,QAAQ,IAA0B,UAAU,OAAO,UAAU;AAC1E,WAAO,OAAO,UAAU,OAAO,UAAU,CAAC,OAAO,CAAC,EAAE,UAAU,IAAI;AAAA,EACtE;AACJ;AAIA,IAAM,YAAyB;AAAA,EAAM;AAAA;AAAuB;AAI5D,IAAM,YAAyB;AAAA,EAAM;AAAA;AAAyB;AAG9D,IAAM,YAAN,MAAM,WAAU;AAAA,EACZ,YAMA,SAEA,SAGA,QAEA,gBAGA,iBAAiB;AACb,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,kBAAkB;AAAA,EAC3B;AAAA,EACA,YAAY,OAAO;AACf,WAAO,IAAI,WAAU,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,gBAAgB,KAAK;AAAA,EAC5F;AAAA,EACA,SAAS;AACL,QAAI,IAAI,IAAI;AACZ,WAAO;AAAA,MACH,UAAU,KAAK,KAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAAA,MAC5E,SAAS,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAAA,MAC1E,iBAAiB,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAAA,MAC1F,iBAAiB,KAAK,gBAAgB,IAAI,OAAK,EAAE,OAAO,CAAC;AAAA,IAC7D;AAAA,EACJ;AAAA,EACA,OAAO,SAAS,MAAM;AAClB,WAAO,IAAI,WAAU,KAAK,WAAW,UAAU,SAAS,KAAK,OAAO,GAAG,CAAC,GAAG,KAAK,UAAU,WAAW,SAAS,KAAK,MAAM,GAAG,KAAK,kBAAkB,gBAAgB,SAAS,KAAK,cAAc,GAAG,KAAK,gBAAgB,IAAI,gBAAgB,QAAQ,CAAC;AAAA,EACxP;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,gBAAgB,IAAI,WAAW;AAClC,QAAI,UAAU;AACd,aAAS,UAAU,GAAG,WAAW,MAAM,eAAe,GAAG;AACrD,UAAI,SAAS,OAAO,EAAE;AACtB,UAAI,OAAO;AACP,kBAAU,QAAQ,OAAO,MAAM;AAAA,IACvC;AACA,QAAI,CAAC,QAAQ,UAAU,GAAG,QAAQ;AAC9B,aAAO;AACX,WAAO,IAAI,WAAU,GAAG,QAAQ,OAAO,GAAG,WAAW,GAAG,GAAG,SAAS,QAAW,aAAa,GAAG,WAAW,WAAW,IAAI;AAAA,EAC7H;AAAA,EACA,OAAO,UAAU,YAAY;AACzB,WAAO,IAAI,WAAU,QAAW,MAAM,QAAW,QAAW,UAAU;AAAA,EAC1E;AACJ;AACA,SAAS,aAAa,QAAQ,IAAI,QAAQ,UAAU;AAChD,MAAI,QAAQ,KAAK,IAAI,SAAS,KAAK,KAAK,SAAS,IAAI;AACrD,MAAI,YAAY,OAAO,MAAM,OAAO,EAAE;AACtC,YAAU,KAAK,QAAQ;AACvB,SAAO;AACX;AACA,SAAS,WAAW,GAAG,GAAG;AACtB,MAAI,SAAS,CAAC,GAAGC,cAAa;AAC9B,IAAE,kBAAkB,CAAC,GAAG,MAAM,OAAO,KAAK,GAAG,CAAC,CAAC;AAC/C,IAAE,kBAAkB,CAAC,IAAI,IAAI,GAAG,MAAM;AAClC,aAAS,IAAI,GAAG,IAAI,OAAO,UAAS;AAChC,UAAI,OAAO,OAAO,GAAG,GAAG,KAAK,OAAO,GAAG;AACvC,UAAI,KAAK,QAAQ,KAAK;AAClB,QAAAA,cAAa;AAAA,IACrB;AAAA,EACJ,CAAC;AACD,SAAOA;AACX;AACA,SAAS,iBAAiB,GAAG,GAAG;AAC5B,SAAO,EAAE,OAAO,UAAU,EAAE,OAAO,UAC/B,EAAE,OAAO,OAAO,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,WAAW;AAC3E;AACA,SAAS,KAAK,GAAG,GAAG;AAChB,SAAO,CAAC,EAAE,SAAS,IAAI,CAAC,EAAE,SAAS,IAAI,EAAE,OAAO,CAAC;AACrD;AACA,IAAM,OAAO,CAAC;AACd,IAAM,wBAAwB;AAC9B,SAAS,aAAa,QAAQ,WAAW;AACrC,MAAI,CAAC,OAAO,QAAQ;AAChB,WAAO,CAAC,UAAU,UAAU,CAAC,SAAS,CAAC,CAAC;AAAA,EAC5C,OACK;AACD,QAAI,YAAY,OAAO,OAAO,SAAS,CAAC;AACxC,QAAI,OAAO,UAAU,gBAAgB,MAAM,KAAK,IAAI,GAAG,UAAU,gBAAgB,SAAS,qBAAqB,CAAC;AAChH,QAAI,KAAK,UAAU,KAAK,KAAK,SAAS,CAAC,EAAE,GAAG,SAAS;AACjD,aAAO;AACX,SAAK,KAAK,SAAS;AACnB,WAAO,aAAa,QAAQ,OAAO,SAAS,GAAG,KAAK,UAAU,YAAY,IAAI,CAAC;AAAA,EACnF;AACJ;AAEA,SAAS,aAAa,QAAQ;AAC1B,MAAI,OAAO,OAAO,OAAO,SAAS,CAAC;AACnC,MAAI,YAAY,OAAO,MAAM;AAC7B,YAAU,OAAO,SAAS,CAAC,IAAI,KAAK,YAAY,KAAK,gBAAgB,MAAM,GAAG,KAAK,gBAAgB,SAAS,CAAC,CAAC;AAC9G,SAAO;AACX;AAIA,SAAS,mBAAmB,QAAQ,SAAS;AACzC,MAAI,CAAC,OAAO;AACR,WAAO;AACX,MAAI,SAAS,OAAO,QAAQ,aAAa;AACzC,SAAO,QAAQ;AACX,QAAI,QAAQ,SAAS,OAAO,SAAS,CAAC,GAAG,SAAS,UAAU;AAC5D,QAAI,MAAM,WAAW,CAAC,MAAM,QAAQ,SAAS,MAAM,QAAQ,QAAQ;AAC/D,UAAI,SAAS,OAAO,MAAM,GAAG,MAAM;AACnC,aAAO,SAAS,CAAC,IAAI;AACrB,aAAO;AAAA,IACX,OACK;AACD,gBAAU,MAAM;AAChB;AACA,mBAAa,MAAM;AAAA,IACvB;AAAA,EACJ;AACA,SAAO,WAAW,SAAS,CAAC,UAAU,UAAU,UAAU,CAAC,IAAI;AACnE;AACA,SAAS,SAAS,OAAO,SAAS,iBAAiB;AAC/C,MAAI,aAAa,KAAK,MAAM,gBAAgB,SAAS,MAAM,gBAAgB,IAAI,OAAK,EAAE,IAAI,OAAO,CAAC,IAAI,MAAM,eAAe;AAE3H,MAAI,CAAC,MAAM;AACP,WAAO,UAAU,UAAU,UAAU;AACzC,MAAI,gBAAgB,MAAM,QAAQ,IAAI,OAAO,GAAG,SAAS,QAAQ,QAAQ,MAAM,SAAS,IAAI;AAC5F,MAAI,cAAc,MAAM,SAAS,MAAM,OAAO,YAAY,MAAM,IAAI;AACpE,SAAO,IAAI,UAAU,eAAe,YAAY,WAAW,MAAM,SAAS,OAAO,GAAG,aAAa,MAAM,eAAe,IAAI,MAAM,GAAG,UAAU;AACjJ;AACA,IAAM,oBAAoB;AAC1B,IAAM,eAAN,MAAM,cAAa;AAAA,EACf,YAAY,MAAM,QAAQ,WAAW,GAAG,gBAAgB,QAAW;AAC/D,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,UAAU;AACN,WAAO,KAAK,WAAW,IAAI,cAAa,KAAK,MAAM,KAAK,MAAM,IAAI;AAAA,EACtE;AAAA,EACA,WAAW,OAAO,MAAM,WAAW,QAAQ,IAAI;AAC3C,QAAI,OAAO,KAAK,MAAM,YAAY,KAAK,KAAK,SAAS,CAAC;AACtD,QAAI,aAAa,UAAU,WAAW,CAAC,UAAU,QAAQ,SAAS,MAAM,YACnE,CAAC,aAAa,kBAAkB,KAAK,SAAS,OAC7C,CAAC,UAAU,gBAAgB,UACzB,OAAO,KAAK,WAAW,OAAO,iBAC9B,OAAO,YAAY,IAAI,WAAW,UAAU,SAAS,MAAM,OAAO,CAAC;AAAA,IAEnE,aAAa,uBAAuB;AACxC,aAAO,aAAa,MAAM,KAAK,SAAS,GAAG,OAAO,UAAU,IAAI,UAAU,MAAM,QAAQ,QAAQ,UAAU,OAAO,GAAG,KAAK,YAAY,WAAW,MAAM,SAAS,UAAU,OAAO,GAAG,UAAU,OAAO,GAAG,UAAU,QAAQ,UAAU,gBAAgB,IAAI,CAAC;AAAA,IAC5P,OACK;AACD,aAAO,aAAa,MAAM,KAAK,QAAQ,OAAO,UAAU,KAAK;AAAA,IACjE;AACA,WAAO,IAAI,cAAa,MAAM,MAAM,MAAM,SAAS;AAAA,EACvD;AAAA,EACA,aAAa,WAAW,MAAM,WAAW,eAAe;AACpD,QAAI,OAAO,KAAK,KAAK,SAAS,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC,EAAE,kBAAkB;AAChF,QAAI,KAAK,SAAS,KACd,OAAO,KAAK,WAAW,iBACvB,aAAa,KAAK,iBAAiB,aAAa,gBAAgB,KAAK,SAAS,KAC9E,iBAAiB,KAAK,KAAK,SAAS,CAAC,GAAG,SAAS;AACjD,aAAO;AACX,WAAO,IAAI,cAAa,aAAa,KAAK,MAAM,SAAS,GAAG,KAAK,QAAQ,MAAM,SAAS;AAAA,EAC5F;AAAA,EACA,WAAW,SAAS;AAChB,WAAO,IAAI,cAAa,mBAAmB,KAAK,MAAM,OAAO,GAAG,mBAAmB,KAAK,QAAQ,OAAO,GAAG,KAAK,UAAU,KAAK,aAAa;AAAA,EAC/I;AAAA,EACA,IAAI,MAAM,OAAO,eAAe;AAC5B,QAAI,SAAS,QAAQ,IAA0B,KAAK,OAAO,KAAK;AAChE,QAAI,OAAO,UAAU;AACjB,aAAO;AACX,QAAI,QAAQ,OAAO,OAAO,SAAS,CAAC,GAAG,YAAY,MAAM,gBAAgB,CAAC,KAAK,MAAM;AACrF,QAAI,iBAAiB,MAAM,gBAAgB,QAAQ;AAC/C,aAAO,MAAM,OAAO;AAAA,QAChB,WAAW,MAAM,gBAAgB,MAAM,gBAAgB,SAAS,CAAC;AAAA,QACjE,aAAa,YAAY,GAAG,EAAE,MAAM,MAAM,aAAa,MAAM,GAAG,UAAU,CAAC;AAAA,QAC3E,WAAW,QAAQ,IAA0B,gBAAgB;AAAA,QAC7D,gBAAgB;AAAA,MACpB,CAAC;AAAA,IACL,WACS,CAAC,MAAM,SAAS;AACrB,aAAO;AAAA,IACX,OACK;AACD,UAAI,OAAO,OAAO,UAAU,IAAI,OAAO,OAAO,MAAM,GAAG,OAAO,SAAS,CAAC;AACxE,UAAI,MAAM;AACN,eAAO,mBAAmB,MAAM,MAAM,MAAM;AAChD,aAAO,MAAM,OAAO;AAAA,QAChB,SAAS,MAAM;AAAA,QACf,WAAW,MAAM;AAAA,QACjB,SAAS,MAAM;AAAA,QACf,aAAa,YAAY,GAAG,EAAE,MAAM,MAAM,UAAU,CAAC;AAAA,QACrD,QAAQ;AAAA,QACR,WAAW,QAAQ,IAA0B,SAAS;AAAA,QACtD,gBAAgB;AAAA,MACpB,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;AACA,aAAa,QAAqB,IAAI,aAAa,MAAM,IAAI;AAS7D,IAAM,gBAAgB;AAAA,EAClB,EAAE,KAAK,SAAS,KAAK,MAAM,gBAAgB,KAAK;AAAA,EAChD,EAAE,KAAK,SAAS,KAAK,eAAe,KAAK,MAAM,gBAAgB,KAAK;AAAA,EACpE,EAAE,OAAO,gBAAgB,KAAK,MAAM,gBAAgB,KAAK;AAAA,EACzD,EAAE,KAAK,SAAS,KAAK,eAAe,gBAAgB,KAAK;AAAA,EACzD,EAAE,KAAK,SAAS,KAAK,eAAe,KAAK,eAAe,gBAAgB,KAAK;AACjF;AAEA,SAAS,UAAU,KAAK,IAAI;AACxB,SAAO,gBAAgB,OAAO,IAAI,OAAO,IAAI,EAAE,GAAG,IAAI,SAAS;AACnE;AACA,SAAS,OAAO,OAAO,WAAW;AAC9B,SAAO,MAAM,OAAO,EAAE,WAAW,gBAAgB,MAAM,WAAW,SAAS,CAAC;AAChF;AACA,SAAS,QAAQ,EAAE,OAAO,SAAS,GAAG,KAAK;AACvC,MAAI,YAAY,UAAU,MAAM,WAAW,GAAG;AAC9C,MAAI,UAAU,GAAG,MAAM,WAAW,IAAI;AAClC,WAAO;AACX,WAAS,OAAO,OAAO,SAAS,CAAC;AACjC,SAAO;AACX;AACA,SAAS,SAAS,OAAO,SAAS;AAC9B,SAAO,gBAAgB,OAAO,UAAU,MAAM,KAAK,MAAM,IAAI;AACjE;AACA,SAAS,aAAa,MAAM,SAAS;AACjC,SAAO,QAAQ,MAAM,WAAS,MAAM,QAAQ,KAAK,WAAW,OAAO,OAAO,IAAI,SAAS,OAAO,OAAO,CAAC;AAC1G;AACA,SAAS,YAAY,MAAM;AACvB,SAAO,KAAK,gBAAgB,KAAK,MAAM,UAAU,KAAK,IAAI,KAAK,UAAU;AAC7E;AAKA,IAAM,iBAAiB,UAAQ,aAAa,MAAM,CAAC,YAAY,IAAI,CAAC;AAIpE,IAAM,kBAAkB,UAAQ,aAAa,MAAM,YAAY,IAAI,CAAC;AASpE,SAAS,cAAc,MAAM,SAAS;AAClC,SAAO,QAAQ,MAAM,WAAS,MAAM,QAAQ,KAAK,YAAY,OAAO,OAAO,IAAI,SAAS,OAAO,OAAO,CAAC;AAC3G;AAKA,IAAM,kBAAkB,UAAQ,cAAc,MAAM,CAAC,YAAY,IAAI,CAAC;AAItE,IAAM,mBAAmB,UAAQ,cAAc,MAAM,YAAY,IAAI,CAAC;AAStE,IAAM,YAAY,OAAO,QAAQ,eAAe,KAAK,YACpC,IAAK,KAAK,UAAW,QAAW,EAAE,aAAa,OAAO,CAAC,IAAI;AAkE5E,SAAS,gBAAgB,OAAO,MAAM,aAAa;AAC/C,MAAI,KAAK,KAAK,KAAK,WAAW;AAC1B,WAAO;AACX,MAAI,MAAM,KAAK,KAAK,KAAK;AACzB,SAAO,QAAQ,MAAM,KAAK,YAAY,KAAK,MAAM,SAAS,KAAK,MAAM,KAAK,EAAE,CAAC,MAAM,KAAK;AAC5F;AACA,SAAS,aAAa,OAAO,OAAO,SAAS;AACzC,MAAI,MAAM,WAAW,KAAK,EAAE,aAAa,MAAM,IAAI;AACnD,MAAI,cAAc,UAAU,SAAS,WAAW,SAAS;AAGzD,WAAS,KAAK,MAAM,UAAQ;AACxB,QAAI,OAAO,UAAU,IAAI,WAAW,EAAE,IAAI,IAAI,YAAY,EAAE;AAC5D,QAAI,CAAC;AACD;AACJ,QAAI,gBAAgB,OAAO,MAAM,WAAW;AACxC,YAAM;AAAA;AAEN,WAAK,UAAU,KAAK,KAAK,KAAK;AAAA,EACtC;AACA,MAAI,UAAU,IAAI,KAAK,KAAK,WAAW,GAAG,OAAO;AACjD,MAAI,YAAY,QAAQ,UAAU,cAAc,OAAO,IAAI,MAAM,CAAC,IAAI,cAAc,OAAO,IAAI,IAAI,EAAE,MAAM,MAAM;AAC7G,aAAS,UAAU,MAAM,IAAI,KAAK,MAAM,IAAI;AAAA;AAE5C,aAAS,UAAU,IAAI,KAAK,IAAI;AACpC,SAAO,gBAAgB,OAAO,QAAQ,UAAU,KAAK,CAAC;AAC1D;AAIA,IAAM,mBAAmB,UAAQ,QAAQ,MAAM,WAAS,aAAa,KAAK,OAAO,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;AAI3G,IAAM,oBAAoB,UAAQ,QAAQ,MAAM,WAAS,aAAa,KAAK,OAAO,OAAO,YAAY,IAAI,CAAC,CAAC;AAC3G,SAAS,aAAa,MAAM,SAAS;AACjC,SAAO,QAAQ,MAAM,WAAS;AAC1B,QAAI,CAAC,MAAM;AACP,aAAO,SAAS,OAAO,OAAO;AAClC,QAAI,QAAQ,KAAK,eAAe,OAAO,OAAO;AAC9C,WAAO,MAAM,QAAQ,MAAM,OAAO,QAAQ,KAAK,mBAAmB,OAAO,OAAO;AAAA,EACpF,CAAC;AACL;AAIA,IAAM,eAAe,UAAQ,aAAa,MAAM,KAAK;AAIrD,IAAM,iBAAiB,UAAQ,aAAa,MAAM,IAAI;AACtD,SAAS,SAAS,MAAM;AACpB,MAAI,aAAa,KAAK,UAAU,eAAe,KAAK,UAAU,eAAe;AAC7E,MAAI,YAAY,GAAG,eAAe,GAAG;AACrC,MAAI,YAAY;AACZ,aAAS,UAAU,KAAK,MAAM,MAAM,WAAW,aAAa,GAAG;AAC3D,UAAI,UAAU,OAAO,IAAI;AACzB,UAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAC1D,oBAAY,KAAK,IAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK,SAAS;AACjG,UAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAC1D,uBAAe,KAAK,IAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAQ,YAAY;AAAA,IAC9G;AACA,aAAS,KAAK,UAAU,eAAe,YAAY;AAAA,EACvD,OACK;AACD,cAAU,KAAK,IAAI,cAAc,eAAe,QAAQ;AAAA,EAC5D;AACA,SAAO;AAAA,IAAE;AAAA,IAAW;AAAA,IAAc;AAAA,IAC9B,QAAQ,KAAK,IAAI,KAAK,mBAAmB,SAAS,CAAC;AAAA,EAAE;AAC7D;AACA,SAAS,aAAa,MAAM,SAAS;AACjC,MAAI,OAAO,SAAS,IAAI;AACxB,MAAI,EAAE,MAAM,IAAI,MAAM,YAAY,UAAU,MAAM,WAAW,WAAS;AAClE,WAAO,MAAM,QAAQ,KAAK,eAAe,OAAO,SAAS,KAAK,MAAM,IAC9D,SAAS,OAAO,OAAO;AAAA,EACjC,CAAC;AACD,MAAI,UAAU,GAAG,MAAM,SAAS;AAC5B,WAAO;AACX,MAAI;AACJ,MAAI,KAAK,YAAY;AACjB,QAAI,WAAW,KAAK,YAAY,MAAM,UAAU,KAAK,IAAI;AACzD,QAAI,aAAa,KAAK,UAAU,sBAAsB;AACtD,QAAI,YAAY,WAAW,MAAM,KAAK,WAAW,eAAe,WAAW,SAAS,KAAK;AACzF,QAAI,YAAY,SAAS,MAAM,aAAa,SAAS,SAAS;AAC1D,eAAS,WAAW,eAAe,UAAU,KAAK,MAAM,EAAE,GAAG,SAAS,SAAS,SAAS,MAAM,UAAU,CAAC;AAAA,EACjH;AACA,OAAK,SAAS,OAAO,OAAO,SAAS,GAAG,EAAE,SAAS,OAAO,CAAC;AAC3D,SAAO;AACX;AAIA,IAAM,eAAe,UAAQ,aAAa,MAAM,KAAK;AAIrD,IAAM,iBAAiB,UAAQ,aAAa,MAAM,IAAI;AACtD,SAAS,mBAAmB,MAAM,OAAO,SAAS;AAC9C,MAAI,OAAO,KAAK,YAAY,MAAM,IAAI,GAAG,QAAQ,KAAK,mBAAmB,OAAO,OAAO;AACvF,MAAI,MAAM,QAAQ,MAAM,QAAQ,MAAM,SAAS,UAAU,KAAK,KAAK,KAAK;AACpE,YAAQ,KAAK,mBAAmB,OAAO,SAAS,KAAK;AACzD,MAAI,CAAC,WAAW,MAAM,QAAQ,KAAK,QAAQ,KAAK,QAAQ;AACpD,QAAI,QAAQ,OAAO,KAAK,KAAK,MAAM,SAAS,KAAK,MAAM,KAAK,IAAI,KAAK,OAAO,KAAK,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;AAC/F,QAAI,SAAS,MAAM,QAAQ,KAAK,OAAO;AACnC,cAAQ,gBAAgB,OAAO,KAAK,OAAO,KAAK;AAAA,EACxD;AACA,SAAO;AACX;AAKA,IAAM,4BAA4B,UAAQ,QAAQ,MAAM,WAAS,mBAAmB,MAAM,OAAO,IAAI,CAAC;AAOtG,IAAM,6BAA6B,UAAQ,QAAQ,MAAM,WAAS,mBAAmB,MAAM,OAAO,KAAK,CAAC;AAIxG,IAAM,yBAAyB,UAAQ,QAAQ,MAAM,WAAS,mBAAmB,MAAM,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;AAIjH,IAAM,0BAA0B,UAAQ,QAAQ,MAAM,WAAS,mBAAmB,MAAM,OAAO,YAAY,IAAI,CAAC,CAAC;AAIjH,IAAM,kBAAkB,UAAQ,QAAQ,MAAM,WAAS,gBAAgB,OAAO,KAAK,YAAY,MAAM,IAAI,EAAE,MAAM,CAAC,CAAC;AAInH,IAAM,gBAAgB,UAAQ,QAAQ,MAAM,WAAS,gBAAgB,OAAO,KAAK,YAAY,MAAM,IAAI,EAAE,IAAI,EAAE,CAAC;AAChH,SAAS,kBAAkB,OAAO,UAAU,QAAQ;AAChD,MAAI,QAAQ,OAAO,YAAY,UAAU,MAAM,WAAW,WAAS;AAC/D,QAAI,WAAW,cAAc,OAAO,MAAM,MAAM,EAAE,KAC3C,cAAc,OAAO,MAAM,MAAM,CAAC,KACjC,MAAM,OAAO,KAAK,cAAc,OAAO,MAAM,OAAO,GAAG,CAAC,KACxD,MAAM,OAAO,MAAM,IAAI,UAAU,cAAc,OAAO,MAAM,OAAO,GAAG,EAAE;AAChF,QAAI,CAAC,YAAY,CAAC,SAAS;AACvB,aAAO;AACX,YAAQ;AACR,QAAI,OAAO,SAAS,MAAM,QAAQ,MAAM,OAAO,SAAS,IAAI,KAAK,SAAS,IAAI;AAC9E,WAAO,SAAS,gBAAgB,MAAM,MAAM,QAAQ,IAAI,IAAI,gBAAgB,OAAO,IAAI;AAAA,EAC3F,CAAC;AACD,MAAI,CAAC;AACD,WAAO;AACX,WAAS,OAAO,OAAO,SAAS,CAAC;AACjC,SAAO;AACX;AAKA,IAAM,wBAAwB,CAAC,EAAE,OAAO,SAAS,MAAM,kBAAkB,OAAO,UAAU,KAAK;AAM/F,SAAS,UAAU,MAAM,KAAK;AAC1B,MAAI,YAAY,UAAU,KAAK,MAAM,WAAW,WAAS;AACrD,QAAI,OAAO,IAAI,KAAK;AACpB,WAAO,gBAAgB,MAAM,MAAM,QAAQ,KAAK,MAAM,KAAK,YAAY,KAAK,aAAa,MAAS;AAAA,EACtG,CAAC;AACD,MAAI,UAAU,GAAG,KAAK,MAAM,SAAS;AACjC,WAAO;AACX,OAAK,SAAS,OAAO,KAAK,OAAO,SAAS,CAAC;AAC3C,SAAO;AACX;AACA,SAAS,aAAa,MAAM,SAAS;AACjC,SAAO,UAAU,MAAM,WAAS,KAAK,WAAW,OAAO,OAAO,CAAC;AACnE;AAKA,IAAM,iBAAiB,UAAQ,aAAa,MAAM,CAAC,YAAY,IAAI,CAAC;AAIpE,IAAM,kBAAkB,UAAQ,aAAa,MAAM,YAAY,IAAI,CAAC;AASpE,SAAS,cAAc,MAAM,SAAS;AAClC,SAAO,UAAU,MAAM,WAAS,KAAK,YAAY,OAAO,OAAO,CAAC;AACpE;AAKA,IAAM,kBAAkB,UAAQ,cAAc,MAAM,CAAC,YAAY,IAAI,CAAC;AAItE,IAAM,mBAAmB,UAAQ,cAAc,MAAM,YAAY,IAAI,CAAC;AAuBtE,IAAM,mBAAmB,UAAQ,UAAU,MAAM,WAAS,aAAa,KAAK,OAAO,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;AAI7G,IAAM,oBAAoB,UAAQ,UAAU,MAAM,WAAS,aAAa,KAAK,OAAO,OAAO,YAAY,IAAI,CAAC,CAAC;AAC7G,SAAS,aAAa,MAAM,SAAS;AACjC,SAAO,UAAU,MAAM,WAAS,KAAK,eAAe,OAAO,OAAO,CAAC;AACvE;AAIA,IAAM,eAAe,UAAQ,aAAa,MAAM,KAAK;AAIrD,IAAM,iBAAiB,UAAQ,aAAa,MAAM,IAAI;AACtD,SAAS,aAAa,MAAM,SAAS;AACjC,SAAO,UAAU,MAAM,WAAS,KAAK,eAAe,OAAO,SAAS,SAAS,IAAI,EAAE,MAAM,CAAC;AAC9F;AAIA,IAAM,eAAe,UAAQ,aAAa,MAAM,KAAK;AAIrD,IAAM,iBAAiB,UAAQ,aAAa,MAAM,IAAI;AAItD,IAAM,4BAA4B,UAAQ,UAAU,MAAM,WAAS,mBAAmB,MAAM,OAAO,IAAI,CAAC;AAIxG,IAAM,6BAA6B,UAAQ,UAAU,MAAM,WAAS,mBAAmB,MAAM,OAAO,KAAK,CAAC;AAI1G,IAAM,yBAAyB,UAAQ,UAAU,MAAM,WAAS,mBAAmB,MAAM,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;AAInH,IAAM,0BAA0B,UAAQ,UAAU,MAAM,WAAS,mBAAmB,MAAM,OAAO,YAAY,IAAI,CAAC,CAAC;AAInH,IAAM,kBAAkB,UAAQ,UAAU,MAAM,WAAS,gBAAgB,OAAO,KAAK,YAAY,MAAM,IAAI,EAAE,IAAI,CAAC;AAIlH,IAAM,gBAAgB,UAAQ,UAAU,MAAM,WAAS,gBAAgB,OAAO,KAAK,YAAY,MAAM,IAAI,EAAE,EAAE,CAAC;AAI9G,IAAM,iBAAiB,CAAC,EAAE,OAAO,SAAS,MAAM;AAC5C,WAAS,OAAO,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;AACrC,SAAO;AACX;AAIA,IAAM,eAAe,CAAC,EAAE,OAAO,SAAS,MAAM;AAC1C,WAAS,OAAO,OAAO,EAAE,QAAQ,MAAM,IAAI,OAAO,CAAC,CAAC;AACpD,SAAO;AACX;AAIA,IAAM,iBAAiB,CAAC,EAAE,OAAO,SAAS,MAAM;AAC5C,WAAS,OAAO,OAAO,EAAE,QAAQ,MAAM,UAAU,KAAK,QAAQ,MAAM,EAAE,CAAC,CAAC;AACxE,SAAO;AACX;AAIA,IAAM,eAAe,CAAC,EAAE,OAAO,SAAS,MAAM;AAC1C,WAAS,OAAO,OAAO,EAAE,QAAQ,MAAM,UAAU,KAAK,QAAQ,MAAM,MAAM,IAAI,OAAO,CAAC,CAAC;AACvF,SAAO;AACX;AAIA,IAAM,YAAY,CAAC,EAAE,OAAO,SAAS,MAAM;AACvC,WAAS,MAAM,OAAO,EAAE,WAAW,EAAE,QAAQ,GAAG,MAAM,MAAM,IAAI,OAAO,GAAG,WAAW,SAAS,CAAC,CAAC;AAChG,SAAO;AACX;AAIA,IAAM,aAAa,CAAC,EAAE,OAAO,SAAS,MAAM;AACxC,MAAI,SAAS,mBAAmB,KAAK,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,MAAM,gBAAgB,MAAM,MAAM,KAAK,IAAI,KAAK,GAAG,MAAM,IAAI,MAAM,CAAC,CAAC;AAC5H,WAAS,MAAM,OAAO,EAAE,WAAW,gBAAgB,OAAO,MAAM,GAAG,WAAW,SAAS,CAAC,CAAC;AACzF,SAAO;AACX;AAOA,IAAM,qBAAqB,CAAC,EAAE,OAAO,SAAS,MAAM;AAChD,MAAI,YAAY,UAAU,MAAM,WAAW,WAAS;AAChD,QAAI;AACJ,QAAI,QAAQ,WAAW,KAAK,EAAE,aAAa,MAAM,MAAM,CAAC;AACxD,aAAS,MAAM,OAAO,KAAK,MAAM,IAAI,MAAM;AACvC,UAAI,EAAE,KAAK,IAAI;AACf,WAAM,KAAK,OAAO,MAAM,QAAQ,KAAK,MAAM,MAAM,MAC5C,KAAK,KAAK,MAAM,MAAM,KAAK,QAAQ,MAAM,WACxC,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC5D,eAAO,gBAAgB,MAAM,KAAK,IAAI,KAAK,IAAI;AAAA,IACvD;AACA,WAAO;AAAA,EACX,CAAC;AACD,WAAS,OAAO,OAAO,SAAS,CAAC;AACjC,SAAO;AACX;AAMA,IAAM,oBAAoB,CAAC,EAAE,OAAO,SAAS,MAAM;AAC/C,MAAI,MAAM,MAAM,WAAW,YAAY;AACvC,MAAI,IAAI,OAAO,SAAS;AACpB,gBAAY,gBAAgB,OAAO,CAAC,IAAI,IAAI,CAAC;AAAA,WACxC,CAAC,IAAI,KAAK;AACf,gBAAY,gBAAgB,OAAO,CAAC,gBAAgB,OAAO,IAAI,KAAK,IAAI,CAAC,CAAC;AAC9E,MAAI,CAAC;AACD,WAAO;AACX,WAAS,OAAO,OAAO,SAAS,CAAC;AACjC,SAAO;AACX;AACA,SAAS,SAAS,QAAQ,IAAI;AAC1B,MAAI,OAAO,MAAM;AACb,WAAO;AACX,MAAI,QAAQ,oBAAoB,EAAE,MAAM,IAAI;AAC5C,MAAI,UAAU,MAAM,cAAc,WAAS;AACvC,QAAI,EAAE,MAAM,GAAG,IAAI;AACnB,QAAI,QAAQ,IAAI;AACZ,UAAI,UAAU,GAAG,KAAK;AACtB,UAAI,UAAU,MAAM;AAChB,gBAAQ;AACR,kBAAU,WAAW,QAAQ,SAAS,KAAK;AAAA,MAC/C,WACS,UAAU,MAAM;AACrB,gBAAQ;AACR,kBAAU,WAAW,QAAQ,SAAS,IAAI;AAAA,MAC9C;AACA,aAAO,KAAK,IAAI,MAAM,OAAO;AAC7B,WAAK,KAAK,IAAI,IAAI,OAAO;AAAA,IAC7B,OACK;AACD,aAAO,WAAW,QAAQ,MAAM,KAAK;AACrC,WAAK,WAAW,QAAQ,IAAI,IAAI;AAAA,IACpC;AACA,WAAO,QAAQ,KAAK,EAAE,MAAM,IAAI,EAAE,SAAS,EAAE,MAAM,GAAG,GAAG,OAAO,gBAAgB,OAAO,MAAM,OAAO,MAAM,OAAO,KAAK,CAAC,EAAE;AAAA,EAC7H,CAAC;AACD,MAAI,QAAQ,QAAQ;AAChB,WAAO;AACX,SAAO,SAAS,MAAM,OAAO,SAAS;AAAA,IAClC,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,SAAS,SAAS,qBAAqB,WAAW,SAAS,GAAG,MAAM,OAAO,mBAAmB,CAAC,IAAI;AAAA,EACvG,CAAC,CAAC;AACF,SAAO;AACX;AACA,SAAS,WAAW,QAAQ,KAAK,SAAS;AACtC,MAAI,kBAAkB;AAClB,aAAS,UAAU,OAAO,MAAM,MAAM,WAAW,YAAY,EAAE,IAAI,OAAK,EAAE,MAAM,CAAC;AAC7E,aAAO,QAAQ,KAAK,KAAK,CAAC,MAAM,OAAO;AACnC,YAAI,OAAO,OAAO,KAAK;AACnB,gBAAM,UAAU,KAAK;AAAA,MAC7B,CAAC;AACT,SAAO;AACX;AACA,IAAM,eAAe,CAAC,QAAQ,SAAS,iBAAiB,SAAS,QAAQ,WAAS;AAC9E,MAAI,MAAM,MAAM,MAAM,EAAE,MAAM,IAAI,QAAQ,OAAO,MAAM,IAAI,OAAO,GAAG,GAAG,QAAQ;AAChF,MAAI,gBAAgB,CAAC,WAAW,MAAM,KAAK,QAAQ,MAAM,KAAK,OAAO,OACjE,CAAC,SAAS,KAAK,SAAS,KAAK,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,GAAG;AAC9D,QAAI,OAAO,OAAO,SAAS,CAAC,KAAK;AAC7B,aAAO,MAAM;AACjB,QAAI,MAAM,YAAY,QAAQ,MAAM,OAAO,GAAG,OAAO,MAAM,cAAc,KAAK,KAAK,cAAc,KAAK;AACtG,aAAS,IAAI,GAAG,IAAI,QAAQ,OAAO,OAAO,SAAS,IAAI,CAAC,KAAK,KAAK;AAC9D;AACJ,gBAAY;AAAA,EAChB,OACK;AACD,gBAAY,iBAAiB,KAAK,MAAM,MAAM,KAAK,MAAM,SAAS,OAAO,IAAI,KAAK;AAClF,QAAI,aAAa,OAAO,KAAK,WAAW,UAAU,MAAM,IAAI,QAAQ;AAChE,mBAAa,UAAU,IAAI;AAAA,aACtB,CAAC,WAAW,kBAAkB,KAAK,KAAK,KAAK,MAAM,YAAY,KAAK,MAAM,MAAM,KAAK,IAAI,CAAC;AAC/F,kBAAY,iBAAiB,KAAK,MAAM,YAAY,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK;AAAA,EAC5F;AACA,SAAO;AACX,CAAC;AAKD,IAAM,qBAAqB,UAAQ,aAAa,MAAM,OAAO,IAAI;AAUjE,IAAM,oBAAoB,UAAQ,aAAa,MAAM,MAAM,KAAK;AAChE,IAAM,gBAAgB,CAAC,QAAQ,YAAY,SAAS,QAAQ,WAAS;AACjE,MAAI,MAAM,MAAM,MAAM,EAAE,MAAM,IAAI,QAAQ,OAAO,MAAM,IAAI,OAAO,GAAG;AACrE,MAAI,aAAa,MAAM,gBAAgB,GAAG;AAC1C,WAAS,MAAM,UAAQ;AACnB,QAAI,QAAQ,UAAU,KAAK,KAAK,KAAK,OAAO;AACxC,UAAI,OAAO,MAAM,QAAQ,KAAK,WAAW,UAAU,MAAM,IAAI,QAAQ;AACjE,eAAO,UAAU,IAAI;AACzB;AAAA,IACJ;AACA,QAAI,OAAO,iBAAiB,KAAK,MAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK;AACxE,QAAI,WAAW,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI;AAC/F,QAAI,UAAU,WAAW,QAAQ;AACjC,QAAI,OAAO,QAAQ,WAAW;AAC1B;AACJ,QAAI,YAAY,OAAO,OAAO,MAAM;AAChC,YAAM;AACV,UAAM;AAAA,EACV;AACA,SAAO;AACX,CAAC;AAMD,IAAM,sBAAsB,YAAU,cAAc,QAAQ,KAAK;AAIjE,IAAM,qBAAqB,YAAU,cAAc,QAAQ,IAAI;AAM/D,IAAM,kBAAkB,UAAQ,SAAS,MAAM,WAAS;AACpD,MAAI,UAAU,KAAK,YAAY,MAAM,IAAI,EAAE;AAC3C,SAAO,MAAM,OAAO,UAAU,UAAU,KAAK,IAAI,KAAK,MAAM,IAAI,QAAQ,MAAM,OAAO,CAAC;AAC1F,CAAC;AAcD,IAAM,6BAA6B,UAAQ,SAAS,MAAM,WAAS;AAC/D,MAAI,YAAY,KAAK,mBAAmB,OAAO,KAAK,EAAE;AACtD,SAAO,MAAM,OAAO,YAAY,YAAY,KAAK,IAAI,GAAG,MAAM,OAAO,CAAC;AAC1E,CAAC;AAKD,IAAM,4BAA4B,UAAQ,SAAS,MAAM,WAAS;AAC9D,MAAI,YAAY,KAAK,mBAAmB,OAAO,IAAI,EAAE;AACrD,SAAO,MAAM,OAAO,YAAY,YAAY,KAAK,IAAI,KAAK,MAAM,IAAI,QAAQ,MAAM,OAAO,CAAC;AAC9F,CAAC;AAiCD,IAAM,YAAY,CAAC,EAAE,OAAO,SAAS,MAAM;AACvC,MAAI,MAAM;AACN,WAAO;AACX,MAAI,UAAU,MAAM,cAAc,WAAS;AACvC,WAAO;AAAA,MAAE,SAAS,EAAE,MAAM,MAAM,MAAM,IAAI,MAAM,IAAI,QAAQ,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE;AAAA,MAC1E,OAAO,gBAAgB,OAAO,MAAM,IAAI;AAAA,IAAE;AAAA,EAClD,CAAC;AACD,WAAS,MAAM,OAAO,SAAS,EAAE,gBAAgB,MAAM,WAAW,QAAQ,CAAC,CAAC;AAC5E,SAAO;AACX;AAIA,IAAM,iBAAiB,CAAC,EAAE,OAAO,SAAS,MAAM;AAC5C,MAAI,MAAM;AACN,WAAO;AACX,MAAI,UAAU,MAAM,cAAc,WAAS;AACvC,QAAI,CAAC,MAAM,SAAS,MAAM,QAAQ,KAAK,MAAM,QAAQ,MAAM,IAAI;AAC3D,aAAO,EAAE,MAAM;AACnB,QAAI,MAAM,MAAM,MAAM,OAAO,MAAM,IAAI,OAAO,GAAG;AACjD,QAAI,OAAO,OAAO,KAAK,OAAO,MAAM,IAAI,iBAAiB,KAAK,MAAM,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK;AACnG,QAAI,KAAK,OAAO,KAAK,KAAK,MAAM,IAAI,iBAAiB,KAAK,MAAM,MAAM,KAAK,MAAM,IAAI,IAAI,KAAK;AAC9F,WAAO;AAAA,MAAE,SAAS,EAAE,MAAM,IAAI,QAAQ,MAAM,IAAI,MAAM,KAAK,EAAE,EAAE,OAAO,MAAM,IAAI,MAAM,MAAM,GAAG,CAAC,EAAE;AAAA,MAC9F,OAAO,gBAAgB,OAAO,EAAE;AAAA,IAAE;AAAA,EAC1C,CAAC;AACD,MAAI,QAAQ,QAAQ;AAChB,WAAO;AACX,WAAS,MAAM,OAAO,SAAS,EAAE,gBAAgB,MAAM,WAAW,iBAAiB,CAAC,CAAC;AACrF,SAAO;AACX;AACA,SAAS,mBAAmB,OAAO;AAC/B,MAAI,SAAS,CAAC,GAAG,OAAO;AACxB,WAAS,SAAS,MAAM,UAAU,QAAQ;AACtC,QAAI,YAAY,MAAM,IAAI,OAAO,MAAM,IAAI,GAAG,UAAU,MAAM,IAAI,OAAO,MAAM,EAAE;AACjF,QAAI,CAAC,MAAM,SAAS,MAAM,MAAM,QAAQ;AACpC,gBAAU,MAAM,IAAI,OAAO,MAAM,KAAK,CAAC;AAC3C,QAAI,QAAQ,UAAU,QAAQ;AAC1B,UAAI,OAAO,OAAO,OAAO,SAAS,CAAC;AACnC,WAAK,KAAK,QAAQ;AAClB,WAAK,OAAO,KAAK,KAAK;AAAA,IAC1B,OACK;AACD,aAAO,KAAK,EAAE,MAAM,UAAU,MAAM,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;AAAA,IACzE;AACA,WAAO,QAAQ,SAAS;AAAA,EAC5B;AACA,SAAO;AACX;AACA,SAAS,SAAS,OAAO,UAAU,SAAS;AACxC,MAAI,MAAM;AACN,WAAO;AACX,MAAI,UAAU,CAAC,GAAG,SAAS,CAAC;AAC5B,WAAS,SAAS,mBAAmB,KAAK,GAAG;AACzC,QAAI,UAAU,MAAM,MAAM,MAAM,IAAI,SAAS,MAAM,QAAQ;AACvD;AACJ,QAAI,WAAW,MAAM,IAAI,OAAO,UAAU,MAAM,KAAK,IAAI,MAAM,OAAO,CAAC;AACvE,QAAI,OAAO,SAAS,SAAS;AAC7B,QAAI,SAAS;AACT,cAAQ,KAAK,EAAE,MAAM,MAAM,IAAI,IAAI,SAAS,GAAG,GAAG,EAAE,MAAM,MAAM,MAAM,QAAQ,SAAS,OAAO,MAAM,UAAU,CAAC;AAC/G,eAAS,KAAK,MAAM;AAChB,eAAO,KAAK,gBAAgB,MAAM,KAAK,IAAI,MAAM,IAAI,QAAQ,EAAE,SAAS,IAAI,GAAG,KAAK,IAAI,MAAM,IAAI,QAAQ,EAAE,OAAO,IAAI,CAAC,CAAC;AAAA,IACjI,OACK;AACD,cAAQ,KAAK,EAAE,MAAM,SAAS,MAAM,IAAI,MAAM,KAAK,GAAG,EAAE,MAAM,MAAM,IAAI,QAAQ,MAAM,YAAY,SAAS,KAAK,CAAC;AACjH,eAAS,KAAK,MAAM;AAChB,eAAO,KAAK,gBAAgB,MAAM,EAAE,SAAS,MAAM,EAAE,OAAO,IAAI,CAAC;AAAA,IACzE;AAAA,EACJ;AACA,MAAI,CAAC,QAAQ;AACT,WAAO;AACX,WAAS,MAAM,OAAO;AAAA,IAClB;AAAA,IACA,gBAAgB;AAAA,IAChB,WAAW,gBAAgB,OAAO,QAAQ,MAAM,UAAU,SAAS;AAAA,IACnE,WAAW;AAAA,EACf,CAAC,CAAC;AACF,SAAO;AACX;AAIA,IAAM,aAAa,CAAC,EAAE,OAAO,SAAS,MAAM,SAAS,OAAO,UAAU,KAAK;AAI3E,IAAM,eAAe,CAAC,EAAE,OAAO,SAAS,MAAM,SAAS,OAAO,UAAU,IAAI;AAC5E,SAAS,SAAS,OAAO,UAAU,SAAS;AACxC,MAAI,MAAM;AACN,WAAO;AACX,MAAI,UAAU,CAAC;AACf,WAAS,SAAS,mBAAmB,KAAK,GAAG;AACzC,QAAI;AACA,cAAQ,KAAK,EAAE,MAAM,MAAM,MAAM,QAAQ,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,EAAE,IAAI,MAAM,UAAU,CAAC;AAAA;AAElG,cAAQ,KAAK,EAAE,MAAM,MAAM,IAAI,QAAQ,MAAM,YAAY,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,EAAE,EAAE,CAAC;AAAA,EACxG;AACA,WAAS,MAAM,OAAO,EAAE,SAAS,gBAAgB,MAAM,WAAW,iBAAiB,CAAC,CAAC;AACrF,SAAO;AACX;AAIA,IAAM,aAAa,CAAC,EAAE,OAAO,SAAS,MAAM,SAAS,OAAO,UAAU,KAAK;AAI3E,IAAM,eAAe,CAAC,EAAE,OAAO,SAAS,MAAM,SAAS,OAAO,UAAU,IAAI;AAI5E,IAAM,aAAa,UAAQ;AACvB,MAAI,KAAK,MAAM;AACX,WAAO;AACX,MAAI,EAAE,MAAM,IAAI,MAAM,UAAU,MAAM,QAAQ,mBAAmB,KAAK,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,MAAM;AAC1F,QAAI,OAAO;AACP;AAAA,aACK,KAAK,MAAM,IAAI;AACpB;AACJ,WAAO,EAAE,MAAM,GAAG;AAAA,EACtB,CAAC,CAAC;AACF,MAAI,YAAY,UAAU,MAAM,WAAW,WAAS;AAChD,QAAI,OAAO;AACX,QAAI,KAAK,cAAc;AACnB,UAAI,QAAQ,KAAK,YAAY,MAAM,IAAI,GAAG,MAAM,KAAK,YAAY,MAAM,MAAM,MAAM,SAAS,CAAC;AAC7F,UAAI;AACA,eAAQ,MAAM,SAAS,KAAK,cAAe,IAAI,SAAS,KAAK,oBAAoB;AAAA,IACzF;AACA,WAAO,KAAK,eAAe,OAAO,MAAM,IAAI;AAAA,EAChD,CAAC,EAAE,IAAI,OAAO;AACd,OAAK,SAAS,EAAE,SAAS,WAAW,gBAAgB,MAAM,WAAW,cAAc,CAAC;AACpF,SAAO;AACX;AAsBA,SAAS,kBAAkB,OAAO,KAAK;AACnC,MAAI,iBAAiB,KAAK,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,CAAC;AACtD,WAAO,EAAE,MAAM,KAAK,IAAI,IAAI;AAChC,MAAI,UAAU,WAAW,KAAK,EAAE,aAAa,GAAG;AAChD,MAAI,SAAS,QAAQ,YAAY,GAAG,GAAG,QAAQ,QAAQ,WAAW,GAAG,GAAG;AACxE,MAAI,UAAU,SAAS,OAAO,MAAM,OAAO,MAAM,QAAQ,QACpD,WAAW,OAAO,KAAK,KAAK,SAAS,QAAQ,MAAM,SAAS,QAAQ,MAAM,IAAI,IAAI,MACnF,MAAM,IAAI,OAAO,OAAO,EAAE,EAAE,QAAQ,MAAM,IAAI,OAAO,MAAM,IAAI,EAAE,QACjE,CAAC,KAAK,KAAK,MAAM,SAAS,OAAO,IAAI,MAAM,IAAI,CAAC;AAChD,WAAO,EAAE,MAAM,OAAO,IAAI,IAAI,MAAM,KAAK;AAC7C,SAAO;AACX;AAQA,IAAM,yBAAsC,iBAAiB,KAAK;AAIlE,IAAM,kBAA+B,iBAAiB,IAAI;AAC1D,SAAS,iBAAiB,OAAO;AAC7B,SAAO,CAAC,EAAE,OAAO,SAAS,MAAM;AAC5B,QAAI,MAAM;AACN,aAAO;AACX,QAAI,UAAU,MAAM,cAAc,WAAS;AACvC,UAAI,EAAE,MAAM,GAAG,IAAI,OAAO,OAAO,MAAM,IAAI,OAAO,IAAI;AACtD,UAAI,UAAU,CAAC,SAAS,QAAQ,MAAM,kBAAkB,OAAO,IAAI;AACnE,UAAI;AACA,eAAO,MAAM,MAAM,KAAK,KAAK,OAAO,MAAM,IAAI,OAAO,EAAE,GAAG;AAC9D,UAAI,KAAK,IAAI,cAAc,OAAO,EAAE,eAAe,MAAM,qBAAqB,CAAC,CAAC,QAAQ,CAAC;AACzF,UAAI,SAAS,eAAe,IAAI,IAAI;AACpC,UAAI,UAAU;AACV,iBAAS,YAAY,OAAO,KAAK,MAAM,IAAI,OAAO,IAAI,EAAE,IAAI,EAAE,CAAC,GAAG,MAAM,OAAO;AACnF,aAAO,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC;AACtD;AACJ,UAAI;AACA,SAAC,EAAE,MAAM,GAAG,IAAI;AAAA,eACX,OAAO,KAAK,QAAQ,OAAO,KAAK,OAAO,OAAO,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,GAAG,IAAI,CAAC;AACtF,eAAO,KAAK;AAChB,UAAI,SAAS,CAAC,IAAI,aAAa,OAAO,MAAM,CAAC;AAC7C,UAAI;AACA,eAAO,KAAK,aAAa,OAAO,GAAG,WAAW,KAAK,MAAM,EAAE,CAAC,CAAC;AACjE,aAAO;AAAA,QAAE,SAAS,EAAE,MAAM,IAAI,QAAQ,KAAK,GAAG,MAAM,EAAE;AAAA,QAClD,OAAO,gBAAgB,OAAO,OAAO,IAAI,OAAO,CAAC,EAAE,MAAM;AAAA,MAAE;AAAA,IACnE,CAAC;AACD,aAAS,MAAM,OAAO,SAAS,EAAE,gBAAgB,MAAM,WAAW,QAAQ,CAAC,CAAC;AAC5E,WAAO;AAAA,EACX;AACJ;AACA,SAAS,qBAAqB,OAAO,GAAG;AACpC,MAAI,SAAS;AACb,SAAO,MAAM,cAAc,WAAS;AAChC,QAAI,UAAU,CAAC;AACf,aAAS,MAAM,MAAM,MAAM,OAAO,MAAM,MAAK;AACzC,UAAI,OAAO,MAAM,IAAI,OAAO,GAAG;AAC/B,UAAI,KAAK,SAAS,WAAW,MAAM,SAAS,MAAM,KAAK,KAAK,OAAO;AAC/D,UAAE,MAAM,SAAS,KAAK;AACtB,iBAAS,KAAK;AAAA,MAClB;AACA,YAAM,KAAK,KAAK;AAAA,IACpB;AACA,QAAI,YAAY,MAAM,QAAQ,OAAO;AACrC,WAAO;AAAA,MAAE;AAAA,MACL,OAAO,gBAAgB,MAAM,UAAU,OAAO,MAAM,QAAQ,CAAC,GAAG,UAAU,OAAO,MAAM,MAAM,CAAC,CAAC;AAAA,IAAE;AAAA,EACzG,CAAC;AACL;AAMA,IAAM,kBAAkB,CAAC,EAAE,OAAO,SAAS,MAAM;AAC7C,MAAI,MAAM;AACN,WAAO;AACX,MAAI,UAAU,uBAAO,OAAO,IAAI;AAChC,MAAI,UAAU,IAAI,cAAc,OAAO,EAAE,qBAAqB,WAAS;AAC/D,QAAI,QAAQ,QAAQ,KAAK;AACzB,WAAO,SAAS,OAAO,KAAK;AAAA,EAChC,EAAE,CAAC;AACP,MAAI,UAAU,qBAAqB,OAAO,CAAC,MAAMC,UAAS,UAAU;AAChE,QAAI,SAAS,eAAe,SAAS,KAAK,IAAI;AAC9C,QAAI,UAAU;AACV;AACJ,QAAI,CAAC,KAAK,KAAK,KAAK,IAAI;AACpB,eAAS;AACb,QAAI,MAAM,OAAO,KAAK,KAAK,IAAI,EAAE,CAAC;AAClC,QAAI,OAAO,aAAa,OAAO,MAAM;AACrC,QAAI,OAAO,QAAQ,MAAM,OAAO,KAAK,OAAO,IAAI,QAAQ;AACpD,cAAQ,KAAK,IAAI,IAAI;AACrB,MAAAA,SAAQ,KAAK,EAAE,MAAM,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,QAAQ,QAAQ,KAAK,CAAC;AAAA,IAC9E;AAAA,EACJ,CAAC;AACD,MAAI,CAAC,QAAQ,QAAQ;AACjB,aAAS,MAAM,OAAO,SAAS,EAAE,WAAW,SAAS,CAAC,CAAC;AAC3D,SAAO;AACX;AAKA,IAAM,aAAa,CAAC,EAAE,OAAO,SAAS,MAAM;AACxC,MAAI,MAAM;AACN,WAAO;AACX,WAAS,MAAM,OAAO,qBAAqB,OAAO,CAAC,MAAM,YAAY;AACjE,YAAQ,KAAK,EAAE,MAAM,KAAK,MAAM,QAAQ,MAAM,MAAM,UAAU,EAAE,CAAC;AAAA,EACrE,CAAC,GAAG,EAAE,WAAW,eAAe,CAAC,CAAC;AAClC,SAAO;AACX;AAKA,IAAM,aAAa,CAAC,EAAE,OAAO,SAAS,MAAM;AACxC,MAAI,MAAM;AACN,WAAO;AACX,WAAS,MAAM,OAAO,qBAAqB,OAAO,CAAC,MAAM,YAAY;AACjE,QAAI,QAAQ,OAAO,KAAK,KAAK,IAAI,EAAE,CAAC;AACpC,QAAI,CAAC;AACD;AACJ,QAAI,MAAM,YAAY,OAAO,MAAM,OAAO,GAAG,OAAO;AACpD,QAAI,SAAS,aAAa,OAAO,KAAK,IAAI,GAAG,MAAM,cAAc,KAAK,CAAC,CAAC;AACxE,WAAO,OAAO,MAAM,UAAU,OAAO,OAAO,UAAU,MAAM,WAAW,IAAI,KAAK,OAAO,WAAW,IAAI;AAClG;AACJ,YAAQ,KAAK,EAAE,MAAM,KAAK,OAAO,MAAM,IAAI,KAAK,OAAO,MAAM,QAAQ,QAAQ,OAAO,MAAM,IAAI,EAAE,CAAC;AAAA,EACrG,CAAC,GAAG,EAAE,WAAW,gBAAgB,CAAC,CAAC;AACnC,SAAO;AACX;AAQA,IAAM,qBAAqB,UAAQ;AAC/B,OAAK,gBAAgB;AACrB,SAAO;AACX;AAwCA,IAAM,mBAAmB;AAAA,EACrB,EAAE,KAAK,UAAU,KAAK,gBAAgB,OAAO,gBAAgB,gBAAgB,KAAK;AAAA,EAClF,EAAE,KAAK,UAAU,KAAK,iBAAiB,OAAO,gBAAgB;AAAA,EAC9D,EAAE,KAAK,UAAU,KAAK,cAAc,OAAO,aAAa;AAAA,EACxD,EAAE,KAAK,UAAU,KAAK,gBAAgB,OAAO,eAAe;AAAA,EAC5D,EAAE,KAAK,UAAU,KAAK,iBAAiB,OAAO,gBAAgB;AAAA,EAC9D,EAAE,KAAK,UAAU,KAAK,eAAe,OAAO,cAAc;AAAA,EAC1D,EAAE,KAAK,UAAU,KAAK,kBAAkB;AAAA,EACxC,EAAE,KAAK,UAAU,KAAK,mBAAmB;AAAA,EACzC,EAAE,KAAK,UAAU,KAAK,gBAAgB;AAAA,EACtC,EAAE,KAAK,cAAc,KAAK,oBAAoB;AAAA,EAC9C,EAAE,KAAK,UAAU,KAAK,UAAU;AAAA,EAChC,EAAE,KAAK,UAAU,KAAK,eAAe;AAAA,EACrC,EAAE,KAAK,UAAU,KAAK,eAAe;AACzC;AAkCA,IAAM,iBAA8B;AAAA,EAChC,EAAE,KAAK,aAAa,KAAK,gBAAgB,OAAO,gBAAgB,gBAAgB,KAAK;AAAA,EACrF,EAAE,KAAK,iBAAiB,KAAK,iBAAiB,KAAK,iBAAiB,OAAO,iBAAiB,gBAAgB,KAAK;AAAA,EACjH,EAAE,KAAK,iBAAiB,KAAK,wBAAwB,OAAO,wBAAwB,gBAAgB,KAAK;AAAA,EACzG,EAAE,KAAK,cAAc,KAAK,iBAAiB,OAAO,iBAAiB,gBAAgB,KAAK;AAAA,EACxF,EAAE,KAAK,kBAAkB,KAAK,kBAAkB,KAAK,kBAAkB,OAAO,kBAAkB,gBAAgB,KAAK;AAAA,EACrH,EAAE,KAAK,kBAAkB,KAAK,yBAAyB,OAAO,yBAAyB,gBAAgB,KAAK;AAAA,EAC5G,EAAE,KAAK,WAAW,KAAK,cAAc,OAAO,cAAc,gBAAgB,KAAK;AAAA,EAC/E,EAAE,KAAK,eAAe,KAAK,gBAAgB,OAAO,eAAe;AAAA,EACjE,EAAE,KAAK,gBAAgB,KAAK,cAAc,OAAO,aAAa;AAAA,EAC9D,EAAE,KAAK,aAAa,KAAK,gBAAgB,OAAO,gBAAgB,gBAAgB,KAAK;AAAA,EACrF,EAAE,KAAK,iBAAiB,KAAK,cAAc,OAAO,aAAa;AAAA,EAC/D,EAAE,KAAK,kBAAkB,KAAK,gBAAgB,OAAO,eAAe;AAAA,EACpE,EAAE,KAAK,UAAU,KAAK,cAAc,OAAO,aAAa;AAAA,EACxD,EAAE,KAAK,YAAY,KAAK,gBAAgB,OAAO,eAAe;AAAA,EAC9D,EAAE,KAAK,QAAQ,KAAK,4BAA4B,OAAO,4BAA4B,gBAAgB,KAAK;AAAA,EACxG,EAAE,KAAK,YAAY,KAAK,gBAAgB,OAAO,eAAe;AAAA,EAC9D,EAAE,KAAK,OAAO,KAAK,2BAA2B,OAAO,2BAA2B,gBAAgB,KAAK;AAAA,EACrG,EAAE,KAAK,WAAW,KAAK,cAAc,OAAO,aAAa;AAAA,EACzD,EAAE,KAAK,SAAS,KAAK,uBAAuB;AAAA,EAC5C,EAAE,KAAK,SAAS,KAAK,UAAU;AAAA,EAC/B,EAAE,KAAK,aAAa,KAAK,oBAAoB,OAAO,mBAAmB;AAAA,EACvE,EAAE,KAAK,UAAU,KAAK,kBAAkB;AAAA,EACxC,EAAE,KAAK,iBAAiB,KAAK,iBAAiB,KAAK,oBAAoB;AAAA,EACvE,EAAE,KAAK,cAAc,KAAK,cAAc,KAAK,mBAAmB;AAAA,EAChE,EAAE,KAAK,iBAAiB,KAAK,2BAA2B;AAAA,EACxD,EAAE,KAAK,cAAc,KAAK,0BAA0B;AACxD,EAAE,OAAoB,iBAAiB,IAAI,QAAM,EAAE,KAAK,EAAE,KAAK,KAAK,EAAE,KAAK,OAAO,EAAE,MAAM,EAAE,CAAC;AAwB7F,IAAM,gBAA6B;AAAA,EAC/B,EAAE,KAAK,iBAAiB,KAAK,kBAAkB,KAAK,kBAAkB,OAAO,iBAAiB;AAAA,EAC9F,EAAE,KAAK,kBAAkB,KAAK,mBAAmB,KAAK,mBAAmB,OAAO,kBAAkB;AAAA,EAClG,EAAE,KAAK,eAAe,KAAK,WAAW;AAAA,EACtC,EAAE,KAAK,qBAAqB,KAAK,WAAW;AAAA,EAC5C,EAAE,KAAK,iBAAiB,KAAK,aAAa;AAAA,EAC1C,EAAE,KAAK,uBAAuB,KAAK,aAAa;AAAA,EAChD,EAAE,KAAK,UAAU,KAAK,kBAAkB;AAAA,EACxC,EAAE,KAAK,aAAa,KAAK,gBAAgB;AAAA,EACzC,EAAE,KAAK,SAAS,KAAK,UAAU,KAAK,WAAW;AAAA,EAC/C,EAAE,KAAK,SAAS,KAAK,oBAAoB,gBAAgB,KAAK;AAAA,EAC9D,EAAE,KAAK,SAAS,KAAK,WAAW;AAAA,EAChC,EAAE,KAAK,SAAS,KAAK,WAAW;AAAA,EAChC,EAAE,KAAK,cAAc,KAAK,gBAAgB;AAAA,EAC1C,EAAE,KAAK,eAAe,KAAK,WAAW;AAAA,EACtC,EAAE,KAAK,gBAAgB,KAAK,sBAAsB;AAAA,EAClD,EAAE,KAAK,SAAS,KAAK,cAAc;AAAA,EACnC,EAAE,KAAK,SAAS,KAAK,mBAAmB;AAAA,EACxC,EAAE,KAAK,UAAU,KAAK,eAAe,KAAK,mBAAmB;AACjE,EAAE,OAAO,cAAc;AAOvB,IAAM,gBAAgB,EAAE,KAAK,OAAO,KAAK,YAAY,OAAO,WAAW;;;ACxoDxD,SAAR,QAAyB;AAC9B,MAAI,MAAM,UAAU,CAAC;AACrB,MAAI,OAAO,OAAO,SAAU,OAAM,SAAS,cAAc,GAAG;AAC5D,MAAI,IAAI,GAAG,OAAO,UAAU,CAAC;AAC7B,MAAI,QAAQ,OAAO,QAAQ,YAAY,KAAK,YAAY,QAAQ,CAAC,MAAM,QAAQ,IAAI,GAAG;AACpF,aAAS,QAAQ,KAAM,KAAI,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AAC3E,UAAI,QAAQ,KAAK,IAAI;AACrB,UAAI,OAAO,SAAS,SAAU,KAAI,aAAa,MAAM,KAAK;AAAA,eACjD,SAAS,KAAM,KAAI,IAAI,IAAI;AAAA,IACtC;AACA;AAAA,EACF;AACA,SAAO,IAAI,UAAU,QAAQ,IAAK,KAAI,KAAK,UAAU,CAAC,CAAC;AACvD,SAAO;AACT;AAEA,SAAS,IAAI,KAAK,OAAO;AACvB,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,YAAY,SAAS,eAAe,KAAK,CAAC;AAAA,EAChD,WAAW,SAAS,MAAM;AAAA,EAC1B,WAAW,MAAM,YAAY,MAAM;AACjC,QAAI,YAAY,KAAK;AAAA,EACvB,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAK,KAAI,KAAK,MAAM,CAAC,CAAC;AAAA,EAC1D,OAAO;AACL,UAAM,IAAI,WAAW,6BAA6B,KAAK;AAAA,EACzD;AACF;;;ACvBA,IAAM,iBAAiB,OAAO,OAAO,UAAU,aAAa,aACtD,CAAAC,OAAKA,GAAE,UAAU,MAAM,IAAI,CAAAA,OAAKA;AAKtC,IAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcf,YAAY,MAAM,OAAO,OAAO,GAAG,KAAK,KAAK,QAAQ,WAAW,MAAM;AAClE,SAAK,OAAO;AAMZ,SAAK,QAAQ,EAAE,MAAM,GAAG,IAAI,EAAE;AAI9B,SAAK,OAAO;AACZ,SAAK,UAAU,CAAC;AAChB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,OAAO,KAAK,UAAU,MAAM,EAAE;AACnC,SAAK,cAAc;AACnB,SAAK,YAAY,YAAY,CAAAA,OAAK,UAAU,eAAeA,EAAC,CAAC,IAAI;AACjE,SAAK,QAAQ,KAAK,UAAU,KAAK;AAAA,EACrC;AAAA,EACA,OAAO;AACH,QAAI,KAAK,aAAa,KAAK,OAAO,QAAQ;AACtC,WAAK,eAAe,KAAK,OAAO;AAChC,WAAK,KAAK,KAAK;AACf,UAAI,KAAK,KAAK;AACV,eAAO;AACX,WAAK,YAAY;AACjB,WAAK,SAAS,KAAK,KAAK;AAAA,IAC5B;AACA,WAAO,YAAY,KAAK,QAAQ,KAAK,SAAS;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO;AACH,WAAO,KAAK,QAAQ;AAChB,WAAK,QAAQ,IAAI;AACrB,WAAO,KAAK,gBAAgB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AACd,eAAS;AACL,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,OAAO,GAAG;AACV,aAAK,OAAO;AACZ,eAAO;AAAA,MACX;AACA,UAAI,MAAM,cAAc,IAAI,GAAG,QAAQ,KAAK,cAAc,KAAK;AAC/D,WAAK,aAAa,cAAc,IAAI;AACpC,UAAI,OAAO,KAAK,UAAU,GAAG;AAC7B,eAAS,IAAI,GAAG,MAAM,SAAQ,KAAK;AAC/B,YAAI,OAAO,KAAK,WAAW,CAAC;AAC5B,YAAI,QAAQ,KAAK,MAAM,MAAM,KAAK,KAAK,YAAY,KAAK,WAAW;AACnE,YAAI,KAAK,KAAK,SAAS,GAAG;AACtB,cAAI,OAAO;AACP,iBAAK,QAAQ;AACb,mBAAO;AAAA,UACX;AACA;AAAA,QACJ;AACA,YAAI,OAAO,SAAS,IAAI,IAAI,UAAU,IAAI,WAAW,CAAC,KAAK;AACvD;AAAA,MACR;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,MAAM,MAAM,KAAK,KAAK;AAClB,QAAI,QAAQ;AACZ,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK,GAAG;AAC7C,UAAI,QAAQ,KAAK,QAAQ,CAAC,GAAG,OAAO;AACpC,UAAI,KAAK,MAAM,WAAW,KAAK,KAAK,MAAM;AACtC,YAAI,SAAS,KAAK,MAAM,SAAS,GAAG;AAChC,kBAAQ,EAAE,MAAM,KAAK,QAAQ,IAAI,CAAC,GAAG,IAAI,IAAI;AAAA,QACjD,OACK;AACD,eAAK,QAAQ,CAAC;AACd,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,UAAI,CAAC,MAAM;AACP,aAAK,QAAQ,OAAO,GAAG,CAAC;AACxB,aAAK;AAAA,MACT;AAAA,IACJ;AACA,QAAI,KAAK,MAAM,WAAW,CAAC,KAAK,MAAM;AAClC,UAAI,KAAK,MAAM,UAAU;AACrB,gBAAQ,EAAE,MAAM,KAAK,IAAI,IAAI;AAAA;AAE7B,aAAK,QAAQ,KAAK,GAAG,GAAG;AAAA,IAChC;AACA,QAAI,SAAS,KAAK,QAAQ,CAAC,KAAK,KAAK,MAAM,MAAM,MAAM,IAAI,KAAK,QAAQ,KAAK,WAAW;AACpF,cAAQ;AACZ,WAAO;AAAA,EACX;AACJ;AACA,IAAI,OAAO,UAAU;AACjB,eAAa,UAAU,OAAO,QAAQ,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM;AAEzE,IAAM,QAAQ,EAAE,MAAM,IAAI,IAAI,IAAI,OAAoB,KAAK,KAAK,EAAE,EAAE;AACpE,IAAM,YAAY,QAAQ,IAAI,WAAW,OAAO,KAAK;AAMrD,IAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMf,YAAY,MAAM,OAAO,SAAS,OAAO,GAAG,KAAK,KAAK,QAAQ;AAC1D,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,UAAU;AAKf,SAAK,OAAO;AAMZ,SAAK,QAAQ;AACb,QAAI,uBAAuB,KAAK,KAAK;AACjC,aAAO,IAAI,sBAAsB,MAAM,OAAO,SAAS,MAAM,EAAE;AACnE,SAAK,KAAK,IAAI,OAAO,OAAO,cAAc,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,MAAM,GAAG;AAC3H,SAAK,OAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AACtE,SAAK,OAAO,KAAK,KAAK;AACtB,QAAI,YAAY,KAAK,OAAO,IAAI;AAChC,SAAK,eAAe,UAAU;AAC9B,SAAK,WAAW,UAAU,MAAM,IAAI;AACpC,SAAK,QAAQ,KAAK,YAAY;AAAA,EAClC;AAAA,EACA,QAAQ,MAAM;AACV,SAAK,KAAK,KAAK,IAAI;AACnB,QAAI,KAAK,KAAK,WAAW;AACrB,WAAK,UAAU;AAAA,IACnB,OACK;AACD,WAAK,UAAU,KAAK,KAAK;AACzB,UAAI,KAAK,eAAe,KAAK,QAAQ,SAAS,KAAK;AAC/C,aAAK,UAAU,KAAK,QAAQ,MAAM,GAAG,KAAK,KAAK,KAAK,YAAY;AACpE,WAAK,KAAK,KAAK;AAAA,IACnB;AAAA,EACJ;AAAA,EACA,WAAW;AACP,SAAK,eAAe,KAAK,eAAe,KAAK,QAAQ,SAAS;AAC9D,QAAI,KAAK,eAAe,KAAK;AACzB,WAAK,UAAU;AAAA;AAEf,WAAK,QAAQ,CAAC;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACH,aAAS,MAAM,KAAK,WAAW,KAAK,kBAAgB;AAChD,WAAK,GAAG,YAAY;AACpB,UAAI,QAAQ,KAAK,YAAY,KAAK,MAAM,KAAK,GAAG,KAAK,KAAK,OAAO;AACjE,UAAI,OAAO;AACP,YAAI,OAAO,KAAK,eAAe,MAAM,OAAO,KAAK,OAAO,MAAM,CAAC,EAAE;AACjE,aAAK,WAAW,UAAU,KAAK,MAAM,MAAM,QAAQ,KAAK,IAAI,EAAE;AAC9D,YAAI,QAAQ,KAAK,eAAe,KAAK,QAAQ;AACzC,eAAK,SAAS;AAClB,aAAK,OAAO,MAAM,OAAO,KAAK,MAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI;AACnF,eAAK,QAAQ,EAAE,MAAM,IAAI,MAAM;AAC/B,iBAAO;AAAA,QACX;AACA,cAAM,KAAK,WAAW,KAAK;AAAA,MAC/B,WACS,KAAK,eAAe,KAAK,QAAQ,SAAS,KAAK,IAAI;AACxD,aAAK,SAAS;AACd,cAAM;AAAA,MACV,OACK;AACD,aAAK,OAAO;AACZ,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,IAAM,YAAyB,oBAAI,QAAQ;AAE3C,IAAM,eAAN,MAAM,cAAa;AAAA,EACf,YAAY,MAAM,MAAM;AACpB,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,KAAK,OAAO,KAAK,KAAK;AAAA,EAAQ;AAAA,EAChD,OAAO,IAAI,KAAK,MAAM,IAAI;AACtB,QAAI,SAAS,UAAU,IAAI,GAAG;AAC9B,QAAI,CAAC,UAAU,OAAO,QAAQ,MAAM,OAAO,MAAM,MAAM;AACnD,UAAI,OAAO,IAAI,cAAa,MAAM,IAAI,YAAY,MAAM,EAAE,CAAC;AAC3D,gBAAU,IAAI,KAAK,IAAI;AACvB,aAAO;AAAA,IACX;AACA,QAAI,OAAO,QAAQ,QAAQ,OAAO,MAAM;AACpC,aAAO;AACX,QAAI,EAAE,MAAM,MAAM,WAAW,IAAI;AACjC,QAAI,aAAa,MAAM;AACnB,aAAO,IAAI,YAAY,MAAM,UAAU,IAAI;AAC3C,mBAAa;AAAA,IACjB;AACA,QAAI,OAAO,KAAK;AACZ,cAAQ,IAAI,YAAY,OAAO,IAAI,EAAE;AACzC,cAAU,IAAI,KAAK,IAAI,cAAa,YAAY,IAAI,CAAC;AACrD,WAAO,IAAI,cAAa,MAAM,KAAK,MAAM,OAAO,YAAY,KAAK,UAAU,CAAC;AAAA,EAChF;AACJ;AACA,IAAM,wBAAN,MAA4B;AAAA,EACxB,YAAY,MAAM,OAAO,SAAS,MAAM,IAAI;AACxC,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,WAAW,UAAU,MAAM,IAAI;AACpC,SAAK,KAAK,IAAI,OAAO,OAAO,cAAc,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,MAAM,GAAG;AAC3H,SAAK,OAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AACtE,SAAK,OAAO,aAAa,IAAI,MAAM,MAAM,KAAK;AAAA,MAAS,OAAO;AAAA;AAAA,IAAqB,CAAC;AAAA,EACxF;AAAA,EACA,SAAS,KAAK;AACV,WAAO,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,EAAE;AAAA,EAC5D;AAAA,EACA,OAAO;AACH,eAAS;AACL,UAAI,MAAM,KAAK,GAAG,YAAY,KAAK,WAAW,KAAK,KAAK;AACxD,UAAI,QAAQ,KAAK,GAAG,KAAK,KAAK,KAAK,IAAI;AAEvC,UAAI,SAAS,CAAC,MAAM,CAAC,KAAK,MAAM,SAAS,KAAK;AAC1C,aAAK,GAAG,YAAY,MAAM;AAC1B,gBAAQ,KAAK,GAAG,KAAK,KAAK,KAAK,IAAI;AAAA,MACvC;AACA,UAAI,OAAO;AACP,YAAI,OAAO,KAAK,KAAK,OAAO,MAAM,OAAO,KAAK,OAAO,MAAM,CAAC,EAAE;AAG9D,aAAK,KAAK,KAAK,MAAM,KAAK,MAAM,MAAM,QAAQ,MAAM,CAAC,EAAE,UAAU,KAAK,KAAK,KAAK,SAAS,QACpF,CAAC,KAAK,QAAQ,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI;AAC5C,eAAK,QAAQ,EAAE,MAAM,IAAI,MAAM;AAC/B,eAAK,WAAW,UAAU,KAAK,MAAM,MAAM,QAAQ,KAAK,IAAI,EAAE;AAC9D,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,UAAI,KAAK,KAAK,MAAM,KAAK,IAAI;AACzB,aAAK,OAAO;AACZ,eAAO;AAAA,MACX;AAEA,WAAK,OAAO,aAAa,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,SAAS,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,SAAS,CAAC,CAAC;AAAA,IACrH;AAAA,EACJ;AACJ;AACA,IAAI,OAAO,UAAU,aAAa;AAC9B,eAAa,UAAU,OAAO,QAAQ,IAAI,sBAAsB,UAAU,OAAO,QAAQ,IACrF,WAAY;AAAE,WAAO;AAAA,EAAM;AACnC;AACA,SAAS,YAAY,QAAQ;AACzB,MAAI;AACA,QAAI,OAAO,QAAQ,SAAS;AAC5B,WAAO;AAAA,EACX,SACO,IAAI;AACP,WAAO;AAAA,EACX;AACJ;AACA,SAAS,UAAU,MAAM,KAAK;AAC1B,MAAI,OAAO,KAAK;AACZ,WAAO;AACX,MAAI,OAAO,KAAK,OAAO,GAAG,GAAG;AAC7B,SAAO,MAAM,KAAK,OAAO,OAAO,KAAK,KAAK,WAAW,MAAM,KAAK,IAAI,MAAM,SAAU,OAAO;AACvF;AACJ,SAAO;AACX;AAEA,SAAS,iBAAiB,MAAM;AAC5B,MAAI,OAAO,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM,UAAU,KAAK,IAAI,EAAE,MAAM;AAC9E,MAAI,QAAQ,MAAI,SAAS,EAAE,OAAO,gBAAgB,MAAM,QAAQ,OAAO,KAAK,CAAC;AAC7E,MAAI,MAAM,MAAI,QAAQ;AAAA,IAClB,OAAO;AAAA,IACP,WAAW,CAAC,UAAU;AAClB,UAAI,MAAM,WAAW,IAAI;AACrB,cAAM,eAAe;AACrB,aAAK,SAAS,EAAE,SAAS,aAAa,GAAG,KAAK,EAAE,CAAC;AACjD,aAAK,MAAM;AAAA,MACf,WACS,MAAM,WAAW,IAAI;AAC1B,cAAM,eAAe;AACrB,WAAG;AAAA,MACP;AAAA,IACJ;AAAA,IACA,UAAU,CAAC,UAAU;AACjB,YAAM,eAAe;AACrB,SAAG;AAAA,IACP;AAAA,EACJ,GAAG,MAAI,SAAS,KAAK,MAAM,OAAO,YAAY,GAAG,MAAM,KAAK,GAAG,KAAK,MAAI,UAAU,EAAE,OAAO,aAAa,MAAM,SAAS,GAAG,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;AAClJ,WAAS,KAAK;AACV,QAAI,QAAQ,6BAA6B,KAAK,MAAM,KAAK;AACzD,QAAI,CAAC;AACD;AACJ,QAAI,EAAE,MAAM,IAAI,MAAM,YAAY,MAAM,IAAI,OAAO,MAAM,UAAU,KAAK,IAAI;AAC5E,QAAI,CAAC,EAAE,MAAM,IAAI,IAAI,OAAO,IAAI;AAChC,QAAI,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI;AAC9B,QAAIC,QAAO,KAAK,CAAC,KAAK,UAAU;AAChC,QAAI,MAAM,SAAS;AACf,UAAI,KAAKA,QAAO;AAChB,UAAI;AACA,aAAK,MAAM,QAAQ,MAAM,KAAK,KAAM,UAAU,SAAS,MAAM,IAAI;AACrE,MAAAA,QAAO,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE;AAAA,IAC1C,WACS,MAAM,MAAM;AACjB,MAAAA,QAAOA,SAAQ,QAAQ,MAAM,KAAK,KAAK,UAAU;AAAA,IACrD;AACA,QAAI,UAAU,MAAM,IAAI,KAAK,KAAK,IAAI,GAAG,KAAK,IAAI,MAAM,IAAI,OAAOA,KAAI,CAAC,CAAC;AACzE,QAAI,YAAY,gBAAgB,OAAO,QAAQ,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,QAAQ,MAAM,CAAC,CAAC;AAChG,SAAK,SAAS;AAAA,MACV,SAAS,CAAC,aAAa,GAAG,KAAK,GAAG,WAAW,eAAe,UAAU,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC;AAAA,MAC5F;AAAA,IACJ,CAAC;AACD,SAAK,MAAM;AAAA,EACf;AACA,SAAO,EAAE,IAAI;AACjB;AACA,IAAM,eAA4B,YAAY,OAAO;AACrD,IAAM,cAA2B,WAAW,OAAO;AAAA,EAC/C,SAAS;AAAE,WAAO;AAAA,EAAM;AAAA,EACxB,OAAO,OAAO,IAAI;AACd,aAAS,KAAK,GAAG;AACb,UAAI,EAAE,GAAG,YAAY;AACjB,gBAAQ,EAAE;AAClB,WAAO;AAAA,EACX;AAAA,EACA,SAAS,OAAK,UAAU,KAAK,GAAG,SAAO,MAAM,mBAAmB,IAAI;AACxE,CAAC;AAUD,IAAM,WAAW,UAAQ;AACrB,MAAI,QAAQ,SAAS,MAAM,gBAAgB;AAC3C,MAAI,CAAC,OAAO;AACR,QAAI,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC;AACpC,QAAI,KAAK,MAAM,MAAM,aAAa,KAAK,KAAK;AACxC,cAAQ,KAAK,YAAY,aAAa,GAAG,CAAC,aAAa,WAAW,CAAC,CAAC;AACxE,SAAK,SAAS,EAAE,QAAQ,CAAC;AACzB,YAAQ,SAAS,MAAM,gBAAgB;AAAA,EAC3C;AACA,MAAI;AACA,UAAM,IAAI,cAAc,OAAO,EAAE,OAAO;AAC5C,SAAO;AACX;AACA,IAAM,cAA2B,WAAW,UAAU;AAAA,EAClD,yBAAyB;AAAA,IACrB,SAAS;AAAA,IACT,WAAW,EAAE,UAAU,MAAM;AAAA,EACjC;AACJ,CAAC;AAED,IAAM,0BAA0B;AAAA,EAC5B,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,YAAY;AAAA,EACZ,YAAY;AAChB;AACA,IAAM,kBAA+B,MAAM,OAAO;AAAA,EAC9C,QAAQ,SAAS;AACb,WAAO,cAAc,SAAS,yBAAyB;AAAA,MACnD,2BAA2B,CAAC,GAAG,MAAM,KAAK;AAAA,MAC1C,oBAAoB,KAAK;AAAA,MACzB,YAAY,KAAK;AAAA,IACrB,CAAC;AAAA,EACL;AACJ,CAAC;AAOD,SAAS,0BAA0B,SAAS;AACxC,MAAI,MAAM,CAAC,cAAc,gBAAgB;AACzC,MAAI;AACA,QAAI,KAAK,gBAAgB,GAAG,OAAO,CAAC;AACxC,SAAO;AACX;AACA,IAAM,YAAyB,WAAW,KAAK,EAAE,OAAO,oBAAoB,CAAC;AAC7E,IAAM,gBAA6B,WAAW,KAAK,EAAE,OAAO,2CAA2C,CAAC;AAExG,SAAS,qBAAqB,OAAO,OAAO,MAAM,IAAI;AAClD,UAAQ,QAAQ,KAAK,MAAM,MAAM,SAAS,OAAO,GAAG,IAAI,CAAC,KAAK,aAAa,UACtE,MAAM,MAAM,IAAI,UAAU,MAAM,MAAM,SAAS,IAAI,KAAK,CAAC,CAAC,KAAK,aAAa;AACrF;AAEA,SAAS,WAAW,OAAO,OAAO,MAAM,IAAI;AACxC,SAAO,MAAM,MAAM,SAAS,MAAM,OAAO,CAAC,CAAC,KAAK,aAAa,QACtD,MAAM,MAAM,SAAS,KAAK,GAAG,EAAE,CAAC,KAAK,aAAa;AAC7D;AACA,IAAM,mBAAgC,WAAW,UAAU,MAAM;AAAA,EAC7D,YAAY,MAAM;AACd,SAAK,cAAc,KAAK,QAAQ,IAAI;AAAA,EACxC;AAAA,EACA,OAAO,QAAQ;AACX,QAAI,OAAO,gBAAgB,OAAO,cAAc,OAAO;AACnD,WAAK,cAAc,KAAK,QAAQ,OAAO,IAAI;AAAA,EACnD;AAAA,EACA,QAAQ,MAAM;AACV,QAAI,OAAO,KAAK,MAAM,MAAM,eAAe;AAC3C,QAAI,EAAE,MAAM,IAAI,MAAM,MAAM,MAAM;AAClC,QAAI,IAAI,OAAO,SAAS;AACpB,aAAO,WAAW;AACtB,QAAI,QAAQ,IAAI,MAAM,OAAO,QAAQ;AACrC,QAAI,MAAM,OAAO;AACb,UAAI,CAAC,KAAK;AACN,eAAO,WAAW;AACtB,UAAI,OAAO,MAAM,OAAO,MAAM,IAAI;AAClC,UAAI,CAAC;AACD,eAAO,WAAW;AACtB,cAAQ,MAAM,gBAAgB,MAAM,IAAI;AACxC,cAAQ,MAAM,SAAS,KAAK,MAAM,KAAK,EAAE;AAAA,IAC7C,OACK;AACD,UAAI,MAAM,MAAM,KAAK,MAAM;AAC3B,UAAI,MAAM,KAAK,sBAAsB,MAAM;AACvC,eAAO,WAAW;AACtB,UAAI,KAAK,YAAY;AACjB,gBAAQ,MAAM,SAAS,MAAM,MAAM,MAAM,EAAE;AAC3C,gBAAQ,MAAM,gBAAgB,MAAM,IAAI;AACxC,YAAI,EAAE,qBAAqB,OAAO,OAAO,MAAM,MAAM,MAAM,EAAE,KACzD,WAAW,OAAO,OAAO,MAAM,MAAM,MAAM,EAAE;AAC7C,iBAAO,WAAW;AAAA,MAC1B,OACK;AACD,gBAAQ,MAAM,SAAS,MAAM,MAAM,MAAM,EAAE;AAC3C,YAAI,CAAC;AACD,iBAAO,WAAW;AAAA,MAC1B;AAAA,IACJ;AACA,QAAI,OAAO,CAAC;AACZ,aAAS,QAAQ,KAAK,eAAe;AACjC,UAAI,SAAS,IAAI,aAAa,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK,EAAE;AAClE,aAAO,CAAC,OAAO,KAAK,EAAE,MAAM;AACxB,YAAI,EAAE,MAAM,GAAG,IAAI,OAAO;AAC1B,YAAI,CAAC,SAAS,qBAAqB,OAAO,OAAO,MAAM,EAAE,GAAG;AACxD,cAAI,MAAM,SAAS,QAAQ,MAAM,QAAQ,MAAM,MAAM;AACjD,iBAAK,KAAK,cAAc,MAAM,MAAM,EAAE,CAAC;AAAA,mBAClC,QAAQ,MAAM,MAAM,MAAM,MAAM;AACrC,iBAAK,KAAK,UAAU,MAAM,MAAM,EAAE,CAAC;AACvC,cAAI,KAAK,SAAS,KAAK;AACnB,mBAAO,WAAW;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,WAAW,IAAI,IAAI;AAAA,EAC9B;AACJ,GAAG;AAAA,EACC,aAAa,OAAK,EAAE;AACxB,CAAC;AACD,IAAM,eAA4B,WAAW,UAAU;AAAA,EACnD,sBAAsB,EAAE,iBAAiB,YAAY;AAAA,EACrD,sCAAsC,EAAE,iBAAiB,cAAc;AAC3E,CAAC;AAED,IAAM,aAAa,CAAC,EAAE,OAAO,SAAS,MAAM;AACxC,MAAI,EAAE,UAAU,IAAI;AACpB,MAAI,SAAS,gBAAgB,OAAO,UAAU,OAAO,IAAI,WAAS,MAAM,OAAO,MAAM,IAAI,KAAK,gBAAgB,OAAO,MAAM,IAAI,CAAC,GAAG,UAAU,SAAS;AACtJ,MAAI,OAAO,GAAG,SAAS;AACnB,WAAO;AACX,WAAS,MAAM,OAAO,EAAE,WAAW,OAAO,CAAC,CAAC;AAC5C,SAAO;AACX;AAGA,SAAS,mBAAmB,OAAO,OAAO;AACtC,MAAI,EAAE,MAAM,OAAO,IAAI,MAAM;AAC7B,MAAI,OAAO,MAAM,OAAO,KAAK,IAAI,GAAG,WAAW,QAAQ,KAAK,QAAQ,KAAK,QAAQ,KAAK,MAAM,KAAK;AACjG,WAAS,SAAS,OAAO,SAAS,IAAI,aAAa,MAAM,KAAK,OAAO,OAAO,OAAO,SAAS,CAAC,EAAE,EAAE,OAAK;AAClG,WAAO,KAAK;AACZ,QAAI,OAAO,MAAM;AACb,UAAI;AACA,eAAO;AACX,eAAS,IAAI,aAAa,MAAM,KAAK,OAAO,GAAG,KAAK,IAAI,GAAG,OAAO,OAAO,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;AAC9F,eAAS;AAAA,IACb,OACK;AACD,UAAI,UAAU,OAAO,KAAK,OAAK,EAAE,QAAQ,OAAO,MAAM,IAAI;AACtD;AACJ,UAAI,UAAU;AACV,YAAIC,QAAO,MAAM,OAAO,OAAO,MAAM,IAAI;AACzC,YAAI,CAACA,SAAQA,MAAK,QAAQ,OAAO,MAAM,QAAQA,MAAK,MAAM,OAAO,MAAM;AACnE;AAAA,MACR;AACA,aAAO,OAAO;AAAA,IAClB;AAAA,EACJ;AACJ;AAKA,IAAM,uBAAuB,CAAC,EAAE,OAAO,SAAS,MAAM;AAClD,MAAI,EAAE,OAAO,IAAI,MAAM;AACvB,MAAI,OAAO,KAAK,SAAO,IAAI,SAAS,IAAI,EAAE;AACtC,WAAO,WAAW,EAAE,OAAO,SAAS,CAAC;AACzC,MAAI,eAAe,MAAM,SAAS,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE;AAC9D,MAAI,MAAM,UAAU,OAAO,KAAK,OAAK,MAAM,SAAS,EAAE,MAAM,EAAE,EAAE,KAAK,YAAY;AAC7E,WAAO;AACX,MAAI,QAAQ,mBAAmB,OAAO,YAAY;AAClD,MAAI,CAAC;AACD,WAAO;AACX,WAAS,MAAM,OAAO;AAAA,IAClB,WAAW,MAAM,UAAU,SAAS,gBAAgB,MAAM,MAAM,MAAM,MAAM,EAAE,GAAG,KAAK;AAAA,IACtF,SAAS,WAAW,eAAe,MAAM,EAAE;AAAA,EAC/C,CAAC,CAAC;AACF,SAAO;AACX;AAEA,IAAM,oBAAiC,MAAM,OAAO;AAAA,EAChD,QAAQ,SAAS;AACb,WAAO,cAAc,SAAS;AAAA,MAC1B,KAAK;AAAA,MACL,eAAe;AAAA,MACf,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa,UAAQ,IAAI,YAAY,IAAI;AAAA,MACzC,eAAe,WAAS,WAAW,eAAe,KAAK;AAAA,IAC3D,CAAC;AAAA,EACL;AACJ,CAAC;AAaD,IAAM,cAAN,MAAkB;AAAA;AAAA;AAAA;AAAA,EAId,YAAY,QAAQ;AAChB,SAAK,SAAS,OAAO;AACrB,SAAK,gBAAgB,CAAC,CAAC,OAAO;AAC9B,SAAK,UAAU,CAAC,CAAC,OAAO;AACxB,SAAK,SAAS,CAAC,CAAC,OAAO;AACvB,SAAK,UAAU,OAAO,WAAW;AACjC,SAAK,QAAQ,CAAC,CAAC,KAAK,WAAW,CAAC,KAAK,UAAU,YAAY,KAAK,MAAM;AACtE,SAAK,WAAW,KAAK,QAAQ,KAAK,MAAM;AACxC,SAAK,YAAY,CAAC,CAAC,OAAO;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,MAAM;AACV,WAAO,KAAK,UAAU,OAClB,KAAK,QAAQ,gBAAgB,CAAC,GAAG,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,MAAO,IAAI;AAAA,EAC7G;AAAA;AAAA;AAAA;AAAA,EAIA,GAAG,OAAO;AACN,WAAO,KAAK,UAAU,MAAM,UAAU,KAAK,WAAW,MAAM,WACxD,KAAK,iBAAiB,MAAM,iBAAiB,KAAK,UAAU,MAAM,UAClE,KAAK,aAAa,MAAM;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,WAAO,KAAK,SAAS,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,IAAI;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO,OAAO,GAAG,IAAI;AAC3B,QAAI,KAAK,MAAM,MAAM,QAAQ,YAAY,OAAO,EAAE,KAAK,MAAM,CAAC;AAC9D,QAAI,MAAM;AACN,WAAK,GAAG,IAAI;AAChB,WAAO,KAAK,SAAS,aAAa,MAAM,IAAI,MAAM,EAAE,IAAI,aAAa,MAAM,IAAI,MAAM,EAAE;AAAA,EAC3F;AACJ;AACA,IAAM,YAAN,MAAgB;AAAA,EACZ,YAAY,MAAM;AACd,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,SAAS,aAAa,MAAM,OAAO,MAAM,IAAI;AACzC,SAAO,IAAI,aAAa,MAAM,KAAK,KAAK,UAAU,MAAM,IAAI,KAAK,gBAAgB,SAAY,CAAAC,OAAKA,GAAE,YAAY,GAAG,KAAK,YAAY,eAAe,MAAM,KAAK,MAAM,gBAAgB,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,MAAS;AAC/N;AACA,SAAS,eAAe,KAAK,aAAa;AACtC,SAAO,CAAC,MAAM,IAAI,KAAK,WAAW;AAC9B,QAAI,SAAS,QAAQ,SAAS,IAAI,SAAS,IAAI;AAC3C,eAAS,KAAK,IAAI,GAAG,OAAO,CAAC;AAC7B,YAAM,IAAI,YAAY,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC9D;AACA,YAAQ,YAAY,WAAW,KAAK,OAAO,MAAM,CAAC,KAAK,aAAa,QAChE,YAAY,UAAU,KAAK,OAAO,MAAM,CAAC,KAAK,aAAa,UAC1D,YAAY,UAAU,KAAK,KAAK,MAAM,CAAC,KAAK,aAAa,QACtD,YAAY,WAAW,KAAK,KAAK,MAAM,CAAC,KAAK,aAAa;AAAA,EACtE;AACJ;AACA,IAAM,cAAN,cAA0B,UAAU;AAAA,EAChC,YAAY,MAAM;AACd,UAAM,IAAI;AAAA,EACd;AAAA,EACA,UAAU,OAAO,SAAS,OAAO;AAC7B,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,OAAO,MAAM,IAAI,MAAM,EAAE,gBAAgB;AACrF,QAAI,OAAO;AACP,eAAS,aAAa,KAAK,MAAM,OAAO,GAAG,OAAO,EAAE,gBAAgB;AACxE,WAAO,OAAO,OAAO,OAAO,OAAO;AAAA,EACvC;AAAA;AAAA;AAAA,EAGA,iBAAiB,OAAO,MAAM,IAAI;AAC9B,aAAS,MAAM,QAAM;AACjB,UAAI,QAAQ,KAAK,IAAI,MAAM,MAAM,MAAiC,KAAK,KAAK,SAAS,MAAM;AAC3F,UAAI,SAAS,aAAa,KAAK,MAAM,OAAO,OAAO,GAAG,GAAG,QAAQ;AACjE,aAAO,CAAC,OAAO,gBAAgB,EAAE;AAC7B,gBAAQ,OAAO;AACnB,UAAI;AACA,eAAO;AACX,UAAI,SAAS;AACT,eAAO;AACX,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,UAAU,OAAO,SAAS,OAAO;AAC7B,WAAO,KAAK,iBAAiB,OAAO,GAAG,OAAO,KAC1C,KAAK,iBAAiB,OAAO,OAAO,MAAM,IAAI,MAAM;AAAA,EAC5D;AAAA,EACA,eAAe,SAAS;AAAE,WAAO,KAAK,KAAK,QAAQ,KAAK,KAAK,OAAO;AAAA,EAAG;AAAA,EACvE,SAAS,OAAO,OAAO;AACnB,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,GAAG,MAAM,IAAI,MAAM,GAAG,SAAS,CAAC;AAC5E,WAAO,CAAC,OAAO,KAAK,EAAE,MAAM;AACxB,UAAI,OAAO,UAAU;AACjB,eAAO;AACX,aAAO,KAAK,OAAO,KAAK;AAAA,IAC5B;AACA,WAAO;AAAA,EACX;AAAA,EACA,UAAU,OAAO,MAAM,IAAIC,MAAK;AAC5B,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,KAAK,IAAI,GAAG,OAAO,KAAK,KAAK,SAAS,MAAM,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,SAAS,QAAQ,MAAM,IAAI,MAAM,CAAC;AACrJ,WAAO,CAAC,OAAO,KAAK,EAAE;AAClB,MAAAA,KAAI,OAAO,MAAM,MAAM,OAAO,MAAM,EAAE;AAAA,EAC9C;AACJ;AACA,SAAS,aAAa,MAAM,OAAO,MAAM,IAAI;AACzC,SAAO,IAAI,aAAa,MAAM,KAAK,KAAK,QAAQ;AAAA,IAC5C,YAAY,CAAC,KAAK;AAAA,IAClB,MAAM,KAAK,YAAY,eAAe,MAAM,gBAAgB,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI;AAAA,EAC9F,GAAG,MAAM,EAAE;AACf;AACA,SAAS,WAAW,KAAK,OAAO;AAC5B,SAAO,IAAI,MAAM,iBAAiB,KAAK,OAAO,KAAK,GAAG,KAAK;AAC/D;AACA,SAAS,UAAU,KAAK,OAAO;AAC3B,SAAO,IAAI,MAAM,OAAO,iBAAiB,KAAK,KAAK,CAAC;AACxD;AACA,SAAS,eAAe,aAAa;AACjC,SAAO,CAAC,OAAO,KAAK,UAAU,CAAC,MAAM,CAAC,EAAE,WACnC,YAAY,WAAW,MAAM,OAAO,MAAM,KAAK,CAAC,KAAK,aAAa,QAC/D,YAAY,UAAU,MAAM,OAAO,MAAM,KAAK,CAAC,KAAK,aAAa,UAChE,YAAY,UAAU,MAAM,OAAO,MAAM,QAAQ,MAAM,CAAC,EAAE,MAAM,CAAC,KAAK,aAAa,QAChF,YAAY,WAAW,MAAM,OAAO,MAAM,QAAQ,MAAM,CAAC,EAAE,MAAM,CAAC,KAAK,aAAa;AACpG;AACA,IAAM,cAAN,cAA0B,UAAU;AAAA,EAChC,UAAU,OAAO,SAAS,OAAO;AAC7B,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,OAAO,MAAM,IAAI,MAAM,EAAE,KAAK;AAC1E,QAAI,OAAO;AACP,eAAS,aAAa,KAAK,MAAM,OAAO,GAAG,OAAO,EAAE,KAAK;AAC7D,WAAO,OAAO,OAAO,OAAO,OAAO;AAAA,EACvC;AAAA,EACA,iBAAiB,OAAO,MAAM,IAAI;AAC9B,aAAS,OAAO,KAAI,QAAQ;AACxB,UAAI,QAAQ,KAAK;AAAA,QAAI;AAAA,QAAM,KAAK,OAAO;AAAA;AAAA,MAA8B;AACrE,UAAI,SAAS,aAAa,KAAK,MAAM,OAAO,OAAO,EAAE,GAAG,QAAQ;AAChE,aAAO,CAAC,OAAO,KAAK,EAAE;AAClB,gBAAQ,OAAO;AACnB,UAAI,UAAU,SAAS,QAAQ,MAAM,OAAO,QAAQ;AAChD,eAAO;AACX,UAAI,SAAS;AACT,eAAO;AAAA,IACf;AAAA,EACJ;AAAA,EACA,UAAU,OAAO,SAAS,OAAO;AAC7B,WAAO,KAAK,iBAAiB,OAAO,GAAG,OAAO,KAC1C,KAAK,iBAAiB,OAAO,OAAO,MAAM,IAAI,MAAM;AAAA,EAC5D;AAAA,EACA,eAAe,QAAQ;AACnB,WAAO,KAAK,KAAK,QAAQ,KAAK,KAAK,OAAO,EAAE,QAAQ,gBAAgB,CAAC,GAAG,MAAM,KAAK,MAAM,MACnF,KAAK,MAAM,OAAO,MAAM,CAAC,IACrB,KAAK,OAAO,CAAC,IAAI,OAAO,MAAM,SAAS,OAAO,MAAM,CAAC,IACjD,CAAC;AAAA,EACnB;AAAA,EACA,SAAS,OAAO,OAAO;AACnB,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,GAAG,MAAM,IAAI,MAAM,GAAG,SAAS,CAAC;AAC5E,WAAO,CAAC,OAAO,KAAK,EAAE,MAAM;AACxB,UAAI,OAAO,UAAU;AACjB,eAAO;AACX,aAAO,KAAK,OAAO,KAAK;AAAA,IAC5B;AACA,WAAO;AAAA,EACX;AAAA,EACA,UAAU,OAAO,MAAM,IAAIA,MAAK;AAC5B,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,KAAK;AAAA,MAAI;AAAA,MAAG,OAAO;AAAA;AAAA,IAAgC,GAAG,KAAK,IAAI,KAAK,KAAkC,MAAM,IAAI,MAAM,CAAC;AACnK,WAAO,CAAC,OAAO,KAAK,EAAE;AAClB,MAAAA,KAAI,OAAO,MAAM,MAAM,OAAO,MAAM,EAAE;AAAA,EAC9C;AACJ;AAQA,IAAM,iBAA8B,YAAY,OAAO;AACvD,IAAM,cAA2B,YAAY,OAAO;AACpD,IAAM,cAA2B,WAAW,OAAO;AAAA,EAC/C,OAAO,OAAO;AACV,WAAO,IAAI,YAAY,aAAa,KAAK,EAAE,OAAO,GAAG,IAAI;AAAA,EAC7D;AAAA,EACA,OAAO,OAAO,IAAI;AACd,aAAS,UAAU,GAAG,SAAS;AAC3B,UAAI,OAAO,GAAG,cAAc;AACxB,gBAAQ,IAAI,YAAY,OAAO,MAAM,OAAO,GAAG,MAAM,KAAK;AAAA,eACrD,OAAO,GAAG,WAAW;AAC1B,gBAAQ,IAAI,YAAY,MAAM,OAAO,OAAO,QAAQ,oBAAoB,IAAI;AAAA,IACpF;AACA,WAAO;AAAA,EACX;AAAA,EACA,SAAS,OAAK,UAAU,KAAK,GAAG,SAAO,IAAI,KAAK;AACpD,CAAC;AAeD,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,OAAO,OAAO;AACtB,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACjB;AACJ;AACA,IAAM,YAAyB,WAAW,KAAK,EAAE,OAAO,iBAAiB,CAAC;AAA1E,IAA6E,oBAAiC,WAAW,KAAK,EAAE,OAAO,yCAAyC,CAAC;AACjL,IAAM,oBAAiC,WAAW,UAAU,MAAM;AAAA,EAC9D,YAAY,MAAM;AACd,SAAK,OAAO;AACZ,SAAK,cAAc,KAAK,UAAU,KAAK,MAAM,MAAM,WAAW,CAAC;AAAA,EACnE;AAAA,EACA,OAAO,QAAQ;AACX,QAAI,QAAQ,OAAO,MAAM,MAAM,WAAW;AAC1C,QAAI,SAAS,OAAO,WAAW,MAAM,WAAW,KAAK,OAAO,cAAc,OAAO,gBAAgB,OAAO;AACpG,WAAK,cAAc,KAAK,UAAU,KAAK;AAAA,EAC/C;AAAA,EACA,UAAU,EAAE,OAAO,MAAM,GAAG;AACxB,QAAI,CAAC,SAAS,CAAC,MAAM,KAAK;AACtB,aAAO,WAAW;AACtB,QAAI,EAAE,KAAK,IAAI;AACf,QAAI,UAAU,IAAI,gBAAgB;AAClC,aAAS,IAAI,GAAG,SAAS,KAAK,eAAe,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AACxE,UAAI,EAAE,MAAM,GAAG,IAAI,OAAO,CAAC;AAC3B,aAAO,IAAI,IAAI,KAAK,KAAK,OAAO,IAAI,CAAC,EAAE,OAAO,IAAI;AAC9C,aAAK,OAAO,EAAE,CAAC,EAAE;AACrB,YAAM,UAAU,KAAK,OAAO,MAAM,IAAI,CAACC,OAAMC,QAAO;AAChD,YAAI,WAAW,KAAK,MAAM,UAAU,OAAO,KAAK,OAAK,EAAE,QAAQD,SAAQ,EAAE,MAAMC,GAAE;AACjF,gBAAQ,IAAID,OAAMC,KAAI,WAAW,oBAAoB,SAAS;AAAA,MAClE,CAAC;AAAA,IACL;AACA,WAAO,QAAQ,OAAO;AAAA,EAC1B;AACJ,GAAG;AAAA,EACC,aAAa,OAAK,EAAE;AACxB,CAAC;AACD,SAAS,cAAc,GAAG;AACtB,SAAO,UAAQ;AACX,QAAI,QAAQ,KAAK,MAAM,MAAM,aAAa,KAAK;AAC/C,WAAO,SAAS,MAAM,MAAM,KAAK,QAAQ,EAAE,MAAM,KAAK,IAAI,gBAAgB,IAAI;AAAA,EAClF;AACJ;AAOA,IAAM,WAAwB,cAAc,CAAC,MAAM,EAAE,MAAM,MAAM;AAC7D,MAAI,EAAE,GAAG,IAAI,KAAK,MAAM,UAAU;AAClC,MAAI,OAAO,MAAM,UAAU,KAAK,OAAO,IAAI,EAAE;AAC7C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,YAAY,gBAAgB,OAAO,KAAK,MAAM,KAAK,EAAE;AACzD,MAAI,SAAS,KAAK,MAAM,MAAM,iBAAiB;AAC/C,OAAK,SAAS;AAAA,IACV;AAAA,IACA,SAAS,CAAC,cAAc,MAAM,IAAI,GAAG,OAAO,cAAc,UAAU,MAAM,IAAI,CAAC;AAAA,IAC/E,WAAW;AAAA,EACf,CAAC;AACD,oBAAkB,IAAI;AACtB,SAAO;AACX,CAAC;AAMD,IAAM,eAA4B,cAAc,CAAC,MAAM,EAAE,MAAM,MAAM;AACjE,MAAI,EAAE,MAAM,IAAI,MAAM,EAAE,KAAK,IAAI,MAAM,UAAU;AACjD,MAAI,OAAO,MAAM,UAAU,OAAO,MAAM,IAAI;AAC5C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,YAAY,gBAAgB,OAAO,KAAK,MAAM,KAAK,EAAE;AACzD,MAAI,SAAS,KAAK,MAAM,MAAM,iBAAiB;AAC/C,OAAK,SAAS;AAAA,IACV;AAAA,IACA,SAAS,CAAC,cAAc,MAAM,IAAI,GAAG,OAAO,cAAc,UAAU,MAAM,IAAI,CAAC;AAAA,IAC/E,WAAW;AAAA,EACf,CAAC;AACD,oBAAkB,IAAI;AACtB,SAAO;AACX,CAAC;AAID,IAAM,gBAA6B,cAAc,CAAC,MAAM,EAAE,MAAM,MAAM;AAClE,MAAI,SAAS,MAAM,SAAS,KAAK,OAAO,GAAI;AAC5C,MAAI,CAAC,UAAU,CAAC,OAAO;AACnB,WAAO;AACX,OAAK,SAAS;AAAA,IACV,WAAW,gBAAgB,OAAO,OAAO,IAAI,OAAK,gBAAgB,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;AAAA,IACtF,WAAW;AAAA,EACf,CAAC;AACD,SAAO;AACX,CAAC;AAID,IAAM,yBAAyB,CAAC,EAAE,OAAO,SAAS,MAAM;AACpD,MAAI,MAAM,MAAM;AAChB,MAAI,IAAI,OAAO,SAAS,KAAK,IAAI,KAAK;AAClC,WAAO;AACX,MAAI,EAAE,MAAM,GAAG,IAAI,IAAI;AACvB,MAAI,SAAS,CAAC,GAAG,OAAO;AACxB,WAAS,MAAM,IAAI,aAAa,MAAM,KAAK,MAAM,SAAS,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE,QAAO;AACrF,QAAI,OAAO,SAAS;AAChB,aAAO;AACX,QAAI,IAAI,MAAM,QAAQ;AAClB,aAAO,OAAO;AAClB,WAAO,KAAK,gBAAgB,MAAM,IAAI,MAAM,MAAM,IAAI,MAAM,EAAE,CAAC;AAAA,EACnE;AACA,WAAS,MAAM,OAAO;AAAA,IAClB,WAAW,gBAAgB,OAAO,QAAQ,IAAI;AAAA,IAC9C,WAAW;AAAA,EACf,CAAC,CAAC;AACF,SAAO;AACX;AAIA,IAAM,cAA2B,cAAc,CAAC,MAAM,EAAE,MAAM,MAAM;AAChE,MAAI,EAAE,MAAM,IAAI,MAAM,EAAE,MAAM,GAAG,IAAI,MAAM,UAAU;AACrD,MAAI,MAAM;AACN,WAAO;AACX,MAAI,OAAO,MAAM,UAAU,OAAO,MAAM,IAAI;AAC5C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,UAAU,CAAC,GAAG,WAAW;AAC7B,MAAI,UAAU,CAAC;AACf,MAAI,KAAK,QAAQ,QAAQ,KAAK,MAAM,IAAI;AACpC,kBAAc,MAAM,OAAO,MAAM,eAAe,IAAI,CAAC;AACrD,YAAQ,KAAK,EAAE,MAAM,KAAK,MAAM,IAAI,KAAK,IAAI,QAAQ,YAAY,CAAC;AAClE,WAAO,MAAM,UAAU,OAAO,KAAK,MAAM,KAAK,EAAE;AAChD,YAAQ,KAAK,WAAW,SAAS,GAAG,MAAM,OAAO,4BAA4B,MAAM,IAAI,OAAO,IAAI,EAAE,MAAM,IAAI,GAAG,CAAC;AAAA,EACtH;AACA,MAAI,MAAM;AACN,QAAI,MAAM,QAAQ,UAAU,KAAK,QAAQ,CAAC,EAAE,QAAQ,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,OAAO,YAAY;AACpG,gBAAY,gBAAgB,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK,GAAG;AACjE,YAAQ,KAAK,cAAc,MAAM,IAAI,CAAC;AACtC,YAAQ,KAAK,MAAM,MAAM,iBAAiB,EAAE,cAAc,UAAU,MAAM,IAAI,CAAC;AAAA,EACnF;AACA,OAAK,SAAS;AAAA,IACV;AAAA,IAAS;AAAA,IAAW;AAAA,IACpB,WAAW;AAAA,EACf,CAAC;AACD,SAAO;AACX,CAAC;AAKD,IAAM,aAA0B,cAAc,CAAC,MAAM,EAAE,MAAM,MAAM;AAC/D,MAAI,KAAK,MAAM;AACX,WAAO;AACX,MAAI,UAAU,MAAM,SAAS,KAAK,OAAO,GAAG,EAAE,IAAI,WAAS;AACvD,QAAI,EAAE,MAAM,GAAG,IAAI;AACnB,WAAO,EAAE,MAAM,IAAI,QAAQ,MAAM,eAAe,KAAK,EAAE;AAAA,EAC3D,CAAC;AACD,MAAI,CAAC,QAAQ;AACT,WAAO;AACX,MAAI,eAAe,KAAK,MAAM,OAAO,sBAAsB,QAAQ,MAAM,IAAI;AAC7E,OAAK,SAAS;AAAA,IACV;AAAA,IACA,SAAS,WAAW,SAAS,GAAG,YAAY;AAAA,IAC5C,WAAW;AAAA,EACf,CAAC;AACD,SAAO;AACX,CAAC;AACD,SAAS,kBAAkB,MAAM;AAC7B,SAAO,KAAK,MAAM,MAAM,iBAAiB,EAAE,YAAY,IAAI;AAC/D;AACA,SAAS,aAAa,OAAO,UAAU;AACnC,MAAI,IAAI,IAAI,IAAI,IAAI;AACpB,MAAI,MAAM,MAAM,UAAU;AAC1B,MAAI,UAAU,IAAI,SAAS,IAAI,KAAK,IAAI,OAAO,MAAM,KAAK,MAAM,SAAS,IAAI,MAAM,IAAI,EAAE;AACzF,MAAI,YAAY,CAAC;AACb,WAAO;AACX,MAAI,SAAS,MAAM,MAAM,iBAAiB;AAC1C,SAAO,IAAI,YAAY;AAAA,IACnB,UAAU,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,aAAa,QAAQ,OAAO,SAAS,KAAK,OAAO,WAAW,UAAU,QAAQ,QAAQ,OAAO,KAAK;AAAA,IAC9K,gBAAgB,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,mBAAmB,QAAQ,OAAO,SAAS,KAAK,OAAO;AAAA,IACzI,UAAU,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,aAAa,QAAQ,OAAO,SAAS,KAAK,OAAO;AAAA,IAC7H,SAAS,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,YAAY,QAAQ,OAAO,SAAS,KAAK,OAAO;AAAA,IAC3H,YAAY,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,eAAe,QAAQ,OAAO,SAAS,KAAK,OAAO;AAAA,EACrI,CAAC;AACL;AACA,SAAS,eAAe,MAAM;AAC1B,MAAI,QAAQ,SAAS,MAAM,iBAAiB;AAC5C,SAAO,SAAS,MAAM,IAAI,cAAc,cAAc;AAC1D;AACA,SAAS,kBAAkB,MAAM;AAC7B,MAAI,QAAQ,eAAe,IAAI;AAC/B,MAAI,SAAS,SAAS,KAAK,KAAK;AAC5B,UAAM,OAAO;AACrB;AAIA,IAAM,kBAAkB,UAAQ;AAC5B,MAAI,QAAQ,KAAK,MAAM,MAAM,aAAa,KAAK;AAC/C,MAAI,SAAS,MAAM,OAAO;AACtB,QAAI,cAAc,eAAe,IAAI;AACrC,QAAI,eAAe,eAAe,KAAK,KAAK,eAAe;AACvD,UAAI,QAAQ,aAAa,KAAK,OAAO,MAAM,MAAM,IAAI;AACrD,UAAI,MAAM;AACN,aAAK,SAAS,EAAE,SAAS,eAAe,GAAG,KAAK,EAAE,CAAC;AACvD,kBAAY,MAAM;AAClB,kBAAY,OAAO;AAAA,IACvB;AAAA,EACJ,OACK;AACD,SAAK,SAAS,EAAE,SAAS;AAAA,MACjB,YAAY,GAAG,IAAI;AAAA,MACnB,QAAQ,eAAe,GAAG,aAAa,KAAK,OAAO,MAAM,MAAM,IAAI,CAAC,IAAI,YAAY,aAAa,GAAG,gBAAgB;AAAA,IACxH,EAAE,CAAC;AAAA,EACX;AACA,SAAO;AACX;AAIA,IAAM,mBAAmB,UAAQ;AAC7B,MAAI,QAAQ,KAAK,MAAM,MAAM,aAAa,KAAK;AAC/C,MAAI,CAAC,SAAS,CAAC,MAAM;AACjB,WAAO;AACX,MAAI,QAAQ,SAAS,MAAM,iBAAiB;AAC5C,MAAI,SAAS,MAAM,IAAI,SAAS,KAAK,KAAK,aAAa;AACnD,SAAK,MAAM;AACf,OAAK,SAAS,EAAE,SAAS,YAAY,GAAG,KAAK,EAAE,CAAC;AAChD,SAAO;AACX;AAUA,IAAM,eAAe;AAAA,EACjB,EAAE,KAAK,SAAS,KAAK,iBAAiB,OAAO,sBAAsB;AAAA,EACnE,EAAE,KAAK,MAAM,KAAK,UAAU,OAAO,cAAc,OAAO,uBAAuB,gBAAgB,KAAK;AAAA,EACpG,EAAE,KAAK,SAAS,KAAK,UAAU,OAAO,cAAc,OAAO,uBAAuB,gBAAgB,KAAK;AAAA,EACvG,EAAE,KAAK,UAAU,KAAK,kBAAkB,OAAO,sBAAsB;AAAA,EACrE,EAAE,KAAK,eAAe,KAAK,uBAAuB;AAAA,EAClD,EAAE,KAAK,aAAa,KAAK,SAAS;AAAA,EAClC,EAAE,KAAK,SAAS,KAAK,sBAAsB,gBAAgB,KAAK;AACpE;AACA,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,MAAM;AACd,SAAK,OAAO;AACZ,QAAI,QAAQ,KAAK,QAAQ,KAAK,MAAM,MAAM,WAAW,EAAE,MAAM;AAC7D,SAAK,SAAS,KAAK,OAAO,KAAK,IAAI;AACnC,SAAK,cAAc,MAAI,SAAS;AAAA,MAC5B,OAAO,MAAM;AAAA,MACb,aAAa,OAAO,MAAM,MAAM;AAAA,MAChC,cAAc,OAAO,MAAM,MAAM;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,cAAc;AAAA,MACd,UAAU,KAAK;AAAA,MACf,SAAS,KAAK;AAAA,IAClB,CAAC;AACD,SAAK,eAAe,MAAI,SAAS;AAAA,MAC7B,OAAO,MAAM;AAAA,MACb,aAAa,OAAO,MAAM,SAAS;AAAA,MACnC,cAAc,OAAO,MAAM,SAAS;AAAA,MACpC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU,KAAK;AAAA,MACf,SAAS,KAAK;AAAA,IAClB,CAAC;AACD,SAAK,YAAY,MAAI,SAAS;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,MACf,UAAU,KAAK;AAAA,IACnB,CAAC;AACD,SAAK,UAAU,MAAI,SAAS;AAAA,MACxB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,MACf,UAAU,KAAK;AAAA,IACnB,CAAC;AACD,SAAK,YAAY,MAAI,SAAS;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,MACf,UAAU,KAAK;AAAA,IACnB,CAAC;AACD,aAAS,OAAO,MAAM,SAAS,SAAS;AACpC,aAAO,MAAI,UAAU,EAAE,OAAO,aAAa,MAAM,SAAS,MAAM,SAAS,GAAG,OAAO;AAAA,IACvF;AACA,SAAK,MAAM,MAAI,OAAO,EAAE,WAAW,CAAC,MAAM,KAAK,QAAQ,CAAC,GAAG,OAAO,YAAY,GAAG;AAAA,MAC7E,KAAK;AAAA,MACL,OAAO,QAAQ,MAAM,SAAS,IAAI,GAAG,CAAC,OAAO,MAAM,MAAM,CAAC,CAAC;AAAA,MAC3D,OAAO,QAAQ,MAAM,aAAa,IAAI,GAAG,CAAC,OAAO,MAAM,UAAU,CAAC,CAAC;AAAA,MACnE,OAAO,UAAU,MAAM,cAAc,IAAI,GAAG,CAAC,OAAO,MAAM,KAAK,CAAC,CAAC;AAAA,MACjE,MAAI,SAAS,MAAM,CAAC,KAAK,WAAW,OAAO,MAAM,YAAY,CAAC,CAAC;AAAA,MAC/D,MAAI,SAAS,MAAM,CAAC,KAAK,SAAS,OAAO,MAAM,QAAQ,CAAC,CAAC;AAAA,MACzD,MAAI,SAAS,MAAM,CAAC,KAAK,WAAW,OAAO,MAAM,SAAS,CAAC,CAAC;AAAA,MAC5D,GAAG,KAAK,MAAM,WAAW,CAAC,IAAI;AAAA,QAC1B,MAAI,IAAI;AAAA,QACR,KAAK;AAAA,QACL,OAAO,WAAW,MAAM,YAAY,IAAI,GAAG,CAAC,OAAO,MAAM,SAAS,CAAC,CAAC;AAAA,QACpE,OAAO,cAAc,MAAM,WAAW,IAAI,GAAG,CAAC,OAAO,MAAM,aAAa,CAAC,CAAC;AAAA,MAC9E;AAAA,MACA,MAAI,UAAU;AAAA,QACV,MAAM;AAAA,QACN,SAAS,MAAM,iBAAiB,IAAI;AAAA,QACpC,cAAc,OAAO,MAAM,OAAO;AAAA,QAClC,MAAM;AAAA,MACV,GAAG,CAAC,GAAG,CAAC;AAAA,IACZ,CAAC;AAAA,EACL;AAAA,EACA,SAAS;AACL,QAAI,QAAQ,IAAI,YAAY;AAAA,MACxB,QAAQ,KAAK,YAAY;AAAA,MACzB,eAAe,KAAK,UAAU;AAAA,MAC9B,QAAQ,KAAK,QAAQ;AAAA,MACrB,WAAW,KAAK,UAAU;AAAA,MAC1B,SAAS,KAAK,aAAa;AAAA,IAC/B,CAAC;AACD,QAAI,CAAC,MAAM,GAAG,KAAK,KAAK,GAAG;AACvB,WAAK,QAAQ;AACb,WAAK,KAAK,SAAS,EAAE,SAAS,eAAe,GAAG,KAAK,EAAE,CAAC;AAAA,IAC5D;AAAA,EACJ;AAAA,EACA,QAAQ,GAAG;AACP,QAAI,iBAAiB,KAAK,MAAM,GAAG,cAAc,GAAG;AAChD,QAAE,eAAe;AAAA,IACrB,WACS,EAAE,WAAW,MAAM,EAAE,UAAU,KAAK,aAAa;AACtD,QAAE,eAAe;AACjB,OAAC,EAAE,WAAW,eAAe,UAAU,KAAK,IAAI;AAAA,IACpD,WACS,EAAE,WAAW,MAAM,EAAE,UAAU,KAAK,cAAc;AACvD,QAAE,eAAe;AACjB,kBAAY,KAAK,IAAI;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,OAAO,QAAQ;AACX,aAAS,MAAM,OAAO;AAClB,eAAS,UAAU,GAAG,SAAS;AAC3B,YAAI,OAAO,GAAG,cAAc,KAAK,CAAC,OAAO,MAAM,GAAG,KAAK,KAAK;AACxD,eAAK,SAAS,OAAO,KAAK;AAAA,MAClC;AAAA,EACR;AAAA,EACA,SAAS,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,YAAY,QAAQ,MAAM;AAC/B,SAAK,aAAa,QAAQ,MAAM;AAChC,SAAK,UAAU,UAAU,MAAM;AAC/B,SAAK,QAAQ,UAAU,MAAM;AAC7B,SAAK,UAAU,UAAU,MAAM;AAAA,EACnC;AAAA,EACA,QAAQ;AACJ,SAAK,YAAY,OAAO;AAAA,EAC5B;AAAA,EACA,IAAI,MAAM;AAAE,WAAO;AAAA,EAAI;AAAA,EACvB,IAAI,MAAM;AAAE,WAAO,KAAK,KAAK,MAAM,MAAM,iBAAiB,EAAE;AAAA,EAAK;AACrE;AACA,SAAS,OAAO,MAAMC,SAAQ;AAAE,SAAO,KAAK,MAAM,OAAOA,OAAM;AAAG;AAClE,IAAM,iBAAiB;AACvB,IAAM,QAAQ;AACd,SAAS,cAAc,MAAM,EAAE,MAAM,GAAG,GAAG;AACvC,MAAI,OAAO,KAAK,MAAM,IAAI,OAAO,IAAI,GAAG,UAAU,KAAK,MAAM,IAAI,OAAO,EAAE,EAAE;AAC5E,MAAI,QAAQ,KAAK,IAAI,KAAK,MAAM,OAAO,cAAc,GAAG,MAAM,KAAK,IAAI,SAAS,KAAK,cAAc;AACnG,MAAI,OAAO,KAAK,MAAM,SAAS,OAAO,GAAG;AACzC,MAAI,SAAS,KAAK,MAAM;AACpB,aAAS,IAAI,GAAG,IAAI,gBAAgB;AAChC,UAAI,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,CAAC,KAAK,MAAM,KAAK,KAAK,CAAC,CAAC,GAAG;AACjD,eAAO,KAAK,MAAM,CAAC;AACnB;AAAA,MACJ;AAAA,EACR;AACA,MAAI,OAAO,SAAS;AAChB,aAAS,IAAI,KAAK,SAAS,GAAG,IAAI,KAAK,SAAS,gBAAgB;AAC5D,UAAI,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,CAAC,KAAK,MAAM,KAAK,KAAK,CAAC,CAAC,GAAG;AACjD,eAAO,KAAK,MAAM,GAAG,CAAC;AACtB;AAAA,MACJ;AAAA,EACR;AACA,SAAO,WAAW,SAAS,GAAG,GAAG,KAAK,MAAM,OAAO,eAAe,CAAC,KAAK,IAAI,IAAI,KAAK,MAAM,OAAO,SAAS,CAAC,IAAI,KAAK,MAAM,GAAG;AAClI;AACA,IAAM,YAAyB,WAAW,UAAU;AAAA,EAChD,uBAAuB;AAAA,IACnB,SAAS;AAAA,IACT,UAAU;AAAA,IACV,kBAAkB;AAAA,MACd,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,IACZ;AAAA,IACA,8BAA8B;AAAA,MAC1B,QAAQ;AAAA,IACZ;AAAA,IACA,0BAA0B;AAAA,MACtB,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,MACP,UAAU;AAAA,MACV,YAAY;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,0BAA0B,EAAE,iBAAiB,YAAY;AAAA,EACzD,yBAAyB,EAAE,iBAAiB,YAAY;AAAA,EACxD,mCAAmC,EAAE,iBAAiB,YAAY;AAAA,EAClE,kCAAkC,EAAE,iBAAiB,YAAY;AACrE,CAAC;AACD,IAAM,mBAAmB;AAAA,EACrB;AAAA,EACa,KAAK,IAAI,iBAAiB;AAAA,EACvC;AACJ;;;AC5rCA,IAAM,qBAAN,MAAyB;AAAA,EACrB,YAAY,MAAM,IAAI,YAAY;AAC9B,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,aAAa;AAAA,EACtB;AACJ;AACA,IAAM,YAAN,MAAM,WAAU;AAAA,EACZ,YAAY,aAAa,OAAO,UAAU;AACtC,SAAK,cAAc;AACnB,SAAK,QAAQ;AACb,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,OAAO,KAAK,aAAa,OAAO,OAAO;AAEnC,QAAI,oBAAoB;AACxB,QAAI,mBAAmB,MAAM,MAAM,UAAU,EAAE;AAC/C,QAAI;AACA,0BAAoB,iBAAiB,mBAAmB,KAAK;AACjE,QAAI,SAAS,WAAW,IAAI,kBAAkB,IAAI,CAAC,MAAM;AAErD,aAAO,EAAE,QAAQ,EAAE,MAAO,EAAE,QAAQ,EAAE,KAAK,KAAK,MAAM,IAAI,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAC3E,WAAW,OAAO;AAAA,QAChB,QAAQ,IAAI,iBAAiB,CAAC;AAAA,QAC9B,YAAY;AAAA,MAChB,CAAC,EAAE,MAAM,EAAE,IAAI,IACb,WAAW,KAAK;AAAA,QACd,YAAY,EAAE,OAAO,+BAA+B,EAAE,YAAY,EAAE,YAAY,MAAM,EAAE,YAAY,IAAI;AAAA,QACxG,YAAY;AAAA,MAChB,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;AAAA,IAC7B,CAAC,GAAG,IAAI;AACR,WAAO,IAAI,WAAU,QAAQ,OAAO,eAAe,MAAM,CAAC;AAAA,EAC9D;AACJ;AACA,SAAS,eAAe,aAAa,aAAa,MAAM,QAAQ,GAAG;AAC/D,MAAI,QAAQ;AACZ,cAAY,QAAQ,OAAO,KAAK,CAAC,MAAM,IAAI,EAAE,KAAK,MAAM;AACpD,QAAI,cAAc,KAAK,cAAc;AACjC;AACJ,YAAQ,IAAI,mBAAmB,MAAM,IAAI,KAAK,UAAU;AACxD,WAAO;AAAA,EACX,CAAC;AACD,SAAO;AACX;AACA,SAAS,YAAY,IAAI,SAAS;AAC9B,MAAI,OAAO,QAAQ,KAAK,KAAK,QAAQ,OAAO;AAC5C,MAAI,SAAS,GAAG,MAAM,MAAM,UAAU,EAAE,OAAO,IAAI,MAAM,EAAE;AAC3D,MAAI,UAAU;AACV,WAAO;AACX,MAAI,OAAO,GAAG,WAAW,IAAI,OAAO,QAAQ,GAAG;AAC/C,SAAO,CAAC,EAAE,GAAG,QAAQ,KAAK,OAAK,EAAE,GAAG,oBAAoB,CAAC,KAAK,GAAG,QAAQ,aAAa,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC;AAC1H;AACA,SAAS,gBAAgB,OAAO,SAAS;AACrC,SAAO,MAAM,MAAM,WAAW,KAAK,IAAI,UAAU,QAAQ,OAAO,YAAY,aAAa,GAAG,cAAc,CAAC;AAC/G;AAMA,SAAS,eAAe,OAAO,aAAa;AACxC,SAAO;AAAA,IACH,SAAS,gBAAgB,OAAO,CAAC,qBAAqB,GAAG,WAAW,CAAC,CAAC;AAAA,EAC1E;AACJ;AAKA,IAAM,uBAAoC,YAAY,OAAO;AAC7D,IAAMC,eAA2B,YAAY,OAAO;AACpD,IAAM,qBAAkC,YAAY,OAAO;AAC3D,IAAM,YAAyB,WAAW,OAAO;AAAA,EAC7C,SAAS;AACL,WAAO,IAAI,UAAU,WAAW,MAAM,MAAM,IAAI;AAAA,EACpD;AAAA,EACA,OAAO,OAAO,IAAI;AACd,QAAI,GAAG,cAAc,MAAM,YAAY,MAAM;AACzC,UAAI,SAAS,MAAM,YAAY,IAAI,GAAG,OAAO,GAAG,WAAW,MAAM,QAAQ,MAAM;AAC/E,UAAI,MAAM,UAAU;AAChB,YAAI,SAAS,GAAG,QAAQ,OAAO,MAAM,SAAS,MAAM,CAAC;AACrD,mBAAW,eAAe,QAAQ,MAAM,SAAS,YAAY,MAAM,KAAK,eAAe,QAAQ,MAAM,MAAM;AAAA,MAC/G;AACA,UAAI,CAAC,OAAO,QAAQ,SAAS,GAAG,MAAM,MAAM,UAAU,EAAE;AACpD,gBAAQ;AACZ,cAAQ,IAAI,UAAU,QAAQ,OAAO,QAAQ;AAAA,IACjD;AACA,aAAS,UAAU,GAAG,SAAS;AAC3B,UAAI,OAAO,GAAG,oBAAoB,GAAG;AACjC,YAAI,QAAQ,CAAC,GAAG,MAAM,MAAM,UAAU,EAAE,YAAY,MAAM,QAAQ,OAAO,MAAM,SAAS,UAAU,OAAO;AACzG,gBAAQ,UAAU,KAAK,OAAO,OAAO,OAAO,GAAG,KAAK;AAAA,MACxD,WACS,OAAO,GAAGA,YAAW,GAAG;AAC7B,gBAAQ,IAAI,UAAU,MAAM,aAAa,OAAO,QAAQ,UAAU,OAAO,MAAM,MAAM,QAAQ;AAAA,MACjG,WACS,OAAO,GAAG,kBAAkB,GAAG;AACpC,gBAAQ,IAAI,UAAU,MAAM,aAAa,MAAM,OAAO,OAAO,KAAK;AAAA,MACtE;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,SAAS,OAAK;AAAA,IAAC,UAAU,KAAK,GAAG,SAAO,IAAI,KAAK;AAAA,IAC7C,WAAW,YAAY,KAAK,GAAG,OAAK,EAAE,WAAW;AAAA,EAAC;AAC1D,CAAC;AAQD,IAAM,aAA0B,WAAW,KAAK,EAAE,OAAO,mCAAmC,CAAC;AAC7F,SAAS,YAAY,MAAM,KAAK,MAAM;AAClC,MAAI,EAAE,YAAY,IAAI,KAAK,MAAM,MAAM,SAAS;AAChD,MAAI,QAAQ,CAAC,GAAG,aAAa,KAAK,WAAW;AAC7C,cAAY,QAAQ,OAAO,OAAO,IAAI,IAAI,IAAI,OAAO,OAAO,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,KAAK,MAAM;AAC5F,QAAI,OAAO,QAAQ,OAAO,OACrB,QAAQ,OAAQ,MAAM,QAAQ,OAAO,OAAO,MAAM,MAAM,OAAO,KAAM;AACtE,YAAM,KAAK,KAAK,UAAU;AAC1B,mBAAa,KAAK,IAAI,MAAM,UAAU;AACtC,iBAAW,KAAK,IAAI,IAAI,QAAQ;AAAA,IACpC;AAAA,EACJ,CAAC;AACD,MAAI,mBAAmB,KAAK,MAAM,MAAM,UAAU,EAAE;AACpD,MAAI;AACA,YAAQ,iBAAiB,OAAO,KAAK,KAAK;AAC9C,MAAI,CAAC,MAAM;AACP,WAAO;AACX,SAAO;AAAA,IACH,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO,KAAK,MAAM,IAAI,OAAO,UAAU,EAAE,KAAK;AAAA,IAC9C,SAAS;AACL,aAAO,EAAE,KAAK,mBAAmB,MAAM,KAAK,EAAE;AAAA,IAClD;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmB,MAAM,aAAa;AAC3C,SAAO,MAAI,MAAM,EAAE,OAAO,kBAAkB,GAAG,YAAY,IAAI,OAAK,iBAAiB,MAAM,GAAG,KAAK,CAAC,CAAC;AACzG;AAIA,IAAM,gBAAgB,CAAC,SAAS;AAC5B,MAAI,QAAQ,KAAK,MAAM,MAAM,WAAW,KAAK;AAC7C,MAAI,CAAC,SAAS,CAAC,MAAM;AACjB,SAAK,SAAS,EAAE,SAAS,gBAAgB,KAAK,OAAO,CAACC,aAAY,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC;AAClF,MAAI,QAAQ,SAAS,MAAM,UAAU,IAAI;AACzC,MAAI;AACA,UAAM,IAAI,cAAc,mBAAmB,EAAE,MAAM;AACvD,SAAO;AACX;AAIA,IAAM,iBAAiB,CAAC,SAAS;AAC7B,MAAI,QAAQ,KAAK,MAAM,MAAM,WAAW,KAAK;AAC7C,MAAI,CAAC,SAAS,CAAC,MAAM;AACjB,WAAO;AACX,OAAK,SAAS,EAAE,SAASA,aAAY,GAAG,KAAK,EAAE,CAAC;AAChD,SAAO;AACX;AAIA,IAAM,iBAAiB,CAAC,SAAS;AAC7B,MAAI,QAAQ,KAAK,MAAM,MAAM,WAAW,KAAK;AAC7C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,MAAM,KAAK,MAAM,UAAU,MAAM,OAAO,MAAM,YAAY,KAAK,IAAI,KAAK,CAAC;AAC7E,MAAI,CAAC,KAAK,OAAO;AACb,WAAO,MAAM,YAAY,KAAK,CAAC;AAC/B,QAAI,CAAC,KAAK,SAAS,KAAK,QAAQ,IAAI,QAAQ,KAAK,MAAM,IAAI;AACvD,aAAO;AAAA,EACf;AACA,OAAK,SAAS,EAAE,WAAW,EAAE,QAAQ,KAAK,MAAM,MAAM,KAAK,GAAG,GAAG,gBAAgB,KAAK,CAAC;AACvF,SAAO;AACX;AA+BA,IAAM,aAAa;AAAA,EACf,EAAE,KAAK,eAAe,KAAK,eAAe,gBAAgB,KAAK;AAAA,EAC/D,EAAE,KAAK,MAAM,KAAK,eAAe;AACrC;AACA,IAAM,aAA0B,WAAW,UAAU,MAAM;AAAA,EACvD,YAAY,MAAM;AACd,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,MAAM;AACX,QAAI,EAAE,MAAM,IAAI,KAAK,MAAM,MAAM,UAAU;AAC3C,SAAK,WAAW,KAAK,IAAI,IAAI;AAC7B,SAAK,MAAM,KAAK,IAAI,KAAK,IAAI;AAC7B,SAAK,UAAU,WAAW,KAAK,KAAK,KAAK;AAAA,EAC7C;AAAA,EACA,MAAM;AACF,iBAAa,KAAK,OAAO;AACzB,QAAI,MAAM,KAAK,IAAI;AACnB,QAAI,MAAM,KAAK,WAAW,IAAI;AAC1B,WAAK,UAAU,WAAW,KAAK,KAAK,KAAK,WAAW,GAAG;AAAA,IAC3D,OACK;AACD,WAAK,MAAM;AACX,UAAI,EAAE,MAAM,IAAI,KAAK,MAAM,EAAE,QAAQ,IAAI,MAAM,MAAM,UAAU;AAC/D,UAAI,QAAQ;AACR,gBAAQ,IAAI,QAAQ,IAAI,YAAU,QAAQ,QAAQ,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,iBAAe;AACvF,cAAI,MAAM,YAAY,OAAO,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,CAAC;AAClD,cAAI,KAAK,KAAK,MAAM,OAAO,MAAM;AAC7B,iBAAK,KAAK,SAAS,eAAe,KAAK,KAAK,OAAO,GAAG,CAAC;AAAA,QAC/D,GAAG,WAAS;AAAE,uBAAa,KAAK,KAAK,OAAO,KAAK;AAAA,QAAG,CAAC;AAAA,IAC7D;AAAA,EACJ;AAAA,EACA,OAAO,QAAQ;AACX,QAAI,SAAS,OAAO,MAAM,MAAM,UAAU;AAC1C,QAAI,OAAO,cAAc,UAAU,OAAO,WAAW,MAAM,UAAU,KACjE,OAAO,gBAAgB,OAAO,aAAa,MAAM,GAAG;AACpD,WAAK,WAAW,KAAK,IAAI,IAAI,OAAO;AACpC,UAAI,CAAC,KAAK,KAAK;AACX,aAAK,MAAM;AACX,aAAK,UAAU,WAAW,KAAK,KAAK,OAAO,KAAK;AAAA,MACpD;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,QAAQ;AACJ,QAAI,KAAK,KAAK;AACV,WAAK,WAAW,KAAK,IAAI;AACzB,WAAK,IAAI;AAAA,IACb;AAAA,EACJ;AAAA,EACA,UAAU;AACN,iBAAa,KAAK,OAAO;AAAA,EAC7B;AACJ,CAAC;AACD,IAAM,aAA0B,MAAM,OAAO;AAAA,EACzC,QAAQ,OAAO;AACX,WAAO,OAAO,OAAO,EAAE,SAAS,MAAM,IAAI,OAAK,EAAE,MAAM,EAAE,OAAO,CAAAC,OAAKA,MAAK,IAAI,EAAE,GAAG,cAAc,MAAM,IAAI,OAAK,EAAE,MAAM,GAAG;AAAA,MACvH,OAAO;AAAA,MACP,cAAc;AAAA,MACd,eAAe;AAAA,MACf,cAAc;AAAA,MACd,QAAQ,MAAM;AAAA,IAClB,GAAG;AAAA,MACC,cAAc,CAAC,GAAG,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,OAAK,EAAE,CAAC,KAAK,EAAE,CAAC;AAAA,IAC9D,CAAC,CAAC;AAAA,EACN;AACJ,CAAC;AAuBD,SAAS,WAAW,SAAS;AACzB,MAAI,WAAW,CAAC;AAChB,MAAI;AACA,YAAS,UAAS,EAAE,KAAK,KAAK,SAAS;AACnC,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,YAAI,KAAK,KAAK,CAAC;AACf,YAAI,WAAW,KAAK,EAAE,KAAK,CAAC,SAAS,KAAK,OAAK,EAAE,YAAY,KAAK,GAAG,YAAY,CAAC,GAAG;AACjF,mBAAS,KAAK,EAAE;AAChB,mBAAS;AAAA,QACb;AAAA,MACJ;AACA,eAAS,KAAK,EAAE;AAAA,IACpB;AACJ,SAAO;AACX;AACA,SAAS,iBAAiB,MAAM,YAAY,SAAS;AACjD,MAAI;AACJ,MAAI,OAAO,UAAU,WAAW,WAAW,OAAO,IAAI,CAAC;AACvD,SAAO,MAAI,MAAM,EAAE,OAAO,iCAAiC,WAAW,SAAS,GAAG,MAAI,QAAQ,EAAE,OAAO,oBAAoB,GAAG,WAAW,gBAAgB,WAAW,cAAc,IAAI,IAAI,WAAW,OAAO,IAAI,KAAK,WAAW,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,CAAC,QAAQ,MAAM;AAChS,QAAI,QAAQ,OAAO,QAAQ,CAAC,MAAM;AAC9B,QAAE,eAAe;AACjB,UAAI;AACA;AACJ,cAAQ;AACR,UAAI,QAAQ,eAAe,KAAK,MAAM,MAAM,SAAS,EAAE,aAAa,UAAU;AAC9E,UAAI;AACA,eAAO,MAAM,MAAM,MAAM,MAAM,MAAM,EAAE;AAAA,IAC/C;AACA,QAAI,EAAE,KAAK,IAAI,QAAQ,WAAW,KAAK,CAAC,IAAI,KAAK,QAAQ,KAAK,CAAC,CAAC,IAAI;AACpE,QAAI,UAAU,WAAW,IAAI,OAAO;AAAA,MAAC,KAAK,MAAM,GAAG,QAAQ;AAAA,MACvD,MAAI,KAAK,KAAK,MAAM,UAAU,WAAW,CAAC,CAAC;AAAA,MAC3C,KAAK,MAAM,WAAW,CAAC;AAAA,IAAC;AAC5B,WAAO,MAAI,UAAU;AAAA,MACjB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,aAAa;AAAA,MACb,cAAc,YAAY,IAAI,GAAG,WAAW,IAAI,KAAK,iBAAiB,KAAK,CAAC,CAAC,IAAI;AAAA,IACrF,GAAG,OAAO;AAAA,EACd,CAAC,GAAG,WAAW,UAAU,MAAI,OAAO,EAAE,OAAO,sBAAsB,GAAG,WAAW,MAAM,CAAC;AAC5F;AACA,IAAM,mBAAN,cAA+B,WAAW;AAAA,EACtC,YAAY,YAAY;AACpB,UAAM;AACN,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,GAAG,OAAO;AAAE,WAAO,MAAM,cAAc,KAAK;AAAA,EAAY;AAAA,EACxD,QAAQ;AACJ,WAAO,MAAI,QAAQ,EAAE,OAAO,+BAA+B,KAAK,WAAW,SAAS,CAAC;AAAA,EACzF;AACJ;AACA,IAAM,YAAN,MAAgB;AAAA,EACZ,YAAY,MAAM,YAAY;AAC1B,SAAK,aAAa;AAClB,SAAK,KAAK,UAAU,KAAK,MAAM,KAAK,OAAO,IAAI,UAAU,EAAE,SAAS,EAAE;AACtE,SAAK,MAAM,iBAAiB,MAAM,YAAY,IAAI;AAClD,SAAK,IAAI,KAAK,KAAK;AACnB,SAAK,IAAI,aAAa,QAAQ,QAAQ;AAAA,EAC1C;AACJ;AACA,IAAM,YAAN,MAAM,WAAU;AAAA,EACZ,YAAY,MAAM;AACd,SAAK,OAAO;AACZ,SAAK,QAAQ,CAAC;AACd,QAAI,YAAY,CAAC,UAAU;AACvB,UAAI,MAAM,WAAW,IAAI;AACrB,uBAAe,KAAK,IAAI;AACxB,aAAK,KAAK,MAAM;AAAA,MACpB,WACS,MAAM,WAAW,MAAM,MAAM,WAAW,IAAI;AACjD,aAAK,eAAe,KAAK,gBAAgB,IAAI,KAAK,MAAM,UAAU,KAAK,MAAM,MAAM;AAAA,MACvF,WACS,MAAM,WAAW,MAAM,MAAM,WAAW,IAAI;AACjD,aAAK,eAAe,KAAK,gBAAgB,KAAK,KAAK,MAAM,MAAM;AAAA,MACnE,WACS,MAAM,WAAW,IAAI;AAC1B,aAAK,cAAc,CAAC;AAAA,MACxB,WACS,MAAM,WAAW,IAAI;AAC1B,aAAK,cAAc,KAAK,MAAM,SAAS,CAAC;AAAA,MAC5C,WACS,MAAM,WAAW,IAAI;AAC1B,aAAK,KAAK,MAAM;AAAA,MACpB,WACS,MAAM,WAAW,MAAM,MAAM,WAAW,MAAM,KAAK,iBAAiB,GAAG;AAC5E,YAAI,EAAE,WAAW,IAAI,KAAK,MAAM,KAAK,aAAa,GAAG,OAAO,WAAW,WAAW,OAAO;AACzF,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ;AAC7B,cAAI,KAAK,CAAC,EAAE,YAAY,EAAE,WAAW,CAAC,KAAK,MAAM,SAAS;AACtD,gBAAI,QAAQ,eAAe,KAAK,KAAK,MAAM,MAAM,SAAS,EAAE,aAAa,UAAU;AACnF,gBAAI;AACA,yBAAW,QAAQ,CAAC,EAAE,MAAM,MAAM,MAAM,MAAM,MAAM,EAAE;AAAA,UAC9D;AAAA,MACR,OACK;AACD;AAAA,MACJ;AACA,YAAM,eAAe;AAAA,IACzB;AACA,QAAI,UAAU,CAAC,UAAU;AACrB,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,YAAI,KAAK,MAAM,CAAC,EAAE,IAAI,SAAS,MAAM,MAAM;AACvC,eAAK,cAAc,CAAC;AAAA,MAC5B;AAAA,IACJ;AACA,SAAK,OAAO,MAAI,MAAM;AAAA,MAClB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,cAAc,KAAK,KAAK,MAAM,OAAO,aAAa;AAAA,MAClD;AAAA,MACA;AAAA,IACJ,CAAC;AACD,SAAK,MAAM,MAAI,OAAO,EAAE,OAAO,gBAAgB,GAAG,KAAK,MAAM,MAAI,UAAU;AAAA,MACvE,MAAM;AAAA,MACN,MAAM;AAAA,MACN,cAAc,KAAK,KAAK,MAAM,OAAO,OAAO;AAAA,MAC5C,SAAS,MAAM,eAAe,KAAK,IAAI;AAAA,IAC3C,GAAG,GAAG,CAAC;AACP,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,gBAAgB;AAChB,QAAI,WAAW,KAAK,KAAK,MAAM,MAAM,SAAS,EAAE;AAChD,QAAI,CAAC;AACD,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ;AACnC,UAAI,KAAK,MAAM,CAAC,EAAE,cAAc,SAAS;AACrC,eAAO;AACf,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,QAAI,EAAE,aAAa,SAAS,IAAI,KAAK,KAAK,MAAM,MAAM,SAAS;AAC/D,QAAI,IAAI,GAAG,YAAY,OAAO,kBAAkB;AAChD,gBAAY,QAAQ,GAAG,KAAK,KAAK,MAAM,IAAI,QAAQ,CAAC,QAAQ,MAAM,EAAE,KAAK,MAAM;AAC3E,UAAI,QAAQ,IAAI;AAChB,eAASC,KAAI,GAAGA,KAAI,KAAK,MAAM,QAAQA;AACnC,YAAI,KAAK,MAAMA,EAAC,EAAE,cAAc,KAAK,YAAY;AAC7C,kBAAQA;AACR;AAAA,QACJ;AACJ,UAAI,QAAQ,GAAG;AACX,eAAO,IAAI,UAAU,KAAK,MAAM,KAAK,UAAU;AAC/C,aAAK,MAAM,OAAO,GAAG,GAAG,IAAI;AAC5B,oBAAY;AAAA,MAChB,OACK;AACD,eAAO,KAAK,MAAM,KAAK;AACvB,YAAI,QAAQ,GAAG;AACX,eAAK,MAAM,OAAO,GAAG,QAAQ,CAAC;AAC9B,sBAAY;AAAA,QAChB;AAAA,MACJ;AACA,UAAI,YAAY,KAAK,cAAc,SAAS,YAAY;AACpD,YAAI,CAAC,KAAK,IAAI,aAAa,eAAe,GAAG;AACzC,eAAK,IAAI,aAAa,iBAAiB,MAAM;AAC7C,4BAAkB;AAAA,QACtB;AAAA,MACJ,WACS,KAAK,IAAI,aAAa,eAAe,GAAG;AAC7C,aAAK,IAAI,gBAAgB,eAAe;AAAA,MAC5C;AACA;AAAA,IACJ,CAAC;AACD,WAAO,IAAI,KAAK,MAAM,UAAU,EAAE,KAAK,MAAM,UAAU,KAAK,KAAK,MAAM,CAAC,EAAE,WAAW,OAAO,IAAI;AAC5F,kBAAY;AACZ,WAAK,MAAM,IAAI;AAAA,IACnB;AACA,QAAI,KAAK,MAAM,UAAU,GAAG;AACxB,WAAK,MAAM,KAAK,IAAI,UAAU,KAAK,MAAM;AAAA,QACrC,MAAM;AAAA,QAAI,IAAI;AAAA,QACd,UAAU;AAAA,QACV,SAAS,KAAK,KAAK,MAAM,OAAO,gBAAgB;AAAA,MACpD,CAAC,CAAC;AACF,kBAAY;AAAA,IAChB;AACA,QAAI,iBAAiB;AACjB,WAAK,KAAK,aAAa,yBAAyB,gBAAgB,EAAE;AAClE,WAAK,KAAK,eAAe;AAAA,QACrB,KAAK;AAAA,QACL,MAAM,OAAO,EAAE,KAAK,gBAAgB,IAAI,sBAAsB,GAAG,OAAO,KAAK,KAAK,sBAAsB,EAAE;AAAA,QAC1G,OAAO,CAAC,EAAE,KAAK,MAAM,MAAM;AACvB,cAAI,SAAS,MAAM,SAAS,KAAK,KAAK;AACtC,cAAI,IAAI,MAAM,MAAM;AAChB,iBAAK,KAAK,cAAc,MAAM,MAAM,IAAI,OAAO;AAAA,mBAC1C,IAAI,SAAS,MAAM;AACxB,iBAAK,KAAK,cAAc,IAAI,SAAS,MAAM,UAAU;AAAA,QAC7D;AAAA,MACJ,CAAC;AAAA,IACL,WACS,KAAK,gBAAgB,GAAG;AAC7B,WAAK,KAAK,gBAAgB,uBAAuB;AAAA,IACrD;AACA,QAAI;AACA,WAAK,KAAK;AAAA,EAClB;AAAA,EACA,OAAO;AACH,QAAI,SAAS,KAAK,KAAK;AACvB,aAAS,KAAK;AACV,UAAI,OAAO;AACX,eAAS,KAAK;AACd,WAAK,OAAO;AAAA,IAChB;AACA,aAAS,QAAQ,KAAK,OAAO;AACzB,UAAI,KAAK,IAAI,cAAc,KAAK,MAAM;AAClC,eAAO,UAAU,KAAK;AAClB,aAAG;AACP,iBAAS,KAAK,IAAI;AAAA,MACtB,OACK;AACD,aAAK,KAAK,aAAa,KAAK,KAAK,MAAM;AAAA,MAC3C;AAAA,IACJ;AACA,WAAO;AACH,SAAG;AAAA,EACX;AAAA,EACA,cAAc,eAAe;AACzB,QAAI,KAAK,gBAAgB;AACrB;AACJ,QAAI,QAAQ,KAAK,KAAK,MAAM,MAAM,SAAS;AAC3C,QAAI,YAAY,eAAe,MAAM,aAAa,KAAK,MAAM,aAAa,EAAE,UAAU;AACtF,QAAI,CAAC;AACD;AACJ,SAAK,KAAK,SAAS;AAAA,MACf,WAAW,EAAE,QAAQ,UAAU,MAAM,MAAM,UAAU,GAAG;AAAA,MACxD,gBAAgB;AAAA,MAChB,SAAS,mBAAmB,GAAG,SAAS;AAAA,IAC5C,CAAC;AAAA,EACL;AAAA,EACA,OAAO,KAAK,MAAM;AAAE,WAAO,IAAI,WAAU,IAAI;AAAA,EAAG;AACpD;AACA,SAAS,IAAI,SAAS,QAAQ,uBAAuB;AACjD,SAAO,mEAAmE,KAAK,IAAI,mBAAmB,OAAO,CAAC;AAClH;AACA,SAAS,UAAU,OAAO;AACtB,SAAO,IAAI,qDAAqD,KAAK,qCAAqC,sBAAsB;AACpI;AACA,IAAMC,aAAyB,WAAW,UAAU;AAAA,EAChD,kBAAkB;AAAA,IACd,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,EAChB;AAAA,EACA,wBAAwB,EAAE,YAAY,iBAAiB;AAAA,EACvD,0BAA0B,EAAE,YAAY,mBAAmB;AAAA,EAC3D,uBAAuB,EAAE,YAAY,iBAAiB;AAAA,EACtD,uBAAuB,EAAE,YAAY,iBAAiB;AAAA,EACtD,wBAAwB;AAAA,IACpB,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,OAAO;AAAA,IACP,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,QAAQ;AAAA,EACZ;AAAA,EACA,wBAAwB;AAAA,IACpB,UAAU;AAAA,IACV,SAAS;AAAA,EACb;AAAA,EACA,iBAAiB;AAAA,IACb,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,IAClB,eAAe;AAAA,EACnB;AAAA,EACA,uBAAuB,EAAE,iBAA8B,UAAU,MAAM,EAAE;AAAA,EACzE,yBAAyB,EAAE,iBAA8B,UAAU,QAAQ,EAAE;AAAA,EAC7E,sBAAsB,EAAE,iBAA8B,UAAU,MAAM,EAAE;AAAA,EACxE,sBAAsB,EAAE,iBAA8B,UAAU,MAAM,EAAE;AAAA,EACxE,wBAAwB,EAAE,iBAAiB,YAAY;AAAA,EACvD,oBAAoB;AAAA,IAChB,SAAS;AAAA,IACT,QAAQ;AAAA,EACZ;AAAA,EACA,iBAAiB;AAAA,IACb,UAAU;AAAA,IACV,WAAW;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,cAAc;AAAA,IAClB;AAAA,EACJ;AAAA,EACA,yBAAyB;AAAA,IACrB,WAAW,EAAE,mBAAmB,SAAS;AAAA,EAC7C;AAAA,EACA,sBAAsB;AAAA,IAClB,WAAW,EAAE,mBAAmB,OAAO;AAAA,EAC3C;AAAA,EACA,sBAAsB;AAAA,IAClB,WAAW,EAAE,mBAAmB,OAAO;AAAA,EAC3C;AAAA,EACA,2BAA2B;AAAA,IACvB,UAAU;AAAA,IACV,QAAQ;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,qBAAqB;AAAA,QACjB,iBAAiB;AAAA,QACjB,OAAO,EAAE,gBAAgB,YAAY;AAAA,MACzC;AAAA,MACA,2BAA2B;AAAA,QACvB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACX;AAAA,MACA,OAAO,EAAE,gBAAgB,OAAO;AAAA,MAChC,SAAS;AAAA,MACT,QAAQ;AAAA,IACZ;AAAA,IACA,kBAAkB;AAAA,MACd,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,IACZ;AAAA,EACJ;AACJ,CAAC;AACD,SAAS,eAAe,KAAK;AACzB,SAAO,OAAO,UAAU,IAAI,OAAO,YAAY,IAAI,OAAO,SAAS,IAAI;AAC3E;AACA,IAAM,mBAAN,cAA+B,aAAa;AAAA,EACxC,YAAY,aAAa;AACrB,UAAM;AACN,SAAK,cAAc;AACnB,SAAK,WAAW,YAAY,OAAO,CAAC,KAAK,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,QAAQ,IAAI,EAAE,WAAW,KAAK,MAAM;AAAA,EAC9H;AAAA,EACA,MAAM,MAAM;AACR,QAAI,MAAM,SAAS,cAAc,KAAK;AACtC,QAAI,YAAY,mCAAmC,KAAK;AACxD,QAAI,cAAc,KAAK;AACvB,QAAI,oBAAoB,KAAK,MAAM,MAAM,gBAAgB,EAAE;AAC3D,QAAI;AACA,oBAAc,kBAAkB,aAAa,KAAK,KAAK;AAC3D,QAAI,YAAY;AACZ,UAAI,cAAc,MAAM,sBAAsB,MAAM,KAAK,WAAW;AACxE,WAAO;AAAA,EACX;AACJ;AACA,SAAS,aAAa,MAAM,QAAQ;AAChC,MAAI,YAAY,CAAC,UAAU;AACvB,QAAI,OAAO,OAAO,sBAAsB;AACxC,QAAI,MAAM,UAAU,KAAK,OAAO,MAAyB,MAAM,UAAU,KAAK,QAAQ,MAClF,MAAM,UAAU,KAAK,MAAM,MAAyB,MAAM,UAAU,KAAK,SAAS;AAClF;AACJ,aAAS,SAAS,MAAM,QAAQ,QAAQ,SAAS,OAAO,YAAY;AAChE,UAAI,OAAO,YAAY,KAAK,OAAO,UAAU,SAAS,iBAAiB;AACnE;AAAA,IACR;AACA,WAAO,oBAAoB,aAAa,SAAS;AACjD,QAAI,KAAK,MAAM,MAAM,iBAAiB;AAClC,WAAK,SAAS,EAAE,SAAS,qBAAqB,GAAG,IAAI,EAAE,CAAC;AAAA,EAChE;AACA,SAAO,iBAAiB,aAAa,SAAS;AAClD;AACA,SAAS,sBAAsB,MAAM,QAAQ,aAAa;AACtD,WAAS,UAAU;AACf,QAAI,OAAO,KAAK,gBAAgB,OAAO,sBAAsB,EAAE,MAAM,IAAI,KAAK,WAAW;AACzF,UAAM,UAAU,KAAK,YAAY,KAAK,IAAI;AAC1C,QAAI,SAAS;AACT,WAAK,SAAS,EAAE,SAAS,qBAAqB,GAAG;AAAA,QACzC,KAAK,KAAK;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AACL,iBAAO;AAAA,YACH,KAAK,mBAAmB,MAAM,WAAW;AAAA,YACzC,WAAW,MAAM,OAAO,sBAAsB;AAAA,UAClD;AAAA,QACJ;AAAA,MACJ,CAAC,EAAE,CAAC;AAAA,IACZ;AACA,WAAO,aAAa,OAAO,cAAc;AACzC,iBAAa,MAAM,MAAM;AAAA,EAC7B;AACA,MAAI,EAAE,UAAU,IAAI,KAAK,MAAM,MAAM,gBAAgB;AACrD,MAAI,eAAe,WAAW,SAAS,SAAS;AAChD,SAAO,aAAa,MAAM;AACtB,iBAAa,YAAY;AACzB,WAAO,aAAa,OAAO,cAAc;AAAA,EAC7C;AACA,SAAO,cAAc,MAAM;AACvB,iBAAa,YAAY;AACzB,mBAAe,WAAW,SAAS,SAAS;AAAA,EAChD;AACJ;AACA,SAAS,sBAAsB,KAAK,aAAa;AAC7C,MAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,WAAS,cAAc,aAAa;AAChC,QAAI,OAAO,IAAI,OAAO,WAAW,IAAI;AACrC,KAAC,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU;AAAA,EACnE;AACA,MAAI,UAAU,CAAC;AACf,WAAS,QAAQ,QAAQ;AACrB,YAAQ,KAAK,IAAI,iBAAiB,OAAO,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC;AAAA,EAChE;AACA,SAAO,SAAS,GAAG,SAAS,IAAI;AACpC;AACA,IAAM,sBAAmC,OAAO;AAAA,EAC5C,OAAO;AAAA,EACP,SAAS,UAAQ,KAAK,MAAM,MAAM,iBAAiB;AACvD,CAAC;AACD,IAAM,oBAAiC,WAAW,OAAO;AAAA,EACrD,SAAS;AACL,WAAO,SAAS;AAAA,EACpB;AAAA,EACA,OAAO,SAAS,IAAI;AAChB,cAAU,QAAQ,IAAI,GAAG,OAAO;AAChC,QAAI,mBAAmB,GAAG,MAAM,MAAM,gBAAgB,EAAE;AACxD,aAAS,UAAU,GAAG,SAAS;AAC3B,UAAI,OAAO,GAAG,oBAAoB,GAAG;AACjC,YAAI,cAAc,OAAO;AACzB,YAAI;AACA,wBAAc,iBAAiB,eAAe,CAAC,GAAG,GAAG,KAAK;AAC9D,kBAAU,sBAAsB,GAAG,MAAM,KAAK,YAAY,MAAM,CAAC,CAAC;AAAA,MACtE;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ,CAAC;AACD,IAAM,uBAAoC,YAAY,OAAO;AAC7D,IAAM,oBAAiC,WAAW,OAAO;AAAA,EACrD,SAAS;AAAE,WAAO;AAAA,EAAM;AAAA,EACxB,OAAO,SAAS,IAAI;AAChB,QAAI,WAAW,GAAG;AACd,gBAAU,YAAY,IAAI,OAAO,IAAI,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,KAAK,GAAG,QAAQ,OAAO,QAAQ,GAAG,EAAE,CAAC;AACjI,WAAO,GAAG,QAAQ,OAAO,CAAC,GAAG,MAAM,EAAE,GAAG,oBAAoB,IAAI,EAAE,QAAQ,GAAG,OAAO;AAAA,EACxF;AAAA,EACA,SAAS,WAAS,YAAY,KAAK,KAAK;AAC5C,CAAC;AACD,IAAM,kBAA+B,WAAW,UAAU;AAAA,EACtD,mBAAmB;AAAA,IACf,OAAO;AAAA,IACP,uBAAuB;AAAA,MACnB,SAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,mBAAmB;AAAA,IACf,OAAO;AAAA,IACP,QAAQ;AAAA,EACZ;AAAA,EACA,wBAAwB;AAAA,IACpB,SAAsB,IAAI,sGAAsG;AAAA,EACpI;AAAA,EACA,2BAA2B;AAAA,IACvB,SAAsB,IAAI,kGAAkG;AAAA,EAChI;AAAA,EACA,yBAAyB;AAAA,IACrB,SAAsB,IAAI,6EAA6E;AAAA,EAC3G;AACJ,CAAC;AACD,IAAM,iBAAiB;AAAA,EACnB;AAAA,EACa,WAAW,YAAY,QAAQ,CAAC,SAAS,GAAG,WAAS;AAC9D,QAAI,EAAE,UAAU,MAAM,IAAI,MAAM,MAAM,SAAS;AAC/C,WAAO,CAAC,YAAY,CAAC,SAAS,SAAS,QAAQ,SAAS,KAAK,WAAW,OAAO,WAAW,IAAI;AAAA,MAC1F,WAAW,MAAM,SAAS,MAAM,SAAS,EAAE;AAAA,IAC/C,CAAC;AAAA,EACL,CAAC;AAAA,EACY,aAAa,aAAa,EAAE,QAAQ,YAAY,CAAC;AAAA,EAC9DA;AACJ;AACA,IAAM,mBAAgC,MAAM,OAAO;AAAA,EAC/C,QAAQ,SAAS;AACb,WAAO,cAAc,SAAS;AAAA,MAC1B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,eAAe;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;;;ACvtBD,IAAM,cAA2B,MAAM;AAAA,EACnC,YAAY;AAAA,EACZ,0BAA0B;AAAA,EAC1B,sBAAsB;AAAA,EACtB,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY,wBAAwB,GAAG,IAAI;AAAA,EAC3C,cAAc;AAAA,EACd,mBAAmB,uBAAuB,EAAE,UAAU,KAAK,CAAC;AAAA,EAC5D,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,eAAe;AAAA,EACf,qBAAqB;AAAA,EACrB,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,0BAA0B;AAAA,EAC1B,OAAO,GAAG;AAAA,IACN,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACP,CAAC;AACL,GAAG;AASH,IAAM,gBAA6B,MAAM;AAAA,EACrC,sBAAsB;AAAA,EACtB,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,mBAAmB,uBAAuB,EAAE,UAAU,KAAK,CAAC;AAAA,EAC5D,OAAO,GAAG;AAAA,IACN,GAAG;AAAA,IACH,GAAG;AAAA,EACP,CAAC;AACL,GAAG;;;ACzFU,IAAAC,KAAiDC,OAAOC,OAAO,EAC1EC,WAAAA,OACAC,UAAAA,OACAC,eAAAA,MACAC,SAAS,GACTC,aAAa,IACbC,aAAAA,MACAC,YAAY,CAACC,UAAAA,EAAAA,CAAAA;AAPF,IAUPC,IAAgBC,OAAO,8BAAA;ACDtBC,ICVKC;ADULD,IAAME,IAAoB,SAACC,GAAAA;AAAE,MAAAC,IAAAD,EAAAC,UAAUC,IAAAF,EAAAE,UAAUC,IAAAH,EAAAG,SAASC,IAAAJ,EAAAI,QAAWC,IAAAA,SAAAA,IAAAA,IAAAA;AAAAA,QAAAA,KAAAA,CAAAA;AAAAA,aAAAA,MAAAA,GAAAA,QAAAA,UAAAA,eAAAA,KAAAA,IAAAA,EAAAA,KAAAA,GAAAA,QAAAA,EAAAA,IAAAA,MAAAA,GAAAA,EAAAA,IAAAA,GAAAA,EAAAA;AAAAA,QAAAA,QAAAA,MAAAA,cAAAA,OAAAA,OAAAA,uBAAAA;AAAAA,UAAAA,KAAAA;AAAAA,WAAAA,KAAAA,OAAAA,sBAAAA,EAAAA,GAAAA,KAAAA,GAAAA,QAAAA,KAAAA,CAAAA,GAAAA,QAAAA,GAAAA,EAAAA,CAAAA,IAAAA,KAAAA,OAAAA,UAAAA,qBAAAA,KAAAA,IAAAA,GAAAA,EAAAA,CAAAA,MAAAA,GAAAA,GAAAA,EAAAA,CAAAA,IAAAA,GAAAA,GAAAA,EAAAA,CAAAA;IAAAA;AAAAA,WAAAA;EAAAA,EAA1CL,GAAA,CAAA,YAAA,YAAA,WAAA,QAAA,CAAA;AAChC,SAAOM,YAAYC,OAAO,EACxBC,KAAKH,EAAOG,KACZC,WAAWJ,EAAOI,WAClBhB,aACMiB,MAAMC,QAAQN,EAAOZ,UAAAA,IAAcY,EAAOZ,aAAa,CAACY,EAAOZ,UAAAA,GACnEmB,OAAA,CAAAC,WAAWC,eAAeC,GAAG,SAACC,IAAAA;AAE5Bf,MAASe,EAAAA,GAELA,GAAWC,cACbf,EAASc,GAAWE,MAAMV,IAAIW,SAAAA,GAAYH,EAAAA,GAGxCA,GAAWI,iBACbJ,GAAWK,KAAKC,WAAWnB,EAAQa,EAAAA,IAAcZ,EAAOY,EAAAA;EAE5D,CAAA,CAAA,CAAA,EAAA,CAAA;AAGN;AApBOnB,IA4BM0B,IAA0B,SAACF,GAAAA;AACtCxB,MAAM2B,IAAc,IAAIC;AAMxB,SAAO,EAAED,aAAAA,GAAaE,KALV,SAACC,GAAAA;AACXH,MAAYI,IAAIP,EAAKH,KAAAA,IACjBG,EAAKQ,SAAS,EAAEC,SAASN,EAAYO,YAAYJ,CAAAA,EAAAA,CAAAA,IACjDN,EAAKQ,SAAS,EAAEC,SAASE,YAAYC,aAAalB,GAAGS,EAAYT,GAAGY,CAAAA,CAAAA,EAAAA,CAAAA;EAC1E,EAAA;AAEF;AApCO9B,IAuCMqC,IAA4B,SAAIb,GAAkBM,GAAAA;AACnC,MAAAQ,IAAGZ,EAAwBF,CAAAA,GAA7CG,IAAAW,EAAAX,aAAkDE,IAAAS,EAAAT;AAC1D,SAAA,SAAQU,IAAAA;AACNvC,QAAMwC,IAAcb,EAAYI,IAAIP,EAAKH,KAAAA;AAEzCQ,OADcU,QAAAA,KAAAA,KAAeC,MAAgBV,KACjCA,IAAY,CAAA,CAAA;EAC1B;AACF;AA9CO9B,IERDyC,IAAwB,EAC5BC,MAAMC,SACNC,SAAAA,OAHgB;AFSX5C,IEHM6C,IAAc,EACzBvD,WAAWmD,GACXlD,UAAUkD,GACVjD,eAAeiD,GACfhD,SAASqD,QACTpD,aAAaqD,QACbC,OAAO5D,QACPO,aAAa8C,GACbQ,SAAS7D,QAET8D,MAAM9D,QACNQ,YAAYiB,OACZD,WAAWxB,OAAAA;AFTNY,IEYMmD,IAAiB,EAC5BC,YAAY,EACVV,MAAMK,QACNH,SAAS,GAAA,EAAA;AFfN5C,IEmBMqD,IAAKjE,OAAAkE,OAAAlE,OAAAkE,OAAA,CAAA,GACbT,CAAAA,GACAM,CAAAA;AAAAA,CD/BL,SAAYlD,GAAAA;AACVA,IAAA,SAAA,UACAA,EAAA,SAAA,UACAA,EAAA,QAAA,SACAA,EAAA,OAAA,QACAA,EAAA,QAAA,SACAA,EAAA,cAAA;AACD,EAPWA,MAAAA,IAOX,CAAA,EAAA;AAEMD,IAAMuD,IAAe,CAAA;AAAA,EAEzBtD,EAASuD,MAAAA,IAAM,SAAIC,GAAetC,GAAAA;AAAsB,SAAA;AAAK,GAE9DoC,EAACtD,EAASyD,MAAAA,IAAAA,SAAUvC,GAAAA;AAAAA,SAAAA;AAA2B,GAC/CoC,EAACtD,EAAS0D,KAAAA,IAAAA,SAASxC,GAAAA;AAAAA,SAAAA;AAA2B,GAC9CoC,EAACtD,EAAS2D,IAAAA,IAAAA,SAAQzC,GAAAA;AAAAA,SAAAA;AAA2B,GAAA,EAE5ClB,EAAS4D,KAAAA,IAAK,SAAIC,GAAAA;AAA4E,SAAA;AAAK;AAG/F9D,IAAM+D,IAAmB,CAAA;AAAA,EAC7B9D,EAAS+D,WAAAA,IAAcT,EAAatD,EAASuD,MAAAA;AAGzCxD,IAAMiE,IAAM7E,OAAAkE,OAAAlE,OAAAkE,OAAA,CAAA,GACdC,CAAAA,GACAQ,CAAAA;AAFE/D,IEnBPkE,IAAeC,gBAAgB,EAC7BC,MAAM,iBACNf,OAAKjE,OAAAkE,OAAA,CAAA,GAAOD,CAAAA,GACZgB,OAAKjF,OAAAkE,OAAA,CAAA,GAAOW,CAAAA,GACZK,OAAK,SAACjB,GAAOkB,GAAAA;AACXvE,MAAMwE,IAAYC,WAAAA,GACZpD,IAAQoD,WAAAA,GACRjD,KAAOiD,WAAAA,GAEPC,KACDtF,OAAAkE,OAAAlE,OAAAkE,OAAA,CAAA,GAAAnE,EAAAA,GJEAwF,OAAoB7E,GAAe,CAAA,CAAA,CAAA,GIElCU,KAASoE,SAAAA,WAAAA;AACb5E,QAAM6E,IAAS,CAAA;AAQf,WAPAzF,OAAO0F,KAAKC,MAAM1B,CAAAA,CAAAA,EAAQ2B,QAAQ,SAACC,GAAAA;AAAAA,UAAAA;AACrB,uBAARA,MAGFJ,EAAOI,CAAAA,IAAqB,UAAA,IAAd5B,EAAM4B,CAAAA,MAAAA,WAAQ9E,IAAAA,IAAAuE,GAAcO,CAAAA;IAE9C,CAAA,GACOJ;EACT,CAAA;AA0GA,SAxGAK,UAAAA,WAAAA;AAAAA,QAAAA;AACE7D,MAAMoC,QAAQvD,EAAkB,EAC9BS,KAAK0C,EAAMD,YACXxC,WAAWJ,GAAOiD,MAAM7C,WAIxBhB,YAAAA,UAAYO,IAAAuE,GAAc9E,eAAAA,WAAAA,IAAAA,IAAc,CAAA,GACxCU,SAAO,SAAGa,IAAAA;AAAAA,aAAeoD,EAAQY,KAAKlF,EAAS0D,OAAOxC,EAAAA;IAAW,GACjEZ,QAAM,SAAGY,IAAAA;AAAAA,aAAeoD,EAAQY,KAAKlF,EAAS2D,MAAMzC,EAAAA;IAAW,GAC/Df,UAAQ,SAAGe,IAAAA;AAAAA,aAAeoD,EAAQY,KAAKlF,EAASyD,QAAQvC,EAAAA;IAAW,GACnEd,UAAU,SAAC+E,IAAQjE,IAAAA;AACbiE,MAAAA,OAAW/B,EAAMD,eACnBmB,EAAQY,KAAKlF,EAASuD,QAAQ4B,IAAQjE,EAAAA,GACtCoD,EAAQY,KAAKlF,EAAS+D,aAAaoB,IAAQjE,EAAAA;IAE9C,EAAA,CAAA,GAGHK,GAAKiC,QAAAA,SHlBsBjD,IAAAA;AAAAA,aAA6B,IAAIQ,WAAgB5B,OAAAkE,OAAA,CAAA,GAAA9C,EAAAA,CAAAA;IAAAA,EGkB9C,EAC5Ba,OAAOA,EAAMoC,OACb4B,QAAQb,EAAUf,OAClBP,MAAM1C,GAAOiD,MAAMP,KAAAA,CAAAA;AAGrBlD,QAAMsF,IHEkB,SAAC9D,IAAAA;AAE7BxB,UAAMuF,KAAM,WAAA;AAAS,eAAA/D,GAAKH,MAAMV,IAAIW,SAAAA;MAAAA,GAiBuBkE,KAA7B9D,EAAwBF,EAAAA,EAAKK,KAGrD4D,IAAiBpD,EAA6Bb,IAAM,CACxDR,WAAW0E,SAASxE,GAAAA,KAAG,GACvBT,YAAYkF,SAASzE,GAAAA,IAAG,CAAA,CAAA,GAIpB0E,IAAsBvD,EAA6Bb,IAAMqE,OAAO3E,GAAG,CAAC1B,aAAAA,CAAAA,CAAAA,GAIlBsG,IAA7BpE,EAAwBF,EAAAA,EAAKK,KAOAkE,IAA7BrE,EAAwBF,EAAAA,EAAKK,KAMImE,IAA7BtE,EAAwBF,EAAAA,EAAKK,KAONoE,IAA7BvE,EAAwBF,EAAAA,EAAKK;AAKtD,aAAO,EAAA,OAzCI,WAAA;AAAA,eAASL,GAAK0E,MAAAA;MAAK,GAAA,QA2C5BX,IAAAA,QAxDa,SAACH,IAAAA;AACVA,QAAAA,OAAWG,GAAAA,KACb/D,GAAKQ,SAAS,EACZmE,SAAS,EACPC,MAAM,GACNC,IAAI7E,GAAKH,MAAMV,IAAI2F,QACnBC,QAAQnB,GAAAA,EAAAA,CAAAA;MAIhB,GAAA,cAgDEI,IAAAA,gBACAC,GAAAA,qBACAG,GAAAA,YA9BiB,SAACnG,IAAAA;AAClBqG,UAAU,CAACrF,YAAYhB,QAAQyB,GAAGzB,EAAAA,GAAU+G,WAAWtF,GAAG,IAAIuF,OAAOhH,EAAAA,CAAAA,CAAAA,CAAAA;MACvE,GAAA,YAKmB,SAACwD,IAAAA;AAClB8C,UAAU,CAACtF,YAAYwC,QAAQ/B,GAAG+B,EAAAA,CAAAA,CAAAA;MACpC,GAAA,gBAIuB,SAACQ,IAAAA;AACtBuC,UAActG,YAAY+D,EAAAA,CAAAA;MAC5B,GAAA,UAKiB,SAACT,IAAAA;AAAAA,mBAAAA,OAAAA,KAAuB,CAAA,IACvCiD,EAAQjF,WAAW0F,MAAM,EAAE,KAAGtH,OAAAkE,OAAA,CAAA,GAAQN,EAAAA,EAAAA,CAAAA,CAAAA;MACxC,EAAA;IAcF,EGvEyCxB,GAAKiC,KAAAA;AAGxCkD,UAAAA,WAAAA;AACQ,aAAAtD,EAAMD;IAAU,GAAA,SACrBwD,IAAAA;AACKA,MAAAA,OAAatB,EAAYC,OAAAA,KAC3BD,EAAYuB,OAAOD,EAAAA;IAEvB,CAAA,GAIFD,MACE,WAAA;AAAA,aAAMtD,EAAMzD;IAAU,GACtB,SAACA,IAAAA;AAAU,aAAK0F,EAAYE,aAAa5F,MAAc,CAAA,CAAA;IAAG,GAC1D,EAAEkH,WAAAA,KAAW,CAAA,GAIfH,MACE,WAAA;AAAA,aAAMnG,GAAOiD,MAAMlE;IAAQ,GAC3B,SAACA,IAAAA;AAAQ,aAAK+F,EAAYG,eAAelG,EAAAA;IAAS,GAClD,EAAEuH,WAAAA,KAAW,CAAA,GAIfH,MACE,WAAA;AAAA,aAAMnG,GAAOiD,MAAMjE;IAAa,GAChC,SAACuH,IAAAA;AAAG,aAAKzB,EAAYM,oBAAoBmB,EAAAA;IAAI,GAC7C,EAAED,WAAAA,KAAW,CAAA,GAIfH,MACE,WAAA;AAAA,aAAMnG,GAAOiD,MAAMhE;IAAO,GAC1B,SAACA,IAAAA;AAAO,aAAK6F,EAAY0B,WAAWvH,EAAAA;IAAS,GAC7C,EAAEqH,WAAAA,KAAW,CAAA,GAIfH,MACE,WAAA;AAAA,aAAMnG,GAAOiD,MAAMR;IAAO,GAAA,SACzBA,IAAAA;AAAY,aAAAqC,EAAY2B,WAAWhE,MAAW,CAAA,CAAA;IAAG,GAClD,EAAE6D,WAAAA,KAAW,CAAA,GAIfH,MACE,WAAA;AAAA,aAAMnG,GAAOiD,MAAM/D;IAAW,GAC9B,SAACA,IAAAA;AAAW,aAAK4F,EAAY4B,eAAexH,EAAAA;IAAa,GACzD,EAAEoH,WAAAA,KAAW,CAAA,GAIfH,MACE,WAAA;AAAA,aAAMnG,GAAOiD,MAAMT;IAAK,GACxB,SAACA,IAAAA;AAAK,aAAKsC,EAAY6B,SAASnE,EAAAA;IAAM,GACtC,EAAE8D,WAAAA,KAAW,CAAA,GAIXtG,GAAOiD,MAAMnE,aACfgG,EAAYY,MAAAA,GAGd3B,EAAQY,KAAKlF,EAAS4D,OAAO,EAC3BxC,OAAOA,EAAMoC,OACbjC,MAAMA,GAAKiC,OACXe,WAAWA,EAAUf,MAAAA,CAAAA;EAEzB,CAAA,GAEA2D,gBAAAA,WAAAA;AACM5G,IAAAA,GAAOiD,MAAM9D,eAAe6B,GAAKiC,SHjGV,SAACjC,GAAAA;AAAqBA,QAAK6F,QAAAA;IAAAA,EGkGlC7F,GAAKiC,KAAAA;EAE3B,CAAA,GAAA,WAAA;AAGE,WAAO6D,EAAE,OAAO,EACdC,OAAO,gBACPvE,OAAO,EAAEwE,SAAS,WAAA,GAClBlF,KAAKkC,EAAAA,CAAAA;EAET;AACD,EAAA,CAAA;AFtHIxE,IGlBMyH,IAAavD;AHkBnBlE,IGjBM0H,IAAO,SAAYC,GAAKjD,GAAAA;AACnCiD,IAAIC,UAAU1D,EAAUE,MAAMF,CAAAA,GAC9ByD,EAAIC,UAAU,cAAc1D,CAAAA,GLGC,SAAIyD,IAAUnH,IAAAA;AAC3CmH,IAAAA,GAAIE,QAAQ/H,GAAeU,EAAAA;EAC7B,EKJqBmH,GAAKjD,CAAAA;AAC1B;AHaO1E,IGXQ8H,IAAA,EAAA,YACbL,GAAAA,SACAC,EAAAA;", "names": ["empty", "isAd<PERSON><PERSON>", "command", "isAd<PERSON><PERSON>", "changes", "x", "line", "word", "x", "add", "from", "to", "phrase", "togglePanel", "togglePanel", "x", "j", "baseTheme", "DEFAULT_CONFIG", "Object", "freeze", "autofocus", "disabled", "indentWithTab", "tabSize", "placeholder", "autoDestroy", "extensions", "basicSetup", "CONFIG_SYMBOL", "Symbol", "const", "EventKey", "createEditorState", "_a", "onUpdate", "onChange", "onFocus", "onBlur", "config", "EditorState", "create", "doc", "selection", "Array", "isArray", "concat", "Editor<PERSON><PERSON><PERSON>", "updateListener", "of", "viewUpdate", "do<PERSON><PERSON><PERSON><PERSON>", "state", "toString", "focusChanged", "view", "hasFocus", "createEditorCompartment", "compartment", "Compartment", "run", "extension", "get", "dispatch", "effects", "reconfigure", "StateEffect", "appendConfig", "createEditorExtensionToggler", "ref", "targetApply", "exExtension", "NonDefaultBooleanType", "type", "Boolean", "default", "configProps", "Number", "String", "style", "phrases", "root", "modelValueProp", "modelValue", "props", "assign", "editorE<PERSON>s", "Change", "value", "Update", "Focus", "Blur", "Ready", "payload", "modelUpdateEvent", "ModelUpdate", "events", "Component", "defineComponent", "name", "emits", "setup", "context", "container", "shallowRef", "defaultConfig", "inject", "computed", "result", "keys", "toRaw", "for<PERSON>ach", "key", "onMounted", "emit", "newDoc", "parent", "editorTools", "getDoc", "reExtensions", "toggleDisabled", "editable", "readOnly", "toggleIndentWithTab", "keymap", "reTabSize", "reP<PERSON>ses", "rePlaceholder", "reStyle", "focus", "changes", "from", "to", "length", "insert", "indentUnit", "repeat", "theme", "watch", "newValue", "setDoc", "immediate", "iwt", "setTabSize", "<PERSON><PERSON><PERSON><PERSON>", "setPlaceholder", "setStyle", "onBeforeUnmount", "destroy", "h", "class", "display", "Codemirror", "install", "app", "component", "provide", "index"]}