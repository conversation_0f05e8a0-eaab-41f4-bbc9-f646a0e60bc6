{"version": 3, "sources": ["../../vue3-print-nb/dist/vue3-print-nb.es.js"], "sourcesContent": ["class e{constructor(e){this.standards={strict:\"strict\",loose:\"loose\",html5:\"html5\"},this.previewBody=null,this.close=null,this.previewBodyUtilPrintBtn=null,this.selectArray=[],this.counter=0,this.settings={standard:this.standards.html5},Object.assign(this.settings,e),this.init()}init(){this.counter++,this.settings.id=`printArea_${this.counter}`;let e=\"\";this.settings.url&&!this.settings.asyncUrl&&(e=this.settings.url);let t=this;if(this.settings.asyncUrl)return void t.settings.asyncUrl((function(e){let i=t.getPrintWindow(e);t.settings.preview?t.previewIfrmaeLoad():t.print(i)}),t.settings.vue);let i=this.getPrintWindow(e);this.settings.url||this.write(i.doc),this.settings.preview?this.previewIfrmaeLoad():this.print(i)}addEvent(e,t,i){e.addEventListener?e.addEventListener(t,i,!1):e.attachEvent?e.attachEvent(\"on\"+t,i):e[\"on\"+t]=i}previewIfrmaeLoad(){let e=document.getElementById(\"vue-pirnt-nb-previewBox\");if(e){let t=this,i=e.querySelector(\"iframe\");this.settings.previewBeforeOpenCallback(),this.addEvent(i,\"load\",(function(){t.previewBoxShow(),t.removeCanvasImg(),t.settings.previewOpenCallback()})),this.addEvent(e.querySelector(\".previewBodyUtilPrintBtn\"),\"click\",(function(){t.settings.beforeOpenCallback(),t.settings.openCallback(),i.contentWindow.print(),t.settings.closeCallback()}))}}removeCanvasImg(){let e=this;try{if(e.elsdom){let t=e.elsdom.querySelectorAll(\".canvasImg\");for(let e=0;e<t.length;e++)t[e].remove()}}catch(t){console.log(t)}}print(e){var t=this;let i=document.getElementById(this.settings.id)||e.f,l=document.getElementById(this.settings.id).contentWindow||e.f.contentWindow;t.settings.beforeOpenCallback(),t.addEvent(i,\"load\",(function(){l.focus(),t.settings.openCallback(),l.print(),i.remove(),t.settings.closeCallback(),t.removeCanvasImg()}))}write(e){e.open(),e.write(`${this.docType()}<html>${this.getHead()}${this.getBody()}</html>`),e.close()}docType(){return this.settings.standard===this.standards.html5?\"<!DOCTYPE html>\":`<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01${this.settings.standard===this.standards.loose?\" Transitional\":\"\"}//EN\" \"http://www.w3.org/TR/html4/${this.settings.standard===this.standards.loose?\"loose\":\"strict\"}.dtd\">`}getHead(){let e=\"\",t=\"\",i=\"\";this.settings.extraHead&&this.settings.extraHead.replace(/([^,]+)/g,(t=>{e+=t})),[].forEach.call(document.querySelectorAll(\"link\"),(function(e){e.href.indexOf(\".css\")>=0&&(t+=`<link type=\"text/css\" rel=\"stylesheet\" href=\"${e.href}\" >`)}));let l=document.styleSheets;if(l&&l.length>0)for(let r=0;r<l.length;r++)try{if(l[r].cssRules||l[r].rules){let e=l[r].cssRules||l[r].rules;for(let t=0;t<e.length;t++)i+=e[t].cssText}}catch(s){console.log(l[r].href+s)}return this.settings.extraCss&&this.settings.extraCss.replace(/([^,\\s]+)/g,(e=>{t+=`<link type=\"text/css\" rel=\"stylesheet\" href=\"${e}\">`})),`<head><title>${this.settings.popTitle}</title>${e}${t}<style type=\"text/css\">${i}</style></head>`}getBody(){let e=this.settings.ids;return e=e.replace(new RegExp(\"#\",\"g\"),\"\"),this.elsdom=this.beforeHanler(document.getElementById(e)),\"<body>\"+this.getFormData(this.elsdom).outerHTML+\"</body>\"}beforeHanler(e){let t=e.querySelectorAll(\"canvas\");for(let i=0;i<t.length;i++)if(!t[i].style.display){let e=t[i].parentNode,l=t[i].toDataURL(\"image/png\"),s=new Image;s.className=\"canvasImg\",s.style.display=\"none\",s.src=l,e.appendChild(s)}return e}getFormData(e){let t=e.cloneNode(!0),i=t.querySelectorAll(\"input,select,textarea\"),l=t.querySelectorAll(\".canvasImg,canvas\"),s=-1;for(let r=0;r<l.length;r++){let e=l[r].parentNode,t=l[r];\"canvas\"===t.tagName.toLowerCase()?e.removeChild(t):t.style.display=\"block\"}for(let r=0;r<i.length;r++){let t=i[r],l=t.getAttribute(\"type\"),n=i[r];if(l||(l=\"SELECT\"===t.tagName?\"select\":\"TEXTAREA\"===t.tagName?\"textarea\":\"\"),\"INPUT\"===t.tagName)\"radio\"===l||\"checkbox\"===l?t.checked&&n.setAttribute(\"checked\",t.checked):(n.value=t.value,n.setAttribute(\"value\",t.value));else if(\"select\"===l){s++;for(let i=0;i<e.querySelectorAll(\"select\").length;i++){let l=e.querySelectorAll(\"select\")[i];if(!l.getAttribute(\"newbs\")&&l.setAttribute(\"newbs\",i),l.getAttribute(\"newbs\")==s){let i=e.querySelectorAll(\"select\")[s].selectedIndex;t.options[i].setAttribute(\"selected\",!0)}}}else n.innerHTML=t.value,n.setAttribute(\"html\",t.value)}return t}getPrintWindow(e){var t=this.Iframe(e);return{f:t,win:t.contentWindow||t,doc:t.doc}}previewBoxShow(){let e=document.getElementById(\"vue-pirnt-nb-previewBox\");e&&(document.querySelector(\"html\").setAttribute(\"style\",\"overflow: hidden\"),e.style.display=\"block\")}previewBoxHide(){let e=document.getElementById(\"vue-pirnt-nb-previewBox\");e&&(document.querySelector(\"html\").setAttribute(\"style\",\"overflow: visible;\"),e.querySelector(\"iframe\")&&e.querySelector(\"iframe\").remove(),e.style.display=\"none\")}previewBox(){let e=document.getElementById(\"vue-pirnt-nb-previewBox\"),t=\"previewBody\";if(e)return e.querySelector(\"iframe\")&&e.querySelector(\"iframe\").remove(),{close:e.querySelector(\".previewClose\"),previewBody:e.querySelector(\".previewBody\")};let i=document.createElement(\"div\");i.setAttribute(\"id\",\"vue-pirnt-nb-previewBox\"),i.setAttribute(\"style\",\"position: fixed;top: 0px;left: 0px;width: 100%;height: 100%;background: white;display:none\"),i.style.zIndex=this.settings.zIndex;let l=document.createElement(\"div\");l.setAttribute(\"class\",\"previewHeader\"),l.setAttribute(\"style\",\"padding: 5px 20px;\"),l.innerHTML=this.settings.previewTitle,i.appendChild(l),this.close=document.createElement(\"div\");let s=this.close;s.setAttribute(\"class\",\"previewClose\"),s.setAttribute(\"style\",\"position: absolute;top: 5px;right: 20px;width: 25px;height: 20px;cursor: pointer;\");let r=document.createElement(\"div\"),n=document.createElement(\"div\");r.setAttribute(\"class\",\"closeBefore\"),r.setAttribute(\"style\",\"position: absolute;width: 3px;height: 100%;background: #040404;transform: rotate(45deg); top: 0px;left: 50%;\"),n.setAttribute(\"class\",\"closeAfter\"),n.setAttribute(\"style\",\"position: absolute;width: 3px;height: 100%;background: #040404;transform: rotate(-45deg); top: 0px;left: 50%;\"),s.appendChild(r),s.appendChild(n),l.appendChild(s),this.previewBody=document.createElement(\"div\");let o=this.previewBody;o.setAttribute(\"class\",t),o.setAttribute(\"style\",\"display: flex;flex-direction: column; height: 100%;\"),i.appendChild(o);let a=document.createElement(\"div\");a.setAttribute(\"class\",\"previewBodyUtil\"),a.setAttribute(\"style\",\"height: 32px;background: #474747;position: relative;\"),o.appendChild(a),this.previewBodyUtilPrintBtn=document.createElement(\"div\");let d=this.previewBodyUtilPrintBtn;return d.setAttribute(\"class\",\"previewBodyUtilPrintBtn\"),d.innerHTML=this.settings.previewPrintBtnLabel,d.setAttribute(\"style\",\"position: absolute;padding: 2px 10px;margin-top: 3px;left: 24px;font-size: 14px;color: white;cursor: pointer;background-color: rgba(0,0,0,.12);background-image: linear-gradient(hsla(0,0%,100%,.05),hsla(0,0%,100%,0));background-clip: padding-box;border: 1px solid rgba(0,0,0,.35);border-color: rgba(0,0,0,.32) rgba(0,0,0,.38) rgba(0,0,0,.42);box-shadow: inset 0 1px 0 hsla(0,0%,100%,.05), inset 0 0 1px hsla(0,0%,100%,.15), 0 1px 0 hsla(0,0%,100%,.05);\"),a.appendChild(d),document.body.appendChild(i),{close:this.close,previewBody:this.previewBody}}iframeBox(e,t){let i=document.createElement(\"iframe\");return i.style.border=\"0px\",i.style.position=\"absolute\",i.style.width=\"0px\",i.style.height=\"0px\",i.style.right=\"0px\",i.style.top=\"0px\",i.setAttribute(\"id\",e),i.setAttribute(\"src\",t),i}Iframe(e){let t=this.settings.id;e=e||(new Date).getTime();let i=this,l=this.iframeBox(t,e);try{if(this.settings.preview){l.setAttribute(\"style\",\"border: 0px;flex: 1;\");let e=this.previewBox(),t=e.previewBody,s=e.close;t.appendChild(l),this.addEvent(s,\"click\",(function(){i.previewBoxHide()}))}else document.body.appendChild(l);l.doc=null,l.doc=l.contentDocument?l.contentDocument:l.contentWindow?l.contentWindow.document:l.document}catch(s){throw new Error(s+\". iframes may not be supported in this browser.\")}if(null==l.doc)throw new Error(\"Cannot find document.\");return l}}var t={directiveName:\"print\",mounted(t,i,l){let s=i.instance,r=\"\";var n,o,a;o=\"click\",a=()=>{if(\"string\"==typeof i.value)r=i.value;else{if(\"object\"!=typeof i.value||!i.value.id)return void window.print();{r=i.value.id;let e=r.replace(new RegExp(\"#\",\"g\"),\"\");document.getElementById(e)||(console.log(\"id in Error\"),r=\"\")}}d()},(n=t).addEventListener?n.addEventListener(o,a,!1):n.attachEvent?n.attachEvent(\"on\"+o,a):n[\"on\"+o]=a;const d=()=>{new e({ids:r,vue:s,url:i.value.url,standard:\"\",extraHead:i.value.extraHead,extraCss:i.value.extraCss,zIndex:i.value.zIndex||20002,previewTitle:i.value.previewTitle||\"打印预览\",previewPrintBtnLabel:i.value.previewPrintBtnLabel||\"打印\",popTitle:i.value.popTitle,preview:i.value.preview||!1,asyncUrl:i.value.asyncUrl,previewBeforeOpenCallback(){i.value.previewBeforeOpenCallback&&i.value.previewBeforeOpenCallback(s)},previewOpenCallback(){i.value.previewOpenCallback&&i.value.previewOpenCallback(s)},openCallback(){i.value.openCallback&&i.value.openCallback(s)},closeCallback(){i.value.closeCallback&&i.value.closeCallback(s)},beforeOpenCallback(){i.value.beforeOpenCallback&&i.value.beforeOpenCallback(s)}})}},install:function(e){e.directive(\"print\",t)}};export default t;\n"], "mappings": ";;;AAAA,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYA,IAAE;AAAC,SAAK,YAAU,EAAC,QAAO,UAAS,OAAM,SAAQ,OAAM,QAAO,GAAE,KAAK,cAAY,MAAK,KAAK,QAAM,MAAK,KAAK,0BAAwB,MAAK,KAAK,cAAY,CAAC,GAAE,KAAK,UAAQ,GAAE,KAAK,WAAS,EAAC,UAAS,KAAK,UAAU,MAAK,GAAE,OAAO,OAAO,KAAK,UAASA,EAAC,GAAE,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,SAAK,WAAU,KAAK,SAAS,KAAG,aAAa,KAAK,OAAO;AAAG,QAAIA,KAAE;AAAG,SAAK,SAAS,OAAK,CAAC,KAAK,SAAS,aAAWA,KAAE,KAAK,SAAS;AAAK,QAAIC,KAAE;AAAK,QAAG,KAAK,SAAS,SAAS,QAAO,KAAKA,GAAE,SAAS,SAAU,SAASD,IAAE;AAAC,UAAIE,KAAED,GAAE,eAAeD,EAAC;AAAE,MAAAC,GAAE,SAAS,UAAQA,GAAE,kBAAkB,IAAEA,GAAE,MAAMC,EAAC;AAAA,IAAC,GAAGD,GAAE,SAAS,GAAG;AAAE,QAAI,IAAE,KAAK,eAAeD,EAAC;AAAE,SAAK,SAAS,OAAK,KAAK,MAAM,EAAE,GAAG,GAAE,KAAK,SAAS,UAAQ,KAAK,kBAAkB,IAAE,KAAK,MAAM,CAAC;AAAA,EAAC;AAAA,EAAC,SAASA,IAAEC,IAAE,GAAE;AAAC,IAAAD,GAAE,mBAAiBA,GAAE,iBAAiBC,IAAE,GAAE,KAAE,IAAED,GAAE,cAAYA,GAAE,YAAY,OAAKC,IAAE,CAAC,IAAED,GAAE,OAAKC,EAAC,IAAE;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,QAAID,KAAE,SAAS,eAAe,yBAAyB;AAAE,QAAGA,IAAE;AAAC,UAAIC,KAAE,MAAK,IAAED,GAAE,cAAc,QAAQ;AAAE,WAAK,SAAS,0BAA0B,GAAE,KAAK,SAAS,GAAE,QAAQ,WAAU;AAAC,QAAAC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEA,GAAE,SAAS,oBAAoB;AAAA,MAAC,CAAE,GAAE,KAAK,SAASD,GAAE,cAAc,0BAA0B,GAAE,SAAS,WAAU;AAAC,QAAAC,GAAE,SAAS,mBAAmB,GAAEA,GAAE,SAAS,aAAa,GAAE,EAAE,cAAc,MAAM,GAAEA,GAAE,SAAS,cAAc;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,QAAID,KAAE;AAAK,QAAG;AAAC,UAAGA,GAAE,QAAO;AAAC,YAAIC,KAAED,GAAE,OAAO,iBAAiB,YAAY;AAAE,iBAAQA,KAAE,GAAEA,KAAEC,GAAE,QAAOD,KAAI,CAAAC,GAAED,EAAC,EAAE,OAAO;AAAA,MAAC;AAAA,IAAC,SAAOC,IAAE;AAAC,cAAQ,IAAIA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAMD,IAAE;AAAC,QAAIC,KAAE;AAAK,QAAI,IAAE,SAAS,eAAe,KAAK,SAAS,EAAE,KAAGD,GAAE,GAAE,IAAE,SAAS,eAAe,KAAK,SAAS,EAAE,EAAE,iBAAeA,GAAE,EAAE;AAAc,IAAAC,GAAE,SAAS,mBAAmB,GAAEA,GAAE,SAAS,GAAE,QAAQ,WAAU;AAAC,QAAE,MAAM,GAAEA,GAAE,SAAS,aAAa,GAAE,EAAE,MAAM,GAAE,EAAE,OAAO,GAAEA,GAAE,SAAS,cAAc,GAAEA,GAAE,gBAAgB;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAMD,IAAE;AAAC,IAAAA,GAAE,KAAK,GAAEA,GAAE,MAAM,GAAG,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,GAAG,KAAK,QAAQ,CAAC,SAAS,GAAEA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,SAAS,aAAW,KAAK,UAAU,QAAM,oBAAkB,+CAA+C,KAAK,SAAS,aAAW,KAAK,UAAU,QAAM,kBAAgB,EAAE,qCAAqC,KAAK,SAAS,aAAW,KAAK,UAAU,QAAM,UAAQ,QAAQ;AAAA,EAAQ;AAAA,EAAC,UAAS;AAAC,QAAIA,KAAE,IAAGC,KAAE,IAAG,IAAE;AAAG,SAAK,SAAS,aAAW,KAAK,SAAS,UAAU,QAAQ,YAAY,CAAAA,OAAG;AAAC,MAAAD,MAAGC;AAAA,IAAC,CAAE,GAAE,CAAC,EAAE,QAAQ,KAAK,SAAS,iBAAiB,MAAM,GAAG,SAASD,IAAE;AAAC,MAAAA,GAAE,KAAK,QAAQ,MAAM,KAAG,MAAIC,MAAG,gDAAgDD,GAAE,IAAI;AAAA,IAAM,CAAE;AAAE,QAAI,IAAE,SAAS;AAAY,QAAG,KAAG,EAAE,SAAO,EAAE,UAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,KAAG;AAAC,UAAG,EAAE,CAAC,EAAE,YAAU,EAAE,CAAC,EAAE,OAAM;AAAC,YAAIA,KAAE,EAAE,CAAC,EAAE,YAAU,EAAE,CAAC,EAAE;AAAM,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,MAAGD,GAAEC,EAAC,EAAE;AAAA,MAAO;AAAA,IAAC,SAAO,GAAE;AAAC,cAAQ,IAAI,EAAE,CAAC,EAAE,OAAK,CAAC;AAAA,IAAC;AAAC,WAAO,KAAK,SAAS,YAAU,KAAK,SAAS,SAAS,QAAQ,cAAc,CAAAD,OAAG;AAAC,MAAAC,MAAG,gDAAgDD,EAAC;AAAA,IAAI,CAAE,GAAE,gBAAgB,KAAK,SAAS,QAAQ,WAAWA,EAAC,GAAGC,EAAC,0BAA0B,CAAC;AAAA,EAAiB;AAAA,EAAC,UAAS;AAAC,QAAID,KAAE,KAAK,SAAS;AAAI,WAAOA,KAAEA,GAAE,QAAQ,IAAI,OAAO,KAAI,GAAG,GAAE,EAAE,GAAE,KAAK,SAAO,KAAK,aAAa,SAAS,eAAeA,EAAC,CAAC,GAAE,WAAS,KAAK,YAAY,KAAK,MAAM,EAAE,YAAU;AAAA,EAAS;AAAA,EAAC,aAAaA,IAAE;AAAC,QAAIC,KAAED,GAAE,iBAAiB,QAAQ;AAAE,aAAQ,IAAE,GAAE,IAAEC,GAAE,QAAO,IAAI,KAAG,CAACA,GAAE,CAAC,EAAE,MAAM,SAAQ;AAAC,UAAID,KAAEC,GAAE,CAAC,EAAE,YAAW,IAAEA,GAAE,CAAC,EAAE,UAAU,WAAW,GAAE,IAAE,IAAI;AAAM,QAAE,YAAU,aAAY,EAAE,MAAM,UAAQ,QAAO,EAAE,MAAI,GAAED,GAAE,YAAY,CAAC;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,QAAIC,KAAED,GAAE,UAAU,IAAE,GAAE,IAAEC,GAAE,iBAAiB,uBAAuB,GAAE,IAAEA,GAAE,iBAAiB,mBAAmB,GAAE,IAAE;AAAG,aAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,UAAID,KAAE,EAAE,CAAC,EAAE,YAAWC,KAAE,EAAE,CAAC;AAAE,mBAAWA,GAAE,QAAQ,YAAY,IAAED,GAAE,YAAYC,EAAC,IAAEA,GAAE,MAAM,UAAQ;AAAA,IAAO;AAAC,aAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,UAAIA,KAAE,EAAE,CAAC,GAAEE,KAAEF,GAAE,aAAa,MAAM,GAAE,IAAE,EAAE,CAAC;AAAE,UAAGE,OAAIA,KAAE,aAAWF,GAAE,UAAQ,WAAS,eAAaA,GAAE,UAAQ,aAAW,KAAI,YAAUA,GAAE,QAAQ,aAAUE,MAAG,eAAaA,KAAEF,GAAE,WAAS,EAAE,aAAa,WAAUA,GAAE,OAAO,KAAG,EAAE,QAAMA,GAAE,OAAM,EAAE,aAAa,SAAQA,GAAE,KAAK;AAAA,eAAW,aAAWE,IAAE;AAAC;AAAI,iBAAQD,KAAE,GAAEA,KAAEF,GAAE,iBAAiB,QAAQ,EAAE,QAAOE,MAAI;AAAC,cAAIC,KAAEH,GAAE,iBAAiB,QAAQ,EAAEE,EAAC;AAAE,cAAG,CAACC,GAAE,aAAa,OAAO,KAAGA,GAAE,aAAa,SAAQD,EAAC,GAAEC,GAAE,aAAa,OAAO,KAAG,GAAE;AAAC,gBAAID,KAAEF,GAAE,iBAAiB,QAAQ,EAAE,CAAC,EAAE;AAAc,YAAAC,GAAE,QAAQC,EAAC,EAAE,aAAa,YAAW,IAAE;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,MAAM,GAAE,YAAUD,GAAE,OAAM,EAAE,aAAa,QAAOA,GAAE,KAAK;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAE;AAAC,QAAIC,KAAE,KAAK,OAAOD,EAAC;AAAE,WAAM,EAAC,GAAEC,IAAE,KAAIA,GAAE,iBAAeA,IAAE,KAAIA,GAAE,IAAG;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,QAAID,KAAE,SAAS,eAAe,yBAAyB;AAAE,IAAAA,OAAI,SAAS,cAAc,MAAM,EAAE,aAAa,SAAQ,kBAAkB,GAAEA,GAAE,MAAM,UAAQ;AAAA,EAAQ;AAAA,EAAC,iBAAgB;AAAC,QAAIA,KAAE,SAAS,eAAe,yBAAyB;AAAE,IAAAA,OAAI,SAAS,cAAc,MAAM,EAAE,aAAa,SAAQ,oBAAoB,GAAEA,GAAE,cAAc,QAAQ,KAAGA,GAAE,cAAc,QAAQ,EAAE,OAAO,GAAEA,GAAE,MAAM,UAAQ;AAAA,EAAO;AAAA,EAAC,aAAY;AAAC,QAAIA,KAAE,SAAS,eAAe,yBAAyB,GAAEC,KAAE;AAAc,QAAGD,GAAE,QAAOA,GAAE,cAAc,QAAQ,KAAGA,GAAE,cAAc,QAAQ,EAAE,OAAO,GAAE,EAAC,OAAMA,GAAE,cAAc,eAAe,GAAE,aAAYA,GAAE,cAAc,cAAc,EAAC;AAAE,QAAI,IAAE,SAAS,cAAc,KAAK;AAAE,MAAE,aAAa,MAAK,yBAAyB,GAAE,EAAE,aAAa,SAAQ,4FAA4F,GAAE,EAAE,MAAM,SAAO,KAAK,SAAS;AAAO,QAAI,IAAE,SAAS,cAAc,KAAK;AAAE,MAAE,aAAa,SAAQ,eAAe,GAAE,EAAE,aAAa,SAAQ,oBAAoB,GAAE,EAAE,YAAU,KAAK,SAAS,cAAa,EAAE,YAAY,CAAC,GAAE,KAAK,QAAM,SAAS,cAAc,KAAK;AAAE,QAAI,IAAE,KAAK;AAAM,MAAE,aAAa,SAAQ,cAAc,GAAE,EAAE,aAAa,SAAQ,mFAAmF;AAAE,QAAI,IAAE,SAAS,cAAc,KAAK,GAAE,IAAE,SAAS,cAAc,KAAK;AAAE,MAAE,aAAa,SAAQ,aAAa,GAAE,EAAE,aAAa,SAAQ,8GAA8G,GAAE,EAAE,aAAa,SAAQ,YAAY,GAAE,EAAE,aAAa,SAAQ,+GAA+G,GAAE,EAAE,YAAY,CAAC,GAAE,EAAE,YAAY,CAAC,GAAE,EAAE,YAAY,CAAC,GAAE,KAAK,cAAY,SAAS,cAAc,KAAK;AAAE,QAAI,IAAE,KAAK;AAAY,MAAE,aAAa,SAAQC,EAAC,GAAE,EAAE,aAAa,SAAQ,qDAAqD,GAAE,EAAE,YAAY,CAAC;AAAE,QAAI,IAAE,SAAS,cAAc,KAAK;AAAE,MAAE,aAAa,SAAQ,iBAAiB,GAAE,EAAE,aAAa,SAAQ,sDAAsD,GAAE,EAAE,YAAY,CAAC,GAAE,KAAK,0BAAwB,SAAS,cAAc,KAAK;AAAE,QAAI,IAAE,KAAK;AAAwB,WAAO,EAAE,aAAa,SAAQ,yBAAyB,GAAE,EAAE,YAAU,KAAK,SAAS,sBAAqB,EAAE,aAAa,SAAQ,qcAAqc,GAAE,EAAE,YAAY,CAAC,GAAE,SAAS,KAAK,YAAY,CAAC,GAAE,EAAC,OAAM,KAAK,OAAM,aAAY,KAAK,YAAW;AAAA,EAAC;AAAA,EAAC,UAAUD,IAAEC,IAAE;AAAC,QAAI,IAAE,SAAS,cAAc,QAAQ;AAAE,WAAO,EAAE,MAAM,SAAO,OAAM,EAAE,MAAM,WAAS,YAAW,EAAE,MAAM,QAAM,OAAM,EAAE,MAAM,SAAO,OAAM,EAAE,MAAM,QAAM,OAAM,EAAE,MAAM,MAAI,OAAM,EAAE,aAAa,MAAKD,EAAC,GAAE,EAAE,aAAa,OAAMC,EAAC,GAAE;AAAA,EAAC;AAAA,EAAC,OAAOD,IAAE;AAAC,QAAIC,KAAE,KAAK,SAAS;AAAG,IAAAD,KAAEA,OAAI,oBAAI,QAAM,QAAQ;AAAE,QAAI,IAAE,MAAK,IAAE,KAAK,UAAUC,IAAED,EAAC;AAAE,QAAG;AAAC,UAAG,KAAK,SAAS,SAAQ;AAAC,UAAE,aAAa,SAAQ,sBAAsB;AAAE,YAAIA,KAAE,KAAK,WAAW,GAAEC,KAAED,GAAE,aAAY,IAAEA,GAAE;AAAM,QAAAC,GAAE,YAAY,CAAC,GAAE,KAAK,SAAS,GAAE,SAAS,WAAU;AAAC,YAAE,eAAe;AAAA,QAAC,CAAE;AAAA,MAAC,MAAM,UAAS,KAAK,YAAY,CAAC;AAAE,QAAE,MAAI,MAAK,EAAE,MAAI,EAAE,kBAAgB,EAAE,kBAAgB,EAAE,gBAAc,EAAE,cAAc,WAAS,EAAE;AAAA,IAAQ,SAAO,GAAE;AAAC,YAAM,IAAI,MAAM,IAAE,iDAAiD;AAAA,IAAC;AAAC,QAAG,QAAM,EAAE,IAAI,OAAM,IAAI,MAAM,uBAAuB;AAAE,WAAO;AAAA,EAAC;AAAC;AAAC,IAAI,IAAE,EAAC,eAAc,SAAQ,QAAQA,IAAE,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,UAAS,IAAE;AAAG,MAAI,GAAE,GAAE;AAAE,MAAE,SAAQ,IAAE,MAAI;AAAC,QAAG,YAAU,OAAO,EAAE,MAAM,KAAE,EAAE;AAAA,SAAU;AAAC,UAAG,YAAU,OAAO,EAAE,SAAO,CAAC,EAAE,MAAM,GAAG,QAAO,KAAK,OAAO,MAAM;AAAE;AAAC,YAAE,EAAE,MAAM;AAAG,YAAID,KAAE,EAAE,QAAQ,IAAI,OAAO,KAAI,GAAG,GAAE,EAAE;AAAE,iBAAS,eAAeA,EAAC,MAAI,QAAQ,IAAI,aAAa,GAAE,IAAE;AAAA,MAAG;AAAA,IAAC;AAAC,MAAE;AAAA,EAAC,IAAG,IAAEC,IAAG,mBAAiB,EAAE,iBAAiB,GAAE,GAAE,KAAE,IAAE,EAAE,cAAY,EAAE,YAAY,OAAK,GAAE,CAAC,IAAE,EAAE,OAAK,CAAC,IAAE;AAAE,QAAM,IAAE,MAAI;AAAC,QAAI,EAAE,EAAC,KAAI,GAAE,KAAI,GAAE,KAAI,EAAE,MAAM,KAAI,UAAS,IAAG,WAAU,EAAE,MAAM,WAAU,UAAS,EAAE,MAAM,UAAS,QAAO,EAAE,MAAM,UAAQ,OAAM,cAAa,EAAE,MAAM,gBAAc,QAAO,sBAAqB,EAAE,MAAM,wBAAsB,MAAK,UAAS,EAAE,MAAM,UAAS,SAAQ,EAAE,MAAM,WAAS,OAAG,UAAS,EAAE,MAAM,UAAS,4BAA2B;AAAC,QAAE,MAAM,6BAA2B,EAAE,MAAM,0BAA0B,CAAC;AAAA,IAAC,GAAE,sBAAqB;AAAC,QAAE,MAAM,uBAAqB,EAAE,MAAM,oBAAoB,CAAC;AAAA,IAAC,GAAE,eAAc;AAAC,QAAE,MAAM,gBAAc,EAAE,MAAM,aAAa,CAAC;AAAA,IAAC,GAAE,gBAAe;AAAC,QAAE,MAAM,iBAAe,EAAE,MAAM,cAAc,CAAC;AAAA,IAAC,GAAE,qBAAoB;AAAC,QAAE,MAAM,sBAAoB,EAAE,MAAM,mBAAmB,CAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAC,GAAE,SAAQ,SAASD,IAAE;AAAC,EAAAA,GAAE,UAAU,SAAQ,CAAC;AAAC,EAAC;AAAE,IAAO,2BAAQ;", "names": ["e", "t", "i", "l"]}