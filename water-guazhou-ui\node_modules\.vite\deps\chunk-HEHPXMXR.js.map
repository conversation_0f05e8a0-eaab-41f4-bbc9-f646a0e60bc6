{"version": 3, "sources": ["../../echarts/lib/chart/pie/pieLayout.js", "../../echarts/lib/processor/dataFilter.js", "../../echarts/lib/chart/pie/labelLayout.js", "../../echarts/lib/chart/helper/sectorHelper.js", "../../echarts/lib/chart/pie/PieView.js", "../../echarts/lib/chart/helper/createSeriesDataSimply.js", "../../echarts/lib/visual/LegendVisualProvider.js", "../../echarts/lib/chart/pie/PieSeries.js", "../../echarts/lib/processor/negativeDataFilter.js", "../../echarts/lib/chart/pie/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { parsePercent, linearMap } from '../../util/number.js';\nimport * as layout from '../../util/layout.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { normalizeArcAngles } from 'zrender/lib/core/PathProxy.js';\nimport { makeInner } from '../../util/model.js';\nvar PI2 = Math.PI * 2;\nvar RADIAN = Math.PI / 180;\nfunction getViewRect(seriesModel, api) {\n  return layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\nexport function getBasicPieLayout(seriesModel, api) {\n  var viewRect = getViewRect(seriesModel, api);\n  // center can be string or number when coordinateSystem is specified\n  var center = seriesModel.get('center');\n  var radius = seriesModel.get('radius');\n  if (!zrUtil.isArray(radius)) {\n    radius = [0, radius];\n  }\n  var width = parsePercent(viewRect.width, api.getWidth());\n  var height = parsePercent(viewRect.height, api.getHeight());\n  var size = Math.min(width, height);\n  var r0 = parsePercent(radius[0], size / 2);\n  var r = parsePercent(radius[1], size / 2);\n  var cx;\n  var cy;\n  var coordSys = seriesModel.coordinateSystem;\n  if (coordSys) {\n    // percentage is not allowed when coordinate system is specified\n    var point = coordSys.dataToPoint(center);\n    cx = point[0] || 0;\n    cy = point[1] || 0;\n  } else {\n    if (!zrUtil.isArray(center)) {\n      center = [center, center];\n    }\n    cx = parsePercent(center[0], width) + viewRect.x;\n    cy = parsePercent(center[1], height) + viewRect.y;\n  }\n  return {\n    cx: cx,\n    cy: cy,\n    r0: r0,\n    r: r\n  };\n}\nexport default function pieLayout(seriesType, ecModel, api) {\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    var data = seriesModel.getData();\n    var valueDim = data.mapDimension('value');\n    var viewRect = getViewRect(seriesModel, api);\n    var _a = getBasicPieLayout(seriesModel, api),\n      cx = _a.cx,\n      cy = _a.cy,\n      r = _a.r,\n      r0 = _a.r0;\n    var startAngle = -seriesModel.get('startAngle') * RADIAN;\n    var endAngle = seriesModel.get('endAngle');\n    var padAngle = seriesModel.get('padAngle') * RADIAN;\n    endAngle = endAngle === 'auto' ? startAngle - PI2 : -endAngle * RADIAN;\n    var minAngle = seriesModel.get('minAngle') * RADIAN;\n    var minAndPadAngle = minAngle + padAngle;\n    var validDataCount = 0;\n    data.each(valueDim, function (value) {\n      !isNaN(value) && validDataCount++;\n    });\n    var sum = data.getSum(valueDim);\n    // Sum may be 0\n    var unitRadian = Math.PI / (sum || validDataCount) * 2;\n    var clockwise = seriesModel.get('clockwise');\n    var roseType = seriesModel.get('roseType');\n    var stillShowZeroSum = seriesModel.get('stillShowZeroSum');\n    // [0...max]\n    var extent = data.getDataExtent(valueDim);\n    extent[0] = 0;\n    var dir = clockwise ? 1 : -1;\n    var angles = [startAngle, endAngle];\n    var halfPadAngle = dir * padAngle / 2;\n    normalizeArcAngles(angles, !clockwise);\n    startAngle = angles[0], endAngle = angles[1];\n    var layoutData = getSeriesLayoutData(seriesModel);\n    layoutData.startAngle = startAngle;\n    layoutData.endAngle = endAngle;\n    layoutData.clockwise = clockwise;\n    var angleRange = Math.abs(endAngle - startAngle);\n    // In the case some sector angle is smaller than minAngle\n    var restAngle = angleRange;\n    var valueSumLargerThanMinAngle = 0;\n    var currentAngle = startAngle;\n    data.setLayout({\n      viewRect: viewRect,\n      r: r\n    });\n    data.each(valueDim, function (value, idx) {\n      var angle;\n      if (isNaN(value)) {\n        data.setItemLayout(idx, {\n          angle: NaN,\n          startAngle: NaN,\n          endAngle: NaN,\n          clockwise: clockwise,\n          cx: cx,\n          cy: cy,\n          r0: r0,\n          r: roseType ? NaN : r\n        });\n        return;\n      }\n      // FIXME 兼容 2.0 但是 roseType 是 area 的时候才是这样？\n      if (roseType !== 'area') {\n        angle = sum === 0 && stillShowZeroSum ? unitRadian : value * unitRadian;\n      } else {\n        angle = angleRange / validDataCount;\n      }\n      if (angle < minAndPadAngle) {\n        angle = minAndPadAngle;\n        restAngle -= minAndPadAngle;\n      } else {\n        valueSumLargerThanMinAngle += value;\n      }\n      var endAngle = currentAngle + dir * angle;\n      // calculate display angle\n      var actualStartAngle = 0;\n      var actualEndAngle = 0;\n      if (padAngle > angle) {\n        actualStartAngle = currentAngle + dir * angle / 2;\n        actualEndAngle = actualStartAngle;\n      } else {\n        actualStartAngle = currentAngle + halfPadAngle;\n        actualEndAngle = endAngle - halfPadAngle;\n      }\n      data.setItemLayout(idx, {\n        angle: angle,\n        startAngle: actualStartAngle,\n        endAngle: actualEndAngle,\n        clockwise: clockwise,\n        cx: cx,\n        cy: cy,\n        r0: r0,\n        r: roseType ? linearMap(value, extent, [r0, r]) : r\n      });\n      currentAngle = endAngle;\n    });\n    // Some sector is constrained by minAngle and padAngle\n    // Rest sectors needs recalculate angle\n    if (restAngle < PI2 && validDataCount) {\n      // Average the angle if rest angle is not enough after all angles is\n      // Constrained by minAngle and padAngle\n      if (restAngle <= 1e-3) {\n        var angle_1 = angleRange / validDataCount;\n        data.each(valueDim, function (value, idx) {\n          if (!isNaN(value)) {\n            var layout_1 = data.getItemLayout(idx);\n            layout_1.angle = angle_1;\n            var actualStartAngle = 0;\n            var actualEndAngle = 0;\n            if (angle_1 < padAngle) {\n              actualStartAngle = startAngle + dir * (idx + 1 / 2) * angle_1;\n              actualEndAngle = actualStartAngle;\n            } else {\n              actualStartAngle = startAngle + dir * idx * angle_1 + halfPadAngle;\n              actualEndAngle = startAngle + dir * (idx + 1) * angle_1 - halfPadAngle;\n            }\n            layout_1.startAngle = actualStartAngle;\n            layout_1.endAngle = actualEndAngle;\n          }\n        });\n      } else {\n        unitRadian = restAngle / valueSumLargerThanMinAngle;\n        currentAngle = startAngle;\n        data.each(valueDim, function (value, idx) {\n          if (!isNaN(value)) {\n            var layout_2 = data.getItemLayout(idx);\n            var angle = layout_2.angle === minAndPadAngle ? minAndPadAngle : value * unitRadian;\n            var actualStartAngle = 0;\n            var actualEndAngle = 0;\n            if (angle < padAngle) {\n              actualStartAngle = currentAngle + dir * angle / 2;\n              actualEndAngle = actualStartAngle;\n            } else {\n              actualStartAngle = currentAngle + halfPadAngle;\n              actualEndAngle = currentAngle + dir * angle - halfPadAngle;\n            }\n            layout_2.startAngle = actualStartAngle;\n            layout_2.endAngle = actualEndAngle;\n            currentAngle += dir * angle;\n          }\n        });\n      }\n    }\n  });\n}\nexport var getSeriesLayoutData = makeInner();", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nexport default function dataFilter(seriesType) {\n  return {\n    seriesType: seriesType,\n    reset: function (seriesModel, ecModel) {\n      var legendModels = ecModel.findComponents({\n        mainType: 'legend'\n      });\n      if (!legendModels || !legendModels.length) {\n        return;\n      }\n      var data = seriesModel.getData();\n      data.filterSelf(function (idx) {\n        var name = data.getName(idx);\n        // If in any legend component the status is not selected.\n        for (var i = 0; i < legendModels.length; i++) {\n          // @ts-ignore FIXME: LegendModel\n          if (!legendModels[i].isSelected(name)) {\n            return false;\n          }\n        }\n        return true;\n      });\n    }\n  };\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// FIXME emphasis label position is not same with normal label position\nimport { parsePercent } from '../../util/number.js';\nimport { Point } from '../../util/graphic.js';\nimport { each, isNumber } from 'zrender/lib/core/util.js';\nimport { limitTurnAngle, limitSurfaceAngle } from '../../label/labelGuideHelper.js';\nimport { shiftLayoutOnY } from '../../label/labelLayoutHelper.js';\nvar RADIAN = Math.PI / 180;\nfunction adjustSingleSide(list, cx, cy, r, dir, viewWidth, viewHeight, viewLeft, viewTop, farthestX) {\n  if (list.length < 2) {\n    return;\n  }\n  ;\n  function recalculateXOnSemiToAlignOnEllipseCurve(semi) {\n    var rB = semi.rB;\n    var rB2 = rB * rB;\n    for (var i = 0; i < semi.list.length; i++) {\n      var item = semi.list[i];\n      var dy = Math.abs(item.label.y - cy);\n      // horizontal r is always same with original r because x is not changed.\n      var rA = r + item.len;\n      var rA2 = rA * rA;\n      // Use ellipse implicit function to calculate x\n      var dx = Math.sqrt((1 - Math.abs(dy * dy / rB2)) * rA2);\n      var newX = cx + (dx + item.len2) * dir;\n      var deltaX = newX - item.label.x;\n      var newTargetWidth = item.targetTextWidth - deltaX * dir;\n      // text x is changed, so need to recalculate width.\n      constrainTextWidth(item, newTargetWidth, true);\n      item.label.x = newX;\n    }\n  }\n  // Adjust X based on the shifted y. Make tight labels aligned on an ellipse curve.\n  function recalculateX(items) {\n    // Extremes of\n    var topSemi = {\n      list: [],\n      maxY: 0\n    };\n    var bottomSemi = {\n      list: [],\n      maxY: 0\n    };\n    for (var i = 0; i < items.length; i++) {\n      if (items[i].labelAlignTo !== 'none') {\n        continue;\n      }\n      var item = items[i];\n      var semi = item.label.y > cy ? bottomSemi : topSemi;\n      var dy = Math.abs(item.label.y - cy);\n      if (dy >= semi.maxY) {\n        var dx = item.label.x - cx - item.len2 * dir;\n        // horizontal r is always same with original r because x is not changed.\n        var rA = r + item.len;\n        // Canculate rB based on the topest / bottemest label.\n        var rB = Math.abs(dx) < rA ? Math.sqrt(dy * dy / (1 - dx * dx / rA / rA)) : rA;\n        semi.rB = rB;\n        semi.maxY = dy;\n      }\n      semi.list.push(item);\n    }\n    recalculateXOnSemiToAlignOnEllipseCurve(topSemi);\n    recalculateXOnSemiToAlignOnEllipseCurve(bottomSemi);\n  }\n  var len = list.length;\n  for (var i = 0; i < len; i++) {\n    if (list[i].position === 'outer' && list[i].labelAlignTo === 'labelLine') {\n      var dx = list[i].label.x - farthestX;\n      list[i].linePoints[1][0] += dx;\n      list[i].label.x = farthestX;\n    }\n  }\n  if (shiftLayoutOnY(list, viewTop, viewTop + viewHeight)) {\n    recalculateX(list);\n  }\n}\nfunction avoidOverlap(labelLayoutList, cx, cy, r, viewWidth, viewHeight, viewLeft, viewTop) {\n  var leftList = [];\n  var rightList = [];\n  var leftmostX = Number.MAX_VALUE;\n  var rightmostX = -Number.MAX_VALUE;\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var label = labelLayoutList[i].label;\n    if (isPositionCenter(labelLayoutList[i])) {\n      continue;\n    }\n    if (label.x < cx) {\n      leftmostX = Math.min(leftmostX, label.x);\n      leftList.push(labelLayoutList[i]);\n    } else {\n      rightmostX = Math.max(rightmostX, label.x);\n      rightList.push(labelLayoutList[i]);\n    }\n  }\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var layout = labelLayoutList[i];\n    if (!isPositionCenter(layout) && layout.linePoints) {\n      if (layout.labelStyleWidth != null) {\n        continue;\n      }\n      var label = layout.label;\n      var linePoints = layout.linePoints;\n      var targetTextWidth = void 0;\n      if (layout.labelAlignTo === 'edge') {\n        if (label.x < cx) {\n          targetTextWidth = linePoints[2][0] - layout.labelDistance - viewLeft - layout.edgeDistance;\n        } else {\n          targetTextWidth = viewLeft + viewWidth - layout.edgeDistance - linePoints[2][0] - layout.labelDistance;\n        }\n      } else if (layout.labelAlignTo === 'labelLine') {\n        if (label.x < cx) {\n          targetTextWidth = leftmostX - viewLeft - layout.bleedMargin;\n        } else {\n          targetTextWidth = viewLeft + viewWidth - rightmostX - layout.bleedMargin;\n        }\n      } else {\n        if (label.x < cx) {\n          targetTextWidth = label.x - viewLeft - layout.bleedMargin;\n        } else {\n          targetTextWidth = viewLeft + viewWidth - label.x - layout.bleedMargin;\n        }\n      }\n      layout.targetTextWidth = targetTextWidth;\n      constrainTextWidth(layout, targetTextWidth);\n    }\n  }\n  adjustSingleSide(rightList, cx, cy, r, 1, viewWidth, viewHeight, viewLeft, viewTop, rightmostX);\n  adjustSingleSide(leftList, cx, cy, r, -1, viewWidth, viewHeight, viewLeft, viewTop, leftmostX);\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var layout = labelLayoutList[i];\n    if (!isPositionCenter(layout) && layout.linePoints) {\n      var label = layout.label;\n      var linePoints = layout.linePoints;\n      var isAlignToEdge = layout.labelAlignTo === 'edge';\n      var padding = label.style.padding;\n      var paddingH = padding ? padding[1] + padding[3] : 0;\n      // textRect.width already contains paddingH if bgColor is set\n      var extraPaddingH = label.style.backgroundColor ? 0 : paddingH;\n      var realTextWidth = layout.rect.width + extraPaddingH;\n      var dist = linePoints[1][0] - linePoints[2][0];\n      if (isAlignToEdge) {\n        if (label.x < cx) {\n          linePoints[2][0] = viewLeft + layout.edgeDistance + realTextWidth + layout.labelDistance;\n        } else {\n          linePoints[2][0] = viewLeft + viewWidth - layout.edgeDistance - realTextWidth - layout.labelDistance;\n        }\n      } else {\n        if (label.x < cx) {\n          linePoints[2][0] = label.x + layout.labelDistance;\n        } else {\n          linePoints[2][0] = label.x - layout.labelDistance;\n        }\n        linePoints[1][0] = linePoints[2][0] + dist;\n      }\n      linePoints[1][1] = linePoints[2][1] = label.y;\n    }\n  }\n}\n/**\n * Set max width of each label, and then wrap each label to the max width.\n *\n * @param layout label layout\n * @param availableWidth max width for the label to display\n * @param forceRecalculate recaculate the text layout even if the current width\n * is smaller than `availableWidth`. This is useful when the text was previously\n * wrapped by calling `constrainTextWidth` but now `availableWidth` changed, in\n * which case, previous wrapping should be redo.\n */\nfunction constrainTextWidth(layout, availableWidth, forceRecalculate) {\n  if (forceRecalculate === void 0) {\n    forceRecalculate = false;\n  }\n  if (layout.labelStyleWidth != null) {\n    // User-defined style.width has the highest priority.\n    return;\n  }\n  var label = layout.label;\n  var style = label.style;\n  var textRect = layout.rect;\n  var bgColor = style.backgroundColor;\n  var padding = style.padding;\n  var paddingH = padding ? padding[1] + padding[3] : 0;\n  var overflow = style.overflow;\n  // textRect.width already contains paddingH if bgColor is set\n  var oldOuterWidth = textRect.width + (bgColor ? 0 : paddingH);\n  if (availableWidth < oldOuterWidth || forceRecalculate) {\n    var oldHeight = textRect.height;\n    if (overflow && overflow.match('break')) {\n      // Temporarily set background to be null to calculate\n      // the bounding box without background.\n      label.setStyle('backgroundColor', null);\n      // Set constraining width\n      label.setStyle('width', availableWidth - paddingH);\n      // This is the real bounding box of the text without padding.\n      var innerRect = label.getBoundingRect();\n      label.setStyle('width', Math.ceil(innerRect.width));\n      label.setStyle('backgroundColor', bgColor);\n    } else {\n      var availableInnerWidth = availableWidth - paddingH;\n      var newWidth = availableWidth < oldOuterWidth\n      // Current text is too wide, use `availableWidth` as max width.\n      ? availableInnerWidth :\n      // Current available width is enough, but the text may have\n      // already been wrapped with a smaller available width.\n      forceRecalculate ? availableInnerWidth > layout.unconstrainedWidth\n      // Current available is larger than text width,\n      // so don't constrain width (otherwise it may have\n      // empty space in the background).\n      ? null\n      // Current available is smaller than text width, so\n      // use the current available width as constraining\n      // width.\n      : availableInnerWidth\n      // Current available width is enough, so no need to\n      // constrain.\n      : null;\n      label.setStyle('width', newWidth);\n    }\n    var newRect = label.getBoundingRect();\n    textRect.width = newRect.width;\n    var margin = (label.style.margin || 0) + 2.1;\n    textRect.height = newRect.height + margin;\n    textRect.y -= (textRect.height - oldHeight) / 2;\n  }\n}\nfunction isPositionCenter(sectorShape) {\n  // Not change x for center label\n  return sectorShape.position === 'center';\n}\nexport default function pieLabelLayout(seriesModel) {\n  var data = seriesModel.getData();\n  var labelLayoutList = [];\n  var cx;\n  var cy;\n  var hasLabelRotate = false;\n  var minShowLabelRadian = (seriesModel.get('minShowLabelAngle') || 0) * RADIAN;\n  var viewRect = data.getLayout('viewRect');\n  var r = data.getLayout('r');\n  var viewWidth = viewRect.width;\n  var viewLeft = viewRect.x;\n  var viewTop = viewRect.y;\n  var viewHeight = viewRect.height;\n  function setNotShow(el) {\n    el.ignore = true;\n  }\n  function isLabelShown(label) {\n    if (!label.ignore) {\n      return true;\n    }\n    for (var key in label.states) {\n      if (label.states[key].ignore === false) {\n        return true;\n      }\n    }\n    return false;\n  }\n  data.each(function (idx) {\n    var sector = data.getItemGraphicEl(idx);\n    var sectorShape = sector.shape;\n    var label = sector.getTextContent();\n    var labelLine = sector.getTextGuideLine();\n    var itemModel = data.getItemModel(idx);\n    var labelModel = itemModel.getModel('label');\n    // Use position in normal or emphasis\n    var labelPosition = labelModel.get('position') || itemModel.get(['emphasis', 'label', 'position']);\n    var labelDistance = labelModel.get('distanceToLabelLine');\n    var labelAlignTo = labelModel.get('alignTo');\n    var edgeDistance = parsePercent(labelModel.get('edgeDistance'), viewWidth);\n    var bleedMargin = labelModel.get('bleedMargin');\n    var labelLineModel = itemModel.getModel('labelLine');\n    var labelLineLen = labelLineModel.get('length');\n    labelLineLen = parsePercent(labelLineLen, viewWidth);\n    var labelLineLen2 = labelLineModel.get('length2');\n    labelLineLen2 = parsePercent(labelLineLen2, viewWidth);\n    if (Math.abs(sectorShape.endAngle - sectorShape.startAngle) < minShowLabelRadian) {\n      each(label.states, setNotShow);\n      label.ignore = true;\n      if (labelLine) {\n        each(labelLine.states, setNotShow);\n        labelLine.ignore = true;\n      }\n      return;\n    }\n    if (!isLabelShown(label)) {\n      return;\n    }\n    var midAngle = (sectorShape.startAngle + sectorShape.endAngle) / 2;\n    var nx = Math.cos(midAngle);\n    var ny = Math.sin(midAngle);\n    var textX;\n    var textY;\n    var linePoints;\n    var textAlign;\n    cx = sectorShape.cx;\n    cy = sectorShape.cy;\n    var isLabelInside = labelPosition === 'inside' || labelPosition === 'inner';\n    if (labelPosition === 'center') {\n      textX = sectorShape.cx;\n      textY = sectorShape.cy;\n      textAlign = 'center';\n    } else {\n      var x1 = (isLabelInside ? (sectorShape.r + sectorShape.r0) / 2 * nx : sectorShape.r * nx) + cx;\n      var y1 = (isLabelInside ? (sectorShape.r + sectorShape.r0) / 2 * ny : sectorShape.r * ny) + cy;\n      textX = x1 + nx * 3;\n      textY = y1 + ny * 3;\n      if (!isLabelInside) {\n        // For roseType\n        var x2 = x1 + nx * (labelLineLen + r - sectorShape.r);\n        var y2 = y1 + ny * (labelLineLen + r - sectorShape.r);\n        var x3 = x2 + (nx < 0 ? -1 : 1) * labelLineLen2;\n        var y3 = y2;\n        if (labelAlignTo === 'edge') {\n          // Adjust textX because text align of edge is opposite\n          textX = nx < 0 ? viewLeft + edgeDistance : viewLeft + viewWidth - edgeDistance;\n        } else {\n          textX = x3 + (nx < 0 ? -labelDistance : labelDistance);\n        }\n        textY = y3;\n        linePoints = [[x1, y1], [x2, y2], [x3, y3]];\n      }\n      textAlign = isLabelInside ? 'center' : labelAlignTo === 'edge' ? nx > 0 ? 'right' : 'left' : nx > 0 ? 'left' : 'right';\n    }\n    var PI = Math.PI;\n    var labelRotate = 0;\n    var rotate = labelModel.get('rotate');\n    if (isNumber(rotate)) {\n      labelRotate = rotate * (PI / 180);\n    } else if (labelPosition === 'center') {\n      labelRotate = 0;\n    } else if (rotate === 'radial' || rotate === true) {\n      var radialAngle = nx < 0 ? -midAngle + PI : -midAngle;\n      labelRotate = radialAngle;\n    } else if (rotate === 'tangential' && labelPosition !== 'outside' && labelPosition !== 'outer') {\n      var rad = Math.atan2(nx, ny);\n      if (rad < 0) {\n        rad = PI * 2 + rad;\n      }\n      var isDown = ny > 0;\n      if (isDown) {\n        rad = PI + rad;\n      }\n      labelRotate = rad - PI;\n    }\n    hasLabelRotate = !!labelRotate;\n    label.x = textX;\n    label.y = textY;\n    label.rotation = labelRotate;\n    label.setStyle({\n      verticalAlign: 'middle'\n    });\n    // Not sectorShape the inside label\n    if (!isLabelInside) {\n      var textRect = label.getBoundingRect().clone();\n      textRect.applyTransform(label.getComputedTransform());\n      // Text has a default 1px stroke. Exclude this.\n      var margin = (label.style.margin || 0) + 2.1;\n      textRect.y -= margin / 2;\n      textRect.height += margin;\n      labelLayoutList.push({\n        label: label,\n        labelLine: labelLine,\n        position: labelPosition,\n        len: labelLineLen,\n        len2: labelLineLen2,\n        minTurnAngle: labelLineModel.get('minTurnAngle'),\n        maxSurfaceAngle: labelLineModel.get('maxSurfaceAngle'),\n        surfaceNormal: new Point(nx, ny),\n        linePoints: linePoints,\n        textAlign: textAlign,\n        labelDistance: labelDistance,\n        labelAlignTo: labelAlignTo,\n        edgeDistance: edgeDistance,\n        bleedMargin: bleedMargin,\n        rect: textRect,\n        unconstrainedWidth: textRect.width,\n        labelStyleWidth: label.style.width\n      });\n    } else {\n      label.setStyle({\n        align: textAlign\n      });\n      var selectState = label.states.select;\n      if (selectState) {\n        selectState.x += label.x;\n        selectState.y += label.y;\n      }\n    }\n    sector.setTextConfig({\n      inside: isLabelInside\n    });\n  });\n  if (!hasLabelRotate && seriesModel.get('avoidLabelOverlap')) {\n    avoidOverlap(labelLayoutList, cx, cy, r, viewWidth, viewHeight, viewLeft, viewTop);\n  }\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var layout = labelLayoutList[i];\n    var label = layout.label;\n    var labelLine = layout.labelLine;\n    var notShowLabel = isNaN(label.x) || isNaN(label.y);\n    if (label) {\n      label.setStyle({\n        align: layout.textAlign\n      });\n      if (notShowLabel) {\n        each(label.states, setNotShow);\n        label.ignore = true;\n      }\n      var selectState = label.states.select;\n      if (selectState) {\n        selectState.x += label.x;\n        selectState.y += label.y;\n      }\n    }\n    if (labelLine) {\n      var linePoints = layout.linePoints;\n      if (notShowLabel || !linePoints) {\n        each(labelLine.states, setNotShow);\n        labelLine.ignore = true;\n      } else {\n        limitTurnAngle(linePoints, layout.minTurnAngle);\n        limitSurfaceAngle(linePoints, layout.surfaceNormal, layout.maxSurfaceAngle);\n        labelLine.setShape({\n          points: linePoints\n        });\n        // Set the anchor to the midpoint of sector\n        label.__hostTarget.textGuideLineConfig = {\n          anchor: new Point(linePoints[0][0], linePoints[0][1])\n        };\n      }\n    }\n  }\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { isArray, map } from 'zrender/lib/core/util.js';\nimport { parsePercent } from 'zrender/lib/contain/text.js';\nexport function getSectorCornerRadius(model, shape, zeroIfNull) {\n  var cornerRadius = model.get('borderRadius');\n  if (cornerRadius == null) {\n    return zeroIfNull ? {\n      cornerRadius: 0\n    } : null;\n  }\n  if (!isArray(cornerRadius)) {\n    cornerRadius = [cornerRadius, cornerRadius, cornerRadius, cornerRadius];\n  }\n  var dr = Math.abs(shape.r || 0 - shape.r0 || 0);\n  return {\n    cornerRadius: map(cornerRadius, function (cr) {\n      return parsePercent(cr, dr);\n    })\n  };\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { extend, retrieve3 } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport ChartView from '../../view/Chart.js';\nimport labelLayout from './labelLayout.js';\nimport { setLabelLineStyle, getLabelLineStatesModels } from '../../label/labelGuideHelper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { getSectorCornerRadius } from '../helper/sectorHelper.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nimport { getBasicPieLayout, getSeriesLayoutData } from './pieLayout.js';\n/**\n * Piece of pie including Sector, Label, LabelLine\n */\nvar PiePiece = /** @class */function (_super) {\n  __extends(PiePiece, _super);\n  function PiePiece(data, idx, startAngle) {\n    var _this = _super.call(this) || this;\n    _this.z2 = 2;\n    var text = new graphic.Text();\n    _this.setTextContent(text);\n    _this.updateData(data, idx, startAngle, true);\n    return _this;\n  }\n  PiePiece.prototype.updateData = function (data, idx, startAngle, firstCreate) {\n    var sector = this;\n    var seriesModel = data.hostModel;\n    var itemModel = data.getItemModel(idx);\n    var emphasisModel = itemModel.getModel('emphasis');\n    var layout = data.getItemLayout(idx);\n    // cornerRadius & innerCornerRadius doesn't exist in the item layout. Use `0` if null value is specified.\n    // see `setItemLayout` in `pieLayout.ts`.\n    var sectorShape = extend(getSectorCornerRadius(itemModel.getModel('itemStyle'), layout, true), layout);\n    // Ignore NaN data.\n    if (isNaN(sectorShape.startAngle)) {\n      // Use NaN shape to avoid drawing shape.\n      sector.setShape(sectorShape);\n      return;\n    }\n    if (firstCreate) {\n      sector.setShape(sectorShape);\n      var animationType = seriesModel.getShallow('animationType');\n      if (seriesModel.ecModel.ssr) {\n        // Use scale animation in SSR mode(opacity?)\n        // Because CSS SVG animation doesn't support very customized shape animation.\n        graphic.initProps(sector, {\n          scaleX: 0,\n          scaleY: 0\n        }, seriesModel, {\n          dataIndex: idx,\n          isFrom: true\n        });\n        sector.originX = sectorShape.cx;\n        sector.originY = sectorShape.cy;\n      } else if (animationType === 'scale') {\n        sector.shape.r = layout.r0;\n        graphic.initProps(sector, {\n          shape: {\n            r: layout.r\n          }\n        }, seriesModel, idx);\n      }\n      // Expansion\n      else {\n        if (startAngle != null) {\n          sector.setShape({\n            startAngle: startAngle,\n            endAngle: startAngle\n          });\n          graphic.initProps(sector, {\n            shape: {\n              startAngle: layout.startAngle,\n              endAngle: layout.endAngle\n            }\n          }, seriesModel, idx);\n        } else {\n          sector.shape.endAngle = layout.startAngle;\n          graphic.updateProps(sector, {\n            shape: {\n              endAngle: layout.endAngle\n            }\n          }, seriesModel, idx);\n        }\n      }\n    } else {\n      saveOldStyle(sector);\n      // Transition animation from the old shape\n      graphic.updateProps(sector, {\n        shape: sectorShape\n      }, seriesModel, idx);\n    }\n    sector.useStyle(data.getItemVisual(idx, 'style'));\n    setStatesStylesFromModel(sector, itemModel);\n    var midAngle = (layout.startAngle + layout.endAngle) / 2;\n    var offset = seriesModel.get('selectedOffset');\n    var dx = Math.cos(midAngle) * offset;\n    var dy = Math.sin(midAngle) * offset;\n    var cursorStyle = itemModel.getShallow('cursor');\n    cursorStyle && sector.attr('cursor', cursorStyle);\n    this._updateLabel(seriesModel, data, idx);\n    sector.ensureState('emphasis').shape = extend({\n      r: layout.r + (emphasisModel.get('scale') ? emphasisModel.get('scaleSize') || 0 : 0)\n    }, getSectorCornerRadius(emphasisModel.getModel('itemStyle'), layout));\n    extend(sector.ensureState('select'), {\n      x: dx,\n      y: dy,\n      shape: getSectorCornerRadius(itemModel.getModel(['select', 'itemStyle']), layout)\n    });\n    extend(sector.ensureState('blur'), {\n      shape: getSectorCornerRadius(itemModel.getModel(['blur', 'itemStyle']), layout)\n    });\n    var labelLine = sector.getTextGuideLine();\n    var labelText = sector.getTextContent();\n    labelLine && extend(labelLine.ensureState('select'), {\n      x: dx,\n      y: dy\n    });\n    // TODO: needs dx, dy in zrender?\n    extend(labelText.ensureState('select'), {\n      x: dx,\n      y: dy\n    });\n    toggleHoverEmphasis(this, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  };\n  PiePiece.prototype._updateLabel = function (seriesModel, data, idx) {\n    var sector = this;\n    var itemModel = data.getItemModel(idx);\n    var labelLineModel = itemModel.getModel('labelLine');\n    var style = data.getItemVisual(idx, 'style');\n    var visualColor = style && style.fill;\n    var visualOpacity = style && style.opacity;\n    setLabelStyle(sector, getLabelStatesModels(itemModel), {\n      labelFetcher: data.hostModel,\n      labelDataIndex: idx,\n      inheritColor: visualColor,\n      defaultOpacity: visualOpacity,\n      defaultText: seriesModel.getFormattedLabel(idx, 'normal') || data.getName(idx)\n    });\n    var labelText = sector.getTextContent();\n    // Set textConfig on sector.\n    sector.setTextConfig({\n      // reset position, rotation\n      position: null,\n      rotation: null\n    });\n    // Make sure update style on labelText after setLabelStyle.\n    // Because setLabelStyle will replace a new style on it.\n    labelText.attr({\n      z2: 10\n    });\n    var labelPosition = seriesModel.get(['label', 'position']);\n    if (labelPosition !== 'outside' && labelPosition !== 'outer') {\n      sector.removeTextGuideLine();\n    } else {\n      var polyline = this.getTextGuideLine();\n      if (!polyline) {\n        polyline = new graphic.Polyline();\n        this.setTextGuideLine(polyline);\n      }\n      // Default use item visual color\n      setLabelLineStyle(this, getLabelLineStatesModels(itemModel), {\n        stroke: visualColor,\n        opacity: retrieve3(labelLineModel.get(['lineStyle', 'opacity']), visualOpacity, 1)\n      });\n    }\n  };\n  return PiePiece;\n}(graphic.Sector);\n// Pie view\nvar PieView = /** @class */function (_super) {\n  __extends(PieView, _super);\n  function PieView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.ignoreLabelLineUpdate = true;\n    return _this;\n  }\n  PieView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var group = this.group;\n    var startAngle;\n    // First render\n    if (!oldData && data.count() > 0) {\n      var shape = data.getItemLayout(0);\n      for (var s = 1; isNaN(shape && shape.startAngle) && s < data.count(); ++s) {\n        shape = data.getItemLayout(s);\n      }\n      if (shape) {\n        startAngle = shape.startAngle;\n      }\n    }\n    // remove empty-circle if it exists\n    if (this._emptyCircleSector) {\n      group.remove(this._emptyCircleSector);\n    }\n    // when all data are filtered, show lightgray empty circle\n    if (data.count() === 0 && seriesModel.get('showEmptyCircle')) {\n      var layoutData = getSeriesLayoutData(seriesModel);\n      var sector = new graphic.Sector({\n        shape: extend(getBasicPieLayout(seriesModel, api), layoutData)\n      });\n      sector.useStyle(seriesModel.getModel('emptyCircleStyle').getItemStyle());\n      this._emptyCircleSector = sector;\n      group.add(sector);\n    }\n    data.diff(oldData).add(function (idx) {\n      var piePiece = new PiePiece(data, idx, startAngle);\n      data.setItemGraphicEl(idx, piePiece);\n      group.add(piePiece);\n    }).update(function (newIdx, oldIdx) {\n      var piePiece = oldData.getItemGraphicEl(oldIdx);\n      piePiece.updateData(data, newIdx, startAngle);\n      piePiece.off('click');\n      group.add(piePiece);\n      data.setItemGraphicEl(newIdx, piePiece);\n    }).remove(function (idx) {\n      var piePiece = oldData.getItemGraphicEl(idx);\n      graphic.removeElementWithFadeOut(piePiece, seriesModel, idx);\n    }).execute();\n    labelLayout(seriesModel);\n    // Always use initial animation.\n    if (seriesModel.get('animationTypeUpdate') !== 'expansion') {\n      this._data = data;\n    }\n  };\n  PieView.prototype.dispose = function () {};\n  PieView.prototype.containPoint = function (point, seriesModel) {\n    var data = seriesModel.getData();\n    var itemLayout = data.getItemLayout(0);\n    if (itemLayout) {\n      var dx = point[0] - itemLayout.cx;\n      var dy = point[1] - itemLayout.cy;\n      var radius = Math.sqrt(dx * dx + dy * dy);\n      return radius <= itemLayout.r && radius >= itemLayout.r0;\n    }\n  };\n  PieView.type = 'pie';\n  return PieView;\n}(ChartView);\nexport default PieView;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport prepareSeriesDataSchema from '../../data/helper/createDimensions.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport { extend, isArray } from 'zrender/lib/core/util.js';\n/**\n * [Usage]:\n * (1)\n * createListSimply(seriesModel, ['value']);\n * (2)\n * createListSimply(seriesModel, {\n *     coordDimensions: ['value'],\n *     dimensionsCount: 5\n * });\n */\nexport default function createSeriesDataSimply(seriesModel, opt, nameList) {\n  opt = isArray(opt) && {\n    coordDimensions: opt\n  } || extend({\n    encodeDefine: seriesModel.getEncode()\n  }, opt);\n  var source = seriesModel.getSource();\n  var dimensions = prepareSeriesDataSchema(source, opt).dimensions;\n  var list = new SeriesData(dimensions, seriesModel);\n  list.initData(source, nameList);\n  return list;\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n/**\n * LegendVisualProvider is an bridge that pick encoded color from data and\n * provide to the legend component.\n */\nvar LegendVisualProvider = /** @class */function () {\n  function LegendVisualProvider(\n  // Function to get data after filtered. It stores all the encoding info\n  getDataWithEncodedVisual,\n  // Function to get raw data before filtered.\n  getRawData) {\n    this._getDataWithEncodedVisual = getDataWithEncodedVisual;\n    this._getRawData = getRawData;\n  }\n  LegendVisualProvider.prototype.getAllNames = function () {\n    var rawData = this._getRawData();\n    // We find the name from the raw data. In case it's filtered by the legend component.\n    // Normally, the name can be found in rawData, but can't be found in filtered data will display as gray.\n    return rawData.mapArray(rawData.getName);\n  };\n  LegendVisualProvider.prototype.containName = function (name) {\n    var rawData = this._getRawData();\n    return rawData.indexOfName(name) >= 0;\n  };\n  LegendVisualProvider.prototype.indexOfName = function (name) {\n    // Only get data when necessary.\n    // Because LegendVisualProvider constructor may be new in the stage that data is not prepared yet.\n    // Invoking Series#getData immediately will throw an error.\n    var dataWithEncodedVisual = this._getDataWithEncodedVisual();\n    return dataWithEncodedVisual.indexOfName(name);\n  };\n  LegendVisualProvider.prototype.getItemVisual = function (dataIndex, key) {\n    // Get encoded visual properties from final filtered data.\n    var dataWithEncodedVisual = this._getDataWithEncodedVisual();\n    return dataWithEncodedVisual.getItemVisual(dataIndex, key);\n  };\n  return LegendVisualProvider;\n}();\nexport default LegendVisualProvider;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesDataSimply from '../helper/createSeriesDataSimply.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\nimport { getPercentSeats } from '../../util/number.js';\nimport { makeSeriesEncodeForNameBased } from '../../data/helper/sourceHelper.js';\nimport LegendVisualProvider from '../../visual/LegendVisualProvider.js';\nimport SeriesModel from '../../model/Series.js';\nvar innerData = modelUtil.makeInner();\nvar PieSeriesModel = /** @class */function (_super) {\n  __extends(PieSeriesModel, _super);\n  function PieSeriesModel() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  /**\n   * @overwrite\n   */\n  PieSeriesModel.prototype.init = function (option) {\n    _super.prototype.init.apply(this, arguments);\n    // Enable legend selection for each data item\n    // Use a function instead of direct access because data reference may changed\n    this.legendVisualProvider = new LegendVisualProvider(zrUtil.bind(this.getData, this), zrUtil.bind(this.getRawData, this));\n    this._defaultLabelLine(option);\n  };\n  /**\n   * @overwrite\n   */\n  PieSeriesModel.prototype.mergeOption = function () {\n    _super.prototype.mergeOption.apply(this, arguments);\n  };\n  /**\n   * @overwrite\n   */\n  PieSeriesModel.prototype.getInitialData = function () {\n    return createSeriesDataSimply(this, {\n      coordDimensions: ['value'],\n      encodeDefaulter: zrUtil.curry(makeSeriesEncodeForNameBased, this)\n    });\n  };\n  /**\n   * @overwrite\n   */\n  PieSeriesModel.prototype.getDataParams = function (dataIndex) {\n    var data = this.getData();\n    // update seats when data is changed\n    var dataInner = innerData(data);\n    var seats = dataInner.seats;\n    if (!seats) {\n      var valueList_1 = [];\n      data.each(data.mapDimension('value'), function (value) {\n        valueList_1.push(value);\n      });\n      seats = dataInner.seats = getPercentSeats(valueList_1, data.hostModel.get('percentPrecision'));\n    }\n    var params = _super.prototype.getDataParams.call(this, dataIndex);\n    // seats may be empty when sum is 0\n    params.percent = seats[dataIndex] || 0;\n    params.$vars.push('percent');\n    return params;\n  };\n  PieSeriesModel.prototype._defaultLabelLine = function (option) {\n    // Extend labelLine emphasis\n    modelUtil.defaultEmphasis(option, 'labelLine', ['show']);\n    var labelLineNormalOpt = option.labelLine;\n    var labelLineEmphasisOpt = option.emphasis.labelLine;\n    // Not show label line if `label.normal.show = false`\n    labelLineNormalOpt.show = labelLineNormalOpt.show && option.label.show;\n    labelLineEmphasisOpt.show = labelLineEmphasisOpt.show && option.emphasis.label.show;\n  };\n  PieSeriesModel.type = 'series.pie';\n  PieSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    legendHoverLink: true,\n    colorBy: 'data',\n    // 默认全局居中\n    center: ['50%', '50%'],\n    radius: [0, '75%'],\n    // 默认顺时针\n    clockwise: true,\n    startAngle: 90,\n    endAngle: 'auto',\n    padAngle: 0,\n    // 最小角度改为0\n    minAngle: 0,\n    // If the angle of a sector less than `minShowLabelAngle`,\n    // the label will not be displayed.\n    minShowLabelAngle: 0,\n    // 选中时扇区偏移量\n    selectedOffset: 10,\n    // 选择模式，默认关闭，可选single，multiple\n    // selectedMode: false,\n    // 南丁格尔玫瑰图模式，'radius'（半径） | 'area'（面积）\n    // roseType: null,\n    percentPrecision: 2,\n    // If still show when all data zero.\n    stillShowZeroSum: true,\n    // cursor: null,\n    left: 0,\n    top: 0,\n    right: 0,\n    bottom: 0,\n    width: null,\n    height: null,\n    label: {\n      // color: 'inherit',\n      // If rotate around circle\n      rotate: 0,\n      show: true,\n      overflow: 'truncate',\n      // 'outer', 'inside', 'center'\n      position: 'outer',\n      // 'none', 'labelLine', 'edge'. Works only when position is 'outer'\n      alignTo: 'none',\n      // Closest distance between label and chart edge.\n      // Works only position is 'outer' and alignTo is 'edge'.\n      edgeDistance: '25%',\n      // Works only position is 'outer' and alignTo is not 'edge'.\n      bleedMargin: 10,\n      // Distance between text and label line.\n      distanceToLabelLine: 5\n      // formatter: 标签文本格式器，同 tooltip.formatter，不支持异步回调\n      // 默认使用全局文本样式，详见 textStyle\n      // distance: 当position为inner时有效，为label位置到圆心的距离与圆半径(环状图为内外半径和)的比例系数\n    },\n\n    // Enabled when label.normal.position is 'outer'\n    labelLine: {\n      show: true,\n      // 引导线两段中的第一段长度\n      length: 15,\n      // 引导线两段中的第二段长度\n      length2: 15,\n      smooth: false,\n      minTurnAngle: 90,\n      maxSurfaceAngle: 90,\n      lineStyle: {\n        // color: 各异,\n        width: 1,\n        type: 'solid'\n      }\n    },\n    itemStyle: {\n      borderWidth: 1,\n      borderJoin: 'round'\n    },\n    showEmptyCircle: true,\n    emptyCircleStyle: {\n      color: 'lightgray',\n      opacity: 1\n    },\n    labelLayout: {\n      // Hide the overlapped label.\n      hideOverlap: true\n    },\n    emphasis: {\n      scale: true,\n      scaleSize: 5\n    },\n    // If use strategy to avoid label overlapping\n    avoidLabelOverlap: true,\n    // Animation type. Valid values: expansion, scale\n    animationType: 'expansion',\n    animationDuration: 1000,\n    // Animation type when update. Valid values: transition, expansion\n    animationTypeUpdate: 'transition',\n    animationEasingUpdate: 'cubicInOut',\n    animationDurationUpdate: 500,\n    animationEasing: 'cubicInOut'\n  };\n  return PieSeriesModel;\n}(SeriesModel);\nexport default PieSeriesModel;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { isNumber } from 'zrender/lib/core/util.js';\nexport default function negativeDataFilter(seriesType) {\n  return {\n    seriesType: seriesType,\n    reset: function (seriesModel, ecModel) {\n      var data = seriesModel.getData();\n      data.filterSelf(function (idx) {\n        // handle negative value condition\n        var valueDim = data.mapDimension('value');\n        var curValue = data.get(valueDim, idx);\n        if (isNumber(curValue) && !isNaN(curValue) && curValue < 0) {\n          return false;\n        }\n        return true;\n      });\n    }\n  };\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createLegacyDataSelectAction } from '../../legacy/dataSelectAction.js';\nimport pieLayout from '../pie/pieLayout.js';\nimport dataFilter from '../../processor/dataFilter.js';\nimport { curry } from 'zrender/lib/core/util.js';\nimport PieView from './PieView.js';\nimport PieSeriesModel from './PieSeries.js';\nimport negativeDataFilter from '../../processor/negativeDataFilter.js';\nexport function install(registers) {\n  registers.registerChartView(PieView);\n  registers.registerSeriesModel(PieSeriesModel);\n  createLegacyDataSelectAction('pie', registers.registerAction);\n  registers.registerLayout(curry(pieLayout, 'pie'));\n  registers.registerProcessor(dataFilter('pie'));\n  registers.registerProcessor(negativeDataFilter('pie'));\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA,IAAI,MAAM,KAAK,KAAK;AACpB,IAAI,SAAS,KAAK,KAAK;AACvB,SAAS,YAAY,aAAa,KAAK;AACrC,SAAc,cAAc,YAAY,mBAAmB,GAAG;AAAA,IAC5D,OAAO,IAAI,SAAS;AAAA,IACpB,QAAQ,IAAI,UAAU;AAAA,EACxB,CAAC;AACH;AACO,SAAS,kBAAkB,aAAa,KAAK;AAClD,MAAI,WAAW,YAAY,aAAa,GAAG;AAE3C,MAAI,SAAS,YAAY,IAAI,QAAQ;AACrC,MAAI,SAAS,YAAY,IAAI,QAAQ;AACrC,MAAI,CAAQ,QAAQ,MAAM,GAAG;AAC3B,aAAS,CAAC,GAAG,MAAM;AAAA,EACrB;AACA,MAAI,QAAQA,cAAa,SAAS,OAAO,IAAI,SAAS,CAAC;AACvD,MAAI,SAASA,cAAa,SAAS,QAAQ,IAAI,UAAU,CAAC;AAC1D,MAAI,OAAO,KAAK,IAAI,OAAO,MAAM;AACjC,MAAI,KAAKA,cAAa,OAAO,CAAC,GAAG,OAAO,CAAC;AACzC,MAAI,IAAIA,cAAa,OAAO,CAAC,GAAG,OAAO,CAAC;AACxC,MAAI;AACJ,MAAI;AACJ,MAAI,WAAW,YAAY;AAC3B,MAAI,UAAU;AAEZ,QAAI,QAAQ,SAAS,YAAY,MAAM;AACvC,SAAK,MAAM,CAAC,KAAK;AACjB,SAAK,MAAM,CAAC,KAAK;AAAA,EACnB,OAAO;AACL,QAAI,CAAQ,QAAQ,MAAM,GAAG;AAC3B,eAAS,CAAC,QAAQ,MAAM;AAAA,IAC1B;AACA,SAAKA,cAAa,OAAO,CAAC,GAAG,KAAK,IAAI,SAAS;AAC/C,SAAKA,cAAa,OAAO,CAAC,GAAG,MAAM,IAAI,SAAS;AAAA,EAClD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACe,SAAR,UAA2B,YAAY,SAAS,KAAK;AAC1D,UAAQ,iBAAiB,YAAY,SAAU,aAAa;AAC1D,QAAI,OAAO,YAAY,QAAQ;AAC/B,QAAI,WAAW,KAAK,aAAa,OAAO;AACxC,QAAI,WAAW,YAAY,aAAa,GAAG;AAC3C,QAAI,KAAK,kBAAkB,aAAa,GAAG,GACzC,KAAK,GAAG,IACR,KAAK,GAAG,IACR,IAAI,GAAG,GACP,KAAK,GAAG;AACV,QAAI,aAAa,CAAC,YAAY,IAAI,YAAY,IAAI;AAClD,QAAI,WAAW,YAAY,IAAI,UAAU;AACzC,QAAI,WAAW,YAAY,IAAI,UAAU,IAAI;AAC7C,eAAW,aAAa,SAAS,aAAa,MAAM,CAAC,WAAW;AAChE,QAAI,WAAW,YAAY,IAAI,UAAU,IAAI;AAC7C,QAAI,iBAAiB,WAAW;AAChC,QAAI,iBAAiB;AACrB,SAAK,KAAK,UAAU,SAAU,OAAO;AACnC,OAAC,MAAM,KAAK,KAAK;AAAA,IACnB,CAAC;AACD,QAAI,MAAM,KAAK,OAAO,QAAQ;AAE9B,QAAI,aAAa,KAAK,MAAM,OAAO,kBAAkB;AACrD,QAAI,YAAY,YAAY,IAAI,WAAW;AAC3C,QAAI,WAAW,YAAY,IAAI,UAAU;AACzC,QAAI,mBAAmB,YAAY,IAAI,kBAAkB;AAEzD,QAAI,SAAS,KAAK,cAAc,QAAQ;AACxC,WAAO,CAAC,IAAI;AACZ,QAAI,MAAM,YAAY,IAAI;AAC1B,QAAI,SAAS,CAAC,YAAY,QAAQ;AAClC,QAAI,eAAe,MAAM,WAAW;AACpC,uBAAmB,QAAQ,CAAC,SAAS;AACrC,iBAAa,OAAO,CAAC,GAAG,WAAW,OAAO,CAAC;AAC3C,QAAI,aAAa,oBAAoB,WAAW;AAChD,eAAW,aAAa;AACxB,eAAW,WAAW;AACtB,eAAW,YAAY;AACvB,QAAI,aAAa,KAAK,IAAI,WAAW,UAAU;AAE/C,QAAI,YAAY;AAChB,QAAI,6BAA6B;AACjC,QAAI,eAAe;AACnB,SAAK,UAAU;AAAA,MACb;AAAA,MACA;AAAA,IACF,CAAC;AACD,SAAK,KAAK,UAAU,SAAU,OAAO,KAAK;AACxC,UAAI;AACJ,UAAI,MAAM,KAAK,GAAG;AAChB,aAAK,cAAc,KAAK;AAAA,UACtB,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,GAAG,WAAW,MAAM;AAAA,QACtB,CAAC;AACD;AAAA,MACF;AAEA,UAAI,aAAa,QAAQ;AACvB,gBAAQ,QAAQ,KAAK,mBAAmB,aAAa,QAAQ;AAAA,MAC/D,OAAO;AACL,gBAAQ,aAAa;AAAA,MACvB;AACA,UAAI,QAAQ,gBAAgB;AAC1B,gBAAQ;AACR,qBAAa;AAAA,MACf,OAAO;AACL,sCAA8B;AAAA,MAChC;AACA,UAAIC,YAAW,eAAe,MAAM;AAEpC,UAAI,mBAAmB;AACvB,UAAI,iBAAiB;AACrB,UAAI,WAAW,OAAO;AACpB,2BAAmB,eAAe,MAAM,QAAQ;AAChD,yBAAiB;AAAA,MACnB,OAAO;AACL,2BAAmB,eAAe;AAClC,yBAAiBA,YAAW;AAAA,MAC9B;AACA,WAAK,cAAc,KAAK;AAAA,QACtB;AAAA,QACA,YAAY;AAAA,QACZ,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,GAAG,WAAW,UAAU,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI;AAAA,MACpD,CAAC;AACD,qBAAeA;AAAA,IACjB,CAAC;AAGD,QAAI,YAAY,OAAO,gBAAgB;AAGrC,UAAI,aAAa,MAAM;AACrB,YAAI,UAAU,aAAa;AAC3B,aAAK,KAAK,UAAU,SAAU,OAAO,KAAK;AACxC,cAAI,CAAC,MAAM,KAAK,GAAG;AACjB,gBAAI,WAAW,KAAK,cAAc,GAAG;AACrC,qBAAS,QAAQ;AACjB,gBAAI,mBAAmB;AACvB,gBAAI,iBAAiB;AACrB,gBAAI,UAAU,UAAU;AACtB,iCAAmB,aAAa,OAAO,MAAM,IAAI,KAAK;AACtD,+BAAiB;AAAA,YACnB,OAAO;AACL,iCAAmB,aAAa,MAAM,MAAM,UAAU;AACtD,+BAAiB,aAAa,OAAO,MAAM,KAAK,UAAU;AAAA,YAC5D;AACA,qBAAS,aAAa;AACtB,qBAAS,WAAW;AAAA,UACtB;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,qBAAa,YAAY;AACzB,uBAAe;AACf,aAAK,KAAK,UAAU,SAAU,OAAO,KAAK;AACxC,cAAI,CAAC,MAAM,KAAK,GAAG;AACjB,gBAAI,WAAW,KAAK,cAAc,GAAG;AACrC,gBAAI,QAAQ,SAAS,UAAU,iBAAiB,iBAAiB,QAAQ;AACzE,gBAAI,mBAAmB;AACvB,gBAAI,iBAAiB;AACrB,gBAAI,QAAQ,UAAU;AACpB,iCAAmB,eAAe,MAAM,QAAQ;AAChD,+BAAiB;AAAA,YACnB,OAAO;AACL,iCAAmB,eAAe;AAClC,+BAAiB,eAAe,MAAM,QAAQ;AAAA,YAChD;AACA,qBAAS,aAAa;AACtB,qBAAS,WAAW;AACpB,4BAAgB,MAAM;AAAA,UACxB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AACH;AACO,IAAI,sBAAsB,UAAU;;;AClM5B,SAAR,WAA4B,YAAY;AAC7C,SAAO;AAAA,IACL;AAAA,IACA,OAAO,SAAU,aAAa,SAAS;AACrC,UAAI,eAAe,QAAQ,eAAe;AAAA,QACxC,UAAU;AAAA,MACZ,CAAC;AACD,UAAI,CAAC,gBAAgB,CAAC,aAAa,QAAQ;AACzC;AAAA,MACF;AACA,UAAI,OAAO,YAAY,QAAQ;AAC/B,WAAK,WAAW,SAAU,KAAK;AAC7B,YAAI,OAAO,KAAK,QAAQ,GAAG;AAE3B,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAE5C,cAAI,CAAC,aAAa,CAAC,EAAE,WAAW,IAAI,GAAG;AACrC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;AClBA,IAAIC,UAAS,KAAK,KAAK;AACvB,SAAS,iBAAiB,MAAM,IAAI,IAAI,GAAG,KAAK,WAAW,YAAY,UAAU,SAAS,WAAW;AACnG,MAAI,KAAK,SAAS,GAAG;AACnB;AAAA,EACF;AACA;AACA,WAAS,wCAAwC,MAAM;AACrD,QAAI,KAAK,KAAK;AACd,QAAI,MAAM,KAAK;AACf,aAASC,KAAI,GAAGA,KAAI,KAAK,KAAK,QAAQA,MAAK;AACzC,UAAI,OAAO,KAAK,KAAKA,EAAC;AACtB,UAAI,KAAK,KAAK,IAAI,KAAK,MAAM,IAAI,EAAE;AAEnC,UAAI,KAAK,IAAI,KAAK;AAClB,UAAI,MAAM,KAAK;AAEf,UAAIC,MAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AACtD,UAAI,OAAO,MAAMA,MAAK,KAAK,QAAQ;AACnC,UAAI,SAAS,OAAO,KAAK,MAAM;AAC/B,UAAI,iBAAiB,KAAK,kBAAkB,SAAS;AAErD,yBAAmB,MAAM,gBAAgB,IAAI;AAC7C,WAAK,MAAM,IAAI;AAAA,IACjB;AAAA,EACF;AAEA,WAAS,aAAa,OAAO;AAE3B,QAAI,UAAU;AAAA,MACZ,MAAM,CAAC;AAAA,MACP,MAAM;AAAA,IACR;AACA,QAAI,aAAa;AAAA,MACf,MAAM,CAAC;AAAA,MACP,MAAM;AAAA,IACR;AACA,aAASD,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACrC,UAAI,MAAMA,EAAC,EAAE,iBAAiB,QAAQ;AACpC;AAAA,MACF;AACA,UAAI,OAAO,MAAMA,EAAC;AAClB,UAAI,OAAO,KAAK,MAAM,IAAI,KAAK,aAAa;AAC5C,UAAI,KAAK,KAAK,IAAI,KAAK,MAAM,IAAI,EAAE;AACnC,UAAI,MAAM,KAAK,MAAM;AACnB,YAAIC,MAAK,KAAK,MAAM,IAAI,KAAK,KAAK,OAAO;AAEzC,YAAI,KAAK,IAAI,KAAK;AAElB,YAAI,KAAK,KAAK,IAAIA,GAAE,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,IAAIA,MAAKA,MAAK,KAAK,GAAG,IAAI;AAC5E,aAAK,KAAK;AACV,aAAK,OAAO;AAAA,MACd;AACA,WAAK,KAAK,KAAK,IAAI;AAAA,IACrB;AACA,4CAAwC,OAAO;AAC/C,4CAAwC,UAAU;AAAA,EACpD;AACA,MAAI,MAAM,KAAK;AACf,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAI,KAAK,CAAC,EAAE,aAAa,WAAW,KAAK,CAAC,EAAE,iBAAiB,aAAa;AACxE,UAAI,KAAK,KAAK,CAAC,EAAE,MAAM,IAAI;AAC3B,WAAK,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,KAAK;AAC5B,WAAK,CAAC,EAAE,MAAM,IAAI;AAAA,IACpB;AAAA,EACF;AACA,MAAI,eAAe,MAAM,SAAS,UAAU,UAAU,GAAG;AACvD,iBAAa,IAAI;AAAA,EACnB;AACF;AACA,SAAS,aAAa,iBAAiB,IAAI,IAAI,GAAG,WAAW,YAAY,UAAU,SAAS;AAC1F,MAAI,WAAW,CAAC;AAChB,MAAI,YAAY,CAAC;AACjB,MAAI,YAAY,OAAO;AACvB,MAAI,aAAa,CAAC,OAAO;AACzB,WAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,QAAI,QAAQ,gBAAgB,CAAC,EAAE;AAC/B,QAAI,iBAAiB,gBAAgB,CAAC,CAAC,GAAG;AACxC;AAAA,IACF;AACA,QAAI,MAAM,IAAI,IAAI;AAChB,kBAAY,KAAK,IAAI,WAAW,MAAM,CAAC;AACvC,eAAS,KAAK,gBAAgB,CAAC,CAAC;AAAA,IAClC,OAAO;AACL,mBAAa,KAAK,IAAI,YAAY,MAAM,CAAC;AACzC,gBAAU,KAAK,gBAAgB,CAAC,CAAC;AAAA,IACnC;AAAA,EACF;AACA,WAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,QAAI,SAAS,gBAAgB,CAAC;AAC9B,QAAI,CAAC,iBAAiB,MAAM,KAAK,OAAO,YAAY;AAClD,UAAI,OAAO,mBAAmB,MAAM;AAClC;AAAA,MACF;AACA,UAAI,QAAQ,OAAO;AACnB,UAAI,aAAa,OAAO;AACxB,UAAI,kBAAkB;AACtB,UAAI,OAAO,iBAAiB,QAAQ;AAClC,YAAI,MAAM,IAAI,IAAI;AAChB,4BAAkB,WAAW,CAAC,EAAE,CAAC,IAAI,OAAO,gBAAgB,WAAW,OAAO;AAAA,QAChF,OAAO;AACL,4BAAkB,WAAW,YAAY,OAAO,eAAe,WAAW,CAAC,EAAE,CAAC,IAAI,OAAO;AAAA,QAC3F;AAAA,MACF,WAAW,OAAO,iBAAiB,aAAa;AAC9C,YAAI,MAAM,IAAI,IAAI;AAChB,4BAAkB,YAAY,WAAW,OAAO;AAAA,QAClD,OAAO;AACL,4BAAkB,WAAW,YAAY,aAAa,OAAO;AAAA,QAC/D;AAAA,MACF,OAAO;AACL,YAAI,MAAM,IAAI,IAAI;AAChB,4BAAkB,MAAM,IAAI,WAAW,OAAO;AAAA,QAChD,OAAO;AACL,4BAAkB,WAAW,YAAY,MAAM,IAAI,OAAO;AAAA,QAC5D;AAAA,MACF;AACA,aAAO,kBAAkB;AACzB,yBAAmB,QAAQ,eAAe;AAAA,IAC5C;AAAA,EACF;AACA,mBAAiB,WAAW,IAAI,IAAI,GAAG,GAAG,WAAW,YAAY,UAAU,SAAS,UAAU;AAC9F,mBAAiB,UAAU,IAAI,IAAI,GAAG,IAAI,WAAW,YAAY,UAAU,SAAS,SAAS;AAC7F,WAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,QAAI,SAAS,gBAAgB,CAAC;AAC9B,QAAI,CAAC,iBAAiB,MAAM,KAAK,OAAO,YAAY;AAClD,UAAI,QAAQ,OAAO;AACnB,UAAI,aAAa,OAAO;AACxB,UAAI,gBAAgB,OAAO,iBAAiB;AAC5C,UAAI,UAAU,MAAM,MAAM;AAC1B,UAAI,WAAW,UAAU,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI;AAEnD,UAAI,gBAAgB,MAAM,MAAM,kBAAkB,IAAI;AACtD,UAAI,gBAAgB,OAAO,KAAK,QAAQ;AACxC,UAAI,OAAO,WAAW,CAAC,EAAE,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;AAC7C,UAAI,eAAe;AACjB,YAAI,MAAM,IAAI,IAAI;AAChB,qBAAW,CAAC,EAAE,CAAC,IAAI,WAAW,OAAO,eAAe,gBAAgB,OAAO;AAAA,QAC7E,OAAO;AACL,qBAAW,CAAC,EAAE,CAAC,IAAI,WAAW,YAAY,OAAO,eAAe,gBAAgB,OAAO;AAAA,QACzF;AAAA,MACF,OAAO;AACL,YAAI,MAAM,IAAI,IAAI;AAChB,qBAAW,CAAC,EAAE,CAAC,IAAI,MAAM,IAAI,OAAO;AAAA,QACtC,OAAO;AACL,qBAAW,CAAC,EAAE,CAAC,IAAI,MAAM,IAAI,OAAO;AAAA,QACtC;AACA,mBAAW,CAAC,EAAE,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC,IAAI;AAAA,MACxC;AACA,iBAAW,CAAC,EAAE,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC,IAAI,MAAM;AAAA,IAC9C;AAAA,EACF;AACF;AAWA,SAAS,mBAAmB,QAAQ,gBAAgB,kBAAkB;AACpE,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB;AAAA,EACrB;AACA,MAAI,OAAO,mBAAmB,MAAM;AAElC;AAAA,EACF;AACA,MAAI,QAAQ,OAAO;AACnB,MAAI,QAAQ,MAAM;AAClB,MAAI,WAAW,OAAO;AACtB,MAAI,UAAU,MAAM;AACpB,MAAI,UAAU,MAAM;AACpB,MAAI,WAAW,UAAU,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI;AACnD,MAAI,WAAW,MAAM;AAErB,MAAI,gBAAgB,SAAS,SAAS,UAAU,IAAI;AACpD,MAAI,iBAAiB,iBAAiB,kBAAkB;AACtD,QAAI,YAAY,SAAS;AACzB,QAAI,YAAY,SAAS,MAAM,OAAO,GAAG;AAGvC,YAAM,SAAS,mBAAmB,IAAI;AAEtC,YAAM,SAAS,SAAS,iBAAiB,QAAQ;AAEjD,UAAI,YAAY,MAAM,gBAAgB;AACtC,YAAM,SAAS,SAAS,KAAK,KAAK,UAAU,KAAK,CAAC;AAClD,YAAM,SAAS,mBAAmB,OAAO;AAAA,IAC3C,OAAO;AACL,UAAI,sBAAsB,iBAAiB;AAC3C,UAAI,WAAW,iBAAiB,gBAE9B;AAAA;AAAA;AAAA,QAGF,mBAAmB,sBAAsB,OAAO,qBAI9C,OAIA,sBAGA;AAAA;AACF,YAAM,SAAS,SAAS,QAAQ;AAAA,IAClC;AACA,QAAI,UAAU,MAAM,gBAAgB;AACpC,aAAS,QAAQ,QAAQ;AACzB,QAAI,UAAU,MAAM,MAAM,UAAU,KAAK;AACzC,aAAS,SAAS,QAAQ,SAAS;AACnC,aAAS,MAAM,SAAS,SAAS,aAAa;AAAA,EAChD;AACF;AACA,SAAS,iBAAiB,aAAa;AAErC,SAAO,YAAY,aAAa;AAClC;AACe,SAAR,eAAgC,aAAa;AAClD,MAAI,OAAO,YAAY,QAAQ;AAC/B,MAAI,kBAAkB,CAAC;AACvB,MAAI;AACJ,MAAI;AACJ,MAAI,iBAAiB;AACrB,MAAI,sBAAsB,YAAY,IAAI,mBAAmB,KAAK,KAAKF;AACvE,MAAI,WAAW,KAAK,UAAU,UAAU;AACxC,MAAI,IAAI,KAAK,UAAU,GAAG;AAC1B,MAAI,YAAY,SAAS;AACzB,MAAI,WAAW,SAAS;AACxB,MAAI,UAAU,SAAS;AACvB,MAAI,aAAa,SAAS;AAC1B,WAAS,WAAW,IAAI;AACtB,OAAG,SAAS;AAAA,EACd;AACA,WAAS,aAAaG,QAAO;AAC3B,QAAI,CAACA,OAAM,QAAQ;AACjB,aAAO;AAAA,IACT;AACA,aAAS,OAAOA,OAAM,QAAQ;AAC5B,UAAIA,OAAM,OAAO,GAAG,EAAE,WAAW,OAAO;AACtC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,OAAK,KAAK,SAAU,KAAK;AACvB,QAAI,SAAS,KAAK,iBAAiB,GAAG;AACtC,QAAI,cAAc,OAAO;AACzB,QAAIA,SAAQ,OAAO,eAAe;AAClC,QAAIC,aAAY,OAAO,iBAAiB;AACxC,QAAI,YAAY,KAAK,aAAa,GAAG;AACrC,QAAI,aAAa,UAAU,SAAS,OAAO;AAE3C,QAAI,gBAAgB,WAAW,IAAI,UAAU,KAAK,UAAU,IAAI,CAAC,YAAY,SAAS,UAAU,CAAC;AACjG,QAAI,gBAAgB,WAAW,IAAI,qBAAqB;AACxD,QAAI,eAAe,WAAW,IAAI,SAAS;AAC3C,QAAI,eAAeC,cAAa,WAAW,IAAI,cAAc,GAAG,SAAS;AACzE,QAAI,cAAc,WAAW,IAAI,aAAa;AAC9C,QAAI,iBAAiB,UAAU,SAAS,WAAW;AACnD,QAAI,eAAe,eAAe,IAAI,QAAQ;AAC9C,mBAAeA,cAAa,cAAc,SAAS;AACnD,QAAI,gBAAgB,eAAe,IAAI,SAAS;AAChD,oBAAgBA,cAAa,eAAe,SAAS;AACrD,QAAI,KAAK,IAAI,YAAY,WAAW,YAAY,UAAU,IAAI,oBAAoB;AAChF,WAAKF,OAAM,QAAQ,UAAU;AAC7B,MAAAA,OAAM,SAAS;AACf,UAAIC,YAAW;AACb,aAAKA,WAAU,QAAQ,UAAU;AACjC,QAAAA,WAAU,SAAS;AAAA,MACrB;AACA;AAAA,IACF;AACA,QAAI,CAAC,aAAaD,MAAK,GAAG;AACxB;AAAA,IACF;AACA,QAAI,YAAY,YAAY,aAAa,YAAY,YAAY;AACjE,QAAI,KAAK,KAAK,IAAI,QAAQ;AAC1B,QAAI,KAAK,KAAK,IAAI,QAAQ;AAC1B,QAAI;AACJ,QAAI;AACJ,QAAIG;AACJ,QAAI;AACJ,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,QAAI,gBAAgB,kBAAkB,YAAY,kBAAkB;AACpE,QAAI,kBAAkB,UAAU;AAC9B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AACpB,kBAAY;AAAA,IACd,OAAO;AACL,UAAI,MAAM,iBAAiB,YAAY,IAAI,YAAY,MAAM,IAAI,KAAK,YAAY,IAAI,MAAM;AAC5F,UAAI,MAAM,iBAAiB,YAAY,IAAI,YAAY,MAAM,IAAI,KAAK,YAAY,IAAI,MAAM;AAC5F,cAAQ,KAAK,KAAK;AAClB,cAAQ,KAAK,KAAK;AAClB,UAAI,CAAC,eAAe;AAElB,YAAI,KAAK,KAAK,MAAM,eAAe,IAAI,YAAY;AACnD,YAAI,KAAK,KAAK,MAAM,eAAe,IAAI,YAAY;AACnD,YAAI,KAAK,MAAM,KAAK,IAAI,KAAK,KAAK;AAClC,YAAI,KAAK;AACT,YAAI,iBAAiB,QAAQ;AAE3B,kBAAQ,KAAK,IAAI,WAAW,eAAe,WAAW,YAAY;AAAA,QACpE,OAAO;AACL,kBAAQ,MAAM,KAAK,IAAI,CAAC,gBAAgB;AAAA,QAC1C;AACA,gBAAQ;AACR,QAAAA,cAAa,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC;AAAA,MAC5C;AACA,kBAAY,gBAAgB,WAAW,iBAAiB,SAAS,KAAK,IAAI,UAAU,SAAS,KAAK,IAAI,SAAS;AAAA,IACjH;AACA,QAAI,KAAK,KAAK;AACd,QAAI,cAAc;AAClB,QAAI,SAAS,WAAW,IAAI,QAAQ;AACpC,QAAI,SAAS,MAAM,GAAG;AACpB,oBAAc,UAAU,KAAK;AAAA,IAC/B,WAAW,kBAAkB,UAAU;AACrC,oBAAc;AAAA,IAChB,WAAW,WAAW,YAAY,WAAW,MAAM;AACjD,UAAI,cAAc,KAAK,IAAI,CAAC,WAAW,KAAK,CAAC;AAC7C,oBAAc;AAAA,IAChB,WAAW,WAAW,gBAAgB,kBAAkB,aAAa,kBAAkB,SAAS;AAC9F,UAAI,MAAM,KAAK,MAAM,IAAI,EAAE;AAC3B,UAAI,MAAM,GAAG;AACX,cAAM,KAAK,IAAI;AAAA,MACjB;AACA,UAAI,SAAS,KAAK;AAClB,UAAI,QAAQ;AACV,cAAM,KAAK;AAAA,MACb;AACA,oBAAc,MAAM;AAAA,IACtB;AACA,qBAAiB,CAAC,CAAC;AACnB,IAAAH,OAAM,IAAI;AACV,IAAAA,OAAM,IAAI;AACV,IAAAA,OAAM,WAAW;AACjB,IAAAA,OAAM,SAAS;AAAA,MACb,eAAe;AAAA,IACjB,CAAC;AAED,QAAI,CAAC,eAAe;AAClB,UAAI,WAAWA,OAAM,gBAAgB,EAAE,MAAM;AAC7C,eAAS,eAAeA,OAAM,qBAAqB,CAAC;AAEpD,UAAI,UAAUA,OAAM,MAAM,UAAU,KAAK;AACzC,eAAS,KAAK,SAAS;AACvB,eAAS,UAAU;AACnB,sBAAgB,KAAK;AAAA,QACnB,OAAOA;AAAA,QACP,WAAWC;AAAA,QACX,UAAU;AAAA,QACV,KAAK;AAAA,QACL,MAAM;AAAA,QACN,cAAc,eAAe,IAAI,cAAc;AAAA,QAC/C,iBAAiB,eAAe,IAAI,iBAAiB;AAAA,QACrD,eAAe,IAAI,cAAM,IAAI,EAAE;AAAA,QAC/B,YAAYE;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,oBAAoB,SAAS;AAAA,QAC7B,iBAAiBH,OAAM,MAAM;AAAA,MAC/B,CAAC;AAAA,IACH,OAAO;AACL,MAAAA,OAAM,SAAS;AAAA,QACb,OAAO;AAAA,MACT,CAAC;AACD,UAAII,eAAcJ,OAAM,OAAO;AAC/B,UAAII,cAAa;AACf,QAAAA,aAAY,KAAKJ,OAAM;AACvB,QAAAI,aAAY,KAAKJ,OAAM;AAAA,MACzB;AAAA,IACF;AACA,WAAO,cAAc;AAAA,MACnB,QAAQ;AAAA,IACV,CAAC;AAAA,EACH,CAAC;AACD,MAAI,CAAC,kBAAkB,YAAY,IAAI,mBAAmB,GAAG;AAC3D,iBAAa,iBAAiB,IAAI,IAAI,GAAG,WAAW,YAAY,UAAU,OAAO;AAAA,EACnF;AACA,WAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,QAAI,SAAS,gBAAgB,CAAC;AAC9B,QAAI,QAAQ,OAAO;AACnB,QAAI,YAAY,OAAO;AACvB,QAAI,eAAe,MAAM,MAAM,CAAC,KAAK,MAAM,MAAM,CAAC;AAClD,QAAI,OAAO;AACT,YAAM,SAAS;AAAA,QACb,OAAO,OAAO;AAAA,MAChB,CAAC;AACD,UAAI,cAAc;AAChB,aAAK,MAAM,QAAQ,UAAU;AAC7B,cAAM,SAAS;AAAA,MACjB;AACA,UAAI,cAAc,MAAM,OAAO;AAC/B,UAAI,aAAa;AACf,oBAAY,KAAK,MAAM;AACvB,oBAAY,KAAK,MAAM;AAAA,MACzB;AAAA,IACF;AACA,QAAI,WAAW;AACb,UAAI,aAAa,OAAO;AACxB,UAAI,gBAAgB,CAAC,YAAY;AAC/B,aAAK,UAAU,QAAQ,UAAU;AACjC,kBAAU,SAAS;AAAA,MACrB,OAAO;AACL,uBAAe,YAAY,OAAO,YAAY;AAC9C,0BAAkB,YAAY,OAAO,eAAe,OAAO,eAAe;AAC1E,kBAAU,SAAS;AAAA,UACjB,QAAQ;AAAA,QACV,CAAC;AAED,cAAM,aAAa,sBAAsB;AAAA,UACvC,QAAQ,IAAI,cAAM,WAAW,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;AAAA,QACtD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC5aO,SAAS,sBAAsB,OAAO,OAAO,YAAY;AAC9D,MAAI,eAAe,MAAM,IAAI,cAAc;AAC3C,MAAI,gBAAgB,MAAM;AACxB,WAAO,aAAa;AAAA,MAClB,cAAc;AAAA,IAChB,IAAI;AAAA,EACN;AACA,MAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,mBAAe,CAAC,cAAc,cAAc,cAAc,YAAY;AAAA,EACxE;AACA,MAAI,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,MAAM,CAAC;AAC9C,SAAO;AAAA,IACL,cAAc,IAAI,cAAc,SAAU,IAAI;AAC5C,aAAO,aAAa,IAAI,EAAE;AAAA,IAC5B,CAAC;AAAA,EACH;AACF;;;ACJA,IAAI;AAAA;AAAA,EAAwB,SAAU,QAAQ;AAC5C,cAAUK,WAAU,MAAM;AAC1B,aAASA,UAAS,MAAM,KAAK,YAAY;AACvC,UAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,YAAM,KAAK;AACX,UAAI,OAAO,IAAY,aAAK;AAC5B,YAAM,eAAe,IAAI;AACzB,YAAM,WAAW,MAAM,KAAK,YAAY,IAAI;AAC5C,aAAO;AAAA,IACT;AACA,IAAAA,UAAS,UAAU,aAAa,SAAU,MAAM,KAAK,YAAY,aAAa;AAC5E,UAAI,SAAS;AACb,UAAI,cAAc,KAAK;AACvB,UAAI,YAAY,KAAK,aAAa,GAAG;AACrC,UAAI,gBAAgB,UAAU,SAAS,UAAU;AACjD,UAAI,SAAS,KAAK,cAAc,GAAG;AAGnC,UAAI,cAAc,OAAO,sBAAsB,UAAU,SAAS,WAAW,GAAG,QAAQ,IAAI,GAAG,MAAM;AAErG,UAAI,MAAM,YAAY,UAAU,GAAG;AAEjC,eAAO,SAAS,WAAW;AAC3B;AAAA,MACF;AACA,UAAI,aAAa;AACf,eAAO,SAAS,WAAW;AAC3B,YAAI,gBAAgB,YAAY,WAAW,eAAe;AAC1D,YAAI,YAAY,QAAQ,KAAK;AAG3B,UAAQ,UAAU,QAAQ;AAAA,YACxB,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV,GAAG,aAAa;AAAA,YACd,WAAW;AAAA,YACX,QAAQ;AAAA,UACV,CAAC;AACD,iBAAO,UAAU,YAAY;AAC7B,iBAAO,UAAU,YAAY;AAAA,QAC/B,WAAW,kBAAkB,SAAS;AACpC,iBAAO,MAAM,IAAI,OAAO;AACxB,UAAQ,UAAU,QAAQ;AAAA,YACxB,OAAO;AAAA,cACL,GAAG,OAAO;AAAA,YACZ;AAAA,UACF,GAAG,aAAa,GAAG;AAAA,QACrB,OAEK;AACH,cAAI,cAAc,MAAM;AACtB,mBAAO,SAAS;AAAA,cACd;AAAA,cACA,UAAU;AAAA,YACZ,CAAC;AACD,YAAQ,UAAU,QAAQ;AAAA,cACxB,OAAO;AAAA,gBACL,YAAY,OAAO;AAAA,gBACnB,UAAU,OAAO;AAAA,cACnB;AAAA,YACF,GAAG,aAAa,GAAG;AAAA,UACrB,OAAO;AACL,mBAAO,MAAM,WAAW,OAAO;AAC/B,YAAQ,YAAY,QAAQ;AAAA,cAC1B,OAAO;AAAA,gBACL,UAAU,OAAO;AAAA,cACnB;AAAA,YACF,GAAG,aAAa,GAAG;AAAA,UACrB;AAAA,QACF;AAAA,MACF,OAAO;AACL,qBAAa,MAAM;AAEnB,QAAQ,YAAY,QAAQ;AAAA,UAC1B,OAAO;AAAA,QACT,GAAG,aAAa,GAAG;AAAA,MACrB;AACA,aAAO,SAAS,KAAK,cAAc,KAAK,OAAO,CAAC;AAChD,+BAAyB,QAAQ,SAAS;AAC1C,UAAI,YAAY,OAAO,aAAa,OAAO,YAAY;AACvD,UAAI,SAAS,YAAY,IAAI,gBAAgB;AAC7C,UAAI,KAAK,KAAK,IAAI,QAAQ,IAAI;AAC9B,UAAI,KAAK,KAAK,IAAI,QAAQ,IAAI;AAC9B,UAAI,cAAc,UAAU,WAAW,QAAQ;AAC/C,qBAAe,OAAO,KAAK,UAAU,WAAW;AAChD,WAAK,aAAa,aAAa,MAAM,GAAG;AACxC,aAAO,YAAY,UAAU,EAAE,QAAQ,OAAO;AAAA,QAC5C,GAAG,OAAO,KAAK,cAAc,IAAI,OAAO,IAAI,cAAc,IAAI,WAAW,KAAK,IAAI;AAAA,MACpF,GAAG,sBAAsB,cAAc,SAAS,WAAW,GAAG,MAAM,CAAC;AACrE,aAAO,OAAO,YAAY,QAAQ,GAAG;AAAA,QACnC,GAAG;AAAA,QACH,GAAG;AAAA,QACH,OAAO,sBAAsB,UAAU,SAAS,CAAC,UAAU,WAAW,CAAC,GAAG,MAAM;AAAA,MAClF,CAAC;AACD,aAAO,OAAO,YAAY,MAAM,GAAG;AAAA,QACjC,OAAO,sBAAsB,UAAU,SAAS,CAAC,QAAQ,WAAW,CAAC,GAAG,MAAM;AAAA,MAChF,CAAC;AACD,UAAI,YAAY,OAAO,iBAAiB;AACxC,UAAI,YAAY,OAAO,eAAe;AACtC,mBAAa,OAAO,UAAU,YAAY,QAAQ,GAAG;AAAA,QACnD,GAAG;AAAA,QACH,GAAG;AAAA,MACL,CAAC;AAED,aAAO,UAAU,YAAY,QAAQ,GAAG;AAAA,QACtC,GAAG;AAAA,QACH,GAAG;AAAA,MACL,CAAC;AACD,0BAAoB,MAAM,cAAc,IAAI,OAAO,GAAG,cAAc,IAAI,WAAW,GAAG,cAAc,IAAI,UAAU,CAAC;AAAA,IACrH;AACA,IAAAA,UAAS,UAAU,eAAe,SAAU,aAAa,MAAM,KAAK;AAClE,UAAI,SAAS;AACb,UAAI,YAAY,KAAK,aAAa,GAAG;AACrC,UAAI,iBAAiB,UAAU,SAAS,WAAW;AACnD,UAAI,QAAQ,KAAK,cAAc,KAAK,OAAO;AAC3C,UAAI,cAAc,SAAS,MAAM;AACjC,UAAI,gBAAgB,SAAS,MAAM;AACnC,oBAAc,QAAQ,qBAAqB,SAAS,GAAG;AAAA,QACrD,cAAc,KAAK;AAAA,QACnB,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,aAAa,YAAY,kBAAkB,KAAK,QAAQ,KAAK,KAAK,QAAQ,GAAG;AAAA,MAC/E,CAAC;AACD,UAAI,YAAY,OAAO,eAAe;AAEtC,aAAO,cAAc;AAAA;AAAA,QAEnB,UAAU;AAAA,QACV,UAAU;AAAA,MACZ,CAAC;AAGD,gBAAU,KAAK;AAAA,QACb,IAAI;AAAA,MACN,CAAC;AACD,UAAI,gBAAgB,YAAY,IAAI,CAAC,SAAS,UAAU,CAAC;AACzD,UAAI,kBAAkB,aAAa,kBAAkB,SAAS;AAC5D,eAAO,oBAAoB;AAAA,MAC7B,OAAO;AACL,YAAI,WAAW,KAAK,iBAAiB;AACrC,YAAI,CAAC,UAAU;AACb,qBAAW,IAAY,iBAAS;AAChC,eAAK,iBAAiB,QAAQ;AAAA,QAChC;AAEA,0BAAkB,MAAM,yBAAyB,SAAS,GAAG;AAAA,UAC3D,QAAQ;AAAA,UACR,SAAS,UAAU,eAAe,IAAI,CAAC,aAAa,SAAS,CAAC,GAAG,eAAe,CAAC;AAAA,QACnF,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAU,cAAM;AAAA;AAEhB,IAAI;AAAA;AAAA,EAAuB,SAAU,QAAQ;AAC3C,cAAUC,UAAS,MAAM;AACzB,aAASA,WAAU;AACjB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,wBAAwB;AAC9B,aAAO;AAAA,IACT;AACA,IAAAA,SAAQ,UAAU,SAAS,SAAU,aAAa,SAAS,KAAK,SAAS;AACvE,UAAI,OAAO,YAAY,QAAQ;AAC/B,UAAI,UAAU,KAAK;AACnB,UAAI,QAAQ,KAAK;AACjB,UAAI;AAEJ,UAAI,CAAC,WAAW,KAAK,MAAM,IAAI,GAAG;AAChC,YAAI,QAAQ,KAAK,cAAc,CAAC;AAChC,iBAAS,IAAI,GAAG,MAAM,SAAS,MAAM,UAAU,KAAK,IAAI,KAAK,MAAM,GAAG,EAAE,GAAG;AACzE,kBAAQ,KAAK,cAAc,CAAC;AAAA,QAC9B;AACA,YAAI,OAAO;AACT,uBAAa,MAAM;AAAA,QACrB;AAAA,MACF;AAEA,UAAI,KAAK,oBAAoB;AAC3B,cAAM,OAAO,KAAK,kBAAkB;AAAA,MACtC;AAEA,UAAI,KAAK,MAAM,MAAM,KAAK,YAAY,IAAI,iBAAiB,GAAG;AAC5D,YAAI,aAAa,oBAAoB,WAAW;AAChD,YAAI,SAAS,IAAY,eAAO;AAAA,UAC9B,OAAO,OAAO,kBAAkB,aAAa,GAAG,GAAG,UAAU;AAAA,QAC/D,CAAC;AACD,eAAO,SAAS,YAAY,SAAS,kBAAkB,EAAE,aAAa,CAAC;AACvE,aAAK,qBAAqB;AAC1B,cAAM,IAAI,MAAM;AAAA,MAClB;AACA,WAAK,KAAK,OAAO,EAAE,IAAI,SAAU,KAAK;AACpC,YAAI,WAAW,IAAI,SAAS,MAAM,KAAK,UAAU;AACjD,aAAK,iBAAiB,KAAK,QAAQ;AACnC,cAAM,IAAI,QAAQ;AAAA,MACpB,CAAC,EAAE,OAAO,SAAU,QAAQ,QAAQ;AAClC,YAAI,WAAW,QAAQ,iBAAiB,MAAM;AAC9C,iBAAS,WAAW,MAAM,QAAQ,UAAU;AAC5C,iBAAS,IAAI,OAAO;AACpB,cAAM,IAAI,QAAQ;AAClB,aAAK,iBAAiB,QAAQ,QAAQ;AAAA,MACxC,CAAC,EAAE,OAAO,SAAU,KAAK;AACvB,YAAI,WAAW,QAAQ,iBAAiB,GAAG;AAC3C,QAAQ,yBAAyB,UAAU,aAAa,GAAG;AAAA,MAC7D,CAAC,EAAE,QAAQ;AACX,qBAAY,WAAW;AAEvB,UAAI,YAAY,IAAI,qBAAqB,MAAM,aAAa;AAC1D,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AACA,IAAAA,SAAQ,UAAU,UAAU,WAAY;AAAA,IAAC;AACzC,IAAAA,SAAQ,UAAU,eAAe,SAAU,OAAO,aAAa;AAC7D,UAAI,OAAO,YAAY,QAAQ;AAC/B,UAAI,aAAa,KAAK,cAAc,CAAC;AACrC,UAAI,YAAY;AACd,YAAI,KAAK,MAAM,CAAC,IAAI,WAAW;AAC/B,YAAI,KAAK,MAAM,CAAC,IAAI,WAAW;AAC/B,YAAI,SAAS,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACxC,eAAO,UAAU,WAAW,KAAK,UAAU,WAAW;AAAA,MACxD;AAAA,IACF;AACA,IAAAA,SAAQ,OAAO;AACf,WAAOA;AAAA,EACT,EAAE,aAAS;AAAA;AACX,IAAO,kBAAQ;;;AClOA,SAAR,uBAAwC,aAAa,KAAK,UAAU;AACzE,QAAM,QAAQ,GAAG,KAAK;AAAA,IACpB,iBAAiB;AAAA,EACnB,KAAK,OAAO;AAAA,IACV,cAAc,YAAY,UAAU;AAAA,EACtC,GAAG,GAAG;AACN,MAAI,SAAS,YAAY,UAAU;AACnC,MAAI,aAAa,wBAAwB,QAAQ,GAAG,EAAE;AACtD,MAAI,OAAO,IAAI,mBAAW,YAAY,WAAW;AACjD,OAAK,SAAS,QAAQ,QAAQ;AAC9B,SAAO;AACT;;;ACpBA,IAAI;AAAA;AAAA,EAAoC,WAAY;AAClD,aAASC,sBAET,0BAEA,YAAY;AACV,WAAK,4BAA4B;AACjC,WAAK,cAAc;AAAA,IACrB;AACA,IAAAA,sBAAqB,UAAU,cAAc,WAAY;AACvD,UAAI,UAAU,KAAK,YAAY;AAG/B,aAAO,QAAQ,SAAS,QAAQ,OAAO;AAAA,IACzC;AACA,IAAAA,sBAAqB,UAAU,cAAc,SAAU,MAAM;AAC3D,UAAI,UAAU,KAAK,YAAY;AAC/B,aAAO,QAAQ,YAAY,IAAI,KAAK;AAAA,IACtC;AACA,IAAAA,sBAAqB,UAAU,cAAc,SAAU,MAAM;AAI3D,UAAI,wBAAwB,KAAK,0BAA0B;AAC3D,aAAO,sBAAsB,YAAY,IAAI;AAAA,IAC/C;AACA,IAAAA,sBAAqB,UAAU,gBAAgB,SAAU,WAAW,KAAK;AAEvE,UAAI,wBAAwB,KAAK,0BAA0B;AAC3D,aAAO,sBAAsB,cAAc,WAAW,GAAG;AAAA,IAC3D;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACF,IAAO,+BAAQ;;;AC7Bf,IAAI,YAAsB,UAAU;AACpC,IAAI;AAAA;AAAA,EAA8B,SAAU,QAAQ;AAClD,cAAUC,iBAAgB,MAAM;AAChC,aAASA,kBAAiB;AACxB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AAIA,IAAAA,gBAAe,UAAU,OAAO,SAAU,QAAQ;AAChD,aAAO,UAAU,KAAK,MAAM,MAAM,SAAS;AAG3C,WAAK,uBAAuB,IAAI,6BAA4B,KAAK,KAAK,SAAS,IAAI,GAAU,KAAK,KAAK,YAAY,IAAI,CAAC;AACxH,WAAK,kBAAkB,MAAM;AAAA,IAC/B;AAIA,IAAAA,gBAAe,UAAU,cAAc,WAAY;AACjD,aAAO,UAAU,YAAY,MAAM,MAAM,SAAS;AAAA,IACpD;AAIA,IAAAA,gBAAe,UAAU,iBAAiB,WAAY;AACpD,aAAO,uBAAuB,MAAM;AAAA,QAClC,iBAAiB,CAAC,OAAO;AAAA,QACzB,iBAAwB,MAAM,8BAA8B,IAAI;AAAA,MAClE,CAAC;AAAA,IACH;AAIA,IAAAA,gBAAe,UAAU,gBAAgB,SAAU,WAAW;AAC5D,UAAI,OAAO,KAAK,QAAQ;AAExB,UAAI,YAAY,UAAU,IAAI;AAC9B,UAAI,QAAQ,UAAU;AACtB,UAAI,CAAC,OAAO;AACV,YAAI,cAAc,CAAC;AACnB,aAAK,KAAK,KAAK,aAAa,OAAO,GAAG,SAAU,OAAO;AACrD,sBAAY,KAAK,KAAK;AAAA,QACxB,CAAC;AACD,gBAAQ,UAAU,QAAQ,gBAAgB,aAAa,KAAK,UAAU,IAAI,kBAAkB,CAAC;AAAA,MAC/F;AACA,UAAI,SAAS,OAAO,UAAU,cAAc,KAAK,MAAM,SAAS;AAEhE,aAAO,UAAU,MAAM,SAAS,KAAK;AACrC,aAAO,MAAM,KAAK,SAAS;AAC3B,aAAO;AAAA,IACT;AACA,IAAAA,gBAAe,UAAU,oBAAoB,SAAU,QAAQ;AAE7D,MAAU,gBAAgB,QAAQ,aAAa,CAAC,MAAM,CAAC;AACvD,UAAI,qBAAqB,OAAO;AAChC,UAAI,uBAAuB,OAAO,SAAS;AAE3C,yBAAmB,OAAO,mBAAmB,QAAQ,OAAO,MAAM;AAClE,2BAAqB,OAAO,qBAAqB,QAAQ,OAAO,SAAS,MAAM;AAAA,IACjF;AACA,IAAAA,gBAAe,OAAO;AACtB,IAAAA,gBAAe,gBAAgB;AAAA;AAAA,MAE7B,GAAG;AAAA,MACH,iBAAiB;AAAA,MACjB,SAAS;AAAA;AAAA,MAET,QAAQ,CAAC,OAAO,KAAK;AAAA,MACrB,QAAQ,CAAC,GAAG,KAAK;AAAA;AAAA,MAEjB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA;AAAA,MAEV,UAAU;AAAA;AAAA;AAAA,MAGV,mBAAmB;AAAA;AAAA,MAEnB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKhB,kBAAkB;AAAA;AAAA,MAElB,kBAAkB;AAAA;AAAA,MAElB,MAAM;AAAA,MACN,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA;AAAA;AAAA,QAGL,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,UAAU;AAAA;AAAA,QAEV,UAAU;AAAA;AAAA,QAEV,SAAS;AAAA;AAAA;AAAA,QAGT,cAAc;AAAA;AAAA,QAEd,aAAa;AAAA;AAAA,QAEb,qBAAqB;AAAA;AAAA;AAAA;AAAA,MAIvB;AAAA;AAAA,MAGA,WAAW;AAAA,QACT,MAAM;AAAA;AAAA,QAEN,QAAQ;AAAA;AAAA,QAER,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,WAAW;AAAA;AAAA,UAET,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,MACd;AAAA,MACA,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,QAChB,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,MACA,aAAa;AAAA;AAAA,QAEX,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,QACR,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA;AAAA,MAEA,mBAAmB;AAAA;AAAA,MAEnB,eAAe;AAAA,MACf,mBAAmB;AAAA;AAAA,MAEnB,qBAAqB;AAAA,MACrB,uBAAuB;AAAA,MACvB,yBAAyB;AAAA,MACzB,iBAAiB;AAAA,IACnB;AACA,WAAOA;AAAA,EACT,EAAE,cAAW;AAAA;AACb,IAAO,oBAAQ;;;AC3KA,SAAR,mBAAoC,YAAY;AACrD,SAAO;AAAA,IACL;AAAA,IACA,OAAO,SAAU,aAAa,SAAS;AACrC,UAAI,OAAO,YAAY,QAAQ;AAC/B,WAAK,WAAW,SAAU,KAAK;AAE7B,YAAI,WAAW,KAAK,aAAa,OAAO;AACxC,YAAI,WAAW,KAAK,IAAI,UAAU,GAAG;AACrC,YAAI,SAAS,QAAQ,KAAK,CAAC,MAAM,QAAQ,KAAK,WAAW,GAAG;AAC1D,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;ACVO,SAAS,QAAQ,WAAW;AACjC,YAAU,kBAAkB,eAAO;AACnC,YAAU,oBAAoB,iBAAc;AAC5C,+BAA6B,OAAO,UAAU,cAAc;AAC5D,YAAU,eAAe,MAAM,WAAW,KAAK,CAAC;AAChD,YAAU,kBAAkB,WAAW,KAAK,CAAC;AAC7C,YAAU,kBAAkB,mBAAmB,KAAK,CAAC;AACvD;", "names": ["parsePercent", "endAngle", "RADIAN", "i", "dx", "label", "labelLine", "parsePercent", "linePoints", "selectState", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>ie<PERSON>", "LegendVisualProvider", "PieSeriesModel"]}