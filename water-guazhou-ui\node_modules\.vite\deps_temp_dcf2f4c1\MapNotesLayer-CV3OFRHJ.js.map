{"version": 3, "sources": ["../../@arcgis/core/layers/MapNotesLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import\"../geometry.js\";import t from\"../Graphic.js\";import\"../symbols.js\";import r from\"../core/Collection.js\";import o from\"../core/Error.js\";import{clone as i}from\"../core/lang.js\";import{isSome as l,isNone as a}from\"../core/maybe.js\";import{MultiOriginJSONMixin as n}from\"../core/MultiOriginJSONSupport.js\";import{setDeepValue as s}from\"../core/object.js\";import p from\"../core/Warning.js\";import{property as y}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import{reader as m}from\"../core/accessorSupport/decorators/reader.js\";import{subclass as u}from\"../core/accessorSupport/decorators/subclass.js\";import{writer as c}from\"../core/accessorSupport/decorators/writer.js\";import{projectOrLoad as f,canProjectWithoutEngine as d,isLoaded as g,load as b,project as S}from\"../geometry/projection.js\";import{empty as h,expand as O,equals as w,toExtent as j,NEGATIVE_INFINITY as N}from\"../geometry/support/aaBoundingRect.js\";import{normalizeCentralMeridian as L}from\"../geometry/support/normalizeUtils.js\";import{equals as x}from\"../geometry/support/spatialReferenceUtils.js\";import J from\"./FeatureLayer.js\";import T from\"./GraphicsLayer.js\";import v from\"./Layer.js\";import{findLastObjectIdFromFeatures as C}from\"./graphics/objectIdUtils.js\";import{BlendLayer as M}from\"./mixins/BlendLayer.js\";import{OperationalLayer as I}from\"./mixins/OperationalLayer.js\";import{PortalLayer as R}from\"./mixins/PortalLayer.js\";import{ScaleRangeLayer as E}from\"./mixins/ScaleRangeLayer.js\";import D from\"./support/Field.js\";import _ from\"../symbols/SimpleFillSymbol.js\";import G from\"../symbols/SimpleLineSymbol.js\";import P from\"../symbols/SimpleMarkerSymbol.js\";import F from\"../symbols/TextSymbol.js\";import B from\"../geometry/SpatialReference.js\";import k from\"../geometry/Extent.js\";function z(e){return\"markup\"===e.featureCollectionType||e.layers.some((e=>null!=e.layerDefinition.visibilityField||!U(e)))}function U({layerDefinition:e,featureSet:t}){const r=e.geometryType??t.geometryType;return H.find((t=>r===t.geometryTypeJSON&&e.drawingInfo?.renderer?.symbol?.type===t.identifyingSymbol.type))}function W(){return new k({xmin:-180,ymin:-90,xmax:180,ymax:90})}const A=new D({name:\"OBJECTID\",alias:\"OBJECTID\",type:\"oid\",nullable:!1,editable:!1}),$=new D({name:\"title\",alias:\"Title\",type:\"string\",nullable:!0,editable:!0});let q=class extends T{constructor(e){super(e),this.visibilityMode=\"inherited\"}initialize(){for(const e of this.graphics)e.sourceLayer=this.layer;this.graphics.on(\"after-add\",(e=>{e.item.sourceLayer=this.layer})),this.graphics.on(\"after-remove\",(e=>{e.item.sourceLayer=null}))}get fullExtent(){const e=this.layer?.spatialReference,t=this.fullBounds;return e?a(t)?f(W(),e).geometry:j(t,e):null}get fullBounds(){const e=this.layer?.spatialReference;if(!e)return null;const t=h();return this.graphics.forEach((r=>{const o=l(r.geometry)?f(r.geometry,e).geometry:null;l(o)&&O(t,\"point\"===o.type?o:o.extent,t)})),w(t,N)?null:t}get sublayers(){return this.graphics}};e([y({readOnly:!0})],q.prototype,\"fullExtent\",null),e([y({readOnly:!0})],q.prototype,\"fullBounds\",null),e([y({readOnly:!0})],q.prototype,\"sublayers\",null),e([y()],q.prototype,\"layer\",void 0),e([y()],q.prototype,\"layerId\",void 0),e([y({readOnly:!0})],q.prototype,\"visibilityMode\",void 0),q=e([u(\"esri.layers.MapNotesLayer.MapNotesSublayer\")],q);const H=[{geometryType:\"polygon\",geometryTypeJSON:\"esriGeometryPolygon\",id:\"polygonLayer\",layerId:0,title:\"Polygons\",identifyingSymbol:(new _).toJSON()},{geometryType:\"polyline\",geometryTypeJSON:\"esriGeometryPolyline\",id:\"polylineLayer\",layerId:1,title:\"Polylines\",identifyingSymbol:(new G).toJSON()},{geometryType:\"multipoint\",geometryTypeJSON:\"esriGeometryMultipoint\",id:\"multipointLayer\",layerId:2,title:\"Multipoints\",identifyingSymbol:(new P).toJSON()},{geometryType:\"point\",geometryTypeJSON:\"esriGeometryPoint\",id:\"pointLayer\",layerId:3,title:\"Points\",identifyingSymbol:(new P).toJSON()},{geometryType:\"point\",geometryTypeJSON:\"esriGeometryPoint\",id:\"textLayer\",layerId:4,title:\"Text\",identifyingSymbol:(new F).toJSON()}];let K=class extends(M(E(I(R(n(v)))))){constructor(e){super(e),this.capabilities={operations:{supportsMapNotesEditing:!0}},this.featureCollections=null,this.featureCollectionJSON=null,this.featureCollectionType=\"notes\",this.legendEnabled=!1,this.listMode=\"hide-children\",this.minScale=0,this.maxScale=0,this.spatialReference=B.WGS84,this.sublayers=new r(H.map((e=>new q({id:e.id,layerId:e.layerId,title:e.title,layer:this})))),this.title=\"Map Notes\",this.type=\"map-notes\",this.visibilityMode=\"inherited\"}readCapabilities(e,t,r){return{operations:{supportsMapNotesEditing:!z(t)&&\"portal-item\"!==r?.origin}}}readFeatureCollections(e,t,o){if(!z(t))return null;const i=t.layers.map((e=>{const t=new J;return t.read(e,o),t}));return new r({items:i})}readLegacyfeatureCollectionJSON(e,t){return z(t)?i(t.featureCollection):null}get fullExtent(){const e=this.spatialReference,t=h();if(l(this.sublayers))this.sublayers.forEach((({fullBounds:e})=>l(e)?O(t,e,t):t),t);else if(this.featureCollectionJSON?.layers.some((e=>e.layerDefinition.extent))){this.featureCollectionJSON.layers.forEach((r=>{const o=f(r.layerDefinition.extent,e).geometry;l(o)&&O(t,o,t)}))}return w(t,N)?f(W(),e).geometry:j(t,e)}readMinScale(e,t){for(const r of t.layers)if(null!=r.layerDefinition.minScale)return r.layerDefinition.minScale;return 0}readMaxScale(e,t){for(const r of t.layers)if(null!=r.layerDefinition.maxScale)return r.layerDefinition.maxScale;return 0}get multipointLayer(){return this._findSublayer(\"multipointLayer\")}get pointLayer(){return this._findSublayer(\"pointLayer\")}get polygonLayer(){return this._findSublayer(\"polygonLayer\")}get polylineLayer(){return this._findSublayer(\"polylineLayer\")}readSpatialReference(e,t){return t.layers.length?B.fromJSON(t.layers[0].layerDefinition.spatialReference):B.WGS84}readSublayers(e,o,i){if(z(o))return null;const a=[];let n=o.layers.reduce(((e,t)=>Math.max(e,t.layerDefinition.id??-1)),-1)+1;for(const r of o.layers){const{layerDefinition:e,featureSet:o}=r,i=e.id??n++,s=U(r);if(l(s)){const r=new q({id:s.id,title:e.name,layerId:i,layer:this,graphics:o.features.map((({geometry:e,symbol:r,attributes:o,popupInfo:i})=>t.fromJSON({attributes:o,geometry:e,symbol:r,popupTemplate:i})))});a.push(r)}}return new r(a)}writeSublayers(e,t,r,i){const{minScale:n,maxScale:p}=this;if(a(e))return;const y=e.some((e=>e.graphics.length>0));if(!this.capabilities.operations.supportsMapNotesEditing)return void(y&&i?.messages?.push(new o(\"map-notes-layer:editing-not-supported\",\"New map notes cannot be added to this layer\")));const m=[];let u=this.spatialReference.toJSON();e:for(const o of e)for(const e of o.graphics)if(l(e.geometry)){u=e.geometry.spatialReference.toJSON();break e}for(const o of H){const t=e.find((e=>o.id===e.id));this._writeMapNoteSublayer(m,t,o,n,p,u,i)}s(\"featureCollection.layers\",m,t)}get textLayer(){return this._findSublayer(\"textLayer\")}load(e){return this.addResolvingPromise(this.loadFromPortal({supportedTypes:[\"Feature Collection\"]},e)),Promise.resolve(this)}read(e,t){\"featureCollection\"in e&&(e=i(e),Object.assign(e,e.featureCollection)),super.read(e,t)}async beforeSave(){if(a(this.sublayers))return;let e=null;const t=[];for(const o of this.sublayers)for(const r of o.graphics)if(l(r.geometry)){const o=r.geometry;e?x(o.spatialReference,e)||(d(o.spatialReference,e)||g()||await b(),r.geometry=S(o,e)):e=o.spatialReference,t.push(r)}const r=await L(t.map((e=>e.geometry)));t.forEach(((e,t)=>e.geometry=r[t]))}_findSublayer(e){return a(this.sublayers)?null:this.sublayers?.find((t=>t.id===e))??null}_writeMapNoteSublayer(e,t,r,o,l,n,s){const p=[];if(!a(t)){for(const e of t.graphics)this._writeMapNote(p,e,r.geometryType,s);this._normalizeObjectIds(p,A),e.push({layerDefinition:{name:t.title,drawingInfo:{renderer:{type:\"simple\",symbol:i(r.identifyingSymbol)}},id:t.layerId,geometryType:r.geometryTypeJSON,minScale:o,maxScale:l,objectIdField:\"OBJECTID\",fields:[A.toJSON(),$.toJSON()],spatialReference:n},featureSet:{features:p,geometryType:r.geometryTypeJSON}})}}_writeMapNote(e,t,r,o){if(a(t))return;const{geometry:i,symbol:n,popupTemplate:s}=t;if(a(i))return;if(i.type!==r)return void o?.messages?.push(new p(\"map-notes-layer:invalid-geometry-type\",`Geometry \"${i.type}\" cannot be saved in \"${r}\" layer`,{graphic:t}));if(a(n))return void o?.messages?.push(new p(\"map-notes-layer:no-symbol\",\"Skipping map notes with no symbol\",{graphic:t}));const y={attributes:{...t.attributes},geometry:i.toJSON(),symbol:n.toJSON()};l(s)&&(y.popupInfo=s.toJSON()),e.push(y)}_normalizeObjectIds(e,t){const r=t.name;let o=C(r,e)+1;const i=new Set;for(const l of e){l.attributes||(l.attributes={});const{attributes:e}=l;(null==e[r]||i.has(e[r]))&&(e[r]=o++),i.add(e[r])}}};e([y({readOnly:!0})],K.prototype,\"capabilities\",void 0),e([m([\"portal-item\",\"web-map\"],\"capabilities\",[\"layers\"])],K.prototype,\"readCapabilities\",null),e([y({readOnly:!0})],K.prototype,\"featureCollections\",void 0),e([m([\"web-map\",\"portal-item\"],\"featureCollections\",[\"layers\"])],K.prototype,\"readFeatureCollections\",null),e([y({readOnly:!0,json:{origins:{\"web-map\":{write:{enabled:!0,target:\"featureCollection\"}}}}})],K.prototype,\"featureCollectionJSON\",void 0),e([m([\"web-map\",\"portal-item\"],\"featureCollectionJSON\",[\"featureCollection\"])],K.prototype,\"readLegacyfeatureCollectionJSON\",null),e([y({readOnly:!0,json:{read:!0,write:{enabled:!0,ignoreOrigin:!0}}})],K.prototype,\"featureCollectionType\",void 0),e([y({readOnly:!0})],K.prototype,\"fullExtent\",null),e([y({readOnly:!0,json:{origins:{\"web-map\":{write:{target:\"featureCollection.showLegend\",overridePolicy(){return{enabled:null!=this.featureCollectionJSON}}}}}}})],K.prototype,\"legendEnabled\",void 0),e([y({type:[\"show\",\"hide\",\"hide-children\"]})],K.prototype,\"listMode\",void 0),e([y({type:Number,nonNullable:!0,json:{write:!1}})],K.prototype,\"minScale\",void 0),e([m([\"web-map\",\"portal-item\"],\"minScale\",[\"layers\"])],K.prototype,\"readMinScale\",null),e([y({type:Number,nonNullable:!0,json:{write:!1}})],K.prototype,\"maxScale\",void 0),e([m([\"web-map\",\"portal-item\"],\"maxScale\",[\"layers\"])],K.prototype,\"readMaxScale\",null),e([y({readOnly:!0})],K.prototype,\"multipointLayer\",null),e([y({value:\"ArcGISFeatureLayer\",type:[\"ArcGISFeatureLayer\"]})],K.prototype,\"operationalLayerType\",void 0),e([y({readOnly:!0})],K.prototype,\"pointLayer\",null),e([y({readOnly:!0})],K.prototype,\"polygonLayer\",null),e([y({readOnly:!0})],K.prototype,\"polylineLayer\",null),e([y({type:B})],K.prototype,\"spatialReference\",void 0),e([m([\"web-map\",\"portal-item\"],\"spatialReference\",[\"layers\"])],K.prototype,\"readSpatialReference\",null),e([y({readOnly:!0,json:{origins:{\"web-map\":{write:{ignoreOrigin:!0}}}}})],K.prototype,\"sublayers\",void 0),e([m(\"web-map\",\"sublayers\",[\"layers\"])],K.prototype,\"readSublayers\",null),e([c(\"web-map\",\"sublayers\")],K.prototype,\"writeSublayers\",null),e([y({readOnly:!0})],K.prototype,\"textLayer\",null),e([y()],K.prototype,\"title\",void 0),e([y({readOnly:!0,json:{read:!1}})],K.prototype,\"type\",void 0),K=e([u(\"esri.layers.MapNotesLayer\")],K);const Q=K;export{Q as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI00D,SAAS,EAAEA,IAAE;AAAC,SAAM,aAAWA,GAAE,yBAAuBA,GAAE,OAAO,KAAM,CAAAA,OAAG,QAAMA,GAAE,gBAAgB,mBAAiB,CAAC,EAAEA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAE,EAAC,iBAAgBA,IAAE,YAAWC,GAAC,GAAE;AAAC,QAAMC,KAAEF,GAAE,gBAAcC,GAAE;AAAa,SAAO,EAAE,KAAM,CAAAA,OAAC;AAJziE;AAI2iE,WAAAC,OAAID,GAAE,sBAAkB,iBAAAD,GAAE,gBAAF,mBAAe,aAAf,mBAAyB,WAAzB,mBAAiC,UAAOC,GAAE,kBAAkB;AAAA,GAAK;AAAC;AAAC,SAAS,IAAG;AAAC,SAAO,IAAI,EAAE,EAAC,MAAK,MAAK,MAAK,KAAI,MAAK,KAAI,MAAK,GAAE,CAAC;AAAC;AAAC,IAAM,IAAE,IAAIE,GAAE,EAAC,MAAK,YAAW,OAAM,YAAW,MAAK,OAAM,UAAS,OAAG,UAAS,MAAE,CAAC;AAAnF,IAAqF,IAAE,IAAIA,GAAE,EAAC,MAAK,SAAQ,OAAM,SAAQ,MAAK,UAAS,UAAS,MAAG,UAAS,KAAE,CAAC;AAAE,IAAI,IAAE,cAAcC,GAAC;AAAA,EAAC,YAAYJ,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,iBAAe;AAAA,EAAW;AAAA,EAAC,aAAY;AAAC,eAAUA,MAAK,KAAK,SAAS,CAAAA,GAAE,cAAY,KAAK;AAAM,SAAK,SAAS,GAAG,aAAa,CAAAA,OAAG;AAAC,MAAAA,GAAE,KAAK,cAAY,KAAK;AAAA,IAAK,CAAE,GAAE,KAAK,SAAS,GAAG,gBAAgB,CAAAA,OAAG;AAAC,MAAAA,GAAE,KAAK,cAAY;AAAA,IAAI,CAAE;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAJ5oF;AAI6oF,UAAMA,MAAE,UAAK,UAAL,mBAAY,kBAAiBC,KAAE,KAAK;AAAW,WAAOD,KAAE,EAAEC,EAAC,IAAE,GAAE,EAAE,GAAED,EAAC,EAAE,WAASK,GAAEJ,IAAED,EAAC,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,aAAY;AAJhwF;AAIiwF,UAAMA,MAAE,UAAK,UAAL,mBAAY;AAAiB,QAAG,CAACA,GAAE,QAAO;AAAK,UAAMC,KAAE,EAAE;AAAE,WAAO,KAAK,SAAS,QAAS,CAAAC,OAAG;AAAC,YAAMI,KAAE,EAAEJ,GAAE,QAAQ,IAAE,GAAEA,GAAE,UAASF,EAAC,EAAE,WAAS;AAAK,QAAEM,EAAC,KAAG,EAAEL,IAAE,YAAUK,GAAE,OAAKA,KAAEA,GAAE,QAAOL,EAAC;AAAA,IAAC,CAAE,GAAE,EAAEA,IAAE,CAAC,IAAE,OAAKA;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,kBAAiB,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,4CAA4C,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE,CAAC,EAAC,cAAa,WAAU,kBAAiB,uBAAsB,IAAG,gBAAe,SAAQ,GAAE,OAAM,YAAW,mBAAmB,IAAI,IAAG,OAAO,EAAC,GAAE,EAAC,cAAa,YAAW,kBAAiB,wBAAuB,IAAG,iBAAgB,SAAQ,GAAE,OAAM,aAAY,mBAAmB,IAAIM,KAAG,OAAO,EAAC,GAAE,EAAC,cAAa,cAAa,kBAAiB,0BAAyB,IAAG,mBAAkB,SAAQ,GAAE,OAAM,eAAc,mBAAmB,IAAIJ,KAAG,OAAO,EAAC,GAAE,EAAC,cAAa,SAAQ,kBAAiB,qBAAoB,IAAG,cAAa,SAAQ,GAAE,OAAM,UAAS,mBAAmB,IAAIA,KAAG,OAAO,EAAC,GAAE,EAAC,cAAa,SAAQ,kBAAiB,qBAAoB,IAAG,aAAY,SAAQ,GAAE,OAAM,QAAO,mBAAmB,IAAI,IAAG,OAAO,EAAC,CAAC;AAAE,IAAIK,KAAE,cAAcC,GAAER,GAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,eAAa,EAAC,YAAW,EAAC,yBAAwB,KAAE,EAAC,GAAE,KAAK,qBAAmB,MAAK,KAAK,wBAAsB,MAAK,KAAK,wBAAsB,SAAQ,KAAK,gBAAc,OAAG,KAAK,WAAS,iBAAgB,KAAK,WAAS,GAAE,KAAK,WAAS,GAAE,KAAK,mBAAiB,EAAE,OAAM,KAAK,YAAU,IAAI,EAAE,EAAE,IAAK,CAAAA,OAAG,IAAI,EAAE,EAAC,IAAGA,GAAE,IAAG,SAAQA,GAAE,SAAQ,OAAMA,GAAE,OAAM,OAAM,KAAI,CAAC,CAAE,CAAC,GAAE,KAAK,QAAM,aAAY,KAAK,OAAK,aAAY,KAAK,iBAAe;AAAA,EAAW;AAAA,EAAC,iBAAiBA,IAAEC,IAAEC,IAAE;AAAC,WAAM,EAAC,YAAW,EAAC,yBAAwB,CAAC,EAAED,EAAC,KAAG,mBAAgBC,MAAA,gBAAAA,GAAG,QAAM,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBF,IAAEC,IAAEK,IAAE;AAAC,QAAG,CAAC,EAAEL,EAAC,EAAE,QAAO;AAAK,UAAM,IAAEA,GAAE,OAAO,IAAK,CAAAD,OAAG;AAAC,YAAMC,KAAE,IAAI;AAAE,aAAOA,GAAE,KAAKD,IAAEM,EAAC,GAAEL;AAAA,IAAC,CAAE;AAAE,WAAO,IAAI,EAAE,EAAC,OAAM,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,gCAAgCD,IAAEC,IAAE;AAAC,WAAO,EAAEA,EAAC,IAAE,EAAEA,GAAE,iBAAiB,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,aAAY;AAJ/2J;AAIg3J,UAAMD,KAAE,KAAK,kBAAiBC,KAAE,EAAE;AAAE,QAAG,EAAE,KAAK,SAAS,EAAE,MAAK,UAAU,QAAS,CAAC,EAAC,YAAWD,GAAC,MAAI,EAAEA,EAAC,IAAE,EAAEC,IAAED,IAAEC,EAAC,IAAEA,IAAGA,EAAC;AAAA,cAAU,UAAK,0BAAL,mBAA4B,OAAO,KAAM,CAAAD,OAAGA,GAAE,gBAAgB,SAAS;AAAC,WAAK,sBAAsB,OAAO,QAAS,CAAAE,OAAG;AAAC,cAAMI,KAAE,GAAEJ,GAAE,gBAAgB,QAAOF,EAAC,EAAE;AAAS,UAAEM,EAAC,KAAG,EAAEL,IAAEK,IAAEL,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAC,WAAO,EAAEA,IAAE,CAAC,IAAE,GAAE,EAAE,GAAED,EAAC,EAAE,WAASK,GAAEJ,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAEC,IAAE;AAAC,eAAUC,MAAKD,GAAE,OAAO,KAAG,QAAMC,GAAE,gBAAgB,SAAS,QAAOA,GAAE,gBAAgB;AAAS,WAAO;AAAA,EAAC;AAAA,EAAC,aAAaF,IAAEC,IAAE;AAAC,eAAUC,MAAKD,GAAE,OAAO,KAAG,QAAMC,GAAE,gBAAgB,SAAS,QAAOA,GAAE,gBAAgB;AAAS,WAAO;AAAA,EAAC;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAO,KAAK,cAAc,iBAAiB;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,cAAc,YAAY;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK,cAAc,cAAc;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,KAAK,cAAc,eAAe;AAAA,EAAC;AAAA,EAAC,qBAAqBF,IAAEC,IAAE;AAAC,WAAOA,GAAE,OAAO,SAAO,EAAE,SAASA,GAAE,OAAO,CAAC,EAAE,gBAAgB,gBAAgB,IAAE,EAAE;AAAA,EAAK;AAAA,EAAC,cAAcD,IAAEM,IAAE,GAAE;AAAC,QAAG,EAAEA,EAAC,EAAE,QAAO;AAAK,UAAMI,KAAE,CAAC;AAAE,QAAID,KAAEH,GAAE,OAAO,OAAQ,CAACN,IAAEC,OAAI,KAAK,IAAID,IAAEC,GAAE,gBAAgB,MAAI,EAAE,GAAG,EAAE,IAAE;AAAE,eAAUC,MAAKI,GAAE,QAAO;AAAC,YAAK,EAAC,iBAAgBN,IAAE,YAAWM,GAAC,IAAEJ,IAAES,KAAEX,GAAE,MAAIS,MAAIG,KAAE,EAAEV,EAAC;AAAE,UAAG,EAAEU,EAAC,GAAE;AAAC,cAAMV,KAAE,IAAI,EAAE,EAAC,IAAGU,GAAE,IAAG,OAAMZ,GAAE,MAAK,SAAQW,IAAE,OAAM,MAAK,UAASL,GAAE,SAAS,IAAK,CAAC,EAAC,UAASN,IAAE,QAAOE,IAAE,YAAWI,IAAE,WAAUK,GAAC,MAAI,EAAE,SAAS,EAAC,YAAWL,IAAE,UAASN,IAAE,QAAOE,IAAE,eAAcS,GAAC,CAAC,CAAE,EAAC,CAAC;AAAE,QAAAD,GAAE,KAAKR,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAO,IAAI,EAAEQ,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeV,IAAEC,IAAEC,IAAE,GAAE;AAJ9vM;AAI+vM,UAAK,EAAC,UAASO,IAAE,UAASI,GAAC,IAAE;AAAK,QAAG,EAAEb,EAAC,EAAE;AAAO,UAAMG,KAAEH,GAAE,KAAM,CAAAA,OAAGA,GAAE,SAAS,SAAO,CAAE;AAAE,QAAG,CAAC,KAAK,aAAa,WAAW,wBAAwB,QAAO,MAAKG,QAAG,4BAAG,aAAH,mBAAa,KAAK,IAAI,EAAE,yCAAwC,6CAA6C;AAAI,UAAMI,KAAE,CAAC;AAAE,QAAI,IAAE,KAAK,iBAAiB,OAAO;AAAE,MAAE,YAAUD,MAAKN,GAAE,YAAUA,MAAKM,GAAE,SAAS,KAAG,EAAEN,GAAE,QAAQ,GAAE;AAAC,UAAEA,GAAE,SAAS,iBAAiB,OAAO;AAAE,YAAM;AAAA,IAAC;AAAC,eAAUM,MAAK,GAAE;AAAC,YAAML,KAAED,GAAE,KAAM,CAAAA,OAAGM,GAAE,OAAKN,GAAE,EAAG;AAAE,WAAK,sBAAsBO,IAAEN,IAAEK,IAAEG,IAAEI,IAAE,GAAE,CAAC;AAAA,IAAC;AAAC,MAAE,4BAA2BN,IAAEN,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK,cAAc,WAAW;AAAA,EAAC;AAAA,EAAC,KAAKD,IAAE;AAAC,WAAO,KAAK,oBAAoB,KAAK,eAAe,EAAC,gBAAe,CAAC,oBAAoB,EAAC,GAAEA,EAAC,CAAC,GAAE,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAEC,IAAE;AAAC,2BAAsBD,OAAIA,KAAE,EAAEA,EAAC,GAAE,OAAO,OAAOA,IAAEA,GAAE,iBAAiB,IAAG,MAAM,KAAKA,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,aAAY;AAAC,QAAG,EAAE,KAAK,SAAS,EAAE;AAAO,QAAID,KAAE;AAAK,UAAMC,KAAE,CAAC;AAAE,eAAUK,MAAK,KAAK,UAAU,YAAUJ,MAAKI,GAAE,SAAS,KAAG,EAAEJ,GAAE,QAAQ,GAAE;AAAC,YAAMI,KAAEJ,GAAE;AAAS,MAAAF,KAAE,EAAEM,GAAE,kBAAiBN,EAAC,MAAI,GAAEM,GAAE,kBAAiBN,EAAC,KAAG,GAAE,KAAG,MAAM,GAAE,GAAEE,GAAE,WAAS,GAAEI,IAAEN,EAAC,KAAGA,KAAEM,GAAE,kBAAiBL,GAAE,KAAKC,EAAC;AAAA,IAAC;AAAC,UAAMA,KAAE,MAAM,EAAED,GAAE,IAAK,CAAAD,OAAGA,GAAE,QAAS,CAAC;AAAE,IAAAC,GAAE,QAAS,CAACD,IAAEC,OAAID,GAAE,WAASE,GAAED,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAE;AAJz7O;AAI07O,WAAO,EAAE,KAAK,SAAS,IAAE,SAAK,UAAK,cAAL,mBAAgB,KAAM,CAAAC,OAAGA,GAAE,OAAKD,QAAK;AAAA,EAAI;AAAA,EAAC,sBAAsBA,IAAEC,IAAEC,IAAEI,IAAE,GAAEG,IAAEG,IAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,QAAG,CAAC,EAAEZ,EAAC,GAAE;AAAC,iBAAUD,MAAKC,GAAE,SAAS,MAAK,cAAcY,IAAEb,IAAEE,GAAE,cAAaU,EAAC;AAAE,WAAK,oBAAoBC,IAAE,CAAC,GAAEb,GAAE,KAAK,EAAC,iBAAgB,EAAC,MAAKC,GAAE,OAAM,aAAY,EAAC,UAAS,EAAC,MAAK,UAAS,QAAO,EAAEC,GAAE,iBAAiB,EAAC,EAAC,GAAE,IAAGD,GAAE,SAAQ,cAAaC,GAAE,kBAAiB,UAASI,IAAE,UAAS,GAAE,eAAc,YAAW,QAAO,CAAC,EAAE,OAAO,GAAE,EAAE,OAAO,CAAC,GAAE,kBAAiBG,GAAC,GAAE,YAAW,EAAC,UAASI,IAAE,cAAaX,GAAE,iBAAgB,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,cAAcF,IAAEC,IAAEC,IAAEI,IAAE;AAJx+P;AAIy+P,QAAG,EAAEL,EAAC,EAAE;AAAO,UAAK,EAAC,UAAS,GAAE,QAAOQ,IAAE,eAAcG,GAAC,IAAEX;AAAE,QAAG,EAAE,CAAC,EAAE;AAAO,QAAG,EAAE,SAAOC,GAAE,QAAO,OAAK,KAAAI,MAAA,gBAAAA,GAAG,aAAH,mBAAa,KAAK,IAAIL,GAAE,yCAAwC,aAAa,EAAE,IAAI,yBAAyBC,EAAC,WAAU,EAAC,SAAQD,GAAC,CAAC;AAAG,QAAG,EAAEQ,EAAC,EAAE,QAAO,OAAK,KAAAH,MAAA,gBAAAA,GAAG,aAAH,mBAAa,KAAK,IAAIL,GAAE,6BAA4B,qCAAoC,EAAC,SAAQA,GAAC,CAAC;AAAG,UAAME,KAAE,EAAC,YAAW,EAAC,GAAGF,GAAE,WAAU,GAAE,UAAS,EAAE,OAAO,GAAE,QAAOQ,GAAE,OAAO,EAAC;AAAE,MAAEG,EAAC,MAAIT,GAAE,YAAUS,GAAE,OAAO,IAAGZ,GAAE,KAAKG,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBH,IAAEC,IAAE;AAAC,UAAMC,KAAED,GAAE;AAAK,QAAIK,KAAE,EAAEJ,IAAEF,EAAC,IAAE;AAAE,UAAM,IAAE,oBAAI;AAAI,eAAU,KAAKA,IAAE;AAAC,QAAE,eAAa,EAAE,aAAW,CAAC;AAAG,YAAK,EAAC,YAAWA,GAAC,IAAE;AAAE,OAAC,QAAMA,GAAEE,EAAC,KAAG,EAAE,IAAIF,GAAEE,EAAC,CAAC,OAAKF,GAAEE,EAAC,IAAEI,OAAK,EAAE,IAAIN,GAAEE,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEM,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAACF,GAAE,CAAC,eAAc,SAAS,GAAE,gBAAe,CAAC,QAAQ,CAAC,CAAC,GAAEE,GAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAACF,GAAE,CAAC,WAAU,aAAa,GAAE,sBAAqB,CAAC,QAAQ,CAAC,CAAC,GAAEE,GAAE,WAAU,0BAAyB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,SAAQ,EAAC,WAAU,EAAC,OAAM,EAAC,SAAQ,MAAG,QAAO,oBAAmB,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAACF,GAAE,CAAC,WAAU,aAAa,GAAE,yBAAwB,CAAC,mBAAmB,CAAC,CAAC,GAAEE,GAAE,WAAU,mCAAkC,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,MAAG,OAAM,EAAC,SAAQ,MAAG,cAAa,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,SAAQ,EAAC,WAAU,EAAC,OAAM,EAAC,QAAO,gCAA+B,iBAAgB;AAAC,SAAM,EAAC,SAAQ,QAAM,KAAK,sBAAqB;AAAC,EAAC,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,QAAO,eAAe,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,MAAK,EAAC,OAAM,MAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACF,GAAE,CAAC,WAAU,aAAa,GAAE,YAAW,CAAC,QAAQ,CAAC,CAAC,GAAEE,GAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,MAAK,EAAC,OAAM,MAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACF,GAAE,CAAC,WAAU,aAAa,GAAE,YAAW,CAAC,QAAQ,CAAC,CAAC,GAAEE,GAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,mBAAkB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,sBAAqB,MAAK,CAAC,oBAAoB,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAACF,GAAE,CAAC,WAAU,aAAa,GAAE,oBAAmB,CAAC,QAAQ,CAAC,CAAC,GAAEE,GAAE,WAAU,wBAAuB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,SAAQ,EAAC,WAAU,EAAC,OAAM,EAAC,cAAa,KAAE,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAACF,GAAE,WAAU,aAAY,CAAC,QAAQ,CAAC,CAAC,GAAEE,GAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAACN,GAAE,WAAU,WAAW,CAAC,GAAEM,GAAE,WAAU,kBAAiB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,2BAA2B,CAAC,GAAEA,EAAC;AAAE,IAAM,IAAEA;", "names": ["e", "t", "r", "y", "h", "f", "o", "m", "K", "n", "a", "i", "s", "p"]}