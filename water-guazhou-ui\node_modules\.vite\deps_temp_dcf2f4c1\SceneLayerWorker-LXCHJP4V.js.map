{"version": 3, "sources": ["../../@arcgis/core/libs/i3s/enums.js", "../../@arcgis/core/libs/i3s/I3SModule.js", "../../@arcgis/core/views/3d/layers/i3s/I3SNode.js", "../../@arcgis/core/views/3d/layers/SceneLayerWorker.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nvar n,e;!function(n){n[n.None=0]=\"None\",n[n.Int16=1]=\"Int16\",n[n.Int32=2]=\"Int32\"}(n||(n={})),function(n){n[n.Replace=0]=\"Replace\",n[n.Outside=1]=\"Outside\",n[n.Inside=2]=\"Inside\",n[n.Finished=3]=\"Finished\"}(e||(e={}));export{n as IndexType,e as ModificationType};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{getAssetUrl as t}from\"../../assets.js\";function e(){return n||(n=new Promise((t=>import(\"../../chunks/i3s.js\").then((t=>t.i)).then((({default:e})=>{const n=e({locateFile:i,onRuntimeInitialized:()=>t(n)});delete n.then})))).catch((t=>{throw t}))),n}function i(e){return t(`esri/libs/i3s/${e}`)}let n;export{e as get};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{f as e}from\"../../../../chunks/sphere.js\";class t{constructor(t,i){this.id=t,this.mbs=i,this.renderMbs=e(0,0,0,-1),this.elevationRange=null}}class i{constructor(){this.min=1/0,this.max=-1/0,this.valid=!1}}var n,o,s,a,c;!function(e){e[e.Unmodified=0]=\"Unmodified\",e[e.Culled=1]=\"Culled\",e[e.NotChecked=2]=\"NotChecked\"}(n||(n={})),function(e){e[e.Unmodified=0]=\"Unmodified\",e[e.PotentiallyModified=1]=\"PotentiallyModified\",e[e.Culled=2]=\"Culled\",e[e.Unknown=3]=\"Unknown\",e[e.NotChecked=4]=\"NotChecked\"}(o||(o={}));class h extends t{constructor(e,t,i,n,a,c,h,d,r,l){super(e,i),this.index=t,this.childCount=n,this.level=a,this.resources=c,this.version=h,this.lodMetric=d,this.maxError=r,this.numFeatures=l,this.failed=!1,this.cacheState=s.Unknown,this.vertexCount=0,this.memory=0,this.childrenLoaded=0,this.hasModifications=!1,this.imModificationImpact=o.NotChecked}}!function(e){e[e.Unknown=0]=\"Unknown\",e[e.Uncached=1]=\"Uncached\",e[e.Cached=2]=\"Cached\"}(s||(s={})),function(e){e[e.None=0]=\"None\",e[e.MaxScreenThreshold=1]=\"MaxScreenThreshold\",e[e.ScreenSpaceRelative=2]=\"ScreenSpaceRelative\",e[e.RemovedFeatureDiameter=3]=\"RemovedFeatureDiameter\",e[e.DistanceRangeFromDefaultCamera=4]=\"DistanceRangeFromDefaultCamera\"}(a||(a={})),function(e){e[e.Hole=0]=\"Hole\",e[e.Leaf=1]=\"Leaf\"}(c||(c={}));class d{constructor(e,t,i,n){this.nodeHasLOD=e,this.isChosen=t,this.lodLevel=i,this.version=n}}export{s as CacheState,i as ElevationRange,a as LodMetric,h as Node,t as NodeBase,n as NodeFilterImpact,o as NodeIMModificationImpact,c as NodeState,d as NodeTraversalState};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../core/maybe.js\";import{IndexType as t}from\"../../../libs/i3s/enums.js\";import{get as r}from\"../../../libs/i3s/I3SModule.js\";import{NodeIMModificationImpact as n}from\"./i3s/I3SNode.js\";async function o(e){await p();const t=[e.geometryBuffer];return{result:b(e,t),transferList:t}}async function s(e){await p();const t=[e.geometryBuffer],{geometryBuffer:r}=e,n=r.byteLength,o=u._malloc(n),s=new Uint8Array(u.HEAPU8.buffer,o,n);s.set(new Uint8Array(r));const i=u.dracoDecompressPointCloudData(o,s.byteLength);if(u._free(o),i.error.length>0)throw new Error(`i3s.wasm: ${i.error}`);const f=i.featureIds?.length>0?i.featureIds.slice():null,a=i.positions.slice();return f&&t.push(f.buffer),t.push(a.buffer),{result:{positions:a,featureIds:f},transferList:t}}async function i(e){await p(),d(e);const t={buffer:e.buffer};return{result:t,transferList:[t.buffer]}}async function f(e){await p(),y(e)}async function a(e){await p(),u.setLegacySchema(e.context,e.jsonSchema)}function l(e){E(e)}let c,u;function y(e){const t=e.modifications,r=u._malloc(8*t.length),n=new Float64Array(u.HEAPU8.buffer,r,t.length);for(let o=0;o<t.length;++o)n[o]=t[o];u.setModifications(e.context,r,t.length,e.isGeodetic),u._free(r)}function b(r,n){if(!u)return null;const{context:o,localOrigin:s,globalTrafo:i,mbs:f,obb:a,elevationOffset:l,geometryBuffer:c,geometryDescriptor:y,indexToVertexProjector:b,vertexToRenderProjector:m}=r,d=u._malloc(c.byteLength),E=33,p=u._malloc(E*Float64Array.BYTES_PER_ELEMENT),g=new Uint8Array(u.HEAPU8.buffer,d,c.byteLength);g.set(new Uint8Array(c));const w=new Float64Array(u.HEAPU8.buffer,p,E);h(w,s);let A=w.byteOffset+3*w.BYTES_PER_ELEMENT,_=new Float64Array(w.buffer,A);h(_,i),A+=16*w.BYTES_PER_ELEMENT,_=new Float64Array(w.buffer,A),h(_,f),A+=4*w.BYTES_PER_ELEMENT,e(a)&&(_=new Float64Array(w.buffer,A),h(_,a.center),A+=3*w.BYTES_PER_ELEMENT,_=new Float64Array(w.buffer,A),h(_,a.halfSize),A+=3*w.BYTES_PER_ELEMENT,_=new Float64Array(w.buffer,A),h(_,a.quaternion));const L=y,I={isDraco:!1,isLegacy:!1,color:r.layouts.some((e=>e.some((e=>\"color\"===e.name)))),normal:r.needNormals&&r.layouts.some((e=>e.some((e=>\"normalCompressed\"===e.name)))),uv0:r.layouts.some((e=>e.some((e=>\"uv0\"===e.name)))),uvRegion:r.layouts.some((e=>e.some((e=>\"uvRegion\"===e.name)))),featureIndex:L.featureIndex},T=u.process(o,!!r.obb,d,g.byteLength,L,I,p,l,b,m,r.normalReferenceFrame);if(u._free(p),u._free(d),T.error.length>0)throw new Error(`i3s.wasm: ${T.error}`);if(T.discarded)return null;const P=T.componentOffsets.length>0?T.componentOffsets.slice():null,U=T.featureIds.length>0?T.featureIds.slice():null,B=T.interleavedVertedData.slice().buffer,F=T.indicesType===t.Int16?new Uint16Array(T.indices.buffer,T.indices.byteOffset,T.indices.byteLength/2).slice():new Uint32Array(T.indices.buffer,T.indices.byteOffset,T.indices.byteLength/4).slice(),M=T.positions.slice(),S=T.positionIndicesType===t.Int16?new Uint16Array(T.positionIndices.buffer,T.positionIndices.byteOffset,T.positionIndices.byteLength/2).slice():new Uint32Array(T.positionIndices.buffer,T.positionIndices.byteOffset,T.positionIndices.byteLength/4).slice(),x={layout:r.layouts[0],interleavedVertexData:B,indices:F,hasColors:T.hasColors,hasModifications:T.hasModifications,positionData:{data:M,indices:S}};return U&&n.push(U.buffer),P&&n.push(P.buffer),n.push(B),n.push(F.buffer),n.push(M.buffer),n.push(S.buffer),{componentOffsets:P,featureIds:U,transformedGeometry:x,obb:T.obb}}function m(e){return 0===e?n.Unmodified:1===e?n.PotentiallyModified:2===e?n.Culled:n.Unknown}function d(e){const{context:t,buffer:r}=e,n=u._malloc(r.byteLength),o=r.byteLength/Float64Array.BYTES_PER_ELEMENT,s=new Float64Array(u.HEAPU8.buffer,n,o),i=new Float64Array(r);s.set(i),u.filterOBBs(t,n,o),i.set(s),u._free(n)}function E(e){u&&u.destroy(e)}function h(e,t){for(let r=0;r<t.length;++r)e[r]=t[r]}function p(){return u?Promise.resolve():(c||(c=r().then((e=>{u=e,c=null}))),c)}const g={transform:b,destroy:E};export{l as destroyContext,s as dracoDecompressPointCloudData,i as filterObbsForModifications,d as filterObbsForModificationsSync,p as initialize,m as interpretObbModificationResults,o as process,a as setLegacySchema,f as setModifications,y as setModificationsSync,g as test};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAI;AAAJ,IAAM;AAAE,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,QAAM,CAAC,IAAE;AAAO,EAAE,MAAI,IAAE,CAAC,EAAE,GAAE,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,WAAS,CAAC,IAAE;AAAU,EAAE,MAAI,IAAE,CAAC,EAAE;;;ACA1K,SAASC,KAAG;AAAC,SAAOC,OAAIA,KAAE,IAAI,QAAS,OAAG,OAAO,mBAAqB,EAAE,KAAM,CAAAC,OAAGA,GAAE,CAAE,EAAE,KAAM,CAAC,EAAC,SAAQF,GAAC,MAAI;AAAC,UAAMC,KAAED,GAAE,EAAC,YAAW,GAAE,sBAAqB,MAAI,EAAEC,EAAC,EAAC,CAAC;AAAE,WAAOA,GAAE;AAAA,EAAI,CAAE,CAAE,EAAE,MAAO,OAAG;AAAC,UAAM;AAAA,EAAC,CAAE,IAAGA;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,SAAO,EAAE,iBAAiBA,EAAC,EAAE;AAAC;AAAC,IAAIC;;;ACA5F,IAAIE;AAAJ,IAAM;AAAN,IAAQ;AAAR,IAAUC;AAAV,IAAY;AAAE,CAAC,SAASC,IAAE;AAAC,EAAAA,GAAEA,GAAE,aAAW,CAAC,IAAE,cAAaA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,aAAW,CAAC,IAAE;AAAY,EAAEF,OAAIA,KAAE,CAAC,EAAE,GAAE,SAASE,IAAE;AAAC,EAAAA,GAAEA,GAAE,aAAW,CAAC,IAAE,cAAaA,GAAEA,GAAE,sBAAoB,CAAC,IAAE,uBAAsBA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,aAAW,CAAC,IAAE;AAAY,EAAE,MAAI,IAAE,CAAC,EAAE;AAAiW,CAAC,SAASC,IAAE;AAAC,EAAAA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,SAAO,CAAC,IAAE;AAAQ,EAAE,MAAI,IAAE,CAAC,EAAE,GAAE,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,qBAAmB,CAAC,IAAE,sBAAqBA,GAAEA,GAAE,sBAAoB,CAAC,IAAE,uBAAsBA,GAAEA,GAAE,yBAAuB,CAAC,IAAE,0BAAyBA,GAAEA,GAAE,iCAA+B,CAAC,IAAE;AAAgC,EAAEC,OAAIA,KAAE,CAAC,EAAE,GAAE,SAASD,IAAE;AAAC,EAAAA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,OAAK,CAAC,IAAE;AAAM,EAAE,MAAI,IAAE,CAAC,EAAE;;;ACAvjC,eAAeE,GAAEC,IAAE;AAAC,QAAM,EAAE;AAAE,QAAM,IAAE,CAACA,GAAE,cAAc;AAAE,SAAM,EAAC,QAAO,EAAEA,IAAE,CAAC,GAAE,cAAa,EAAC;AAAC;AAAC,eAAeC,GAAED,IAAE;AAJzU;AAI0U,QAAM,EAAE;AAAE,QAAM,IAAE,CAACA,GAAE,cAAc,GAAE,EAAC,gBAAeE,GAAC,IAAEF,IAAEG,KAAED,GAAE,YAAWH,KAAE,EAAE,QAAQI,EAAC,GAAEF,KAAE,IAAI,WAAW,EAAE,OAAO,QAAOF,IAAEI,EAAC;AAAE,EAAAF,GAAE,IAAI,IAAI,WAAWC,EAAC,CAAC;AAAE,QAAME,KAAE,EAAE,8BAA8BL,IAAEE,GAAE,UAAU;AAAE,MAAG,EAAE,MAAMF,EAAC,GAAEK,GAAE,MAAM,SAAO,EAAE,OAAM,IAAI,MAAM,aAAaA,GAAE,KAAK,EAAE;AAAE,QAAMC,OAAE,KAAAD,GAAE,eAAF,mBAAc,UAAO,IAAEA,GAAE,WAAW,MAAM,IAAE,MAAKE,KAAEF,GAAE,UAAU,MAAM;AAAE,SAAOC,MAAG,EAAE,KAAKA,GAAE,MAAM,GAAE,EAAE,KAAKC,GAAE,MAAM,GAAE,EAAC,QAAO,EAAC,WAAUA,IAAE,YAAWD,GAAC,GAAE,cAAa,EAAC;AAAC;AAAC,eAAeD,GAAEJ,IAAE;AAAC,QAAM,EAAE,GAAE,EAAEA,EAAC;AAAE,QAAM,IAAE,EAAC,QAAOA,GAAE,OAAM;AAAE,SAAM,EAAC,QAAO,GAAE,cAAa,CAAC,EAAE,MAAM,EAAC;AAAC;AAAC,eAAe,EAAEA,IAAE;AAAC,QAAM,EAAE,GAAE,EAAEA,EAAC;AAAC;AAAC,eAAeM,GAAEN,IAAE;AAAC,QAAM,EAAE,GAAE,EAAE,gBAAgBA,GAAE,SAAQA,GAAE,UAAU;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,EAAAO,GAAEP,EAAC;AAAC;AAAC,IAAIQ;AAAJ,IAAM;AAAE,SAAS,EAAER,IAAE;AAAC,QAAM,IAAEA,GAAE,eAAcE,KAAE,EAAE,QAAQ,IAAE,EAAE,MAAM,GAAEC,KAAE,IAAI,aAAa,EAAE,OAAO,QAAOD,IAAE,EAAE,MAAM;AAAE,WAAQH,KAAE,GAAEA,KAAE,EAAE,QAAO,EAAEA,GAAE,CAAAI,GAAEJ,EAAC,IAAE,EAAEA,EAAC;AAAE,IAAE,iBAAiBC,GAAE,SAAQE,IAAE,EAAE,QAAOF,GAAE,UAAU,GAAE,EAAE,MAAME,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,MAAG,CAAC,EAAE,QAAO;AAAK,QAAK,EAAC,SAAQJ,IAAE,aAAYE,IAAE,aAAYG,IAAE,KAAIC,IAAE,KAAIC,IAAE,iBAAgBG,IAAE,gBAAeD,IAAE,oBAAmBE,IAAE,wBAAuBC,IAAE,yBAAwBC,GAAC,IAAEV,IAAEW,KAAE,EAAE,QAAQL,GAAE,UAAU,GAAED,KAAE,IAAGO,KAAE,EAAE,QAAQP,KAAE,aAAa,iBAAiB,GAAEQ,KAAE,IAAI,WAAW,EAAE,OAAO,QAAOF,IAAEL,GAAE,UAAU;AAAE,EAAAO,GAAE,IAAI,IAAI,WAAWP,EAAC,CAAC;AAAE,QAAM,IAAE,IAAI,aAAa,EAAE,OAAO,QAAOM,IAAEP,EAAC;AAAE,IAAE,GAAEN,EAAC;AAAE,MAAI,IAAE,EAAE,aAAW,IAAE,EAAE,mBAAkB,IAAE,IAAI,aAAa,EAAE,QAAO,CAAC;AAAE,IAAE,GAAEG,EAAC,GAAE,KAAG,KAAG,EAAE,mBAAkB,IAAE,IAAI,aAAa,EAAE,QAAO,CAAC,GAAE,EAAE,GAAEC,EAAC,GAAE,KAAG,IAAE,EAAE,mBAAkB,EAAEC,EAAC,MAAI,IAAE,IAAI,aAAa,EAAE,QAAO,CAAC,GAAE,EAAE,GAAEA,GAAE,MAAM,GAAE,KAAG,IAAE,EAAE,mBAAkB,IAAE,IAAI,aAAa,EAAE,QAAO,CAAC,GAAE,EAAE,GAAEA,GAAE,QAAQ,GAAE,KAAG,IAAE,EAAE,mBAAkB,IAAE,IAAI,aAAa,EAAE,QAAO,CAAC,GAAE,EAAE,GAAEA,GAAE,UAAU;AAAG,QAAM,IAAEI,IAAE,IAAE,EAAC,SAAQ,OAAG,UAAS,OAAG,OAAMR,GAAE,QAAQ,KAAM,CAAAF,OAAGA,GAAE,KAAM,CAAAA,OAAG,YAAUA,GAAE,IAAK,CAAE,GAAE,QAAOE,GAAE,eAAaA,GAAE,QAAQ,KAAM,CAAAF,OAAGA,GAAE,KAAM,CAAAA,OAAG,uBAAqBA,GAAE,IAAK,CAAE,GAAE,KAAIE,GAAE,QAAQ,KAAM,CAAAF,OAAGA,GAAE,KAAM,CAAAA,OAAG,UAAQA,GAAE,IAAK,CAAE,GAAE,UAASE,GAAE,QAAQ,KAAM,CAAAF,OAAGA,GAAE,KAAM,CAAAA,OAAG,eAAaA,GAAE,IAAK,CAAE,GAAE,cAAa,EAAE,aAAY,GAAE,IAAE,EAAE,QAAQD,IAAE,CAAC,CAACG,GAAE,KAAIW,IAAEE,GAAE,YAAW,GAAE,GAAED,IAAEL,IAAEE,IAAEC,IAAEV,GAAE,oBAAoB;AAAE,MAAG,EAAE,MAAMY,EAAC,GAAE,EAAE,MAAMD,EAAC,GAAE,EAAE,MAAM,SAAO,EAAE,OAAM,IAAI,MAAM,aAAa,EAAE,KAAK,EAAE;AAAE,MAAG,EAAE,UAAU,QAAO;AAAK,QAAM,IAAE,EAAE,iBAAiB,SAAO,IAAE,EAAE,iBAAiB,MAAM,IAAE,MAAK,IAAE,EAAE,WAAW,SAAO,IAAE,EAAE,WAAW,MAAM,IAAE,MAAK,IAAE,EAAE,sBAAsB,MAAM,EAAE,QAAO,IAAE,EAAE,gBAAc,EAAE,QAAM,IAAI,YAAY,EAAE,QAAQ,QAAO,EAAE,QAAQ,YAAW,EAAE,QAAQ,aAAW,CAAC,EAAE,MAAM,IAAE,IAAI,YAAY,EAAE,QAAQ,QAAO,EAAE,QAAQ,YAAW,EAAE,QAAQ,aAAW,CAAC,EAAE,MAAM,GAAE,IAAE,EAAE,UAAU,MAAM,GAAE,IAAE,EAAE,wBAAsB,EAAE,QAAM,IAAI,YAAY,EAAE,gBAAgB,QAAO,EAAE,gBAAgB,YAAW,EAAE,gBAAgB,aAAW,CAAC,EAAE,MAAM,IAAE,IAAI,YAAY,EAAE,gBAAgB,QAAO,EAAE,gBAAgB,YAAW,EAAE,gBAAgB,aAAW,CAAC,EAAE,MAAM,GAAE,IAAE,EAAC,QAAOX,GAAE,QAAQ,CAAC,GAAE,uBAAsB,GAAE,SAAQ,GAAE,WAAU,EAAE,WAAU,kBAAiB,EAAE,kBAAiB,cAAa,EAAC,MAAK,GAAE,SAAQ,EAAC,EAAC;AAAE,SAAO,KAAGC,GAAE,KAAK,EAAE,MAAM,GAAE,KAAGA,GAAE,KAAK,EAAE,MAAM,GAAEA,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,EAAE,MAAM,GAAEA,GAAE,KAAK,EAAE,MAAM,GAAEA,GAAE,KAAK,EAAE,MAAM,GAAE,EAAC,kBAAiB,GAAE,YAAW,GAAE,qBAAoB,GAAE,KAAI,EAAE,IAAG;AAAC;AAAC,SAAS,EAAEH,IAAE;AAAC,SAAO,MAAIA,KAAE,EAAE,aAAW,MAAIA,KAAE,EAAE,sBAAoB,MAAIA,KAAE,EAAE,SAAO,EAAE;AAAO;AAAC,SAAS,EAAEA,IAAE;AAAC,QAAK,EAAC,SAAQ,GAAE,QAAOE,GAAC,IAAEF,IAAEG,KAAE,EAAE,QAAQD,GAAE,UAAU,GAAEH,KAAEG,GAAE,aAAW,aAAa,mBAAkBD,KAAE,IAAI,aAAa,EAAE,OAAO,QAAOE,IAAEJ,EAAC,GAAEK,KAAE,IAAI,aAAaF,EAAC;AAAE,EAAAD,GAAE,IAAIG,EAAC,GAAE,EAAE,WAAW,GAAED,IAAEJ,EAAC,GAAEK,GAAE,IAAIH,EAAC,GAAE,EAAE,MAAME,EAAC;AAAC;AAAC,SAASI,GAAEP,IAAE;AAAC,OAAG,EAAE,QAAQA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE;AAAC,WAAQE,KAAE,GAAEA,KAAE,EAAE,QAAO,EAAEA,GAAE,CAAAF,GAAEE,EAAC,IAAE,EAAEA,EAAC;AAAC;AAAC,SAAS,IAAG;AAAC,SAAO,IAAE,QAAQ,QAAQ,KAAGM,OAAIA,KAAER,GAAE,EAAE,KAAM,CAAAA,OAAG;AAAC,QAAEA,IAAEQ,KAAE;AAAA,EAAI,CAAE,IAAGA;AAAE;AAAC,IAAM,IAAE,EAAC,WAAU,GAAE,SAAQD,GAAC;", "names": ["n", "e", "n", "t", "n", "a", "e", "e", "a", "o", "e", "s", "r", "n", "i", "f", "a", "E", "c", "l", "y", "b", "m", "d", "p", "g"]}