import {
  a as a5,
  d as d4,
  h as h2,
  m as m6,
  x as x2
} from "./chunk-I4NBMHG5.js";
import {
  C as C2,
  c as c6,
  f as f5,
  h as h3,
  i as i2,
  m as m7
} from "./chunk-6SD5VHPQ.js";
import {
  $ as $2,
  C,
  J,
  Q,
  T as T3,
  U as U3,
  V as V2,
  j as j4,
  ne,
  oe
} from "./chunk-OGHY3MGX.js";
import {
  $,
  L as L3,
  S as S4,
  V,
  c as c4,
  c2 as c5,
  d as d2,
  j as j3,
  l as l3,
  m as m5,
  n as n4,
  o as o4,
  w as w4
} from "./chunk-YJ6DAFGY.js";
import {
  a as a4,
  d as d3,
  g,
  h,
  m as m4,
  p as p5,
  s as s3,
  v as v3,
  y as y3
} from "./chunk-JI2BFAR3.js";
import {
  B,
  D as D2,
  L as L2,
  N,
  R as R3,
  S as S3,
  T as T2,
  U as U2,
  g as g2,
  j as j2,
  n3,
  n4 as n5,
  p as p6,
  r as r2,
  r2 as r3,
  u as u2,
  v2 as v4
} from "./chunk-XOTZS7TA.js";
import {
  D,
  F,
  R as R2,
  S as S2,
  b as b2,
  d,
  f2 as f3,
  f3 as f4,
  m as m2,
  m3
} from "./chunk-7UML52SE.js";
import {
  u
} from "./chunk-YACF4WM5.js";
import {
  n as n2,
  z
} from "./chunk-KHDLCZBA.js";
import "./chunk-WJKHSSMC.js";
import "./chunk-44A5UMFM.js";
import "./chunk-ANH6666P.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-O2BYTJI4.js";
import {
  j,
  p
} from "./chunk-IEIKQ72S.js";
import "./chunk-3IDKVHSA.js";
import "./chunk-2RO3UJ2R.js";
import "./chunk-P2G4OGHI.js";
import "./chunk-4DICTWL2.js";
import "./chunk-WGU7CS6R.js";
import "./chunk-4JOAXMIS.js";
import "./chunk-UYHYCNVE.js";
import "./chunk-INCFUNSS.js";
import "./chunk-6JIS2R4B.js";
import "./chunk-7BSY2CUN.js";
import "./chunk-6RN6WNY4.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-DTQ34PEY.js";
import {
  p as p3
} from "./chunk-KTB2COPC.js";
import {
  S
} from "./chunk-HTXGAKOK.js";
import {
  o as o3
} from "./chunk-OQK7L3JR.js";
import {
  p as p4
} from "./chunk-5BWF7URZ.js";
import {
  a as a3
} from "./chunk-ND4JUK42.js";
import "./chunk-2WMCP27R.js";
import {
  p as p2
} from "./chunk-JV6TBH5W.js";
import {
  _
} from "./chunk-TV6WKLTU.js";
import "./chunk-5VDORDNW.js";
import {
  c as c3
} from "./chunk-FHKOFAQ2.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import "./chunk-3HW44BD3.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-P37TUI4J.js";
import "./chunk-Q4VCSCSY.js";
import {
  n
} from "./chunk-LAEW33J6.js";
import {
  t as t2
} from "./chunk-NGPCXWDX.js";
import "./chunk-IXMTA2RC.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-67MHB3E3.js";
import "./chunk-MKG56XBH.js";
import "./chunk-FZ7BG3VX.js";
import {
  x
} from "./chunk-N4YJNWPS.js";
import {
  y as y2
} from "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-NUZU7NCS.js";
import "./chunk-LHJ7PAFO.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import {
  c as c2,
  f as f2
} from "./chunk-YJWWP4AU.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-EQYLMJEJ.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-5EGPPD3R.js";
import "./chunk-L66EKMJP.js";
import "./chunk-3M3FTH72.js";
import "./chunk-XLHYMGQY.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-52L4L36S.js";
import "./chunk-VTOVDEW2.js";
import "./chunk-UKESBP6X.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-TD7BKFJX.js";
import {
  k
} from "./chunk-LNGMNZ4I.js";
import "./chunk-24NZLSKM.js";
import "./chunk-BNFBWJYJ.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-23VNIYCK.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-OVIRLBJH.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import {
  m
} from "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import {
  U
} from "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w3
} from "./chunk-XTO3XXZ3.js";
import {
  l as l2
} from "./chunk-QUHG7NMD.js";
import "./chunk-4YKNTCHK.js";
import "./chunk-XVA5SA7P.js";
import {
  L
} from "./chunk-U4SVMKOQ.js";
import "./chunk-CGBA4LNQ.js";
import "./chunk-2RZLOKYO.js";
import "./chunk-VIXZ7ZAD.js";
import {
  w as w2
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  R,
  f2 as f
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-HHMRK4FX.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import {
  o as o2
} from "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e as e2
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T,
  v
} from "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  E,
  a as a2,
  v as v2,
  w
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  has
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  c,
  e,
  i,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/rasterDatasets/BaseRaster.js
var $3 = 8;
var X = 256;
var K = class extends m(l) {
  constructor() {
    super(...arguments), this.rasterJobHandler = null, this.datasetName = null, this.datasetFormat = null, this.hasUniqueSourceStorageInfo = true, this.rasterInfo = null, this.ioConfig = { sampling: "closest" };
  }
  async init() {
    const e4 = T3();
    this.addResolvingPromise(e4), await this.when();
  }
  normalizeCtorArgs(e4) {
    return e4 && e4.ioConfig && (e4 = { ...e4, ioConfig: { resolution: null, bandIds: null, sampling: "closest", tileInfo: j.create(), ...e4.ioConfig } }), e4;
  }
  get _isGlobalWrappableSource() {
    const { rasterInfo: e4 } = this, t5 = U3(e4.spatialReference);
    return r(t5) && e4.extent.width >= t5 / 2;
  }
  set url(e4) {
    this._set("url", S(e4, s.getLogger(this.declaredClass)));
  }
  async open(e4) {
    throw new s2("BaseRaster:open-not-implemented", "open() is not implemented");
  }
  async fetchTile(e4, t5, i4, o6 = {}) {
    const r5 = o6.tileInfo || this.rasterInfo.storageInfo.tileInfo, n9 = this.getTileExtentFromTileInfo(e4, t5, i4, r5);
    return this.fetchPixels(n9, r5.size[0], r5.size[1], o6);
  }
  async identify(e4, t5 = {}) {
    var _a;
    e4 = v(w2, e4).clone().normalize();
    const { multidimensionalDefinition: i4, timeExtent: o6 } = t5, { rasterInfo: r5 } = this, { hasMultidimensionalTranspose: l5, multidimensionalInfo: c10 } = r5;
    let { transposedVariableName: m9 } = t5;
    const f8 = r(c10) && l5 && (null != o6 || g(i4));
    if (f8 && !m9) {
      m9 = r(i4) && i4.length > 0 ? i4[0].variableName ?? void 0 : c10.variables[0].name, t5 = { ...t5, transposedVariableName: m9 };
    }
    t5 = this._getRequestOptionsWithSliceId(t5);
    const { spatialReference: u5, extent: p8 } = r5, { datumTransformation: d7 } = t5;
    let x4 = j4(e4, u5, d7);
    if (!p8.intersects(x4)) return { location: x4, value: null };
    if (r(r5.transform)) {
      const e5 = r5.transform.inverseTransform(x4);
      if (!r5.nativeExtent.intersects(e5)) return { location: e5, value: null };
      x4 = e5;
    }
    let y4 = 0;
    const g3 = r(m9) && r(c10) && r5.hasMultidimensionalTranspose;
    if ("Function" === this.datasetFormat) {
      const e5 = this.primaryRasters.rasters[0];
      if (g3) return e5.identify(x4, t5);
      const { pixelSize: i5 } = r5, o7 = 3, a6 = i5.x * o7 / 2, l6 = i5.y * o7 / 2, c11 = new w3({ xmin: x4.x - a6, xmax: x4.x + a6, ymin: x4.y - l6, ymax: x4.y + l6, spatialReference: u5 }), m10 = { interpolation: "nearest" }, { pixelBlock: f9 } = await e5.fetchPixels(c11, o7, o7, m10), { pixelBlock: h5 } = await this.fetchPixels(c11, o7, o7, m10);
      if (t(f9)) return { location: x4, value: null };
      const p9 = Math.floor(o7 * o7 * 0.5), d8 = !f9.mask || f9.mask[p9] ? f9.pixels.map((e6) => e6[p9]) : null;
      let y5;
      return r(h5) && (y5 = !h5.mask || h5.mask[p9] ? h5.pixels.map((e6) => e6[p9]) : void 0), { location: x4, value: d8, processedValue: y5, pyramidLevel: 0 };
    }
    if (!g3) {
      if (t5.srcResolution) {
        y4 = oe(t5.srcResolution, r5, this.ioConfig.sampling).pyramidLevel;
      } else if (y4 = await this.computeBestPyramidLevelForLocation(e4, t5), null == y4) return { location: x4, value: null };
    }
    const I4 = this.identifyPixelLocation(x4, y4, null, g3);
    if (null === I4) return { location: x4, value: null };
    const { row: w8, col: k3, rowOffset: S6, colOffset: B2, blockWidth: P } = I4, M = m9 ?? e(t5.sliceId), W = a5(this.url, M), _2 = `${y4}/${w8}/${k3}`;
    let C4 = x2(W, null, _2);
    t(C4) && (C4 = this.fetchRawTile(y4, w8, k3, t5), h2(W, null, _2, C4));
    const j5 = await C4;
    if (t(j5) || !((_a = j5.pixels) == null ? void 0 : _a.length)) return { location: x4, value: null };
    const H = S6 * P + B2;
    return this._processIdentifyResult(j5, { srcLocation: x4, position: H, pyramidLevel: y4, useTransposedTile: !!g3, requestSomeSlices: f8, identifyOptions: t5 });
  }
  async fetchPixels(e4, t5, i4, o6 = {}) {
    if (e4 = ne(e4), (o6 = this._getRequestOptionsWithSliceId(o6)).requestRawData) return this._fetchPixels(e4, t5, i4, o6);
    const r5 = U3(e4.spatialReference), n9 = Q(e4);
    if (t(r5) || 0 === n9 || 1 === n9 && this._isGlobalWrappableSource) return this._fetchPixels(e4, t5, i4, o6);
    if (n9 >= 3) return { extent: e4, pixelBlock: null };
    const a6 = [], { xmin: l5, xmax: c10 } = e4, m9 = Math.round(r5 / (c10 - l5) * t5), f8 = m9 - Math.round((r5 / 2 - l5) / (c10 - l5) * t5);
    let h5 = 0;
    const u5 = [];
    for (let s4 = 0; s4 <= n9; s4++) {
      const p9 = new w3({ xmin: 0 === s4 ? l5 : -r5 / 2, xmax: s4 === n9 ? c10 - r5 * s4 : r5 / 2, ymin: e4.ymin, ymax: e4.ymax, spatialReference: e4.spatialReference }), d8 = 0 === s4 ? m9 - f8 : s4 === n9 ? t5 - h5 : m9;
      h5 += d8, u5.push(d8);
      const x5 = o6.disableWrapAround && s4 > 0 ? null : this._fetchPixels(p9, d8, i4, o6);
      a6.push(x5);
    }
    const p8 = (await Promise.all(a6)).map((e5) => e5 == null ? void 0 : e5.pixelBlock);
    let d7 = null;
    const x4 = { width: t5, height: i4 };
    if (this.rasterJobHandler) {
      d7 = (await this.rasterJobHandler.mosaicAndTransform({ srcPixelBlocks: p8, srcMosaicSize: x4, destDimension: null, coefs: null, sampleSpacing: null, interpolation: "nearest", alignmentInfo: null, blockWidths: u5 }, o6)).pixelBlock;
    } else d7 = b2(p8, x4, { blockWidths: u5 });
    return { extent: e4, srcExtent: J(e4, this.rasterInfo.spatialReference, o6.datumTransformation), pixelBlock: d7 };
  }
  async fetchRawPixels(e4, t5, i4, o6 = {}) {
    t5 = { x: Math.floor(t5.x), y: Math.floor(t5.y) };
    const r5 = await this._fetchRawTiles(e4, t5, i4, o6), { nativeExtent: s4, nativePixelSize: a6, storageInfo: l5 } = this.rasterInfo, c10 = 2 ** e4, m9 = a6.x * c10, f8 = a6.y * c10, h5 = new w3({ xmin: s4.xmin + m9 * t5.x, xmax: s4.xmin + m9 * (t5.x + i4.width - 1), ymin: s4.ymax - f8 * (t5.y + i4.height - 1), ymax: s4.ymax - f8 * t5.y, spatialReference: s4.spatialReference });
    if (!r5) return { extent: h5, srcExtent: h5, pixelBlock: null };
    const { pixelBlocks: u5, mosaicSize: p8 } = r5;
    if (1 === u5.length && r(u5[0]) && u5[0].width === i4.width && u5[0].height === i4.height) return { extent: h5, srcExtent: h5, pixelBlock: r5.pixelBlocks[0] };
    const d7 = e4 > 0 ? l5.pyramidBlockWidth : l5.blockWidth, x4 = e4 > 0 ? l5.pyramidBlockHeight : l5.blockHeight, y4 = { x: t5.x % d7, y: t5.y % x4 };
    let g3;
    if (this.rasterJobHandler) {
      g3 = (await this.rasterJobHandler.mosaicAndTransform({ srcPixelBlocks: u5, srcMosaicSize: p8, destDimension: i4, clipOffset: y4, clipSize: i4, coefs: null, sampleSpacing: null, interpolation: o6.interpolation, alignmentInfo: null, blockWidths: null }, o6)).pixelBlock;
    } else g3 = b2(u5, p8, { clipOffset: y4, clipSize: i4 });
    return { extent: h5, srcExtent: h5, pixelBlock: g3 };
  }
  fetchRawTile(e4, t5, o6, r5) {
    throw new s2("BaseRaster:read-not-implemented", "fetchRawTile() is not implemented");
  }
  computeExtent(e4) {
    return J(this.rasterInfo.extent, e4);
  }
  decodePixelBlock(e4, t5) {
    return !this.rasterJobHandler || t5.useCanvas ? S3(e4, t5) : this.rasterJobHandler.decode({ data: e4, options: t5 });
  }
  async request(e4, i4, o6 = 0) {
    const { customFetchParameters: r5 } = this.ioConfig, { range: n9, query: s4, headers: a6 } = i4;
    o6 = o6 ?? i4.retryCount ?? this.ioConfig.retryCount;
    const l5 = n9 ? { Range: `bytes=${n9.from}-${n9.to}` } : null;
    try {
      return await U(e4, { ...i4, query: { ...s4, ...r5 }, headers: { ...a6, ...l5 } });
    } catch (c10) {
      if (o6 > 0) return o6--, this.request(e4, i4, o6);
      throw c10;
    }
  }
  getSliceIndex(e4) {
    const { multidimensionalInfo: t5 } = this.rasterInfo;
    return t(t5) || t(e4) || 0 === e4.length ? null : y3(e4, t5);
  }
  getTileExtentFromTileInfo(e4, t5, i4, o6) {
    const r5 = c(o6.lodAt(e4));
    return this.getTileExtent({ x: r5.resolution, y: r5.resolution }, t5, i4, o6.origin, o6.spatialReference, o6.size);
  }
  updateTileInfo() {
    const { storageInfo: e4, spatialReference: t5, extent: i4, pixelSize: o6 } = this.rasterInfo;
    if (!e4.tileInfo) {
      const r5 = [], n9 = e4.maximumPyramidLevel || 0;
      let s4 = Math.max(o6.x, o6.y), a6 = 1 / 0.0254 * 96 * s4;
      for (let e5 = 0; e5 <= n9; e5++) r5.push(new p({ level: n9 - e5, resolution: s4, scale: a6 })), s4 *= 2, a6 *= 2;
      const l5 = new w2({ x: i4.xmin, y: i4.ymax, spatialReference: t5 });
      e4.tileInfo = new j({ origin: l5, size: [e4.blockWidth, e4.blockHeight], spatialReference: t5, lods: r5 }), e4.isVirtualTileInfo = true;
    }
  }
  createRemoteDatasetStorageInfo(e4, t5 = 512, i4 = 512, o6) {
    const { width: r5, height: n9, nativeExtent: s4, pixelSize: a6, spatialReference: l5 } = e4, c10 = new w2({ x: s4.xmin, y: s4.ymax, spatialReference: l5 });
    null == o6 && (o6 = Math.max(0, Math.round(Math.log(Math.max(r5, n9)) / Math.LN2 - 8)));
    const m9 = this.computeBlockBoundary(s4, 512, 512, { x: s4.xmin, y: s4.ymax }, [a6], o6);
    e4.storageInfo = new n3({ blockWidth: t5, blockHeight: i4, pyramidBlockWidth: t5, pyramidBlockHeight: i4, origin: c10, firstPyramidLevel: 1, maximumPyramidLevel: o6, blockBoundary: m9 });
  }
  async computeBestPyramidLevelForLocation(e4, t5 = {}) {
    return 0;
  }
  computeBlockBoundary(e4, t5, i4, o6, r5, n9 = 0, s4 = 2) {
    if (1 === r5.length && n9 > 0) {
      r5 = [...r5];
      let { x: e5, y: t6 } = r5[0];
      for (let i5 = 0; i5 < n9; i5++) e5 *= s4, t6 *= s4, r5.push({ x: e5, y: t6 });
    }
    const a6 = [], { x: l5, y: c10 } = o6;
    for (let m9 = 0; m9 < r5.length; m9++) {
      const { x: o7, y: n10 } = r5[m9];
      a6.push({ minCol: Math.floor((e4.xmin - l5 + 0.1 * o7) / t5 / o7), maxCol: Math.floor((e4.xmax - l5 - 0.1 * o7) / t5 / o7), minRow: Math.floor((c10 - e4.ymax + 0.1 * n10) / i4 / n10), maxRow: Math.floor((c10 - e4.ymin - 0.1 * n10) / i4 / n10) });
    }
    return a6;
  }
  getPyramidPixelSize(e4) {
    const { nativePixelSize: t5 } = this.rasterInfo, { pyramidResolutions: i4, pyramidScalingFactor: o6 } = this.rasterInfo.storageInfo;
    if (0 === e4) return t5;
    if (r(i4) && i4.length) return i4[e4 - 1];
    const r5 = o6 ** e4;
    return { x: t5.x * r5, y: t5.y * r5 };
  }
  identifyPixelLocation(e4, t5, i4, o6) {
    const { spatialReference: r5, nativeExtent: s4, storageInfo: a6 } = this.rasterInfo, { maximumPyramidLevel: l5, origin: c10, transposeInfo: m9 } = a6, f8 = o6 && r(m9) ? m9.tileSize[0] : a6.blockWidth, h5 = o6 && r(m9) ? m9.tileSize[1] : a6.blockHeight, u5 = j4(e4, r5, i4);
    if (!s4.intersects(u5)) return null;
    if (t5 < 0 || t5 > l5) return null;
    const p8 = this.getPyramidPixelSize(t5), { x: d7, y: x4 } = p8, y4 = (c10.y - u5.y) / x4 / h5, g3 = (u5.x - c10.x) / d7 / f8, I4 = Math.min(h5 - 1, Math.floor((y4 - Math.floor(y4)) * h5)), R4 = Math.min(f8 - 1, Math.floor((g3 - Math.floor(g3)) * f8));
    return { pyramidLevel: t5, row: Math.floor(y4), col: Math.floor(g3), rowOffset: I4, colOffset: R4, blockWidth: f8, srcLocation: u5 };
  }
  getTileExtent(e4, t5, i4, o6, r5, n9) {
    const [s4, a6] = n9, l5 = o6.x + i4 * s4 * e4.x, c10 = l5 + s4 * e4.x, m9 = o6.y - t5 * a6 * e4.y, f8 = m9 - a6 * e4.y;
    return new w3({ xmin: l5, xmax: c10, ymin: f8, ymax: m9, spatialReference: r5 });
  }
  getBlockWidthHeight(e4) {
    return { blockWidth: e4 > 0 ? this.rasterInfo.storageInfo.pyramidBlockWidth : this.rasterInfo.storageInfo.blockWidth, blockHeight: e4 > 0 ? this.rasterInfo.storageInfo.pyramidBlockHeight : this.rasterInfo.storageInfo.blockHeight };
  }
  isBlockOutside(e4, t5, i4) {
    const o6 = this.rasterInfo.storageInfo.blockBoundary[e4];
    return !o6 || o6.maxRow < t5 || o6.maxCol < i4 || o6.minRow > t5 || o6.minCol > i4;
  }
  async _fetchPixels(e4, t5, i4, o6 = {}) {
    let r5 = Q(e4);
    if (r5 >= 2) return { extent: e4, pixelBlock: null };
    const s4 = this._getSourceDataInfo(e4, t5, i4, o6), { pyramidLevel: l5, pyramidResolution: c10, srcResolution: m9, srcExtent: f8, srcWidth: h5, srcHeight: u5 } = s4;
    if (0 === h5 || 0 === u5) return { extent: e4, srcExtent: f8, pixelBlock: null };
    const p8 = e(this.rasterInfo.transform), d7 = "gcs-shift" === (p8 == null ? void 0 : p8.type), x4 = r(U3(e4.spatialReference));
    !d7 && x4 || (r5 = Q(s4.srcExtent, d7));
    const y4 = this.rasterInfo.storageInfo, g3 = { x: Math.floor((f8.xmin - y4.origin.x) / c10.x + 0.1), y: Math.floor((y4.origin.y - f8.ymax) / c10.y + 0.1) }, I4 = await this._fetchRawTiles(l5, g3, { width: h5, height: u5, wrapCount: r5 }, o6);
    if (!I4) return { extent: e4, srcExtent: f8, pixelBlock: null };
    const R4 = l5 > 0 ? y4.pyramidBlockWidth : y4.blockWidth, w8 = l5 > 0 ? y4.pyramidBlockHeight : y4.blockHeight, k3 = R4 === h5 && w8 === u5 && g3.x % R4 == 0 && g3.y % w8 == 0, S6 = new w2({ x: (e4.xmax - e4.xmin) / t5, y: (e4.ymax - e4.ymin) / i4, spatialReference: e4.spatialReference }), v6 = !e4.spatialReference.equals(this.rasterInfo.spatialReference), { datumTransformation: T4 } = o6;
    if (!v6 && k3 && 1 === I4.pixelBlocks.length && R4 === t5 && w8 === i4 && m9.x === S6.x && m9.y === S6.y) return { extent: e4, srcExtent: f8, pixelBlock: I4.pixelBlocks[0] };
    const b4 = x4 && r(U3(f8.spatialReference)), B2 = o6.requestProjectedLocalDirections && this.rasterInfo.dataType.startsWith("vector");
    B2 && !this.rasterJobHandler && await T3();
    const P = this.rasterJobHandler ? await this.rasterJobHandler.getProjectionOffsetGrid({ projectedExtent: e4, srcBufferExtent: I4.extent, pixelSize: S6.toJSON(), datumTransformation: T4, rasterTransform: p8, hasWrapAround: r5 > 0 || b4, isAdaptive: false !== this.ioConfig.optimizeProjectionAccuracy, includeGCSGrid: B2 }, o6) : $2({ projectedExtent: e4, srcBufferExtent: I4.extent, pixelSize: S6, datumTransformation: T4, rasterTransform: p8, hasWrapAround: r5 > 0 || b4, isAdaptive: false, includeGCSGrid: B2 });
    let M;
    const W = !o6.requestRawData, E4 = { rows: P.spacing[0], cols: P.spacing[1] }, D4 = e(this._getRasterTileAlignmentInfo(l5, I4.extent.xmin)), { pixelBlocks: z3, mosaicSize: F3, isPartiallyFilled: O3 } = I4;
    let G = null;
    if (this.rasterJobHandler) {
      const e5 = await this.rasterJobHandler.mosaicAndTransform({ srcPixelBlocks: z3, srcMosaicSize: F3, destDimension: W ? { width: t5, height: i4 } : null, coefs: W ? P.coefficients : null, sampleSpacing: W ? E4 : null, projectDirections: B2, gcsGrid: B2 ? P.gcsGrid : null, isUV: "vector-uv" === this.rasterInfo.dataType, interpolation: o6.interpolation, alignmentInfo: D4, blockWidths: null }, o6);
      ({ pixelBlock: M, localNorthDirections: G } = e5);
    } else {
      const e5 = b2(z3, F3, { alignmentInfo: D4 });
      M = W ? F(e5, { width: t5, height: i4 }, P.coefficients, E4, o6.interpolation) : e5, B2 && P.gcsGrid && (G = D({ width: t5, height: i4 }, P.gcsGrid), M = m3(M, this.rasterInfo.dataType, G));
    }
    return o6.requestRawData || B2 ? { srcExtent: f8, pixelBlock: M, transformGrid: P, localNorthDirections: G, extent: e4, isPartiallyFilled: O3 } : { srcExtent: f8, extent: e4, pixelBlock: M };
  }
  async _fetchRawTiles(e4, t5, i4, o6) {
    const { origin: r5, blockBoundary: s4 } = this.rasterInfo.storageInfo, { blockWidth: a6, blockHeight: l5 } = this.getBlockWidthHeight(e4);
    let { x: c10, y: m9 } = t5, { width: f8, height: h5, wrapCount: u5 } = i4;
    const p8 = this._getRasterTileAlignmentInfo(e4, 0);
    o6.buffer && (c10 -= o6.buffer.cols, m9 -= o6.buffer.rows, f8 += 2 * o6.buffer.cols, h5 += 2 * o6.buffer.rows);
    let d7 = 0, x4 = 0, y4 = 0;
    if (u5 && r(p8)) {
      ({ worldColumnCountFromOrigin: x4, originColumnOffset: y4, rightPadding: d7 } = p8);
      x4 * p8.blockWidth - d7 >= c10 + f8 && (d7 = 0);
    }
    const g3 = Math.floor(c10 / a6), I4 = Math.floor(m9 / l5), R4 = Math.floor((c10 + f8 + d7 - 1) / a6), w8 = Math.floor((m9 + h5 + d7 - 1) / l5), k3 = s4[e4];
    if (!k3) return null;
    const { minRow: S6, minCol: v6, maxCol: T4, maxRow: b4 } = k3;
    if (0 === u5 && (w8 < S6 || R4 < v6 || I4 > b4 || g3 > T4)) return null;
    const B2 = new Array();
    let P = false;
    const M = null == this.ioConfig.allowPartialFill ? o6.allowPartialFill : this.ioConfig.allowPartialFill;
    for (let E4 = I4; E4 <= w8; E4++) for (let t6 = g3; t6 <= R4; t6++) {
      let i5 = t6;
      if (!o6.disableWrapAround && u5 && r(p8) && x4 <= t6 && (i5 = t6 - x4 - y4), E4 >= S6 && i5 >= v6 && b4 >= E4 && T4 >= i5) {
        const t7 = this._fetchRawTile(e4, E4, i5, o6);
        M ? B2.push(new Promise((e5) => {
          t7.then((t8) => e5(t8)).catch(() => {
            P = true, e5(null);
          });
        })) : B2.push(t7);
      } else B2.push(Promise.resolve(null));
    }
    if (0 === B2.length) return null;
    const W = await Promise.all(B2), _2 = { height: (w8 - I4 + 1) * l5, width: (R4 - g3 + 1) * a6 }, { spatialReference: C4 } = this.rasterInfo, j5 = this.getPyramidPixelSize(e4), { x: H, y: L4 } = j5;
    return { extent: new w3({ xmin: r5.x + g3 * a6 * H, xmax: r5.x + (R4 + 1) * a6 * H, ymin: r5.y - (w8 + 1) * l5 * L4, ymax: r5.y - I4 * l5 * L4, spatialReference: C4 }), pixelBlocks: W, mosaicSize: _2, isPartiallyFilled: P };
  }
  _fetchRawTile(e4, t5, i4, o6) {
    const r5 = this.rasterInfo.storageInfo.blockBoundary[e4];
    if (!r5) return Promise.resolve(null);
    const { minRow: n9, minCol: a6, maxCol: l5, maxRow: c10 } = r5;
    if (t5 < n9 || i4 < a6 || t5 > c10 || i4 > l5) return Promise.resolve(null);
    const f8 = a5(this.url, o6.sliceId), h5 = `${e4}/${t5}/${i4}`;
    let u5 = x2(f8, o6.registryId, h5);
    if (t(u5)) {
      const r6 = new AbortController();
      u5 = this.fetchRawTile(e4, t5, i4, { ...o6, signal: r6.signal }), h2(f8, o6.registryId, h5, u5, r6), u5.catch(() => d4(f8, o6.registryId, h5));
    }
    return o6.signal && v2(o6, () => {
      m6(f8, o6.registryId, h5);
    }), u5;
  }
  _computeMagDirValues(e4) {
    var _a;
    const { bandCount: t5, dataType: i4 } = this.rasterInfo;
    if (!(2 === t5 && "vector-magdir" === i4 || "vector-uv" === i4) || 2 !== (e4 == null ? void 0 : e4.length) || !((_a = e4[0]) == null ? void 0 : _a.length)) return null;
    const o6 = e4[0].length;
    if ("vector-magdir" === i4) {
      const t6 = e4[1].map((e5) => (e5 + 360) % 360);
      return [e4[0], t6];
    }
    const [r5, n9] = e4, s4 = [], a6 = [];
    for (let l5 = 0; l5 < o6; l5++) {
      const [e5, t6] = f3([r5[l5], n9[l5]]);
      s4.push(e5), a6.push(t6);
    }
    return [s4, a6];
  }
  _getRasterTileAlignmentInfo(e4, t5) {
    return null == this._rasterTileAlighmentInfo && (this._rasterTileAlighmentInfo = V2(this.rasterInfo)), t(this._rasterTileAlighmentInfo.pyramidsInfo) ? null : { startX: t5, halfWorldWidth: this._rasterTileAlighmentInfo.halfWorldWidth, hasGCSSShiftTransform: this._rasterTileAlighmentInfo.hasGCSSShiftTransform, ...this._rasterTileAlighmentInfo.pyramidsInfo[e4] };
  }
  _getSourceDataInfo(e4, t5, i4, o6 = {}) {
    const r5 = { datumTransformation: o6.datumTransformation, pyramidLevel: 0, pyramidResolution: null, srcExtent: null, srcHeight: 0, srcResolution: null, srcWidth: 0 };
    o6.srcResolution && (r5.srcResolution = o6.srcResolution, this._updateSourceDataInfo(e4, r5));
    const n9 = this.rasterInfo.storageInfo.maximumPyramidLevel || 0, { srcWidth: s4, srcHeight: a6, pyramidLevel: l5 } = r5, c10 = s4 / t5, m9 = a6 / i4, f8 = l5 < n9 && c10 * m9 >= 16, h5 = l5 === n9 && this._requireTooManySrcTiles(s4, a6, t5, i4);
    if (f8 || h5 || (0 === s4 || 0 === a6)) {
      const s5 = new w2({ x: (e4.xmax - e4.xmin) / t5, y: (e4.ymax - e4.ymin) / i4, spatialReference: e4.spatialReference });
      let a7 = C(s5, this.rasterInfo.spatialReference, e4, r5.datumTransformation);
      const h6 = !a7 || o6.srcResolution && a7.x + a7.y < o6.srcResolution.x + o6.srcResolution.y;
      if (f8 && o6.srcResolution && h6) {
        const e5 = Math.round(Math.log(Math.max(c10, m9)) / Math.LN2) - 1;
        if (n9 - l5 + 3 >= e5) {
          const t6 = 2 ** e5;
          a7 = { x: o6.srcResolution.x * t6, y: o6.srcResolution.y * t6 };
        }
      }
      a7 && (r5.srcResolution = a7, this._updateSourceDataInfo(e4, r5));
    }
    return this._requireTooManySrcTiles(r5.srcWidth, r5.srcHeight, t5, i4) && (r5.srcWidth = 0, r5.srcHeight = 0), r5;
  }
  _requireTooManySrcTiles(e4, t5, i4, o6) {
    const { tileInfo: r5 } = this.rasterInfo.storageInfo;
    return Math.ceil(e4 / r5.size[0]) * Math.ceil(t5 / r5.size[1]) >= X || e4 / i4 > $3 || t5 / o6 > $3;
  }
  _updateSourceDataInfo(e4, t5) {
    t5.srcWidth = 0, t5.srcHeight = 0;
    const i4 = this.rasterInfo.spatialReference, { srcResolution: o6, datumTransformation: r5 } = t5, { pyramidLevel: n9, pyramidResolution: s4, excessiveReading: l5 } = oe(o6, this.rasterInfo, this.ioConfig.sampling);
    if (l5) return;
    let c10 = t5.srcExtent || J(e4, i4, r5);
    if (null == c10) return;
    const m9 = e(this.rasterInfo.transform);
    m9 && (c10 = m9.inverseTransform(c10)), t5.srcExtent = c10;
    const f8 = Math.ceil((c10.xmax - c10.xmin) / s4.x - 0.1), h5 = Math.ceil((c10.ymax - c10.ymin) / s4.y - 0.1);
    t5.pyramidLevel = n9, t5.pyramidResolution = s4, t5.srcWidth = f8, t5.srcHeight = h5;
  }
  _getRequestOptionsWithSliceId(e4) {
    return r(this.rasterInfo.multidimensionalInfo) && null == e4.sliceId && (e4 = { ...e4, sliceId: this.getSliceIndex(e4.multidimensionalDefinition) }), e4;
  }
  _processIdentifyResult(e4, t5) {
    const { srcLocation: i4, position: o6, pyramidLevel: r5, useTransposedTile: l5 } = t5, c10 = e4.pixels[0].length / e4.width / e4.height;
    if (!(!e4.mask || e4.mask[o6])) return { location: i4, value: null };
    const { multidimensionalInfo: m9 } = this.rasterInfo;
    if (t(m9) || !l5) {
      const t6 = e4.pixels.map((e5) => e5[o6]), n9 = { location: i4, value: t6, pyramidLevel: r5 }, s4 = this._computeMagDirValues(t6.map((e5) => [e5]));
      return (s4 == null ? void 0 : s4.length) && (n9.magdirValue = s4.map((e5) => e5[0])), n9;
    }
    let f8 = e4.pixels.map((e5) => e5.slice(o6 * c10, o6 * c10 + c10)), h5 = this._computeMagDirValues(f8);
    const { requestSomeSlices: u5, identifyOptions: p8 } = t5;
    let d7 = a4(m9, p8.transposedVariableName);
    if (u5) {
      const e5 = s3(d7, e(p8.multidimensionalDefinition), e(p8.timeExtent));
      f8 = f8.map((t6) => e5.map((e6) => t6[e6])), h5 = h5 == null ? void 0 : h5.map((t6) => e5.map((e6) => t6[e6])), d7 = e5.map((e6) => d7[e6]);
    }
    const y4 = e4.noDataValues || this.rasterInfo.noDataValue, g3 = { pixels: f8, pixelType: e4.pixelType };
    let I4;
    r(y4) && (u(g3, y4), I4 = g3.mask);
    return { location: i4, value: null, dataSeries: d7.map((e5, t6) => {
      const i5 = { value: 0 === (I4 == null ? void 0 : I4[t6]) ? null : f8.map((e6) => e6[t6]), multidimensionalDefinition: e5.multidimensionalDefinition.map((e6) => new p5({ ...e6, isSlice: true })) };
      return (h5 == null ? void 0 : h5.length) && (i5.magdirValue = [h5[0][t6], h5[1][t6]]), i5;
    }), pyramidLevel: r5 };
  }
};
e2([y()], K.prototype, "_rasterTileAlighmentInfo", void 0), e2([y({ readOnly: true })], K.prototype, "_isGlobalWrappableSource", null), e2([y(f2)], K.prototype, "url", null), e2([y({ type: String, json: { write: true } })], K.prototype, "datasetName", void 0), e2([y({ type: String, json: { write: true } })], K.prototype, "datasetFormat", void 0), e2([y()], K.prototype, "hasUniqueSourceStorageInfo", void 0), e2([y()], K.prototype, "rasterInfo", void 0), e2([y()], K.prototype, "ioConfig", void 0), e2([y()], K.prototype, "sourceJSON", void 0), K = e2([a("esri.layers.support.rasterDatasets.BaseRaster")], K);
var Q2 = K;

// node_modules/@arcgis/core/layers/support/rasterDatasets/FunctionRaster.js
var n6 = class extends Q2 {
  constructor() {
    super(...arguments), this.datasetFormat = "Function", this.tileType = "Raster", this.rasterFunction = null;
  }
  async open(r5) {
    var _a, _b;
    await this.init();
    const { rasterFunction: e4 } = this;
    ((_b = (_a = this.primaryRasters) == null ? void 0 : _a.rasters) == null ? void 0 : _b.length) ? e4.sourceRasters = this.primaryRasters.rasters : this.primaryRasters = e4.getPrimaryRasters();
    const { rasters: s4, rasterIds: a6 } = this.primaryRasters, o6 = s4.map((t5) => t5.rasterInfo ? void 0 : t5.open(r5));
    await Promise.all(o6);
    const i4 = s4.map(({ rasterInfo: r6 }) => r6), n9 = e4.bind({ rasterInfos: i4, rasterIds: a6 });
    if (!n9.success || 0 === i4.length) throw new s2("raster-function:open", `cannot bind the function: ${n9.error ?? ""}`);
    await this.syncJobHandler();
    const c10 = i4[0];
    this.hasUniqueSourceStorageInfo = 1 === i4.length || i4.slice(1).every((r6) => this._hasSameStorageInfo(r6, c10)), this.set("sourceJSON", s4[0].sourceJSON), this.set("rasterInfo", e4.rasterInfo);
  }
  async syncJobHandler() {
    var _a;
    return (_a = this.rasterJobHandler) == null ? void 0 : _a.updateRasterFunction(this.rasterFunction);
  }
  async fetchPixels(r5, t5, a6, o6 = {}) {
    var _a;
    const { rasters: i4, rasterIds: n9 } = this.primaryRasters, c10 = i4.map((e4) => e4.fetchPixels(r5, t5, a6, o6)), p8 = await Promise.all(c10), l5 = p8.map((r6) => r6.pixelBlock);
    if (o6.skipRasterFunction || l5.every((r6) => t(r6))) return p8[0];
    const m9 = ((_a = p8.find((r6) => r(r6.pixelBlock))) == null ? void 0 : _a.extent) ?? r5, u5 = this.rasterJobHandler ? await this.rasterJobHandler.process({ extent: m9, primaryPixelBlocks: l5, primaryRasterIds: n9 }) : this.rasterFunction.process({ extent: m9, primaryPixelBlocks: l5, primaryRasterIds: n9 });
    return { ...p8[0], pixelBlock: u5 };
  }
  _hasSameStorageInfo(r5, t5) {
    const { storageInfo: e4, pixelSize: s4, spatialReference: a6, extent: o6 } = r5, { storageInfo: i4, pixelSize: n9, spatialReference: c10, extent: p8 } = t5;
    return s4.x === n9.x && s4.y === n9.y && a6.equals(c10) && o6.equals(p8) && e4.blockHeight === i4.blockHeight && e4.blockWidth === i4.blockWidth && e4.maximumPyramidLevel === i4.maximumPyramidLevel;
  }
};
e2([y({ type: String, json: { write: true } })], n6.prototype, "datasetFormat", void 0), e2([y()], n6.prototype, "tileType", void 0), e2([y()], n6.prototype, "rasterFunction", void 0), e2([y()], n6.prototype, "primaryRasters", void 0), n6 = e2([a("esri.layers.support.rasterDatasets.FunctionRaster")], n6);
var c7 = n6;

// node_modules/@arcgis/core/layers/mixins/ImageryTileMixin.js
var O2 = s.getLogger("esri.layers.mixins.ImageryTileMixin");
var z2 = (s4) => {
  let z3 = class extends s4 {
    constructor(...t5) {
      var _a, _b;
      super(...t5), this._isConstructedFromFunctionRaster = false, this._rasterJobHandler = { instance: null, refCount: 0, connectionPromise: null }, this.bandIds = null, this.copyright = null, this.interpolation = "nearest", this.multidimensionalSubset = null, this.raster = null, this.rasterFunction = null, this.rasterInfo = null, this.sourceJSON = null, this.spatialReference = null, this.symbolizer = null, this._isConstructedFromFunctionRaster = "Function" === ((_b = (_a = t5[0]) == null ? void 0 : _a.raster) == null ? void 0 : _b.datasetFormat);
    }
    get fullExtent() {
      var _a;
      return (_a = this.rasterInfo) == null ? void 0 : _a.extent;
    }
    set multidimensionalDefinition(t5) {
      this._set("multidimensionalDefinition", t5), this.updateRenderer();
    }
    get tileInfo() {
      var _a;
      return (_a = this.rasterInfo) == null ? void 0 : _a.storageInfo.tileInfo;
    }
    set url(t5) {
      this._set("url", S(t5, O2));
    }
    set renderer(t5) {
      this._set("renderer", t5), this.updateRenderer();
    }
    async convertVectorFieldData(t5, e4) {
      if (t(t5) || !this.rasterInfo) return null;
      const r5 = this._rasterJobHandler.instance, i4 = this.rasterInfo.dataType;
      return r5 ? r5.convertVectorFieldData({ pixelBlock: t5, dataType: i4 }, e4) : d(t5, i4);
    }
    async createFlowMesh(t5, e4) {
      const r5 = this._rasterJobHandler.instance;
      return r5 ? r5.createFlowMesh(t5, e4) : f4(t5.meshType, t5.simulationSettings, t5.flowData, r(e4.signal) ? e4.signal : new AbortController().signal);
    }
    normalizeRasterFetchOptions(t5) {
      var _a, _b;
      const { multidimensionalInfo: e4 } = this.rasterInfo ?? {};
      if (t(e4)) return t5;
      let r5 = t5.multidimensionalDefinition || this.multidimensionalDefinition;
      !t(r5) && r5.length || (r5 = h(this.raster.rasterInfo, { multidimensionalSubset: this.multidimensionalSubset }));
      const i4 = t5.timeExtent || this.timeExtent;
      if (r(r5) && r(i4) && (r(i4.start) || r(i4.end))) {
        r5 = r5.map((t6) => t6.clone());
        const s5 = (_b = (_a = e4.variables.find(({ name: t6 }) => t6 === r5[0].variableName)) == null ? void 0 : _a.dimensions) == null ? void 0 : _b.find(({ name: t6 }) => "StdTime" === t6), a6 = r5.find(({ dimensionName: t6 }) => "StdTime" === t6);
        if (!s5 || !a6) return { ...t5, multidimensionalDefinition: null };
        const { start: l5, end: u5 } = i4, m9 = t(l5) ? null : l5.getTime(), d7 = t(u5) ? null : u5.getTime(), c10 = m9 ?? d7, h5 = d7 ?? m9;
        if (r(s5.values)) {
          const t6 = s5.values.filter((t7) => {
            if (Array.isArray(t7)) {
              if (c10 === h5) return t7[0] <= c10 && t7[1] >= c10;
              const e5 = t7[0] <= c10 && t7[1] > c10 || t7[0] < h5 && t7[1] >= h5, r6 = t7[0] >= c10 && t7[1] <= h5 || t7[0] < c10 && t7[1] > h5;
              return e5 || r6;
            }
            return c10 === h5 ? t7 === c10 : t7 >= c10 && t7 <= h5;
          });
          if (t6.length) {
            const e5 = t6.sort((t7, e6) => {
              if (c10 === h5) return (t7[0] ?? t7) - (e6[0] ?? e6);
              return Math.abs((t7[1] ?? t7) - h5) - Math.abs((e6[1] ?? e6) - h5);
            })[0];
            a6.values = [e5];
          } else r5 = null;
        } else if (s5.hasRegularIntervals && s5.extent) {
          const [t6, e5] = s5.extent;
          c10 > e5 || h5 < t6 ? r5 = null : a6.values = c10 === h5 ? [c10] : [Math.max(t6, c10), Math.min(e5, h5)];
        }
      }
      return r(r5) && m4(r5, this.multidimensionalSubset) ? { ...t5, multidimensionalDefinition: null } : { ...t5, multidimensionalDefinition: r5 };
    }
    async updateRasterFunction() {
      var _a, _b;
      if ("imagery-tile" !== this.type || !this.rasterFunction && !this._cachedRasterFunctionJson || JSON.stringify(this.rasterFunction) === JSON.stringify(this._cachedRasterFunctionJson)) return;
      if (this._isConstructedFromFunctionRaster && "Function" === this.raster.datasetFormat) {
        const t6 = this.raster.rasterFunction.toJSON();
        return !this.rasterFunction && t6 && this._set("rasterFunction", w4.fromJSON(t6)), void (this._cachedRasterFunctionJson = (_a = this.rasterFunction) == null ? void 0 : _a.toJSON());
      }
      let t5, e4 = this.raster, r5 = false;
      "Function" === e4.datasetFormat ? (t5 = e4.primaryRasters.rasters, e4 = t5[0], r5 = true) : t5 = [e4];
      const { rasterFunction: i4 } = this;
      if (i4) {
        const r6 = { raster: e4 };
        t5.length > 1 && t5.forEach((t6) => r6[t6.url] = t6);
        const s6 = C2(i4.rasterFunctionDefinition ?? i4.toJSON(), r6), n10 = new c7({ rasterFunction: s6 });
        n10.rasterJobHandler = this._rasterJobHandler.instance, await n10.open(), this._cachedRasterFunctionJson = (_b = this.rasterFunction) == null ? void 0 : _b.toJSON(), this.raster = n10;
      } else this.raster = e4, this._cachedRasterFunctionJson = null;
      if (this._cachedRendererJson = null, !r5 && !i4) return;
      const { bandIds: s5 } = this, { bandCount: n9 } = this.raster.rasterInfo, o6 = (s5 == null ? void 0 : s5.length) ? s5.some((t6) => t6 >= n9) : n9 >= 3;
      s5 && (o6 || "raster-stretch" !== this.renderer.type) && this._set("bandIds", null), this._configDefaultRenderer("auto");
    }
    async updateRenderer() {
      var _a;
      const { loaded: t5, symbolizer: e4 } = this;
      if (!t5 || !e4) return;
      const { rasterInfo: r5 } = this.raster, i4 = (_a = d3(r5, { multidimensionalDefinition: this.multidimensionalDefinition, multidimensionalSubset: this.multidimensionalSubset })) == null ? void 0 : _a.name, s5 = $({ ...this.renderer.toJSON(), variableName: i4 });
      if (JSON.stringify(this._cachedRendererJson) === JSON.stringify(s5)) return;
      const n9 = this._rasterJobHandler.instance;
      n9 && (e4.rasterInfo = S4(r5, i4), e4.rendererJSON = s5, e4.bind(), await n9.updateSymbolizer(e4), this._cachedRendererJson = s5);
    }
    async applyRenderer(t5, e4) {
      const r5 = t5 && t5.pixelBlock;
      if (!(r(r5) && r5.pixels && r5.pixels.length > 0)) return null;
      let i4;
      await this.updateRenderer();
      const s5 = this._rasterJobHandler.instance, n9 = this.bandIds ?? [];
      return i4 = s5 ? await s5.symbolize({ ...t5, simpleStretchParams: e4, bandIds: n9 }) : this.symbolizer.symbolize({ ...t5, simpleStretchParams: e4, bandIds: n9 }), i4;
    }
    getTileUrl(t5, e4, r5) {
      return "RasterTileServer" === this.raster.datasetFormat ? `${this.url}/tile/${t5}/${e4}/${r5}` : "";
    }
    getCompatibleTileInfo(t5, e4, r5 = false) {
      if (!this.loaded || t(e4)) return null;
      if (r5 && t5.equals(this.spatialReference)) return this.tileInfo;
      const i4 = R(t5);
      return j.create({ size: 256, spatialReference: t5, origin: i4 ? { x: i4.origin[0], y: i4.origin[1] } : { x: e4.xmin, y: e4.ymax } });
    }
    getCompatibleFullExtent(t5) {
      return this.loaded ? (this._compatibleFullExtent && this._compatibleFullExtent.spatialReference.equals(t5) || (this._compatibleFullExtent = this.raster.computeExtent(t5)), this._compatibleFullExtent) : null;
    }
    async fetchTile(t5, e4, i4, s5 = {}) {
      if (C4(this), s5.requestAsImageElement) {
        const n9 = this.getTileUrl(t5, e4, i4);
        return U(n9, { responseType: "image", query: { ...this.refreshParameters, ...this.raster.ioConfig.customFetchParameters }, signal: s5.signal }).then((t6) => t6.data);
      }
      const { rasterInfo: a6 } = this;
      if (r(a6.multidimensionalInfo) && (s5 = this.normalizeRasterFetchOptions(s5), t(s5.multidimensionalDefinition))) {
        const r5 = s5.tileInfo || a6.storageInfo.tileInfo;
        return { extent: this.raster.getTileExtentFromTileInfo(t5, e4, i4, r5), pixelBlock: null };
      }
      return await this._initJobHandler(), await this.updateRasterFunction(), "raster-shaded-relief" === this.renderer.type && (s5 = { ...s5, buffer: { cols: 1, rows: 1 } }), this.raster.fetchTile(t5, e4, i4, s5);
    }
    async fetchPixels(t5, e4, r5, i4 = {}) {
      return r(this.rasterInfo.multidimensionalInfo) && (i4 = this.normalizeRasterFetchOptions(i4), t(i4.multidimensionalDefinition)) ? { extent: t5, pixelBlock: null } : (await this._initJobHandler(), await this.updateRasterFunction(), this.raster.fetchPixels(t5, e4, r5, i4));
    }
    async identify(t5, e4 = {}) {
      var _a;
      const { raster: r5, rasterInfo: s5 } = this;
      if (r(s5.multidimensionalInfo)) {
        if (!(s5.hasMultidimensionalTranspose && !!(g(e4.multidimensionalDefinition) || e4.transposedVariableName || e4.timeExtent)) && (e4 = this.normalizeRasterFetchOptions(e4), t(e4.multidimensionalDefinition))) return { location: t5, value: null };
      }
      const a6 = (_a = this.multidimensionalSubset) == null ? void 0 : _a.areaOfInterest;
      if (a6 && !a6.contains(t5)) throw new s2("imagery-tile-mixin:identify", "the request cannot be fulfilled when falling outside of the multidimensional subset");
      return r5.identify(t5, e4);
    }
    increaseRasterJobHandlerUsage() {
      this._rasterJobHandler.refCount++;
    }
    decreaseRasterJobHandlerUsage() {
      this._rasterJobHandler.refCount--, this._rasterJobHandler.refCount <= 0 && this._shutdownJobHandler();
    }
    hasStandardTime() {
      var _a, _b, _c;
      const t5 = (_a = this.rasterInfo) == null ? void 0 : _a.multidimensionalInfo;
      if (t(t5) || "standard-time" !== ((_b = this.rasterInfo) == null ? void 0 : _b.dataType)) return false;
      const e4 = this.multidimensionalDefinition, r5 = (_c = e4 == null ? void 0 : e4[0]) == null ? void 0 : _c.variableName;
      return t5.variables.some((t6) => t6.name === r5 && (!(e4 == null ? void 0 : e4[0].dimensionName) || t6.dimensions.some((t7) => "StdTime" === t7.name)));
    }
    getStandardTimeValue(t5) {
      return new Date(24 * (t5 - 25569) * 3600 * 1e3).toString();
    }
    getMultidimensionalSubsetVariables(t5) {
      var _a;
      const e4 = t5 ?? ((_a = this.rasterInfo) == null ? void 0 : _a.multidimensionalInfo);
      return v3(this.multidimensionalSubset, e4);
    }
    _configDefaultSettings() {
      this._configDefaultInterpolation(), this.multidimensionalDefinition || (this.multidimensionalDefinition = h(this.raster.rasterInfo, { multidimensionalSubset: this.multidimensionalSubset })), this._configDefaultRenderer();
    }
    _initJobHandler() {
      if (null != this._rasterJobHandler.connectionPromise) return this._rasterJobHandler.connectionPromise;
      const t5 = new n4();
      return this._rasterJobHandler.connectionPromise = t5.initialize().then(() => {
        C4(this), this._rasterJobHandler.instance = t5, this.raster.rasterJobHandler = t5, this.renderer && this.updateRenderer(), "Function" === this.raster.datasetFormat && this.raster.syncJobHandler();
      }).catch(() => {
      }), this._rasterJobHandler.connectionPromise;
    }
    _shutdownJobHandler() {
      this._rasterJobHandler.instance && this._rasterJobHandler.instance.destroy(), this._rasterJobHandler.instance = null, this._rasterJobHandler.connectionPromise = null, this._rasterJobHandler.refCount = 0, this._cachedRendererJson = null, this.raster && (this.raster.rasterJobHandler = null);
    }
    _configDefaultInterpolation() {
      var _a;
      if (null == this.interpolation) {
        C4(this);
        const { raster: t5 } = this, e4 = V(t5.rasterInfo, t5.tileType, (_a = this.sourceJSON) == null ? void 0 : _a.defaultResamplingMethod);
        this._set("interpolation", e4);
      }
    }
    _configDefaultRenderer(t5 = "no") {
      var _a, _b, _c, _d, _e;
      C4(this);
      const { rasterInfo: e4 } = this.raster;
      !this.bandIds && e4.bandCount > 1 && (this.bandIds = L3(e4));
      const r5 = (_a = d3(e4, { multidimensionalDefinition: this.multidimensionalDefinition, multidimensionalSubset: this.multidimensionalSubset })) == null ? void 0 : _a.name;
      if (!this.renderer || "override" === t5) {
        const t6 = j3(e4, { bandIds: this.bandIds, variableName: r5 });
        "WCSServer" === this.raster.datasetFormat && "raster-stretch" === t6.type && ((((_b = e4.statistics) == null ? void 0 : _b[0].max) ?? 0) > 1e24 || (((_c = e4.statistics) == null ? void 0 : _c[0].min) ?? 0) < -1e24) && (t6.dynamicRangeAdjustment = true, t6.statistics = null, "none" === t6.stretchType && (t6.stretchType = "min-max")), this.renderer = t6;
      }
      const i4 = $({ ...this.renderer.toJSON(), variableName: r5 }), s5 = S4(e4, r5);
      this.symbolizer ? (this.symbolizer.rendererJSON = i4, this.symbolizer.rasterInfo = s5) : this.symbolizer = new T2({ rendererJSON: i4, rasterInfo: s5 });
      const n9 = this.symbolizer.bind();
      if (n9.success) {
        if ("auto" === t5) {
          const { colormap: t6 } = this.raster.rasterInfo, e5 = this.renderer;
          if (r(t6)) if ("raster-colormap" !== e5.type) this._configDefaultRenderer("override");
          else {
            const t7 = j3(this.raster.rasterInfo);
            JSON.stringify(t7) !== JSON.stringify(e5) && this._configDefaultRenderer("override");
          }
          else if ("raster-stretch" === e5.type) {
            const t7 = (_d = this.bandIds) == null ? void 0 : _d.length, r6 = (_e = e5.statistics) == null ? void 0 : _e.length;
            !e5.dynamicRangeAdjustment && r6 && t7 && r6 !== t7 && this._configDefaultRenderer("override");
          }
        }
      } else O2.warn("imagery-tile-mixin", n9.error || "The given renderer is not supported by the layer."), "auto" === t5 && this._configDefaultRenderer("override");
    }
  };
  function C4(t5) {
    if (!t5.raster || !t5.rasterInfo) throw new s2("imagery-tile", "no raster");
  }
  return e2([y()], z3.prototype, "_cachedRendererJson", void 0), e2([y()], z3.prototype, "_cachedRasterFunctionJson", void 0), e2([y()], z3.prototype, "_compatibleFullExtent", void 0), e2([y()], z3.prototype, "_isConstructedFromFunctionRaster", void 0), e2([y()], z3.prototype, "_rasterJobHandler", void 0), e2([y()], z3.prototype, "bandIds", void 0), e2([y({ json: { origins: { service: { read: { source: "copyrightText" } } } } })], z3.prototype, "copyright", void 0), e2([y({ json: { read: false } })], z3.prototype, "fullExtent", null), e2([y()], z3.prototype, "interpolation", void 0), e2([y()], z3.prototype, "ioConfig", void 0), e2([y({ type: [p5], json: { write: true } })], z3.prototype, "multidimensionalDefinition", null), e2([y({ type: c5, json: { write: true } })], z3.prototype, "multidimensionalSubset", void 0), e2([y()], z3.prototype, "raster", void 0), e2([y({ type: w4 })], z3.prototype, "rasterFunction", void 0), e2([y()], z3.prototype, "rasterInfo", void 0), e2([y()], z3.prototype, "sourceJSON", void 0), e2([y({ readOnly: true, type: f, json: { read: false } })], z3.prototype, "spatialReference", void 0), e2([y({ json: { read: false } })], z3.prototype, "tileInfo", null), e2([y(f2)], z3.prototype, "url", null), e2([y({ types: l3 })], z3.prototype, "renderer", null), e2([y()], z3.prototype, "symbolizer", void 0), z3 = e2([a("esri.layers.ImageryTileMixin")], z3), z3;
};

// node_modules/@arcgis/core/layers/support/rasterDatasets/DBFParser.js
function t3(e4) {
  const t5 = e4.fields, r5 = e4.records, n9 = t5.some((e5) => "oid" === e5.name.toLowerCase()) ? "OBJECTID" : "OID", i4 = [{ name: n9, type: "esriFieldTypeOID", alias: "OID" }].concat(t5.map((e5) => ({ name: e5.name, type: "esriFieldType" + e5.typeName, alias: e5.name }))), s4 = i4.map((e5) => e5.name), a6 = [];
  let o6 = 0, l5 = 0;
  return r5.forEach((e5) => {
    const t6 = {};
    for (t6[n9] = o6++, l5 = 1; l5 < s4.length; l5++) t6[s4[l5]] = e5[l5 - 1];
    a6.push({ attributes: t6 });
  }), { displayFieldName: "", fields: i4, features: a6 };
}
var r4 = class {
  static get supportedVersions() {
    return [5];
  }
  static parse(r5) {
    const n9 = new DataView(r5), i4 = 3 & n9.getUint8(0);
    if (3 !== i4) return { header: { version: i4 }, recordSet: null };
    const s4 = n9.getUint32(4, true), a6 = n9.getUint16(8, true), o6 = n9.getUint16(10, true), l5 = { version: i4, recordCount: s4, headerByteCount: a6, recordByteCount: o6 };
    let p8 = 32;
    const g3 = [], u5 = [];
    let d7;
    if (3 === i4) {
      for (; 13 !== n9.getUint8(p8); ) d7 = String.fromCharCode(n9.getUint8(p8 + 11)).trim(), g3.push({ name: r2(new Uint8Array(r5, p8, 11)), type: d7, typeName: ["String", "Date", "Double", "Boolean", "String", "Integer"][["C", "D", "F", "L", "M", "N"].indexOf(d7)], length: n9.getUint8(p8 + 16) }), p8 += 32;
      if (p8 += 1, g3.length > 0) for (; u5.length < s4 && r5.byteLength - p8 > o6; ) {
        const t5 = [];
        32 === n9.getUint8(p8) ? (p8 += 1, g3.forEach((n10) => {
          if ("C" === n10.type) t5.push(r2(new Uint8Array(r5, p8, n10.length)).trim());
          else if ("N" === n10.type) t5.push(parseInt(String.fromCharCode.apply(null, new Uint8Array(r5, p8, n10.length)).trim(), 10));
          else if ("F" === n10.type) t5.push(parseFloat(String.fromCharCode.apply(null, new Uint8Array(r5, p8, n10.length)).trim()));
          else if ("D" === n10.type) {
            const e4 = String.fromCharCode.apply(null, new Uint8Array(r5, p8, n10.length)).trim();
            t5.push(new Date(parseInt(e4.substring(0, 4), 10), parseInt(e4.substring(4, 6), 10) - 1, parseInt(e4.substring(6, 8), 10)));
          }
          p8 += n10.length;
        }), u5.push(t5)) : p8 += o6;
      }
    }
    return { header: l5, fields: g3, records: u5, recordSet: t3({ fields: g3, records: u5 }) };
  }
};

// node_modules/@arcgis/core/layers/support/rasterDatasets/CloudRaster.js
var x3 = /* @__PURE__ */ new Map();
x3.set("int16", "esriFieldTypeSmallInteger"), x3.set("int32", "esriFieldTypeInteger"), x3.set("int64", "esriFieldTypeInteger"), x3.set("float32", "esriFieldTypeSingle"), x3.set("float64", "esriFieldTypeDouble"), x3.set("text", "esriFieldTypeString");
var S5 = 8;
var I = class extends Q2 {
  constructor() {
    super(...arguments), this.storageInfo = null, this.datasetFormat = "CRF";
  }
  async open(e4) {
    await this.init();
    const { data: r5 } = await this.request(this.url + "/conf.json", { signal: e4 == null ? void 0 : e4.signal });
    if (!this._validateHeader(r5)) throw new s2("cloudraster:open", "Invalid or unsupported conf.json.");
    this.datasetName = this.url.slice(this.url.lastIndexOf("/") + 1);
    const { storageInfo: o6, rasterInfo: i4 } = this._parseHeader(r5);
    if ("thematic" === i4.dataType) {
      const e5 = await this._fetchAuxiliaryInformation();
      i4.attributeTable = e5;
    }
    this._set("storageInfo", o6), this._set("rasterInfo", i4), this.ioConfig.retryCount = this.ioConfig.retryCount || 0;
  }
  async fetchRawTile(e4, t5, r5, o6 = {}) {
    const { transposeInfo: i4 } = this.rasterInfo.storageInfo, { transposedVariableName: s4 } = o6, a6 = !(!i4 || !s4), n9 = a6 ? 0 : this.rasterInfo.storageInfo.maximumPyramidLevel - e4;
    if (n9 < 0) return null;
    const l5 = this._buildCacheFilePath(n9, t5, r5, o6.multidimensionalDefinition, s4), f8 = this._getIndexRecordFromBundle(t5, r5, a6), m9 = await this.request(l5, { range: { from: 0, to: this.storageInfo.headerSize - 1 }, responseType: "array-buffer", signal: o6.signal });
    if (!m9) return null;
    const c10 = new Uint8Array(m9.data), p8 = this._getTileEndAndContentType(c10, f8);
    if (0 === p8.recordSize) return null;
    const d7 = await this.request(l5, { range: { from: p8.position, to: p8.position + p8.recordSize }, responseType: "array-buffer", signal: o6.signal });
    if (!d7) return null;
    const [u5, h5] = this._getTileSize(a6);
    return this.decodePixelBlock(d7.data, { width: u5, height: h5, planes: null, pixelType: null, returnInterleaved: a6 });
  }
  _validateHeader(e4) {
    const t5 = ["origin", "extent", "geodataXform", "LODInfos", "blockWidth", "blockHeight", "bandCount", "pixelType", "pixelSizeX", "pixelSizeY", "format", "packetSize"];
    return e4 && "RasterInfo" === e4.type && !t5.some((t6) => !e4[t6]);
  }
  _parseHeader(e4) {
    var _a;
    const t5 = ["u1", "u2", "u4", "u8", "s8", "u16", "s16", "u32", "s32", "f32", "f64"][e4.pixelType], { bandCount: r5, histograms: o6, colormap: i4, blockWidth: s4, blockHeight: m9, firstPyramidLevel: c10, maximumPyramidLevel: p8 } = e4, d7 = e4.statistics && e4.statistics.map((e5) => ({ min: e5.min, max: e5.max, avg: e5.mean, stddev: e5.standardDeviation, median: e5.median, mode: e5.mode })), u5 = e4.extent.spatialReference, x4 = (_a = e4.geodataXform) == null ? void 0 : _a.spatialReference, I4 = new f((u5 == null ? void 0 : u5.wkid) || (u5 == null ? void 0 : u5.wkt) ? u5 : x4);
    let w8 = new w3({ xmin: e4.extent.xmin, ymin: e4.extent.ymin, xmax: e4.extent.xmax, ymax: e4.extent.ymax, spatialReference: I4 });
    const _2 = new w2({ x: e4.pixelSizeX, y: e4.pixelSizeY, spatialReference: I4 }), v6 = Math.round((w8.xmax - w8.xmin) / _2.x), b4 = Math.round((w8.ymax - w8.ymin) / _2.y), z3 = this._parseTransform(e4.geodataXform), T4 = z3 ? w8 : null;
    z3 && (w8 = z3.forwardTransform(w8), _2.x = (w8.xmax - w8.xmin) / v6, _2.y = (w8.ymax - w8.ymin) / b4);
    const k3 = e4.properties ?? {}, j5 = e4.format.toLowerCase().replace("cache/", ""), C4 = new w2(e4.origin.x, e4.origin.y, I4);
    let R4, F3, P, H;
    if (i4 && i4.colors) for (R4 = [], F3 = 0; F3 < i4.colors.length; F3++) P = i4.colors[F3], H = i4.values ? i4.values[F3] : F3, R4.push([H, 255 & P, P << 16 >>> 24, P << 8 >>> 24, P >>> 24]);
    const D4 = e4.LODInfos, L4 = [];
    for (F3 = 0; F3 < D4.levels.length; F3++) L4.push(new p({ level: D4.levels[F3], resolution: D4.resolutions[F3], scale: 96 / 0.0254 * D4.resolutions[F3] }));
    const M = new j({ dpi: 96, lods: L4, format: j5, origin: C4, size: [s4, m9], spatialReference: I4 }), O3 = { recordSize: S5, packetSize: e4.packetSize, headerSize: e4.packetSize * e4.packetSize * S5 + 64 }, B2 = [{ maxCol: Math.ceil(v6 / s4) - 1, maxRow: Math.ceil(b4 / m9) - 1, minCol: 0, minRow: 0 }];
    let $4 = 2;
    if (p8 > 0) for (F3 = 0; F3 < p8; F3++) B2.push({ maxCol: Math.ceil(v6 / $4 / s4) - 1, maxRow: Math.ceil(b4 / $4 / m9) - 1, minCol: 0, minRow: 0 }), $4 *= 2;
    const N2 = e4.mdInfo;
    let q = null;
    if (N2 && k3._yxs) {
      const e5 = k3._yxs;
      q = { packetSize: e5.PacketSize, tileSize: [e5.TileXSize, e5.TileYSize] };
    }
    return { storageInfo: O3, rasterInfo: new u2({ width: v6, height: b4, pixelType: t5, bandCount: r5, extent: w8, nativeExtent: T4, transform: z3, spatialReference: I4, pixelSize: _2, keyProperties: k3, statistics: d7, histograms: o6, multidimensionalInfo: N2, colormap: R4, storageInfo: new n3({ blockWidth: s4, blockHeight: m9, pyramidBlockWidth: s4, pyramidBlockHeight: m9, origin: C4, tileInfo: M, transposeInfo: q, firstPyramidLevel: c10, maximumPyramidLevel: p8, blockBoundary: B2 }) }) };
  }
  _parseTransform(e4) {
    var _a, _b;
    if (!f5(e4)) throw new s2("cloudraster:open", "the data contains unsupported geodata transform types");
    const r5 = i2(e4);
    if ("identity" === r5.type) return null;
    if ("polynomial" !== r5.type || !((_a = r5.forwardCoefficients) == null ? void 0 : _a.length) || !((_b = r5.inverseCoefficients) == null ? void 0 : _b.length)) throw new s2("cloudraster:open", "the data contains unsupported geodata transforms - both forward and inverse coefficients are required currently");
    return r5;
  }
  async _fetchAuxiliaryInformation(e4) {
    const t5 = this.request(this.url + "/conf.vat.json", { signal: e4 }).then((e5) => e5.data).catch(() => null), r5 = this.request(this.url + "/conf.vat.dbf", { responseType: "array-buffer", signal: e4 }).then((e5) => e5.data).catch(() => null), o6 = await Promise.all([t5, r5]);
    let i4;
    if (o6[0]) {
      let e5 = o6[0].fields;
      const t6 = o6[0].values;
      if (e5 && t6) {
        e5 = e5.map((e6) => ({ type: "OID" === e6.name ? "esriFieldTypeOID" : x3.get(e6.type), name: e6.name, alias: e6.alias || e6.name }));
        const r6 = t6.map((e6) => ({ attributes: e6 }));
        e5 && t6 && (i4 = { fields: e5, features: r6 });
      }
    }
    if (!i4 && o6[1]) {
      i4 = r4.parse(o6[1]).recordSet;
    }
    return x.fromJSON(i4);
  }
  _buildCacheFilePath(e4, t5, o6, i4, s4) {
    const a6 = this._getPackageSize(!!s4), n9 = Math.floor(t5 / a6) * a6, l5 = Math.floor(o6 / a6) * a6, f8 = "R" + this._toHexString4(n9) + "C" + this._toHexString4(l5);
    let m9 = "L";
    m9 += e4 >= 10 ? e4.toString() : "0" + e4.toString();
    const { multidimensionalInfo: c10 } = this.rasterInfo, p8 = i4 == null ? void 0 : i4[0];
    if (t(c10) || !p8) return `${this.url}/_alllayers/${m9}/${f8}.bundle`;
    let d7 = "_yxs";
    if (!s4) {
      d7 = c10.variables.find((e6) => e6.name === p8.variableName).dimensions[0].values.indexOf(p8.values[0]).toString(16);
      const e5 = 4 - d7.length;
      for (let t6 = 0; t6 < e5; t6++) d7 = "0" + d7;
      d7 = "S" + d7;
    }
    const u5 = this._getVariableFolderName(s4 || p8.variableName);
    return `${this.url}/_alllayers/${u5}/${d7}/${m9}/${f8}.bundle`;
  }
  _getPackageSize(e4 = false) {
    const { transposeInfo: t5 } = this.rasterInfo.storageInfo;
    return e4 && r(t5) ? t5.packetSize ?? 0 : this.storageInfo.packetSize;
  }
  _getTileSize(e4 = false) {
    const { storageInfo: t5 } = this.rasterInfo, { transposeInfo: r5 } = t5;
    return e4 && r(r5) ? r5.tileSize : t5.tileInfo.size;
  }
  _getVariableFolderName(e4) {
    return "" === (e4 = e4.trim()) ? "_v" : e4.replace(/[\{|\}\-]/g, "_").replace("\\*", "_v");
  }
  _getIndexRecordFromBundle(e4, t5, r5 = false) {
    const o6 = this._getPackageSize(r5), i4 = o6 * (e4 % o6) + t5 % o6;
    if (i4 < 0) throw new Error("Invalid level / row / col");
    return 20 + i4 * this.storageInfo.recordSize + 44;
  }
  _getTileEndAndContentType(e4, t5) {
    const r5 = e4.subarray(t5, t5 + 8);
    let o6, i4 = 0;
    for (o6 = 0; o6 < 5; o6++) i4 |= (255 & r5[o6]) << 8 * o6;
    const s4 = 1099511627775 & i4;
    for (i4 = 0, o6 = 5; o6 < 8; o6++) i4 |= (255 & r5[o6]) << 8 * (o6 - 5);
    return { position: s4, recordSize: 1099511627775 & i4 };
  }
  _toHexString4(e4) {
    let t5 = e4.toString(16);
    if (4 !== t5.length) {
      let e5 = 4 - t5.length;
      for (; e5-- > 0; ) t5 = "0" + t5;
    }
    return t5;
  }
};
e2([y({ readOnly: true })], I.prototype, "storageInfo", void 0), e2([y({ type: String, json: { write: true } })], I.prototype, "datasetFormat", void 0), I = e2([a("esri.layers.support.rasterDatasets.CloudRaster")], I);
var w5 = I;

// node_modules/@arcgis/core/layers/support/rasterDatasets/InMemoryRaster.js
var h4 = class extends Q2 {
  constructor() {
    super(...arguments), this.datasetFormat = "MEMORY", this.data = null;
  }
  async open(t5) {
    await this.init();
    const e4 = this.data, { pixelBlock: s4, statistics: r5, histograms: i4, name: o6, keyProperties: m9, nativeExtent: n9, transform: l5 } = this.data, { width: h5, height: f8, pixelType: d7 } = s4, u5 = e4.extent ?? new w3({ xmin: -0.5, ymin: 0.5, xmax: h5 - 0.5, ymax: f8 - 0.5, spatialReference: new f({ wkid: 3857 }) }), y4 = e4.isPseudoSpatialReference ?? !e4.extent, x4 = { x: u5.width / h5, y: u5.height / f8 }, g3 = new u2({ width: h5, height: f8, pixelType: d7, extent: u5, nativeExtent: n9, transform: l5, pixelSize: x4, spatialReference: u5.spatialReference, bandCount: s4.pixels.length, keyProperties: m9 || {}, statistics: r5, isPseudoSpatialReference: y4, histograms: i4 });
    this.createRemoteDatasetStorageInfo(g3, 512, 512), this._set("rasterInfo", g3), this.updateTileInfo(), await this._buildInMemoryRaster(s4, { width: 512, height: 512 }, t5), this.datasetName = o6, this.url = "/InMemory/" + o6;
  }
  fetchRawTile(t5, e4, s4, r5 = {}) {
    const i4 = this._pixelBlockTiles.get(`${t5}/${e4}/${s4}`);
    return Promise.resolve(i4);
  }
  async _buildInMemoryRaster(t5, i4, o6) {
    var _a, _b;
    const a6 = this.rasterInfo.storageInfo.maximumPyramidLevel, m9 = this.rasterJobHandler ? this.rasterJobHandler.split({ pixelBlock: t5, tileSize: i4, maximumPyramidLevel: a6 }, o6) : Promise.resolve(R2(t5, i4, a6)), p8 = r(this.rasterInfo.statistics), c10 = r(this.rasterInfo.histograms), h5 = p8 ? Promise.resolve({ statistics: null, histograms: null }) : this.rasterJobHandler ? this.rasterJobHandler.estimateStatisticsHistograms({ pixelBlock: t5 }, o6) : Promise.resolve(p6(t5)), f8 = await E([m9, h5]);
    if (!f8[0].value && f8[1].value) throw new s2("inmemory-raster:open", "failed to build in memory raster");
    this._pixelBlockTiles = f8[0].value, p8 || (this.rasterInfo.statistics = (_a = f8[1].value) == null ? void 0 : _a.statistics), c10 || (this.rasterInfo.histograms = (_b = f8[1].value) == null ? void 0 : _b.histograms);
  }
};
e2([y({ type: String, json: { write: true } })], h4.prototype, "datasetFormat", void 0), e2([y()], h4.prototype, "data", void 0), h4 = e2([a("esri.layers.support.rasterDatasets.InMemoryRaster")], h4);
var f6 = h4;

// node_modules/@arcgis/core/layers/support/rasterDatasets/xmlUtilities.js
function n7(e4, t5) {
  if (!e4 || !t5) return [];
  let l5 = t5;
  t5.includes("/") ? (l5 = t5.slice(0, t5.indexOf("/")), t5 = t5.slice(t5.indexOf("/") + 1)) : t5 = "";
  const r5 = [];
  if (t5) {
    const u6 = n7(e4, l5);
    for (let e5 = 0; e5 < u6.length; e5++) {
      n7(u6[e5], t5).forEach((n9) => r5.push(n9));
    }
    return r5;
  }
  const u5 = e4.getElementsByTagNameNS("*", l5);
  if (!u5 || 0 === u5.length) return [];
  for (let n9 = 0; n9 < u5.length; n9++) r5.push(u5[n9] || u5.item[n9]);
  return r5;
}
function e3(t5, l5) {
  if (!t5 || !l5) return null;
  let r5 = l5;
  l5.includes("/") ? (r5 = l5.slice(0, l5.indexOf("/")), l5 = l5.slice(l5.indexOf("/") + 1)) : l5 = "";
  const u5 = n7(t5, r5);
  return u5.length > 0 ? l5 ? e3(u5[0], l5) : u5[0] : null;
}
function t4(n9, t5 = null) {
  const l5 = t5 ? e3(n9, t5) : n9;
  let r5;
  return l5 ? (r5 = l5.textContent || l5.nodeValue, r5 ? r5.trim() : null) : null;
}
function l4(e4, t5) {
  const l5 = n7(e4, t5), r5 = [];
  let u5;
  for (let n9 = 0; n9 < l5.length; n9++) u5 = l5[n9].textContent || l5[n9].nodeValue, u5 && (u5 = u5.trim(), "" !== u5 && r5.push(u5));
  return r5;
}
function u3(n9, e4) {
  return l4(n9, e4).map((n10) => Number(n10));
}
function o5(n9, e4) {
  const l5 = t4(n9, e4);
  return Number(l5);
}
function i3(n9, e4) {
  var _a;
  const t5 = (_a = n9 == null ? void 0 : n9.nodeName) == null ? void 0 : _a.toLowerCase(), l5 = e4.toLowerCase();
  return t5.slice(t5.lastIndexOf(":") + 1) === l5;
}

// node_modules/@arcgis/core/layers/support/rasterDatasets/pamParser.js
function f7(e4, t5) {
  if (!e4 || !t5) return null;
  const n9 = [];
  for (let r5 = 0; r5 < e4.length; r5++) n9.push(e4[r5]), n9.push(t5[r5]);
  return n9;
}
function u4(e4) {
  const t5 = e3(e4, "GeodataXform"), r5 = m8(o5(t5, "SpatialReference/WKID") || t4(t5, "SpatialReference/WKT"));
  if ("typens:PolynomialXform" !== t5.getAttribute("xsi:type")) return { spatialReference: r5, transform: null };
  const o6 = o5(t5, "PolynomialOrder") ?? 1, u5 = u3(t5, "CoeffX/Double"), c10 = u3(t5, "CoeffY/Double"), d7 = u3(t5, "InverseCoeffX/Double"), p8 = u3(t5, "InverseCoeffY/Double"), S6 = f7(u5, c10), C4 = f7(d7, p8);
  return { spatialReference: r5, transform: S6 && C4 && S6.length && C4.length ? new m7({ spatialReference: r5, polynomialOrder: o6, forwardCoefficients: S6, inverseCoefficients: C4 }) : null };
}
function c8(e4) {
  var _a;
  const t5 = o5(e4, "NoDataValue"), i4 = e3(e4, "Histograms/HistItem"), l5 = o5(i4, "HistMin"), o6 = o5(i4, "HistMax"), f8 = o5(i4, "BucketCount"), u5 = (_a = t4(i4, "HistCounts")) == null ? void 0 : _a.split("|").map((e5) => Number(e5));
  let c10, m9, d7, p8;
  n7(e4, "Metadata/MDI").forEach((e5) => {
    const t6 = Number(e5.textContent ?? e5.nodeValue);
    switch (e5.getAttribute("key").toUpperCase()) {
      case "STATISTICS_MINIMUM":
        c10 = t6;
        break;
      case "STATISTICS_MAXIMUM":
        m9 = t6;
        break;
      case "STATISTICS_MEAN":
        d7 = t6;
        break;
      case "STATISTICS_STDDEV":
        p8 = t6;
    }
  });
  const S6 = o5(e4, "Metadata/SourceBandIndex");
  return { noDataValue: t5, histogram: (u5 == null ? void 0 : u5.length) && null != l5 && null != o6 ? { min: l5, max: o6, size: f8 || u5.length, counts: u5 } : null, sourceBandIndex: S6, statistics: null != c10 && null != m9 ? { min: c10, max: m9, avg: d7, stddev: p8 } : null };
}
function m8(e4) {
  if (!e4) return null;
  let t5 = Number(e4);
  if (!isNaN(t5) && 0 !== t5) return new f({ wkid: t5 });
  if ((e4 = String(e4)).startsWith("COMPD_CS")) {
    if (!e4.includes("VERTCS") || !e4.includes("GEOGCS") && !e4.startsWith("PROJCS")) return null;
    const n9 = e4.indexOf("VERTCS"), r5 = e4.indexOf("PROJCS"), s4 = r5 > -1 ? r5 : e4.indexOf("GEOGCS");
    if (-1 === s4) return null;
    const a6 = e4.slice(s4, e4.lastIndexOf("]", n9) + 1).trim(), i4 = e4.slice(n9, e4.lastIndexOf("]")).trim();
    t5 = d5(a6);
    const l5 = new f(t5 ? { wkid: t5 } : { wkt: a6 }), f8 = d5(i4);
    return f8 && (l5.vcsWkid = f8), l5;
  }
  return e4.startsWith("GEOGCS") || e4.startsWith("PROJCS") ? (t5 = d5(e4), new f(0 !== t5 ? { wkid: t5 } : { wkt: e4 })) : null;
}
function d5(e4) {
  var _a;
  const t5 = e4.replace(/\]/g, "[").replace(/\"/g, "").split("[").map((e5) => e5.trim()).filter((e5) => "" !== e5), n9 = t5[t5.length - 1].split(","), r5 = (_a = n9[0]) == null ? void 0 : _a.toLowerCase();
  if (("epsg" === r5 || "esri" === r5) && e4.endsWith('"]]')) {
    const e5 = Number(n9[1]);
    if (!isNaN(e5) && 0 !== e5) return e5;
  }
  return 0;
}
function p7(s4) {
  var _a;
  if ("pamdataset" !== ((_a = s4 == null ? void 0 : s4.documentElement.tagName) == null ? void 0 : _a.toLowerCase())) return {};
  const a6 = { spatialReference: null, transform: null, metadata: {}, rasterBands: [], statistics: null, histograms: null };
  s4.documentElement.childNodes.forEach((e4) => {
    if (1 === e4.nodeType) {
      if (i3(e4, "SRS")) {
        if (!a6.spatialReference) {
          const t5 = t4(e4);
          a6.spatialReference = m8(t5);
        }
      } else if (i3(e4, "Metadata")) if ("xml:ESRI" === e4.getAttribute("domain")) {
        const { spatialReference: t5, transform: n9 } = u4(e4);
        a6.transform = n9, a6.spatialReference || (a6.spatialReference = t5);
      } else {
        n7(e4, "MDI").forEach((e5) => a6.metadata[e5.getAttribute("key")] = t4(e5));
      }
      else if (i3(e4, "PAMRasterBand")) {
        const t5 = c8(e4);
        null != t5.sourceBandIndex && null == a6.rasterBands[t5.sourceBandIndex] ? a6.rasterBands[t5.sourceBandIndex] = t5 : a6.rasterBands.push(t5);
      }
    }
  });
  const i4 = a6.rasterBands;
  if (i4.length) {
    const t5 = !!i4[0].statistics;
    a6.statistics = t5 ? i4.map((e4) => e4.statistics).filter(r) : null;
    const n9 = !!i4[0].histogram;
    a6.histograms = n9 ? i4.map((e4) => e4.histogram).filter(r) : null;
  }
  return a6;
}

// node_modules/@arcgis/core/layers/support/rasterDatasets/ImageAuxRaster.js
var d6 = class extends Q2 {
  async open(t5) {
    await this.init();
    const e4 = await this._fetchData(t5);
    let { spatialReference: s4, statistics: r5, histograms: a6, transform: o6 } = await this._fetchAuxiliaryData(t5);
    const i4 = !s4;
    i4 && (s4 = new f({ wkid: 3857 })), (a6 == null ? void 0 : a6.length) && null == r5 && (r5 = g2(a6));
    const { width: n9, height: l5 } = e4;
    let p8 = new w3({ xmin: -0.5, ymin: 0.5 - l5, xmax: n9 - 0.5, ymax: 0.5, spatialReference: s4 });
    const f8 = o6 ? o6.forwardTransform(p8) : p8;
    let d7 = true;
    if (o6) {
      const t6 = o6.forwardCoefficients;
      d7 = t6 && 0 === t6[1] && 0 === t6[2], d7 && (o6 = null, p8 = f8);
    }
    const w8 = new f6({ data: { extent: f8, nativeExtent: p8, transform: o6, pixelBlock: e4, statistics: r5, histograms: a6, keyProperties: { DateType: "Processed" }, isPseudoSpatialReference: i4 } });
    await w8.open(), w8.data = null, this._set("rasterInfo", w8.rasterInfo), this._inMemoryRaster = w8;
  }
  fetchRawTile(t5, e4, s4, r5 = {}) {
    return this._inMemoryRaster.fetchRawTile(t5, e4, s4, r5);
  }
  async _fetchData(t5) {
    const { data: s4 } = await this.request(this.url, { responseType: "array-buffer", signal: t5 == null ? void 0 : t5.signal }), r5 = j2(s4).toUpperCase();
    if ("JPG" !== r5 && "PNG" !== r5 && "GIF" !== r5 && "BMP" !== r5) throw new s2("image-aux-raster:open", "the data is not a supported format");
    this._set("datasetFormat", r5);
    const a6 = r5.toLowerCase(), o6 = "gif" === a6 || "bmp" === a6 || !has("ios"), i4 = await this.decodePixelBlock(s4, { format: a6, useCanvas: o6, hasNoZlibMask: true });
    if (null == i4) throw new s2("image-aux-raster:open", "the data cannot be decoded");
    return i4;
  }
  async _fetchAuxiliaryData(t5) {
    var _a;
    const e4 = e(t5 == null ? void 0 : t5.signal), o6 = this.ioConfig.skipExtensions ?? [], i4 = o6.includes("aux.xml") ? null : this.request(this.url + ".aux.xml", { responseType: "xml", signal: e4 }), n9 = this.datasetFormat, m9 = "JPG" === n9 ? "jgw" : "PNG" === n9 ? "pgw" : "BMP" === n9 ? "bpw" : null, p8 = m9 && o6.includes(m9) ? null : this.request(this.url.slice(0, this.url.lastIndexOf(".")) + "." + m9, { responseType: "text", signal: e4 }), c10 = await E([i4, p8]);
    if (e4 == null ? void 0 : e4.aborted) throw a2();
    const u5 = p7((_a = c10[0].value) == null ? void 0 : _a.data);
    if (!u5.transform) {
      const t6 = c10[1].value ? c10[1].value.data.split("\n").slice(0, 6).map((t7) => Number(t7)) : null;
      u5.transform = 6 === (t6 == null ? void 0 : t6.length) ? new m7({ forwardCoefficients: [t6[4], t6[5], t6[0], -t6[1], t6[2], -t6[3]] }) : null;
    }
    return u5;
  }
};
e2([y({ type: String, json: { write: true } })], d6.prototype, "datasetFormat", void 0), d6 = e2([a("esri.layers.support.rasterDatasets.ImageAuxRaster")], d6);
var w6 = d6;

// node_modules/@arcgis/core/layers/support/rasterDatasets/ImageServerRaster.js
var I2 = class extends Q2 {
  constructor() {
    super(...arguments), this._levelOffset = 0, this._tilemapCache = null, this._slices = null, this.datasetFormat = "RasterTileServer", this.tileType = null;
  }
  async open(e4) {
    var _a, _b;
    await this.init();
    const r5 = e4 && e4.signal, a6 = this.sourceJSON ? { data: this.sourceJSON } : await this.request(this.url, { query: { f: "json" }, signal: r5 });
    a6.ssl && (this.url = this.url.replace(/^http:/i, "https:"));
    const o6 = a6.data;
    if (this.sourceJSON = o6, !o6) throw new s2("imageserverraster:open", "cannot initialize tiled image service, missing service info");
    if (!o6.tileInfo) throw new s2("imageserverraster:open", "use ImageryLayer to open non-tiled image services");
    this._fixScaleInServiceInfo();
    const n9 = ["jpg", "jpeg", "png", "png8", "png24", "png32", "mixed"];
    this.tileType = o6.cacheType, null == this.tileType && (n9.includes(o6.tileInfo.format.toLowerCase()) ? this.tileType = "Map" : "lerc" === o6.tileInfo.format.toLowerCase() ? this.tileType = "Elevation" : this.tileType = "Raster"), this.datasetName = ((_a = o6.name) == null ? void 0 : _a.slice(o6.name.indexOf("/") + 1)) ?? "";
    const c10 = await this._fetchRasterInfo({ signal: r5 });
    if (t(c10)) throw new s2("image-server-raster:open", "cannot initialize image service");
    const p8 = "Map" === this.tileType ? n2(o6.tileInfo, o6) : j.fromJSON(o6.tileInfo);
    i(p8);
    const [y4, d7] = this._computeMinMaxLOD(c10, p8), { extent: x4, pixelSize: g3 } = c10, v6 = 0.5 / c10.width * g3.x, S6 = Math.max(g3.x, g3.y), { lods: I4 } = p8;
    ("Map" !== this.tileType && 0 !== o6.maxScale || Math.abs(g3.x - g3.y) > v6 || !I4.some((e5) => Math.abs(e5.resolution - S6) < v6)) && (g3.x = g3.y = y4.resolution, c10.width = Math.ceil((x4.xmax - x4.xmin) / g3.x - 0.1), c10.height = Math.ceil((x4.ymax - x4.ymin) / g3.y - 0.1));
    const w8 = y4.level - d7.level, [j5, T4] = p8.size, b4 = [], M = [];
    I4.forEach((e5, t5) => {
      e5.level >= d7.level && e5.level <= y4.level && b4.push({ x: e5.resolution, y: e5.resolution }), t5 < I4.length - 1 && M.push(Math.round(10 * e5.resolution / I4[t5 + 1].resolution) / 10);
    }), b4.sort((e5, t5) => e5.x - t5.x);
    const _2 = this.computeBlockBoundary(x4, j5, T4, p8.origin, b4, w8), R4 = b4.length > 1 ? b4.slice(1) : null;
    let z3;
    o6.transposeInfo && (z3 = { tileSize: [o6.transposeInfo.rows, o6.transposeInfo.cols], packetSize: ((_b = c10.keyProperties) == null ? void 0 : _b._yxs.PacketSize) ?? 0 });
    const P = M.length <= 1 || M.length >= 3 && M.slice(0, M.length - 1).every((e5) => e5 === M[0]) ? M[0] ?? 2 : Math.round(10 / (d7.resolution / y4.resolution) ** (-1 / w8)) / 10;
    if (c10.storageInfo = new n3({ blockWidth: p8.size[0], blockHeight: p8.size[1], pyramidBlockWidth: p8.size[0], pyramidBlockHeight: p8.size[1], pyramidResolutions: R4, pyramidScalingFactor: P, compression: p8.format, origin: p8.origin, firstPyramidLevel: 1, maximumPyramidLevel: w8, tileInfo: p8, transposeInfo: z3, blockBoundary: _2 }), this._fixGCSShift(c10), this._set("rasterInfo", c10), o6.capabilities.toLowerCase().includes("tilemap")) {
      const e5 = { tileInfo: c10.storageInfo.tileInfo, parsedUrl: L(this.url), url: this.url, tileServers: [], type: "tile" };
      this._tilemapCache = new z({ layer: e5 });
    }
  }
  async fetchRawTile(e4, t5, i4, s4 = {}) {
    const { storageInfo: l5, extent: o6 } = this.rasterInfo, { transposeInfo: n9 } = l5, c10 = r(n9) && !!s4.transposedVariableName;
    if (this._slices && !c10 && null == s4.sliceId) return null;
    const m9 = c10 ? 0 : l5.maximumPyramidLevel - e4 + this._levelOffset, h5 = `${this.url}/tile/${m9}/${t5}/${i4}`, u5 = this._slices ? c10 ? { variable: s4.transposedVariableName } : { sliceId: s4.sliceId || 0 } : null, { data: f8 } = await this.request(h5, { query: u5, responseType: "array-buffer", signal: s4.signal });
    if (!f8) return null;
    const p8 = c10 ? n9.tileSize : l5.tileInfo.size, d7 = await this.decodePixelBlock(f8, { width: p8[0], height: p8[1], planes: null, pixelType: null, isPoint: "Elevation" === this.tileType, returnInterleaved: c10, noDataValue: e(this.rasterInfo.noDataValue) });
    if (null == d7) return null;
    const x4 = l5.blockBoundary[e4];
    if ("jpg" !== l5.compression || i4 > x4.minCol && i4 < x4.maxCol && t5 > x4.minRow && t5 < x4.maxRow) return d7;
    const { origin: g3, blockWidth: v6, blockHeight: S6 } = l5, { x: I4, y: w8 } = this.getPyramidPixelSize(e4), j5 = Math.round((o6.xmin - g3.x) / I4) % v6, T4 = Math.round((o6.xmax - g3.x) / I4) % v6 || v6, b4 = Math.round((g3.y - o6.ymax) / w8) % S6, M = Math.round((g3.y - o6.ymin) / w8) % S6 || S6, _2 = i4 === x4.minCol ? j5 : 0, R4 = t5 === x4.minRow ? b4 : 0, z3 = i4 === x4.maxCol ? T4 : v6, P = t5 === x4.maxRow ? M : S6;
    return S2(d7, { x: _2, y: R4 }, { width: z3 - _2, height: P - R4 }), d7;
  }
  getSliceIndex(e4) {
    if (!this._slices || t(e4) || 0 === e4.length) return null;
    const t5 = e4;
    for (let i4 = 0; i4 < this._slices.length; i4++) {
      const e5 = this._slices[i4].multidimensionalDefinition;
      if (e5.length === t5.length && !e5.some((e6) => {
        const i5 = t5.find((t6) => e6.variableName === t6.variableName && t6.dimensionName === e6.dimensionName);
        if (!i5) return true;
        return (Array.isArray(e6.values[0]) ? `${e6.values[0][0]}-${e6.values[0][1]}` : e6.values[0]) !== (Array.isArray(i5.values[0]) ? `${i5.values[0][0]}-${i5.values[0][1]}` : i5.values[0]);
      })) return i4;
    }
    return null;
  }
  async fetchVariableStatisticsHistograms(e4, t5) {
    const i4 = this.request(this.url + "/statistics", { query: { variable: e4, f: "json" }, signal: t5 }).then((e5) => {
      var _a;
      return (_a = e5.data) == null ? void 0 : _a.statistics;
    }), s4 = this.request(this.url + "/histograms", { query: { variable: e4, f: "json" }, signal: t5 }).then((e5) => {
      var _a;
      return (_a = e5.data) == null ? void 0 : _a.histograms;
    }), r5 = await Promise.all([i4, s4]);
    return r5[0] && r5[0].forEach((e5) => {
      e5.avg = e5.mean, e5.stddev = e5.standardDeviation;
    }), { statistics: r5[0] || null, histograms: r5[1] || null };
  }
  async computeBestPyramidLevelForLocation(e4, t5 = {}) {
    if (!this._tilemapCache) return 0;
    let i4 = this.identifyPixelLocation(e4, 0, e(t5.datumTransformation));
    if (null === i4) return null;
    let s4 = 0;
    const { maximumPyramidLevel: r5 } = this.rasterInfo.storageInfo;
    let l5 = r5 - s4 + this._levelOffset;
    const o6 = i4.srcLocation;
    for (; l5 >= 0; ) {
      try {
        if ("available" === await this._tilemapCache.fetchAvailability(l5, i4.row, i4.col, t5)) break;
      } catch {
      }
      if (l5--, s4++, i4 = this.identifyPixelLocation(o6, s4, e(t5.datumTransformation)), null === i4) return null;
    }
    return -1 === l5 || null == i4 ? null : s4;
  }
  async _fetchRasterInfo(e4) {
    const t5 = this.sourceJSON;
    if ("Map" === this.tileType) {
      const e5 = t5.fullExtent || t5.extent, i5 = Math.ceil((e5.xmax - e5.xmin) / t5.pixelSizeX - 0.1), s5 = Math.ceil((e5.ymax - e5.ymin) / t5.pixelSizeY - 0.1), r6 = f.fromJSON(t5.spatialReference || e5.spatialReference), a7 = new w2({ x: t5.pixelSizeX, y: t5.pixelSizeY, spatialReference: r6 });
      return new u2({ width: i5, height: s5, bandCount: 3, extent: w3.fromJSON(e5), spatialReference: r6, pixelSize: a7, pixelType: "u8", statistics: null, keyProperties: { DataType: "processed" } });
    }
    const { signal: i4 } = e4, s4 = m5(this.url, this.sourceJSON, { signal: i4, query: this.ioConfig.customFetchParameters }), r5 = t5.hasMultidimensions ? this.request(`${this.url}/slices`, { query: { f: "json" }, signal: i4 }).then((e5) => e5.data && e5.data.slices).catch(() => null) : null, a6 = await Promise.all([s4, r5]);
    return this._slices = a6[1], a6[0];
  }
  _fixScaleInServiceInfo() {
    const { sourceJSON: e4 } = this;
    e4.minScale && e4.minScale < 0 && (e4.minScale = 0), e4.maxScale && e4.maxScale < 0 && (e4.maxScale = 0);
  }
  _fixGCSShift(e4) {
    const { extent: t5, spatialReference: i4 } = e4;
    t5.xmin > -1 && t5.xmax > 181 && (i4 == null ? void 0 : i4.wkid) && i4.isGeographic && (e4.nativeExtent = e4.extent, e4.transform = new c6(), e4.extent = e4.transform.forwardTransform(t5));
  }
  _computeMinMaxLOD(e4, t5) {
    const { pixelSize: i4 } = e4, s4 = 0.5 / e4.width * i4.x, { lods: r5 } = t5, a6 = t5.lodAt(Math.max.apply(null, r5.map((e5) => e5.level))), l5 = t5.lodAt(Math.min.apply(null, r5.map((e5) => e5.level))), { tileType: o6 } = this;
    if ("Map" === o6) return this._levelOffset = r5[0].level, [a6, l5];
    if ("Raster" === o6) {
      return [r5.find((e5) => e5.resolution === i4.x) ?? a6, l5];
    }
    const { minScale: n9, maxScale: c10 } = this.sourceJSON;
    let m9 = a6;
    c10 > 0 && (m9 = r5.find((e5) => Math.abs(e5.scale - c10) < s4), m9 || (m9 = r5.filter((e5) => e5.scale > c10).sort((e5, t6) => e5.scale > t6.scale ? 1 : -1)[0] ?? a6));
    let h5 = l5;
    return n9 > 0 && (h5 = r5.find((e5) => Math.abs(e5.scale - n9) < s4) ?? l5, this._levelOffset = h5.level - l5.level), [m9, h5];
  }
};
e2([y({ type: String, json: { write: true } })], I2.prototype, "datasetFormat", void 0), e2([y()], I2.prototype, "tileType", void 0), I2 = e2([a("esri.layers.support.rasterDatasets.ImageServerRaster")], I2);
var w7 = I2;

// node_modules/@arcgis/core/layers/support/rasterDatasets/MRFRaster.js
var I3 = /* @__PURE__ */ new Map();
I3.set("Int8", "s8"), I3.set("UInt8", "u8"), I3.set("Int16", "s16"), I3.set("UInt16", "u16"), I3.set("Int32", "s32"), I3.set("UInt32", "u32"), I3.set("Float32", "f32"), I3.set("Float64", "f32"), I3.set("Double64", "f32");
var b3 = /* @__PURE__ */ new Map();
b3.set("none", { blobExtension: ".til", isOneSegment: true, decoderFormat: "bip" }), b3.set("lerc", { blobExtension: ".lrc", isOneSegment: false, decoderFormat: "lerc" }), b3.set("deflate", { blobExtension: ".pzp", isOneSegment: true, decoderFormat: "deflate" }), b3.set("jpeg", { blobExtension: ".pjg", isOneSegment: true, decoderFormat: "jpg" });
var A = class extends Q2 {
  constructor() {
    super(...arguments), this._files = null, this._storageIndex = null, this.datasetFormat = "MRF";
  }
  async open(t5) {
    var _a;
    await this.init(), this.datasetName = this.url.slice(this.url.lastIndexOf("/") + 1);
    const e4 = t5 ? e(t5.signal) : null, o6 = await this.request(this.url, { responseType: "xml", signal: e4 }), { rasterInfo: i4, files: a6 } = this._parseHeader(o6.data);
    if (-1 === ((_a = this.ioConfig.skipExtensions) == null ? void 0 : _a.indexOf("aux.xml"))) {
      const e5 = await this._fetchAuxiliaryData(t5);
      null != e5 && (i4.statistics = e5.statistics ?? i4.statistics, i4.histograms = e5.histograms, e5.histograms && t(i4.statistics) && (i4.statistics = g2(e5.histograms)));
    }
    this._set("rasterInfo", i4), this._files = a6;
    const n9 = await this.request(a6.index, { responseType: "array-buffer", signal: e4 });
    this._storageIndex = this._parseIndex(n9.data);
    const { blockWidth: l5, blockHeight: f8 } = this.rasterInfo.storageInfo, c10 = this.rasterInfo.storageInfo.pyramidScalingFactor, { width: m9, height: p8 } = this.rasterInfo, h5 = [], u5 = this._getBandSegmentCount();
    let g3 = 0, y4 = -1;
    for (; g3 < this._storageIndex.length; ) {
      y4++;
      const t6 = Math.ceil(m9 / l5 / c10 ** y4) - 1, e5 = Math.ceil(p8 / f8 / c10 ** y4) - 1;
      g3 += (t6 + 1) * (e5 + 1) * u5 * 4, h5.push({ maxRow: e5, maxCol: t6, minCol: 0, minRow: 0 });
    }
    this.rasterInfo.storageInfo.blockBoundary = h5, y4 > 0 && (this.rasterInfo.storageInfo.firstPyramidLevel = 1, this.rasterInfo.storageInfo.maximumPyramidLevel = y4), this.updateTileInfo();
  }
  async fetchRawTile(t5, e4, r5, s4 = {}) {
    const { blockWidth: i4, blockHeight: a6, blockBoundary: l5 } = this.rasterInfo.storageInfo, f8 = l5[t5];
    if (!f8 || f8.maxRow < e4 || f8.maxCol < r5 || f8.minRow > e4 || f8.minCol > r5) return null;
    const { bandCount: c10, pixelType: m9 } = this.rasterInfo, { ranges: p8, actualTileWidth: h5, actualTileHeight: u5 } = this._getTileLocation(t5, e4, r5);
    if (!p8 || 0 === p8.length) return null;
    if (0 === p8[0].from && 0 === p8[0].to) {
      const t6 = new Uint8Array(i4 * a6);
      return new m2({ width: i4, height: a6, pixels: null, mask: t6, validPixelCount: 0 });
    }
    const { bandIds: g3 } = this.ioConfig, d7 = this._getBandSegmentCount(), y4 = [];
    let x4 = 0;
    for (x4 = 0; x4 < d7; x4++) (!g3 || g3.indexOf[x4] > -1) && y4.push(this.request(this._files.data, { range: { from: p8[x4].from, to: p8[x4].to }, responseType: "array-buffer", signal: s4.signal }));
    const w8 = await Promise.all(y4), I4 = w8.map((t6) => t6.data.byteLength).reduce((t6, e5) => t6 + e5), A2 = new Uint8Array(I4);
    let F3 = 0;
    for (x4 = 0; x4 < d7; x4++) A2.set(new Uint8Array(w8[x4].data), F3), F3 += w8[x4].data.byteLength;
    const _2 = b3.get(this.rasterInfo.storageInfo.compression).decoderFormat, R4 = await this.decodePixelBlock(A2.buffer, { width: i4, height: a6, format: _2, planes: (g3 == null ? void 0 : g3.length) || c10, pixelType: m9 });
    if (null == R4) return null;
    if (r(this.rasterInfo.noDataValue) && "lerc" !== _2 && !R4.mask) {
      const t6 = this.rasterInfo.noDataValue[0];
      if (null != t6) {
        const e5 = R4.width * R4.height, r6 = new Uint8Array(e5);
        if (Math.abs(t6) > 1e24) for (x4 = 0; x4 < e5; x4++) Math.abs((R4.pixels[0][x4] - t6) / t6) > 1e-6 && (r6[x4] = 1);
        else for (x4 = 0; x4 < e5; x4++) R4.pixels[0][x4] !== t6 && (r6[x4] = 1);
        R4.mask = r6;
      }
    }
    let S6 = 0, j5 = 0;
    if (h5 !== i4 || u5 !== a6) {
      let t6 = R4.mask;
      if (t6) for (x4 = 0; x4 < a6; x4++) if (j5 = x4 * i4, x4 < u5) for (S6 = h5; S6 < i4; S6++) t6[j5 + S6] = 0;
      else for (S6 = 0; S6 < i4; S6++) t6[j5 + S6] = 0;
      else for (t6 = new Uint8Array(i4 * a6), R4.mask = t6, x4 = 0; x4 < u5; x4++) for (j5 = x4 * i4, S6 = 0; S6 < h5; S6++) t6[j5 + S6] = 1;
    }
    return R4;
  }
  _parseIndex(t5) {
    if (t5.byteLength % 16 > 0) throw new Error("invalid array buffer must be multiples of 16");
    let e4, r5, s4, o6, i4, a6;
    if (r3) {
      for (r5 = new Uint8Array(t5), o6 = new ArrayBuffer(t5.byteLength), s4 = new Uint8Array(o6), i4 = 0; i4 < t5.byteLength / 4; i4++) for (a6 = 0; a6 < 4; a6++) s4[4 * i4 + a6] = r5[4 * i4 + 3 - a6];
      e4 = new Uint32Array(o6);
    } else e4 = new Uint32Array(t5);
    return e4;
  }
  _getBandSegmentCount() {
    return b3.get(this.rasterInfo.storageInfo.compression).isOneSegment ? 1 : this.rasterInfo.bandCount;
  }
  _getTileLocation(t5, e4, r5) {
    const { blockWidth: s4, blockHeight: o6, pyramidScalingFactor: i4 } = this.rasterInfo.storageInfo, { width: a6, height: n9 } = this.rasterInfo, l5 = this._getBandSegmentCount();
    let f8, c10, m9, p8 = 0, h5 = 0;
    for (m9 = 0; m9 < t5; m9++) h5 = i4 ** m9, f8 = Math.ceil(a6 / s4 / h5), c10 = Math.ceil(n9 / o6 / h5), p8 += f8 * c10;
    h5 = i4 ** t5, f8 = Math.ceil(a6 / s4 / h5), c10 = Math.ceil(n9 / o6 / h5), p8 += e4 * f8 + r5, p8 *= 4 * l5;
    const u5 = this._storageIndex.subarray(p8, p8 + 4 * l5);
    let g3 = 0, d7 = 0;
    const y4 = [];
    for (let x4 = 0; x4 < l5; x4++) g3 = u5[4 * x4 + 0] * 2 ** 32 + u5[4 * x4 + 1], d7 = g3 + u5[4 * x4 + 2] * 2 ** 32 + u5[4 * x4 + 3], y4.push({ from: g3, to: d7 });
    return { ranges: y4, actualTileWidth: r5 < f8 - 1 ? s4 : Math.ceil(a6 / h5) - s4 * (f8 - 1), actualTileHeight: e4 < c10 - 1 ? o6 : Math.ceil(n9 / h5) - o6 * (c10 - 1) };
  }
  _parseHeader(t5) {
    const r5 = e3(t5, "MRF_META/Raster");
    if (!r5) throw new s2("mrf:open", "not a valid MRF format");
    const s4 = e3(r5, "Size"), o6 = parseInt(s4.getAttribute("x"), 10), i4 = parseInt(s4.getAttribute("y"), 10), a6 = parseInt(s4.getAttribute("c"), 10), n9 = (t4(r5, "Compression") || "none").toLowerCase();
    if (!b3.has(n9)) throw new s2("mrf:open", "currently does not support compression " + n9);
    const c10 = t4(r5, "DataType") || "UInt8", p8 = I3.get(c10);
    if (null == p8) throw new s2("mrf:open", "currently does not support pixel type " + c10);
    const g3 = e3(r5, "PageSize"), d7 = parseInt(g3.getAttribute("x"), 10), A2 = parseInt(g3.getAttribute("y"), 10), F3 = e3(r5, "DataValues");
    let _2, R4;
    F3 && (R4 = F3.getAttribute("NoData"), null != R4 && (_2 = R4.trim().split(" ").map((t6) => parseFloat(t6))));
    if (e3(t5, "MRF_META/CachedSource")) throw new s2("mrf:open", "currently does not support MRF referencing other data files");
    const S6 = e3(t5, "MRF_META/GeoTags"), j5 = e3(S6, "BoundingBox");
    let k3, M = false;
    if (null != j5) {
      const t6 = parseFloat(j5.getAttribute("minx")), e4 = parseFloat(j5.getAttribute("miny")), r6 = parseFloat(j5.getAttribute("maxx")), s5 = parseFloat(j5.getAttribute("maxy")), o7 = t4(S6, "Projection") || "";
      let i5 = f.WGS84;
      if ("LOCAL_CS[]" !== o7) if (o7.toLowerCase().startsWith("epsg:")) {
        const t7 = Number(o7.slice(5));
        isNaN(t7) || 0 === t7 || (i5 = new f({ wkid: t7 }));
      } else i5 = m8(o7) ?? f.WGS84;
      else M = true, i5 = new f({ wkid: 3857 });
      k3 = new w3(t6, e4, r6, s5), k3.spatialReference = i5;
    } else M = true, k3 = new w3({ xmin: -0.5, ymin: 0.5 - i4, xmax: o6 - 0.5, ymax: 0.5, spatialReference: new f({ wkid: 3857 }) });
    const T4 = e3(t5, "MRF_META/Rsets"), C4 = parseInt(T4 && T4.getAttribute("scale") || "2", 10), U4 = k3.spatialReference, B2 = new n3({ origin: new w2({ x: k3.xmin, y: k3.ymax, spatialReference: U4 }), blockWidth: d7, blockHeight: A2, pyramidBlockWidth: d7, pyramidBlockHeight: A2, compression: n9, pyramidScalingFactor: C4 }), E4 = new w2({ x: k3.width / o6, y: k3.height / i4, spatialReference: U4 }), L4 = new u2({ width: o6, height: i4, extent: k3, isPseudoSpatialReference: M, spatialReference: U4, bandCount: a6, pixelType: p8, pixelSize: E4, noDataValue: _2, storageInfo: B2 }), P = t4(t5, "datafile"), O3 = t4(t5, "IndexFile");
    return { rasterInfo: L4, files: { mrf: this.url, index: O3 || this.url.replace(".mrf", ".idx"), data: P || this.url.replace(".mrf", b3.get(n9).blobExtension) } };
  }
  async _fetchAuxiliaryData(t5) {
    try {
      const { data: e4 } = await this.request(this.url + ".aux.xml", { responseType: "xml", signal: t5 == null ? void 0 : t5.signal });
      return p7(e4);
    } catch {
      return null;
    }
  }
};
e2([y()], A.prototype, "_files", void 0), e2([y()], A.prototype, "_storageIndex", void 0), e2([y({ type: String, json: { write: true } })], A.prototype, "datasetFormat", void 0), A = e2([a("esri.layers.support.rasterIO.MRFRaster")], A);
var F2 = A;

// node_modules/@arcgis/core/layers/support/rasterDatasets/TIFFRaster.js
var E2 = (e4, t5) => {
  var _a;
  return (_a = e4.get(t5)) == null ? void 0 : _a.values;
};
var k2 = (e4, t5) => {
  var _a, _b;
  return (_b = (_a = e4.get(t5)) == null ? void 0 : _a.values) == null ? void 0 : _b[0];
};
var D3 = class extends Q2 {
  constructor() {
    super(...arguments), this._files = null, this._headerInfo = null, this._bufferSize = 1048576, this.datasetFormat = "TIFF";
  }
  async open(e4) {
    await this.init();
    const s4 = e4 ? e(e4.signal) : null, { data: a6 } = await this.request(this.url, { range: { from: 0, to: this._bufferSize }, responseType: "array-buffer", signal: s4 });
    if (!a6) throw new s2("tiffraster:open", "failed to open url " + this.url);
    this.datasetName = this.url.slice(this.url.lastIndexOf("/") + 1, this.url.lastIndexOf("."));
    const { littleEndian: n9, firstIFDPos: o6, isBigTiff: f8 } = v4(a6), l5 = [];
    await this._readIFDs(l5, a6, n9, o6, 0, f8 ? 8 : 4, s4);
    const { imageInfo: u5, rasterInfo: c10 } = this._parseIFDs(l5), p8 = N(l5), y4 = U2(l5);
    if (this._headerInfo = { littleEndian: n9, isBigTiff: f8, ifds: l5, pyramidIFDs: p8, maskIFDs: y4, ...u5 }, this._set("rasterInfo", c10), !u5.isSupported) throw new s2("tiffraster:open", "this tiff is not supported: " + u5.message);
    if (!u5.tileWidth) throw new s2("tiffraster:open", "none-tiled tiff is not optimized for access, convert to COG and retry.");
    const { skipExtensions: g3 = [] } = this.ioConfig;
    if (!g3.includes("aux.xml")) {
      const t5 = await this._fetchAuxiliaryMetaData(e4);
      null != t5 && this._processPAMInfo(t5, c10);
    }
    g3.includes("vat.dbf") || 1 !== c10.bandCount || "u8" !== c10.pixelType || (c10.attributeTable = await this._fetchAuxiliaryTable(e4), r(c10.attributeTable) && (c10.keyProperties.DataType = "thematic")), this.updateTileInfo();
  }
  async fetchRawTile(e4, t5, r5, s4 = {}) {
    var _a;
    if (!((_a = this._headerInfo) == null ? void 0 : _a.isSupported) || this.isBlockOutside(e4, t5, r5)) return null;
    const a6 = await this._fetchRawTiffTile(e4, t5, r5, false, s4);
    if (r(a6) && this._headerInfo.hasMaskBand) {
      const n9 = await this._fetchRawTiffTile(e4, t5, r5, true, s4);
      r(n9) && n9.pixels[0] instanceof Uint8Array && (a6.mask = n9.pixels[0]);
    }
    return a6;
  }
  _parseIFDs(e4) {
    var _a, _b;
    const t5 = R3(e4), { width: r5, height: i4, tileWidth: s4, tileHeight: a6, planes: n9, pixelType: l5, compression: u5, firstPyramidLevel: p8, maximumPyramidLevel: m9, pyramidBlockWidth: h5, pyramidBlockHeight: d7, tileBoundary: g3, affine: T4, metadata: x4 } = t5, I4 = ((_a = t5.extent.spatialReference) == null ? void 0 : _a.wkt) || ((_b = t5.extent.spatialReference) == null ? void 0 : _b.wkid);
    let w8 = m8(I4), b4 = !!t5.isPseudoGeographic;
    null == w8 && (b4 = true, w8 = new f({ wkid: 3857 }));
    const D4 = new w3({ ...t5.extent, spatialReference: w8 }), v6 = new w2(D4 ? { x: D4.xmin, y: D4.ymax, spatialReference: w8 } : { x: 0, y: 0 }), P = new n3({ blockWidth: s4, blockHeight: a6, pyramidBlockWidth: h5, pyramidBlockHeight: d7, compression: u5, origin: v6, firstPyramidLevel: p8, maximumPyramidLevel: m9, blockBoundary: g3 }), O3 = new w2({ x: (D4.xmax - D4.xmin) / r5, y: (D4.ymax - D4.ymin) / i4, spatialReference: w8 }), j5 = x4 ? { BandProperties: x4.bandProperties, DataType: x4.dataType } : {};
    let B2 = null;
    const L4 = k2(e4[0], "PHOTOMETRICINTERPRETATION"), A2 = E2(e4[0], "COLORMAP");
    if (L4 <= 3 && (A2 == null ? void 0 : A2.length) > 3 && A2.length % 3 == 0) {
      B2 = [];
      const e5 = A2.length / 3;
      for (let t6 = 0; t6 < e5; t6++) B2.push([t6, A2[t6] >>> 8, A2[t6 + e5] >>> 8, A2[t6 + 2 * e5] >>> 8]);
    }
    const z3 = new u2({ width: r5, height: i4, bandCount: n9, pixelType: l5, pixelSize: O3, storageInfo: P, spatialReference: w8, isPseudoSpatialReference: b4, keyProperties: j5, extent: D4, colormap: B2, statistics: x4 ? x4.statistics : null });
    return (T4 == null ? void 0 : T4.length) && (z3.nativeExtent = new w3({ xmin: -0.5, ymin: 0.5 - i4, xmax: r5 - 0.5, ymax: 0.5, spatialReference: w8 }), z3.transform = new m7({ polynomialOrder: 1, forwardCoefficients: [T4[2] + T4[0] / 2, T4[5] - T4[3] / 2, T4[0], T4[3], -T4[1], -T4[4]] }), z3.extent = z3.transform.forwardTransform(z3.nativeExtent), z3.pixelSize = new w2({ x: (D4.xmax - D4.xmin) / r5, y: (D4.ymax - D4.ymin) / i4, spatialReference: w8 }), P.origin.x = -0.5, P.origin.y = 0.5), { imageInfo: t5, rasterInfo: z3 };
  }
  _processPAMInfo(e4, t5) {
    if (t5.statistics = e4.statistics ?? t5.statistics, t5.histograms = e4.histograms, e4.histograms && t(t5.statistics) && (t5.statistics = g2(e4.histograms)), e4.transform && t(t5.transform)) {
      t5.transform = e4.transform, t5.nativeExtent = t5.extent;
      const r5 = t5.transform.forwardTransform(t5.nativeExtent);
      t5.pixelSize = new w2({ x: (r5.xmax - r5.xmin) / t5.width, y: (r5.ymax - r5.ymin) / t5.height, spatialReference: t5.spatialReference }), t5.extent = r5;
    }
    t5.isPseudoSpatialReference && e4.spatialReference && (t5.spatialReference = e4.spatialReference);
  }
  async _readIFDs(e4, t5, r5, i4, s4, a6 = 4, n9) {
    if (!i4) return null;
    if (i4 >= t5.byteLength || i4 < 0) {
      t5 = (await this.request(this.url, { range: { from: i4 + s4, to: i4 + s4 + this._bufferSize }, responseType: "array-buffer", signal: n9 })).data, s4 = i4 + s4, i4 = 0;
    }
    const o6 = await this._readIFD(t5, r5, i4, s4, n5.TIFF_TAGS, a6, n9);
    if (e4.push(o6.ifd), !o6.nextIFD) return null;
    await this._readIFDs(e4, t5, r5, o6.nextIFD - s4, s4, a6, n9);
  }
  async _readIFD(e4, t5, r5, s4, a6 = n5.TIFF_TAGS, n9 = 4, o6) {
    var _a, _b;
    if (!e4) return null;
    const f8 = B(e4, t5, r5, s4, a6, n9);
    if (f8.success) {
      const r6 = [];
      if ((_a = f8.ifd) == null ? void 0 : _a.forEach((e5) => {
        e5.values || r6.push(e5);
      }), r6.length > 0) {
        const a7 = r6.map((e5) => e5.offlineOffsetSize).filter(r), n10 = Math.min.apply(null, a7.map((e5) => e5[0]));
        if (Math.min.apply(null, a7.map((e5) => e5[0] + e5[1])) - n10 <= this._bufferSize) {
          const { data: i4 } = await this.request(this.url, { range: { from: n10, to: n10 + this._bufferSize }, responseType: "array-buffer", signal: o6 });
          e4 = i4, s4 = n10, r6.forEach((r7) => L2(e4, t5, r7, s4));
        }
      }
      if ((_b = f8.ifd) == null ? void 0 : _b.has("GEOKEYDIRECTORY")) {
        const r7 = f8.ifd.get("GEOKEYDIRECTORY"), i4 = r7 == null ? void 0 : r7.values;
        if (i4 && i4.length > 4) {
          const a7 = i4[0] + "." + i4[1] + "." + i4[2], n10 = await this._readIFD(e4, t5, r7.valueOffset + 6 - s4, s4, n5.GEO_KEYS, 2, o6);
          r7.data = n10.ifd, r7.data && r7.data.set("GEOTIFFVersion", { id: 0, type: 2, valueCount: 1, valueOffset: null, values: [a7] });
        }
      }
      return f8;
    }
    if (f8.requiredBufferSize && f8.requiredBufferSize !== e4.byteLength) {
      const r6 = await this.request(this.url, { range: { from: s4, to: s4 + f8.requiredBufferSize + 4 }, responseType: "array-buffer", signal: o6 });
      return (e4 = r6.data).byteLength < f8.requiredBufferSize ? null : this._readIFD(e4, t5, 0, s4, n5.TIFF_TAGS, 4, o6);
    }
  }
  async _fetchRawTiffTile(e4, t5, r5, i4, s4 = {}) {
    const a6 = this._getTileLocation(e4, t5, r5, i4);
    if (!a6) return null;
    const { ranges: n9, actualTileWidth: o6, actualTileHeight: f8, ifd: l5 } = a6, u5 = n9.map((e5) => this.request(this.url, { range: e5, responseType: "array-buffer", signal: s4.signal })), c10 = await Promise.all(u5), p8 = c10.map((e5) => e5.data.byteLength).reduce((e5, t6) => e5 + t6), m9 = 1 === c10.length ? c10[0].data : new ArrayBuffer(p8), h5 = [0], d7 = [0];
    if (c10.length > 1) {
      const e5 = new Uint8Array(m9);
      for (let t6 = 0, r6 = 0; t6 < c10.length; t6++) {
        const i5 = c10[t6].data;
        e5.set(new Uint8Array(i5), r6), h5[t6] = r6, r6 += i5.byteLength, d7[t6] = i5.byteLength;
      }
    }
    const { blockWidth: y4, blockHeight: g3 } = this.getBlockWidthHeight(e4), T4 = await this.decodePixelBlock(m9, { format: "tiff", customOptions: { headerInfo: this._headerInfo, ifd: l5, offsets: h5, sizes: d7 }, width: y4, height: g3, planes: null, pixelType: null });
    if (null == T4) return null;
    let x4, I4, w8;
    if (o6 !== y4 || f8 !== g3) {
      let e5 = T4.mask;
      if (e5) for (x4 = 0; x4 < g3; x4++) if (w8 = x4 * y4, x4 < f8) for (I4 = o6; I4 < y4; I4++) e5[w8 + I4] = 0;
      else for (I4 = 0; I4 < y4; I4++) e5[w8 + I4] = 0;
      else for (e5 = new Uint8Array(y4 * g3), T4.mask = e5, x4 = 0; x4 < f8; x4++) for (w8 = x4 * y4, I4 = 0; I4 < o6; I4++) e5[w8 + I4] = 1;
    }
    return T4;
  }
  _getTileLocation(e4, t5, r5, i4 = false) {
    const { firstPyramidLevel: s4, blockBoundary: a6 } = this.rasterInfo.storageInfo, n9 = 0 === e4 ? 0 : e4 - (s4 - 1), { _headerInfo: o6 } = this;
    if (!o6) return null;
    const f8 = i4 ? o6.maskIFDs[n9] : 0 === n9 ? o6 == null ? void 0 : o6.ifds[0] : o6 == null ? void 0 : o6.pyramidIFDs[n9 - 1];
    if (!f8) return null;
    const l5 = D2(f8, o6), u5 = E2(f8, "TILEOFFSETS");
    if (void 0 === u5) return null;
    const c10 = E2(f8, "TILEBYTECOUNTS"), { minRow: p8, minCol: m9, maxRow: h5, maxCol: d7 } = a6[n9];
    if (t5 > h5 || r5 > d7 || t5 < p8 || r5 < m9) return null;
    const y4 = k2(f8, "IMAGEWIDTH"), g3 = k2(f8, "IMAGELENGTH"), T4 = k2(f8, "TILEWIDTH"), I4 = k2(f8, "TILELENGTH"), w8 = l5 ? this.rasterInfo.bandCount : 1, _2 = w8 * t5 * (d7 + 1) + r5, b4 = [{ from: u5[_2], to: u5[_2 + w8 - 1] + c10[_2 + w8 - 1] - 1 }];
    if (l5) {
      let e5 = true;
      for (let t6 = 0; t6 < w8; t6++) if (u5[_2 + t6] + c10[_2 + t6] !== u5[_2 + t6 + 1]) {
        e5 = false;
        break;
      }
      if (!e5) for (let t6 = 0; t6 < w8; t6++) b4[t6] = { from: u5[_2 + t6], to: u5[_2 + t6] + c10[_2 + t6] - 1 };
    }
    const F3 = u5[_2], S6 = c10[_2];
    if (null == F3 || null == S6) return null;
    return { ranges: b4, ifd: f8, actualTileWidth: r5 === d7 && y4 % T4 || T4, actualTileHeight: t5 === h5 && g3 % I4 || I4 };
  }
  async _fetchAuxiliaryMetaData(e4) {
    try {
      const { data: t5 } = await this.request(this.url + ".aux.xml", { responseType: "xml", signal: e4 == null ? void 0 : e4.signal });
      return p7(t5);
    } catch {
      return null;
    }
  }
  async _fetchAuxiliaryTable(e4) {
    try {
      const { data: t5 } = await this.request(this.url + ".vat.dbf", { responseType: "array-buffer", signal: e4 == null ? void 0 : e4.signal }), r5 = r4.parse(t5);
      return (r5 == null ? void 0 : r5.recordSet) ? x.fromJSON(r5.recordSet) : null;
    } catch {
      return null;
    }
  }
};
e2([y()], D3.prototype, "_files", void 0), e2([y()], D3.prototype, "_headerInfo", void 0), e2([y()], D3.prototype, "_bufferSize", void 0), e2([y({ type: String, json: { write: true } })], D3.prototype, "datasetFormat", void 0), D3 = e2([a("esri.layers.support.rasterDatasets.TIFFRaster")], D3);
var v5 = D3;

// node_modules/@arcgis/core/layers/support/rasterDatasets/RasterFactory.js
var c9 = /* @__PURE__ */ new Map();
c9.set("CRF", { desc: "Cloud Raster Format", constructor: w5 }), c9.set("MRF", { desc: "Meta Raster Format", constructor: F2 }), c9.set("TIFF", { desc: "GeoTIFF", constructor: v5 }), c9.set("RasterTileServer", { desc: "Raster Tile Server", constructor: w7 }), c9.set("JPG", { desc: "JPG Raster Format", constructor: w6 }), c9.set("PNG", { desc: "PNG Raster Format", constructor: w6 }), c9.set("GIF", { desc: "GIF Raster Format", constructor: w6 }), c9.set("BMP", { desc: "BMP Raster Format", constructor: w6 });
var n8 = class {
  static get supportedFormats() {
    const t5 = /* @__PURE__ */ new Set();
    return c9.forEach((e4, r5) => t5.add(r5)), t5;
  }
  static async open(e4) {
    const { url: r5, ioConfig: s4, sourceJSON: o6 } = e4;
    let a6 = e4.datasetFormat;
    null == a6 && r5.lastIndexOf(".") && (a6 = r5.slice(r5.lastIndexOf(".") + 1).toUpperCase()), "OVR" === a6 || "TIF" === a6 ? a6 = "TIFF" : "JPG" !== a6 && "JPEG" !== a6 && "JFIF" !== a6 || (a6 = "JPG"), r5.toLowerCase().includes("/imageserver") && !r5.toLowerCase().includes("/wcsserver") && (a6 = "RasterTileServer");
    const n9 = { url: r5, sourceJSON: o6, datasetFormat: a6, ioConfig: s4 ?? { bandIds: null, sampling: null } };
    let l5, i4;
    if (a6 && this.supportedFormats.has(a6)) {
      if ("CRF" === a6 && !(s4 == null ? void 0 : s4.enableCRF)) throw new s2("rasterfactory:open", `cannot open raster: ${r5}`);
      return l5 = c9.get(a6).constructor, i4 = new l5(n9), await i4.open({ signal: e4.signal }), i4;
    }
    if (a6) throw new s2("rasterfactory:open", "not a supported format " + a6);
    const u5 = Array.from(c9.keys());
    let F3 = 0;
    const m9 = () => (a6 = u5[F3++], a6 && ("CRF" !== a6 || (s4 == null ? void 0 : s4.enableCRF)) ? (l5 = c9.get(a6).constructor, i4 = new l5(n9), i4.open({ signal: e4.signal }).then(() => i4).catch(() => m9())) : null);
    return m9();
  }
  static register(t5, e4, r5) {
    c9.has(t5.toUpperCase()) || c9.set(t5.toUpperCase(), { desc: e4, constructor: r5 });
  }
};

// node_modules/@arcgis/core/layers/ImageryTileLayer.js
var C3 = class extends n(t2(c3(_(o3(z2(a3(p3(p2(O(b)))))))))) {
  constructor(...e4) {
    super(...e4), this._primaryRasters = null, this.bandIds = null, this.interpolation = null, this.legendEnabled = true, this.isReference = null, this.listMode = "show", this.sourceJSON = null, this.version = null, this.type = "imagery-tile", this.operationalLayerType = "ArcGISTiledImageServiceLayer", this.popupEnabled = true, this.popupTemplate = null, this.fields = null;
  }
  normalizeCtorArgs(e4, r5) {
    return "string" == typeof e4 ? { url: e4, ...r5 } : e4;
  }
  load(e4) {
    const r5 = r(e4) ? e4.signal : null;
    return this.addResolvingPromise(this.loadFromPortal({ supportedTypes: ["Image Service"] }, e4).catch(w).then(() => this._openRaster(r5))), Promise.resolve(this);
  }
  get defaultPopupTemplate() {
    return this.createPopupTemplate();
  }
  get rasterFields() {
    var _a;
    let e4 = [new y2({ name: "Raster.ServicePixelValue", alias: "Pixel Value", domain: null, editable: false, length: 50, type: "string" })];
    const { rasterInfo: r5 } = this, t5 = r5 == null ? void 0 : r5.attributeTable, s4 = r(t5) ? t5.fields : null, i4 = "Raster.";
    if (s4) {
      const r6 = s4.filter((e5) => "oid" !== e5.type && "value" !== e5.name.toLowerCase()).map((e5) => {
        const r7 = e5.clone();
        return r7.name = i4 + e5.name, r7;
      });
      e4 = e4.concat(r6);
    }
    const o6 = r5 == null ? void 0 : r5.dataType, a6 = r5 == null ? void 0 : r5.multidimensionalInfo;
    if (("vector-magdir" === o6 || "vector-uv" === o6) && r(a6)) {
      const r6 = (_a = a6.variables[0].unit) == null ? void 0 : _a.trim(), t6 = "Magnitude" + (r6 ? ` (${r6})` : "");
      e4.push(new y2({ name: "Raster.Magnitude", alias: t6, domain: null, editable: false, type: "double" })), e4.push(new y2({ name: "Raster.Direction", alias: "Direction (°)", domain: null, editable: false, type: "double" }));
    }
    return e4;
  }
  set renderer(e4) {
    this._set("renderer", e4), this.updateRenderer();
  }
  readRenderer(e4, r5, s4) {
    const i4 = r5 && r5.layerDefinition && r5.layerDefinition.drawingInfo && r5.layerDefinition.drawingInfo.renderer, o6 = c4(i4, s4) || void 0;
    if (null != o6) return o6;
  }
  createPopupTemplate(e4) {
    return p4({ fields: this.rasterFields, title: this.title }, e4);
  }
  async generateRasterInfo(e4, r5) {
    if (!(e4 = v(w4, e4))) return this._primaryRasters[0].rasterInfo;
    try {
      const t5 = { raster: this._primaryRasters[0] };
      this._primaryRasters.length > 1 && this._primaryRasters.forEach((e5) => t5[e5.url] = e5);
      const s4 = C2(e4.toJSON(), t5), i4 = new c7({ rasterFunction: s4 });
      return await i4.open(r5), i4.rasterInfo;
    } catch {
      return null;
    }
  }
  write(e4, r5) {
    const { raster: t5 } = this;
    if (this.loaded ? "RasterTileServer" === t5.datasetFormat && ("Raster" === t5.tileType || "Map" === t5.tileType) : this.url && /\/ImageServer(\/|\/?$)/i.test(this.url)) return super.write(e4, r5);
    if (r5 && r5.messages) {
      const e5 = `${r5.origin}/${r5.layerContainerType || "operational-layers"}`;
      r5.messages.push(new s2("layer:unsupported", `Layers (${this.title}, ${this.id}) of type '${this.declaredClass}' are not supported in the context of '${e5}'`, { layer: this }));
    }
    return null;
  }
  async _openRaster(e4) {
    let r5 = false;
    if (this.raster) this.raster.rasterInfo || await this.raster.open(), "Function" === this.raster.datasetFormat ? (r5 = true, this._primaryRasters = this.raster.primaryRasters.rasters) : this._primaryRasters = [this.raster], this.url = this.raster.url;
    else {
      const { rasterFunction: r6 } = this, t6 = [this.url];
      r6 && h3(r6.toJSON(), t6);
      const i4 = await Promise.all(t6.map((r7) => n8.open({ url: r7, sourceJSON: this.sourceJSON, ioConfig: { sampling: "closest", ...this.ioConfig, customFetchParameters: this.customParameters }, signal: e4 }))), n9 = i4.findIndex((e5) => null == e5);
      if (n9 > -1) throw new s2("imagery-tile-layer:open", `cannot open raster: ${t6[n9]}`);
      if (this._primaryRasters = i4, r6) {
        const e5 = { raster: this._primaryRasters[0] };
        this._primaryRasters.length > 1 && this._primaryRasters.forEach((r7) => e5[r7.url] = r7);
        const t7 = C2(r6.rasterFunctionDefinition ?? r6.toJSON(), e5), n10 = new c7({ rasterFunction: t7 });
        try {
          await n10.open(), this.raster = n10;
        } catch (s4) {
          const e6 = s.getLogger(this.declaredClass);
          s4 instanceof s2 && e6.error("imagery-tile-layer:open", s4.message), e6.warn("imagery-tile-layer:open", "the raster function cannot be applied and is removed"), this._set("rasterFunction", null), this.raster = i4[0];
        }
      } else this.raster = i4[0];
    }
    const t5 = this.raster.rasterInfo;
    if (!t5) throw new s2("imagery-tile-layer:load", "cannot load resources on " + this.url);
    if (this._set("rasterInfo", r5 ? t5 : this._primaryRasters[0].rasterInfo), this._set("spatialReference", t5.spatialReference), this.sourceJSON = this.sourceJSON || this.raster.sourceJSON, null != this.sourceJSON) {
      const e5 = "Map" === this.raster.tileType && null != this.sourceJSON.minLOD && null != this.sourceJSON.maxLOD ? this.sourceJSON : { ...this.sourceJSON, minScale: 0, maxScale: 0 };
      this.read(e5, { origin: "service" });
    }
    this.title || (this.title = this.raster.datasetName), "Map" === this.raster.tileType && (this.popupEnabled = false), this._configDefaultSettings(), this.addHandles(l2(() => this.customParameters, (e5) => {
      this.raster && (this.raster.ioConfig.customFetchParameters = e5);
    }));
  }
};
e2([y()], C3.prototype, "_primaryRasters", void 0), e2([y({ type: [T], json: { write: { overridePolicy() {
  var _a;
  return { enabled: !this.loaded || "Raster" === this.raster.tileType || "0,1,2" !== ((_a = this.bandIds) == null ? void 0 : _a.join(",")) };
} } } })], C3.prototype, "bandIds", void 0), e2([y({ json: { write: { overridePolicy() {
  return { enabled: !this.loaded || "Raster" === this.raster.tileType || "bilinear" !== this.interpolation };
} } } }), o2(o4)], C3.prototype, "interpolation", void 0), e2([y(c2)], C3.prototype, "legendEnabled", void 0), e2([y({ type: Boolean, json: { read: false, write: { enabled: true, overridePolicy: () => ({ enabled: false }) } } })], C3.prototype, "isReference", void 0), e2([y({ type: ["show", "hide"] })], C3.prototype, "listMode", void 0), e2([y({ json: { read: true, write: true } })], C3.prototype, "blendMode", void 0), e2([y()], C3.prototype, "sourceJSON", void 0), e2([y({ readOnly: true, json: { origins: { service: { read: { source: "currentVersion" } } } } })], C3.prototype, "version", void 0), e2([y({ readOnly: true, json: { read: false } })], C3.prototype, "type", void 0), e2([y({ type: ["ArcGISTiledImageServiceLayer"] })], C3.prototype, "operationalLayerType", void 0), e2([y({ type: Boolean, value: true, json: { read: { source: "disablePopup", reader: (e4, r5) => !r5.disablePopup }, write: { target: "disablePopup", overridePolicy() {
  return { enabled: !this.loaded || "Raster" === this.raster.tileType };
}, writer(e4, r5, t5) {
  r5[t5] = !e4;
} } } })], C3.prototype, "popupEnabled", void 0), e2([y({ type: k, json: { read: { source: "popupInfo" }, write: { target: "popupInfo", overridePolicy() {
  return { enabled: !this.loaded || "Raster" === this.raster.tileType };
} } } })], C3.prototype, "popupTemplate", void 0), e2([y({ readOnly: true })], C3.prototype, "defaultPopupTemplate", null), e2([y({ readOnly: true, type: [y2] })], C3.prototype, "fields", void 0), e2([y({ readOnly: true, type: [y2] })], C3.prototype, "rasterFields", null), e2([y({ types: l3, json: { name: "layerDefinition.drawingInfo.renderer", write: { overridePolicy() {
  var _a;
  const e4 = "raster-stretch" === ((_a = this.renderer) == null ? void 0 : _a.type) && "none" === this.renderer.stretchType && !this.renderer.useGamma;
  return { enabled: !this.loaded || "Raster" === this.raster.tileType || !e4 };
} }, origins: { "web-scene": { types: d2, name: "layerDefinition.drawingInfo.renderer", write: { overridePolicy: (e4) => ({ enabled: e4 && "vector-field" !== e4.type && "flow" !== e4.type }) } } } } })], C3.prototype, "renderer", null), e2([o("renderer")], C3.prototype, "readRenderer", null), C3 = e2([a("esri.layers.ImageryTileLayer")], C3);
var E3 = C3;
export {
  E3 as default
};
//# sourceMappingURL=ImageryTileLayer-IBVKRIHA.js.map
