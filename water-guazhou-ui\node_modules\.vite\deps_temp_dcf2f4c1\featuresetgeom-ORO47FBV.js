import {
  f2 as f,
  u2 as u
} from "./chunk-FS4WSDY7.js";
import {
  O,
  R,
  S,
  h,
  m,
  p as p2,
  x
} from "./chunk-76V5FCU2.js";
import "./chunk-MYYUEN6M.js";
import {
  s
} from "./chunk-CF4Y76HG.js";
import {
  B,
  G,
  be,
  re
} from "./chunk-SGDQG3NL.js";
import "./chunk-6OFWBRK2.js";
import "./chunk-WC4DQSYX.js";
import "./chunk-2CLVPBYJ.js";
import {
  e,
  t
} from "./chunk-YFVPK4WM.js";
import "./chunk-6JIS2R4B.js";
import "./chunk-7BSY2CUN.js";
import "./chunk-6RN6WNY4.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-D3MAF4VS.js";
import "./chunk-2WMCP27R.js";
import "./chunk-3HW44BD3.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-U4SVMKOQ.js";
import {
  p
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/arcade/functions/featuresetgeom.js
function h2(e2) {
  return e2 instanceof p;
}
function S2(i, a, c, S3) {
  return S3(i, a, async (S4, R3, v) => {
    if (v.length < 2) throw new t(i, e.WrongNumberOfParameters, a);
    if (null === (v = be(v))[0] && null === v[1]) return false;
    if (G(v[0])) {
      if (v[1] instanceof p) return new f({ parentfeatureset: v[0], relation: c, relationGeom: v[1] });
      if (null === v[1]) return new u({ parentfeatureset: v[0] });
      throw new t(i, e.InvalidParameter, a);
    }
    if (h2(v[0])) {
      if (h2(v[1])) {
        switch (c) {
          case "esriSpatialRelEnvelopeIntersects":
            return h(s(v[0]), s(v[1]));
          case "esriSpatialRelIntersects":
            return h(v[0], v[1]);
          case "esriSpatialRelContains":
            return p2(v[0], v[1]);
          case "esriSpatialRelOverlaps":
            return O(v[0], v[1]);
          case "esriSpatialRelWithin":
            return x(v[0], v[1]);
          case "esriSpatialRelTouches":
            return S(v[0], v[1]);
          case "esriSpatialRelCrosses":
            return m(v[0], v[1]);
        }
        throw new t(i, e.InvalidParameter, a);
      }
      if (G(v[1])) return new f({ parentfeatureset: v[1], relation: c, relationGeom: v[0] });
      if (null === v[1]) return false;
      throw new t(i, e.InvalidParameter, a);
    }
    if (null !== v[0]) throw new t(i, e.InvalidParameter, a);
    return G(v[1]) ? new u({ parentfeatureset: v[1] }) : !(v[1] instanceof p || null === v[1]) && void 0;
  });
}
function R2(t2) {
  "async" === t2.mode && (t2.functions.intersects = function(e2, n) {
    return S2(e2, n, "esriSpatialRelIntersects", t2.standardFunctionAsync);
  }, t2.functions.envelopeintersects = function(e2, n) {
    return S2(e2, n, "esriSpatialRelEnvelopeIntersects", t2.standardFunctionAsync);
  }, t2.signatures.push({ name: "envelopeintersects", min: 2, max: 2 }), t2.functions.contains = function(e2, n) {
    return S2(e2, n, "esriSpatialRelContains", t2.standardFunctionAsync);
  }, t2.functions.overlaps = function(e2, n) {
    return S2(e2, n, "esriSpatialRelOverlaps", t2.standardFunctionAsync);
  }, t2.functions.within = function(e2, n) {
    return S2(e2, n, "esriSpatialRelWithin", t2.standardFunctionAsync);
  }, t2.functions.touches = function(e2, n) {
    return S2(e2, n, "esriSpatialRelTouches", t2.standardFunctionAsync);
  }, t2.functions.crosses = function(e2, n) {
    return S2(e2, n, "esriSpatialRelCrosses", t2.standardFunctionAsync);
  }, t2.functions.relate = function(u2, f2) {
    return t2.standardFunctionAsync(u2, f2, (t3, p3, m2) => {
      if (m2 = be(m2), B(m2, 3, 3, u2, f2), h2(m2[0]) && h2(m2[1])) return R(m2[0], m2[1], re(m2[2]));
      if (m2[0] instanceof p && null === m2[1]) return false;
      if (m2[1] instanceof p && null === m2[0]) return false;
      if (G(m2[0]) && null === m2[1]) return new u({ parentfeatureset: m2[0] });
      if (G(m2[1]) && null === m2[0]) return new u({ parentfeatureset: m2[1] });
      if (G(m2[0]) && m2[1] instanceof p) return m2[0].relate(m2[1], re(m2[2]));
      if (G(m2[1]) && m2[0] instanceof p) return m2[1].relate(m2[0], re(m2[2]));
      if (null === m2[0] && null === m2[1]) return false;
      throw new t(u2, e.InvalidParameter, f2);
    });
  });
}
export {
  R2 as registerFunctions
};
//# sourceMappingURL=featuresetgeom-ORO47FBV.js.map
