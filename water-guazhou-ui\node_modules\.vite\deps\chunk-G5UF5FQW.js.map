{"version": 3, "sources": ["../../echarts/lib/echarts.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nexport * from './export/core.js';\nimport { use } from './extension.js';\nimport { init } from './core/echarts.js';\nimport { install as CanvasRenderer } from './renderer/installCanvasRenderer.js';\nimport { install as DatasetComponent } from './component/dataset/install.js';\n// Default to have canvas renderer and dataset for compitatble reason.\nuse([CanvasRenderer, DatasetComponent]);\n// TODO: Compatitable with the following code\n// import echarts from 'echarts/lib/echarts.js'\nexport default {\n  init: function () {\n    if (process.env.NODE_ENV !== 'production') {\n      /* eslint-disable-next-line */\n      console.error(\"\\\"import echarts from 'echarts/lib/echarts.js'\\\" is not supported anymore. Use \\\"import * as echarts from 'echarts/lib/echarts.js'\\\" instead;\");\n    }\n    // @ts-ignore\n    return init.apply(null, arguments);\n  }\n};\n// Import label layout by default.\n// TODO remove\nimport { installLabelLayout } from './label/installLabelLayout.js';\nuse(installLabelLayout);"], "mappings": ";;;;;;;;;;;;;;AAiDA,IAAI,CAAC,SAAgBA,QAAgB,CAAC;AAgBtC,IAAI,kBAAkB;", "names": ["install"]}