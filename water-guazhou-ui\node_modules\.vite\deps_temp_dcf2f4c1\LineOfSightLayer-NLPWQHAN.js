import {
  g
} from "./chunk-L6LJUVNN.js";
import "./chunk-XX6IKIRW.js";
import {
  c as c3
} from "./chunk-PXLH6GWI.js";
import "./chunk-OWSDEANX.js";
import {
  i as i2
} from "./chunk-CDZ24ELJ.js";
import "./chunk-PCLDCFRI.js";
import {
  c as c2
} from "./chunk-FHKOFAQ2.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import {
  un
} from "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-67MHB3E3.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-YJWWP4AU.js";
import {
  x
} from "./chunk-EQYLMJEJ.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-N7ADFPOO.js";
import {
  c,
  m,
  u as u2
} from "./chunk-AVKOL7OR.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-37DYRJVQ.js";
import {
  n,
  t as t2
} from "./chunk-CTPXU2ZH.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import {
  i,
  l
} from "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import {
  l as l2,
  w as w2
} from "./chunk-QUHG7NMD.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import {
  w
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import {
  u2 as u
} from "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e as e2,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  d,
  e,
  o,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/analysis/featureReferenceUtils.js
function t3(e3, t4) {
  return l3(e3) === l3(t4);
}
function l3(t4) {
  if (t(t4)) return null;
  const l4 = null != t4.layer ? t4.layer.id : "";
  let r3 = null;
  return r3 = null != t4.objectId ? t4.objectId : null != t4.layer && "objectIdField" in t4.layer && null != t4.layer.objectIdField && null != t4.attributes ? t4.attributes[t4.layer.objectIdField] : t4.uid, null == r3 ? null : `o-${l4}-${r3}`;
}
var r2 = { json: { write: { writer: n2, target: { "feature.layerId": { type: [Number, String] }, "feature.objectId": { type: [Number, String] } } }, origins: { "web-scene": { read: u3 } } } };
function n2(t4, l4) {
  var _a;
  t(t4) || null == ((_a = t4.layer) == null ? void 0 : _a.objectIdField) || null == t4.attributes || (l4.feature = { layerId: t4.layer.id, objectId: t4.attributes[t4.layer.objectIdField] });
}
function u3(e3) {
  if (null != e3.layerId && null != e3.objectId) return { uid: null, layer: { id: e3.layerId, objectIdField: "ObjectId" }, attributes: { ObjectId: e3.objectId } };
}

// node_modules/@arcgis/core/analysis/LineOfSightAnalysisObserver.js
var f = class extends u(i(v)) {
  constructor(o2) {
    super(o2), this.position = null, this.elevationInfo = null, this.feature = null;
  }
  equals(o2) {
    return d(this.position, o2.position) && d(this.elevationInfo, o2.elevationInfo) && t3(this.feature, o2.feature);
  }
};
e2([y({ type: w }), g()], f.prototype, "position", void 0), e2([y({ type: x }), g()], f.prototype, "elevationInfo", void 0), e2([y(r2)], f.prototype, "feature", void 0), f = e2([a("esri.analysis.LineOfSightAnalysisObserver")], f);
var u4 = f;

// node_modules/@arcgis/core/analysis/LineOfSightAnalysisTarget.js
var m2 = class extends u(l) {
  constructor(o2) {
    super(o2), this.position = null, this.elevationInfo = null, this.feature = null;
  }
  equals(o2) {
    return d(this.position, o2.position) && d(this.elevationInfo, o2.elevationInfo) && t3(this.feature, o2.feature);
  }
};
e2([y({ type: w }), g()], m2.prototype, "position", void 0), e2([y({ type: x }), g()], m2.prototype, "elevationInfo", void 0), e2([y(r2)], m2.prototype, "feature", void 0), m2 = e2([a("esri.analysis.LineOfSightAnalysisTarget")], m2);
var f2 = m2;

// node_modules/@arcgis/core/analysis/LineOfSightAnalysis.js
var b2 = j.ofType(f2);
var x2 = class extends c3 {
  constructor(e3) {
    super(e3), this.type = "line-of-sight", this.observer = null, this.extent = null;
  }
  initialize() {
    this.addHandles(l2(() => this._computeExtent(), (e3) => {
      (t(e3) || t(e3.pending)) && this._set("extent", r(e3) ? e3.extent : null);
    }, w2));
  }
  get targets() {
    return this._get("targets") || new b2();
  }
  set targets(e3) {
    this._set("targets", n(e3, this.targets, b2));
  }
  get spatialReference() {
    return r(this.observer) && r(this.observer.position) ? this.observer.position.spatialReference : null;
  }
  get requiredPropertiesForEditing() {
    return [o(this.observer, (e3) => e3.position)];
  }
  async waitComputeExtent() {
    const e3 = this._computeExtent();
    return r(e3) ? e(e3.pending) : Promise.resolve();
  }
  _computeExtent() {
    const e3 = this.spatialReference;
    if (t(this.observer) || t(this.observer.position) || t(e3)) return null;
    const t4 = (e4) => "absolute-height" === i2(e4.position, e4.elevationInfo), r3 = this.observer.position, o2 = u2(r3.x, r3.y, r3.z, r3.x, r3.y, r3.z);
    for (const i3 of this.targets) if (r(i3.position)) {
      const t5 = un(i3.position, e3);
      if (r(t5.pending)) return { pending: t5.pending, extent: null };
      if (r(t5.geometry)) {
        const { x: e4, y: r4, z: s2 } = t5.geometry;
        c(o2, [e4, r4, s2]);
      }
    }
    const s = m(o2, e3);
    return t4(this.observer) && this.targets.every(t4) || (s.zmin = void 0, s.zmax = void 0), { pending: null, extent: s };
  }
  clear() {
    this.observer = null, this.targets.removeAll();
  }
};
e2([y({ type: ["line-of-sight"] })], x2.prototype, "type", void 0), e2([y({ type: u4, json: { read: true, write: true } })], x2.prototype, "observer", void 0), e2([y({ cast: t2, type: b2, nonNullable: true, json: { read: true, write: true } })], x2.prototype, "targets", null), e2([y({ value: null, readOnly: true })], x2.prototype, "extent", void 0), e2([y({ readOnly: true })], x2.prototype, "spatialReference", null), e2([y({ readOnly: true })], x2.prototype, "requiredPropertiesForEditing", null), x2 = e2([a("esri.analysis.LineOfSightAnalysis")], x2);
var O2 = x2;

// node_modules/@arcgis/core/layers/LineOfSightLayer.js
var f3 = j.ofType(f2);
var g2 = class extends c2(O(b)) {
  constructor(e3) {
    super(e3), this.type = "line-of-sight", this.operationalLayerType = "LineOfSightLayer", this.analysis = new O2(), this.opacity = 1;
  }
  initialize() {
    this.addHandles(l2(() => this.analysis, (e3, t4) => {
      r(t4) && t4.parent === this && (t4.parent = null), r(e3) && (e3.parent = this);
    }, w2));
  }
  async load() {
    return r(this.analysis) && this.addResolvingPromise(this.analysis.waitComputeExtent()), this;
  }
  get observer() {
    return o(this.analysis, (e3) => e3.observer);
  }
  set observer(e3) {
    o(this.analysis, (t4) => t4.observer = e3);
  }
  get targets() {
    return r(this.analysis) ? this.analysis.targets : new j();
  }
  set targets(e3) {
    var _a;
    n(e3, (_a = this.analysis) == null ? void 0 : _a.targets);
  }
  get fullExtent() {
    return r(this.analysis) ? this.analysis.extent : null;
  }
  get spatialReference() {
    return r(this.analysis) ? e(this.analysis.spatialReference) : null;
  }
  releaseAnalysis(e3) {
    this.analysis === e3 && (this.analysis = new O2());
  }
};
e2([y({ json: { read: false }, readOnly: true })], g2.prototype, "type", void 0), e2([y({ type: ["LineOfSightLayer"] })], g2.prototype, "operationalLayerType", void 0), e2([y({ type: u4, json: { read: true, write: { ignoreOrigin: true } } })], g2.prototype, "observer", null), e2([y({ type: f3, json: { read: true, write: { ignoreOrigin: true } } })], g2.prototype, "targets", null), e2([y({ nonNullable: true, json: { read: false, write: false } })], g2.prototype, "analysis", void 0), e2([y({ readOnly: true })], g2.prototype, "fullExtent", null), e2([y({ readOnly: true })], g2.prototype, "spatialReference", null), e2([y({ readOnly: true, json: { read: false, write: false, origins: { service: { read: false, write: false }, "portal-item": { read: false, write: false }, "web-document": { read: false, write: false } } } })], g2.prototype, "opacity", void 0), e2([y({ type: ["show", "hide"] })], g2.prototype, "listMode", void 0), g2 = e2([a("esri.layers.LineOfSightLayer")], g2);
var j2 = g2;
export {
  j2 as default
};
//# sourceMappingURL=LineOfSightLayer-NLPWQHAN.js.map
