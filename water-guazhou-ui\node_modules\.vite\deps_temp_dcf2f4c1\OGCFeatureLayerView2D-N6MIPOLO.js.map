{"version": 3, "sources": ["../../@arcgis/core/views/layers/OGCFeatureLayerView.js", "../../@arcgis/core/views/2d/layers/OGCFeatureLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";const o=o=>{let t=class extends o{get availableFields(){return this.layer.fieldsIndex.fields.map((e=>e.name))}};return e([r()],t.prototype,\"layer\",void 0),e([r({readOnly:!0})],t.prototype,\"availableFields\",null),t=e([s(\"esri.views.layers.OGCFeatureLayerView\")],t),t};export{o as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import\"../../../core/Logger.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import\"../../../core/Error.js\";import\"../../../core/has.js\";import{subclass as e}from\"../../../core/accessorSupport/decorators/subclass.js\";import s from\"./FeatureLayerView2D.js\";import o from\"../../layers/OGCFeatureLayerView.js\";let t=class extends(o(s)){supportsSpatialReference(r){return this.layer.serviceSupportsSpatialReference(r)}};t=r([e(\"esri.views.2d.layers.OGCFeatureLayerView2D\")],t);const a=t;export{a as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI0R,IAAM,IAAE,CAAAA,OAAG;AAAC,MAAIC,KAAE,cAAcD,GAAC;AAAA,IAAC,IAAI,kBAAiB;AAAC,aAAO,KAAK,MAAM,YAAY,OAAO,IAAK,CAAAE,OAAGA,GAAE,IAAK;AAAA,IAAC;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,mBAAkB,IAAI,GAAEA,KAAE,EAAE,CAAC,EAAE,uCAAuC,CAAC,GAAEA,EAAC,GAAEA;AAAC;;;ACApJ,IAAI,IAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,yBAAyB,GAAE;AAAC,WAAO,KAAK,MAAM,gCAAgC,CAAC;AAAA,EAAC;AAAC;AAAE,IAAE,EAAE,CAAC,EAAE,4CAA4C,CAAC,GAAE,CAAC;AAAE,IAAME,KAAE;", "names": ["o", "t", "e", "a"]}