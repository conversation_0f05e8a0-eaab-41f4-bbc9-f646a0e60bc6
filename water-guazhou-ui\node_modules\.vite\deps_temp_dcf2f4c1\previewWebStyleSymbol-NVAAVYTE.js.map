{"version": 3, "sources": ["../../@arcgis/core/symbols/support/previewWebStyleSymbol.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../request.js\";import{pt2px as e}from\"../../core/screenUtils.js\";import{SymbolSizeDefaults as i}from\"./previewUtils.js\";import{fetchWebStyleSymbol as n}from\"./utils.js\";function h(e,i,h){const s=e.thumbnail&&e.thumbnail.url;return s?t(s,{responseType:\"image\"}).then((t=>{const e=r(t.data,h);return h&&h.node?(h.node.appendChild(e),h.node):e})):n(e).then((t=>t?i(t,h):null))}function r(t,n){const h=!/\\\\.svg$/i.test(t.src)&&n&&n.disableUpsampling,r=Math.max(t.width,t.height);let s=n&&null!=n.maxSize?e(n.maxSize):i.maxSize;h&&(s=Math.min(r,s));const o=\"number\"==typeof n?.size?n?.size:null,m=Math.min(s,null!=o?e(o):r);if(m!==r){const e=0!==t.width&&0!==t.height?t.width/t.height:1;e>=1?(t.width=m,t.height=m/e):(t.width=m*e,t.height=m)}return t}export{h as previewWebStyleSymbol};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI0L,SAAS,EAAE,GAAE,GAAEA,IAAE;AAAC,QAAM,IAAE,EAAE,aAAW,EAAE,UAAU;AAAI,SAAO,IAAE,EAAE,GAAE,EAAC,cAAa,QAAO,CAAC,EAAE,KAAM,CAAAC,OAAG;AAAC,UAAMC,KAAE,EAAED,GAAE,MAAKD,EAAC;AAAE,WAAOA,MAAGA,GAAE,QAAMA,GAAE,KAAK,YAAYE,EAAC,GAAEF,GAAE,QAAME;AAAA,EAAC,CAAE,IAAE,EAAE,CAAC,EAAE,KAAM,CAAAD,OAAGA,KAAE,EAAEA,IAAED,EAAC,IAAE,IAAK;AAAC;AAAC,SAAS,EAAEC,IAAE,GAAE;AAAC,QAAMD,KAAE,CAAC,WAAW,KAAKC,GAAE,GAAG,KAAG,KAAG,EAAE,mBAAkBE,KAAE,KAAK,IAAIF,GAAE,OAAMA,GAAE,MAAM;AAAE,MAAI,IAAE,KAAG,QAAM,EAAE,UAAQ,EAAE,EAAE,OAAO,IAAE,EAAE;AAAQ,EAAAD,OAAI,IAAE,KAAK,IAAIG,IAAE,CAAC;AAAG,QAAM,IAAE,YAAU,QAAO,uBAAG,QAAK,uBAAG,OAAK,MAAK,IAAE,KAAK,IAAI,GAAE,QAAM,IAAE,EAAE,CAAC,IAAEA,EAAC;AAAE,MAAG,MAAIA,IAAE;AAAC,UAAM,IAAE,MAAIF,GAAE,SAAO,MAAIA,GAAE,SAAOA,GAAE,QAAMA,GAAE,SAAO;AAAE,SAAG,KAAGA,GAAE,QAAM,GAAEA,GAAE,SAAO,IAAE,MAAIA,GAAE,QAAM,IAAE,GAAEA,GAAE,SAAO;AAAA,EAAE;AAAC,SAAOA;AAAC;", "names": ["h", "t", "e", "r"]}