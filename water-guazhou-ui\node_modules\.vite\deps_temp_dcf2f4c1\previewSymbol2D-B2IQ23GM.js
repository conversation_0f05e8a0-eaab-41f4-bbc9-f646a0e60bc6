import {
  u as u2
} from "./chunk-GCMPBEAK.js";
import {
  n
} from "./chunk-KEY2Y5WF.js";
import {
  l as l2
} from "./chunk-QKHTZGC3.js";
import {
  d,
  e as e2,
  t
} from "./chunk-Q7KDWWJV.js";
import {
  f,
  h,
  y
} from "./chunk-44A5UMFM.js";
import "./chunk-ANH6666P.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-O2BYTJI4.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-IZLLLMFE.js";
import "./chunk-77E52HT5.js";
import "./chunk-2RO3UJ2R.js";
import "./chunk-P37TUI4J.js";
import "./chunk-IXMTA2RC.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-BNFBWJYJ.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-23VNIYCK.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-OVIRLBJH.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-4YKNTCHK.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-CGBA4LNQ.js";
import "./chunk-2RZLOKYO.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-HHMRK4FX.js";
import "./chunk-QMG7GZIF.js";
import {
  e,
  u
} from "./chunk-G5KX4JSG.js";
import {
  l
} from "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/symbols/support/previewSymbol2D.js
var p = "picture-fill";
var d2 = "picture-marker";
var f2 = "simple-fill";
var y2 = "simple-line";
var w = "simple-marker";
var g = "text";
var v = "Aa";
var b = t.size;
var x = t.maxSize;
var k = t.maxOutlineSize;
var M = t.lineWidth;
var z = 225;
var L = document.createElement("canvas");
function j(e3, t2) {
  const a = L.getContext("2d"), n2 = [];
  return t2 && (t2.weight && n2.push(t2.weight), t2.size && n2.push(t2.size + "px"), t2.family && n2.push(t2.family)), a.font = n2.join(" "), a.measureText(e3).width;
}
var C = 7.2 / 2.54;
var S = 72 / 2.54;
function F(e3) {
  if (0 === e3.length) return 0;
  if (e3.length > 2) {
    const t2 = e(1), a = parseFloat(e3);
    switch (e3.slice(-2)) {
      case "px":
        return a;
      case "pt":
        return a * t2;
      case "in":
        return 72 * a * t2;
      case "pc":
        return 12 * a * t2;
      case "mm":
        return a * C * t2;
      case "cm":
        return a * S * t2;
    }
  }
  return parseFloat(e3);
}
function E(e3) {
  const t2 = e3 == null ? void 0 : e3.size;
  return { width: null != t2 && "object" == typeof t2 && "width" in t2 ? u(t2.width) : null, height: null != t2 && "object" == typeof t2 && "height" in t2 ? u(t2.height) : null };
}
async function U(e3, t2) {
  const a = t2.fill, n2 = e3.color;
  if ("pattern" === (a == null ? void 0 : a.type) && n2 && e3.type !== p) {
    const e4 = await h(a.src, n2.toCss(true));
    a.src = e4, t2.fill = a;
  }
}
async function Z(e3, t2, n2, l3) {
  var _a, _b, _c;
  if (!("font" in e3) || !e3.font || "text" !== t2.shape.type) return;
  try {
    await n(e3.font);
  } catch {
  }
  const { width: i } = E(l3), s2 = /[\uE600-\uE6FF]/.test(t2.shape.text);
  null != i || s2 || (n2[0] = j(t2.shape.text, { weight: (_a = t2.font) == null ? void 0 : _a.weight, size: (_b = t2.font) == null ? void 0 : _b.size, family: (_c = t2.font) == null ? void 0 : _c.family }));
}
function q(e3, t2) {
  return e3 > t2 ? "dark" : "light";
}
function D(e3, t2) {
  var _a;
  const a = "number" == typeof (t2 == null ? void 0 : t2.size) ? t2 == null ? void 0 : t2.size : null, l3 = null != a ? u(a) : null, o = null != (t2 == null ? void 0 : t2.maxSize) ? u(t2.maxSize) : null, r = null != (t2 == null ? void 0 : t2.rotation) ? t2.rotation : "angle" in e3 ? e3.angle : null, m = f(e3);
  let u3 = y(e3);
  "dark" !== T(e3, 245) || (t2 == null ? void 0 : t2.ignoreWhiteSymbols) || (u3 = { width: 0.75, ...u3, color: "#bdc3c7" });
  const h2 = { shape: null, fill: m, stroke: u3, offset: [0, 0] };
  (u3 == null ? void 0 : u3.width) && (u3.width = Math.min(u3.width, k));
  const z2 = (u3 == null ? void 0 : u3.width) || 0;
  let L2 = null != (t2 == null ? void 0 : t2.size) && (null == (t2 == null ? void 0 : t2.scale) || (t2 == null ? void 0 : t2.scale)), C2 = 0, S2 = 0, U2 = false;
  switch (e3.type) {
    case w: {
      const a2 = e3.style, { width: i, height: s2 } = E(t2), c = i === s2 && null != i ? i : null != l3 ? l3 : Math.min(u(e3.size), o || x);
      switch (C2 = c, S2 = c, a2) {
        case "circle":
          h2.shape = { type: "circle", cx: 0, cy: 0, r: 0.5 * c }, L2 || (C2 += z2, S2 += z2);
          break;
        case "cross":
          h2.shape = { type: "path", path: [{ command: "M", values: [0, 0.5 * S2] }, { command: "L", values: [C2, 0.5 * S2] }, { command: "M", values: [0.5 * C2, 0] }, { command: "L", values: [0.5 * C2, S2] }] };
          break;
        case "diamond":
          h2.shape = { type: "path", path: [{ command: "M", values: [0, 0.5 * S2] }, { command: "L", values: [0.5 * C2, 0] }, { command: "L", values: [C2, 0.5 * S2] }, { command: "L", values: [0.5 * C2, S2] }, { command: "Z", values: [] }] }, L2 || (C2 += z2, S2 += z2);
          break;
        case "square":
          h2.shape = { type: "path", path: [{ command: "M", values: [0, 0] }, { command: "L", values: [C2, 0] }, { command: "L", values: [C2, S2] }, { command: "L", values: [0, S2] }, { command: "Z", values: [] }] }, L2 || (C2 += z2, S2 += z2), r && (U2 = true);
          break;
        case "triangle":
          h2.shape = { type: "path", path: [{ command: "M", values: [0.5 * C2, 0] }, { command: "L", values: [C2, S2] }, { command: "L", values: [0, S2] }, { command: "Z", values: [] }] }, L2 || (C2 += z2, S2 += z2), r && (U2 = true);
          break;
        case "x":
          h2.shape = { type: "path", path: [{ command: "M", values: [0, 0] }, { command: "L", values: [C2, S2] }, { command: "M", values: [C2, 0] }, { command: "L", values: [0, S2] }] }, r && (U2 = true);
          break;
        case "path":
          h2.shape = { type: "path", path: e3.path || "" }, L2 || (C2 += z2, S2 += z2), r && (U2 = true), L2 = true;
      }
      break;
    }
    case y2: {
      const { width: e4, height: a2 } = E(t2), n2 = null != a2 ? a2 : null != l3 ? l3 : z2, i = null != e4 ? e4 : M;
      u3 && (u3.width = n2), C2 = i, S2 = n2;
      const s2 = ((_a = h2 == null ? void 0 : h2.stroke) == null ? void 0 : _a.cap) || "butt", o2 = "round" === s2;
      L2 = true, h2.stroke && (h2.stroke.cap = "butt" === s2 ? "square" : s2), h2.shape = { type: "path", path: [{ command: "M", values: [o2 ? n2 / 2 : 0, S2 / 2] }, { command: "L", values: [o2 ? C2 - n2 / 2 : C2, S2 / 2] }] };
      break;
    }
    case p:
    case f2: {
      const e4 = "object" == typeof (t2 == null ? void 0 : t2.symbolConfig) && (t2 == null ? void 0 : t2.symbolConfig.isSquareFill), { width: a2, height: n2 } = E(t2);
      C2 = !e4 && a2 !== n2 || null == a2 ? null != l3 ? l3 : b : a2, S2 = !e4 && a2 !== n2 || null == n2 ? C2 : n2, L2 || (C2 += z2, S2 += z2), L2 = true, h2.shape = e4 ? { type: "path", path: [{ command: "M", values: [0, 0] }, { command: "L", values: [C2, 0] }, { command: "L", values: [C2, S2] }, { command: "L", values: [0, S2] }, { command: "L", values: [0, 0] }, { command: "Z", values: [] }] } : e2.fill[0];
      break;
    }
    case d2: {
      const a2 = Math.min(u(e3.width), o || x), i = Math.min(u(e3.height), o || x), { width: s2, height: c } = E(t2), m2 = s2 === c && null != s2 ? s2 : null != l3 ? l3 : Math.max(a2, i), u4 = a2 / i;
      C2 = u4 <= 1 ? Math.ceil(m2 * u4) : m2, S2 = u4 <= 1 ? m2 : Math.ceil(m2 / u4), h2.shape = { type: "image", x: -Math.round(C2 / 2), y: -Math.round(S2 / 2), width: C2, height: S2, src: e3.url || "" }, r && (U2 = true);
      break;
    }
    case g: {
      const a2 = e3, i = (t2 == null ? void 0 : t2.overrideText) || a2.text || v, s2 = a2.font, { width: r2, height: c } = E(t2), m2 = null != c ? c : null != l3 ? l3 : Math.min(u(s2.size), o || x), u4 = j(i, { weight: s2.weight, size: m2, family: s2.family }), p2 = /[\uE600-\uE6FF]/.test(i);
      C2 = r2 ?? (p2 ? m2 : u4), S2 = m2;
      let d3 = 0.25 * F((s2 ? m2 : 0).toString());
      p2 && (d3 += 5), h2.shape = { type: "text", text: i, x: a2.xoffset || 0, y: a2.yoffset || d3, align: "middle", alignBaseline: a2.verticalAlignment, decoration: s2 && s2.decoration, rotated: a2.rotated, kerning: a2.kerning }, h2.font = s2 && { size: m2, style: s2.style, decoration: s2.decoration, weight: s2.weight, family: s2.family };
      break;
    }
  }
  return { shapeDescriptor: h2, size: [C2, S2], renderOptions: { node: t2 == null ? void 0 : t2.node, scale: L2, opacity: t2 == null ? void 0 : t2.opacity, rotation: r, useRotationSize: U2, effectView: t2 == null ? void 0 : t2.effectView } };
}
async function O(e3, a) {
  const { shapeDescriptor: n2, size: l3, renderOptions: i } = D(e3, a);
  if (!n2.shape) throw new s("symbolPreview: renderPreviewHTML2D", "symbol not supported.");
  await U(e3, n2), await Z(e3, n2, l3, a);
  const s2 = [[n2]];
  if ("object" == typeof (a == null ? void 0 : a.symbolConfig) && (a == null ? void 0 : a.symbolConfig.applyColorModulation)) {
    const e4 = 0.6 * l3[0];
    s2.unshift([{ ...n2, offset: [-e4, 0], fill: d(n2.fill, -0.3) }]), s2.push([{ ...n2, offset: [e4, 0], fill: d(n2.fill, 0.3) }]), l3[0] += 2 * e4, i.scale = false;
  }
  return l2(s2, l3, i);
}
function T(t2, a = z) {
  const n2 = f(t2), l3 = y(t2), o = !n2 || "type" in n2 ? null : new l(n2), r = (l3 == null ? void 0 : l3.color) ? new l(l3 == null ? void 0 : l3.color) : null, c = o ? q(u2(o), a) : null, m = r ? q(u2(r), a) : null;
  return m ? c ? c === m ? c : a >= z ? "light" : "dark" : m : c;
}
export {
  T as getContrastingBackgroundTheme,
  D as getRenderSymbolParameters,
  O as previewSymbol2D
};
//# sourceMappingURL=previewSymbol2D-B2IQ23GM.js.map
