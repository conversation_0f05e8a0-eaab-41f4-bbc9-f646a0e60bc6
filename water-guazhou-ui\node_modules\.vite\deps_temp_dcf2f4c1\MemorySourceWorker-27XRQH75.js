import {
  n,
  t as t2
} from "./chunk-W42VJAAS.js";
import {
  a as a2,
  f as f2,
  g as g3,
  m,
  w
} from "./chunk-55OAEKKJ.js";
import {
  g as g2
} from "./chunk-WP7DEIEE.js";
import "./chunk-TZ4YPYDT.js";
import {
  ee
} from "./chunk-KU2IHOQF.js";
import "./chunk-DJASJBTH.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-ZK5O2DLX.js";
import "./chunk-CCFNWAA2.js";
import {
  a,
  i as i2,
  o
} from "./chunk-D7BTYVTV.js";
import "./chunk-JLELSJK5.js";
import "./chunk-FDLE77SA.js";
import "./chunk-6DAQTVXB.js";
import "./chunk-NQB6UCZ5.js";
import "./chunk-2CLVPBYJ.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-M4ZUXRA3.js";
import "./chunk-WJKHSSMC.js";
import {
  f,
  g
} from "./chunk-J5A2YARY.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-ZQY4DQCR.js";
import "./chunk-FRO3RSRO.js";
import "./chunk-4VO6N7OL.js";
import "./chunk-7VXHHPI3.js";
import "./chunk-2RO3UJ2R.js";
import "./chunk-ONE6GLG5.js";
import {
  nt,
  ot,
  st
} from "./chunk-DBDYNPQT.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-4DICTWL2.js";
import "./chunk-WGU7CS6R.js";
import "./chunk-4JOAXMIS.js";
import "./chunk-UYHYCNVE.js";
import "./chunk-INCFUNSS.js";
import {
  r as r2
} from "./chunk-D3MAF4VS.js";
import "./chunk-3HW44BD3.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-FZ7BG3VX.js";
import {
  i
} from "./chunk-DHWMTT76.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-AVKOL7OR.js";
import {
  M
} from "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import {
  c as c2,
  s as s2
} from "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  c
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/graphics/sources/support/MemorySourceWorker.js
var R = c;
var q = { xmin: -180, ymin: -90, xmax: 180, ymax: 90, spatialReference: c };
var w2 = { hasAttachments: false, capabilities: "query, editing, create, delete, update", useStandardizedQueries: true, supportsCoordinatesQuantization: true, supportsReturningQueryGeometry: true, advancedQueryCapabilities: { supportsQueryAttachments: false, supportsStatistics: true, supportsPercentileStatistics: true, supportsReturningGeometryCentroid: true, supportsQueryWithDistance: true, supportsDistinct: true, supportsReturningQueryExtent: true, supportsReturningGeometryProperties: false, supportsHavingClause: true, supportsOrderBy: true, supportsPagination: true, supportsQueryWithResultType: false, supportsSqlExpression: true, supportsDisjointSpatialRel: true } };
function D(e) {
  return s2(e) ? null != e.z : !!e.hasZ;
}
function O(e) {
  return s2(e) ? null != e.m : !!e.hasM;
}
var S = class {
  constructor() {
    this._queryEngine = null, this._nextObjectId = null;
  }
  destroy() {
    this._queryEngine && this._queryEngine && this._queryEngine.destroy(), this._queryEngine = this._fieldsIndex = this._createDefaultAttributes = null;
  }
  async load(t3) {
    const i3 = [], { features: s3 } = t3, r3 = this._inferLayerProperties(s3, t3.fields), n2 = t3.fields || [], a3 = null != t3.hasM ? t3.hasM : !!r3.hasM, o2 = null != t3.hasZ ? t3.hasZ : !!r3.hasZ, l = !t3.spatialReference && !r3.spatialReference, c3 = l ? R : t3.spatialReference || r3.spatialReference, I = l ? q : null, b = t3.geometryType || r3.geometryType, F = !b;
    let j = t3.objectIdField || r3.objectIdField, _ = t3.timeInfo;
    if (!F && (l && i3.push({ name: "feature-layer:spatial-reference-not-found", message: "Spatial reference not provided or found in features. Defaults to WGS84" }), !b)) throw new s("feature-layer:missing-property", "geometryType not set and couldn't be inferred from the provided features");
    if (!j) throw new s("feature-layer:missing-property", "objectIdField not set and couldn't be found in the provided fields");
    if (r3.objectIdField && j !== r3.objectIdField && (i3.push({ name: "feature-layer:duplicated-oid-field", message: `Provided objectIdField "${j}" doesn't match the field name "${r3.objectIdField}", found in the provided fields` }), j = r3.objectIdField), j && !r3.objectIdField) {
      let e = null;
      n2.some((t4) => t4.name === j && (e = t4, true)) ? (e.type = "esriFieldTypeOID", e.editable = false, e.nullable = false) : n2.unshift({ alias: j, name: j, type: "esriFieldTypeOID", editable: false, nullable: false });
    }
    for (const d of n2) {
      if (null == d.name && (d.name = d.alias), null == d.alias && (d.alias = d.name), !d.name) throw new s("feature-layer:invalid-field-name", "field name is missing", { field: d });
      if (d.name === j && (d.type = "esriFieldTypeOID"), !i.jsonValues.includes(d.type)) throw new s("feature-layer:invalid-field-type", `invalid type for field "${d.name}"`, { field: d });
    }
    const D2 = {};
    for (const e of n2) if ("esriFieldTypeOID" !== e.type && "esriFieldTypeGlobalID" !== e.type) {
      const t4 = M(e);
      void 0 !== t4 && (D2[e.name] = t4);
    }
    if (this._fieldsIndex = new r2(n2), this._createDefaultAttributes = i2(D2, j), _) {
      if (_.startTimeField) {
        const e = this._fieldsIndex.get(_.startTimeField);
        e ? (_.startTimeField = e.name, e.type = "esriFieldTypeDate") : _.startTimeField = null;
      }
      if (_.endTimeField) {
        const e = this._fieldsIndex.get(_.endTimeField);
        e ? (_.endTimeField = e.name, e.type = "esriFieldTypeDate") : _.endTimeField = null;
      }
      if (_.trackIdField) {
        const e = this._fieldsIndex.get(_.trackIdField);
        e ? _.trackIdField = e.name : (_.trackIdField = null, i3.push({ name: "feature-layer:invalid-timeInfo-trackIdField", message: "trackIdField is missing", details: { timeInfo: _ } }));
      }
      _.startTimeField || _.endTimeField || (i3.push({ name: "feature-layer:invalid-timeInfo", message: "startTimeField and endTimeField are missing or invalid", details: { timeInfo: _ } }), _ = null);
    }
    const O2 = { warnings: i3, featureErrors: [], layerDefinition: { ...w2, drawingInfo: o(b), templates: a(D2), extent: I, geometryType: b, objectIdField: j, fields: n2, hasZ: o2, hasM: a3, timeInfo: _ }, assignedObjectIds: {} };
    if (this._queryEngine = new ee({ fields: n2, geometryType: b, hasM: a3, hasZ: o2, objectIdField: j, spatialReference: c3, featureStore: new g2({ geometryType: b, hasM: a3, hasZ: o2 }), timeInfo: _, cacheSpatialQueries: true }), !s3 || !s3.length) return this._nextObjectId = t2, O2;
    const S2 = n(j, s3);
    return this._nextObjectId = S2 + 1, await f(s3, c3), this._loadInitialFeatures(O2, s3);
  }
  async applyEdits(e) {
    const { spatialReference: t3, geometryType: i3 } = this._queryEngine;
    return await Promise.all([w(t3, i3), f(e.adds, t3), f(e.updates, t3)]), this._applyEdits(e);
  }
  queryFeatures(e, t3 = {}) {
    return this._queryEngine.executeQuery(e, t3.signal);
  }
  queryFeatureCount(e, t3 = {}) {
    return this._queryEngine.executeQueryForCount(e, t3.signal);
  }
  queryObjectIds(e, t3 = {}) {
    return this._queryEngine.executeQueryForIds(e, t3.signal);
  }
  queryExtent(e, t3 = {}) {
    return this._queryEngine.executeQueryForExtent(e, t3.signal);
  }
  querySnapping(e, t3 = {}) {
    return this._queryEngine.executeQueryForSnapping(e, t3.signal);
  }
  _inferLayerProperties(e, i3) {
    let r3, n2, a3 = null, o2 = null, l = null;
    for (const d of e) {
      const e2 = d.geometry;
      if (!t(e2) && (a3 || (a3 = c2(e2)), o2 || (o2 = e2.spatialReference), null == r3 && (r3 = D(e2)), null == n2 && (n2 = O(e2)), a3 && o2 && null != r3 && null != n2)) break;
    }
    if (i3 && i3.length) {
      let e2 = null;
      i3.some((t3) => {
        const i4 = "esriFieldTypeOID" === t3.type, s3 = !t3.type && t3.name && "objectid" === t3.name.toLowerCase();
        return e2 = t3, i4 || s3;
      }) && (l = e2.name);
    }
    return { geometryType: a3, spatialReference: o2, objectIdField: l, hasM: n2, hasZ: r3 };
  }
  async _loadInitialFeatures(e, t3) {
    const { geometryType: r3, hasM: n2, hasZ: o2, objectIdField: l, spatialReference: d, featureStore: u } = this._queryEngine, p = [];
    for (const a3 of t3) {
      if (null != a3.uid && (e.assignedObjectIds[a3.uid] = -1), a3.geometry && r3 !== c2(a3.geometry)) {
        e.featureErrors.push(a2("Incorrect geometry type."));
        continue;
      }
      const t4 = this._createDefaultAttributes(), n3 = m(this._fieldsIndex, t4, a3.attributes, true, e.warnings);
      n3 ? e.featureErrors.push(n3) : (this._assignObjectId(t4, a3.attributes, true), a3.attributes = t4, null != a3.uid && (e.assignedObjectIds[a3.uid] = a3.attributes[l]), r(a3.geometry) && (a3.geometry = g(a3.geometry, a3.geometry.spatialReference, d)), p.push(a3));
    }
    u.addMany(nt([], p, r3, o2, n2, l));
    const { fullExtent: f3, timeExtent: y } = await this._queryEngine.fetchRecomputedExtents();
    if (e.layerDefinition.extent = f3, y) {
      const { start: t4, end: i3 } = y;
      e.layerDefinition.timeInfo.timeExtent = [t4, i3];
    }
    return e;
  }
  async _applyEdits(e) {
    const { adds: t3, updates: i3, deletes: s3 } = e, r3 = { addResults: [], deleteResults: [], updateResults: [], uidToObjectId: {} };
    if (t3 && t3.length && this._applyAddEdits(r3, t3), i3 && i3.length && this._applyUpdateEdits(r3, i3), s3 && s3.length) {
      for (const e2 of s3) r3.deleteResults.push(f2(e2));
      this._queryEngine.featureStore.removeManyById(s3);
    }
    const { fullExtent: n2, timeExtent: a3 } = await this._queryEngine.fetchRecomputedExtents();
    return { extent: n2, timeExtent: a3, featureEditResults: r3 };
  }
  _applyAddEdits(e, t3) {
    const { addResults: r3 } = e, { geometryType: n2, hasM: o2, hasZ: l, objectIdField: d, spatialReference: u, featureStore: p } = this._queryEngine, f3 = [];
    for (const a3 of t3) {
      if (a3.geometry && n2 !== c2(a3.geometry)) {
        r3.push(a2("Incorrect geometry type."));
        continue;
      }
      const t4 = this._createDefaultAttributes(), o3 = m(this._fieldsIndex, t4, a3.attributes);
      if (o3) r3.push(o3);
      else {
        if (this._assignObjectId(t4, a3.attributes), a3.attributes = t4, null != a3.uid) {
          const t5 = a3.attributes[d];
          e.uidToObjectId[a3.uid] = t5;
        }
        if (r(a3.geometry)) {
          const e2 = a3.geometry.spatialReference ?? u;
          a3.geometry = g(g3(a3.geometry, e2), e2, u);
        }
        f3.push(a3), r3.push(f2(a3.attributes[d]));
      }
    }
    p.addMany(nt([], f3, n2, l, o2, d));
  }
  _applyUpdateEdits({ updateResults: e }, t3) {
    const { geometryType: r3, hasM: n2, hasZ: a3, objectIdField: d, spatialReference: u, featureStore: p } = this._queryEngine;
    for (const f3 of t3) {
      const { attributes: t4, geometry: y } = f3, m2 = t4 && t4[d];
      if (null == m2) {
        e.push(a2(`Identifier field ${d} missing`));
        continue;
      }
      if (!p.has(m2)) {
        e.push(a2(`Feature with object id ${m2} missing`));
        continue;
      }
      const h = st(p.getFeature(m2), r3, a3, n2);
      if (r(y)) {
        if (r3 !== c2(y)) {
          e.push(a2("Incorrect geometry type."));
          continue;
        }
        const t5 = y.spatialReference ?? u;
        h.geometry = g(g3(y, t5), t5, u);
      }
      if (t4) {
        const i3 = m(this._fieldsIndex, h.attributes, t4);
        if (i3) {
          e.push(i3);
          continue;
        }
      }
      p.add(ot(h, r3, a3, n2, d)), e.push(f2(m2));
    }
  }
  _assignObjectId(e, t3, i3 = false) {
    const s3 = this._queryEngine.objectIdField;
    i3 && t3 && isFinite(t3[s3]) ? e[s3] = t3[s3] : e[s3] = this._nextObjectId++;
  }
};
export {
  S as default
};
//# sourceMappingURL=MemorySourceWorker-27XRQH75.js.map
