import {
  _,
  x
} from "./chunk-Y4E3DGVA.js";
import {
  ie
} from "./chunk-2KFL4KXW.js";
import {
  n
} from "./chunk-CHUBHVZP.js";
import {
  L,
  S,
  m
} from "./chunk-RFYOGM4H.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/labelFormatUtils.js
var c = s.getLogger("esri.layers.support.labelFormatUtils");
var f = { type: "simple", evaluate: () => null };
var p = { getAttribute: (e, r) => e.field(r) };
async function m2(r, a, n2) {
  if (!r || !r.symbol || !a) return f;
  const o = r.where, i = x(r), m3 = o ? await import("./WhereClause-3PEEQH24.js") : null;
  let g;
  if ("arcade" === i.type) {
    const r2 = await n(i.expression, n2, a);
    if (t(r2)) return f;
    g = { type: "arcade", evaluate(t2) {
      try {
        const e = r2.evaluate({ $feature: "attributes" in t2 ? r2.repurposeFeature(t2) : t2 });
        if (null != e) return e.toString();
      } catch (a2) {
        c.error(new s2("arcade-expression-error", "Encountered an error when evaluating label expression for feature", { feature: t2, expression: i }));
      }
      return null;
    }, needsHydrationToEvaluate: () => null == _(i.expression) };
  } else g = { type: "simple", evaluate: (e) => i.expression.replace(/{[^}]*}/g, (r2) => {
    const t2 = r2.slice(1, -1), n3 = a.get(t2);
    if (!n3) return r2;
    let o2 = null;
    if ("attributes" in e) {
      e && e.attributes && (o2 = e.attributes[n3.name]);
    } else o2 = e.field(n3.name);
    return null == o2 ? "" : d(o2, n3);
  }) };
  if (o) {
    let r2;
    try {
      r2 = m3.WhereClause.create(o, a);
    } catch (y) {
      return c.error(new s2("bad-where-clause", "Encountered an error when evaluating where clause, ignoring", { where: o, error: y })), f;
    }
    const t2 = g.evaluate;
    g.evaluate = (a2) => {
      const n3 = "attributes" in a2 ? void 0 : p;
      try {
        if (r2.testFeature(a2, n3)) return t2(a2);
      } catch (y) {
        c.error(new s2("bad-where-clause", "Encountered an error when evaluating where clause for feature", { where: o, feature: a2, error: y }));
      }
      return null;
    };
  }
  return g;
}
function d(e, r) {
  if (null == e) return "";
  const t2 = r.domain;
  if (t2) {
    if ("codedValue" === t2.type || "coded-value" === t2.type) {
      const r2 = e;
      for (const e2 of t2.codedValues) if (e2.code === r2) return e2.name;
    } else if ("range" === t2.type) {
      const r2 = +e, a = "range" in t2 ? t2.range[0] : t2.minValue, n2 = "range" in t2 ? t2.range[1] : t2.maxValue;
      if (a <= r2 && r2 <= n2) return t2.name;
    }
  }
  let l = e;
  return "date" === r.type || "esriFieldTypeDate" === r.type ? l = L(l, S("short-date")) : ie(r) && (l = m(+l)), l || "";
}
export {
  m2 as createLabelFunction,
  d as formatField
};
//# sourceMappingURL=labelFormatUtils-6NZQHW6W.js.map
