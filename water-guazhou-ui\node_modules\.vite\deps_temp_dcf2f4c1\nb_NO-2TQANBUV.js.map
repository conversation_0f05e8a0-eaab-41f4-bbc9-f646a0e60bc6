{"version": 3, "sources": ["../../@arcgis/core/chunks/nb_NO.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{g as e}from\"./_commonjsHelpers.js\";import{c as r}from\"./_commonjs-dynamic-modules.js\";function o(e,r){for(var o=0;o<r.length;o++){const a=r[o];if(\"string\"!=typeof a&&!Array.isArray(a))for(const r in a)if(\"default\"!==r&&!(r in e)){const o=Object.getOwnPropertyDescriptor(a,r);o&&Object.defineProperty(e,r,o.get?o:{enumerable:!0,get:()=>a[r]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}))}var a,t,_={},n={get exports(){return _},set exports(e){_=e}};a=n,void 0!==(t=function(e,r){Object.defineProperty(r,\"__esModule\",{value:!0}),r.default={_decimalSeparator:\",\",_thousandSeparator:\" \",_percentPrefix:null,_percentSuffix:\"%\",_big_number_suffix_3:\"k\",_big_number_suffix_6:\"M\",_big_number_suffix_9:\"G\",_big_number_suffix_12:\"T\",_big_number_suffix_15:\"P\",_big_number_suffix_18:\"E\",_big_number_suffix_21:\"Z\",_big_number_suffix_24:\"Y\",_small_number_suffix_3:\"m\",_small_number_suffix_6:\"μ\",_small_number_suffix_9:\"n\",_small_number_suffix_12:\"p\",_small_number_suffix_15:\"f\",_small_number_suffix_18:\"a\",_small_number_suffix_21:\"z\",_small_number_suffix_24:\"y\",_byte_suffix_B:\"B\",_byte_suffix_KB:\"KB\",_byte_suffix_MB:\"MB\",_byte_suffix_GB:\"GB\",_byte_suffix_TB:\"TB\",_byte_suffix_PB:\"PB\",_date_millisecond:\"mm:ss SSS\",_date_second:\"HH:mm:ss\",_date_minute:\"HH:mm\",_date_hour:\"HH:mm\",_date_day:\"MMM dd\",_date_week:\"ww\",_date_month:\"MMM\",_date_year:\"yyyy\",_duration_millisecond:\"SSS\",_duration_millisecond_second:\"ss.SSS\",_duration_millisecond_minute:\"mm:ss SSS\",_duration_millisecond_hour:\"hh:mm:ss SSS\",_duration_millisecond_day:\"d'd' mm:ss SSS\",_duration_millisecond_week:\"d'd' mm:ss SSS\",_duration_millisecond_month:\"M'm' dd'd' mm:ss SSS\",_duration_millisecond_year:\"y'y' MM'm' dd'd' mm:ss SSS\",_duration_second:\"ss\",_duration_second_minute:\"mm:ss\",_duration_second_hour:\"hh:mm:ss\",_duration_second_day:\"d'd' hh:mm:ss\",_duration_second_week:\"d'd' hh:mm:ss\",_duration_second_month:\"M'm' dd'd' hh:mm:ss\",_duration_second_year:\"y'y' MM'm' dd'd' hh:mm:ss\",_duration_minute:\"mm\",_duration_minute_hour:\"hh:mm\",_duration_minute_day:\"d'd' hh:mm\",_duration_minute_week:\"d'd' hh:mm\",_duration_minute_month:\"M'm' dd'd' hh:mm\",_duration_minute_year:\"y'y' MM'm' dd'd' hh:mm\",_duration_hour:\"hh'h'\",_duration_hour_day:\"d'd' hh'h'\",_duration_hour_week:\"d'd' hh'h'\",_duration_hour_month:\"M'm' dd'd' hh'h'\",_duration_hour_year:\"y'y' MM'm' dd'd' hh'h'\",_duration_day:\"d'd'\",_duration_day_week:\"d'd'\",_duration_day_month:\"M'm' dd'd'\",_duration_day_year:\"y'y' MM'm' dd'd'\",_duration_week:\"w'w'\",_duration_week_month:\"w'w'\",_duration_week_year:\"w'w'\",_duration_month:\"M'm'\",_duration_month_year:\"y'y' MM'm'\",_duration_year:\"y'y'\",_era_ad:\"e.Kr.\",_era_bc:\"f.Kr.\",A:\"a\",P:\"p\",AM:\"a.m.\",PM:\"p.m.\",\"A.M.\":\"a.m.\",\"P.M.\":\"p.m.\",January:\"januar\",February:\"februar\",March:\"mars\",April:\"april\",May:\"mai\",June:\"juni\",July:\"juli\",August:\"august\",September:\"september\",October:\"oktober\",November:\"november\",December:\"desember\",Jan:\"jan.\",Feb:\"feb.\",Mar:\"mar.\",Apr:\"apr.\",\"May(short)\":\"mai\",Jun:\"jun.\",Jul:\"jul.\",Aug:\"aug.\",Sep:\"sep.\",Oct:\"okt.\",Nov:\"nov.\",Dec:\"des.\",Sunday:\"søndag\",Monday:\"mandag\",Tuesday:\"tirsdag\",Wednesday:\"onsdag\",Thursday:\"torsdag\",Friday:\"fredag\",Saturday:\"lørdag\",Sun:\"søn.\",Mon:\"man.\",Tue:\"tir.\",Wed:\"ons.\",Thu:\"tor.\",Fri:\"fre.\",Sat:\"lør.\",_dateOrd:function(e){var r=\"th\";if(e<11||e>13)switch(e%10){case 1:r=\"st\";break;case 2:r=\"nd\";break;case 3:r=\"rd\"}return r},\"Zoom Out\":\"Zoom\",Play:\"Spill av\",Stop:\"Stopp\",Legend:\"Tegnforklaring\",\"Click, tap or press ENTER to toggle\":\"\",Loading:\"Laster inn\",Home:\"Hjem\",Chart:\"\",\"Serial chart\":\"\",\"X/Y chart\":\"\",\"Pie chart\":\"\",\"Gauge chart\":\"\",\"Radar chart\":\"\",\"Sankey diagram\":\"\",\"Flow diagram\":\"\",\"Chord diagram\":\"\",\"TreeMap chart\":\"\",\"Sliced chart\":\"\",Series:\"\",\"Candlestick Series\":\"\",\"OHLC Series\":\"\",\"Column Series\":\"\",\"Line Series\":\"\",\"Pie Slice Series\":\"\",\"Funnel Series\":\"\",\"Pyramid Series\":\"\",\"X/Y Series\":\"\",Map:\"\",\"Press ENTER to zoom in\":\"\",\"Press ENTER to zoom out\":\"\",\"Use arrow keys to zoom in and out\":\"\",\"Use plus and minus keys on your keyboard to zoom in and out\":\"\",Export:\"Skriv ut\",Image:\"Bilde\",Data:\"Data\",Print:\"Skriv ut\",\"Click, tap or press ENTER to open\":\"\",\"Click, tap or press ENTER to print.\":\"\",\"Click, tap or press ENTER to export as %1.\":\"\",'To save the image, right-click this link and choose \"Save picture as...\"':\"\",'To save the image, right-click thumbnail on the left and choose \"Save picture as...\"':\"\",\"(Press ESC to close this message)\":\"\",\"Image Export Complete\":\"\",\"Export operation took longer than expected. Something might have gone wrong.\":\"\",\"Saved from\":\"\",PNG:\"\",JPG:\"\",GIF:\"\",SVG:\"\",PDF:\"\",JSON:\"\",CSV:\"\",XLSX:\"\",\"Use TAB to select grip buttons or left and right arrows to change selection\":\"\",\"Use left and right arrows to move selection\":\"\",\"Use left and right arrows to move left selection\":\"\",\"Use left and right arrows to move right selection\":\"\",\"Use TAB select grip buttons or up and down arrows to change selection\":\"\",\"Use up and down arrows to move selection\":\"\",\"Use up and down arrows to move lower selection\":\"\",\"Use up and down arrows to move upper selection\":\"\",\"From %1 to %2\":\"Fra %1 til %2\",\"From %1\":\"Fra %1\",\"To %1\":\"Til %1\",\"No parser available for file: %1\":\"\",\"Error parsing file: %1\":\"\",\"Unable to load file: %1\":\"\",\"Invalid date\":\"\"}}(r,_))&&(a.exports=t);const i=o({__proto__:null,default:e(_)},[_]);export{i as n};\n"], "mappings": ";;;;;;;;;AAI6F,SAASA,GAAE,GAAEC,IAAE;AAAC,WAAQD,KAAE,GAAEA,KAAEC,GAAE,QAAOD,MAAI;AAAC,UAAME,KAAED,GAAED,EAAC;AAAE,QAAG,YAAU,OAAOE,MAAG,CAAC,MAAM,QAAQA,EAAC;AAAE,iBAAUD,MAAKC,GAAE,KAAG,cAAYD,MAAG,EAAEA,MAAK,IAAG;AAAC,cAAMD,KAAE,OAAO,yBAAyBE,IAAED,EAAC;AAAE,QAAAD,MAAG,OAAO,eAAe,GAAEC,IAAED,GAAE,MAAIA,KAAE,EAAC,YAAW,MAAG,KAAI,MAAIE,GAAED,EAAC,EAAC,CAAC;AAAA,MAAC;AAAA;AAAA,EAAC;AAAC,SAAO,OAAO,OAAO,OAAO,eAAe,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;AAAC;AAAC,IAAI;AAAJ,IAAM;AAAN,IAAQ,IAAE,CAAC;AAAX,IAAa,IAAE,EAAC,IAAI,UAAS;AAAC,SAAO;AAAC,GAAE,IAAI,QAAQ,GAAE;AAAC,MAAE;AAAC,EAAC;AAAE,IAAE,GAAE,YAAU,IAAE,SAAS,GAAEA,IAAE;AAAC,SAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAEA,GAAE,UAAQ,EAAC,mBAAkB,KAAI,oBAAmB,KAAI,gBAAe,MAAK,gBAAe,KAAI,sBAAqB,KAAI,sBAAqB,KAAI,sBAAqB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,wBAAuB,KAAI,wBAAuB,KAAI,wBAAuB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,gBAAe,KAAI,iBAAgB,MAAK,iBAAgB,MAAK,iBAAgB,MAAK,iBAAgB,MAAK,iBAAgB,MAAK,mBAAkB,aAAY,cAAa,YAAW,cAAa,SAAQ,YAAW,SAAQ,WAAU,UAAS,YAAW,MAAK,aAAY,OAAM,YAAW,QAAO,uBAAsB,OAAM,8BAA6B,UAAS,8BAA6B,aAAY,4BAA2B,gBAAe,2BAA0B,kBAAiB,4BAA2B,kBAAiB,6BAA4B,wBAAuB,4BAA2B,8BAA6B,kBAAiB,MAAK,yBAAwB,SAAQ,uBAAsB,YAAW,sBAAqB,iBAAgB,uBAAsB,iBAAgB,wBAAuB,uBAAsB,uBAAsB,6BAA4B,kBAAiB,MAAK,uBAAsB,SAAQ,sBAAqB,cAAa,uBAAsB,cAAa,wBAAuB,oBAAmB,uBAAsB,0BAAyB,gBAAe,SAAQ,oBAAmB,cAAa,qBAAoB,cAAa,sBAAqB,oBAAmB,qBAAoB,0BAAyB,eAAc,QAAO,oBAAmB,QAAO,qBAAoB,cAAa,oBAAmB,oBAAmB,gBAAe,QAAO,sBAAqB,QAAO,qBAAoB,QAAO,iBAAgB,QAAO,sBAAqB,cAAa,gBAAe,QAAO,SAAQ,SAAQ,SAAQ,SAAQ,GAAE,KAAI,GAAE,KAAI,IAAG,QAAO,IAAG,QAAO,QAAO,QAAO,QAAO,QAAO,SAAQ,UAAS,UAAS,WAAU,OAAM,QAAO,OAAM,SAAQ,KAAI,OAAM,MAAK,QAAO,MAAK,QAAO,QAAO,UAAS,WAAU,aAAY,SAAQ,WAAU,UAAS,YAAW,UAAS,YAAW,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,cAAa,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,QAAO,UAAS,QAAO,UAAS,SAAQ,WAAU,WAAU,UAAS,UAAS,WAAU,QAAO,UAAS,UAAS,UAAS,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,UAAS,SAASE,IAAE;AAAC,QAAIF,KAAE;AAAK,QAAGE,KAAE,MAAIA,KAAE,GAAG,SAAOA,KAAE,IAAG;AAAA,MAAC,KAAK;AAAE,QAAAF,KAAE;AAAK;AAAA,MAAM,KAAK;AAAE,QAAAA,KAAE;AAAK;AAAA,MAAM,KAAK;AAAE,QAAAA,KAAE;AAAA,IAAI;AAAC,WAAOA;AAAA,EAAC,GAAE,YAAW,QAAO,MAAK,YAAW,MAAK,SAAQ,QAAO,kBAAiB,uCAAsC,IAAG,SAAQ,cAAa,MAAK,QAAO,OAAM,IAAG,gBAAe,IAAG,aAAY,IAAG,aAAY,IAAG,eAAc,IAAG,eAAc,IAAG,kBAAiB,IAAG,gBAAe,IAAG,iBAAgB,IAAG,iBAAgB,IAAG,gBAAe,IAAG,QAAO,IAAG,sBAAqB,IAAG,eAAc,IAAG,iBAAgB,IAAG,eAAc,IAAG,oBAAmB,IAAG,iBAAgB,IAAG,kBAAiB,IAAG,cAAa,IAAG,KAAI,IAAG,0BAAyB,IAAG,2BAA0B,IAAG,qCAAoC,IAAG,+DAA8D,IAAG,QAAO,YAAW,OAAM,SAAQ,MAAK,QAAO,OAAM,YAAW,qCAAoC,IAAG,uCAAsC,IAAG,8CAA6C,IAAG,4EAA2E,IAAG,wFAAuF,IAAG,qCAAoC,IAAG,yBAAwB,IAAG,gFAA+E,IAAG,cAAa,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,MAAK,IAAG,KAAI,IAAG,MAAK,IAAG,+EAA8E,IAAG,+CAA8C,IAAG,oDAAmD,IAAG,qDAAoD,IAAG,yEAAwE,IAAG,4CAA2C,IAAG,kDAAiD,IAAG,kDAAiD,IAAG,iBAAgB,iBAAgB,WAAU,UAAS,SAAQ,UAAS,oCAAmC,IAAG,0BAAyB,IAAG,2BAA0B,IAAG,gBAAe,GAAE;AAAC,EAAE,GAAE,CAAC,OAAK,EAAE,UAAQ;AAAG,IAAM,IAAED,GAAE,EAAC,WAAU,MAAK,SAAQ,EAAE,CAAC,EAAC,GAAE,CAAC,CAAC,CAAC;", "names": ["o", "r", "a", "e"]}