{"version": 3, "sources": ["../../@arcgis/core/arcade/functions/featuresetstring.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport n from\"../Dictionary.js\";import{ArcadeExecutionError as a,ExecutionErrorCodes as e}from\"../executionError.js\";import{y as t,w as r,P as o,g as i,j as s,T as c,Y as d,O as u,C as m,Q as f,Z as l,S as y,_ as v,A as w,t as p,N as T}from\"../../chunks/languageUtils.js\";import{layerFieldEsriConstants as h}from\"../featureset/support/shared.js\";function A(a,e){return a&&a.domain?\"coded-value\"===a.domain.type||\"codedValue\"===a.domain.type?n.convertObjectToArcadeDictionary({type:\"codedValue\",name:a.domain.name,dataType:h[a.field.type],codedValues:a.domain.codedValues.map((n=>({name:n.name,code:n.code})))},m(e)):n.convertObjectToArcadeDictionary({type:\"range\",name:a.domain.name,dataType:h[a.field.type],min:a.domain.min,max:a.domain.max},m(e)):null}function b(h){\"async\"===h.mode&&(h.functions.domain=function(n,u){return h.standardFunctionAsync(n,u,(async(m,f,l)=>{if(t(l,2,3,n,u),r(l[0])){return A(o(l[0],s(l[1]),void 0===l[2]?void 0:i(l[2])),n)}if(c(l[0])){await l[0]._ensureLoaded();return A(d(s(l[1]),l[0],null,void 0===l[2]?void 0:i(l[2])),n)}throw new a(n,e.InvalidParameter,u)}))},h.functions.subtypes=function(o,i){return h.standardFunctionAsync(o,i,(async(s,d,f)=>{if(t(f,1,1,o,i),r(f[0])){const a=u(f[0]);return a?n.convertObjectToArcadeDictionary(a,m(o)):null}if(c(f[0])){await f[0]._ensureLoaded();const a=f[0].subtypes();return a?n.convertObjectToArcadeDictionary(a,m(o)):null}throw new a(o,e.InvalidParameter,i)}))},h.functions.domainname=function(n,o){return h.standardFunctionAsync(n,o,(async(u,m,y)=>{if(t(y,2,4,n,o),r(y[0]))return f(y[0],s(y[1]),y[2],void 0===y[3]?void 0:i(y[3]));if(c(y[0])){await y[0]._ensureLoaded();const n=d(s(y[1]),y[0],null,void 0===y[3]?void 0:i(y[3]));return l(n,y[2])}throw new a(n,e.InvalidParameter,o)}))},h.signatures.push({name:\"domainname\",min:2,max:4}),h.functions.domaincode=function(n,o){return h.standardFunctionAsync(n,o,(async(u,m,f)=>{if(t(f,2,4,n,o),r(f[0]))return y(f[0],s(f[1]),f[2],void 0===f[3]?void 0:i(f[3]));if(c(f[0])){await f[0]._ensureLoaded();const n=d(s(f[1]),f[0],null,void 0===f[3]?void 0:i(f[3]));return v(n,f[2])}throw new a(n,e.InvalidParameter,o)}))},h.signatures.push({name:\"domaincode\",min:2,max:4})),h.functions.text=function(n,a){return h.standardFunctionAsync(n,a,((e,r,o)=>{if(t(o,1,2,n,a),!c(o[0]))return p(o[0],o[1]);{const n=w(o[1],\"\");if(\"\"===n)return o[0].castToText();if(\"schema\"===n.toLowerCase())return o[0].convertToText(\"schema\",e.abortSignal);if(\"featureset\"===n.toLowerCase())return o[0].convertToText(\"featureset\",e.abortSignal)}}))},h.functions.gdbversion=function(n,o){return h.standardFunctionAsync(n,o,(async(i,s,d)=>{if(t(d,1,1,n,o),r(d[0]))return d[0].gdbVersion();if(c(d[0])){return(await d[0].load()).gdbVersion}throw new a(n,e.InvalidParameter,o)}))},h.functions.schema=function(o,i){return h.standardFunctionAsync(o,i,(async(s,d,u)=>{if(t(u,1,1,o,i),c(u[0]))return await u[0].load(),n.convertObjectToArcadeDictionary(u[0].schema(),m(o));if(r(u[0])){const a=T(u[0]);return a?n.convertObjectToArcadeDictionary(a,m(o)):null}throw new a(o,e.InvalidParameter,i)}))}}export{b as registerFunctions};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI0V,SAAS,EAAE,GAAEA,IAAE;AAAC,SAAO,KAAG,EAAE,SAAO,kBAAgB,EAAE,OAAO,QAAM,iBAAe,EAAE,OAAO,OAAK,EAAE,gCAAgC,EAAC,MAAK,cAAa,MAAK,EAAE,OAAO,MAAK,UAAS,EAAE,EAAE,MAAM,IAAI,GAAE,aAAY,EAAE,OAAO,YAAY,IAAK,QAAI,EAAC,MAAK,EAAE,MAAK,MAAK,EAAE,KAAI,EAAG,EAAC,GAAE,GAAEA,EAAC,CAAC,IAAE,EAAE,gCAAgC,EAAC,MAAK,SAAQ,MAAK,EAAE,OAAO,MAAK,UAAS,EAAE,EAAE,MAAM,IAAI,GAAE,KAAI,EAAE,OAAO,KAAI,KAAI,EAAE,OAAO,IAAG,GAAE,GAAEA,EAAC,CAAC,IAAE;AAAI;AAAC,SAASC,GAAE,GAAE;AAAC,cAAU,EAAE,SAAO,EAAE,UAAU,SAAO,SAAS,GAAE,GAAE;AAAC,WAAO,EAAE,sBAAsB,GAAE,GAAG,OAAM,GAAE,GAAE,MAAI;AAAC,UAAG,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,eAAO,EAAE,GAAE,EAAE,CAAC,GAAE,GAAE,EAAE,CAAC,CAAC,GAAE,WAAS,EAAE,CAAC,IAAE,SAAO,GAAE,EAAE,CAAC,CAAC,CAAC,GAAE,CAAC;AAAA,MAAC;AAAC,UAAG,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,cAAM,EAAE,CAAC,EAAE,cAAc;AAAE,eAAO,EAAE,GAAE,GAAE,EAAE,CAAC,CAAC,GAAE,EAAE,CAAC,GAAE,MAAK,WAAS,EAAE,CAAC,IAAE,SAAO,GAAE,EAAE,CAAC,CAAC,CAAC,GAAE,CAAC;AAAA,MAAC;AAAC,YAAM,IAAI,EAAE,GAAE,EAAE,kBAAiB,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,WAAS,SAAS,GAAE,GAAE;AAAC,WAAO,EAAE,sBAAsB,GAAE,GAAG,OAAM,GAAE,GAAE,MAAI;AAAC,UAAG,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,cAAM,IAAE,GAAE,EAAE,CAAC,CAAC;AAAE,eAAO,IAAE,EAAE,gCAAgC,GAAE,GAAE,CAAC,CAAC,IAAE;AAAA,MAAI;AAAC,UAAG,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,cAAM,EAAE,CAAC,EAAE,cAAc;AAAE,cAAM,IAAE,EAAE,CAAC,EAAE,SAAS;AAAE,eAAO,IAAE,EAAE,gCAAgC,GAAE,GAAE,CAAC,CAAC,IAAE;AAAA,MAAI;AAAC,YAAM,IAAI,EAAE,GAAE,EAAE,kBAAiB,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,aAAW,SAAS,GAAE,GAAE;AAAC,WAAO,EAAE,sBAAsB,GAAE,GAAG,OAAM,GAAE,GAAE,MAAI;AAAC,UAAG,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,EAAE,CAAC,CAAC,EAAE,QAAO,GAAE,EAAE,CAAC,GAAE,GAAE,EAAE,CAAC,CAAC,GAAE,EAAE,CAAC,GAAE,WAAS,EAAE,CAAC,IAAE,SAAO,GAAE,EAAE,CAAC,CAAC,CAAC;AAAE,UAAG,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,cAAM,EAAE,CAAC,EAAE,cAAc;AAAE,cAAMC,KAAE,GAAE,GAAE,EAAE,CAAC,CAAC,GAAE,EAAE,CAAC,GAAE,MAAK,WAAS,EAAE,CAAC,IAAE,SAAO,GAAE,EAAE,CAAC,CAAC,CAAC;AAAE,eAAO,GAAEA,IAAE,EAAE,CAAC,CAAC;AAAA,MAAC;AAAC,YAAM,IAAI,EAAE,GAAE,EAAE,kBAAiB,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,WAAW,KAAK,EAAC,MAAK,cAAa,KAAI,GAAE,KAAI,EAAC,CAAC,GAAE,EAAE,UAAU,aAAW,SAAS,GAAE,GAAE;AAAC,WAAO,EAAE,sBAAsB,GAAE,GAAG,OAAM,GAAE,GAAE,MAAI;AAAC,UAAG,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,EAAE,CAAC,CAAC,EAAE,QAAO,GAAE,EAAE,CAAC,GAAE,GAAE,EAAE,CAAC,CAAC,GAAE,EAAE,CAAC,GAAE,WAAS,EAAE,CAAC,IAAE,SAAO,GAAE,EAAE,CAAC,CAAC,CAAC;AAAE,UAAG,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,cAAM,EAAE,CAAC,EAAE,cAAc;AAAE,cAAMA,KAAE,GAAE,GAAE,EAAE,CAAC,CAAC,GAAE,EAAE,CAAC,GAAE,MAAK,WAAS,EAAE,CAAC,IAAE,SAAO,GAAE,EAAE,CAAC,CAAC,CAAC;AAAE,eAAO,GAAEA,IAAE,EAAE,CAAC,CAAC;AAAA,MAAC;AAAC,YAAM,IAAI,EAAE,GAAE,EAAE,kBAAiB,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,WAAW,KAAK,EAAC,MAAK,cAAa,KAAI,GAAE,KAAI,EAAC,CAAC,IAAG,EAAE,UAAU,OAAK,SAAS,GAAE,GAAE;AAAC,WAAO,EAAE,sBAAsB,GAAE,GAAG,CAACF,IAAE,GAAE,MAAI;AAAC,UAAG,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,QAAO,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAE;AAAC,cAAME,KAAE,EAAE,EAAE,CAAC,GAAE,EAAE;AAAE,YAAG,OAAKA,GAAE,QAAO,EAAE,CAAC,EAAE,WAAW;AAAE,YAAG,aAAWA,GAAE,YAAY,EAAE,QAAO,EAAE,CAAC,EAAE,cAAc,UAASF,GAAE,WAAW;AAAE,YAAG,iBAAeE,GAAE,YAAY,EAAE,QAAO,EAAE,CAAC,EAAE,cAAc,cAAaF,GAAE,WAAW;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,aAAW,SAAS,GAAE,GAAE;AAAC,WAAO,EAAE,sBAAsB,GAAE,GAAG,OAAM,GAAE,GAAE,MAAI;AAAC,UAAG,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,EAAE,CAAC,CAAC,EAAE,QAAO,EAAE,CAAC,EAAE,WAAW;AAAE,UAAG,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,gBAAO,MAAM,EAAE,CAAC,EAAE,KAAK,GAAG;AAAA,MAAU;AAAC,YAAM,IAAI,EAAE,GAAE,EAAE,kBAAiB,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,SAAO,SAAS,GAAE,GAAE;AAAC,WAAO,EAAE,sBAAsB,GAAE,GAAG,OAAM,GAAE,GAAE,MAAI;AAAC,UAAG,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,EAAE,CAAC,CAAC,EAAE,QAAO,MAAM,EAAE,CAAC,EAAE,KAAK,GAAE,EAAE,gCAAgC,EAAE,CAAC,EAAE,OAAO,GAAE,GAAE,CAAC,CAAC;AAAE,UAAG,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,cAAM,IAAE,GAAE,EAAE,CAAC,CAAC;AAAE,eAAO,IAAE,EAAE,gCAAgC,GAAE,GAAE,CAAC,CAAC,IAAE;AAAA,MAAI;AAAC,YAAM,IAAI,EAAE,GAAE,EAAE,kBAAiB,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;", "names": ["e", "b", "n"]}