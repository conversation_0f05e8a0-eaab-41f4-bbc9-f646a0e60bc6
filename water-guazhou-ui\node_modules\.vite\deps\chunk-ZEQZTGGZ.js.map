{"version": 3, "sources": ["../../@lezer/lr/dist/index.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, NodeProp, NodeSet, NodeType, <PERSON><PERSON>ult<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tree, IterMode } from '@lezer/common';\n\n/**\nA parse stack. These are used internally by the parser to track\nparsing progress. They also provide some properties and methods\nthat external code such as a tokenizer can use to get information\nabout the parse state.\n*/\nclass Stack {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parse that this stack is part of @internal\n    */\n    p, \n    /**\n    Holds state, input pos, buffer index triplets for all but the\n    top state @internal\n    */\n    stack, \n    /**\n    The current parse state @internal\n    */\n    state, \n    // The position at which the next reduce should take place. This\n    // can be less than `this.pos` when skipped expressions have been\n    // added to the stack (which should be moved outside of the next\n    // reduction)\n    /**\n    @internal\n    */\n    reducePos, \n    /**\n    The input position up to which this stack has parsed.\n    */\n    pos, \n    /**\n    The dynamic score of the stack, including dynamic precedence\n    and error-recovery penalties\n    @internal\n    */\n    score, \n    // The output buffer. Holds (type, start, end, size) quads\n    // representing nodes created by the parser, where `size` is\n    // amount of buffer array entries covered by this node.\n    /**\n    @internal\n    */\n    buffer, \n    // The base offset of the buffer. When stacks are split, the split\n    // instance shared the buffer history with its parent up to\n    // `bufferBase`, which is the absolute offset (including the\n    // offset of previous splits) into the buffer at which this stack\n    // starts writing.\n    /**\n    @internal\n    */\n    bufferBase, \n    /**\n    @internal\n    */\n    curContext, \n    /**\n    @internal\n    */\n    lookAhead = 0, \n    // A parent stack from which this was split off, if any. This is\n    // set up so that it always points to a stack that has some\n    // additional buffer content, never to a stack with an equal\n    // `bufferBase`.\n    /**\n    @internal\n    */\n    parent) {\n        this.p = p;\n        this.stack = stack;\n        this.state = state;\n        this.reducePos = reducePos;\n        this.pos = pos;\n        this.score = score;\n        this.buffer = buffer;\n        this.bufferBase = bufferBase;\n        this.curContext = curContext;\n        this.lookAhead = lookAhead;\n        this.parent = parent;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return `[${this.stack.filter((_, i) => i % 3 == 0).concat(this.state)}]@${this.pos}${this.score ? \"!\" + this.score : \"\"}`;\n    }\n    // Start an empty stack\n    /**\n    @internal\n    */\n    static start(p, state, pos = 0) {\n        let cx = p.parser.context;\n        return new Stack(p, [], state, pos, pos, 0, [], 0, cx ? new StackContext(cx, cx.start) : null, 0, null);\n    }\n    /**\n    The stack's current [context](#lr.ContextTracker) value, if\n    any. Its type will depend on the context tracker's type\n    parameter, or it will be `null` if there is no context\n    tracker.\n    */\n    get context() { return this.curContext ? this.curContext.context : null; }\n    // Push a state onto the stack, tracking its start position as well\n    // as the buffer base at that point.\n    /**\n    @internal\n    */\n    pushState(state, start) {\n        this.stack.push(this.state, start, this.bufferBase + this.buffer.length);\n        this.state = state;\n    }\n    // Apply a reduce action\n    /**\n    @internal\n    */\n    reduce(action) {\n        var _a;\n        let depth = action >> 19 /* Action.ReduceDepthShift */, type = action & 65535 /* Action.ValueMask */;\n        let { parser } = this.p;\n        let lookaheadRecord = this.reducePos < this.pos - 25 /* Lookahead.Margin */;\n        if (lookaheadRecord)\n            this.setLookAhead(this.pos);\n        let dPrec = parser.dynamicPrecedence(type);\n        if (dPrec)\n            this.score += dPrec;\n        if (depth == 0) {\n            this.pushState(parser.getGoto(this.state, type, true), this.reducePos);\n            // Zero-depth reductions are a special case—they add stuff to\n            // the stack without popping anything off.\n            if (type < parser.minRepeatTerm)\n                this.storeNode(type, this.reducePos, this.reducePos, lookaheadRecord ? 8 : 4, true);\n            this.reduceContext(type, this.reducePos);\n            return;\n        }\n        // Find the base index into `this.stack`, content after which will\n        // be dropped. Note that with `StayFlag` reductions we need to\n        // consume two extra frames (the dummy parent node for the skipped\n        // expression and the state that we'll be staying in, which should\n        // be moved to `this.state`).\n        let base = this.stack.length - ((depth - 1) * 3) - (action & 262144 /* Action.StayFlag */ ? 6 : 0);\n        let start = base ? this.stack[base - 2] : this.p.ranges[0].from, size = this.reducePos - start;\n        // This is a kludge to try and detect overly deep left-associative\n        // trees, which will not increase the parse stack depth and thus\n        // won't be caught by the regular stack-depth limit check.\n        if (size >= 2000 /* Recover.MinBigReduction */ && !((_a = this.p.parser.nodeSet.types[type]) === null || _a === void 0 ? void 0 : _a.isAnonymous)) {\n            if (start == this.p.lastBigReductionStart) {\n                this.p.bigReductionCount++;\n                this.p.lastBigReductionSize = size;\n            }\n            else if (this.p.lastBigReductionSize < size) {\n                this.p.bigReductionCount = 1;\n                this.p.lastBigReductionStart = start;\n                this.p.lastBigReductionSize = size;\n            }\n        }\n        let bufferBase = base ? this.stack[base - 1] : 0, count = this.bufferBase + this.buffer.length - bufferBase;\n        // Store normal terms or `R -> R R` repeat reductions\n        if (type < parser.minRepeatTerm || (action & 131072 /* Action.RepeatFlag */)) {\n            let pos = parser.stateFlag(this.state, 1 /* StateFlag.Skipped */) ? this.pos : this.reducePos;\n            this.storeNode(type, start, pos, count + 4, true);\n        }\n        if (action & 262144 /* Action.StayFlag */) {\n            this.state = this.stack[base];\n        }\n        else {\n            let baseStateID = this.stack[base - 3];\n            this.state = parser.getGoto(baseStateID, type, true);\n        }\n        while (this.stack.length > base)\n            this.stack.pop();\n        this.reduceContext(type, start);\n    }\n    // Shift a value into the buffer\n    /**\n    @internal\n    */\n    storeNode(term, start, end, size = 4, mustSink = false) {\n        if (term == 0 /* Term.Err */ &&\n            (!this.stack.length || this.stack[this.stack.length - 1] < this.buffer.length + this.bufferBase)) {\n            // Try to omit/merge adjacent error nodes\n            let cur = this, top = this.buffer.length;\n            if (top == 0 && cur.parent) {\n                top = cur.bufferBase - cur.parent.bufferBase;\n                cur = cur.parent;\n            }\n            if (top > 0 && cur.buffer[top - 4] == 0 /* Term.Err */ && cur.buffer[top - 1] > -1) {\n                if (start == end)\n                    return;\n                if (cur.buffer[top - 2] >= start) {\n                    cur.buffer[top - 2] = end;\n                    return;\n                }\n            }\n        }\n        if (!mustSink || this.pos == end) { // Simple case, just append\n            this.buffer.push(term, start, end, size);\n        }\n        else { // There may be skipped nodes that have to be moved forward\n            let index = this.buffer.length;\n            if (index > 0 && this.buffer[index - 4] != 0 /* Term.Err */) {\n                let mustMove = false;\n                for (let scan = index; scan > 0 && this.buffer[scan - 2] > end; scan -= 4) {\n                    if (this.buffer[scan - 1] >= 0) {\n                        mustMove = true;\n                        break;\n                    }\n                }\n                if (mustMove)\n                    while (index > 0 && this.buffer[index - 2] > end) {\n                        // Move this record forward\n                        this.buffer[index] = this.buffer[index - 4];\n                        this.buffer[index + 1] = this.buffer[index - 3];\n                        this.buffer[index + 2] = this.buffer[index - 2];\n                        this.buffer[index + 3] = this.buffer[index - 1];\n                        index -= 4;\n                        if (size > 4)\n                            size -= 4;\n                    }\n            }\n            this.buffer[index] = term;\n            this.buffer[index + 1] = start;\n            this.buffer[index + 2] = end;\n            this.buffer[index + 3] = size;\n        }\n    }\n    // Apply a shift action\n    /**\n    @internal\n    */\n    shift(action, type, start, end) {\n        if (action & 131072 /* Action.GotoFlag */) {\n            this.pushState(action & 65535 /* Action.ValueMask */, this.pos);\n        }\n        else if ((action & 262144 /* Action.StayFlag */) == 0) { // Regular shift\n            let nextState = action, { parser } = this.p;\n            if (end > this.pos || type <= parser.maxNode) {\n                this.pos = end;\n                if (!parser.stateFlag(nextState, 1 /* StateFlag.Skipped */))\n                    this.reducePos = end;\n            }\n            this.pushState(nextState, start);\n            this.shiftContext(type, start);\n            if (type <= parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n        else { // Shift-and-stay, which means this is a skipped token\n            this.pos = end;\n            this.shiftContext(type, start);\n            if (type <= this.p.parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n    }\n    // Apply an action\n    /**\n    @internal\n    */\n    apply(action, next, nextStart, nextEnd) {\n        if (action & 65536 /* Action.ReduceFlag */)\n            this.reduce(action);\n        else\n            this.shift(action, next, nextStart, nextEnd);\n    }\n    // Add a prebuilt (reused) node into the buffer.\n    /**\n    @internal\n    */\n    useNode(value, next) {\n        let index = this.p.reused.length - 1;\n        if (index < 0 || this.p.reused[index] != value) {\n            this.p.reused.push(value);\n            index++;\n        }\n        let start = this.pos;\n        this.reducePos = this.pos = start + value.length;\n        this.pushState(next, start);\n        this.buffer.push(index, start, this.reducePos, -1 /* size == -1 means this is a reused value */);\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reuse(this.curContext.context, value, this, this.p.stream.reset(this.pos - value.length)));\n    }\n    // Split the stack. Due to the buffer sharing and the fact\n    // that `this.stack` tends to stay quite shallow, this isn't very\n    // expensive.\n    /**\n    @internal\n    */\n    split() {\n        let parent = this;\n        let off = parent.buffer.length;\n        // Because the top of the buffer (after this.pos) may be mutated\n        // to reorder reductions and skipped tokens, and shared buffers\n        // should be immutable, this copies any outstanding skipped tokens\n        // to the new buffer, and puts the base pointer before them.\n        while (off > 0 && parent.buffer[off - 2] > parent.reducePos)\n            off -= 4;\n        let buffer = parent.buffer.slice(off), base = parent.bufferBase + off;\n        // Make sure parent points to an actual parent with content, if there is such a parent.\n        while (parent && base == parent.bufferBase)\n            parent = parent.parent;\n        return new Stack(this.p, this.stack.slice(), this.state, this.reducePos, this.pos, this.score, buffer, base, this.curContext, this.lookAhead, parent);\n    }\n    // Try to recover from an error by 'deleting' (ignoring) one token.\n    /**\n    @internal\n    */\n    recoverByDelete(next, nextEnd) {\n        let isNode = next <= this.p.parser.maxNode;\n        if (isNode)\n            this.storeNode(next, this.pos, nextEnd, 4);\n        this.storeNode(0 /* Term.Err */, this.pos, nextEnd, isNode ? 8 : 4);\n        this.pos = this.reducePos = nextEnd;\n        this.score -= 190 /* Recover.Delete */;\n    }\n    /**\n    Check if the given term would be able to be shifted (optionally\n    after some reductions) on this stack. This can be useful for\n    external tokenizers that want to make sure they only provide a\n    given token when it applies.\n    */\n    canShift(term) {\n        for (let sim = new SimulatedStack(this);;) {\n            let action = this.p.parser.stateSlot(sim.state, 4 /* ParseState.DefaultReduce */) || this.p.parser.hasAction(sim.state, term);\n            if (action == 0)\n                return false;\n            if ((action & 65536 /* Action.ReduceFlag */) == 0)\n                return true;\n            sim.reduce(action);\n        }\n    }\n    // Apply up to Recover.MaxNext recovery actions that conceptually\n    // inserts some missing token or rule.\n    /**\n    @internal\n    */\n    recoverByInsert(next) {\n        if (this.stack.length >= 300 /* Recover.MaxInsertStackDepth */)\n            return [];\n        let nextStates = this.p.parser.nextStates(this.state);\n        if (nextStates.length > 4 /* Recover.MaxNext */ << 1 || this.stack.length >= 120 /* Recover.DampenInsertStackDepth */) {\n            let best = [];\n            for (let i = 0, s; i < nextStates.length; i += 2) {\n                if ((s = nextStates[i + 1]) != this.state && this.p.parser.hasAction(s, next))\n                    best.push(nextStates[i], s);\n            }\n            if (this.stack.length < 120 /* Recover.DampenInsertStackDepth */)\n                for (let i = 0; best.length < 4 /* Recover.MaxNext */ << 1 && i < nextStates.length; i += 2) {\n                    let s = nextStates[i + 1];\n                    if (!best.some((v, i) => (i & 1) && v == s))\n                        best.push(nextStates[i], s);\n                }\n            nextStates = best;\n        }\n        let result = [];\n        for (let i = 0; i < nextStates.length && result.length < 4 /* Recover.MaxNext */; i += 2) {\n            let s = nextStates[i + 1];\n            if (s == this.state)\n                continue;\n            let stack = this.split();\n            stack.pushState(s, this.pos);\n            stack.storeNode(0 /* Term.Err */, stack.pos, stack.pos, 4, true);\n            stack.shiftContext(nextStates[i], this.pos);\n            stack.reducePos = this.pos;\n            stack.score -= 200 /* Recover.Insert */;\n            result.push(stack);\n        }\n        return result;\n    }\n    // Force a reduce, if possible. Return false if that can't\n    // be done.\n    /**\n    @internal\n    */\n    forceReduce() {\n        let { parser } = this.p;\n        let reduce = parser.stateSlot(this.state, 5 /* ParseState.ForcedReduce */);\n        if ((reduce & 65536 /* Action.ReduceFlag */) == 0)\n            return false;\n        if (!parser.validAction(this.state, reduce)) {\n            let depth = reduce >> 19 /* Action.ReduceDepthShift */, term = reduce & 65535 /* Action.ValueMask */;\n            let target = this.stack.length - depth * 3;\n            if (target < 0 || parser.getGoto(this.stack[target], term, false) < 0) {\n                let backup = this.findForcedReduction();\n                if (backup == null)\n                    return false;\n                reduce = backup;\n            }\n            this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n            this.score -= 100 /* Recover.Reduce */;\n        }\n        this.reducePos = this.pos;\n        this.reduce(reduce);\n        return true;\n    }\n    /**\n    Try to scan through the automaton to find some kind of reduction\n    that can be applied. Used when the regular ForcedReduce field\n    isn't a valid action. @internal\n    */\n    findForcedReduction() {\n        let { parser } = this.p, seen = [];\n        let explore = (state, depth) => {\n            if (seen.includes(state))\n                return;\n            seen.push(state);\n            return parser.allActions(state, (action) => {\n                if (action & (262144 /* Action.StayFlag */ | 131072 /* Action.GotoFlag */)) ;\n                else if (action & 65536 /* Action.ReduceFlag */) {\n                    let rDepth = (action >> 19 /* Action.ReduceDepthShift */) - depth;\n                    if (rDepth > 1) {\n                        let term = action & 65535 /* Action.ValueMask */, target = this.stack.length - rDepth * 3;\n                        if (target >= 0 && parser.getGoto(this.stack[target], term, false) >= 0)\n                            return (rDepth << 19 /* Action.ReduceDepthShift */) | 65536 /* Action.ReduceFlag */ | term;\n                    }\n                }\n                else {\n                    let found = explore(action, depth + 1);\n                    if (found != null)\n                        return found;\n                }\n            });\n        };\n        return explore(this.state, 0);\n    }\n    /**\n    @internal\n    */\n    forceAll() {\n        while (!this.p.parser.stateFlag(this.state, 2 /* StateFlag.Accepting */)) {\n            if (!this.forceReduce()) {\n                this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n                break;\n            }\n        }\n        return this;\n    }\n    /**\n    Check whether this state has no further actions (assumed to be a direct descendant of the\n    top state, since any other states must be able to continue\n    somehow). @internal\n    */\n    get deadEnd() {\n        if (this.stack.length != 3)\n            return false;\n        let { parser } = this.p;\n        return parser.data[parser.stateSlot(this.state, 1 /* ParseState.Actions */)] == 65535 /* Seq.End */ &&\n            !parser.stateSlot(this.state, 4 /* ParseState.DefaultReduce */);\n    }\n    /**\n    Restart the stack (put it back in its start state). Only safe\n    when this.stack.length == 3 (state is directly below the top\n    state). @internal\n    */\n    restart() {\n        this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n        this.state = this.stack[0];\n        this.stack.length = 0;\n    }\n    /**\n    @internal\n    */\n    sameState(other) {\n        if (this.state != other.state || this.stack.length != other.stack.length)\n            return false;\n        for (let i = 0; i < this.stack.length; i += 3)\n            if (this.stack[i] != other.stack[i])\n                return false;\n        return true;\n    }\n    /**\n    Get the parser used by this stack.\n    */\n    get parser() { return this.p.parser; }\n    /**\n    Test whether a given dialect (by numeric ID, as exported from\n    the terms file) is enabled.\n    */\n    dialectEnabled(dialectID) { return this.p.parser.dialect.flags[dialectID]; }\n    shiftContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.shift(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    reduceContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reduce(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    /**\n    @internal\n    */\n    emitContext() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -3)\n            this.buffer.push(this.curContext.hash, this.pos, this.pos, -3);\n    }\n    /**\n    @internal\n    */\n    emitLookAhead() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -4)\n            this.buffer.push(this.lookAhead, this.pos, this.pos, -4);\n    }\n    updateContext(context) {\n        if (context != this.curContext.context) {\n            let newCx = new StackContext(this.curContext.tracker, context);\n            if (newCx.hash != this.curContext.hash)\n                this.emitContext();\n            this.curContext = newCx;\n        }\n    }\n    /**\n    @internal\n    */\n    setLookAhead(lookAhead) {\n        if (lookAhead > this.lookAhead) {\n            this.emitLookAhead();\n            this.lookAhead = lookAhead;\n        }\n    }\n    /**\n    @internal\n    */\n    close() {\n        if (this.curContext && this.curContext.tracker.strict)\n            this.emitContext();\n        if (this.lookAhead > 0)\n            this.emitLookAhead();\n    }\n}\nclass StackContext {\n    constructor(tracker, context) {\n        this.tracker = tracker;\n        this.context = context;\n        this.hash = tracker.strict ? tracker.hash(context) : 0;\n    }\n}\n// Used to cheaply run some reductions to scan ahead without mutating\n// an entire stack\nclass SimulatedStack {\n    constructor(start) {\n        this.start = start;\n        this.state = start.state;\n        this.stack = start.stack;\n        this.base = this.stack.length;\n    }\n    reduce(action) {\n        let term = action & 65535 /* Action.ValueMask */, depth = action >> 19 /* Action.ReduceDepthShift */;\n        if (depth == 0) {\n            if (this.stack == this.start.stack)\n                this.stack = this.stack.slice();\n            this.stack.push(this.state, 0, 0);\n            this.base += 3;\n        }\n        else {\n            this.base -= (depth - 1) * 3;\n        }\n        let goto = this.start.p.parser.getGoto(this.stack[this.base - 3], term, true);\n        this.state = goto;\n    }\n}\n// This is given to `Tree.build` to build a buffer, and encapsulates\n// the parent-stack-walking necessary to read the nodes.\nclass StackBufferCursor {\n    constructor(stack, pos, index) {\n        this.stack = stack;\n        this.pos = pos;\n        this.index = index;\n        this.buffer = stack.buffer;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    static create(stack, pos = stack.bufferBase + stack.buffer.length) {\n        return new StackBufferCursor(stack, pos, pos - stack.bufferBase);\n    }\n    maybeNext() {\n        let next = this.stack.parent;\n        if (next != null) {\n            this.index = this.stack.bufferBase - next.bufferBase;\n            this.stack = next;\n            this.buffer = next.buffer;\n        }\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    next() {\n        this.index -= 4;\n        this.pos -= 4;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    fork() {\n        return new StackBufferCursor(this.stack, this.pos, this.index);\n    }\n}\n\n// See lezer-generator/src/encode.ts for comments about the encoding\n// used here\nfunction decodeArray(input, Type = Uint16Array) {\n    if (typeof input != \"string\")\n        return input;\n    let array = null;\n    for (let pos = 0, out = 0; pos < input.length;) {\n        let value = 0;\n        for (;;) {\n            let next = input.charCodeAt(pos++), stop = false;\n            if (next == 126 /* Encode.BigValCode */) {\n                value = 65535 /* Encode.BigVal */;\n                break;\n            }\n            if (next >= 92 /* Encode.Gap2 */)\n                next--;\n            if (next >= 34 /* Encode.Gap1 */)\n                next--;\n            let digit = next - 32 /* Encode.Start */;\n            if (digit >= 46 /* Encode.Base */) {\n                digit -= 46 /* Encode.Base */;\n                stop = true;\n            }\n            value += digit;\n            if (stop)\n                break;\n            value *= 46 /* Encode.Base */;\n        }\n        if (array)\n            array[out++] = value;\n        else\n            array = new Type(value);\n    }\n    return array;\n}\n\nclass CachedToken {\n    constructor() {\n        this.start = -1;\n        this.value = -1;\n        this.end = -1;\n        this.extended = -1;\n        this.lookAhead = 0;\n        this.mask = 0;\n        this.context = 0;\n    }\n}\nconst nullToken = new CachedToken;\n/**\n[Tokenizers](#lr.ExternalTokenizer) interact with the input\nthrough this interface. It presents the input as a stream of\ncharacters, tracking lookahead and hiding the complexity of\n[ranges](#common.Parser.parse^ranges) from tokenizer code.\n*/\nclass InputStream {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    input, \n    /**\n    @internal\n    */\n    ranges) {\n        this.input = input;\n        this.ranges = ranges;\n        /**\n        @internal\n        */\n        this.chunk = \"\";\n        /**\n        @internal\n        */\n        this.chunkOff = 0;\n        /**\n        Backup chunk\n        */\n        this.chunk2 = \"\";\n        this.chunk2Pos = 0;\n        /**\n        The character code of the next code unit in the input, or -1\n        when the stream is at the end of the input.\n        */\n        this.next = -1;\n        /**\n        @internal\n        */\n        this.token = nullToken;\n        this.rangeIndex = 0;\n        this.pos = this.chunkPos = ranges[0].from;\n        this.range = ranges[0];\n        this.end = ranges[ranges.length - 1].to;\n        this.readNext();\n    }\n    /**\n    @internal\n    */\n    resolveOffset(offset, assoc) {\n        let range = this.range, index = this.rangeIndex;\n        let pos = this.pos + offset;\n        while (pos < range.from) {\n            if (!index)\n                return null;\n            let next = this.ranges[--index];\n            pos -= range.from - next.to;\n            range = next;\n        }\n        while (assoc < 0 ? pos > range.to : pos >= range.to) {\n            if (index == this.ranges.length - 1)\n                return null;\n            let next = this.ranges[++index];\n            pos += next.from - range.to;\n            range = next;\n        }\n        return pos;\n    }\n    /**\n    @internal\n    */\n    clipPos(pos) {\n        if (pos >= this.range.from && pos < this.range.to)\n            return pos;\n        for (let range of this.ranges)\n            if (range.to > pos)\n                return Math.max(pos, range.from);\n        return this.end;\n    }\n    /**\n    Look at a code unit near the stream position. `.peek(0)` equals\n    `.next`, `.peek(-1)` gives you the previous character, and so\n    on.\n    \n    Note that looking around during tokenizing creates dependencies\n    on potentially far-away content, which may reduce the\n    effectiveness incremental parsing—when looking forward—or even\n    cause invalid reparses when looking backward more than 25 code\n    units, since the library does not track lookbehind.\n    */\n    peek(offset) {\n        let idx = this.chunkOff + offset, pos, result;\n        if (idx >= 0 && idx < this.chunk.length) {\n            pos = this.pos + offset;\n            result = this.chunk.charCodeAt(idx);\n        }\n        else {\n            let resolved = this.resolveOffset(offset, 1);\n            if (resolved == null)\n                return -1;\n            pos = resolved;\n            if (pos >= this.chunk2Pos && pos < this.chunk2Pos + this.chunk2.length) {\n                result = this.chunk2.charCodeAt(pos - this.chunk2Pos);\n            }\n            else {\n                let i = this.rangeIndex, range = this.range;\n                while (range.to <= pos)\n                    range = this.ranges[++i];\n                this.chunk2 = this.input.chunk(this.chunk2Pos = pos);\n                if (pos + this.chunk2.length > range.to)\n                    this.chunk2 = this.chunk2.slice(0, range.to - pos);\n                result = this.chunk2.charCodeAt(0);\n            }\n        }\n        if (pos >= this.token.lookAhead)\n            this.token.lookAhead = pos + 1;\n        return result;\n    }\n    /**\n    Accept a token. By default, the end of the token is set to the\n    current stream position, but you can pass an offset (relative to\n    the stream position) to change that.\n    */\n    acceptToken(token, endOffset = 0) {\n        let end = endOffset ? this.resolveOffset(endOffset, -1) : this.pos;\n        if (end == null || end < this.token.start)\n            throw new RangeError(\"Token end out of bounds\");\n        this.token.value = token;\n        this.token.end = end;\n    }\n    /**\n    Accept a token ending at a specific given position.\n    */\n    acceptTokenTo(token, endPos) {\n        this.token.value = token;\n        this.token.end = endPos;\n    }\n    getChunk() {\n        if (this.pos >= this.chunk2Pos && this.pos < this.chunk2Pos + this.chunk2.length) {\n            let { chunk, chunkPos } = this;\n            this.chunk = this.chunk2;\n            this.chunkPos = this.chunk2Pos;\n            this.chunk2 = chunk;\n            this.chunk2Pos = chunkPos;\n            this.chunkOff = this.pos - this.chunkPos;\n        }\n        else {\n            this.chunk2 = this.chunk;\n            this.chunk2Pos = this.chunkPos;\n            let nextChunk = this.input.chunk(this.pos);\n            let end = this.pos + nextChunk.length;\n            this.chunk = end > this.range.to ? nextChunk.slice(0, this.range.to - this.pos) : nextChunk;\n            this.chunkPos = this.pos;\n            this.chunkOff = 0;\n        }\n    }\n    readNext() {\n        if (this.chunkOff >= this.chunk.length) {\n            this.getChunk();\n            if (this.chunkOff == this.chunk.length)\n                return this.next = -1;\n        }\n        return this.next = this.chunk.charCodeAt(this.chunkOff);\n    }\n    /**\n    Move the stream forward N (defaults to 1) code units. Returns\n    the new value of [`next`](#lr.InputStream.next).\n    */\n    advance(n = 1) {\n        this.chunkOff += n;\n        while (this.pos + n >= this.range.to) {\n            if (this.rangeIndex == this.ranges.length - 1)\n                return this.setDone();\n            n -= this.range.to - this.pos;\n            this.range = this.ranges[++this.rangeIndex];\n            this.pos = this.range.from;\n        }\n        this.pos += n;\n        if (this.pos >= this.token.lookAhead)\n            this.token.lookAhead = this.pos + 1;\n        return this.readNext();\n    }\n    setDone() {\n        this.pos = this.chunkPos = this.end;\n        this.range = this.ranges[this.rangeIndex = this.ranges.length - 1];\n        this.chunk = \"\";\n        return this.next = -1;\n    }\n    /**\n    @internal\n    */\n    reset(pos, token) {\n        if (token) {\n            this.token = token;\n            token.start = pos;\n            token.lookAhead = pos + 1;\n            token.value = token.extended = -1;\n        }\n        else {\n            this.token = nullToken;\n        }\n        if (this.pos != pos) {\n            this.pos = pos;\n            if (pos == this.end) {\n                this.setDone();\n                return this;\n            }\n            while (pos < this.range.from)\n                this.range = this.ranges[--this.rangeIndex];\n            while (pos >= this.range.to)\n                this.range = this.ranges[++this.rangeIndex];\n            if (pos >= this.chunkPos && pos < this.chunkPos + this.chunk.length) {\n                this.chunkOff = pos - this.chunkPos;\n            }\n            else {\n                this.chunk = \"\";\n                this.chunkOff = 0;\n            }\n            this.readNext();\n        }\n        return this;\n    }\n    /**\n    @internal\n    */\n    read(from, to) {\n        if (from >= this.chunkPos && to <= this.chunkPos + this.chunk.length)\n            return this.chunk.slice(from - this.chunkPos, to - this.chunkPos);\n        if (from >= this.chunk2Pos && to <= this.chunk2Pos + this.chunk2.length)\n            return this.chunk2.slice(from - this.chunk2Pos, to - this.chunk2Pos);\n        if (from >= this.range.from && to <= this.range.to)\n            return this.input.read(from, to);\n        let result = \"\";\n        for (let r of this.ranges) {\n            if (r.from >= to)\n                break;\n            if (r.to > from)\n                result += this.input.read(Math.max(r.from, from), Math.min(r.to, to));\n        }\n        return result;\n    }\n}\n/**\n@internal\n*/\nclass TokenGroup {\n    constructor(data, id) {\n        this.data = data;\n        this.id = id;\n    }\n    token(input, stack) {\n        let { parser } = stack.p;\n        readToken(this.data, input, stack, this.id, parser.data, parser.tokenPrecTable);\n    }\n}\nTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n@hide\n*/\nclass LocalTokenGroup {\n    constructor(data, precTable, elseToken) {\n        this.precTable = precTable;\n        this.elseToken = elseToken;\n        this.data = typeof data == \"string\" ? decodeArray(data) : data;\n    }\n    token(input, stack) {\n        let start = input.pos, skipped = 0;\n        for (;;) {\n            let atEof = input.next < 0, nextPos = input.resolveOffset(1, 1);\n            readToken(this.data, input, stack, 0, this.data, this.precTable);\n            if (input.token.value > -1)\n                break;\n            if (this.elseToken == null)\n                return;\n            if (!atEof)\n                skipped++;\n            if (nextPos == null)\n                break;\n            input.reset(nextPos, input.token);\n        }\n        if (skipped) {\n            input.reset(start, input.token);\n            input.acceptToken(this.elseToken, skipped);\n        }\n    }\n}\nLocalTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n`@external tokens` declarations in the grammar should resolve to\nan instance of this class.\n*/\nclass ExternalTokenizer {\n    /**\n    Create a tokenizer. The first argument is the function that,\n    given an input stream, scans for the types of tokens it\n    recognizes at the stream's position, and calls\n    [`acceptToken`](#lr.InputStream.acceptToken) when it finds\n    one.\n    */\n    constructor(\n    /**\n    @internal\n    */\n    token, options = {}) {\n        this.token = token;\n        this.contextual = !!options.contextual;\n        this.fallback = !!options.fallback;\n        this.extend = !!options.extend;\n    }\n}\n// Tokenizer data is stored a big uint16 array containing, for each\n// state:\n//\n//  - A group bitmask, indicating what token groups are reachable from\n//    this state, so that paths that can only lead to tokens not in\n//    any of the current groups can be cut off early.\n//\n//  - The position of the end of the state's sequence of accepting\n//    tokens\n//\n//  - The number of outgoing edges for the state\n//\n//  - The accepting tokens, as (token id, group mask) pairs\n//\n//  - The outgoing edges, as (start character, end character, state\n//    index) triples, with end character being exclusive\n//\n// This function interprets that data, running through a stream as\n// long as new states with the a matching group mask can be reached,\n// and updating `input.token` when it matches a token.\nfunction readToken(data, input, stack, group, precTable, precOffset) {\n    let state = 0, groupMask = 1 << group, { dialect } = stack.p.parser;\n    scan: for (;;) {\n        if ((groupMask & data[state]) == 0)\n            break;\n        let accEnd = data[state + 1];\n        // Check whether this state can lead to a token in the current group\n        // Accept tokens in this state, possibly overwriting\n        // lower-precedence / shorter tokens\n        for (let i = state + 3; i < accEnd; i += 2)\n            if ((data[i + 1] & groupMask) > 0) {\n                let term = data[i];\n                if (dialect.allows(term) &&\n                    (input.token.value == -1 || input.token.value == term ||\n                        overrides(term, input.token.value, precTable, precOffset))) {\n                    input.acceptToken(term);\n                    break;\n                }\n            }\n        let next = input.next, low = 0, high = data[state + 2];\n        // Special case for EOF\n        if (input.next < 0 && high > low && data[accEnd + high * 3 - 3] == 65535 /* Seq.End */) {\n            state = data[accEnd + high * 3 - 1];\n            continue scan;\n        }\n        // Do a binary search on the state's edges\n        for (; low < high;) {\n            let mid = (low + high) >> 1;\n            let index = accEnd + mid + (mid << 1);\n            let from = data[index], to = data[index + 1] || 0x10000;\n            if (next < from)\n                high = mid;\n            else if (next >= to)\n                low = mid + 1;\n            else {\n                state = data[index + 2];\n                input.advance();\n                continue scan;\n            }\n        }\n        break;\n    }\n}\nfunction findOffset(data, start, term) {\n    for (let i = start, next; (next = data[i]) != 65535 /* Seq.End */; i++)\n        if (next == term)\n            return i - start;\n    return -1;\n}\nfunction overrides(token, prev, tableData, tableOffset) {\n    let iPrev = findOffset(tableData, tableOffset, prev);\n    return iPrev < 0 || findOffset(tableData, tableOffset, token) < iPrev;\n}\n\n// Environment variable used to control console output\nconst verbose = typeof process != \"undefined\" && process.env && /\\bparse\\b/.test(process.env.LOG);\nlet stackIDs = null;\nfunction cutAt(tree, pos, side) {\n    let cursor = tree.cursor(IterMode.IncludeAnonymous);\n    cursor.moveTo(pos);\n    for (;;) {\n        if (!(side < 0 ? cursor.childBefore(pos) : cursor.childAfter(pos)))\n            for (;;) {\n                if ((side < 0 ? cursor.to < pos : cursor.from > pos) && !cursor.type.isError)\n                    return side < 0 ? Math.max(0, Math.min(cursor.to - 1, pos - 25 /* Lookahead.Margin */))\n                        : Math.min(tree.length, Math.max(cursor.from + 1, pos + 25 /* Lookahead.Margin */));\n                if (side < 0 ? cursor.prevSibling() : cursor.nextSibling())\n                    break;\n                if (!cursor.parent())\n                    return side < 0 ? 0 : tree.length;\n            }\n    }\n}\nclass FragmentCursor {\n    constructor(fragments, nodeSet) {\n        this.fragments = fragments;\n        this.nodeSet = nodeSet;\n        this.i = 0;\n        this.fragment = null;\n        this.safeFrom = -1;\n        this.safeTo = -1;\n        this.trees = [];\n        this.start = [];\n        this.index = [];\n        this.nextFragment();\n    }\n    nextFragment() {\n        let fr = this.fragment = this.i == this.fragments.length ? null : this.fragments[this.i++];\n        if (fr) {\n            this.safeFrom = fr.openStart ? cutAt(fr.tree, fr.from + fr.offset, 1) - fr.offset : fr.from;\n            this.safeTo = fr.openEnd ? cutAt(fr.tree, fr.to + fr.offset, -1) - fr.offset : fr.to;\n            while (this.trees.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n            }\n            this.trees.push(fr.tree);\n            this.start.push(-fr.offset);\n            this.index.push(0);\n            this.nextStart = this.safeFrom;\n        }\n        else {\n            this.nextStart = 1e9;\n        }\n    }\n    // `pos` must be >= any previously given `pos` for this cursor\n    nodeAt(pos) {\n        if (pos < this.nextStart)\n            return null;\n        while (this.fragment && this.safeTo <= pos)\n            this.nextFragment();\n        if (!this.fragment)\n            return null;\n        for (;;) {\n            let last = this.trees.length - 1;\n            if (last < 0) { // End of tree\n                this.nextFragment();\n                return null;\n            }\n            let top = this.trees[last], index = this.index[last];\n            if (index == top.children.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n                continue;\n            }\n            let next = top.children[index];\n            let start = this.start[last] + top.positions[index];\n            if (start > pos) {\n                this.nextStart = start;\n                return null;\n            }\n            if (next instanceof Tree) {\n                if (start == pos) {\n                    if (start < this.safeFrom)\n                        return null;\n                    let end = start + next.length;\n                    if (end <= this.safeTo) {\n                        let lookAhead = next.prop(NodeProp.lookAhead);\n                        if (!lookAhead || end + lookAhead < this.fragment.to)\n                            return next;\n                    }\n                }\n                this.index[last]++;\n                if (start + next.length >= Math.max(this.safeFrom, pos)) { // Enter this node\n                    this.trees.push(next);\n                    this.start.push(start);\n                    this.index.push(0);\n                }\n            }\n            else {\n                this.index[last]++;\n                this.nextStart = start + next.length;\n            }\n        }\n    }\n}\nclass TokenCache {\n    constructor(parser, stream) {\n        this.stream = stream;\n        this.tokens = [];\n        this.mainToken = null;\n        this.actions = [];\n        this.tokens = parser.tokenizers.map(_ => new CachedToken);\n    }\n    getActions(stack) {\n        let actionIndex = 0;\n        let main = null;\n        let { parser } = stack.p, { tokenizers } = parser;\n        let mask = parser.stateSlot(stack.state, 3 /* ParseState.TokenizerMask */);\n        let context = stack.curContext ? stack.curContext.hash : 0;\n        let lookAhead = 0;\n        for (let i = 0; i < tokenizers.length; i++) {\n            if (((1 << i) & mask) == 0)\n                continue;\n            let tokenizer = tokenizers[i], token = this.tokens[i];\n            if (main && !tokenizer.fallback)\n                continue;\n            if (tokenizer.contextual || token.start != stack.pos || token.mask != mask || token.context != context) {\n                this.updateCachedToken(token, tokenizer, stack);\n                token.mask = mask;\n                token.context = context;\n            }\n            if (token.lookAhead > token.end + 25 /* Lookahead.Margin */)\n                lookAhead = Math.max(token.lookAhead, lookAhead);\n            if (token.value != 0 /* Term.Err */) {\n                let startIndex = actionIndex;\n                if (token.extended > -1)\n                    actionIndex = this.addActions(stack, token.extended, token.end, actionIndex);\n                actionIndex = this.addActions(stack, token.value, token.end, actionIndex);\n                if (!tokenizer.extend) {\n                    main = token;\n                    if (actionIndex > startIndex)\n                        break;\n                }\n            }\n        }\n        while (this.actions.length > actionIndex)\n            this.actions.pop();\n        if (lookAhead)\n            stack.setLookAhead(lookAhead);\n        if (!main && stack.pos == this.stream.end) {\n            main = new CachedToken;\n            main.value = stack.p.parser.eofTerm;\n            main.start = main.end = stack.pos;\n            actionIndex = this.addActions(stack, main.value, main.end, actionIndex);\n        }\n        this.mainToken = main;\n        return this.actions;\n    }\n    getMainToken(stack) {\n        if (this.mainToken)\n            return this.mainToken;\n        let main = new CachedToken, { pos, p } = stack;\n        main.start = pos;\n        main.end = Math.min(pos + 1, p.stream.end);\n        main.value = pos == p.stream.end ? p.parser.eofTerm : 0 /* Term.Err */;\n        return main;\n    }\n    updateCachedToken(token, tokenizer, stack) {\n        let start = this.stream.clipPos(stack.pos);\n        tokenizer.token(this.stream.reset(start, token), stack);\n        if (token.value > -1) {\n            let { parser } = stack.p;\n            for (let i = 0; i < parser.specialized.length; i++)\n                if (parser.specialized[i] == token.value) {\n                    let result = parser.specializers[i](this.stream.read(token.start, token.end), stack);\n                    if (result >= 0 && stack.p.parser.dialect.allows(result >> 1)) {\n                        if ((result & 1) == 0 /* Specialize.Specialize */)\n                            token.value = result >> 1;\n                        else\n                            token.extended = result >> 1;\n                        break;\n                    }\n                }\n        }\n        else {\n            token.value = 0 /* Term.Err */;\n            token.end = this.stream.clipPos(start + 1);\n        }\n    }\n    putAction(action, token, end, index) {\n        // Don't add duplicate actions\n        for (let i = 0; i < index; i += 3)\n            if (this.actions[i] == action)\n                return index;\n        this.actions[index++] = action;\n        this.actions[index++] = token;\n        this.actions[index++] = end;\n        return index;\n    }\n    addActions(stack, token, end, index) {\n        let { state } = stack, { parser } = stack.p, { data } = parser;\n        for (let set = 0; set < 2; set++) {\n            for (let i = parser.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */);; i += 3) {\n                if (data[i] == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */) {\n                        i = pair(data, i + 2);\n                    }\n                    else {\n                        if (index == 0 && data[i + 1] == 2 /* Seq.Other */)\n                            index = this.putAction(pair(data, i + 2), token, end, index);\n                        break;\n                    }\n                }\n                if (data[i] == token)\n                    index = this.putAction(pair(data, i + 1), token, end, index);\n            }\n        }\n        return index;\n    }\n}\nclass Parse {\n    constructor(parser, input, fragments, ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.recovering = 0;\n        this.nextStackID = 0x2654; // ♔, ♕, ♖, ♗, ♘, ♙, ♠, ♡, ♢, ♣, ♤, ♥, ♦, ♧\n        this.minStackPos = 0;\n        this.reused = [];\n        this.stoppedAt = null;\n        this.lastBigReductionStart = -1;\n        this.lastBigReductionSize = 0;\n        this.bigReductionCount = 0;\n        this.stream = new InputStream(input, ranges);\n        this.tokens = new TokenCache(parser, this.stream);\n        this.topTerm = parser.top[1];\n        let { from } = ranges[0];\n        this.stacks = [Stack.start(this, parser.top[0], from)];\n        this.fragments = fragments.length && this.stream.end - from > parser.bufferLength * 4\n            ? new FragmentCursor(fragments, parser.nodeSet) : null;\n    }\n    get parsedPos() {\n        return this.minStackPos;\n    }\n    // Move the parser forward. This will process all parse stacks at\n    // `this.pos` and try to advance them to a further position. If no\n    // stack for such a position is found, it'll start error-recovery.\n    //\n    // When the parse is finished, this will return a syntax tree. When\n    // not, it returns `null`.\n    advance() {\n        let stacks = this.stacks, pos = this.minStackPos;\n        // This will hold stacks beyond `pos`.\n        let newStacks = this.stacks = [];\n        let stopped, stoppedTokens;\n        // If a large amount of reductions happened with the same start\n        // position, force the stack out of that production in order to\n        // avoid creating a tree too deep to recurse through.\n        // (This is an ugly kludge, because unfortunately there is no\n        // straightforward, cheap way to check for this happening, due to\n        // the history of reductions only being available in an\n        // expensive-to-access format in the stack buffers.)\n        if (this.bigReductionCount > 300 /* Rec.MaxLeftAssociativeReductionCount */ && stacks.length == 1) {\n            let [s] = stacks;\n            while (s.forceReduce() && s.stack.length && s.stack[s.stack.length - 2] >= this.lastBigReductionStart) { }\n            this.bigReductionCount = this.lastBigReductionSize = 0;\n        }\n        // Keep advancing any stacks at `pos` until they either move\n        // forward or can't be advanced. Gather stacks that can't be\n        // advanced further in `stopped`.\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i];\n            for (;;) {\n                this.tokens.mainToken = null;\n                if (stack.pos > pos) {\n                    newStacks.push(stack);\n                }\n                else if (this.advanceStack(stack, newStacks, stacks)) {\n                    continue;\n                }\n                else {\n                    if (!stopped) {\n                        stopped = [];\n                        stoppedTokens = [];\n                    }\n                    stopped.push(stack);\n                    let tok = this.tokens.getMainToken(stack);\n                    stoppedTokens.push(tok.value, tok.end);\n                }\n                break;\n            }\n        }\n        if (!newStacks.length) {\n            let finished = stopped && findFinished(stopped);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Finish with \" + this.stackID(finished));\n                return this.stackToTree(finished);\n            }\n            if (this.parser.strict) {\n                if (verbose && stopped)\n                    console.log(\"Stuck with token \" + (this.tokens.mainToken ? this.parser.getName(this.tokens.mainToken.value) : \"none\"));\n                throw new SyntaxError(\"No parse at \" + pos);\n            }\n            if (!this.recovering)\n                this.recovering = 5 /* Rec.Distance */;\n        }\n        if (this.recovering && stopped) {\n            let finished = this.stoppedAt != null && stopped[0].pos > this.stoppedAt ? stopped[0]\n                : this.runRecovery(stopped, stoppedTokens, newStacks);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Force-finish \" + this.stackID(finished));\n                return this.stackToTree(finished.forceAll());\n            }\n        }\n        if (this.recovering) {\n            let maxRemaining = this.recovering == 1 ? 1 : this.recovering * 3 /* Rec.MaxRemainingPerStep */;\n            if (newStacks.length > maxRemaining) {\n                newStacks.sort((a, b) => b.score - a.score);\n                while (newStacks.length > maxRemaining)\n                    newStacks.pop();\n            }\n            if (newStacks.some(s => s.reducePos > pos))\n                this.recovering--;\n        }\n        else if (newStacks.length > 1) {\n            // Prune stacks that are in the same state, or that have been\n            // running without splitting for a while, to avoid getting stuck\n            // with multiple successful stacks running endlessly on.\n            outer: for (let i = 0; i < newStacks.length - 1; i++) {\n                let stack = newStacks[i];\n                for (let j = i + 1; j < newStacks.length; j++) {\n                    let other = newStacks[j];\n                    if (stack.sameState(other) ||\n                        stack.buffer.length > 500 /* Rec.MinBufferLengthPrune */ && other.buffer.length > 500 /* Rec.MinBufferLengthPrune */) {\n                        if (((stack.score - other.score) || (stack.buffer.length - other.buffer.length)) > 0) {\n                            newStacks.splice(j--, 1);\n                        }\n                        else {\n                            newStacks.splice(i--, 1);\n                            continue outer;\n                        }\n                    }\n                }\n            }\n            if (newStacks.length > 12 /* Rec.MaxStackCount */)\n                newStacks.splice(12 /* Rec.MaxStackCount */, newStacks.length - 12 /* Rec.MaxStackCount */);\n        }\n        this.minStackPos = newStacks[0].pos;\n        for (let i = 1; i < newStacks.length; i++)\n            if (newStacks[i].pos < this.minStackPos)\n                this.minStackPos = newStacks[i].pos;\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    // Returns an updated version of the given stack, or null if the\n    // stack can't advance normally. When `split` and `stacks` are\n    // given, stacks split off by ambiguous operations will be pushed to\n    // `split`, or added to `stacks` if they move `pos` forward.\n    advanceStack(stack, stacks, split) {\n        let start = stack.pos, { parser } = this;\n        let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n        if (this.stoppedAt != null && start > this.stoppedAt)\n            return stack.forceReduce() ? stack : null;\n        if (this.fragments) {\n            let strictCx = stack.curContext && stack.curContext.tracker.strict, cxHash = strictCx ? stack.curContext.hash : 0;\n            for (let cached = this.fragments.nodeAt(start); cached;) {\n                let match = this.parser.nodeSet.types[cached.type.id] == cached.type ? parser.getGoto(stack.state, cached.type.id) : -1;\n                if (match > -1 && cached.length && (!strictCx || (cached.prop(NodeProp.contextHash) || 0) == cxHash)) {\n                    stack.useNode(cached, match);\n                    if (verbose)\n                        console.log(base + this.stackID(stack) + ` (via reuse of ${parser.getName(cached.type.id)})`);\n                    return true;\n                }\n                if (!(cached instanceof Tree) || cached.children.length == 0 || cached.positions[0] > 0)\n                    break;\n                let inner = cached.children[0];\n                if (inner instanceof Tree && cached.positions[0] == 0)\n                    cached = inner;\n                else\n                    break;\n            }\n        }\n        let defaultReduce = parser.stateSlot(stack.state, 4 /* ParseState.DefaultReduce */);\n        if (defaultReduce > 0) {\n            stack.reduce(defaultReduce);\n            if (verbose)\n                console.log(base + this.stackID(stack) + ` (via always-reduce ${parser.getName(defaultReduce & 65535 /* Action.ValueMask */)})`);\n            return true;\n        }\n        if (stack.stack.length >= 8400 /* Rec.CutDepth */) {\n            while (stack.stack.length > 6000 /* Rec.CutTo */ && stack.forceReduce()) { }\n        }\n        let actions = this.tokens.getActions(stack);\n        for (let i = 0; i < actions.length;) {\n            let action = actions[i++], term = actions[i++], end = actions[i++];\n            let last = i == actions.length || !split;\n            let localStack = last ? stack : stack.split();\n            let main = this.tokens.mainToken;\n            localStack.apply(action, term, main ? main.start : localStack.pos, end);\n            if (verbose)\n                console.log(base + this.stackID(localStack) + ` (via ${(action & 65536 /* Action.ReduceFlag */) == 0 ? \"shift\"\n                    : `reduce of ${parser.getName(action & 65535 /* Action.ValueMask */)}`} for ${parser.getName(term)} @ ${start}${localStack == stack ? \"\" : \", split\"})`);\n            if (last)\n                return true;\n            else if (localStack.pos > start)\n                stacks.push(localStack);\n            else\n                split.push(localStack);\n        }\n        return false;\n    }\n    // Advance a given stack forward as far as it will go. Returns the\n    // (possibly updated) stack if it got stuck, or null if it moved\n    // forward and was given to `pushStackDedup`.\n    advanceFully(stack, newStacks) {\n        let pos = stack.pos;\n        for (;;) {\n            if (!this.advanceStack(stack, null, null))\n                return false;\n            if (stack.pos > pos) {\n                pushStackDedup(stack, newStacks);\n                return true;\n            }\n        }\n    }\n    runRecovery(stacks, tokens, newStacks) {\n        let finished = null, restarted = false;\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i], token = tokens[i << 1], tokenEnd = tokens[(i << 1) + 1];\n            let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n            if (stack.deadEnd) {\n                if (restarted)\n                    continue;\n                restarted = true;\n                stack.restart();\n                if (verbose)\n                    console.log(base + this.stackID(stack) + \" (restarted)\");\n                let done = this.advanceFully(stack, newStacks);\n                if (done)\n                    continue;\n            }\n            let force = stack.split(), forceBase = base;\n            for (let j = 0; force.forceReduce() && j < 10 /* Rec.ForceReduceLimit */; j++) {\n                if (verbose)\n                    console.log(forceBase + this.stackID(force) + \" (via force-reduce)\");\n                let done = this.advanceFully(force, newStacks);\n                if (done)\n                    break;\n                if (verbose)\n                    forceBase = this.stackID(force) + \" -> \";\n            }\n            for (let insert of stack.recoverByInsert(token)) {\n                if (verbose)\n                    console.log(base + this.stackID(insert) + \" (via recover-insert)\");\n                this.advanceFully(insert, newStacks);\n            }\n            if (this.stream.end > stack.pos) {\n                if (tokenEnd == stack.pos) {\n                    tokenEnd++;\n                    token = 0 /* Term.Err */;\n                }\n                stack.recoverByDelete(token, tokenEnd);\n                if (verbose)\n                    console.log(base + this.stackID(stack) + ` (via recover-delete ${this.parser.getName(token)})`);\n                pushStackDedup(stack, newStacks);\n            }\n            else if (!finished || finished.score < stack.score) {\n                finished = stack;\n            }\n        }\n        return finished;\n    }\n    // Convert the stack's buffer to a syntax tree.\n    stackToTree(stack) {\n        stack.close();\n        return Tree.build({ buffer: StackBufferCursor.create(stack),\n            nodeSet: this.parser.nodeSet,\n            topID: this.topTerm,\n            maxBufferLength: this.parser.bufferLength,\n            reused: this.reused,\n            start: this.ranges[0].from,\n            length: stack.pos - this.ranges[0].from,\n            minRepeatType: this.parser.minRepeatTerm });\n    }\n    stackID(stack) {\n        let id = (stackIDs || (stackIDs = new WeakMap)).get(stack);\n        if (!id)\n            stackIDs.set(stack, id = String.fromCodePoint(this.nextStackID++));\n        return id + stack;\n    }\n}\nfunction pushStackDedup(stack, newStacks) {\n    for (let i = 0; i < newStacks.length; i++) {\n        let other = newStacks[i];\n        if (other.pos == stack.pos && other.sameState(stack)) {\n            if (newStacks[i].score < stack.score)\n                newStacks[i] = stack;\n            return;\n        }\n    }\n    newStacks.push(stack);\n}\nclass Dialect {\n    constructor(source, flags, disabled) {\n        this.source = source;\n        this.flags = flags;\n        this.disabled = disabled;\n    }\n    allows(term) { return !this.disabled || this.disabled[term] == 0; }\n}\nconst id = x => x;\n/**\nContext trackers are used to track stateful context (such as\nindentation in the Python grammar, or parent elements in the XML\ngrammar) needed by external tokenizers. You declare them in a\ngrammar file as `@context exportName from \"module\"`.\n\nContext values should be immutable, and can be updated (replaced)\non shift or reduce actions.\n\nThe export used in a `@context` declaration should be of this\ntype.\n*/\nclass ContextTracker {\n    /**\n    Define a context tracker.\n    */\n    constructor(spec) {\n        this.start = spec.start;\n        this.shift = spec.shift || id;\n        this.reduce = spec.reduce || id;\n        this.reuse = spec.reuse || id;\n        this.hash = spec.hash || (() => 0);\n        this.strict = spec.strict !== false;\n    }\n}\n/**\nHolds the parse tables for a given grammar, as generated by\n`lezer-generator`, and provides [methods](#common.Parser) to parse\ncontent with.\n*/\nclass LRParser extends Parser {\n    /**\n    @internal\n    */\n    constructor(spec) {\n        super();\n        /**\n        @internal\n        */\n        this.wrappers = [];\n        if (spec.version != 14 /* File.Version */)\n            throw new RangeError(`Parser version (${spec.version}) doesn't match runtime version (${14 /* File.Version */})`);\n        let nodeNames = spec.nodeNames.split(\" \");\n        this.minRepeatTerm = nodeNames.length;\n        for (let i = 0; i < spec.repeatNodeCount; i++)\n            nodeNames.push(\"\");\n        let topTerms = Object.keys(spec.topRules).map(r => spec.topRules[r][1]);\n        let nodeProps = [];\n        for (let i = 0; i < nodeNames.length; i++)\n            nodeProps.push([]);\n        function setProp(nodeID, prop, value) {\n            nodeProps[nodeID].push([prop, prop.deserialize(String(value))]);\n        }\n        if (spec.nodeProps)\n            for (let propSpec of spec.nodeProps) {\n                let prop = propSpec[0];\n                if (typeof prop == \"string\")\n                    prop = NodeProp[prop];\n                for (let i = 1; i < propSpec.length;) {\n                    let next = propSpec[i++];\n                    if (next >= 0) {\n                        setProp(next, prop, propSpec[i++]);\n                    }\n                    else {\n                        let value = propSpec[i + -next];\n                        for (let j = -next; j > 0; j--)\n                            setProp(propSpec[i++], prop, value);\n                        i++;\n                    }\n                }\n            }\n        this.nodeSet = new NodeSet(nodeNames.map((name, i) => NodeType.define({\n            name: i >= this.minRepeatTerm ? undefined : name,\n            id: i,\n            props: nodeProps[i],\n            top: topTerms.indexOf(i) > -1,\n            error: i == 0,\n            skipped: spec.skippedNodes && spec.skippedNodes.indexOf(i) > -1\n        })));\n        if (spec.propSources)\n            this.nodeSet = this.nodeSet.extend(...spec.propSources);\n        this.strict = false;\n        this.bufferLength = DefaultBufferLength;\n        let tokenArray = decodeArray(spec.tokenData);\n        this.context = spec.context;\n        this.specializerSpecs = spec.specialized || [];\n        this.specialized = new Uint16Array(this.specializerSpecs.length);\n        for (let i = 0; i < this.specializerSpecs.length; i++)\n            this.specialized[i] = this.specializerSpecs[i].term;\n        this.specializers = this.specializerSpecs.map(getSpecializer);\n        this.states = decodeArray(spec.states, Uint32Array);\n        this.data = decodeArray(spec.stateData);\n        this.goto = decodeArray(spec.goto);\n        this.maxTerm = spec.maxTerm;\n        this.tokenizers = spec.tokenizers.map(value => typeof value == \"number\" ? new TokenGroup(tokenArray, value) : value);\n        this.topRules = spec.topRules;\n        this.dialects = spec.dialects || {};\n        this.dynamicPrecedences = spec.dynamicPrecedences || null;\n        this.tokenPrecTable = spec.tokenPrec;\n        this.termNames = spec.termNames || null;\n        this.maxNode = this.nodeSet.types.length - 1;\n        this.dialect = this.parseDialect();\n        this.top = this.topRules[Object.keys(this.topRules)[0]];\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new Parse(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /**\n    Get a goto table entry @internal\n    */\n    getGoto(state, term, loose = false) {\n        let table = this.goto;\n        if (term >= table[0])\n            return -1;\n        for (let pos = table[term + 1];;) {\n            let groupTag = table[pos++], last = groupTag & 1;\n            let target = table[pos++];\n            if (last && loose)\n                return target;\n            for (let end = pos + (groupTag >> 1); pos < end; pos++)\n                if (table[pos] == state)\n                    return target;\n            if (last)\n                return -1;\n        }\n    }\n    /**\n    Check if this state has an action for a given terminal @internal\n    */\n    hasAction(state, terminal) {\n        let data = this.data;\n        for (let set = 0; set < 2; set++) {\n            for (let i = this.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */), next;; i += 3) {\n                if ((next = data[i]) == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */)\n                        next = data[i = pair(data, i + 2)];\n                    else if (data[i + 1] == 2 /* Seq.Other */)\n                        return pair(data, i + 2);\n                    else\n                        break;\n                }\n                if (next == terminal || next == 0 /* Term.Err */)\n                    return pair(data, i + 1);\n            }\n        }\n        return 0;\n    }\n    /**\n    @internal\n    */\n    stateSlot(state, slot) {\n        return this.states[(state * 6 /* ParseState.Size */) + slot];\n    }\n    /**\n    @internal\n    */\n    stateFlag(state, flag) {\n        return (this.stateSlot(state, 0 /* ParseState.Flags */) & flag) > 0;\n    }\n    /**\n    @internal\n    */\n    validAction(state, action) {\n        return !!this.allActions(state, a => a == action ? true : null);\n    }\n    /**\n    @internal\n    */\n    allActions(state, action) {\n        let deflt = this.stateSlot(state, 4 /* ParseState.DefaultReduce */);\n        let result = deflt ? action(deflt) : undefined;\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */); result == null; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            result = action(pair(this.data, i + 1));\n        }\n        return result;\n    }\n    /**\n    Get the states that can follow this one through shift actions or\n    goto jumps. @internal\n    */\n    nextStates(state) {\n        let result = [];\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */);; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            if ((this.data[i + 2] & (65536 /* Action.ReduceFlag */ >> 16)) == 0) {\n                let value = this.data[i + 1];\n                if (!result.some((v, i) => (i & 1) && v == value))\n                    result.push(this.data[i], value);\n            }\n        }\n        return result;\n    }\n    /**\n    Configure the parser. Returns a new parser instance that has the\n    given settings modified. Settings not provided in `config` are\n    kept from the original parser.\n    */\n    configure(config) {\n        // Hideous reflection-based kludge to make it easy to create a\n        // slightly modified copy of a parser.\n        let copy = Object.assign(Object.create(LRParser.prototype), this);\n        if (config.props)\n            copy.nodeSet = this.nodeSet.extend(...config.props);\n        if (config.top) {\n            let info = this.topRules[config.top];\n            if (!info)\n                throw new RangeError(`Invalid top rule name ${config.top}`);\n            copy.top = info;\n        }\n        if (config.tokenizers)\n            copy.tokenizers = this.tokenizers.map(t => {\n                let found = config.tokenizers.find(r => r.from == t);\n                return found ? found.to : t;\n            });\n        if (config.specializers) {\n            copy.specializers = this.specializers.slice();\n            copy.specializerSpecs = this.specializerSpecs.map((s, i) => {\n                let found = config.specializers.find(r => r.from == s.external);\n                if (!found)\n                    return s;\n                let spec = Object.assign(Object.assign({}, s), { external: found.to });\n                copy.specializers[i] = getSpecializer(spec);\n                return spec;\n            });\n        }\n        if (config.contextTracker)\n            copy.context = config.contextTracker;\n        if (config.dialect)\n            copy.dialect = this.parseDialect(config.dialect);\n        if (config.strict != null)\n            copy.strict = config.strict;\n        if (config.wrap)\n            copy.wrappers = copy.wrappers.concat(config.wrap);\n        if (config.bufferLength != null)\n            copy.bufferLength = config.bufferLength;\n        return copy;\n    }\n    /**\n    Tells you whether any [parse wrappers](#lr.ParserConfig.wrap)\n    are registered for this parser.\n    */\n    hasWrappers() {\n        return this.wrappers.length > 0;\n    }\n    /**\n    Returns the name associated with a given term. This will only\n    work for all terms when the parser was generated with the\n    `--names` option. By default, only the names of tagged terms are\n    stored.\n    */\n    getName(term) {\n        return this.termNames ? this.termNames[term] : String(term <= this.maxNode && this.nodeSet.types[term].name || term);\n    }\n    /**\n    The eof term id is always allocated directly after the node\n    types. @internal\n    */\n    get eofTerm() { return this.maxNode + 1; }\n    /**\n    The type of top node produced by the parser.\n    */\n    get topNode() { return this.nodeSet.types[this.top[1]]; }\n    /**\n    @internal\n    */\n    dynamicPrecedence(term) {\n        let prec = this.dynamicPrecedences;\n        return prec == null ? 0 : prec[term] || 0;\n    }\n    /**\n    @internal\n    */\n    parseDialect(dialect) {\n        let values = Object.keys(this.dialects), flags = values.map(() => false);\n        if (dialect)\n            for (let part of dialect.split(\" \")) {\n                let id = values.indexOf(part);\n                if (id >= 0)\n                    flags[id] = true;\n            }\n        let disabled = null;\n        for (let i = 0; i < values.length; i++)\n            if (!flags[i]) {\n                for (let j = this.dialects[values[i]], id; (id = this.data[j++]) != 65535 /* Seq.End */;)\n                    (disabled || (disabled = new Uint8Array(this.maxTerm + 1)))[id] = 1;\n            }\n        return new Dialect(dialect, flags, disabled);\n    }\n    /**\n    Used by the output of the parser generator. Not available to\n    user code. @hide\n    */\n    static deserialize(spec) {\n        return new LRParser(spec);\n    }\n}\nfunction pair(data, off) { return data[off] | (data[off + 1] << 16); }\nfunction findFinished(stacks) {\n    let best = null;\n    for (let stack of stacks) {\n        let stopped = stack.p.stoppedAt;\n        if ((stack.pos == stack.p.stream.end || stopped != null && stack.pos > stopped) &&\n            stack.p.parser.stateFlag(stack.state, 2 /* StateFlag.Accepting */) &&\n            (!best || best.score < stack.score))\n            best = stack;\n    }\n    return best;\n}\nfunction getSpecializer(spec) {\n    if (spec.external) {\n        let mask = spec.extend ? 1 /* Specialize.Extend */ : 0 /* Specialize.Specialize */;\n        return (value, stack) => (spec.external(value, stack) << 1) | mask;\n    }\n    return spec.get;\n}\n\nexport { ContextTracker, ExternalTokenizer, InputStream, LRParser, LocalTokenGroup, Stack };\n"], "mappings": ";;;;;;;;;;;AAQA,IAAM,QAAN,MAAM,OAAM;AAAA;AAAA;AAAA;AAAA,EAIR,YAIA,GAKA,OAIA,OAQA,WAIA,KAMA,OAOA,QASA,YAIA,YAIA,YAAY,GAQZ,QAAQ;AACJ,SAAK,IAAI;AACT,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,WAAO,IAAI,KAAK,MAAM,OAAO,CAAC,GAAG,MAAM,IAAI,KAAK,CAAC,EAAE,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,GAAG,GAAG,KAAK,QAAQ,MAAM,KAAK,QAAQ,EAAE;AAAA,EAC3H;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,MAAM,GAAG,OAAO,MAAM,GAAG;AAC5B,QAAI,KAAK,EAAE,OAAO;AAClB,WAAO,IAAI,OAAM,GAAG,CAAC,GAAG,OAAO,KAAK,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,aAAa,IAAI,GAAG,KAAK,IAAI,MAAM,GAAG,IAAI;AAAA,EAC1G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,UAAU;AAAE,WAAO,KAAK,aAAa,KAAK,WAAW,UAAU;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzE,UAAU,OAAO,OAAO;AACpB,SAAK,MAAM,KAAK,KAAK,OAAO,OAAO,KAAK,aAAa,KAAK,OAAO,MAAM;AACvE,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,QAAQ;AACX,QAAI;AACJ,QAAI,QAAQ,UAAU,IAAkC,OAAO,SAAS;AACxE,QAAI,EAAE,OAAO,IAAI,KAAK;AACtB,QAAI,kBAAkB,KAAK,YAAY,KAAK,MAAM;AAClD,QAAI;AACA,WAAK,aAAa,KAAK,GAAG;AAC9B,QAAI,QAAQ,OAAO,kBAAkB,IAAI;AACzC,QAAI;AACA,WAAK,SAAS;AAClB,QAAI,SAAS,GAAG;AACZ,WAAK,UAAU,OAAO,QAAQ,KAAK,OAAO,MAAM,IAAI,GAAG,KAAK,SAAS;AAGrE,UAAI,OAAO,OAAO;AACd,aAAK,UAAU,MAAM,KAAK,WAAW,KAAK,WAAW,kBAAkB,IAAI,GAAG,IAAI;AACtF,WAAK,cAAc,MAAM,KAAK,SAAS;AACvC;AAAA,IACJ;AAMA,QAAI,OAAO,KAAK,MAAM,UAAW,QAAQ,KAAK,KAAM,SAAS,SAA+B,IAAI;AAChG,QAAI,QAAQ,OAAO,KAAK,MAAM,OAAO,CAAC,IAAI,KAAK,EAAE,OAAO,CAAC,EAAE,MAAM,OAAO,KAAK,YAAY;AAIzF,QAAI,QAAQ,OAAsC,GAAG,KAAK,KAAK,EAAE,OAAO,QAAQ,MAAM,IAAI,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc;AAC/I,UAAI,SAAS,KAAK,EAAE,uBAAuB;AACvC,aAAK,EAAE;AACP,aAAK,EAAE,uBAAuB;AAAA,MAClC,WACS,KAAK,EAAE,uBAAuB,MAAM;AACzC,aAAK,EAAE,oBAAoB;AAC3B,aAAK,EAAE,wBAAwB;AAC/B,aAAK,EAAE,uBAAuB;AAAA,MAClC;AAAA,IACJ;AACA,QAAI,aAAa,OAAO,KAAK,MAAM,OAAO,CAAC,IAAI,GAAG,QAAQ,KAAK,aAAa,KAAK,OAAO,SAAS;AAEjG,QAAI,OAAO,OAAO,iBAAkB,SAAS,QAAiC;AAC1E,UAAI,MAAM,OAAO;AAAA,QAAU,KAAK;AAAA,QAAO;AAAA;AAAA,MAAyB,IAAI,KAAK,MAAM,KAAK;AACpF,WAAK,UAAU,MAAM,OAAO,KAAK,QAAQ,GAAG,IAAI;AAAA,IACpD;AACA,QAAI,SAAS,QAA8B;AACvC,WAAK,QAAQ,KAAK,MAAM,IAAI;AAAA,IAChC,OACK;AACD,UAAI,cAAc,KAAK,MAAM,OAAO,CAAC;AACrC,WAAK,QAAQ,OAAO,QAAQ,aAAa,MAAM,IAAI;AAAA,IACvD;AACA,WAAO,KAAK,MAAM,SAAS;AACvB,WAAK,MAAM,IAAI;AACnB,SAAK,cAAc,MAAM,KAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,MAAM,OAAO,KAAK,OAAO,GAAG,WAAW,OAAO;AACpD,QAAI,QAAQ,MACP,CAAC,KAAK,MAAM,UAAU,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC,IAAI,KAAK,OAAO,SAAS,KAAK,aAAa;AAElG,UAAI,MAAM,MAAM,MAAM,KAAK,OAAO;AAClC,UAAI,OAAO,KAAK,IAAI,QAAQ;AACxB,cAAM,IAAI,aAAa,IAAI,OAAO;AAClC,cAAM,IAAI;AAAA,MACd;AACA,UAAI,MAAM,KAAK,IAAI,OAAO,MAAM,CAAC,KAAK,KAAoB,IAAI,OAAO,MAAM,CAAC,IAAI,IAAI;AAChF,YAAI,SAAS;AACT;AACJ,YAAI,IAAI,OAAO,MAAM,CAAC,KAAK,OAAO;AAC9B,cAAI,OAAO,MAAM,CAAC,IAAI;AACtB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,YAAY,KAAK,OAAO,KAAK;AAC9B,WAAK,OAAO,KAAK,MAAM,OAAO,KAAK,IAAI;AAAA,IAC3C,OACK;AACD,UAAI,QAAQ,KAAK,OAAO;AACxB,UAAI,QAAQ,KAAK,KAAK,OAAO,QAAQ,CAAC,KAAK,GAAkB;AACzD,YAAI,WAAW;AACf,iBAAS,OAAO,OAAO,OAAO,KAAK,KAAK,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,GAAG;AACvE,cAAI,KAAK,OAAO,OAAO,CAAC,KAAK,GAAG;AAC5B,uBAAW;AACX;AAAA,UACJ;AAAA,QACJ;AACA,YAAI;AACA,iBAAO,QAAQ,KAAK,KAAK,OAAO,QAAQ,CAAC,IAAI,KAAK;AAE9C,iBAAK,OAAO,KAAK,IAAI,KAAK,OAAO,QAAQ,CAAC;AAC1C,iBAAK,OAAO,QAAQ,CAAC,IAAI,KAAK,OAAO,QAAQ,CAAC;AAC9C,iBAAK,OAAO,QAAQ,CAAC,IAAI,KAAK,OAAO,QAAQ,CAAC;AAC9C,iBAAK,OAAO,QAAQ,CAAC,IAAI,KAAK,OAAO,QAAQ,CAAC;AAC9C,qBAAS;AACT,gBAAI,OAAO;AACP,sBAAQ;AAAA,UAChB;AAAA,MACR;AACA,WAAK,OAAO,KAAK,IAAI;AACrB,WAAK,OAAO,QAAQ,CAAC,IAAI;AACzB,WAAK,OAAO,QAAQ,CAAC,IAAI;AACzB,WAAK,OAAO,QAAQ,CAAC,IAAI;AAAA,IAC7B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,QAAQ,MAAM,OAAO,KAAK;AAC5B,QAAI,SAAS,QAA8B;AACvC,WAAK,UAAU,SAAS,OAA8B,KAAK,GAAG;AAAA,IAClE,YACU,SAAS,WAAiC,GAAG;AACnD,UAAI,YAAY,QAAQ,EAAE,OAAO,IAAI,KAAK;AAC1C,UAAI,MAAM,KAAK,OAAO,QAAQ,OAAO,SAAS;AAC1C,aAAK,MAAM;AACX,YAAI,CAAC,OAAO;AAAA,UAAU;AAAA,UAAW;AAAA;AAAA,QAAyB;AACtD,eAAK,YAAY;AAAA,MACzB;AACA,WAAK,UAAU,WAAW,KAAK;AAC/B,WAAK,aAAa,MAAM,KAAK;AAC7B,UAAI,QAAQ,OAAO;AACf,aAAK,OAAO,KAAK,MAAM,OAAO,KAAK,CAAC;AAAA,IAC5C,OACK;AACD,WAAK,MAAM;AACX,WAAK,aAAa,MAAM,KAAK;AAC7B,UAAI,QAAQ,KAAK,EAAE,OAAO;AACtB,aAAK,OAAO,KAAK,MAAM,OAAO,KAAK,CAAC;AAAA,IAC5C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,QAAQ,MAAM,WAAW,SAAS;AACpC,QAAI,SAAS;AACT,WAAK,OAAO,MAAM;AAAA;AAElB,WAAK,MAAM,QAAQ,MAAM,WAAW,OAAO;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,OAAO,MAAM;AACjB,QAAI,QAAQ,KAAK,EAAE,OAAO,SAAS;AACnC,QAAI,QAAQ,KAAK,KAAK,EAAE,OAAO,KAAK,KAAK,OAAO;AAC5C,WAAK,EAAE,OAAO,KAAK,KAAK;AACxB;AAAA,IACJ;AACA,QAAI,QAAQ,KAAK;AACjB,SAAK,YAAY,KAAK,MAAM,QAAQ,MAAM;AAC1C,SAAK,UAAU,MAAM,KAAK;AAC1B,SAAK,OAAO;AAAA,MAAK;AAAA,MAAO;AAAA,MAAO,KAAK;AAAA,MAAW;AAAA;AAAA,IAAgD;AAC/F,QAAI,KAAK;AACL,WAAK,cAAc,KAAK,WAAW,QAAQ,MAAM,KAAK,WAAW,SAAS,OAAO,MAAM,KAAK,EAAE,OAAO,MAAM,KAAK,MAAM,MAAM,MAAM,CAAC,CAAC;AAAA,EAC5I;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACJ,QAAI,SAAS;AACb,QAAI,MAAM,OAAO,OAAO;AAKxB,WAAO,MAAM,KAAK,OAAO,OAAO,MAAM,CAAC,IAAI,OAAO;AAC9C,aAAO;AACX,QAAI,SAAS,OAAO,OAAO,MAAM,GAAG,GAAG,OAAO,OAAO,aAAa;AAElE,WAAO,UAAU,QAAQ,OAAO;AAC5B,eAAS,OAAO;AACpB,WAAO,IAAI,OAAM,KAAK,GAAG,KAAK,MAAM,MAAM,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,KAAK,YAAY,KAAK,WAAW,MAAM;AAAA,EACxJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,MAAM,SAAS;AAC3B,QAAI,SAAS,QAAQ,KAAK,EAAE,OAAO;AACnC,QAAI;AACA,WAAK,UAAU,MAAM,KAAK,KAAK,SAAS,CAAC;AAC7C,SAAK,UAAU,GAAkB,KAAK,KAAK,SAAS,SAAS,IAAI,CAAC;AAClE,SAAK,MAAM,KAAK,YAAY;AAC5B,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,MAAM;AACX,aAAS,MAAM,IAAI,eAAe,IAAI,OAAK;AACvC,UAAI,SAAS,KAAK,EAAE,OAAO;AAAA,QAAU,IAAI;AAAA,QAAO;AAAA;AAAA,MAAgC,KAAK,KAAK,EAAE,OAAO,UAAU,IAAI,OAAO,IAAI;AAC5H,UAAI,UAAU;AACV,eAAO;AACX,WAAK,SAAS,UAAkC;AAC5C,eAAO;AACX,UAAI,OAAO,MAAM;AAAA,IACrB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,MAAM;AAClB,QAAI,KAAK,MAAM,UAAU;AACrB,aAAO,CAAC;AACZ,QAAI,aAAa,KAAK,EAAE,OAAO,WAAW,KAAK,KAAK;AACpD,QAAI,WAAW,SAAS,KAA2B,KAAK,KAAK,MAAM,UAAU,KAA0C;AACnH,UAAI,OAAO,CAAC;AACZ,eAAS,IAAI,GAAG,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AAC9C,aAAK,IAAI,WAAW,IAAI,CAAC,MAAM,KAAK,SAAS,KAAK,EAAE,OAAO,UAAU,GAAG,IAAI;AACxE,eAAK,KAAK,WAAW,CAAC,GAAG,CAAC;AAAA,MAClC;AACA,UAAI,KAAK,MAAM,SAAS;AACpB,iBAAS,IAAI,GAAG,KAAK,SAAS,KAA2B,KAAK,IAAI,WAAW,QAAQ,KAAK,GAAG;AACzF,cAAI,IAAI,WAAW,IAAI,CAAC;AACxB,cAAI,CAAC,KAAK,KAAK,CAAC,GAAGA,OAAOA,KAAI,KAAM,KAAK,CAAC;AACtC,iBAAK,KAAK,WAAW,CAAC,GAAG,CAAC;AAAA,QAClC;AACJ,mBAAa;AAAA,IACjB;AACA,QAAI,SAAS,CAAC;AACd,aAAS,IAAI,GAAG,IAAI,WAAW,UAAU,OAAO,SAAS,GAAyB,KAAK,GAAG;AACtF,UAAI,IAAI,WAAW,IAAI,CAAC;AACxB,UAAI,KAAK,KAAK;AACV;AACJ,UAAI,QAAQ,KAAK,MAAM;AACvB,YAAM,UAAU,GAAG,KAAK,GAAG;AAC3B,YAAM,UAAU,GAAkB,MAAM,KAAK,MAAM,KAAK,GAAG,IAAI;AAC/D,YAAM,aAAa,WAAW,CAAC,GAAG,KAAK,GAAG;AAC1C,YAAM,YAAY,KAAK;AACvB,YAAM,SAAS;AACf,aAAO,KAAK,KAAK;AAAA,IACrB;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACV,QAAI,EAAE,OAAO,IAAI,KAAK;AACtB,QAAI,SAAS,OAAO;AAAA,MAAU,KAAK;AAAA,MAAO;AAAA;AAAA,IAA+B;AACzE,SAAK,SAAS,UAAkC;AAC5C,aAAO;AACX,QAAI,CAAC,OAAO,YAAY,KAAK,OAAO,MAAM,GAAG;AACzC,UAAI,QAAQ,UAAU,IAAkC,OAAO,SAAS;AACxE,UAAI,SAAS,KAAK,MAAM,SAAS,QAAQ;AACzC,UAAI,SAAS,KAAK,OAAO,QAAQ,KAAK,MAAM,MAAM,GAAG,MAAM,KAAK,IAAI,GAAG;AACnE,YAAI,SAAS,KAAK,oBAAoB;AACtC,YAAI,UAAU;AACV,iBAAO;AACX,iBAAS;AAAA,MACb;AACA,WAAK,UAAU,GAAkB,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI;AAC5D,WAAK,SAAS;AAAA,IAClB;AACA,SAAK,YAAY,KAAK;AACtB,SAAK,OAAO,MAAM;AAClB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB;AAClB,QAAI,EAAE,OAAO,IAAI,KAAK,GAAG,OAAO,CAAC;AACjC,QAAI,UAAU,CAAC,OAAO,UAAU;AAC5B,UAAI,KAAK,SAAS,KAAK;AACnB;AACJ,WAAK,KAAK,KAAK;AACf,aAAO,OAAO,WAAW,OAAO,CAAC,WAAW;AACxC,YAAI,UAAU,SAA+B,QAA+B;AAAA,iBACnE,SAAS,OAA+B;AAC7C,cAAI,UAAU,UAAU,MAAoC;AAC5D,cAAI,SAAS,GAAG;AACZ,gBAAI,OAAO,SAAS,OAA8B,SAAS,KAAK,MAAM,SAAS,SAAS;AACxF,gBAAI,UAAU,KAAK,OAAO,QAAQ,KAAK,MAAM,MAAM,GAAG,MAAM,KAAK,KAAK;AAClE,qBAAQ,UAAU,KAAoC,QAAgC;AAAA,UAC9F;AAAA,QACJ,OACK;AACD,cAAI,QAAQ,QAAQ,QAAQ,QAAQ,CAAC;AACrC,cAAI,SAAS;AACT,mBAAO;AAAA,QACf;AAAA,MACJ,CAAC;AAAA,IACL;AACA,WAAO,QAAQ,KAAK,OAAO,CAAC;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,WAAO,CAAC,KAAK,EAAE,OAAO;AAAA,MAAU,KAAK;AAAA,MAAO;AAAA;AAAA,IAA2B,GAAG;AACtE,UAAI,CAAC,KAAK,YAAY,GAAG;AACrB,aAAK,UAAU,GAAkB,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI;AAC5D;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU;AACV,QAAI,KAAK,MAAM,UAAU;AACrB,aAAO;AACX,QAAI,EAAE,OAAO,IAAI,KAAK;AACtB,WAAO,OAAO,KAAK,OAAO;AAAA,MAAU,KAAK;AAAA,MAAO;AAAA;AAAA,IAA0B,CAAC,KAAK,SAC5E,CAAC,OAAO;AAAA,MAAU,KAAK;AAAA,MAAO;AAAA;AAAA,IAAgC;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACN,SAAK,UAAU,GAAkB,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI;AAC5D,SAAK,QAAQ,KAAK,MAAM,CAAC;AACzB,SAAK,MAAM,SAAS;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,OAAO;AACb,QAAI,KAAK,SAAS,MAAM,SAAS,KAAK,MAAM,UAAU,MAAM,MAAM;AAC9D,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,UAAI,KAAK,MAAM,CAAC,KAAK,MAAM,MAAM,CAAC;AAC9B,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AAAE,WAAO,KAAK,EAAE;AAAA,EAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrC,eAAe,WAAW;AAAE,WAAO,KAAK,EAAE,OAAO,QAAQ,MAAM,SAAS;AAAA,EAAG;AAAA,EAC3E,aAAa,MAAM,OAAO;AACtB,QAAI,KAAK;AACL,WAAK,cAAc,KAAK,WAAW,QAAQ,MAAM,KAAK,WAAW,SAAS,MAAM,MAAM,KAAK,EAAE,OAAO,MAAM,KAAK,CAAC,CAAC;AAAA,EACzH;AAAA,EACA,cAAc,MAAM,OAAO;AACvB,QAAI,KAAK;AACL,WAAK,cAAc,KAAK,WAAW,QAAQ,OAAO,KAAK,WAAW,SAAS,MAAM,MAAM,KAAK,EAAE,OAAO,MAAM,KAAK,CAAC,CAAC;AAAA,EAC1H;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACV,QAAI,OAAO,KAAK,OAAO,SAAS;AAChC,QAAI,OAAO,KAAK,KAAK,OAAO,IAAI,KAAK;AACjC,WAAK,OAAO,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,KAAK,EAAE;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACZ,QAAI,OAAO,KAAK,OAAO,SAAS;AAChC,QAAI,OAAO,KAAK,KAAK,OAAO,IAAI,KAAK;AACjC,WAAK,OAAO,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,EAAE;AAAA,EAC/D;AAAA,EACA,cAAc,SAAS;AACnB,QAAI,WAAW,KAAK,WAAW,SAAS;AACpC,UAAI,QAAQ,IAAI,aAAa,KAAK,WAAW,SAAS,OAAO;AAC7D,UAAI,MAAM,QAAQ,KAAK,WAAW;AAC9B,aAAK,YAAY;AACrB,WAAK,aAAa;AAAA,IACtB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,WAAW;AACpB,QAAI,YAAY,KAAK,WAAW;AAC5B,WAAK,cAAc;AACnB,WAAK,YAAY;AAAA,IACrB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,QAAI,KAAK,cAAc,KAAK,WAAW,QAAQ;AAC3C,WAAK,YAAY;AACrB,QAAI,KAAK,YAAY;AACjB,WAAK,cAAc;AAAA,EAC3B;AACJ;AACA,IAAM,eAAN,MAAmB;AAAA,EACf,YAAY,SAAS,SAAS;AAC1B,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,OAAO,QAAQ,SAAS,QAAQ,KAAK,OAAO,IAAI;AAAA,EACzD;AACJ;AAGA,IAAM,iBAAN,MAAqB;AAAA,EACjB,YAAY,OAAO;AACf,SAAK,QAAQ;AACb,SAAK,QAAQ,MAAM;AACnB,SAAK,QAAQ,MAAM;AACnB,SAAK,OAAO,KAAK,MAAM;AAAA,EAC3B;AAAA,EACA,OAAO,QAAQ;AACX,QAAI,OAAO,SAAS,OAA8B,QAAQ,UAAU;AACpE,QAAI,SAAS,GAAG;AACZ,UAAI,KAAK,SAAS,KAAK,MAAM;AACzB,aAAK,QAAQ,KAAK,MAAM,MAAM;AAClC,WAAK,MAAM,KAAK,KAAK,OAAO,GAAG,CAAC;AAChC,WAAK,QAAQ;AAAA,IACjB,OACK;AACD,WAAK,SAAS,QAAQ,KAAK;AAAA,IAC/B;AACA,QAAI,OAAO,KAAK,MAAM,EAAE,OAAO,QAAQ,KAAK,MAAM,KAAK,OAAO,CAAC,GAAG,MAAM,IAAI;AAC5E,SAAK,QAAQ;AAAA,EACjB;AACJ;AAGA,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACpB,YAAY,OAAO,KAAK,OAAO;AAC3B,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,SAAS,MAAM;AACpB,QAAI,KAAK,SAAS;AACd,WAAK,UAAU;AAAA,EACvB;AAAA,EACA,OAAO,OAAO,OAAO,MAAM,MAAM,aAAa,MAAM,OAAO,QAAQ;AAC/D,WAAO,IAAI,mBAAkB,OAAO,KAAK,MAAM,MAAM,UAAU;AAAA,EACnE;AAAA,EACA,YAAY;AACR,QAAI,OAAO,KAAK,MAAM;AACtB,QAAI,QAAQ,MAAM;AACd,WAAK,QAAQ,KAAK,MAAM,aAAa,KAAK;AAC1C,WAAK,QAAQ;AACb,WAAK,SAAS,KAAK;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,KAAK,OAAO,KAAK,QAAQ,CAAC;AAAA,EAAG;AAAA,EAC/C,IAAI,QAAQ;AAAE,WAAO,KAAK,OAAO,KAAK,QAAQ,CAAC;AAAA,EAAG;AAAA,EAClD,IAAI,MAAM;AAAE,WAAO,KAAK,OAAO,KAAK,QAAQ,CAAC;AAAA,EAAG;AAAA,EAChD,IAAI,OAAO;AAAE,WAAO,KAAK,OAAO,KAAK,QAAQ,CAAC;AAAA,EAAG;AAAA,EACjD,OAAO;AACH,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,QAAI,KAAK,SAAS;AACd,WAAK,UAAU;AAAA,EACvB;AAAA,EACA,OAAO;AACH,WAAO,IAAI,mBAAkB,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK;AAAA,EACjE;AACJ;AAIA,SAAS,YAAY,OAAO,OAAO,aAAa;AAC5C,MAAI,OAAO,SAAS;AAChB,WAAO;AACX,MAAI,QAAQ;AACZ,WAAS,MAAM,GAAG,MAAM,GAAG,MAAM,MAAM,UAAS;AAC5C,QAAI,QAAQ;AACZ,eAAS;AACL,UAAI,OAAO,MAAM,WAAW,KAAK,GAAG,OAAO;AAC3C,UAAI,QAAQ,KAA6B;AACrC,gBAAQ;AACR;AAAA,MACJ;AACA,UAAI,QAAQ;AACR;AACJ,UAAI,QAAQ;AACR;AACJ,UAAI,QAAQ,OAAO;AACnB,UAAI,SAAS,IAAsB;AAC/B,iBAAS;AACT,eAAO;AAAA,MACX;AACA,eAAS;AACT,UAAI;AACA;AACJ,eAAS;AAAA,IACb;AACA,QAAI;AACA,YAAM,KAAK,IAAI;AAAA;AAEf,cAAQ,IAAI,KAAK,KAAK;AAAA,EAC9B;AACA,SAAO;AACX;AAEA,IAAM,cAAN,MAAkB;AAAA,EACd,cAAc;AACV,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EACnB;AACJ;AACA,IAAM,YAAY,IAAI;AAOtB,IAAM,cAAN,MAAkB;AAAA;AAAA;AAAA;AAAA,EAId,YAIA,OAIA,QAAQ;AACJ,SAAK,QAAQ;AACb,SAAK,SAAS;AAId,SAAK,QAAQ;AAIb,SAAK,WAAW;AAIhB,SAAK,SAAS;AACd,SAAK,YAAY;AAKjB,SAAK,OAAO;AAIZ,SAAK,QAAQ;AACb,SAAK,aAAa;AAClB,SAAK,MAAM,KAAK,WAAW,OAAO,CAAC,EAAE;AACrC,SAAK,QAAQ,OAAO,CAAC;AACrB,SAAK,MAAM,OAAO,OAAO,SAAS,CAAC,EAAE;AACrC,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,QAAQ,OAAO;AACzB,QAAI,QAAQ,KAAK,OAAO,QAAQ,KAAK;AACrC,QAAI,MAAM,KAAK,MAAM;AACrB,WAAO,MAAM,MAAM,MAAM;AACrB,UAAI,CAAC;AACD,eAAO;AACX,UAAI,OAAO,KAAK,OAAO,EAAE,KAAK;AAC9B,aAAO,MAAM,OAAO,KAAK;AACzB,cAAQ;AAAA,IACZ;AACA,WAAO,QAAQ,IAAI,MAAM,MAAM,KAAK,OAAO,MAAM,IAAI;AACjD,UAAI,SAAS,KAAK,OAAO,SAAS;AAC9B,eAAO;AACX,UAAI,OAAO,KAAK,OAAO,EAAE,KAAK;AAC9B,aAAO,KAAK,OAAO,MAAM;AACzB,cAAQ;AAAA,IACZ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,KAAK;AACT,QAAI,OAAO,KAAK,MAAM,QAAQ,MAAM,KAAK,MAAM;AAC3C,aAAO;AACX,aAAS,SAAS,KAAK;AACnB,UAAI,MAAM,KAAK;AACX,eAAO,KAAK,IAAI,KAAK,MAAM,IAAI;AACvC,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,KAAK,QAAQ;AACT,QAAI,MAAM,KAAK,WAAW,QAAQ,KAAK;AACvC,QAAI,OAAO,KAAK,MAAM,KAAK,MAAM,QAAQ;AACrC,YAAM,KAAK,MAAM;AACjB,eAAS,KAAK,MAAM,WAAW,GAAG;AAAA,IACtC,OACK;AACD,UAAI,WAAW,KAAK,cAAc,QAAQ,CAAC;AAC3C,UAAI,YAAY;AACZ,eAAO;AACX,YAAM;AACN,UAAI,OAAO,KAAK,aAAa,MAAM,KAAK,YAAY,KAAK,OAAO,QAAQ;AACpE,iBAAS,KAAK,OAAO,WAAW,MAAM,KAAK,SAAS;AAAA,MACxD,OACK;AACD,YAAI,IAAI,KAAK,YAAY,QAAQ,KAAK;AACtC,eAAO,MAAM,MAAM;AACf,kBAAQ,KAAK,OAAO,EAAE,CAAC;AAC3B,aAAK,SAAS,KAAK,MAAM,MAAM,KAAK,YAAY,GAAG;AACnD,YAAI,MAAM,KAAK,OAAO,SAAS,MAAM;AACjC,eAAK,SAAS,KAAK,OAAO,MAAM,GAAG,MAAM,KAAK,GAAG;AACrD,iBAAS,KAAK,OAAO,WAAW,CAAC;AAAA,MACrC;AAAA,IACJ;AACA,QAAI,OAAO,KAAK,MAAM;AAClB,WAAK,MAAM,YAAY,MAAM;AACjC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO,YAAY,GAAG;AAC9B,QAAI,MAAM,YAAY,KAAK,cAAc,WAAW,EAAE,IAAI,KAAK;AAC/D,QAAI,OAAO,QAAQ,MAAM,KAAK,MAAM;AAChC,YAAM,IAAI,WAAW,yBAAyB;AAClD,SAAK,MAAM,QAAQ;AACnB,SAAK,MAAM,MAAM;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,OAAO,QAAQ;AACzB,SAAK,MAAM,QAAQ;AACnB,SAAK,MAAM,MAAM;AAAA,EACrB;AAAA,EACA,WAAW;AACP,QAAI,KAAK,OAAO,KAAK,aAAa,KAAK,MAAM,KAAK,YAAY,KAAK,OAAO,QAAQ;AAC9E,UAAI,EAAE,OAAO,SAAS,IAAI;AAC1B,WAAK,QAAQ,KAAK;AAClB,WAAK,WAAW,KAAK;AACrB,WAAK,SAAS;AACd,WAAK,YAAY;AACjB,WAAK,WAAW,KAAK,MAAM,KAAK;AAAA,IACpC,OACK;AACD,WAAK,SAAS,KAAK;AACnB,WAAK,YAAY,KAAK;AACtB,UAAI,YAAY,KAAK,MAAM,MAAM,KAAK,GAAG;AACzC,UAAI,MAAM,KAAK,MAAM,UAAU;AAC/B,WAAK,QAAQ,MAAM,KAAK,MAAM,KAAK,UAAU,MAAM,GAAG,KAAK,MAAM,KAAK,KAAK,GAAG,IAAI;AAClF,WAAK,WAAW,KAAK;AACrB,WAAK,WAAW;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,WAAW;AACP,QAAI,KAAK,YAAY,KAAK,MAAM,QAAQ;AACpC,WAAK,SAAS;AACd,UAAI,KAAK,YAAY,KAAK,MAAM;AAC5B,eAAO,KAAK,OAAO;AAAA,IAC3B;AACA,WAAO,KAAK,OAAO,KAAK,MAAM,WAAW,KAAK,QAAQ;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,IAAI,GAAG;AACX,SAAK,YAAY;AACjB,WAAO,KAAK,MAAM,KAAK,KAAK,MAAM,IAAI;AAClC,UAAI,KAAK,cAAc,KAAK,OAAO,SAAS;AACxC,eAAO,KAAK,QAAQ;AACxB,WAAK,KAAK,MAAM,KAAK,KAAK;AAC1B,WAAK,QAAQ,KAAK,OAAO,EAAE,KAAK,UAAU;AAC1C,WAAK,MAAM,KAAK,MAAM;AAAA,IAC1B;AACA,SAAK,OAAO;AACZ,QAAI,KAAK,OAAO,KAAK,MAAM;AACvB,WAAK,MAAM,YAAY,KAAK,MAAM;AACtC,WAAO,KAAK,SAAS;AAAA,EACzB;AAAA,EACA,UAAU;AACN,SAAK,MAAM,KAAK,WAAW,KAAK;AAChC,SAAK,QAAQ,KAAK,OAAO,KAAK,aAAa,KAAK,OAAO,SAAS,CAAC;AACjE,SAAK,QAAQ;AACb,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,KAAK,OAAO;AACd,QAAI,OAAO;AACP,WAAK,QAAQ;AACb,YAAM,QAAQ;AACd,YAAM,YAAY,MAAM;AACxB,YAAM,QAAQ,MAAM,WAAW;AAAA,IACnC,OACK;AACD,WAAK,QAAQ;AAAA,IACjB;AACA,QAAI,KAAK,OAAO,KAAK;AACjB,WAAK,MAAM;AACX,UAAI,OAAO,KAAK,KAAK;AACjB,aAAK,QAAQ;AACb,eAAO;AAAA,MACX;AACA,aAAO,MAAM,KAAK,MAAM;AACpB,aAAK,QAAQ,KAAK,OAAO,EAAE,KAAK,UAAU;AAC9C,aAAO,OAAO,KAAK,MAAM;AACrB,aAAK,QAAQ,KAAK,OAAO,EAAE,KAAK,UAAU;AAC9C,UAAI,OAAO,KAAK,YAAY,MAAM,KAAK,WAAW,KAAK,MAAM,QAAQ;AACjE,aAAK,WAAW,MAAM,KAAK;AAAA,MAC/B,OACK;AACD,aAAK,QAAQ;AACb,aAAK,WAAW;AAAA,MACpB;AACA,WAAK,SAAS;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,MAAM,IAAI;AACX,QAAI,QAAQ,KAAK,YAAY,MAAM,KAAK,WAAW,KAAK,MAAM;AAC1D,aAAO,KAAK,MAAM,MAAM,OAAO,KAAK,UAAU,KAAK,KAAK,QAAQ;AACpE,QAAI,QAAQ,KAAK,aAAa,MAAM,KAAK,YAAY,KAAK,OAAO;AAC7D,aAAO,KAAK,OAAO,MAAM,OAAO,KAAK,WAAW,KAAK,KAAK,SAAS;AACvE,QAAI,QAAQ,KAAK,MAAM,QAAQ,MAAM,KAAK,MAAM;AAC5C,aAAO,KAAK,MAAM,KAAK,MAAM,EAAE;AACnC,QAAI,SAAS;AACb,aAAS,KAAK,KAAK,QAAQ;AACvB,UAAI,EAAE,QAAQ;AACV;AACJ,UAAI,EAAE,KAAK;AACP,kBAAU,KAAK,MAAM,KAAK,KAAK,IAAI,EAAE,MAAM,IAAI,GAAG,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC;AAAA,IAC5E;AACA,WAAO;AAAA,EACX;AACJ;AAIA,IAAM,aAAN,MAAiB;AAAA,EACb,YAAY,MAAMC,KAAI;AAClB,SAAK,OAAO;AACZ,SAAK,KAAKA;AAAA,EACd;AAAA,EACA,MAAM,OAAO,OAAO;AAChB,QAAI,EAAE,OAAO,IAAI,MAAM;AACvB,cAAU,KAAK,MAAM,OAAO,OAAO,KAAK,IAAI,OAAO,MAAM,OAAO,cAAc;AAAA,EAClF;AACJ;AACA,WAAW,UAAU,aAAa,WAAW,UAAU,WAAW,WAAW,UAAU,SAAS;AAIhG,IAAM,kBAAN,MAAsB;AAAA,EAClB,YAAY,MAAM,WAAW,WAAW;AACpC,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,OAAO,OAAO,QAAQ,WAAW,YAAY,IAAI,IAAI;AAAA,EAC9D;AAAA,EACA,MAAM,OAAO,OAAO;AAChB,QAAI,QAAQ,MAAM,KAAK,UAAU;AACjC,eAAS;AACL,UAAI,QAAQ,MAAM,OAAO,GAAG,UAAU,MAAM,cAAc,GAAG,CAAC;AAC9D,gBAAU,KAAK,MAAM,OAAO,OAAO,GAAG,KAAK,MAAM,KAAK,SAAS;AAC/D,UAAI,MAAM,MAAM,QAAQ;AACpB;AACJ,UAAI,KAAK,aAAa;AAClB;AACJ,UAAI,CAAC;AACD;AACJ,UAAI,WAAW;AACX;AACJ,YAAM,MAAM,SAAS,MAAM,KAAK;AAAA,IACpC;AACA,QAAI,SAAS;AACT,YAAM,MAAM,OAAO,MAAM,KAAK;AAC9B,YAAM,YAAY,KAAK,WAAW,OAAO;AAAA,IAC7C;AAAA,EACJ;AACJ;AACA,gBAAgB,UAAU,aAAa,WAAW,UAAU,WAAW,WAAW,UAAU,SAAS;AAKrG,IAAM,oBAAN,MAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQpB,YAIA,OAAO,UAAU,CAAC,GAAG;AACjB,SAAK,QAAQ;AACb,SAAK,aAAa,CAAC,CAAC,QAAQ;AAC5B,SAAK,WAAW,CAAC,CAAC,QAAQ;AAC1B,SAAK,SAAS,CAAC,CAAC,QAAQ;AAAA,EAC5B;AACJ;AAqBA,SAAS,UAAU,MAAM,OAAO,OAAO,OAAO,WAAW,YAAY;AACjE,MAAI,QAAQ,GAAG,YAAY,KAAK,OAAO,EAAE,QAAQ,IAAI,MAAM,EAAE;AAC7D,OAAM,YAAS;AACX,SAAK,YAAY,KAAK,KAAK,MAAM;AAC7B;AACJ,QAAI,SAAS,KAAK,QAAQ,CAAC;AAI3B,aAAS,IAAI,QAAQ,GAAG,IAAI,QAAQ,KAAK;AACrC,WAAK,KAAK,IAAI,CAAC,IAAI,aAAa,GAAG;AAC/B,YAAI,OAAO,KAAK,CAAC;AACjB,YAAI,QAAQ,OAAO,IAAI,MAClB,MAAM,MAAM,SAAS,MAAM,MAAM,MAAM,SAAS,QAC7C,UAAU,MAAM,MAAM,MAAM,OAAO,WAAW,UAAU,IAAI;AAChE,gBAAM,YAAY,IAAI;AACtB;AAAA,QACJ;AAAA,MACJ;AACJ,QAAI,OAAO,MAAM,MAAM,MAAM,GAAG,OAAO,KAAK,QAAQ,CAAC;AAErD,QAAI,MAAM,OAAO,KAAK,OAAO,OAAO,KAAK,SAAS,OAAO,IAAI,CAAC,KAAK,OAAqB;AACpF,cAAQ,KAAK,SAAS,OAAO,IAAI,CAAC;AAClC,eAAS;AAAA,IACb;AAEA,WAAO,MAAM,QAAO;AAChB,UAAI,MAAO,MAAM,QAAS;AAC1B,UAAI,QAAQ,SAAS,OAAO,OAAO;AACnC,UAAI,OAAO,KAAK,KAAK,GAAG,KAAK,KAAK,QAAQ,CAAC,KAAK;AAChD,UAAI,OAAO;AACP,eAAO;AAAA,eACF,QAAQ;AACb,cAAM,MAAM;AAAA,WACX;AACD,gBAAQ,KAAK,QAAQ,CAAC;AACtB,cAAM,QAAQ;AACd,iBAAS;AAAA,MACb;AAAA,IACJ;AACA;AAAA,EACJ;AACJ;AACA,SAAS,WAAW,MAAM,OAAO,MAAM;AACnC,WAAS,IAAI,OAAO,OAAO,OAAO,KAAK,CAAC,MAAM,OAAqB;AAC/D,QAAI,QAAQ;AACR,aAAO,IAAI;AACnB,SAAO;AACX;AACA,SAAS,UAAU,OAAO,MAAM,WAAW,aAAa;AACpD,MAAI,QAAQ,WAAW,WAAW,aAAa,IAAI;AACnD,SAAO,QAAQ,KAAK,WAAW,WAAW,aAAa,KAAK,IAAI;AACpE;AAGA,IAAM,UAAU,OAAO,WAAW,eAAe,QAAQ,OAAO,YAAY,KAAK,QAAQ,IAAI,GAAG;AAChG,IAAI,WAAW;AACf,SAAS,MAAM,MAAM,KAAK,MAAM;AAC5B,MAAI,SAAS,KAAK,OAAO,SAAS,gBAAgB;AAClD,SAAO,OAAO,GAAG;AACjB,aAAS;AACL,QAAI,EAAE,OAAO,IAAI,OAAO,YAAY,GAAG,IAAI,OAAO,WAAW,GAAG;AAC5D,iBAAS;AACL,aAAK,OAAO,IAAI,OAAO,KAAK,MAAM,OAAO,OAAO,QAAQ,CAAC,OAAO,KAAK;AACjE,iBAAO,OAAO,IAAI,KAAK,IAAI,GAAG,KAAK;AAAA,YAAI,OAAO,KAAK;AAAA,YAAG,MAAM;AAAA;AAAA,UAAyB,CAAC,IAChF,KAAK,IAAI,KAAK,QAAQ,KAAK;AAAA,YAAI,OAAO,OAAO;AAAA,YAAG,MAAM;AAAA;AAAA,UAAyB,CAAC;AAC1F,YAAI,OAAO,IAAI,OAAO,YAAY,IAAI,OAAO,YAAY;AACrD;AACJ,YAAI,CAAC,OAAO,OAAO;AACf,iBAAO,OAAO,IAAI,IAAI,KAAK;AAAA,MACnC;AAAA,EACR;AACJ;AACA,IAAM,iBAAN,MAAqB;AAAA,EACjB,YAAY,WAAW,SAAS;AAC5B,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,IAAI;AACT,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,QAAQ,CAAC;AACd,SAAK,QAAQ,CAAC;AACd,SAAK,QAAQ,CAAC;AACd,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,eAAe;AACX,QAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,UAAU,SAAS,OAAO,KAAK,UAAU,KAAK,GAAG;AACzF,QAAI,IAAI;AACJ,WAAK,WAAW,GAAG,YAAY,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,QAAQ,CAAC,IAAI,GAAG,SAAS,GAAG;AACvF,WAAK,SAAS,GAAG,UAAU,MAAM,GAAG,MAAM,GAAG,KAAK,GAAG,QAAQ,EAAE,IAAI,GAAG,SAAS,GAAG;AAClF,aAAO,KAAK,MAAM,QAAQ;AACtB,aAAK,MAAM,IAAI;AACf,aAAK,MAAM,IAAI;AACf,aAAK,MAAM,IAAI;AAAA,MACnB;AACA,WAAK,MAAM,KAAK,GAAG,IAAI;AACvB,WAAK,MAAM,KAAK,CAAC,GAAG,MAAM;AAC1B,WAAK,MAAM,KAAK,CAAC;AACjB,WAAK,YAAY,KAAK;AAAA,IAC1B,OACK;AACD,WAAK,YAAY;AAAA,IACrB;AAAA,EACJ;AAAA;AAAA,EAEA,OAAO,KAAK;AACR,QAAI,MAAM,KAAK;AACX,aAAO;AACX,WAAO,KAAK,YAAY,KAAK,UAAU;AACnC,WAAK,aAAa;AACtB,QAAI,CAAC,KAAK;AACN,aAAO;AACX,eAAS;AACL,UAAI,OAAO,KAAK,MAAM,SAAS;AAC/B,UAAI,OAAO,GAAG;AACV,aAAK,aAAa;AAClB,eAAO;AAAA,MACX;AACA,UAAI,MAAM,KAAK,MAAM,IAAI,GAAG,QAAQ,KAAK,MAAM,IAAI;AACnD,UAAI,SAAS,IAAI,SAAS,QAAQ;AAC9B,aAAK,MAAM,IAAI;AACf,aAAK,MAAM,IAAI;AACf,aAAK,MAAM,IAAI;AACf;AAAA,MACJ;AACA,UAAI,OAAO,IAAI,SAAS,KAAK;AAC7B,UAAI,QAAQ,KAAK,MAAM,IAAI,IAAI,IAAI,UAAU,KAAK;AAClD,UAAI,QAAQ,KAAK;AACb,aAAK,YAAY;AACjB,eAAO;AAAA,MACX;AACA,UAAI,gBAAgB,MAAM;AACtB,YAAI,SAAS,KAAK;AACd,cAAI,QAAQ,KAAK;AACb,mBAAO;AACX,cAAI,MAAM,QAAQ,KAAK;AACvB,cAAI,OAAO,KAAK,QAAQ;AACpB,gBAAI,YAAY,KAAK,KAAK,SAAS,SAAS;AAC5C,gBAAI,CAAC,aAAa,MAAM,YAAY,KAAK,SAAS;AAC9C,qBAAO;AAAA,UACf;AAAA,QACJ;AACA,aAAK,MAAM,IAAI;AACf,YAAI,QAAQ,KAAK,UAAU,KAAK,IAAI,KAAK,UAAU,GAAG,GAAG;AACrD,eAAK,MAAM,KAAK,IAAI;AACpB,eAAK,MAAM,KAAK,KAAK;AACrB,eAAK,MAAM,KAAK,CAAC;AAAA,QACrB;AAAA,MACJ,OACK;AACD,aAAK,MAAM,IAAI;AACf,aAAK,YAAY,QAAQ,KAAK;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,IAAM,aAAN,MAAiB;AAAA,EACb,YAAY,QAAQ,QAAQ;AACxB,SAAK,SAAS;AACd,SAAK,SAAS,CAAC;AACf,SAAK,YAAY;AACjB,SAAK,UAAU,CAAC;AAChB,SAAK,SAAS,OAAO,WAAW,IAAI,OAAK,IAAI,aAAW;AAAA,EAC5D;AAAA,EACA,WAAW,OAAO;AACd,QAAI,cAAc;AAClB,QAAI,OAAO;AACX,QAAI,EAAE,OAAO,IAAI,MAAM,GAAG,EAAE,WAAW,IAAI;AAC3C,QAAI,OAAO,OAAO;AAAA,MAAU,MAAM;AAAA,MAAO;AAAA;AAAA,IAAgC;AACzE,QAAI,UAAU,MAAM,aAAa,MAAM,WAAW,OAAO;AACzD,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,WAAM,KAAK,IAAK,SAAS;AACrB;AACJ,UAAI,YAAY,WAAW,CAAC,GAAG,QAAQ,KAAK,OAAO,CAAC;AACpD,UAAI,QAAQ,CAAC,UAAU;AACnB;AACJ,UAAI,UAAU,cAAc,MAAM,SAAS,MAAM,OAAO,MAAM,QAAQ,QAAQ,MAAM,WAAW,SAAS;AACpG,aAAK,kBAAkB,OAAO,WAAW,KAAK;AAC9C,cAAM,OAAO;AACb,cAAM,UAAU;AAAA,MACpB;AACA,UAAI,MAAM,YAAY,MAAM,MAAM;AAC9B,oBAAY,KAAK,IAAI,MAAM,WAAW,SAAS;AACnD,UAAI,MAAM,SAAS,GAAkB;AACjC,YAAI,aAAa;AACjB,YAAI,MAAM,WAAW;AACjB,wBAAc,KAAK,WAAW,OAAO,MAAM,UAAU,MAAM,KAAK,WAAW;AAC/E,sBAAc,KAAK,WAAW,OAAO,MAAM,OAAO,MAAM,KAAK,WAAW;AACxE,YAAI,CAAC,UAAU,QAAQ;AACnB,iBAAO;AACP,cAAI,cAAc;AACd;AAAA,QACR;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,KAAK,QAAQ,SAAS;AACzB,WAAK,QAAQ,IAAI;AACrB,QAAI;AACA,YAAM,aAAa,SAAS;AAChC,QAAI,CAAC,QAAQ,MAAM,OAAO,KAAK,OAAO,KAAK;AACvC,aAAO,IAAI;AACX,WAAK,QAAQ,MAAM,EAAE,OAAO;AAC5B,WAAK,QAAQ,KAAK,MAAM,MAAM;AAC9B,oBAAc,KAAK,WAAW,OAAO,KAAK,OAAO,KAAK,KAAK,WAAW;AAAA,IAC1E;AACA,SAAK,YAAY;AACjB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,aAAa,OAAO;AAChB,QAAI,KAAK;AACL,aAAO,KAAK;AAChB,QAAI,OAAO,IAAI,eAAa,EAAE,KAAK,EAAE,IAAI;AACzC,SAAK,QAAQ;AACb,SAAK,MAAM,KAAK,IAAI,MAAM,GAAG,EAAE,OAAO,GAAG;AACzC,SAAK,QAAQ,OAAO,EAAE,OAAO,MAAM,EAAE,OAAO,UAAU;AACtD,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB,OAAO,WAAW,OAAO;AACvC,QAAI,QAAQ,KAAK,OAAO,QAAQ,MAAM,GAAG;AACzC,cAAU,MAAM,KAAK,OAAO,MAAM,OAAO,KAAK,GAAG,KAAK;AACtD,QAAI,MAAM,QAAQ,IAAI;AAClB,UAAI,EAAE,OAAO,IAAI,MAAM;AACvB,eAAS,IAAI,GAAG,IAAI,OAAO,YAAY,QAAQ;AAC3C,YAAI,OAAO,YAAY,CAAC,KAAK,MAAM,OAAO;AACtC,cAAI,SAAS,OAAO,aAAa,CAAC,EAAE,KAAK,OAAO,KAAK,MAAM,OAAO,MAAM,GAAG,GAAG,KAAK;AACnF,cAAI,UAAU,KAAK,MAAM,EAAE,OAAO,QAAQ,OAAO,UAAU,CAAC,GAAG;AAC3D,iBAAK,SAAS,MAAM;AAChB,oBAAM,QAAQ,UAAU;AAAA;AAExB,oBAAM,WAAW,UAAU;AAC/B;AAAA,UACJ;AAAA,QACJ;AAAA,IACR,OACK;AACD,YAAM,QAAQ;AACd,YAAM,MAAM,KAAK,OAAO,QAAQ,QAAQ,CAAC;AAAA,IAC7C;AAAA,EACJ;AAAA,EACA,UAAU,QAAQ,OAAO,KAAK,OAAO;AAEjC,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC5B,UAAI,KAAK,QAAQ,CAAC,KAAK;AACnB,eAAO;AACf,SAAK,QAAQ,OAAO,IAAI;AACxB,SAAK,QAAQ,OAAO,IAAI;AACxB,SAAK,QAAQ,OAAO,IAAI;AACxB,WAAO;AAAA,EACX;AAAA,EACA,WAAW,OAAO,OAAO,KAAK,OAAO;AACjC,QAAI,EAAE,MAAM,IAAI,OAAO,EAAE,OAAO,IAAI,MAAM,GAAG,EAAE,KAAK,IAAI;AACxD,aAAS,MAAM,GAAG,MAAM,GAAG,OAAO;AAC9B,eAAS,IAAI,OAAO;AAAA,QAAU;AAAA,QAAO,MAAM,IAA0B;AAAA;AAAA,MAA0B,KAAI,KAAK,GAAG;AACvG,YAAI,KAAK,CAAC,KAAK,OAAqB;AAChC,cAAI,KAAK,IAAI,CAAC,KAAK,GAAkB;AACjC,gBAAI,KAAK,MAAM,IAAI,CAAC;AAAA,UACxB,OACK;AACD,gBAAI,SAAS,KAAK,KAAK,IAAI,CAAC,KAAK;AAC7B,sBAAQ,KAAK,UAAU,KAAK,MAAM,IAAI,CAAC,GAAG,OAAO,KAAK,KAAK;AAC/D;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,KAAK,CAAC,KAAK;AACX,kBAAQ,KAAK,UAAU,KAAK,MAAM,IAAI,CAAC,GAAG,OAAO,KAAK,KAAK;AAAA,MACnE;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AACA,IAAM,QAAN,MAAY;AAAA,EACR,YAAY,QAAQ,OAAO,WAAW,QAAQ;AAC1C,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,SAAS,CAAC;AACf,SAAK,YAAY;AACjB,SAAK,wBAAwB;AAC7B,SAAK,uBAAuB;AAC5B,SAAK,oBAAoB;AACzB,SAAK,SAAS,IAAI,YAAY,OAAO,MAAM;AAC3C,SAAK,SAAS,IAAI,WAAW,QAAQ,KAAK,MAAM;AAChD,SAAK,UAAU,OAAO,IAAI,CAAC;AAC3B,QAAI,EAAE,KAAK,IAAI,OAAO,CAAC;AACvB,SAAK,SAAS,CAAC,MAAM,MAAM,MAAM,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC;AACrD,SAAK,YAAY,UAAU,UAAU,KAAK,OAAO,MAAM,OAAO,OAAO,eAAe,IAC9E,IAAI,eAAe,WAAW,OAAO,OAAO,IAAI;AAAA,EAC1D;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACN,QAAI,SAAS,KAAK,QAAQ,MAAM,KAAK;AAErC,QAAI,YAAY,KAAK,SAAS,CAAC;AAC/B,QAAI,SAAS;AAQb,QAAI,KAAK,oBAAoB,OAAkD,OAAO,UAAU,GAAG;AAC/F,UAAI,CAAC,CAAC,IAAI;AACV,aAAO,EAAE,YAAY,KAAK,EAAE,MAAM,UAAU,EAAE,MAAM,EAAE,MAAM,SAAS,CAAC,KAAK,KAAK,uBAAuB;AAAA,MAAE;AACzG,WAAK,oBAAoB,KAAK,uBAAuB;AAAA,IACzD;AAIA,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,QAAQ,OAAO,CAAC;AACpB,iBAAS;AACL,aAAK,OAAO,YAAY;AACxB,YAAI,MAAM,MAAM,KAAK;AACjB,oBAAU,KAAK,KAAK;AAAA,QACxB,WACS,KAAK,aAAa,OAAO,WAAW,MAAM,GAAG;AAClD;AAAA,QACJ,OACK;AACD,cAAI,CAAC,SAAS;AACV,sBAAU,CAAC;AACX,4BAAgB,CAAC;AAAA,UACrB;AACA,kBAAQ,KAAK,KAAK;AAClB,cAAI,MAAM,KAAK,OAAO,aAAa,KAAK;AACxC,wBAAc,KAAK,IAAI,OAAO,IAAI,GAAG;AAAA,QACzC;AACA;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,UAAU,QAAQ;AACnB,UAAI,WAAW,WAAW,aAAa,OAAO;AAC9C,UAAI,UAAU;AACV,YAAI;AACA,kBAAQ,IAAI,iBAAiB,KAAK,QAAQ,QAAQ,CAAC;AACvD,eAAO,KAAK,YAAY,QAAQ;AAAA,MACpC;AACA,UAAI,KAAK,OAAO,QAAQ;AACpB,YAAI,WAAW;AACX,kBAAQ,IAAI,uBAAuB,KAAK,OAAO,YAAY,KAAK,OAAO,QAAQ,KAAK,OAAO,UAAU,KAAK,IAAI,OAAO;AACzH,cAAM,IAAI,YAAY,iBAAiB,GAAG;AAAA,MAC9C;AACA,UAAI,CAAC,KAAK;AACN,aAAK,aAAa;AAAA,IAC1B;AACA,QAAI,KAAK,cAAc,SAAS;AAC5B,UAAI,WAAW,KAAK,aAAa,QAAQ,QAAQ,CAAC,EAAE,MAAM,KAAK,YAAY,QAAQ,CAAC,IAC9E,KAAK,YAAY,SAAS,eAAe,SAAS;AACxD,UAAI,UAAU;AACV,YAAI;AACA,kBAAQ,IAAI,kBAAkB,KAAK,QAAQ,QAAQ,CAAC;AACxD,eAAO,KAAK,YAAY,SAAS,SAAS,CAAC;AAAA,MAC/C;AAAA,IACJ;AACA,QAAI,KAAK,YAAY;AACjB,UAAI,eAAe,KAAK,cAAc,IAAI,IAAI,KAAK,aAAa;AAChE,UAAI,UAAU,SAAS,cAAc;AACjC,kBAAU,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;AAC1C,eAAO,UAAU,SAAS;AACtB,oBAAU,IAAI;AAAA,MACtB;AACA,UAAI,UAAU,KAAK,OAAK,EAAE,YAAY,GAAG;AACrC,aAAK;AAAA,IACb,WACS,UAAU,SAAS,GAAG;AAI3B,YAAO,UAAS,IAAI,GAAG,IAAI,UAAU,SAAS,GAAG,KAAK;AAClD,YAAI,QAAQ,UAAU,CAAC;AACvB,iBAAS,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC3C,cAAI,QAAQ,UAAU,CAAC;AACvB,cAAI,MAAM,UAAU,KAAK,KACrB,MAAM,OAAO,SAAS,OAAsC,MAAM,OAAO,SAAS,KAAoC;AACtH,iBAAM,MAAM,QAAQ,MAAM,SAAW,MAAM,OAAO,SAAS,MAAM,OAAO,UAAW,GAAG;AAClF,wBAAU,OAAO,KAAK,CAAC;AAAA,YAC3B,OACK;AACD,wBAAU,OAAO,KAAK,CAAC;AACvB,uBAAS;AAAA,YACb;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,UAAU,SAAS;AACnB,kBAAU;AAAA,UAAO;AAAA,UAA4B,UAAU,SAAS;AAAA;AAAA,QAA0B;AAAA,IAClG;AACA,SAAK,cAAc,UAAU,CAAC,EAAE;AAChC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ;AAClC,UAAI,UAAU,CAAC,EAAE,MAAM,KAAK;AACxB,aAAK,cAAc,UAAU,CAAC,EAAE;AACxC,WAAO;AAAA,EACX;AAAA,EACA,OAAO,KAAK;AACR,QAAI,KAAK,aAAa,QAAQ,KAAK,YAAY;AAC3C,YAAM,IAAI,WAAW,8BAA8B;AACvD,SAAK,YAAY;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAO,QAAQ,OAAO;AAC/B,QAAI,QAAQ,MAAM,KAAK,EAAE,OAAO,IAAI;AACpC,QAAI,OAAO,UAAU,KAAK,QAAQ,KAAK,IAAI,SAAS;AACpD,QAAI,KAAK,aAAa,QAAQ,QAAQ,KAAK;AACvC,aAAO,MAAM,YAAY,IAAI,QAAQ;AACzC,QAAI,KAAK,WAAW;AAChB,UAAI,WAAW,MAAM,cAAc,MAAM,WAAW,QAAQ,QAAQ,SAAS,WAAW,MAAM,WAAW,OAAO;AAChH,eAAS,SAAS,KAAK,UAAU,OAAO,KAAK,GAAG,UAAS;AACrD,YAAI,QAAQ,KAAK,OAAO,QAAQ,MAAM,OAAO,KAAK,EAAE,KAAK,OAAO,OAAO,OAAO,QAAQ,MAAM,OAAO,OAAO,KAAK,EAAE,IAAI;AACrH,YAAI,QAAQ,MAAM,OAAO,WAAW,CAAC,aAAa,OAAO,KAAK,SAAS,WAAW,KAAK,MAAM,SAAS;AAClG,gBAAM,QAAQ,QAAQ,KAAK;AAC3B,cAAI;AACA,oBAAQ,IAAI,OAAO,KAAK,QAAQ,KAAK,IAAI,kBAAkB,OAAO,QAAQ,OAAO,KAAK,EAAE,CAAC,GAAG;AAChG,iBAAO;AAAA,QACX;AACA,YAAI,EAAE,kBAAkB,SAAS,OAAO,SAAS,UAAU,KAAK,OAAO,UAAU,CAAC,IAAI;AAClF;AACJ,YAAI,QAAQ,OAAO,SAAS,CAAC;AAC7B,YAAI,iBAAiB,QAAQ,OAAO,UAAU,CAAC,KAAK;AAChD,mBAAS;AAAA;AAET;AAAA,MACR;AAAA,IACJ;AACA,QAAI,gBAAgB,OAAO;AAAA,MAAU,MAAM;AAAA,MAAO;AAAA;AAAA,IAAgC;AAClF,QAAI,gBAAgB,GAAG;AACnB,YAAM,OAAO,aAAa;AAC1B,UAAI;AACA,gBAAQ,IAAI,OAAO,KAAK,QAAQ,KAAK,IAAI,uBAAuB,OAAO;AAAA,UAAQ,gBAAgB;AAAA;AAAA,QAA4B,CAAC,GAAG;AACnI,aAAO;AAAA,IACX;AACA,QAAI,MAAM,MAAM,UAAU,MAAyB;AAC/C,aAAO,MAAM,MAAM,SAAS,OAAwB,MAAM,YAAY,GAAG;AAAA,MAAE;AAAA,IAC/E;AACA,QAAI,UAAU,KAAK,OAAO,WAAW,KAAK;AAC1C,aAAS,IAAI,GAAG,IAAI,QAAQ,UAAS;AACjC,UAAI,SAAS,QAAQ,GAAG,GAAG,OAAO,QAAQ,GAAG,GAAG,MAAM,QAAQ,GAAG;AACjE,UAAI,OAAO,KAAK,QAAQ,UAAU,CAAC;AACnC,UAAI,aAAa,OAAO,QAAQ,MAAM,MAAM;AAC5C,UAAI,OAAO,KAAK,OAAO;AACvB,iBAAW,MAAM,QAAQ,MAAM,OAAO,KAAK,QAAQ,WAAW,KAAK,GAAG;AACtE,UAAI;AACA,gBAAQ,IAAI,OAAO,KAAK,QAAQ,UAAU,IAAI,UAAU,SAAS,UAAkC,IAAI,UACjG,aAAa,OAAO;AAAA,UAAQ,SAAS;AAAA;AAAA,QAA4B,CAAC,EAAE,QAAQ,OAAO,QAAQ,IAAI,CAAC,MAAM,KAAK,GAAG,cAAc,QAAQ,KAAK,SAAS,GAAG;AAC/J,UAAI;AACA,eAAO;AAAA,eACF,WAAW,MAAM;AACtB,eAAO,KAAK,UAAU;AAAA;AAEtB,cAAM,KAAK,UAAU;AAAA,IAC7B;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,OAAO,WAAW;AAC3B,QAAI,MAAM,MAAM;AAChB,eAAS;AACL,UAAI,CAAC,KAAK,aAAa,OAAO,MAAM,IAAI;AACpC,eAAO;AACX,UAAI,MAAM,MAAM,KAAK;AACjB,uBAAe,OAAO,SAAS;AAC/B,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,YAAY,QAAQ,QAAQ,WAAW;AACnC,QAAI,WAAW,MAAM,YAAY;AACjC,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,QAAQ,OAAO,CAAC,GAAG,QAAQ,OAAO,KAAK,CAAC,GAAG,WAAW,QAAQ,KAAK,KAAK,CAAC;AAC7E,UAAI,OAAO,UAAU,KAAK,QAAQ,KAAK,IAAI,SAAS;AACpD,UAAI,MAAM,SAAS;AACf,YAAI;AACA;AACJ,oBAAY;AACZ,cAAM,QAAQ;AACd,YAAI;AACA,kBAAQ,IAAI,OAAO,KAAK,QAAQ,KAAK,IAAI,cAAc;AAC3D,YAAI,OAAO,KAAK,aAAa,OAAO,SAAS;AAC7C,YAAI;AACA;AAAA,MACR;AACA,UAAI,QAAQ,MAAM,MAAM,GAAG,YAAY;AACvC,eAAS,IAAI,GAAG,MAAM,YAAY,KAAK,IAAI,IAA+B,KAAK;AAC3E,YAAI;AACA,kBAAQ,IAAI,YAAY,KAAK,QAAQ,KAAK,IAAI,qBAAqB;AACvE,YAAI,OAAO,KAAK,aAAa,OAAO,SAAS;AAC7C,YAAI;AACA;AACJ,YAAI;AACA,sBAAY,KAAK,QAAQ,KAAK,IAAI;AAAA,MAC1C;AACA,eAAS,UAAU,MAAM,gBAAgB,KAAK,GAAG;AAC7C,YAAI;AACA,kBAAQ,IAAI,OAAO,KAAK,QAAQ,MAAM,IAAI,uBAAuB;AACrE,aAAK,aAAa,QAAQ,SAAS;AAAA,MACvC;AACA,UAAI,KAAK,OAAO,MAAM,MAAM,KAAK;AAC7B,YAAI,YAAY,MAAM,KAAK;AACvB;AACA,kBAAQ;AAAA,QACZ;AACA,cAAM,gBAAgB,OAAO,QAAQ;AACrC,YAAI;AACA,kBAAQ,IAAI,OAAO,KAAK,QAAQ,KAAK,IAAI,wBAAwB,KAAK,OAAO,QAAQ,KAAK,CAAC,GAAG;AAClG,uBAAe,OAAO,SAAS;AAAA,MACnC,WACS,CAAC,YAAY,SAAS,QAAQ,MAAM,OAAO;AAChD,mBAAW;AAAA,MACf;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA,EAEA,YAAY,OAAO;AACf,UAAM,MAAM;AACZ,WAAO,KAAK,MAAM;AAAA,MAAE,QAAQ,kBAAkB,OAAO,KAAK;AAAA,MACtD,SAAS,KAAK,OAAO;AAAA,MACrB,OAAO,KAAK;AAAA,MACZ,iBAAiB,KAAK,OAAO;AAAA,MAC7B,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK,OAAO,CAAC,EAAE;AAAA,MACtB,QAAQ,MAAM,MAAM,KAAK,OAAO,CAAC,EAAE;AAAA,MACnC,eAAe,KAAK,OAAO;AAAA,IAAc,CAAC;AAAA,EAClD;AAAA,EACA,QAAQ,OAAO;AACX,QAAIA,OAAM,aAAa,WAAW,oBAAI,YAAU,IAAI,KAAK;AACzD,QAAI,CAACA;AACD,eAAS,IAAI,OAAOA,MAAK,OAAO,cAAc,KAAK,aAAa,CAAC;AACrE,WAAOA,MAAK;AAAA,EAChB;AACJ;AACA,SAAS,eAAe,OAAO,WAAW;AACtC,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,QAAI,QAAQ,UAAU,CAAC;AACvB,QAAI,MAAM,OAAO,MAAM,OAAO,MAAM,UAAU,KAAK,GAAG;AAClD,UAAI,UAAU,CAAC,EAAE,QAAQ,MAAM;AAC3B,kBAAU,CAAC,IAAI;AACnB;AAAA,IACJ;AAAA,EACJ;AACA,YAAU,KAAK,KAAK;AACxB;AACA,IAAM,UAAN,MAAc;AAAA,EACV,YAAY,QAAQ,OAAO,UAAU;AACjC,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,OAAO,MAAM;AAAE,WAAO,CAAC,KAAK,YAAY,KAAK,SAAS,IAAI,KAAK;AAAA,EAAG;AACtE;AACA,IAAM,KAAK,OAAK;AAahB,IAAM,iBAAN,MAAqB;AAAA;AAAA;AAAA;AAAA,EAIjB,YAAY,MAAM;AACd,SAAK,QAAQ,KAAK;AAClB,SAAK,QAAQ,KAAK,SAAS;AAC3B,SAAK,SAAS,KAAK,UAAU;AAC7B,SAAK,QAAQ,KAAK,SAAS;AAC3B,SAAK,OAAO,KAAK,SAAS,MAAM;AAChC,SAAK,SAAS,KAAK,WAAW;AAAA,EAClC;AACJ;AAMA,IAAM,WAAN,MAAM,kBAAiB,OAAO;AAAA;AAAA;AAAA;AAAA,EAI1B,YAAY,MAAM;AACd,UAAM;AAIN,SAAK,WAAW,CAAC;AACjB,QAAI,KAAK,WAAW;AAChB,YAAM,IAAI,WAAW,mBAAmB,KAAK,OAAO,oCAAoC,EAAqB,GAAG;AACpH,QAAI,YAAY,KAAK,UAAU,MAAM,GAAG;AACxC,SAAK,gBAAgB,UAAU;AAC/B,aAAS,IAAI,GAAG,IAAI,KAAK,iBAAiB;AACtC,gBAAU,KAAK,EAAE;AACrB,QAAI,WAAW,OAAO,KAAK,KAAK,QAAQ,EAAE,IAAI,OAAK,KAAK,SAAS,CAAC,EAAE,CAAC,CAAC;AACtE,QAAI,YAAY,CAAC;AACjB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ;AAClC,gBAAU,KAAK,CAAC,CAAC;AACrB,aAAS,QAAQ,QAAQ,MAAM,OAAO;AAClC,gBAAU,MAAM,EAAE,KAAK,CAAC,MAAM,KAAK,YAAY,OAAO,KAAK,CAAC,CAAC,CAAC;AAAA,IAClE;AACA,QAAI,KAAK;AACL,eAAS,YAAY,KAAK,WAAW;AACjC,YAAI,OAAO,SAAS,CAAC;AACrB,YAAI,OAAO,QAAQ;AACf,iBAAO,SAAS,IAAI;AACxB,iBAAS,IAAI,GAAG,IAAI,SAAS,UAAS;AAClC,cAAI,OAAO,SAAS,GAAG;AACvB,cAAI,QAAQ,GAAG;AACX,oBAAQ,MAAM,MAAM,SAAS,GAAG,CAAC;AAAA,UACrC,OACK;AACD,gBAAI,QAAQ,SAAS,IAAI,CAAC,IAAI;AAC9B,qBAAS,IAAI,CAAC,MAAM,IAAI,GAAG;AACvB,sBAAQ,SAAS,GAAG,GAAG,MAAM,KAAK;AACtC;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACJ,SAAK,UAAU,IAAI,QAAQ,UAAU,IAAI,CAAC,MAAM,MAAM,SAAS,OAAO;AAAA,MAClE,MAAM,KAAK,KAAK,gBAAgB,SAAY;AAAA,MAC5C,IAAI;AAAA,MACJ,OAAO,UAAU,CAAC;AAAA,MAClB,KAAK,SAAS,QAAQ,CAAC,IAAI;AAAA,MAC3B,OAAO,KAAK;AAAA,MACZ,SAAS,KAAK,gBAAgB,KAAK,aAAa,QAAQ,CAAC,IAAI;AAAA,IACjE,CAAC,CAAC,CAAC;AACH,QAAI,KAAK;AACL,WAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,WAAW;AAC1D,SAAK,SAAS;AACd,SAAK,eAAe;AACpB,QAAI,aAAa,YAAY,KAAK,SAAS;AAC3C,SAAK,UAAU,KAAK;AACpB,SAAK,mBAAmB,KAAK,eAAe,CAAC;AAC7C,SAAK,cAAc,IAAI,YAAY,KAAK,iBAAiB,MAAM;AAC/D,aAAS,IAAI,GAAG,IAAI,KAAK,iBAAiB,QAAQ;AAC9C,WAAK,YAAY,CAAC,IAAI,KAAK,iBAAiB,CAAC,EAAE;AACnD,SAAK,eAAe,KAAK,iBAAiB,IAAI,cAAc;AAC5D,SAAK,SAAS,YAAY,KAAK,QAAQ,WAAW;AAClD,SAAK,OAAO,YAAY,KAAK,SAAS;AACtC,SAAK,OAAO,YAAY,KAAK,IAAI;AACjC,SAAK,UAAU,KAAK;AACpB,SAAK,aAAa,KAAK,WAAW,IAAI,WAAS,OAAO,SAAS,WAAW,IAAI,WAAW,YAAY,KAAK,IAAI,KAAK;AACnH,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,KAAK,YAAY,CAAC;AAClC,SAAK,qBAAqB,KAAK,sBAAsB;AACrD,SAAK,iBAAiB,KAAK;AAC3B,SAAK,YAAY,KAAK,aAAa;AACnC,SAAK,UAAU,KAAK,QAAQ,MAAM,SAAS;AAC3C,SAAK,UAAU,KAAK,aAAa;AACjC,SAAK,MAAM,KAAK,SAAS,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC,CAAC;AAAA,EAC1D;AAAA,EACA,YAAY,OAAO,WAAW,QAAQ;AAClC,QAAI,QAAQ,IAAI,MAAM,MAAM,OAAO,WAAW,MAAM;AACpD,aAAS,KAAK,KAAK;AACf,cAAQ,EAAE,OAAO,OAAO,WAAW,MAAM;AAC7C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,OAAO,MAAM,QAAQ,OAAO;AAChC,QAAI,QAAQ,KAAK;AACjB,QAAI,QAAQ,MAAM,CAAC;AACf,aAAO;AACX,aAAS,MAAM,MAAM,OAAO,CAAC,OAAK;AAC9B,UAAI,WAAW,MAAM,KAAK,GAAG,OAAO,WAAW;AAC/C,UAAI,SAAS,MAAM,KAAK;AACxB,UAAI,QAAQ;AACR,eAAO;AACX,eAAS,MAAM,OAAO,YAAY,IAAI,MAAM,KAAK;AAC7C,YAAI,MAAM,GAAG,KAAK;AACd,iBAAO;AACf,UAAI;AACA,eAAO;AAAA,IACf;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,OAAO,UAAU;AACvB,QAAI,OAAO,KAAK;AAChB,aAAS,MAAM,GAAG,MAAM,GAAG,OAAO;AAC9B,eAAS,IAAI,KAAK;AAAA,QAAU;AAAA,QAAO,MAAM,IAA0B;AAAA;AAAA,MAA0B,GAAG,QAAO,KAAK,GAAG;AAC3G,aAAK,OAAO,KAAK,CAAC,MAAM,OAAqB;AACzC,cAAI,KAAK,IAAI,CAAC,KAAK;AACf,mBAAO,KAAK,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC;AAAA,mBAC5B,KAAK,IAAI,CAAC,KAAK;AACpB,mBAAO,KAAK,MAAM,IAAI,CAAC;AAAA;AAEvB;AAAA,QACR;AACA,YAAI,QAAQ,YAAY,QAAQ;AAC5B,iBAAO,KAAK,MAAM,IAAI,CAAC;AAAA,MAC/B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,OAAO,MAAM;AACnB,WAAO,KAAK,OAAQ,QAAQ,IAA2B,IAAI;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,OAAO,MAAM;AACnB,YAAQ,KAAK;AAAA,MAAU;AAAA,MAAO;AAAA;AAAA,IAAwB,IAAI,QAAQ;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,OAAO,QAAQ;AACvB,WAAO,CAAC,CAAC,KAAK,WAAW,OAAO,OAAK,KAAK,SAAS,OAAO,IAAI;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO,QAAQ;AACtB,QAAI,QAAQ,KAAK;AAAA,MAAU;AAAA,MAAO;AAAA;AAAA,IAAgC;AAClE,QAAI,SAAS,QAAQ,OAAO,KAAK,IAAI;AACrC,aAAS,IAAI,KAAK;AAAA,MAAU;AAAA,MAAO;AAAA;AAAA,IAA0B,GAAG,UAAU,MAAM,KAAK,GAAG;AACpF,UAAI,KAAK,KAAK,CAAC,KAAK,OAAqB;AACrC,YAAI,KAAK,KAAK,IAAI,CAAC,KAAK;AACpB,cAAI,KAAK,KAAK,MAAM,IAAI,CAAC;AAAA;AAEzB;AAAA,MACR;AACA,eAAS,OAAO,KAAK,KAAK,MAAM,IAAI,CAAC,CAAC;AAAA,IAC1C;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AACd,QAAI,SAAS,CAAC;AACd,aAAS,IAAI,KAAK;AAAA,MAAU;AAAA,MAAO;AAAA;AAAA,IAA0B,KAAI,KAAK,GAAG;AACrE,UAAI,KAAK,KAAK,CAAC,KAAK,OAAqB;AACrC,YAAI,KAAK,KAAK,IAAI,CAAC,KAAK;AACpB,cAAI,KAAK,KAAK,MAAM,IAAI,CAAC;AAAA;AAEzB;AAAA,MACR;AACA,WAAK,KAAK,KAAK,IAAI,CAAC,IAAK,SAAiC,OAAQ,GAAG;AACjE,YAAI,QAAQ,KAAK,KAAK,IAAI,CAAC;AAC3B,YAAI,CAAC,OAAO,KAAK,CAAC,GAAGD,OAAOA,KAAI,KAAM,KAAK,KAAK;AAC5C,iBAAO,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK;AAAA,MACvC;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,QAAQ;AAGd,QAAI,OAAO,OAAO,OAAO,OAAO,OAAO,UAAS,SAAS,GAAG,IAAI;AAChE,QAAI,OAAO;AACP,WAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,OAAO,KAAK;AACtD,QAAI,OAAO,KAAK;AACZ,UAAI,OAAO,KAAK,SAAS,OAAO,GAAG;AACnC,UAAI,CAAC;AACD,cAAM,IAAI,WAAW,yBAAyB,OAAO,GAAG,EAAE;AAC9D,WAAK,MAAM;AAAA,IACf;AACA,QAAI,OAAO;AACP,WAAK,aAAa,KAAK,WAAW,IAAI,OAAK;AACvC,YAAI,QAAQ,OAAO,WAAW,KAAK,OAAK,EAAE,QAAQ,CAAC;AACnD,eAAO,QAAQ,MAAM,KAAK;AAAA,MAC9B,CAAC;AACL,QAAI,OAAO,cAAc;AACrB,WAAK,eAAe,KAAK,aAAa,MAAM;AAC5C,WAAK,mBAAmB,KAAK,iBAAiB,IAAI,CAAC,GAAG,MAAM;AACxD,YAAI,QAAQ,OAAO,aAAa,KAAK,OAAK,EAAE,QAAQ,EAAE,QAAQ;AAC9D,YAAI,CAAC;AACD,iBAAO;AACX,YAAI,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,MAAM,GAAG,CAAC;AACrE,aAAK,aAAa,CAAC,IAAI,eAAe,IAAI;AAC1C,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,QAAI,OAAO;AACP,WAAK,UAAU,OAAO;AAC1B,QAAI,OAAO;AACP,WAAK,UAAU,KAAK,aAAa,OAAO,OAAO;AACnD,QAAI,OAAO,UAAU;AACjB,WAAK,SAAS,OAAO;AACzB,QAAI,OAAO;AACP,WAAK,WAAW,KAAK,SAAS,OAAO,OAAO,IAAI;AACpD,QAAI,OAAO,gBAAgB;AACvB,WAAK,eAAe,OAAO;AAC/B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACV,WAAO,KAAK,SAAS,SAAS;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,MAAM;AACV,WAAO,KAAK,YAAY,KAAK,UAAU,IAAI,IAAI,OAAO,QAAQ,KAAK,WAAW,KAAK,QAAQ,MAAM,IAAI,EAAE,QAAQ,IAAI;AAAA,EACvH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AAAE,WAAO,KAAK,UAAU;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIzC,IAAI,UAAU;AAAE,WAAO,KAAK,QAAQ,MAAM,KAAK,IAAI,CAAC,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIxD,kBAAkB,MAAM;AACpB,QAAI,OAAO,KAAK;AAChB,WAAO,QAAQ,OAAO,IAAI,KAAK,IAAI,KAAK;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,SAAS;AAClB,QAAI,SAAS,OAAO,KAAK,KAAK,QAAQ,GAAG,QAAQ,OAAO,IAAI,MAAM,KAAK;AACvE,QAAI;AACA,eAAS,QAAQ,QAAQ,MAAM,GAAG,GAAG;AACjC,YAAIC,MAAK,OAAO,QAAQ,IAAI;AAC5B,YAAIA,OAAM;AACN,gBAAMA,GAAE,IAAI;AAAA,MACpB;AACJ,QAAI,WAAW;AACf,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ;AAC/B,UAAI,CAAC,MAAM,CAAC,GAAG;AACX,iBAAS,IAAI,KAAK,SAAS,OAAO,CAAC,CAAC,GAAGA,MAAKA,MAAK,KAAK,KAAK,GAAG,MAAM;AAChE,WAAC,aAAa,WAAW,IAAI,WAAW,KAAK,UAAU,CAAC,IAAIA,GAAE,IAAI;AAAA,MAC1E;AACJ,WAAO,IAAI,QAAQ,SAAS,OAAO,QAAQ;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,YAAY,MAAM;AACrB,WAAO,IAAI,UAAS,IAAI;AAAA,EAC5B;AACJ;AACA,SAAS,KAAK,MAAM,KAAK;AAAE,SAAO,KAAK,GAAG,IAAK,KAAK,MAAM,CAAC,KAAK;AAAK;AACrE,SAAS,aAAa,QAAQ;AAC1B,MAAI,OAAO;AACX,WAAS,SAAS,QAAQ;AACtB,QAAI,UAAU,MAAM,EAAE;AACtB,SAAK,MAAM,OAAO,MAAM,EAAE,OAAO,OAAO,WAAW,QAAQ,MAAM,MAAM,YACnE,MAAM,EAAE,OAAO;AAAA,MAAU,MAAM;AAAA,MAAO;AAAA;AAAA,IAA2B,MAChE,CAAC,QAAQ,KAAK,QAAQ,MAAM;AAC7B,aAAO;AAAA,EACf;AACA,SAAO;AACX;AACA,SAAS,eAAe,MAAM;AAC1B,MAAI,KAAK,UAAU;AACf,QAAI,OAAO,KAAK,SAAS,IAA4B;AACrD,WAAO,CAAC,OAAO,UAAW,KAAK,SAAS,OAAO,KAAK,KAAK,IAAK;AAAA,EAClE;AACA,SAAO,KAAK;AAChB;", "names": ["i", "id"]}