{"version": 3, "sources": ["../../@arcgis/core/layers/graphics/sources/OGCFeatureSource.js", "../../@arcgis/core/layers/OGCFeatureLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import\"../../../geometry.js\";import t from\"../../../core/Error.js\";import s from\"../../../core/Loadable.js\";import{isNone as r,isSome as o}from\"../../../core/maybe.js\";import{property as p}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as i}from\"../../../core/accessorSupport/decorators/subclass.js\";import{queryFeatureSetJSON as a,crsDefault as u,getSpatialReferenceWkid as n,getServerLandingPage as c,getServerConformance as l,getServerCollections as d,getServerOpenApi as m,getCollectionDefinition as y,crsPrefix as f}from\"../../ogc/ogcFeatureUtils.js\";import g from\"../../../rest/support/FeatureSet.js\";import h from\"../../../geometry/SpatialReference.js\";import{typeKebabDictionary as S}from\"../../../geometry/support/typeUtils.js\";let C=class extends s{constructor(){super(...arguments),this.featureDefinition=null,this.type=\"ogc-feature\"}load(e){return this.addResolvingPromise(this._loadOGCServices(this.layer,e)),this.when()}getSource(){const{featureDefinition:{collection:e,layerDefinition:t,spatialReference:s,supportedCrs:r},layer:{apiKey:o,customParameters:p,effectiveMaxRecordCount:i}}=this;return{type:\"ogc-source\",collection:e,layerDefinition:t,maxRecordCount:i,queryParameters:{apiKey:o,customParameters:p},spatialReference:s,supportedCrs:r}}queryExtent(e,t={}){return null}queryFeatureCount(e,t={}){return null}queryFeatures(e,t={}){return this.queryFeaturesJSON(e,t).then((e=>g.fromJSON(e)))}queryFeaturesJSON(e,t={}){const s=this.getSource();return this.load(t).then((()=>a(s,e,t)))}queryObjectIds(e,t={}){return null}serviceSupportsSpatialReference(e){return!(!e.isWGS84&&!e.isWebMercator)||!!this.featureDefinition.supportedCrs[e.wkid]}_conformsToType(e,t){const s=new RegExp(`^${t}$`,\"i\");return e.conformsTo.some((e=>s.test(e)))??!1}_getCapabilities(e,t){return{analytics:{supportsCacheHint:!1},attachment:null,data:{isVersioned:!1,supportsAttachment:!1,supportsM:!1,supportsZ:e},metadata:{supportsAdvancedFieldProperties:!1},operations:{supportsCalculate:!1,supportsTruncate:!1,supportsValidateSql:!1,supportsAdd:!1,supportsDelete:!1,supportsEditing:!1,supportsChangeTracking:!1,supportsQuery:!1,supportsQueryAnalytics:!1,supportsQueryAttachments:!1,supportsQueryTopFeatures:!1,supportsResizeAttachments:!1,supportsSync:!1,supportsUpdate:!1,supportsExceedsLimitStatistics:!1},query:{maxRecordCount:t,maxRecordCountFactor:void 0,standardMaxRecordCount:void 0,supportsCacheHint:!1,supportsCentroid:!1,supportsDisjointSpatialRelationship:!1,supportsDistance:!1,supportsDistinct:!1,supportsExtent:!1,supportsFormatPBF:!1,supportsGeometryProperties:!1,supportsHavingClause:!1,supportsHistoricMoment:!1,supportsMaxRecordCountFactor:!1,supportsOrderBy:!1,supportsPagination:!1,supportsPercentileStatistics:!1,supportsQuantization:!1,supportsQuantizationEditMode:!1,supportsQueryByOthers:!1,supportsQueryGeometry:!1,supportsResultType:!1,supportsStandardizedQueriesOnly:!1,supportsTopFeaturesQuery:!1,supportsStatistics:!1,supportsSpatialAggregationStatistics:!1,supportedSpatialAggregationStatistics:{envelope:!1,centroid:!1,convexHull:!1},supportsDefaultSpatialReference:!1,supportsFullTextSearch:!1,supportsCompactGeometry:!1,supportsSqlExpression:!1,tileMaxRecordCount:void 0},queryRelated:{supportsCount:!1,supportsOrderBy:!1,supportsPagination:!1,supportsCacheHint:!1},queryTopFeatures:{supportsCacheHint:!1},editing:{supportsDeleteByAnonymous:!1,supportsDeleteByOthers:!1,supportsGeometryUpdate:!1,supportsGlobalId:!1,supportsReturnServiceEditsInSourceSpatialReference:!1,supportsRollbackOnFailure:!1,supportsUpdateByAnonymous:!1,supportsUpdateByOthers:!1,supportsUploadWithItemId:!1,supportsUpdateWithoutM:!1}}}_getMaxRecordCount(e){const t=e?.components?.parameters;return t?.limit?.schema?.maximum??t?.limitFeatures?.schema?.maximum}_getStorageSpatialReference(e){const t=e.storageCrs??u,s=n(t);return r(s)?h.WGS84:new h({wkid:s})}_getSupportedSpatialReferences(e,t){const s=\"#/crs\",r=e.crs??[u],o=r.includes(s)?r.filter((e=>e!==s)).concat(t.crs??[]):r,p=/^http:\\/\\/www\\.opengis.net\\/def\\/crs\\/epsg\\/.*\\/3785$/i;return o.filter((e=>!p.test(e)))}async _loadOGCServices(e,s){const r=o(s)?s.signal:null,{apiKey:p,collectionId:i,customParameters:a,fields:u,geometryType:g,hasZ:h,objectIdField:C,timeInfo:R,url:w}=e,x={fields:u?.map((e=>e.toJSON())),geometryType:S.toJSON(g),hasZ:h??!1,objectIdField:C,timeInfo:R?.toJSON()},j={apiKey:p,customParameters:a,signal:r},F=await c(w,j),[O,v]=await Promise.all([l(F,j),d(F,j)]);if(!this._conformsToType(O,\"http://www.opengis.net/spec/ogcapi-features-1/1.0/conf/geojson\"))throw new t(\"ogc-feature-layer:no-geojson-support\",\"Server does not support geojson\");const T=v.collections.find((e=>e.id===i));if(!T)throw new t(\"ogc-feature-layer:collection-not-found\",\"Server does not contain the named collection\");const D=this._conformsToType(O,\"http://www.opengis.net/spec/ogcapi-features-1/1.0/conf/oas30\")?await m(F,j):null,_=await y(T,x,j),b=this._getMaxRecordCount(D),P=this._getCapabilities(_.hasZ,b),q=this._getStorageSpatialReference(T).toJSON(),M=this._getSupportedSpatialReferences(T,v),A=new RegExp(`^${f}`,\"i\"),E={};for(const t of M){const e=n(t);o(e)&&(E[e]||(E[e]=t.replace(A,\"\")))}this.featureDefinition={capabilities:P,collection:T,layerDefinition:_,spatialReference:q,supportedCrs:E}}};e([p()],C.prototype,\"featureDefinition\",void 0),e([p({constructOnly:!0})],C.prototype,\"layer\",void 0),e([p()],C.prototype,\"type\",void 0),C=e([i(\"esri.layers.graphics.sources.OGCFeatureSource\")],C);export{C as OGCFeatureSource};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import\"../geometry.js\";import r from\"../PopupTemplate.js\";import\"../renderers/ClassBreaksRenderer.js\";import\"../renderers/DictionaryRenderer.js\";import\"../renderers/DotDensityRenderer.js\";import\"../renderers/HeatmapRenderer.js\";import\"../renderers/PieChartRenderer.js\";import\"../renderers/Renderer.js\";import\"../renderers/SimpleRenderer.js\";import\"../renderers/UniqueValueRenderer.js\";import\"../renderers/support/jsonUtils.js\";import{rendererTypes as t,webSceneRendererTypes as i}from\"../renderers/support/types.js\";import{MultiOriginJSONMixin as o}from\"../core/MultiOriginJSONSupport.js\";import{property as s}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as n}from\"../core/accessorSupport/decorators/subclass.js\";import{featureGeometryTypeKebabDictionary as p}from\"../geometry/support/typeUtils.js\";import l from\"./Layer.js\";import{OGCFeatureSource as a}from\"./graphics/sources/OGCFeatureSource.js\";import{APIKeyMixin as d}from\"./mixins/APIKeyMixin.js\";import{BlendLayer as y}from\"./mixins/BlendLayer.js\";import{CustomParametersMixin as m}from\"./mixins/CustomParametersMixin.js\";import{FeatureEffectLayer as u}from\"./mixins/FeatureEffectLayer.js\";import{FeatureReductionLayer as c}from\"./mixins/FeatureReductionLayer.js\";import{OperationalLayer as f}from\"./mixins/OperationalLayer.js\";import{OrderedLayer as h}from\"./mixins/OrderedLayer.js\";import{PortalLayer as j}from\"./mixins/PortalLayer.js\";import{RefreshableLayer as g}from\"./mixins/RefreshableLayer.js\";import{ScaleRangeLayer as v}from\"./mixins/ScaleRangeLayer.js\";import{TemporalLayer as x}from\"./mixins/TemporalLayer.js\";import{elevationInfo as b,labelsVisible as I,legendEnabled as S,popupEnabled as R,screenSizePerspectiveEnabled as F,url as L}from\"./support/commonProperties.js\";import O from\"./support/FeatureType.js\";import T from\"./support/Field.js\";import{defineFieldProperties as w}from\"./support/fieldProperties.js\";import{fixRendererFields as D,fixTimeInfoFields as C}from\"./support/fieldUtils.js\";import P from\"./support/LabelClass.js\";import{reader as E}from\"./support/labelingInfo.js\";import _ from\"../rest/support/Query.js\";import{createPopupTemplate as U}from\"../support/popupUtils.js\";import q from\"../geometry/Extent.js\";import G from\"../geometry/SpatialReference.js\";const M=w();let B=class extends(d(m(c(u(y(h(x(v(f(j(g(o(l))))))))))))){constructor(e){super(e),this.capabilities=null,this.collectionId=null,this.copyright=null,this.definitionExpression=null,this.description=null,this.displayField=null,this.elevationInfo=null,this.fields=null,this.fieldsIndex=null,this.fullExtent=null,this.geometryType=null,this.hasZ=void 0,this.labelingInfo=null,this.labelsVisible=!0,this.legendEnabled=!0,this.maxRecordCount=null,this.objectIdField=null,this.operationalLayerType=\"OGCFeatureLayer\",this.popupEnabled=!0,this.popupTemplate=null,this.screenSizePerspectiveEnabled=!0,this.source=new a({layer:this}),this.spatialReference=null,this.title=null,this.type=\"ogc-feature\",this.typeIdField=null,this.types=null,this.url=null}destroy(){this.source?.destroy()}load(e){return this.addResolvingPromise(this.loadFromPortal({supportedTypes:[\"OGCFeatureServer\"]},e).then((()=>this._fetchService(e)))),this.when()}get defaultPopupTemplate(){return this.createPopupTemplate()}get effectiveMaxRecordCount(){return this.maxRecordCount??this.capabilities?.query.maxRecordCount??5e3}get isTable(){return this.loaded&&null==this.geometryType}set renderer(e){D(e,this.fieldsIndex),this._set(\"renderer\",e)}on(e,r){return super.on(e,r)}createPopupTemplate(e){return U(this,e)}createQuery(){return new _}getField(e){return this.fieldsIndex.get(e)}getFieldDomain(e,r){let t,i=!1;const o=r?.feature?.attributes,s=this.typeIdField&&o?.[this.typeIdField];return null!=s&&this.types&&(i=this.types.some((r=>r.id==s&&(t=r.domains?.[e],\"inherited\"===t?.type&&(t=this._getLayerDomain(e)),!0)))),i||t||(t=this._getLayerDomain(e)),t}queryFeatures(e,r){return this.load().then((()=>this.source.queryFeatures(_.from(e)||this.createQuery(),r))).then((e=>(e?.features?.forEach((e=>{e.layer=e.sourceLayer=this})),e)))}serviceSupportsSpatialReference(e){return this.source?.serviceSupportsSpatialReference(e)??!1}async _fetchService(e){await this.source.load(e),this.read(this.source.featureDefinition,{origin:\"service\"}),D(this.renderer,this.fieldsIndex),C(this.timeInfo,this.fieldsIndex)}_getLayerDomain(e){if(!this.fields)return null;for(const r of this.fields)if(r.name===e&&r.domain)return r.domain;return null}};e([s({readOnly:!0,json:{origins:{service:{read:!0}}}})],B.prototype,\"capabilities\",void 0),e([s({type:String,json:{write:!0}})],B.prototype,\"collectionId\",void 0),e([s({type:String})],B.prototype,\"copyright\",void 0),e([s({readOnly:!0})],B.prototype,\"defaultPopupTemplate\",null),e([s({type:String})],B.prototype,\"definitionExpression\",void 0),e([s({readOnly:!0,type:String,json:{origins:{service:{name:\"collection.description\"}}}})],B.prototype,\"description\",void 0),e([s({type:String})],B.prototype,\"displayField\",void 0),e([s({type:Number})],B.prototype,\"effectiveMaxRecordCount\",null),e([s(b)],B.prototype,\"elevationInfo\",void 0),e([s({type:[T],json:{origins:{service:{name:\"layerDefinition.fields\"}}}})],B.prototype,\"fields\",void 0),e([s(M.fieldsIndex)],B.prototype,\"fieldsIndex\",void 0),e([s({readOnly:!0,type:q,json:{origins:{service:{name:\"layerDefinition.extent\"}}}})],B.prototype,\"fullExtent\",void 0),e([s({type:p.apiValues,json:{origins:{service:{name:\"layerDefinition.geometryType\",read:{reader:p.read}}}}})],B.prototype,\"geometryType\",void 0),e([s({type:Boolean,json:{origins:{service:{name:\"layerDefinition.hasZ\"}}}})],B.prototype,\"hasZ\",void 0),e([s({type:Boolean,readOnly:!0})],B.prototype,\"isTable\",null),e([s({type:[P],json:{origins:{\"web-document\":{name:\"layerDefinition.drawingInfo.labelingInfo\",read:{reader:E},write:!0}}}})],B.prototype,\"labelingInfo\",void 0),e([s(I)],B.prototype,\"labelsVisible\",void 0),e([s(S)],B.prototype,\"legendEnabled\",void 0),e([s({type:Number})],B.prototype,\"maxRecordCount\",void 0),e([s({type:String,json:{origins:{service:{name:\"layerDefinition.objectIdField\"}}}})],B.prototype,\"objectIdField\",void 0),e([s({type:[\"OGCFeatureLayer\"]})],B.prototype,\"operationalLayerType\",void 0),e([s(R)],B.prototype,\"popupEnabled\",void 0),e([s({type:r,json:{name:\"popupInfo\",write:!0}})],B.prototype,\"popupTemplate\",void 0),e([s({types:t,json:{origins:{service:{name:\"layerDefinition.drawingInfo.renderer\",write:!1},\"web-scene\":{types:i,name:\"layerDefinition.drawingInfo.renderer\",write:!0}},name:\"layerDefinition.drawingInfo.renderer\",write:!0}})],B.prototype,\"renderer\",null),e([s(F)],B.prototype,\"screenSizePerspectiveEnabled\",void 0),e([s({readOnly:!0})],B.prototype,\"source\",void 0),e([s({readOnly:!0,type:G,json:{origins:{service:{read:!0}}}})],B.prototype,\"spatialReference\",void 0),e([s({type:String,json:{write:{enabled:!0,ignoreOrigin:!0,isRequired:!0},origins:{service:{name:\"collection.title\"}}}})],B.prototype,\"title\",void 0),e([s({readOnly:!0,json:{read:!1}})],B.prototype,\"type\",void 0),e([s({type:String,readOnly:!0})],B.prototype,\"typeIdField\",void 0),e([s({type:[O]})],B.prototype,\"types\",void 0),e([s(L)],B.prototype,\"url\",void 0),B=e([n(\"esri.layers.OGCFeatureLayer\")],B);const V=B;export{V as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIs4B,IAAIA,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,oBAAkB,MAAK,KAAK,OAAK;AAAA,EAAa;AAAA,EAAC,KAAKC,IAAE;AAAC,WAAO,KAAK,oBAAoB,KAAK,iBAAiB,KAAK,OAAMA,EAAC,CAAC,GAAE,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,UAAK,EAAC,mBAAkB,EAAC,YAAWA,IAAE,iBAAgBC,IAAE,kBAAiBC,IAAE,cAAaC,GAAC,GAAE,OAAM,EAAC,QAAOC,IAAE,kBAAiBC,IAAE,yBAAwBC,GAAC,EAAC,IAAE;AAAK,WAAM,EAAC,MAAK,cAAa,YAAWN,IAAE,iBAAgBC,IAAE,gBAAeK,IAAE,iBAAgB,EAAC,QAAOF,IAAE,kBAAiBC,GAAC,GAAE,kBAAiBH,IAAE,cAAaC,GAAC;AAAA,EAAC;AAAA,EAAC,YAAYH,IAAEC,KAAE,CAAC,GAAE;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,kBAAkBD,IAAEC,KAAE,CAAC,GAAE;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,cAAcD,IAAEC,KAAE,CAAC,GAAE;AAAC,WAAO,KAAK,kBAAkBD,IAAEC,EAAC,EAAE,KAAM,CAAAD,OAAGO,GAAE,SAASP,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAEC,KAAE,CAAC,GAAE;AAAC,UAAMC,KAAE,KAAK,UAAU;AAAE,WAAO,KAAK,KAAKD,EAAC,EAAE,KAAM,MAAI,EAAEC,IAAEF,IAAEC,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAEC,KAAE,CAAC,GAAE;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,gCAAgCD,IAAE;AAAC,WAAM,EAAE,CAACA,GAAE,WAAS,CAACA,GAAE,kBAAgB,CAAC,CAAC,KAAK,kBAAkB,aAAaA,GAAE,IAAI;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAEC,IAAE;AAAC,UAAMC,KAAE,IAAI,OAAO,IAAID,EAAC,KAAI,GAAG;AAAE,WAAOD,GAAE,WAAW,KAAM,CAAAA,OAAGE,GAAE,KAAKF,EAAC,CAAE,KAAG;AAAA,EAAE;AAAA,EAAC,iBAAiBA,IAAEC,IAAE;AAAC,WAAM,EAAC,WAAU,EAAC,mBAAkB,MAAE,GAAE,YAAW,MAAK,MAAK,EAAC,aAAY,OAAG,oBAAmB,OAAG,WAAU,OAAG,WAAUD,GAAC,GAAE,UAAS,EAAC,iCAAgC,MAAE,GAAE,YAAW,EAAC,mBAAkB,OAAG,kBAAiB,OAAG,qBAAoB,OAAG,aAAY,OAAG,gBAAe,OAAG,iBAAgB,OAAG,wBAAuB,OAAG,eAAc,OAAG,wBAAuB,OAAG,0BAAyB,OAAG,0BAAyB,OAAG,2BAA0B,OAAG,cAAa,OAAG,gBAAe,OAAG,gCAA+B,MAAE,GAAE,OAAM,EAAC,gBAAeC,IAAE,sBAAqB,QAAO,wBAAuB,QAAO,mBAAkB,OAAG,kBAAiB,OAAG,qCAAoC,OAAG,kBAAiB,OAAG,kBAAiB,OAAG,gBAAe,OAAG,mBAAkB,OAAG,4BAA2B,OAAG,sBAAqB,OAAG,wBAAuB,OAAG,8BAA6B,OAAG,iBAAgB,OAAG,oBAAmB,OAAG,8BAA6B,OAAG,sBAAqB,OAAG,8BAA6B,OAAG,uBAAsB,OAAG,uBAAsB,OAAG,oBAAmB,OAAG,iCAAgC,OAAG,0BAAyB,OAAG,oBAAmB,OAAG,sCAAqC,OAAG,uCAAsC,EAAC,UAAS,OAAG,UAAS,OAAG,YAAW,MAAE,GAAE,iCAAgC,OAAG,wBAAuB,OAAG,yBAAwB,OAAG,uBAAsB,OAAG,oBAAmB,OAAM,GAAE,cAAa,EAAC,eAAc,OAAG,iBAAgB,OAAG,oBAAmB,OAAG,mBAAkB,MAAE,GAAE,kBAAiB,EAAC,mBAAkB,MAAE,GAAE,SAAQ,EAAC,2BAA0B,OAAG,wBAAuB,OAAG,wBAAuB,OAAG,kBAAiB,OAAG,oDAAmD,OAAG,2BAA0B,OAAG,2BAA0B,OAAG,wBAAuB,OAAG,0BAAyB,OAAG,wBAAuB,MAAE,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBD,IAAE;AAJ9uH;AAI+uH,UAAMC,MAAE,KAAAD,MAAA,gBAAAA,GAAG,eAAH,mBAAe;AAAW,aAAO,WAAAC,MAAA,gBAAAA,GAAG,UAAH,mBAAU,WAAV,mBAAkB,cAAS,WAAAA,MAAA,gBAAAA,GAAG,kBAAH,mBAAkB,WAAlB,mBAA0B;AAAA,EAAO;AAAA,EAAC,4BAA4BD,IAAE;AAAC,UAAMC,KAAED,GAAE,cAAY,GAAEE,KAAE,EAAED,EAAC;AAAE,WAAO,EAAEC,EAAC,IAAE,EAAE,QAAM,IAAI,EAAE,EAAC,MAAKA,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,+BAA+BF,IAAEC,IAAE;AAAC,UAAMC,KAAE,SAAQC,KAAEH,GAAE,OAAK,CAAC,CAAC,GAAEI,KAAED,GAAE,SAASD,EAAC,IAAEC,GAAE,OAAQ,CAAAH,OAAGA,OAAIE,EAAE,EAAE,OAAOD,GAAE,OAAK,CAAC,CAAC,IAAEE,IAAEE,KAAE;AAAyD,WAAOD,GAAE,OAAQ,CAAAJ,OAAG,CAACK,GAAE,KAAKL,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiBA,IAAEE,IAAE;AAAC,UAAMC,KAAE,EAAED,EAAC,IAAEA,GAAE,SAAO,MAAK,EAAC,QAAOG,IAAE,cAAaC,IAAE,kBAAiBE,IAAE,QAAO,GAAE,cAAa,GAAE,MAAK,GAAE,eAAcT,IAAE,UAAS,GAAE,KAAIU,GAAC,IAAET,IAAEO,KAAE,EAAC,QAAO,uBAAG,IAAK,CAAAP,OAAGA,GAAE,OAAO,IAAI,cAAa,EAAE,OAAO,CAAC,GAAE,MAAK,KAAG,OAAG,eAAcD,IAAE,UAAS,uBAAG,SAAQ,GAAEW,KAAE,EAAC,QAAOL,IAAE,kBAAiBG,IAAE,QAAOL,GAAC,GAAEQ,KAAE,MAAMJ,GAAEE,IAAEC,EAAC,GAAE,CAACE,IAAEC,EAAC,IAAE,MAAM,QAAQ,IAAI,CAAC,EAAEF,IAAED,EAAC,GAAE,EAAEC,IAAED,EAAC,CAAC,CAAC;AAAE,QAAG,CAAC,KAAK,gBAAgBE,IAAE,gEAAgE,EAAE,OAAM,IAAI,EAAE,wCAAuC,iCAAiC;AAAE,UAAME,KAAED,GAAE,YAAY,KAAM,CAAAb,OAAGA,GAAE,OAAKM,EAAE;AAAE,QAAG,CAACQ,GAAE,OAAM,IAAI,EAAE,0CAAyC,8CAA8C;AAAE,UAAM,IAAE,KAAK,gBAAgBF,IAAE,8DAA8D,IAAE,MAAM,EAAED,IAAED,EAAC,IAAE,MAAKK,KAAE,MAAM,EAAED,IAAEP,IAAEG,EAAC,GAAEM,KAAE,KAAK,mBAAmB,CAAC,GAAE,IAAE,KAAK,iBAAiBD,GAAE,MAAKC,EAAC,GAAE,IAAE,KAAK,4BAA4BF,EAAC,EAAE,OAAO,GAAEG,KAAE,KAAK,+BAA+BH,IAAED,EAAC,GAAE,IAAE,IAAI,OAAO,IAAI,CAAC,IAAG,GAAG,GAAE,IAAE,CAAC;AAAE,eAAUZ,MAAKgB,IAAE;AAAC,YAAMjB,KAAE,EAAEC,EAAC;AAAE,QAAED,EAAC,MAAI,EAAEA,EAAC,MAAI,EAAEA,EAAC,IAAEC,GAAE,QAAQ,GAAE,EAAE;AAAA,IAAG;AAAC,SAAK,oBAAkB,EAAC,cAAa,GAAE,YAAWa,IAAE,iBAAgBC,IAAE,kBAAiB,GAAE,cAAa,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEhB,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,+CAA+C,CAAC,GAAEA,EAAC;;;ACAlqG,IAAM,IAAEmB,GAAE;AAAE,IAAI,IAAE,cAAcC,GAAEC,GAAEC,GAAEC,GAAE,EAAEC,GAAEC,GAAEC,GAAEF,GAAE,EAAED,GAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,YAAYI,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,eAAa,MAAK,KAAK,eAAa,MAAK,KAAK,YAAU,MAAK,KAAK,uBAAqB,MAAK,KAAK,cAAY,MAAK,KAAK,eAAa,MAAK,KAAK,gBAAc,MAAK,KAAK,SAAO,MAAK,KAAK,cAAY,MAAK,KAAK,aAAW,MAAK,KAAK,eAAa,MAAK,KAAK,OAAK,QAAO,KAAK,eAAa,MAAK,KAAK,gBAAc,MAAG,KAAK,gBAAc,MAAG,KAAK,iBAAe,MAAK,KAAK,gBAAc,MAAK,KAAK,uBAAqB,mBAAkB,KAAK,eAAa,MAAG,KAAK,gBAAc,MAAK,KAAK,+BAA6B,MAAG,KAAK,SAAO,IAAIC,GAAE,EAAC,OAAM,KAAI,CAAC,GAAE,KAAK,mBAAiB,MAAK,KAAK,QAAM,MAAK,KAAK,OAAK,eAAc,KAAK,cAAY,MAAK,KAAK,QAAM,MAAK,KAAK,MAAI;AAAA,EAAI;AAAA,EAAC,UAAS;AAJ7kG;AAI8kG,eAAK,WAAL,mBAAa;AAAA,EAAS;AAAA,EAAC,KAAKD,IAAE;AAAC,WAAO,KAAK,oBAAoB,KAAK,eAAe,EAAC,gBAAe,CAAC,kBAAkB,EAAC,GAAEA,EAAC,EAAE,KAAM,MAAI,KAAK,cAAcA,EAAC,CAAE,CAAC,GAAE,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,IAAI,0BAAyB;AAJn1G;AAIo1G,WAAO,KAAK,oBAAgB,UAAK,iBAAL,mBAAmB,MAAM,mBAAgB;AAAA,EAAG;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,UAAQ,QAAM,KAAK;AAAA,EAAY;AAAA,EAAC,IAAI,SAASA,IAAE;AAAC,IAAAE,GAAEF,IAAE,KAAK,WAAW,GAAE,KAAK,KAAK,YAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEG,IAAE;AAAC,WAAO,MAAM,GAAGH,IAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBH,IAAE;AAAC,WAAOJ,GAAE,MAAKI,EAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,WAAO,IAAI;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,WAAO,KAAK,YAAY,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAEG,IAAE;AAJnrH;AAIorH,QAAIJ,IAAEN,KAAE;AAAG,UAAMC,MAAE,KAAAS,MAAA,gBAAAA,GAAG,YAAH,mBAAY,YAAWX,KAAE,KAAK,gBAAaE,MAAA,gBAAAA,GAAI,KAAK;AAAa,WAAO,QAAMF,MAAG,KAAK,UAAQC,KAAE,KAAK,MAAM,KAAM,CAAAU,OAAC;AAJzzH,UAAAC;AAI2zH,aAAAD,GAAE,MAAIX,OAAIO,MAAEK,MAAAD,GAAE,YAAF,gBAAAC,IAAYJ,KAAG,iBAAcD,MAAA,gBAAAA,GAAG,UAAOA,KAAE,KAAK,gBAAgBC,EAAC,IAAG;AAAA,KAAI,IAAGP,MAAGM,OAAIA,KAAE,KAAK,gBAAgBC,EAAC,IAAGD;AAAA,EAAC;AAAA,EAAC,cAAcC,IAAEG,IAAE;AAAC,WAAO,KAAK,KAAK,EAAE,KAAM,MAAI,KAAK,OAAO,cAAc,EAAE,KAAKH,EAAC,KAAG,KAAK,YAAY,GAAEG,EAAC,CAAE,EAAE,KAAM,CAAAH,OAAC;AAJxiI;AAI2iI,mBAAAA,MAAA,gBAAAA,GAAG,aAAH,mBAAa,QAAS,CAAAA,OAAG;AAAC,QAAAA,GAAE,QAAMA,GAAE,cAAY;AAAA,MAAI,IAAIA;AAAA,KAAG;AAAA,EAAC;AAAA,EAAC,gCAAgCA,IAAE;AAJ1oI;AAI2oI,aAAO,UAAK,WAAL,mBAAa,gCAAgCA,QAAI;AAAA,EAAE;AAAA,EAAC,MAAM,cAAcA,IAAE;AAAC,UAAM,KAAK,OAAO,KAAKA,EAAC,GAAE,KAAK,KAAK,KAAK,OAAO,mBAAkB,EAAC,QAAO,UAAS,CAAC,GAAEE,GAAE,KAAK,UAAS,KAAK,WAAW,GAAEG,GAAE,KAAK,UAAS,KAAK,WAAW;AAAA,EAAC;AAAA,EAAC,gBAAgBL,IAAE;AAAC,QAAG,CAAC,KAAK,OAAO,QAAO;AAAK,eAAUG,MAAK,KAAK,OAAO,KAAGA,GAAE,SAAOH,MAAGG,GAAE,OAAO,QAAOA,GAAE;AAAO,WAAO;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,KAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,wBAAuB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,yBAAwB,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,2BAA0B,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACG,EAAC,GAAE,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,yBAAwB,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAE,WAAW,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,GAAE,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,yBAAwB,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,WAAU,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,gCAA+B,MAAK,EAAC,QAAO,EAAE,KAAI,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,uBAAsB,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,GAAE,MAAK,EAAC,SAAQ,EAAC,gBAAe,EAAC,MAAK,4CAA2C,MAAK,EAAC,QAAOb,GAAC,GAAE,OAAM,KAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAEc,EAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,gCAA+B,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,iBAAiB,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKC,IAAE,MAAK,EAAC,MAAK,aAAY,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAMZ,IAAE,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,wCAAuC,OAAM,MAAE,GAAE,aAAY,EAAC,OAAMD,IAAE,MAAK,wCAAuC,OAAM,KAAE,EAAC,GAAE,MAAK,wCAAuC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gCAA+B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,GAAE,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,KAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,cAAa,MAAG,YAAW,KAAE,GAAE,SAAQ,EAAC,SAAQ,EAAC,MAAK,mBAAkB,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACA,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAEc,EAAC,CAAC,GAAE,EAAE,WAAU,OAAM,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,6BAA6B,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["C", "e", "t", "s", "r", "o", "p", "i", "x", "a", "w", "j", "F", "O", "v", "T", "_", "b", "M", "s", "i", "o", "n", "p", "c", "a", "t", "e", "C", "F", "r", "_a", "x", "y", "m", "k", "f"]}