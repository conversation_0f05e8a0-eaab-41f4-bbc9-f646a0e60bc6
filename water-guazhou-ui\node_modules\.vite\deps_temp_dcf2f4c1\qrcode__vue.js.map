{"version": 3, "sources": ["../../qrcode.vue/dist/qrcode.vue.esm.js"], "sourcesContent": ["/*!\n * qrcode.vue v3.4.1\n * A Vue.js component to generate QRCode.\n * © 2017-2023 @scopewu(https://github.com/scopewu)\n * MIT License.\n */\nimport { defineComponent, ref, onUpdated, h, onMounted } from 'vue';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\n/*\n * QR Code generator library (TypeScript)\n *\n * Copyright (c) Project Nayuki. (MIT License)\n * https://www.nayuki.io/page/qr-code-generator-library\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy of\n * this software and associated documentation files (the \"Software\"), to deal in\n * the Software without restriction, including without limitation the rights to\n * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\n * the Software, and to permit persons to whom the Software is furnished to do so,\n * subject to the following conditions:\n * - The above copyright notice and this permission notice shall be included in\n *   all copies or substantial portions of the Software.\n * - The Software is provided \"as is\", without warranty of any kind, express or\n *   implied, including but not limited to the warranties of merchantability,\n *   fitness for a particular purpose and noninfringement. In no event shall the\n *   authors or copyright holders be liable for any claim, damages or other\n *   liability, whether in an action of contract, tort or otherwise, arising from,\n *   out of or in connection with the Software or the use or other dealings in the\n *   Software.\n */\nvar qrcodegen;\n(function (qrcodegen) {\n    /*---- QR Code symbol class ----*/\n    /*\n     * A QR Code symbol, which is a type of two-dimension barcode.\n     * Invented by Denso Wave and described in the ISO/IEC 18004 standard.\n     * Instances of this class represent an immutable square grid of dark and light cells.\n     * The class provides static factory functions to create a QR Code from text or binary data.\n     * The class covers the QR Code Model 2 specification, supporting all versions (sizes)\n     * from 1 to 40, all 4 error correction levels, and 4 character encoding modes.\n     *\n     * Ways to create a QR Code object:\n     * - High level: Take the payload data and call QrCode.encodeText() or QrCode.encodeBinary().\n     * - Mid level: Custom-make the list of segments and call QrCode.encodeSegments().\n     * - Low level: Custom-make the array of data codeword bytes (including\n     *   segment headers and final padding, excluding error correction codewords),\n     *   supply the appropriate version number, and call the QrCode() constructor.\n     * (Note that all ways require supplying the desired error correction level.)\n     */\n    var QrCode = /** @class */ (function () {\n        /*-- Constructor (low level) and fields --*/\n        // Creates a new QR Code with the given version number,\n        // error correction level, data codeword bytes, and mask number.\n        // This is a low-level API that most users should not use directly.\n        // A mid-level API is the encodeSegments() function.\n        function QrCode(\n        // The version number of this QR Code, which is between 1 and 40 (inclusive).\n        // This determines the size of this barcode.\n        version, \n        // The error correction level used in this QR Code.\n        errorCorrectionLevel, dataCodewords, msk) {\n            this.version = version;\n            this.errorCorrectionLevel = errorCorrectionLevel;\n            // The modules of this QR Code (false = light, true = dark).\n            // Immutable after constructor finishes. Accessed through getModule().\n            this.modules = [];\n            // Indicates function modules that are not subjected to masking. Discarded when constructor finishes.\n            this.isFunction = [];\n            // Check scalar arguments\n            if (version < QrCode.MIN_VERSION || version > QrCode.MAX_VERSION)\n                throw new RangeError(\"Version value out of range\");\n            if (msk < -1 || msk > 7)\n                throw new RangeError(\"Mask value out of range\");\n            this.size = version * 4 + 17;\n            // Initialize both grids to be size*size arrays of Boolean false\n            var row = [];\n            for (var i = 0; i < this.size; i++)\n                row.push(false);\n            for (var i = 0; i < this.size; i++) {\n                this.modules.push(row.slice()); // Initially all light\n                this.isFunction.push(row.slice());\n            }\n            // Compute ECC, draw modules\n            this.drawFunctionPatterns();\n            var allCodewords = this.addEccAndInterleave(dataCodewords);\n            this.drawCodewords(allCodewords);\n            // Do masking\n            if (msk == -1) { // Automatically choose best mask\n                var minPenalty = 1000000000;\n                for (var i = 0; i < 8; i++) {\n                    this.applyMask(i);\n                    this.drawFormatBits(i);\n                    var penalty = this.getPenaltyScore();\n                    if (penalty < minPenalty) {\n                        msk = i;\n                        minPenalty = penalty;\n                    }\n                    this.applyMask(i); // Undoes the mask due to XOR\n                }\n            }\n            assert(0 <= msk && msk <= 7);\n            this.mask = msk;\n            this.applyMask(msk); // Apply the final choice of mask\n            this.drawFormatBits(msk); // Overwrite old format bits\n            this.isFunction = [];\n        }\n        /*-- Static factory functions (high level) --*/\n        // Returns a QR Code representing the given Unicode text string at the given error correction level.\n        // As a conservative upper bound, this function is guaranteed to succeed for strings that have 738 or fewer\n        // Unicode code points (not UTF-16 code units) if the low error correction level is used. The smallest possible\n        // QR Code version is automatically chosen for the output. The ECC level of the result may be higher than the\n        // ecl argument if it can be done without increasing the version.\n        QrCode.encodeText = function (text, ecl) {\n            var segs = qrcodegen.QrSegment.makeSegments(text);\n            return QrCode.encodeSegments(segs, ecl);\n        };\n        // Returns a QR Code representing the given binary data at the given error correction level.\n        // This function always encodes using the binary segment mode, not any text mode. The maximum number of\n        // bytes allowed is 2953. The smallest possible QR Code version is automatically chosen for the output.\n        // The ECC level of the result may be higher than the ecl argument if it can be done without increasing the version.\n        QrCode.encodeBinary = function (data, ecl) {\n            var seg = qrcodegen.QrSegment.makeBytes(data);\n            return QrCode.encodeSegments([seg], ecl);\n        };\n        /*-- Static factory functions (mid level) --*/\n        // Returns a QR Code representing the given segments with the given encoding parameters.\n        // The smallest possible QR Code version within the given range is automatically\n        // chosen for the output. Iff boostEcl is true, then the ECC level of the result\n        // may be higher than the ecl argument if it can be done without increasing the\n        // version. The mask number is either between 0 to 7 (inclusive) to force that\n        // mask, or -1 to automatically choose an appropriate mask (which may be slow).\n        // This function allows the user to create a custom sequence of segments that switches\n        // between modes (such as alphanumeric and byte) to encode text in less space.\n        // This is a mid-level API; the high-level API is encodeText() and encodeBinary().\n        QrCode.encodeSegments = function (segs, ecl, minVersion, maxVersion, mask, boostEcl) {\n            if (minVersion === void 0) { minVersion = 1; }\n            if (maxVersion === void 0) { maxVersion = 40; }\n            if (mask === void 0) { mask = -1; }\n            if (boostEcl === void 0) { boostEcl = true; }\n            if (!(QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= QrCode.MAX_VERSION)\n                || mask < -1 || mask > 7)\n                throw new RangeError(\"Invalid value\");\n            // Find the minimal version number to use\n            var version;\n            var dataUsedBits;\n            for (version = minVersion;; version++) {\n                var dataCapacityBits_1 = QrCode.getNumDataCodewords(version, ecl) * 8; // Number of data bits available\n                var usedBits = QrSegment.getTotalBits(segs, version);\n                if (usedBits <= dataCapacityBits_1) {\n                    dataUsedBits = usedBits;\n                    break; // This version number is found to be suitable\n                }\n                if (version >= maxVersion) // All versions in the range could not fit the given data\n                    throw new RangeError(\"Data too long\");\n            }\n            // Increase the error correction level while the data still fits in the current version number\n            for (var _i = 0, _a = [QrCode.Ecc.MEDIUM, QrCode.Ecc.QUARTILE, QrCode.Ecc.HIGH]; _i < _a.length; _i++) { // From low to high\n                var newEcl = _a[_i];\n                if (boostEcl && dataUsedBits <= QrCode.getNumDataCodewords(version, newEcl) * 8)\n                    ecl = newEcl;\n            }\n            // Concatenate all segments to create the data bit string\n            var bb = [];\n            for (var _b = 0, segs_1 = segs; _b < segs_1.length; _b++) {\n                var seg = segs_1[_b];\n                appendBits(seg.mode.modeBits, 4, bb);\n                appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n                for (var _c = 0, _d = seg.getData(); _c < _d.length; _c++) {\n                    var b = _d[_c];\n                    bb.push(b);\n                }\n            }\n            assert(bb.length == dataUsedBits);\n            // Add terminator and pad up to a byte if applicable\n            var dataCapacityBits = QrCode.getNumDataCodewords(version, ecl) * 8;\n            assert(bb.length <= dataCapacityBits);\n            appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n            appendBits(0, (8 - bb.length % 8) % 8, bb);\n            assert(bb.length % 8 == 0);\n            // Pad with alternating bytes until data capacity is reached\n            for (var padByte = 0xEC; bb.length < dataCapacityBits; padByte ^= 0xEC ^ 0x11)\n                appendBits(padByte, 8, bb);\n            // Pack bits into bytes in big endian\n            var dataCodewords = [];\n            while (dataCodewords.length * 8 < bb.length)\n                dataCodewords.push(0);\n            bb.forEach(function (b, i) {\n                return dataCodewords[i >>> 3] |= b << (7 - (i & 7));\n            });\n            // Create the QR Code object\n            return new QrCode(version, ecl, dataCodewords, mask);\n        };\n        /*-- Accessor methods --*/\n        // Returns the color of the module (pixel) at the given coordinates, which is false\n        // for light or true for dark. The top left corner has the coordinates (x=0, y=0).\n        // If the given coordinates are out of bounds, then false (light) is returned.\n        QrCode.prototype.getModule = function (x, y) {\n            return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];\n        };\n        QrCode.prototype.getModules = function () {\n            return this.modules;\n        };\n        /*-- Private helper methods for constructor: Drawing function modules --*/\n        // Reads this object's version field, and draws and marks all function modules.\n        QrCode.prototype.drawFunctionPatterns = function () {\n            // Draw horizontal and vertical timing patterns\n            for (var i = 0; i < this.size; i++) {\n                this.setFunctionModule(6, i, i % 2 == 0);\n                this.setFunctionModule(i, 6, i % 2 == 0);\n            }\n            // Draw 3 finder patterns (all corners except bottom right; overwrites some timing modules)\n            this.drawFinderPattern(3, 3);\n            this.drawFinderPattern(this.size - 4, 3);\n            this.drawFinderPattern(3, this.size - 4);\n            // Draw numerous alignment patterns\n            var alignPatPos = this.getAlignmentPatternPositions();\n            var numAlign = alignPatPos.length;\n            for (var i = 0; i < numAlign; i++) {\n                for (var j = 0; j < numAlign; j++) {\n                    // Don't draw on the three finder corners\n                    if (!(i == 0 && j == 0 || i == 0 && j == numAlign - 1 || i == numAlign - 1 && j == 0))\n                        this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);\n                }\n            }\n            // Draw configuration data\n            this.drawFormatBits(0); // Dummy mask value; overwritten later in the constructor\n            this.drawVersion();\n        };\n        // Draws two copies of the format bits (with its own error correction code)\n        // based on the given mask and this object's error correction level field.\n        QrCode.prototype.drawFormatBits = function (mask) {\n            // Calculate error correction code and pack bits\n            var data = this.errorCorrectionLevel.formatBits << 3 | mask; // errCorrLvl is uint2, mask is uint3\n            var rem = data;\n            for (var i = 0; i < 10; i++)\n                rem = (rem << 1) ^ ((rem >>> 9) * 0x537);\n            var bits = (data << 10 | rem) ^ 0x5412; // uint15\n            assert(bits >>> 15 == 0);\n            // Draw first copy\n            for (var i = 0; i <= 5; i++)\n                this.setFunctionModule(8, i, getBit(bits, i));\n            this.setFunctionModule(8, 7, getBit(bits, 6));\n            this.setFunctionModule(8, 8, getBit(bits, 7));\n            this.setFunctionModule(7, 8, getBit(bits, 8));\n            for (var i = 9; i < 15; i++)\n                this.setFunctionModule(14 - i, 8, getBit(bits, i));\n            // Draw second copy\n            for (var i = 0; i < 8; i++)\n                this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));\n            for (var i = 8; i < 15; i++)\n                this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));\n            this.setFunctionModule(8, this.size - 8, true); // Always dark\n        };\n        // Draws two copies of the version bits (with its own error correction code),\n        // based on this object's version field, iff 7 <= version <= 40.\n        QrCode.prototype.drawVersion = function () {\n            if (this.version < 7)\n                return;\n            // Calculate error correction code and pack bits\n            var rem = this.version; // version is uint6, in the range [7, 40]\n            for (var i = 0; i < 12; i++)\n                rem = (rem << 1) ^ ((rem >>> 11) * 0x1F25);\n            var bits = this.version << 12 | rem; // uint18\n            assert(bits >>> 18 == 0);\n            // Draw two copies\n            for (var i = 0; i < 18; i++) {\n                var color = getBit(bits, i);\n                var a = this.size - 11 + i % 3;\n                var b = Math.floor(i / 3);\n                this.setFunctionModule(a, b, color);\n                this.setFunctionModule(b, a, color);\n            }\n        };\n        // Draws a 9*9 finder pattern including the border separator,\n        // with the center module at (x, y). Modules can be out of bounds.\n        QrCode.prototype.drawFinderPattern = function (x, y) {\n            for (var dy = -4; dy <= 4; dy++) {\n                for (var dx = -4; dx <= 4; dx++) {\n                    var dist = Math.max(Math.abs(dx), Math.abs(dy)); // Chebyshev/infinity norm\n                    var xx = x + dx;\n                    var yy = y + dy;\n                    if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size)\n                        this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n                }\n            }\n        };\n        // Draws a 5*5 alignment pattern, with the center module\n        // at (x, y). All modules must be in bounds.\n        QrCode.prototype.drawAlignmentPattern = function (x, y) {\n            for (var dy = -2; dy <= 2; dy++) {\n                for (var dx = -2; dx <= 2; dx++)\n                    this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n            }\n        };\n        // Sets the color of a module and marks it as a function module.\n        // Only used by the constructor. Coordinates must be in bounds.\n        QrCode.prototype.setFunctionModule = function (x, y, isDark) {\n            this.modules[y][x] = isDark;\n            this.isFunction[y][x] = true;\n        };\n        /*-- Private helper methods for constructor: Codewords and masking --*/\n        // Returns a new byte string representing the given data with the appropriate error correction\n        // codewords appended to it, based on this object's version and error correction level.\n        QrCode.prototype.addEccAndInterleave = function (data) {\n            var ver = this.version;\n            var ecl = this.errorCorrectionLevel;\n            if (data.length != QrCode.getNumDataCodewords(ver, ecl))\n                throw new RangeError(\"Invalid argument\");\n            // Calculate parameter numbers\n            var numBlocks = QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n            var blockEccLen = QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n            var rawCodewords = Math.floor(QrCode.getNumRawDataModules(ver) / 8);\n            var numShortBlocks = numBlocks - rawCodewords % numBlocks;\n            var shortBlockLen = Math.floor(rawCodewords / numBlocks);\n            // Split data into blocks and append ECC to each block\n            var blocks = [];\n            var rsDiv = QrCode.reedSolomonComputeDivisor(blockEccLen);\n            for (var i = 0, k = 0; i < numBlocks; i++) {\n                var dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n                k += dat.length;\n                var ecc = QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n                if (i < numShortBlocks)\n                    dat.push(0);\n                blocks.push(dat.concat(ecc));\n            }\n            // Interleave (not concatenate) the bytes from every block into a single sequence\n            var result = [];\n            var _loop_1 = function (i) {\n                blocks.forEach(function (block, j) {\n                    // Skip the padding byte in short blocks\n                    if (i != shortBlockLen - blockEccLen || j >= numShortBlocks)\n                        result.push(block[i]);\n                });\n            };\n            for (var i = 0; i < blocks[0].length; i++) {\n                _loop_1(i);\n            }\n            assert(result.length == rawCodewords);\n            return result;\n        };\n        // Draws the given sequence of 8-bit codewords (data and error correction) onto the entire\n        // data area of this QR Code. Function modules need to be marked off before this is called.\n        QrCode.prototype.drawCodewords = function (data) {\n            if (data.length != Math.floor(QrCode.getNumRawDataModules(this.version) / 8))\n                throw new RangeError(\"Invalid argument\");\n            var i = 0; // Bit index into the data\n            // Do the funny zigzag scan\n            for (var right = this.size - 1; right >= 1; right -= 2) { // Index of right column in each column pair\n                if (right == 6)\n                    right = 5;\n                for (var vert = 0; vert < this.size; vert++) { // Vertical counter\n                    for (var j = 0; j < 2; j++) {\n                        var x = right - j; // Actual x coordinate\n                        var upward = ((right + 1) & 2) == 0;\n                        var y = upward ? this.size - 1 - vert : vert; // Actual y coordinate\n                        if (!this.isFunction[y][x] && i < data.length * 8) {\n                            this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n                            i++;\n                        }\n                        // If this QR Code has any remainder bits (0 to 7), they were assigned as\n                        // 0/false/light by the constructor and are left unchanged by this method\n                    }\n                }\n            }\n            assert(i == data.length * 8);\n        };\n        // XORs the codeword modules in this QR Code with the given mask pattern.\n        // The function modules must be marked and the codeword bits must be drawn\n        // before masking. Due to the arithmetic of XOR, calling applyMask() with\n        // the same mask value a second time will undo the mask. A final well-formed\n        // QR Code needs exactly one (not zero, two, etc.) mask applied.\n        QrCode.prototype.applyMask = function (mask) {\n            if (mask < 0 || mask > 7)\n                throw new RangeError(\"Mask value out of range\");\n            for (var y = 0; y < this.size; y++) {\n                for (var x = 0; x < this.size; x++) {\n                    var invert = void 0;\n                    switch (mask) {\n                        case 0:\n                            invert = (x + y) % 2 == 0;\n                            break;\n                        case 1:\n                            invert = y % 2 == 0;\n                            break;\n                        case 2:\n                            invert = x % 3 == 0;\n                            break;\n                        case 3:\n                            invert = (x + y) % 3 == 0;\n                            break;\n                        case 4:\n                            invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n                            break;\n                        case 5:\n                            invert = x * y % 2 + x * y % 3 == 0;\n                            break;\n                        case 6:\n                            invert = (x * y % 2 + x * y % 3) % 2 == 0;\n                            break;\n                        case 7:\n                            invert = ((x + y) % 2 + x * y % 3) % 2 == 0;\n                            break;\n                        default: throw new Error(\"Unreachable\");\n                    }\n                    if (!this.isFunction[y][x] && invert)\n                        this.modules[y][x] = !this.modules[y][x];\n                }\n            }\n        };\n        // Calculates and returns the penalty score based on state of this QR Code's current modules.\n        // This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.\n        QrCode.prototype.getPenaltyScore = function () {\n            var result = 0;\n            // Adjacent modules in row having same color, and finder-like patterns\n            for (var y = 0; y < this.size; y++) {\n                var runColor = false;\n                var runX = 0;\n                var runHistory = [0, 0, 0, 0, 0, 0, 0];\n                for (var x = 0; x < this.size; x++) {\n                    if (this.modules[y][x] == runColor) {\n                        runX++;\n                        if (runX == 5)\n                            result += QrCode.PENALTY_N1;\n                        else if (runX > 5)\n                            result++;\n                    }\n                    else {\n                        this.finderPenaltyAddHistory(runX, runHistory);\n                        if (!runColor)\n                            result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;\n                        runColor = this.modules[y][x];\n                        runX = 1;\n                    }\n                }\n                result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * QrCode.PENALTY_N3;\n            }\n            // Adjacent modules in column having same color, and finder-like patterns\n            for (var x = 0; x < this.size; x++) {\n                var runColor = false;\n                var runY = 0;\n                var runHistory = [0, 0, 0, 0, 0, 0, 0];\n                for (var y = 0; y < this.size; y++) {\n                    if (this.modules[y][x] == runColor) {\n                        runY++;\n                        if (runY == 5)\n                            result += QrCode.PENALTY_N1;\n                        else if (runY > 5)\n                            result++;\n                    }\n                    else {\n                        this.finderPenaltyAddHistory(runY, runHistory);\n                        if (!runColor)\n                            result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;\n                        runColor = this.modules[y][x];\n                        runY = 1;\n                    }\n                }\n                result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * QrCode.PENALTY_N3;\n            }\n            // 2*2 blocks of modules having same color\n            for (var y = 0; y < this.size - 1; y++) {\n                for (var x = 0; x < this.size - 1; x++) {\n                    var color = this.modules[y][x];\n                    if (color == this.modules[y][x + 1] &&\n                        color == this.modules[y + 1][x] &&\n                        color == this.modules[y + 1][x + 1])\n                        result += QrCode.PENALTY_N2;\n                }\n            }\n            // Balance of dark and light modules\n            var dark = 0;\n            for (var _i = 0, _a = this.modules; _i < _a.length; _i++) {\n                var row = _a[_i];\n                dark = row.reduce(function (sum, color) { return sum + (color ? 1 : 0); }, dark);\n            }\n            var total = this.size * this.size; // Note that size is odd, so dark/total != 1/2\n            // Compute the smallest integer k >= 0 such that (45-5k)% <= dark/total <= (55+5k)%\n            var k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n            assert(0 <= k && k <= 9);\n            result += k * QrCode.PENALTY_N4;\n            assert(0 <= result && result <= 2568888); // Non-tight upper bound based on default values of PENALTY_N1, ..., N4\n            return result;\n        };\n        /*-- Private helper functions --*/\n        // Returns an ascending list of positions of alignment patterns for this version number.\n        // Each position is in the range [0,177), and are used on both the x and y axes.\n        // This could be implemented as lookup table of 40 variable-length lists of integers.\n        QrCode.prototype.getAlignmentPatternPositions = function () {\n            if (this.version == 1)\n                return [];\n            else {\n                var numAlign = Math.floor(this.version / 7) + 2;\n                var step = (this.version == 32) ? 26 :\n                    Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;\n                var result = [6];\n                for (var pos = this.size - 7; result.length < numAlign; pos -= step)\n                    result.splice(1, 0, pos);\n                return result;\n            }\n        };\n        // Returns the number of data bits that can be stored in a QR Code of the given version number, after\n        // all function modules are excluded. This includes remainder bits, so it might not be a multiple of 8.\n        // The result is in the range [208, 29648]. This could be implemented as a 40-entry lookup table.\n        QrCode.getNumRawDataModules = function (ver) {\n            if (ver < QrCode.MIN_VERSION || ver > QrCode.MAX_VERSION)\n                throw new RangeError(\"Version number out of range\");\n            var result = (16 * ver + 128) * ver + 64;\n            if (ver >= 2) {\n                var numAlign = Math.floor(ver / 7) + 2;\n                result -= (25 * numAlign - 10) * numAlign - 55;\n                if (ver >= 7)\n                    result -= 36;\n            }\n            assert(208 <= result && result <= 29648);\n            return result;\n        };\n        // Returns the number of 8-bit data (i.e. not error correction) codewords contained in any\n        // QR Code of the given version number and error correction level, with remainder bits discarded.\n        // This stateless pure function could be implemented as a (40*4)-cell lookup table.\n        QrCode.getNumDataCodewords = function (ver, ecl) {\n            return Math.floor(QrCode.getNumRawDataModules(ver) / 8) -\n                QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] *\n                    QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n        };\n        // Returns a Reed-Solomon ECC generator polynomial for the given degree. This could be\n        // implemented as a lookup table over all possible parameter values, instead of as an algorithm.\n        QrCode.reedSolomonComputeDivisor = function (degree) {\n            if (degree < 1 || degree > 255)\n                throw new RangeError(\"Degree out of range\");\n            // Polynomial coefficients are stored from highest to lowest power, excluding the leading term which is always 1.\n            // For example the polynomial x^3 + 255x^2 + 8x + 93 is stored as the uint8 array [255, 8, 93].\n            var result = [];\n            for (var i = 0; i < degree - 1; i++)\n                result.push(0);\n            result.push(1); // Start off with the monomial x^0\n            // Compute the product polynomial (x - r^0) * (x - r^1) * (x - r^2) * ... * (x - r^{degree-1}),\n            // and drop the highest monomial term which is always 1x^degree.\n            // Note that r = 0x02, which is a generator element of this field GF(2^8/0x11D).\n            var root = 1;\n            for (var i = 0; i < degree; i++) {\n                // Multiply the current product by (x - r^i)\n                for (var j = 0; j < result.length; j++) {\n                    result[j] = QrCode.reedSolomonMultiply(result[j], root);\n                    if (j + 1 < result.length)\n                        result[j] ^= result[j + 1];\n                }\n                root = QrCode.reedSolomonMultiply(root, 0x02);\n            }\n            return result;\n        };\n        // Returns the Reed-Solomon error correction codeword for the given data and divisor polynomials.\n        QrCode.reedSolomonComputeRemainder = function (data, divisor) {\n            var result = divisor.map(function (_) { return 0; });\n            var _loop_2 = function (b) {\n                var factor = b ^ result.shift();\n                result.push(0);\n                divisor.forEach(function (coef, i) {\n                    return result[i] ^= QrCode.reedSolomonMultiply(coef, factor);\n                });\n            };\n            for (var _i = 0, data_1 = data; _i < data_1.length; _i++) {\n                var b = data_1[_i];\n                _loop_2(b);\n            }\n            return result;\n        };\n        // Returns the product of the two given field elements modulo GF(2^8/0x11D). The arguments and result\n        // are unsigned 8-bit integers. This could be implemented as a lookup table of 256*256 entries of uint8.\n        QrCode.reedSolomonMultiply = function (x, y) {\n            if (x >>> 8 != 0 || y >>> 8 != 0)\n                throw new RangeError(\"Byte out of range\");\n            // Russian peasant multiplication\n            var z = 0;\n            for (var i = 7; i >= 0; i--) {\n                z = (z << 1) ^ ((z >>> 7) * 0x11D);\n                z ^= ((y >>> i) & 1) * x;\n            }\n            assert(z >>> 8 == 0);\n            return z;\n        };\n        // Can only be called immediately after a light run is added, and\n        // returns either 0, 1, or 2. A helper function for getPenaltyScore().\n        QrCode.prototype.finderPenaltyCountPatterns = function (runHistory) {\n            var n = runHistory[1];\n            assert(n <= this.size * 3);\n            var core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n            return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0)\n                + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n        };\n        // Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().\n        QrCode.prototype.finderPenaltyTerminateAndCount = function (currentRunColor, currentRunLength, runHistory) {\n            if (currentRunColor) { // Terminate dark run\n                this.finderPenaltyAddHistory(currentRunLength, runHistory);\n                currentRunLength = 0;\n            }\n            currentRunLength += this.size; // Add light border to final run\n            this.finderPenaltyAddHistory(currentRunLength, runHistory);\n            return this.finderPenaltyCountPatterns(runHistory);\n        };\n        // Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().\n        QrCode.prototype.finderPenaltyAddHistory = function (currentRunLength, runHistory) {\n            if (runHistory[0] == 0)\n                currentRunLength += this.size; // Add light border to initial run\n            runHistory.pop();\n            runHistory.unshift(currentRunLength);\n        };\n        /*-- Constants and tables --*/\n        // The minimum version number supported in the QR Code Model 2 standard.\n        QrCode.MIN_VERSION = 1;\n        // The maximum version number supported in the QR Code Model 2 standard.\n        QrCode.MAX_VERSION = 40;\n        // For use in getPenaltyScore(), when evaluating which mask is best.\n        QrCode.PENALTY_N1 = 3;\n        QrCode.PENALTY_N2 = 3;\n        QrCode.PENALTY_N3 = 40;\n        QrCode.PENALTY_N4 = 10;\n        QrCode.ECC_CODEWORDS_PER_BLOCK = [\n            // Version: (note that index 0 is for padding, and is set to an illegal value)\n            //0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n            [-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n            [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n            [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n            [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], // High\n        ];\n        QrCode.NUM_ERROR_CORRECTION_BLOCKS = [\n            // Version: (note that index 0 is for padding, and is set to an illegal value)\n            //0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n            [-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n            [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n            [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n            [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81], // High\n        ];\n        return QrCode;\n    }());\n    qrcodegen.QrCode = QrCode;\n    // Appends the given number of low-order bits of the given value\n    // to the given buffer. Requires 0 <= len <= 31 and 0 <= val < 2^len.\n    function appendBits(val, len, bb) {\n        if (len < 0 || len > 31 || val >>> len != 0)\n            throw new RangeError(\"Value out of range\");\n        for (var i = len - 1; i >= 0; i--) // Append bit by bit\n            bb.push((val >>> i) & 1);\n    }\n    // Returns true iff the i'th bit of x is set to 1.\n    function getBit(x, i) {\n        return ((x >>> i) & 1) != 0;\n    }\n    // Throws an exception if the given condition is false.\n    function assert(cond) {\n        if (!cond)\n            throw new Error(\"Assertion error\");\n    }\n    /*---- Data segment class ----*/\n    /*\n     * A segment of character/binary/control data in a QR Code symbol.\n     * Instances of this class are immutable.\n     * The mid-level way to create a segment is to take the payload data\n     * and call a static factory function such as QrSegment.makeNumeric().\n     * The low-level way to create a segment is to custom-make the bit buffer\n     * and call the QrSegment() constructor with appropriate values.\n     * This segment class imposes no length restrictions, but QR Codes have restrictions.\n     * Even in the most favorable conditions, a QR Code can only hold 7089 characters of data.\n     * Any segment longer than this is meaningless for the purpose of generating QR Codes.\n     */\n    var QrSegment = /** @class */ (function () {\n        /*-- Constructor (low level) and fields --*/\n        // Creates a new QR Code segment with the given attributes and data.\n        // The character count (numChars) must agree with the mode and the bit buffer length,\n        // but the constraint isn't checked. The given bit buffer is cloned and stored.\n        function QrSegment(\n        // The mode indicator of this segment.\n        mode, \n        // The length of this segment's unencoded data. Measured in characters for\n        // numeric/alphanumeric/kanji mode, bytes for byte mode, and 0 for ECI mode.\n        // Always zero or positive. Not the same as the data's bit length.\n        numChars, \n        // The data bits of this segment. Accessed through getData().\n        bitData) {\n            this.mode = mode;\n            this.numChars = numChars;\n            this.bitData = bitData;\n            if (numChars < 0)\n                throw new RangeError(\"Invalid argument\");\n            this.bitData = bitData.slice(); // Make defensive copy\n        }\n        /*-- Static factory functions (mid level) --*/\n        // Returns a segment representing the given binary data encoded in\n        // byte mode. All input byte arrays are acceptable. Any text string\n        // can be converted to UTF-8 bytes and encoded as a byte mode segment.\n        QrSegment.makeBytes = function (data) {\n            var bb = [];\n            for (var _i = 0, data_2 = data; _i < data_2.length; _i++) {\n                var b = data_2[_i];\n                appendBits(b, 8, bb);\n            }\n            return new QrSegment(QrSegment.Mode.BYTE, data.length, bb);\n        };\n        // Returns a segment representing the given string of decimal digits encoded in numeric mode.\n        QrSegment.makeNumeric = function (digits) {\n            if (!QrSegment.isNumeric(digits))\n                throw new RangeError(\"String contains non-numeric characters\");\n            var bb = [];\n            for (var i = 0; i < digits.length;) { // Consume up to 3 digits per iteration\n                var n = Math.min(digits.length - i, 3);\n                appendBits(parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);\n                i += n;\n            }\n            return new QrSegment(QrSegment.Mode.NUMERIC, digits.length, bb);\n        };\n        // Returns a segment representing the given text string encoded in alphanumeric mode.\n        // The characters allowed are: 0 to 9, A to Z (uppercase only), space,\n        // dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n        QrSegment.makeAlphanumeric = function (text) {\n            if (!QrSegment.isAlphanumeric(text))\n                throw new RangeError(\"String contains unencodable characters in alphanumeric mode\");\n            var bb = [];\n            var i;\n            for (i = 0; i + 2 <= text.length; i += 2) { // Process groups of 2\n                var temp = QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n                temp += QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n                appendBits(temp, 11, bb);\n            }\n            if (i < text.length) // 1 character remaining\n                appendBits(QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n            return new QrSegment(QrSegment.Mode.ALPHANUMERIC, text.length, bb);\n        };\n        // Returns a new mutable list of zero or more segments to represent the given Unicode text string.\n        // The result may use various segment modes and switch modes to optimize the length of the bit stream.\n        QrSegment.makeSegments = function (text) {\n            // Select the most efficient segment encoding automatically\n            if (text == \"\")\n                return [];\n            else if (QrSegment.isNumeric(text))\n                return [QrSegment.makeNumeric(text)];\n            else if (QrSegment.isAlphanumeric(text))\n                return [QrSegment.makeAlphanumeric(text)];\n            else\n                return [QrSegment.makeBytes(QrSegment.toUtf8ByteArray(text))];\n        };\n        // Returns a segment representing an Extended Channel Interpretation\n        // (ECI) designator with the given assignment value.\n        QrSegment.makeEci = function (assignVal) {\n            var bb = [];\n            if (assignVal < 0)\n                throw new RangeError(\"ECI assignment value out of range\");\n            else if (assignVal < (1 << 7))\n                appendBits(assignVal, 8, bb);\n            else if (assignVal < (1 << 14)) {\n                appendBits(2, 2, bb);\n                appendBits(assignVal, 14, bb);\n            }\n            else if (assignVal < 1000000) {\n                appendBits(6, 3, bb);\n                appendBits(assignVal, 21, bb);\n            }\n            else\n                throw new RangeError(\"ECI assignment value out of range\");\n            return new QrSegment(QrSegment.Mode.ECI, 0, bb);\n        };\n        // Tests whether the given string can be encoded as a segment in numeric mode.\n        // A string is encodable iff each character is in the range 0 to 9.\n        QrSegment.isNumeric = function (text) {\n            return QrSegment.NUMERIC_REGEX.test(text);\n        };\n        // Tests whether the given string can be encoded as a segment in alphanumeric mode.\n        // A string is encodable iff each character is in the following set: 0 to 9, A to Z\n        // (uppercase only), space, dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n        QrSegment.isAlphanumeric = function (text) {\n            return QrSegment.ALPHANUMERIC_REGEX.test(text);\n        };\n        /*-- Methods --*/\n        // Returns a new copy of the data bits of this segment.\n        QrSegment.prototype.getData = function () {\n            return this.bitData.slice(); // Make defensive copy\n        };\n        // (Package-private) Calculates and returns the number of bits needed to encode the given segments at\n        // the given version. The result is infinity if a segment has too many characters to fit its length field.\n        QrSegment.getTotalBits = function (segs, version) {\n            var result = 0;\n            for (var _i = 0, segs_2 = segs; _i < segs_2.length; _i++) {\n                var seg = segs_2[_i];\n                var ccbits = seg.mode.numCharCountBits(version);\n                if (seg.numChars >= (1 << ccbits))\n                    return Infinity; // The segment's length doesn't fit the field's bit width\n                result += 4 + ccbits + seg.bitData.length;\n            }\n            return result;\n        };\n        // Returns a new array of bytes representing the given string encoded in UTF-8.\n        QrSegment.toUtf8ByteArray = function (str) {\n            str = encodeURI(str);\n            var result = [];\n            for (var i = 0; i < str.length; i++) {\n                if (str.charAt(i) != \"%\")\n                    result.push(str.charCodeAt(i));\n                else {\n                    result.push(parseInt(str.substring(i + 1, i + 3), 16));\n                    i += 2;\n                }\n            }\n            return result;\n        };\n        /*-- Constants --*/\n        // Describes precisely all strings that are encodable in numeric mode.\n        QrSegment.NUMERIC_REGEX = /^[0-9]*$/;\n        // Describes precisely all strings that are encodable in alphanumeric mode.\n        QrSegment.ALPHANUMERIC_REGEX = /^[A-Z0-9 $%*+.\\/:-]*$/;\n        // The set of all legal characters in alphanumeric mode,\n        // where each character value maps to the index in the string.\n        QrSegment.ALPHANUMERIC_CHARSET = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:\";\n        return QrSegment;\n    }());\n    qrcodegen.QrSegment = QrSegment;\n})(qrcodegen || (qrcodegen = {}));\n/*---- Public helper enumeration ----*/\n(function (qrcodegen) {\n    (function (QrCode) {\n        /*\n         * The error correction level in a QR Code symbol. Immutable.\n         */\n        var Ecc = /** @class */ (function () {\n            /*-- Constructor and fields --*/\n            function Ecc(\n            // In the range 0 to 3 (unsigned 2-bit integer).\n            ordinal, \n            // (Package-private) In the range 0 to 3 (unsigned 2-bit integer).\n            formatBits) {\n                this.ordinal = ordinal;\n                this.formatBits = formatBits;\n            }\n            /*-- Constants --*/\n            Ecc.LOW = new Ecc(0, 1); // The QR Code can tolerate about  7% erroneous codewords\n            Ecc.MEDIUM = new Ecc(1, 0); // The QR Code can tolerate about 15% erroneous codewords\n            Ecc.QUARTILE = new Ecc(2, 3); // The QR Code can tolerate about 25% erroneous codewords\n            Ecc.HIGH = new Ecc(3, 2); // The QR Code can tolerate about 30% erroneous codewords\n            return Ecc;\n        }());\n        QrCode.Ecc = Ecc;\n    })(qrcodegen.QrCode || (qrcodegen.QrCode = {}));\n})(qrcodegen || (qrcodegen = {}));\n/*---- Public helper enumeration ----*/\n(function (qrcodegen) {\n    (function (QrSegment) {\n        /*\n         * Describes how a segment's data bits are interpreted. Immutable.\n         */\n        var Mode = /** @class */ (function () {\n            /*-- Constructor and fields --*/\n            function Mode(\n            // The mode indicator bits, which is a uint4 value (range 0 to 15).\n            modeBits, \n            // Number of character count bits for three different version ranges.\n            numBitsCharCount) {\n                this.modeBits = modeBits;\n                this.numBitsCharCount = numBitsCharCount;\n            }\n            /*-- Method --*/\n            // (Package-private) Returns the bit width of the character count field for a segment in\n            // this mode in a QR Code at the given version number. The result is in the range [0, 16].\n            Mode.prototype.numCharCountBits = function (ver) {\n                return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n            };\n            /*-- Constants --*/\n            Mode.NUMERIC = new Mode(0x1, [10, 12, 14]);\n            Mode.ALPHANUMERIC = new Mode(0x2, [9, 11, 13]);\n            Mode.BYTE = new Mode(0x4, [8, 16, 16]);\n            Mode.KANJI = new Mode(0x8, [8, 10, 12]);\n            Mode.ECI = new Mode(0x7, [0, 0, 0]);\n            return Mode;\n        }());\n        QrSegment.Mode = Mode;\n    })(qrcodegen.QrSegment || (qrcodegen.QrSegment = {}));\n})(qrcodegen || (qrcodegen = {}));\nvar QR = qrcodegen;\n\nvar defaultErrorCorrectLevel = 'H';\nvar ErrorCorrectLevelMap = {\n    L: QR.QrCode.Ecc.LOW,\n    M: QR.QrCode.Ecc.MEDIUM,\n    Q: QR.QrCode.Ecc.QUARTILE,\n    H: QR.QrCode.Ecc.HIGH,\n};\n// Thanks the `qrcode.react`\nvar SUPPORTS_PATH2D = (function () {\n    try {\n        new Path2D().addPath(new Path2D());\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n})();\nfunction validErrorCorrectLevel(level) {\n    return level in ErrorCorrectLevelMap;\n}\nfunction generatePath(modules, margin) {\n    if (margin === void 0) { margin = 0; }\n    var ops = [];\n    modules.forEach(function (row, y) {\n        var start = null;\n        row.forEach(function (cell, x) {\n            if (!cell && start !== null) {\n                // M0 0h7v1H0z injects the space with the move and drops the comma,\n                // saving a char per operation\n                ops.push(\"M\".concat(start + margin, \" \").concat(y + margin, \"h\").concat(x - start, \"v1H\").concat(start + margin, \"z\"));\n                start = null;\n                return;\n            }\n            // end of row, clean up or skip\n            if (x === row.length - 1) {\n                if (!cell) {\n                    // We would have closed the op above already so this can only mean\n                    // 2+ light modules in a row.\n                    return;\n                }\n                if (start === null) {\n                    // Just a single dark module.\n                    ops.push(\"M\".concat(x + margin, \",\").concat(y + margin, \" h1v1H\").concat(x + margin, \"z\"));\n                }\n                else {\n                    // Otherwise finish the current line.\n                    ops.push(\"M\".concat(start + margin, \",\").concat(y + margin, \" h\").concat(x + 1 - start, \"v1H\").concat(start + margin, \"z\"));\n                }\n                return;\n            }\n            if (cell && start === null) {\n                start = x;\n            }\n        });\n    });\n    return ops.join('');\n}\nvar QRCodeProps = {\n    value: {\n        type: String,\n        required: true,\n        default: '',\n    },\n    size: {\n        type: Number,\n        default: 100,\n    },\n    level: {\n        type: String,\n        default: defaultErrorCorrectLevel,\n        validator: function (l) { return validErrorCorrectLevel(l); },\n    },\n    background: {\n        type: String,\n        default: '#fff',\n    },\n    foreground: {\n        type: String,\n        default: '#000',\n    },\n    margin: {\n        type: Number,\n        required: false,\n        default: 0,\n    },\n};\nvar QRCodeVueProps = __assign(__assign({}, QRCodeProps), { renderAs: {\n        type: String,\n        required: false,\n        default: 'canvas',\n        validator: function (as) { return ['canvas', 'svg'].indexOf(as) > -1; },\n    } });\nvar QRCodeSvg = defineComponent({\n    name: 'QRCodeSvg',\n    props: QRCodeProps,\n    setup: function (props) {\n        var numCells = ref(0);\n        var fgPath = ref('');\n        var generate = function () {\n            var value = props.value, level = props.level, margin = props.margin;\n            var cells = QR.QrCode.encodeText(value, ErrorCorrectLevelMap[level]).getModules();\n            numCells.value = cells.length + margin * 2;\n            // Drawing strategy: instead of a rect per module, we're going to create a\n            // single path for the dark modules and layer that on top of a light rect,\n            // for a total of 2 DOM nodes. We pay a bit more in string concat but that's\n            // way faster than DOM ops.\n            // For level 1, 441 nodes -> 2\n            // For level 40, 31329 -> 2\n            fgPath.value = generatePath(cells, margin);\n        };\n        generate();\n        onUpdated(generate);\n        return function () { return h('svg', {\n            width: props.size,\n            height: props.size,\n            'shape-rendering': \"crispEdges\",\n            xmlns: 'http://www.w3.org/2000/svg',\n            viewBox: \"0 0 \".concat(numCells.value, \" \").concat(numCells.value),\n        }, [\n            h('path', {\n                fill: props.background,\n                d: \"M0,0 h\".concat(numCells.value, \"v\").concat(numCells.value, \"H0z\"),\n            }),\n            h('path', { fill: props.foreground, d: fgPath.value }),\n        ]); };\n    },\n});\nvar QRCodeCanvas = defineComponent({\n    name: 'QRCodeCanvas',\n    props: QRCodeProps,\n    setup: function (props) {\n        var canvasEl = ref(null);\n        var generate = function () {\n            var value = props.value, level = props.level, size = props.size, margin = props.margin, background = props.background, foreground = props.foreground;\n            var canvas = canvasEl.value;\n            if (!canvas) {\n                return;\n            }\n            var ctx = canvas.getContext('2d');\n            if (!ctx) {\n                return;\n            }\n            var cells = QR.QrCode.encodeText(value, ErrorCorrectLevelMap[level]).getModules();\n            var numCells = cells.length + margin * 2;\n            var devicePixelRatio = window.devicePixelRatio || 1;\n            var scale = (size / numCells) * devicePixelRatio;\n            canvas.height = canvas.width = size * devicePixelRatio;\n            ctx.scale(scale, scale);\n            ctx.fillStyle = background;\n            ctx.fillRect(0, 0, numCells, numCells);\n            ctx.fillStyle = foreground;\n            if (SUPPORTS_PATH2D) {\n                ctx.fill(new Path2D(generatePath(cells, margin)));\n            }\n            else {\n                cells.forEach(function (row, rdx) {\n                    row.forEach(function (cell, cdx) {\n                        if (cell) {\n                            ctx.fillRect(cdx + margin, rdx + margin, 1, 1);\n                        }\n                    });\n                });\n            }\n        };\n        onMounted(generate);\n        onUpdated(generate);\n        return function () { return h('canvas', {\n            ref: canvasEl,\n            style: { width: \"\".concat(props.size, \"px\"), height: \"\".concat(props.size, \"px\") },\n        }); };\n    },\n});\nvar QrcodeVue = defineComponent({\n    name: 'Qrcode',\n    render: function () {\n        var _a = this.$props, renderAs = _a.renderAs, value = _a.value, _size = _a.size, _margin = _a.margin, _level = _a.level, background = _a.background, foreground = _a.foreground;\n        var size = _size >>> 0;\n        var margin = _margin >>> 0;\n        var level = validErrorCorrectLevel(_level) ? _level : defaultErrorCorrectLevel;\n        return h(renderAs === 'svg' ? QRCodeSvg : QRCodeCanvas, { value: value, size: size, margin: margin, level: level, background: background, foreground: foreground });\n    },\n    props: QRCodeVueProps,\n});\n\nexport { QrcodeVue as default };\n"], "mappings": ";;;;;;;;;;;AAyBA,IAAI,WAAW,WAAW;AACtB,aAAW,OAAO,UAAU,SAASA,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AA6BA,IAAI;AAAA,CACH,SAAUC,YAAW;AAkBlB,MAAI;AAAA;AAAA,IAAwB,WAAY;AAMpC,eAASC,QAGT,SAEA,sBAAsB,eAAe,KAAK;AACtC,aAAK,UAAU;AACf,aAAK,uBAAuB;AAG5B,aAAK,UAAU,CAAC;AAEhB,aAAK,aAAa,CAAC;AAEnB,YAAI,UAAUA,QAAO,eAAe,UAAUA,QAAO;AACjD,gBAAM,IAAI,WAAW,4BAA4B;AACrD,YAAI,MAAM,MAAM,MAAM;AAClB,gBAAM,IAAI,WAAW,yBAAyB;AAClD,aAAK,OAAO,UAAU,IAAI;AAE1B,YAAI,MAAM,CAAC;AACX,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM;AAC3B,cAAI,KAAK,KAAK;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAChC,eAAK,QAAQ,KAAK,IAAI,MAAM,CAAC;AAC7B,eAAK,WAAW,KAAK,IAAI,MAAM,CAAC;AAAA,QACpC;AAEA,aAAK,qBAAqB;AAC1B,YAAI,eAAe,KAAK,oBAAoB,aAAa;AACzD,aAAK,cAAc,YAAY;AAE/B,YAAI,OAAO,IAAI;AACX,cAAI,aAAa;AACjB,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,iBAAK,UAAU,CAAC;AAChB,iBAAK,eAAe,CAAC;AACrB,gBAAI,UAAU,KAAK,gBAAgB;AACnC,gBAAI,UAAU,YAAY;AACtB,oBAAM;AACN,2BAAa;AAAA,YACjB;AACA,iBAAK,UAAU,CAAC;AAAA,UACpB;AAAA,QACJ;AACA,eAAO,KAAK,OAAO,OAAO,CAAC;AAC3B,aAAK,OAAO;AACZ,aAAK,UAAU,GAAG;AAClB,aAAK,eAAe,GAAG;AACvB,aAAK,aAAa,CAAC;AAAA,MACvB;AAOA,MAAAA,QAAO,aAAa,SAAU,MAAM,KAAK;AACrC,YAAI,OAAOD,WAAU,UAAU,aAAa,IAAI;AAChD,eAAOC,QAAO,eAAe,MAAM,GAAG;AAAA,MAC1C;AAKA,MAAAA,QAAO,eAAe,SAAU,MAAM,KAAK;AACvC,YAAI,MAAMD,WAAU,UAAU,UAAU,IAAI;AAC5C,eAAOC,QAAO,eAAe,CAAC,GAAG,GAAG,GAAG;AAAA,MAC3C;AAWA,MAAAA,QAAO,iBAAiB,SAAU,MAAM,KAAK,YAAY,YAAY,MAAM,UAAU;AACjF,YAAI,eAAe,QAAQ;AAAE,uBAAa;AAAA,QAAG;AAC7C,YAAI,eAAe,QAAQ;AAAE,uBAAa;AAAA,QAAI;AAC9C,YAAI,SAAS,QAAQ;AAAE,iBAAO;AAAA,QAAI;AAClC,YAAI,aAAa,QAAQ;AAAE,qBAAW;AAAA,QAAM;AAC5C,YAAI,EAAEA,QAAO,eAAe,cAAc,cAAc,cAAc,cAAcA,QAAO,gBACpF,OAAO,MAAM,OAAO;AACvB,gBAAM,IAAI,WAAW,eAAe;AAExC,YAAI;AACJ,YAAI;AACJ,aAAK,UAAU,cAAa,WAAW;AACnC,cAAI,qBAAqBA,QAAO,oBAAoB,SAAS,GAAG,IAAI;AACpE,cAAI,WAAW,UAAU,aAAa,MAAM,OAAO;AACnD,cAAI,YAAY,oBAAoB;AAChC,2BAAe;AACf;AAAA,UACJ;AACA,cAAI,WAAW;AACX,kBAAM,IAAI,WAAW,eAAe;AAAA,QAC5C;AAEA,iBAAS,KAAK,GAAG,KAAK,CAACA,QAAO,IAAI,QAAQA,QAAO,IAAI,UAAUA,QAAO,IAAI,IAAI,GAAG,KAAK,GAAG,QAAQ,MAAM;AACnG,cAAI,SAAS,GAAG,EAAE;AAClB,cAAI,YAAY,gBAAgBA,QAAO,oBAAoB,SAAS,MAAM,IAAI;AAC1E,kBAAM;AAAA,QACd;AAEA,YAAI,KAAK,CAAC;AACV,iBAAS,KAAK,GAAG,SAAS,MAAM,KAAK,OAAO,QAAQ,MAAM;AACtD,cAAI,MAAM,OAAO,EAAE;AACnB,qBAAW,IAAI,KAAK,UAAU,GAAG,EAAE;AACnC,qBAAW,IAAI,UAAU,IAAI,KAAK,iBAAiB,OAAO,GAAG,EAAE;AAC/D,mBAAS,KAAK,GAAG,KAAK,IAAI,QAAQ,GAAG,KAAK,GAAG,QAAQ,MAAM;AACvD,gBAAI,IAAI,GAAG,EAAE;AACb,eAAG,KAAK,CAAC;AAAA,UACb;AAAA,QACJ;AACA,eAAO,GAAG,UAAU,YAAY;AAEhC,YAAI,mBAAmBA,QAAO,oBAAoB,SAAS,GAAG,IAAI;AAClE,eAAO,GAAG,UAAU,gBAAgB;AACpC,mBAAW,GAAG,KAAK,IAAI,GAAG,mBAAmB,GAAG,MAAM,GAAG,EAAE;AAC3D,mBAAW,IAAI,IAAI,GAAG,SAAS,KAAK,GAAG,EAAE;AACzC,eAAO,GAAG,SAAS,KAAK,CAAC;AAEzB,iBAAS,UAAU,KAAM,GAAG,SAAS,kBAAkB,WAAW,MAAO;AACrE,qBAAW,SAAS,GAAG,EAAE;AAE7B,YAAI,gBAAgB,CAAC;AACrB,eAAO,cAAc,SAAS,IAAI,GAAG;AACjC,wBAAc,KAAK,CAAC;AACxB,WAAG,QAAQ,SAAUC,IAAG,GAAG;AACvB,iBAAO,cAAc,MAAM,CAAC,KAAKA,MAAM,KAAK,IAAI;AAAA,QACpD,CAAC;AAED,eAAO,IAAID,QAAO,SAAS,KAAK,eAAe,IAAI;AAAA,MACvD;AAKA,MAAAA,QAAO,UAAU,YAAY,SAAU,GAAG,GAAG;AACzC,eAAO,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,QAAQ,CAAC,EAAE,CAAC;AAAA,MAClF;AACA,MAAAA,QAAO,UAAU,aAAa,WAAY;AACtC,eAAO,KAAK;AAAA,MAChB;AAGA,MAAAA,QAAO,UAAU,uBAAuB,WAAY;AAEhD,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAChC,eAAK,kBAAkB,GAAG,GAAG,IAAI,KAAK,CAAC;AACvC,eAAK,kBAAkB,GAAG,GAAG,IAAI,KAAK,CAAC;AAAA,QAC3C;AAEA,aAAK,kBAAkB,GAAG,CAAC;AAC3B,aAAK,kBAAkB,KAAK,OAAO,GAAG,CAAC;AACvC,aAAK,kBAAkB,GAAG,KAAK,OAAO,CAAC;AAEvC,YAAI,cAAc,KAAK,6BAA6B;AACpD,YAAI,WAAW,YAAY;AAC3B,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAC/B,mBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAE/B,gBAAI,EAAE,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,WAAW,KAAK,KAAK;AAC/E,mBAAK,qBAAqB,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAAA,UAChE;AAAA,QACJ;AAEA,aAAK,eAAe,CAAC;AACrB,aAAK,YAAY;AAAA,MACrB;AAGA,MAAAA,QAAO,UAAU,iBAAiB,SAAU,MAAM;AAE9C,YAAI,OAAO,KAAK,qBAAqB,cAAc,IAAI;AACvD,YAAI,MAAM;AACV,iBAAS,IAAI,GAAG,IAAI,IAAI;AACpB,gBAAO,OAAO,KAAO,QAAQ,KAAK;AACtC,YAAI,QAAQ,QAAQ,KAAK,OAAO;AAChC,eAAO,SAAS,MAAM,CAAC;AAEvB,iBAAS,IAAI,GAAG,KAAK,GAAG;AACpB,eAAK,kBAAkB,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAChD,aAAK,kBAAkB,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAC5C,aAAK,kBAAkB,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAC5C,aAAK,kBAAkB,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAC5C,iBAAS,IAAI,GAAG,IAAI,IAAI;AACpB,eAAK,kBAAkB,KAAK,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAErD,iBAAS,IAAI,GAAG,IAAI,GAAG;AACnB,eAAK,kBAAkB,KAAK,OAAO,IAAI,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAChE,iBAAS,IAAI,GAAG,IAAI,IAAI;AACpB,eAAK,kBAAkB,GAAG,KAAK,OAAO,KAAK,GAAG,OAAO,MAAM,CAAC,CAAC;AACjE,aAAK,kBAAkB,GAAG,KAAK,OAAO,GAAG,IAAI;AAAA,MACjD;AAGA,MAAAA,QAAO,UAAU,cAAc,WAAY;AACvC,YAAI,KAAK,UAAU;AACf;AAEJ,YAAI,MAAM,KAAK;AACf,iBAAS,IAAI,GAAG,IAAI,IAAI;AACpB,gBAAO,OAAO,KAAO,QAAQ,MAAM;AACvC,YAAI,OAAO,KAAK,WAAW,KAAK;AAChC,eAAO,SAAS,MAAM,CAAC;AAEvB,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,cAAI,QAAQ,OAAO,MAAM,CAAC;AAC1B,cAAI,IAAI,KAAK,OAAO,KAAK,IAAI;AAC7B,cAAI,IAAI,KAAK,MAAM,IAAI,CAAC;AACxB,eAAK,kBAAkB,GAAG,GAAG,KAAK;AAClC,eAAK,kBAAkB,GAAG,GAAG,KAAK;AAAA,QACtC;AAAA,MACJ;AAGA,MAAAA,QAAO,UAAU,oBAAoB,SAAU,GAAG,GAAG;AACjD,iBAAS,KAAK,IAAI,MAAM,GAAG,MAAM;AAC7B,mBAAS,KAAK,IAAI,MAAM,GAAG,MAAM;AAC7B,gBAAI,OAAO,KAAK,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AAC9C,gBAAI,KAAK,IAAI;AACb,gBAAI,KAAK,IAAI;AACb,gBAAI,KAAK,MAAM,KAAK,KAAK,QAAQ,KAAK,MAAM,KAAK,KAAK;AAClD,mBAAK,kBAAkB,IAAI,IAAI,QAAQ,KAAK,QAAQ,CAAC;AAAA,UAC7D;AAAA,QACJ;AAAA,MACJ;AAGA,MAAAA,QAAO,UAAU,uBAAuB,SAAU,GAAG,GAAG;AACpD,iBAAS,KAAK,IAAI,MAAM,GAAG,MAAM;AAC7B,mBAAS,KAAK,IAAI,MAAM,GAAG;AACvB,iBAAK,kBAAkB,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC;AAAA,QACxF;AAAA,MACJ;AAGA,MAAAA,QAAO,UAAU,oBAAoB,SAAU,GAAG,GAAG,QAAQ;AACzD,aAAK,QAAQ,CAAC,EAAE,CAAC,IAAI;AACrB,aAAK,WAAW,CAAC,EAAE,CAAC,IAAI;AAAA,MAC5B;AAIA,MAAAA,QAAO,UAAU,sBAAsB,SAAU,MAAM;AACnD,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,KAAK;AACf,YAAI,KAAK,UAAUA,QAAO,oBAAoB,KAAK,GAAG;AAClD,gBAAM,IAAI,WAAW,kBAAkB;AAE3C,YAAI,YAAYA,QAAO,4BAA4B,IAAI,OAAO,EAAE,GAAG;AACnE,YAAI,cAAcA,QAAO,wBAAwB,IAAI,OAAO,EAAE,GAAG;AACjE,YAAI,eAAe,KAAK,MAAMA,QAAO,qBAAqB,GAAG,IAAI,CAAC;AAClE,YAAI,iBAAiB,YAAY,eAAe;AAChD,YAAI,gBAAgB,KAAK,MAAM,eAAe,SAAS;AAEvD,YAAI,SAAS,CAAC;AACd,YAAI,QAAQA,QAAO,0BAA0B,WAAW;AACxD,iBAAS,IAAI,GAAG,IAAI,GAAG,IAAI,WAAW,KAAK;AACvC,cAAI,MAAM,KAAK,MAAM,GAAG,IAAI,gBAAgB,eAAe,IAAI,iBAAiB,IAAI,EAAE;AACtF,eAAK,IAAI;AACT,cAAI,MAAMA,QAAO,4BAA4B,KAAK,KAAK;AACvD,cAAI,IAAI;AACJ,gBAAI,KAAK,CAAC;AACd,iBAAO,KAAK,IAAI,OAAO,GAAG,CAAC;AAAA,QAC/B;AAEA,YAAI,SAAS,CAAC;AACd,YAAI,UAAU,SAAUE,IAAG;AACvB,iBAAO,QAAQ,SAAU,OAAO,GAAG;AAE/B,gBAAIA,MAAK,gBAAgB,eAAe,KAAK;AACzC,qBAAO,KAAK,MAAMA,EAAC,CAAC;AAAA,UAC5B,CAAC;AAAA,QACL;AACA,iBAAS,IAAI,GAAG,IAAI,OAAO,CAAC,EAAE,QAAQ,KAAK;AACvC,kBAAQ,CAAC;AAAA,QACb;AACA,eAAO,OAAO,UAAU,YAAY;AACpC,eAAO;AAAA,MACX;AAGA,MAAAF,QAAO,UAAU,gBAAgB,SAAU,MAAM;AAC7C,YAAI,KAAK,UAAU,KAAK,MAAMA,QAAO,qBAAqB,KAAK,OAAO,IAAI,CAAC;AACvE,gBAAM,IAAI,WAAW,kBAAkB;AAC3C,YAAI,IAAI;AAER,iBAAS,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS,GAAG;AACpD,cAAI,SAAS;AACT,oBAAQ;AACZ,mBAAS,OAAO,GAAG,OAAO,KAAK,MAAM,QAAQ;AACzC,qBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,kBAAI,IAAI,QAAQ;AAChB,kBAAI,UAAW,QAAQ,IAAK,MAAM;AAClC,kBAAI,IAAI,SAAS,KAAK,OAAO,IAAI,OAAO;AACxC,kBAAI,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK,SAAS,GAAG;AAC/C,qBAAK,QAAQ,CAAC,EAAE,CAAC,IAAI,OAAO,KAAK,MAAM,CAAC,GAAG,KAAK,IAAI,EAAE;AACtD;AAAA,cACJ;AAAA,YAGJ;AAAA,UACJ;AAAA,QACJ;AACA,eAAO,KAAK,KAAK,SAAS,CAAC;AAAA,MAC/B;AAMA,MAAAA,QAAO,UAAU,YAAY,SAAU,MAAM;AACzC,YAAI,OAAO,KAAK,OAAO;AACnB,gBAAM,IAAI,WAAW,yBAAyB;AAClD,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAChC,mBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAChC,gBAAI,SAAS;AACb,oBAAQ,MAAM;AAAA,cACV,KAAK;AACD,0BAAU,IAAI,KAAK,KAAK;AACxB;AAAA,cACJ,KAAK;AACD,yBAAS,IAAI,KAAK;AAClB;AAAA,cACJ,KAAK;AACD,yBAAS,IAAI,KAAK;AAClB;AAAA,cACJ,KAAK;AACD,0BAAU,IAAI,KAAK,KAAK;AACxB;AAAA,cACJ,KAAK;AACD,0BAAU,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,KAAK;AACxD;AAAA,cACJ,KAAK;AACD,yBAAS,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AAClC;AAAA,cACJ,KAAK;AACD,0BAAU,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK;AACxC;AAAA,cACJ,KAAK;AACD,2BAAW,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK;AAC1C;AAAA,cACJ;AAAS,sBAAM,IAAI,MAAM,aAAa;AAAA,YAC1C;AACA,gBAAI,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,KAAK;AAC1B,mBAAK,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,QAAQ,CAAC,EAAE,CAAC;AAAA,UAC/C;AAAA,QACJ;AAAA,MACJ;AAGA,MAAAA,QAAO,UAAU,kBAAkB,WAAY;AAC3C,YAAI,SAAS;AAEb,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAChC,cAAI,WAAW;AACf,cAAI,OAAO;AACX,cAAI,aAAa,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACrC,mBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAChC,gBAAI,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,UAAU;AAChC;AACA,kBAAI,QAAQ;AACR,0BAAUA,QAAO;AAAA,uBACZ,OAAO;AACZ;AAAA,YACR,OACK;AACD,mBAAK,wBAAwB,MAAM,UAAU;AAC7C,kBAAI,CAAC;AACD,0BAAU,KAAK,2BAA2B,UAAU,IAAIA,QAAO;AACnE,yBAAW,KAAK,QAAQ,CAAC,EAAE,CAAC;AAC5B,qBAAO;AAAA,YACX;AAAA,UACJ;AACA,oBAAU,KAAK,+BAA+B,UAAU,MAAM,UAAU,IAAIA,QAAO;AAAA,QACvF;AAEA,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAChC,cAAI,WAAW;AACf,cAAI,OAAO;AACX,cAAI,aAAa,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACrC,mBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAChC,gBAAI,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,UAAU;AAChC;AACA,kBAAI,QAAQ;AACR,0BAAUA,QAAO;AAAA,uBACZ,OAAO;AACZ;AAAA,YACR,OACK;AACD,mBAAK,wBAAwB,MAAM,UAAU;AAC7C,kBAAI,CAAC;AACD,0BAAU,KAAK,2BAA2B,UAAU,IAAIA,QAAO;AACnE,yBAAW,KAAK,QAAQ,CAAC,EAAE,CAAC;AAC5B,qBAAO;AAAA,YACX;AAAA,UACJ;AACA,oBAAU,KAAK,+BAA+B,UAAU,MAAM,UAAU,IAAIA,QAAO;AAAA,QACvF;AAEA,iBAAS,IAAI,GAAG,IAAI,KAAK,OAAO,GAAG,KAAK;AACpC,mBAAS,IAAI,GAAG,IAAI,KAAK,OAAO,GAAG,KAAK;AACpC,gBAAI,QAAQ,KAAK,QAAQ,CAAC,EAAE,CAAC;AAC7B,gBAAI,SAAS,KAAK,QAAQ,CAAC,EAAE,IAAI,CAAC,KAC9B,SAAS,KAAK,QAAQ,IAAI,CAAC,EAAE,CAAC,KAC9B,SAAS,KAAK,QAAQ,IAAI,CAAC,EAAE,IAAI,CAAC;AAClC,wBAAUA,QAAO;AAAA,UACzB;AAAA,QACJ;AAEA,YAAI,OAAO;AACX,iBAAS,KAAK,GAAG,KAAK,KAAK,SAAS,KAAK,GAAG,QAAQ,MAAM;AACtD,cAAI,MAAM,GAAG,EAAE;AACf,iBAAO,IAAI,OAAO,SAAU,KAAKG,QAAO;AAAE,mBAAO,OAAOA,SAAQ,IAAI;AAAA,UAAI,GAAG,IAAI;AAAA,QACnF;AACA,YAAI,QAAQ,KAAK,OAAO,KAAK;AAE7B,YAAI,IAAI,KAAK,KAAK,KAAK,IAAI,OAAO,KAAK,QAAQ,EAAE,IAAI,KAAK,IAAI;AAC9D,eAAO,KAAK,KAAK,KAAK,CAAC;AACvB,kBAAU,IAAIH,QAAO;AACrB,eAAO,KAAK,UAAU,UAAU,OAAO;AACvC,eAAO;AAAA,MACX;AAKA,MAAAA,QAAO,UAAU,+BAA+B,WAAY;AACxD,YAAI,KAAK,WAAW;AAChB,iBAAO,CAAC;AAAA,aACP;AACD,cAAI,WAAW,KAAK,MAAM,KAAK,UAAU,CAAC,IAAI;AAC9C,cAAI,OAAQ,KAAK,WAAW,KAAM,KAC9B,KAAK,MAAM,KAAK,UAAU,IAAI,MAAM,WAAW,IAAI,EAAE,IAAI;AAC7D,cAAI,SAAS,CAAC,CAAC;AACf,mBAAS,MAAM,KAAK,OAAO,GAAG,OAAO,SAAS,UAAU,OAAO;AAC3D,mBAAO,OAAO,GAAG,GAAG,GAAG;AAC3B,iBAAO;AAAA,QACX;AAAA,MACJ;AAIA,MAAAA,QAAO,uBAAuB,SAAU,KAAK;AACzC,YAAI,MAAMA,QAAO,eAAe,MAAMA,QAAO;AACzC,gBAAM,IAAI,WAAW,6BAA6B;AACtD,YAAI,UAAU,KAAK,MAAM,OAAO,MAAM;AACtC,YAAI,OAAO,GAAG;AACV,cAAI,WAAW,KAAK,MAAM,MAAM,CAAC,IAAI;AACrC,qBAAW,KAAK,WAAW,MAAM,WAAW;AAC5C,cAAI,OAAO;AACP,sBAAU;AAAA,QAClB;AACA,eAAO,OAAO,UAAU,UAAU,KAAK;AACvC,eAAO;AAAA,MACX;AAIA,MAAAA,QAAO,sBAAsB,SAAU,KAAK,KAAK;AAC7C,eAAO,KAAK,MAAMA,QAAO,qBAAqB,GAAG,IAAI,CAAC,IAClDA,QAAO,wBAAwB,IAAI,OAAO,EAAE,GAAG,IAC3CA,QAAO,4BAA4B,IAAI,OAAO,EAAE,GAAG;AAAA,MAC/D;AAGA,MAAAA,QAAO,4BAA4B,SAAU,QAAQ;AACjD,YAAI,SAAS,KAAK,SAAS;AACvB,gBAAM,IAAI,WAAW,qBAAqB;AAG9C,YAAI,SAAS,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,SAAS,GAAG;AAC5B,iBAAO,KAAK,CAAC;AACjB,eAAO,KAAK,CAAC;AAIb,YAAI,OAAO;AACX,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAE7B,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,mBAAO,CAAC,IAAIA,QAAO,oBAAoB,OAAO,CAAC,GAAG,IAAI;AACtD,gBAAI,IAAI,IAAI,OAAO;AACf,qBAAO,CAAC,KAAK,OAAO,IAAI,CAAC;AAAA,UACjC;AACA,iBAAOA,QAAO,oBAAoB,MAAM,CAAI;AAAA,QAChD;AACA,eAAO;AAAA,MACX;AAEA,MAAAA,QAAO,8BAA8B,SAAU,MAAM,SAAS;AAC1D,YAAI,SAAS,QAAQ,IAAI,SAAU,GAAG;AAAE,iBAAO;AAAA,QAAG,CAAC;AACnD,YAAI,UAAU,SAAUC,IAAG;AACvB,cAAI,SAASA,KAAI,OAAO,MAAM;AAC9B,iBAAO,KAAK,CAAC;AACb,kBAAQ,QAAQ,SAAU,MAAM,GAAG;AAC/B,mBAAO,OAAO,CAAC,KAAKD,QAAO,oBAAoB,MAAM,MAAM;AAAA,UAC/D,CAAC;AAAA,QACL;AACA,iBAAS,KAAK,GAAG,SAAS,MAAM,KAAK,OAAO,QAAQ,MAAM;AACtD,cAAI,IAAI,OAAO,EAAE;AACjB,kBAAQ,CAAC;AAAA,QACb;AACA,eAAO;AAAA,MACX;AAGA,MAAAA,QAAO,sBAAsB,SAAU,GAAG,GAAG;AACzC,YAAI,MAAM,KAAK,KAAK,MAAM,KAAK;AAC3B,gBAAM,IAAI,WAAW,mBAAmB;AAE5C,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,KAAK,GAAG,KAAK;AACzB,cAAK,KAAK,KAAO,MAAM,KAAK;AAC5B,gBAAO,MAAM,IAAK,KAAK;AAAA,QAC3B;AACA,eAAO,MAAM,KAAK,CAAC;AACnB,eAAO;AAAA,MACX;AAGA,MAAAA,QAAO,UAAU,6BAA6B,SAAU,YAAY;AAChE,YAAI,IAAI,WAAW,CAAC;AACpB,eAAO,KAAK,KAAK,OAAO,CAAC;AACzB,YAAI,OAAO,IAAI,KAAK,WAAW,CAAC,KAAK,KAAK,WAAW,CAAC,KAAK,IAAI,KAAK,WAAW,CAAC,KAAK,KAAK,WAAW,CAAC,KAAK;AAC3G,gBAAQ,QAAQ,WAAW,CAAC,KAAK,IAAI,KAAK,WAAW,CAAC,KAAK,IAAI,IAAI,MAC5D,QAAQ,WAAW,CAAC,KAAK,IAAI,KAAK,WAAW,CAAC,KAAK,IAAI,IAAI;AAAA,MACtE;AAEA,MAAAA,QAAO,UAAU,iCAAiC,SAAU,iBAAiB,kBAAkB,YAAY;AACvG,YAAI,iBAAiB;AACjB,eAAK,wBAAwB,kBAAkB,UAAU;AACzD,6BAAmB;AAAA,QACvB;AACA,4BAAoB,KAAK;AACzB,aAAK,wBAAwB,kBAAkB,UAAU;AACzD,eAAO,KAAK,2BAA2B,UAAU;AAAA,MACrD;AAEA,MAAAA,QAAO,UAAU,0BAA0B,SAAU,kBAAkB,YAAY;AAC/E,YAAI,WAAW,CAAC,KAAK;AACjB,8BAAoB,KAAK;AAC7B,mBAAW,IAAI;AACf,mBAAW,QAAQ,gBAAgB;AAAA,MACvC;AAGA,MAAAA,QAAO,cAAc;AAErB,MAAAA,QAAO,cAAc;AAErB,MAAAA,QAAO,aAAa;AACpB,MAAAA,QAAO,aAAa;AACpB,MAAAA,QAAO,aAAa;AACpB,MAAAA,QAAO,aAAa;AACpB,MAAAA,QAAO,0BAA0B;AAAA;AAAA;AAAA,QAG7B,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,QAClK,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,QACnK,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,QACnK,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MACvK;AACA,MAAAA,QAAO,8BAA8B;AAAA;AAAA;AAAA,QAGjC,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,QAC5I,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,QACrJ,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,QACxJ,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAC7J;AACA,aAAOA;AAAA,IACX,EAAE;AAAA;AACF,EAAAD,WAAU,SAAS;AAGnB,WAAS,WAAW,KAAK,KAAK,IAAI;AAC9B,QAAI,MAAM,KAAK,MAAM,MAAM,QAAQ,OAAO;AACtC,YAAM,IAAI,WAAW,oBAAoB;AAC7C,aAAS,IAAI,MAAM,GAAG,KAAK,GAAG;AAC1B,SAAG,KAAM,QAAQ,IAAK,CAAC;AAAA,EAC/B;AAEA,WAAS,OAAO,GAAG,GAAG;AAClB,YAAS,MAAM,IAAK,MAAM;AAAA,EAC9B;AAEA,WAAS,OAAO,MAAM;AAClB,QAAI,CAAC;AACD,YAAM,IAAI,MAAM,iBAAiB;AAAA,EACzC;AAaA,MAAI;AAAA;AAAA,IAA2B,WAAY;AAKvC,eAASK,WAET,MAIA,UAEA,SAAS;AACL,aAAK,OAAO;AACZ,aAAK,WAAW;AAChB,aAAK,UAAU;AACf,YAAI,WAAW;AACX,gBAAM,IAAI,WAAW,kBAAkB;AAC3C,aAAK,UAAU,QAAQ,MAAM;AAAA,MACjC;AAKA,MAAAA,WAAU,YAAY,SAAU,MAAM;AAClC,YAAI,KAAK,CAAC;AACV,iBAAS,KAAK,GAAG,SAAS,MAAM,KAAK,OAAO,QAAQ,MAAM;AACtD,cAAI,IAAI,OAAO,EAAE;AACjB,qBAAW,GAAG,GAAG,EAAE;AAAA,QACvB;AACA,eAAO,IAAIA,WAAUA,WAAU,KAAK,MAAM,KAAK,QAAQ,EAAE;AAAA,MAC7D;AAEA,MAAAA,WAAU,cAAc,SAAU,QAAQ;AACtC,YAAI,CAACA,WAAU,UAAU,MAAM;AAC3B,gBAAM,IAAI,WAAW,wCAAwC;AACjE,YAAI,KAAK,CAAC;AACV,iBAAS,IAAI,GAAG,IAAI,OAAO,UAAS;AAChC,cAAI,IAAI,KAAK,IAAI,OAAO,SAAS,GAAG,CAAC;AACrC,qBAAW,SAAS,OAAO,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,GAAG,EAAE;AAClE,eAAK;AAAA,QACT;AACA,eAAO,IAAIA,WAAUA,WAAU,KAAK,SAAS,OAAO,QAAQ,EAAE;AAAA,MAClE;AAIA,MAAAA,WAAU,mBAAmB,SAAU,MAAM;AACzC,YAAI,CAACA,WAAU,eAAe,IAAI;AAC9B,gBAAM,IAAI,WAAW,6DAA6D;AACtF,YAAI,KAAK,CAAC;AACV,YAAI;AACJ,aAAK,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK,GAAG;AACtC,cAAI,OAAOA,WAAU,qBAAqB,QAAQ,KAAK,OAAO,CAAC,CAAC,IAAI;AACpE,kBAAQA,WAAU,qBAAqB,QAAQ,KAAK,OAAO,IAAI,CAAC,CAAC;AACjE,qBAAW,MAAM,IAAI,EAAE;AAAA,QAC3B;AACA,YAAI,IAAI,KAAK;AACT,qBAAWA,WAAU,qBAAqB,QAAQ,KAAK,OAAO,CAAC,CAAC,GAAG,GAAG,EAAE;AAC5E,eAAO,IAAIA,WAAUA,WAAU,KAAK,cAAc,KAAK,QAAQ,EAAE;AAAA,MACrE;AAGA,MAAAA,WAAU,eAAe,SAAU,MAAM;AAErC,YAAI,QAAQ;AACR,iBAAO,CAAC;AAAA,iBACHA,WAAU,UAAU,IAAI;AAC7B,iBAAO,CAACA,WAAU,YAAY,IAAI,CAAC;AAAA,iBAC9BA,WAAU,eAAe,IAAI;AAClC,iBAAO,CAACA,WAAU,iBAAiB,IAAI,CAAC;AAAA;AAExC,iBAAO,CAACA,WAAU,UAAUA,WAAU,gBAAgB,IAAI,CAAC,CAAC;AAAA,MACpE;AAGA,MAAAA,WAAU,UAAU,SAAU,WAAW;AACrC,YAAI,KAAK,CAAC;AACV,YAAI,YAAY;AACZ,gBAAM,IAAI,WAAW,mCAAmC;AAAA,iBACnD,YAAa,KAAK;AACvB,qBAAW,WAAW,GAAG,EAAE;AAAA,iBACtB,YAAa,KAAK,IAAK;AAC5B,qBAAW,GAAG,GAAG,EAAE;AACnB,qBAAW,WAAW,IAAI,EAAE;AAAA,QAChC,WACS,YAAY,KAAS;AAC1B,qBAAW,GAAG,GAAG,EAAE;AACnB,qBAAW,WAAW,IAAI,EAAE;AAAA,QAChC;AAEI,gBAAM,IAAI,WAAW,mCAAmC;AAC5D,eAAO,IAAIA,WAAUA,WAAU,KAAK,KAAK,GAAG,EAAE;AAAA,MAClD;AAGA,MAAAA,WAAU,YAAY,SAAU,MAAM;AAClC,eAAOA,WAAU,cAAc,KAAK,IAAI;AAAA,MAC5C;AAIA,MAAAA,WAAU,iBAAiB,SAAU,MAAM;AACvC,eAAOA,WAAU,mBAAmB,KAAK,IAAI;AAAA,MACjD;AAGA,MAAAA,WAAU,UAAU,UAAU,WAAY;AACtC,eAAO,KAAK,QAAQ,MAAM;AAAA,MAC9B;AAGA,MAAAA,WAAU,eAAe,SAAU,MAAM,SAAS;AAC9C,YAAI,SAAS;AACb,iBAAS,KAAK,GAAG,SAAS,MAAM,KAAK,OAAO,QAAQ,MAAM;AACtD,cAAI,MAAM,OAAO,EAAE;AACnB,cAAI,SAAS,IAAI,KAAK,iBAAiB,OAAO;AAC9C,cAAI,IAAI,YAAa,KAAK;AACtB,mBAAO;AACX,oBAAU,IAAI,SAAS,IAAI,QAAQ;AAAA,QACvC;AACA,eAAO;AAAA,MACX;AAEA,MAAAA,WAAU,kBAAkB,SAAU,KAAK;AACvC,cAAM,UAAU,GAAG;AACnB,YAAI,SAAS,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,cAAI,IAAI,OAAO,CAAC,KAAK;AACjB,mBAAO,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,eAC5B;AACD,mBAAO,KAAK,SAAS,IAAI,UAAU,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACrD,iBAAK;AAAA,UACT;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAGA,MAAAA,WAAU,gBAAgB;AAE1B,MAAAA,WAAU,qBAAqB;AAG/B,MAAAA,WAAU,uBAAuB;AACjC,aAAOA;AAAA,IACX,EAAE;AAAA;AACF,EAAAL,WAAU,YAAY;AAC1B,GAAG,cAAc,YAAY,CAAC,EAAE;AAAA,CAE/B,SAAUA,YAAW;AAClB,GAAC,SAAU,QAAQ;AAIf,QAAI;AAAA;AAAA,MAAqB,WAAY;AAEjC,iBAASM,KAET,SAEA,YAAY;AACR,eAAK,UAAU;AACf,eAAK,aAAa;AAAA,QACtB;AAEA,QAAAA,KAAI,MAAM,IAAIA,KAAI,GAAG,CAAC;AACtB,QAAAA,KAAI,SAAS,IAAIA,KAAI,GAAG,CAAC;AACzB,QAAAA,KAAI,WAAW,IAAIA,KAAI,GAAG,CAAC;AAC3B,QAAAA,KAAI,OAAO,IAAIA,KAAI,GAAG,CAAC;AACvB,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,WAAO,MAAM;AAAA,EACjB,GAAGN,WAAU,WAAWA,WAAU,SAAS,CAAC,EAAE;AAClD,GAAG,cAAc,YAAY,CAAC,EAAE;AAAA,CAE/B,SAAUA,YAAW;AAClB,GAAC,SAAU,WAAW;AAIlB,QAAI;AAAA;AAAA,MAAsB,WAAY;AAElC,iBAASO,MAET,UAEA,kBAAkB;AACd,eAAK,WAAW;AAChB,eAAK,mBAAmB;AAAA,QAC5B;AAIA,QAAAA,MAAK,UAAU,mBAAmB,SAAU,KAAK;AAC7C,iBAAO,KAAK,iBAAiB,KAAK,OAAO,MAAM,KAAK,EAAE,CAAC;AAAA,QAC3D;AAEA,QAAAA,MAAK,UAAU,IAAIA,MAAK,GAAK,CAAC,IAAI,IAAI,EAAE,CAAC;AACzC,QAAAA,MAAK,eAAe,IAAIA,MAAK,GAAK,CAAC,GAAG,IAAI,EAAE,CAAC;AAC7C,QAAAA,MAAK,OAAO,IAAIA,MAAK,GAAK,CAAC,GAAG,IAAI,EAAE,CAAC;AACrC,QAAAA,MAAK,QAAQ,IAAIA,MAAK,GAAK,CAAC,GAAG,IAAI,EAAE,CAAC;AACtC,QAAAA,MAAK,MAAM,IAAIA,MAAK,GAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AAClC,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,cAAU,OAAO;AAAA,EACrB,GAAGP,WAAU,cAAcA,WAAU,YAAY,CAAC,EAAE;AACxD,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI,KAAK;AAET,IAAI,2BAA2B;AAC/B,IAAI,uBAAuB;AAAA,EACvB,GAAG,GAAG,OAAO,IAAI;AAAA,EACjB,GAAG,GAAG,OAAO,IAAI;AAAA,EACjB,GAAG,GAAG,OAAO,IAAI;AAAA,EACjB,GAAG,GAAG,OAAO,IAAI;AACrB;AAEA,IAAI,kBAAmB,WAAY;AAC/B,MAAI;AACA,QAAI,OAAO,EAAE,QAAQ,IAAI,OAAO,CAAC;AAAA,EACrC,SACO,GAAG;AACN,WAAO;AAAA,EACX;AACA,SAAO;AACX,EAAG;AACH,SAAS,uBAAuB,OAAO;AACnC,SAAO,SAAS;AACpB;AACA,SAAS,aAAa,SAAS,QAAQ;AACnC,MAAI,WAAW,QAAQ;AAAE,aAAS;AAAA,EAAG;AACrC,MAAI,MAAM,CAAC;AACX,UAAQ,QAAQ,SAAU,KAAK,GAAG;AAC9B,QAAI,QAAQ;AACZ,QAAI,QAAQ,SAAU,MAAM,GAAG;AAC3B,UAAI,CAAC,QAAQ,UAAU,MAAM;AAGzB,YAAI,KAAK,IAAI,OAAO,QAAQ,QAAQ,GAAG,EAAE,OAAO,IAAI,QAAQ,GAAG,EAAE,OAAO,IAAI,OAAO,KAAK,EAAE,OAAO,QAAQ,QAAQ,GAAG,CAAC;AACrH,gBAAQ;AACR;AAAA,MACJ;AAEA,UAAI,MAAM,IAAI,SAAS,GAAG;AACtB,YAAI,CAAC,MAAM;AAGP;AAAA,QACJ;AACA,YAAI,UAAU,MAAM;AAEhB,cAAI,KAAK,IAAI,OAAO,IAAI,QAAQ,GAAG,EAAE,OAAO,IAAI,QAAQ,QAAQ,EAAE,OAAO,IAAI,QAAQ,GAAG,CAAC;AAAA,QAC7F,OACK;AAED,cAAI,KAAK,IAAI,OAAO,QAAQ,QAAQ,GAAG,EAAE,OAAO,IAAI,QAAQ,IAAI,EAAE,OAAO,IAAI,IAAI,OAAO,KAAK,EAAE,OAAO,QAAQ,QAAQ,GAAG,CAAC;AAAA,QAC9H;AACA;AAAA,MACJ;AACA,UAAI,QAAQ,UAAU,MAAM;AACxB,gBAAQ;AAAA,MACZ;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACD,SAAO,IAAI,KAAK,EAAE;AACtB;AACA,IAAI,cAAc;AAAA,EACd,OAAO;AAAA,IACH,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,EACb;AAAA,EACA,MAAM;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACH,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW,SAAU,GAAG;AAAE,aAAO,uBAAuB,CAAC;AAAA,IAAG;AAAA,EAChE;AAAA,EACA,YAAY;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACJ,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,EACb;AACJ;AACA,IAAI,iBAAiB,SAAS,SAAS,CAAC,GAAG,WAAW,GAAG,EAAE,UAAU;AAAA,EAC7D,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW,SAAU,IAAI;AAAE,WAAO,CAAC,UAAU,KAAK,EAAE,QAAQ,EAAE,IAAI;AAAA,EAAI;AAC1E,EAAE,CAAC;AACP,IAAI,YAAY,gBAAgB;AAAA,EAC5B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO,SAAU,OAAO;AACpB,QAAI,WAAW,IAAI,CAAC;AACpB,QAAI,SAAS,IAAI,EAAE;AACnB,QAAI,WAAW,WAAY;AACvB,UAAI,QAAQ,MAAM,OAAO,QAAQ,MAAM,OAAO,SAAS,MAAM;AAC7D,UAAI,QAAQ,GAAG,OAAO,WAAW,OAAO,qBAAqB,KAAK,CAAC,EAAE,WAAW;AAChF,eAAS,QAAQ,MAAM,SAAS,SAAS;AAOzC,aAAO,QAAQ,aAAa,OAAO,MAAM;AAAA,IAC7C;AACA,aAAS;AACT,cAAU,QAAQ;AAClB,WAAO,WAAY;AAAE,aAAO,EAAE,OAAO;AAAA,QACjC,OAAO,MAAM;AAAA,QACb,QAAQ,MAAM;AAAA,QACd,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,SAAS,OAAO,OAAO,SAAS,OAAO,GAAG,EAAE,OAAO,SAAS,KAAK;AAAA,MACrE,GAAG;AAAA,QACC,EAAE,QAAQ;AAAA,UACN,MAAM,MAAM;AAAA,UACZ,GAAG,SAAS,OAAO,SAAS,OAAO,GAAG,EAAE,OAAO,SAAS,OAAO,KAAK;AAAA,QACxE,CAAC;AAAA,QACD,EAAE,QAAQ,EAAE,MAAM,MAAM,YAAY,GAAG,OAAO,MAAM,CAAC;AAAA,MACzD,CAAC;AAAA,IAAG;AAAA,EACR;AACJ,CAAC;AACD,IAAI,eAAe,gBAAgB;AAAA,EAC/B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO,SAAU,OAAO;AACpB,QAAI,WAAW,IAAI,IAAI;AACvB,QAAI,WAAW,WAAY;AACvB,UAAI,QAAQ,MAAM,OAAO,QAAQ,MAAM,OAAO,OAAO,MAAM,MAAM,SAAS,MAAM,QAAQ,aAAa,MAAM,YAAY,aAAa,MAAM;AAC1I,UAAI,SAAS,SAAS;AACtB,UAAI,CAAC,QAAQ;AACT;AAAA,MACJ;AACA,UAAI,MAAM,OAAO,WAAW,IAAI;AAChC,UAAI,CAAC,KAAK;AACN;AAAA,MACJ;AACA,UAAI,QAAQ,GAAG,OAAO,WAAW,OAAO,qBAAqB,KAAK,CAAC,EAAE,WAAW;AAChF,UAAI,WAAW,MAAM,SAAS,SAAS;AACvC,UAAI,mBAAmB,OAAO,oBAAoB;AAClD,UAAI,QAAS,OAAO,WAAY;AAChC,aAAO,SAAS,OAAO,QAAQ,OAAO;AACtC,UAAI,MAAM,OAAO,KAAK;AACtB,UAAI,YAAY;AAChB,UAAI,SAAS,GAAG,GAAG,UAAU,QAAQ;AACrC,UAAI,YAAY;AAChB,UAAI,iBAAiB;AACjB,YAAI,KAAK,IAAI,OAAO,aAAa,OAAO,MAAM,CAAC,CAAC;AAAA,MACpD,OACK;AACD,cAAM,QAAQ,SAAU,KAAK,KAAK;AAC9B,cAAI,QAAQ,SAAU,MAAM,KAAK;AAC7B,gBAAI,MAAM;AACN,kBAAI,SAAS,MAAM,QAAQ,MAAM,QAAQ,GAAG,CAAC;AAAA,YACjD;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,MACL;AAAA,IACJ;AACA,cAAU,QAAQ;AAClB,cAAU,QAAQ;AAClB,WAAO,WAAY;AAAE,aAAO,EAAE,UAAU;AAAA,QACpC,KAAK;AAAA,QACL,OAAO,EAAE,OAAO,GAAG,OAAO,MAAM,MAAM,IAAI,GAAG,QAAQ,GAAG,OAAO,MAAM,MAAM,IAAI,EAAE;AAAA,MACrF,CAAC;AAAA,IAAG;AAAA,EACR;AACJ,CAAC;AACD,IAAI,YAAY,gBAAgB;AAAA,EAC5B,MAAM;AAAA,EACN,QAAQ,WAAY;AAChB,QAAI,KAAK,KAAK,QAAQ,WAAW,GAAG,UAAU,QAAQ,GAAG,OAAO,QAAQ,GAAG,MAAM,UAAU,GAAG,QAAQ,SAAS,GAAG,OAAO,aAAa,GAAG,YAAY,aAAa,GAAG;AACrK,QAAI,OAAO,UAAU;AACrB,QAAI,SAAS,YAAY;AACzB,QAAI,QAAQ,uBAAuB,MAAM,IAAI,SAAS;AACtD,WAAO,EAAE,aAAa,QAAQ,YAAY,cAAc,EAAE,OAAc,MAAY,QAAgB,OAAc,YAAwB,WAAuB,CAAC;AAAA,EACtK;AAAA,EACA,OAAO;AACX,CAAC;", "names": ["__assign", "qrcodegen", "QrCode", "b", "i", "color", "QrSegment", "Ecc", "Mode"]}