{"version": 3, "sources": ["../../@arcgis/core/portal/support/featureCollectionUtils.js", "../../@arcgis/core/layers/support/layersCreator.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nfunction e(e){return t(e,\"notes\")}function r(e){return t(e,\"markup\")}function n(e){return t(e,\"route\")}function t(e,r){return!(!e.layerType||\"ArcGISFeatureLayer\"!==e.layerType)&&e.featureCollectionType===r}export{e as isMapNotesLayer,r as isMarkupLayer,n as isRouteLayer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../core/Collection.js\";import\"../../core/has.js\";import{eachAlways as r}from\"../../core/promiseUtils.js\";import{layerLookupMap as a}from\"./lazyLayerLoader.js\";import y from\"../../portal/PortalItem.js\";import{isMapNotesLayer as t,isMarkupLayer as i,isRouteLayer as L}from\"../../portal/support/featureCollectionUtils.js\";import{selectLayerClassPath as n}from\"../../portal/support/portalLayers.js\";import{loadStyleRenderer as o}from\"../../renderers/support/styleUtils.js\";async function c(e,a,y){if(!a)return;const t=[];for(const r of a){const e=d(r,y);\"GroupLayer\"===r.layerType?t.push(M(e,r,y)):t.push(e)}const i=await r(t);for(const r of i)r.value&&e.add(r.value)}const l={ArcGISDimensionLayer:\"DimensionLayer\",ArcGISFeatureLayer:\"FeatureLayer\",ArcGISImageServiceLayer:\"ImageryLayer\",ArcGISMapServiceLayer:\"MapImageLayer\",PointCloudLayer:\"PointCloudLayer\",ArcGISSceneServiceLayer:\"SceneLayer\",IntegratedMeshLayer:\"IntegratedMeshLayer\",OGCFeatureLayer:\"OGCFeatureLayer\",BuildingSceneLayer:\"BuildingSceneLayer\",ArcGISTiledElevationServiceLayer:\"ElevationLayer\",ArcGISTiledImageServiceLayer:\"ImageryTileLayer\",ArcGISTiledMapServiceLayer:\"TileLayer\",GroupLayer:\"GroupLayer\",GeoJSON:\"GeoJSONLayer\",WebTiledLayer:\"WebTileLayer\",CSV:\"CSVLayer\",VectorTileLayer:\"VectorTileLayer\",WFS:\"WFSLayer\",WMS:\"WMSLayer\",DefaultTileLayer:\"TileLayer\",KML:\"KMLLayer\",RasterDataLayer:\"UnsupportedLayer\",Voxel:\"VoxelLayer\",LineOfSightLayer:\"LineOfSightLayer\"},s={ArcGISTiledElevationServiceLayer:\"ElevationLayer\",DefaultTileLayer:\"ElevationLayer\",RasterDataElevationLayer:\"UnsupportedLayer\"},p={ArcGISTiledMapServiceLayer:\"TileLayer\",ArcGISTiledImageServiceLayer:\"ImageryTileLayer\",OpenStreetMap:\"OpenStreetMapLayer\",WebTiledLayer:\"WebTileLayer\",VectorTileLayer:\"VectorTileLayer\",ArcGISImageServiceLayer:\"UnsupportedLayer\",WMS:\"UnsupportedLayer\",ArcGISMapServiceLayer:\"UnsupportedLayer\",ArcGISSceneServiceLayer:\"SceneLayer\",DefaultTileLayer:\"TileLayer\"},S={ArcGISAnnotationLayer:\"UnsupportedLayer\",ArcGISDimensionLayer:\"UnsupportedLayer\",ArcGISFeatureLayer:\"FeatureLayer\",ArcGISImageServiceLayer:\"ImageryLayer\",ArcGISImageServiceVectorLayer:\"ImageryLayer\",ArcGISMapServiceLayer:\"MapImageLayer\",ArcGISStreamLayer:\"StreamLayer\",ArcGISTiledImageServiceLayer:\"ImageryTileLayer\",ArcGISTiledMapServiceLayer:\"TileLayer\",BingMapsAerial:\"BingMapsLayer\",BingMapsRoad:\"BingMapsLayer\",BingMapsHybrid:\"BingMapsLayer\",CSV:\"CSVLayer\",DefaultTileLayer:\"TileLayer\",GeoRSS:\"GeoRSSLayer\",GeoJSON:\"GeoJSONLayer\",GroupLayer:\"GroupLayer\",KML:\"KMLLayer\",MediaLayer:\"MediaLayer\",OGCFeatureLayer:\"OGCFeatureLayer\",OrientedImageryLayer:\"OrientedImageryLayer\",SubtypeGroupLayer:\"SubtypeGroupLayer\",VectorTileLayer:\"VectorTileLayer\",WFS:\"WFSLayer\",WMS:\"WMSLayer\",WebTiledLayer:\"WebTileLayer\"},u={ArcGISFeatureLayer:\"FeatureLayer\"},I={ArcGISImageServiceLayer:\"ImageryLayer\",ArcGISImageServiceVectorLayer:\"ImageryLayer\",ArcGISMapServiceLayer:\"MapImageLayer\",ArcGISTiledImageServiceLayer:\"ImageryTileLayer\",ArcGISTiledMapServiceLayer:\"TileLayer\",OpenStreetMap:\"OpenStreetMapLayer\",VectorTileLayer:\"VectorTileLayer\",WebTiledLayer:\"WebTileLayer\",BingMapsAerial:\"BingMapsLayer\",BingMapsRoad:\"BingMapsLayer\",BingMapsHybrid:\"BingMapsLayer\",WMS:\"WMSLayer\",DefaultTileLayer:\"TileLayer\"};async function d(e,r){return m(await T(e,r),e,r)}async function m(e,r,a){const y=new e;return y.read(r,a.context),\"group\"===y.type&&g(r)&&await A(y,r,a.context),await o(y,a.context),y}async function T(e,r){const o=r.context,c=f(o);let l=e.layerType||e.type;!l&&r&&r.defaultLayerType&&(l=r.defaultLayerType);const s=c[l];let p=s?a[s]:a.UnknownLayer;if(G(e)){const r=o?.portal;if(e.itemId){const t=new y({id:e.itemId,portal:r});await t.load();const i=(await n(t)).className||\"UnknownLayer\";p=a[i]}}else\"ArcGISFeatureLayer\"===l?t(e)||i(e)?p=a.MapNotesLayer:L(e)?p=a.RouteLayer:g(e)&&(p=a.GroupLayer):e.wmtsInfo&&e.wmtsInfo.url&&e.wmtsInfo.layerIdentifier?p=a.WMTSLayer:\"WFS\"===l&&\"2.0.0\"!==e.wfsInfo?.version&&(p=a.UnsupportedLayer);return p()}function g(e){if(\"ArcGISFeatureLayer\"!==e.layerType||G(e))return!1;return(e.featureCollection?.layers?.length??0)>1}function G(e){return\"Feature Collection\"===e.type}function f(e){let r;if(\"web-scene\"===e.origin)switch(e.layerContainerType){case\"basemap\":r=p;break;case\"ground\":r=s;break;default:r=l}else switch(e.layerContainerType){case\"basemap\":r=I;break;case\"tables\":r=u;break;default:r=S}return r}async function M(r,a,y){const t=new e,i=c(t,Array.isArray(a.layers)?a.layers:[],y),L=await r;if(await i,\"group\"===L.type)return L.layers.addMany(t),L}async function A(e,r,y){const t=a.FeatureLayer,i=await t(),L=r.featureCollection,n=L?.showLegend,o=L?.layers?.map(((a,t)=>{const L=new i;L.read(a,y);const o={...y,ignoreDefaults:!0};return L.read({id:`${e.id}-sublayer-${t}`,visibility:r.visibleLayers?.includes(t)??!0},o),null!=n&&L.read({showLegend:n},o),L}));e.layers.addMany(o??[])}export{c as populateOperationalLayers};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,SAAS,EAAEA,IAAE;AAAC,SAAOC,GAAED,IAAE,OAAO;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAOC,GAAED,IAAE,QAAQ;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAOC,GAAED,IAAE,OAAO;AAAC;AAAC,SAASC,GAAED,IAAEE,IAAE;AAAC,SAAM,EAAE,CAACF,GAAE,aAAW,yBAAuBA,GAAE,cAAYA,GAAE,0BAAwBE;AAAC;;;ACAyR,eAAe,EAAEC,IAAEC,IAAE,GAAE;AAAC,MAAG,CAACA,GAAE;AAAO,QAAMC,KAAE,CAAC;AAAE,aAAUC,MAAKF,IAAE;AAAC,UAAMD,KAAE,EAAEG,IAAE,CAAC;AAAE,qBAAeA,GAAE,YAAUD,GAAE,KAAK,EAAEF,IAAEG,IAAE,CAAC,CAAC,IAAED,GAAE,KAAKF,EAAC;AAAA,EAAC;AAAC,QAAM,IAAE,MAAM,EAAEE,EAAC;AAAE,aAAUC,MAAK,EAAE,CAAAA,GAAE,SAAOH,GAAE,IAAIG,GAAE,KAAK;AAAC;AAAC,IAAM,IAAE,EAAC,sBAAqB,kBAAiB,oBAAmB,gBAAe,yBAAwB,gBAAe,uBAAsB,iBAAgB,iBAAgB,mBAAkB,yBAAwB,cAAa,qBAAoB,uBAAsB,iBAAgB,mBAAkB,oBAAmB,sBAAqB,kCAAiC,kBAAiB,8BAA6B,oBAAmB,4BAA2B,aAAY,YAAW,cAAa,SAAQ,gBAAe,eAAc,gBAAe,KAAI,YAAW,iBAAgB,mBAAkB,KAAI,YAAW,KAAI,YAAW,kBAAiB,aAAY,KAAI,YAAW,iBAAgB,oBAAmB,OAAM,cAAa,kBAAiB,mBAAkB;AAAlwB,IAAowB,IAAE,EAAC,kCAAiC,kBAAiB,kBAAiB,kBAAiB,0BAAyB,mBAAkB;AAAt4B,IAAw4B,IAAE,EAAC,4BAA2B,aAAY,8BAA6B,oBAAmB,eAAc,sBAAqB,eAAc,gBAAe,iBAAgB,mBAAkB,yBAAwB,oBAAmB,KAAI,oBAAmB,uBAAsB,oBAAmB,yBAAwB,cAAa,kBAAiB,YAAW;AAAhvC,IAAkvC,IAAE,EAAC,uBAAsB,oBAAmB,sBAAqB,oBAAmB,oBAAmB,gBAAe,yBAAwB,gBAAe,+BAA8B,gBAAe,uBAAsB,iBAAgB,mBAAkB,eAAc,8BAA6B,oBAAmB,4BAA2B,aAAY,gBAAe,iBAAgB,cAAa,iBAAgB,gBAAe,iBAAgB,KAAI,YAAW,kBAAiB,aAAY,QAAO,eAAc,SAAQ,gBAAe,YAAW,cAAa,KAAI,YAAW,YAAW,cAAa,iBAAgB,mBAAkB,sBAAqB,wBAAuB,mBAAkB,qBAAoB,iBAAgB,mBAAkB,KAAI,YAAW,KAAI,YAAW,eAAc,eAAc;AAA3hE,IAA6hE,IAAE,EAAC,oBAAmB,eAAc;AAAjkE,IAAmkE,IAAE,EAAC,yBAAwB,gBAAe,+BAA8B,gBAAe,uBAAsB,iBAAgB,8BAA6B,oBAAmB,4BAA2B,aAAY,eAAc,sBAAqB,iBAAgB,mBAAkB,eAAc,gBAAe,gBAAe,iBAAgB,cAAa,iBAAgB,gBAAe,iBAAgB,KAAI,YAAW,kBAAiB,YAAW;AAAE,eAAe,EAAEH,IAAEG,IAAE;AAAC,SAAOC,GAAE,MAAM,EAAEJ,IAAEG,EAAC,GAAEH,IAAEG,EAAC;AAAC;AAAC,eAAeC,GAAEJ,IAAEG,IAAEF,IAAE;AAAC,QAAM,IAAE,IAAID;AAAE,SAAO,EAAE,KAAKG,IAAEF,GAAE,OAAO,GAAE,YAAU,EAAE,QAAM,EAAEE,EAAC,KAAG,MAAM,EAAE,GAAEA,IAAEF,GAAE,OAAO,GAAE,MAAM,EAAE,GAAEA,GAAE,OAAO,GAAE;AAAC;AAAC,eAAe,EAAED,IAAEG,IAAE;AAJv3G;AAIw3G,QAAM,IAAEA,GAAE,SAAQE,KAAE,EAAE,CAAC;AAAE,MAAIC,KAAEN,GAAE,aAAWA,GAAE;AAAK,GAACM,MAAGH,MAAGA,GAAE,qBAAmBG,KAAEH,GAAE;AAAkB,QAAMI,KAAEF,GAAEC,EAAC;AAAE,MAAIE,KAAED,KAAE,EAAEA,EAAC,IAAE,EAAE;AAAa,MAAG,EAAEP,EAAC,GAAE;AAAC,UAAMG,KAAE,uBAAG;AAAO,QAAGH,GAAE,QAAO;AAAC,YAAME,KAAE,IAAI,EAAE,EAAC,IAAGF,GAAE,QAAO,QAAOG,GAAC,CAAC;AAAE,YAAMD,GAAE,KAAK;AAAE,YAAM,KAAG,MAAM,EAAEA,EAAC,GAAG,aAAW;AAAe,MAAAM,KAAE,EAAE,CAAC;AAAA,IAAC;AAAA,EAAC,MAAK,0BAAuBF,KAAE,EAAEN,EAAC,KAAG,EAAEA,EAAC,IAAEQ,KAAE,EAAE,gBAAc,EAAER,EAAC,IAAEQ,KAAE,EAAE,aAAW,EAAER,EAAC,MAAIQ,KAAE,EAAE,cAAYR,GAAE,YAAUA,GAAE,SAAS,OAAKA,GAAE,SAAS,kBAAgBQ,KAAE,EAAE,YAAU,UAAQF,MAAG,cAAU,KAAAN,GAAE,YAAF,mBAAW,aAAUQ,KAAE,EAAE;AAAkB,SAAOA,GAAE;AAAC;AAAC,SAAS,EAAER,IAAE;AAJ55H;AAI65H,MAAG,yBAAuBA,GAAE,aAAW,EAAEA,EAAC,EAAE,QAAM;AAAG,YAAO,WAAAA,GAAE,sBAAF,mBAAqB,WAArB,mBAA6B,WAAQ,KAAG;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAM,yBAAuBA,GAAE;AAAI;AAAC,SAAS,EAAEA,IAAE;AAAC,MAAIG;AAAE,MAAG,gBAAcH,GAAE,OAAO,SAAOA,GAAE,oBAAmB;AAAA,IAAC,KAAI;AAAU,MAAAG,KAAE;AAAE;AAAA,IAAM,KAAI;AAAS,MAAAA,KAAE;AAAE;AAAA,IAAM;AAAQ,MAAAA,KAAE;AAAA,EAAC;AAAA,MAAM,SAAOH,GAAE,oBAAmB;AAAA,IAAC,KAAI;AAAU,MAAAG,KAAE;AAAE;AAAA,IAAM,KAAI;AAAS,MAAAA,KAAE;AAAE;AAAA,IAAM;AAAQ,MAAAA,KAAE;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,eAAe,EAAEA,IAAEF,IAAE,GAAE;AAAC,QAAMC,KAAE,IAAI,KAAE,IAAE,EAAEA,IAAE,MAAM,QAAQD,GAAE,MAAM,IAAEA,GAAE,SAAO,CAAC,GAAE,CAAC,GAAE,IAAE,MAAME;AAAE,MAAG,MAAM,GAAE,YAAU,EAAE,KAAK,QAAO,EAAE,OAAO,QAAQD,EAAC,GAAE;AAAC;AAAC,eAAe,EAAEF,IAAEG,IAAE,GAAE;AAJ98I;AAI+8I,QAAMD,KAAE,EAAE,cAAa,IAAE,MAAMA,GAAE,GAAE,IAAEC,GAAE,mBAAkBM,KAAE,uBAAG,YAAW,KAAE,4BAAG,WAAH,mBAAW,IAAK,CAACR,IAAEC,OAAI;AAJjjJ,QAAAQ;AAIkjJ,UAAMC,KAAE,IAAI;AAAE,IAAAA,GAAE,KAAKV,IAAE,CAAC;AAAE,UAAMW,KAAE,EAAC,GAAG,GAAE,gBAAe,KAAE;AAAE,WAAOD,GAAE,KAAK,EAAC,IAAG,GAAGX,GAAE,EAAE,aAAaE,EAAC,IAAG,cAAWQ,MAAAP,GAAE,kBAAF,gBAAAO,IAAiB,SAASR,QAAI,KAAE,GAAEU,EAAC,GAAE,QAAMH,MAAGE,GAAE,KAAK,EAAC,YAAWF,GAAC,GAAEG,EAAC,GAAED;AAAA,EAAC;AAAI,EAAAX,GAAE,OAAO,QAAQ,KAAG,CAAC,CAAC;AAAC;", "names": ["e", "t", "r", "e", "a", "t", "r", "m", "c", "l", "s", "p", "n", "_a", "L", "o"]}